/**
 * ProductDataFromLIMSSinceResponse.java
 *
 * This file was auto-generated from WSDL
 * by the Apache Axis 1.4 Apr 22, 2006 (06:55:48 PDT) WSDL2Java emitter.
 */

package com.sgs.ilayer.cclas.client.outbound.wsdl;

public class ProductDataFromLIMSSinceResponse  implements java.io.Serializable {
    private com.sgs.ilayer.cclas.client.outbound.wsdl.ProductDataFromLIMSSinceResponseProductDataFromLIMSSinceResult productDataFromLIMSSinceResult;

    public ProductDataFromLIMSSinceResponse() {
    }

    public ProductDataFromLIMSSinceResponse(
           com.sgs.ilayer.cclas.client.outbound.wsdl.ProductDataFromLIMSSinceResponseProductDataFromLIMSSinceResult productDataFromLIMSSinceResult) {
           this.productDataFromLIMSSinceResult = productDataFromLIMSSinceResult;
    }


    /**
     * Gets the productDataFromLIMSSinceResult value for this ProductDataFromLIMSSinceResponse.
     * 
     * @return productDataFromLIMSSinceResult
     */
    public com.sgs.ilayer.cclas.client.outbound.wsdl.ProductDataFromLIMSSinceResponseProductDataFromLIMSSinceResult getProductDataFromLIMSSinceResult() {
        return productDataFromLIMSSinceResult;
    }


    /**
     * Sets the productDataFromLIMSSinceResult value for this ProductDataFromLIMSSinceResponse.
     * 
     * @param productDataFromLIMSSinceResult
     */
    public void setProductDataFromLIMSSinceResult(com.sgs.ilayer.cclas.client.outbound.wsdl.ProductDataFromLIMSSinceResponseProductDataFromLIMSSinceResult productDataFromLIMSSinceResult) {
        this.productDataFromLIMSSinceResult = productDataFromLIMSSinceResult;
    }

    private Object __equalsCalc = null;
    public synchronized boolean equals(Object obj) {
        if (!(obj instanceof ProductDataFromLIMSSinceResponse)) return false;
        ProductDataFromLIMSSinceResponse other = (ProductDataFromLIMSSinceResponse) obj;
        if (obj == null) return false;
        if (this == obj) return true;
        if (__equalsCalc != null) {
            return (__equalsCalc == obj);
        }
        __equalsCalc = obj;
        boolean _equals;
        _equals = true && 
            ((this.productDataFromLIMSSinceResult==null && other.getProductDataFromLIMSSinceResult()==null) || 
             (this.productDataFromLIMSSinceResult!=null &&
              this.productDataFromLIMSSinceResult.equals(other.getProductDataFromLIMSSinceResult())));
        __equalsCalc = null;
        return _equals;
    }

    private boolean __hashCodeCalc = false;
    public synchronized int hashCode() {
        if (__hashCodeCalc) {
            return 0;
        }
        __hashCodeCalc = true;
        int _hashCode = 1;
        if (getProductDataFromLIMSSinceResult() != null) {
            _hashCode += getProductDataFromLIMSSinceResult().hashCode();
        }
        __hashCodeCalc = false;
        return _hashCode;
    }

    // Type metadata
    private static org.apache.axis.description.TypeDesc typeDesc =
        new org.apache.axis.description.TypeDesc(ProductDataFromLIMSSinceResponse.class, true);

    static {
        typeDesc.setXmlType(new javax.xml.namespace.QName("http://ilayer.sgs.com/cclas/client/outbound/wsdl/", ">ProductDataFromLIMSSinceResponse"));
        org.apache.axis.description.ElementDesc elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("productDataFromLIMSSinceResult");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ilayer.sgs.com/cclas/client/outbound/wsdl/", "ProductDataFromLIMSSinceResult"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://ilayer.sgs.com/cclas/client/outbound/wsdl/", ">>ProductDataFromLIMSSinceResponse>ProductDataFromLIMSSinceResult"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
    }

    /**
     * Return type metadata object
     */
    public static org.apache.axis.description.TypeDesc getTypeDesc() {
        return typeDesc;
    }

    /**
     * Get Custom Serializer
     */
    public static org.apache.axis.encoding.Serializer getSerializer(
           String mechType,
           Class _javaType,
           javax.xml.namespace.QName _xmlType) {
        return 
          new  org.apache.axis.encoding.ser.BeanSerializer(
            _javaType, _xmlType, typeDesc);
    }

    /**
     * Get Custom Deserializer
     */
    public static org.apache.axis.encoding.Deserializer getDeserializer(
           String mechType,
           Class _javaType,
           javax.xml.namespace.QName _xmlType) {
        return 
          new  org.apache.axis.encoding.ser.BeanDeserializer(
            _javaType, _xmlType, typeDesc);
    }

}
