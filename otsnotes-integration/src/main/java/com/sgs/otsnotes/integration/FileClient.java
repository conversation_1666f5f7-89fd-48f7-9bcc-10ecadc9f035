package com.sgs.otsnotes.integration;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.core.base.CustomResult;
import com.sgs.framework.core.context.SystemContextHolder;
import com.sgs.framework.model.enums.SgsSystem;
import com.sgs.framework.tool.utils.Func;
import com.sgs.otsnotes.core.config.GPOConfig;
import com.sgs.otsnotes.core.config.InterfaceConfig;
import com.sgs.otsnotes.core.config.SysConstants;
import com.sgs.otsnotes.core.util.HttpClientUtil;
import com.sgs.otsnotes.facade.model.common.HttpResult;
import com.sgs.otsnotes.facade.model.dto.Doc2FdfDTO;
import com.sgs.otsnotes.facade.model.dto.FileDTO;
import com.sgs.otsnotes.facade.model.dto.SlimJobDTO;
import com.sgs.otsnotes.facade.model.enums.DssActionType;
import com.sgs.otsnotes.facade.model.enums.DssCopyType;
import com.sgs.otsnotes.facade.model.enums.DssReportFileType;
import com.sgs.otsnotes.facade.model.enums.DssWaterMarkType;
import com.sgs.otsnotes.facade.model.info.FileInfo;
import com.sgs.otsnotes.facade.model.info.UploadFileInfo;
import com.sgs.otsnotes.facade.model.info.UploadTestReportInfo;
import com.sgs.otsnotes.facade.model.req.framework.FrameworkUploadFileReq;
import com.sgs.otsnotes.facade.model.rsp.framework.UploadFileResult;
import com.sgs.otsnotes.facade.model.rsp.hhx.UploadFileRsp;
import com.sgs.preorder.facade.model.dto.order.OrderInfoDto;
import com.sgs.preorder.facade.model.req.OrderIdReq;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpResponse;
import org.apache.http.HttpStatus;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.mime.MultipartEntityBuilder;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.AbstractResource;
import org.springframework.core.io.UrlResource;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.multipart.MultipartFile;

import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;

@Component
public class FileClient {
    private static final Logger logger = LoggerFactory.getLogger(FileClient.class);
    @Autowired
    private InterfaceConfig interfaceConfig;
    @Autowired
    private RestTemplate restTemplate;
    @Autowired
    private OrderClient orderClient;
    @Autowired
    private GPOConfig gpoConfig;
    @Autowired
    private FrameWorkClient frameWorkClient;
    /**
     *
     * @param reportFiles
     * @param file
     * @return
     */
    public CustomResult uploadFile(ConcurrentHashMap<String, String> reportFiles, UploadFileInfo file){
        CustomResult result = new CustomResult();
        try{
            StringBuffer reqParams = new StringBuffer();
            reqParams.append(String.format("systemID=%s", SgsSystem.GPO.getSgsSystemId()));
            reqParams.append(String.format("&buID=%s", file.getBuId()));
            reqParams.append(String.format("&locationID=%s", file.getLocationID()));
            reqParams.append(String.format("&orderID=%s", file.getOrderId()));
            reqParams.append(String.format("&objectID=%s", file.getId()));
            reqParams.append(String.format("&objectType=%s", 2));

            /*String uploadUrl = String.format("http://10.205.139.111/FrameWorkApi/file/doUpload?systemID=3&buID=3&locationID=11&orderID=%s&objectID=%s&objectType=2",testLine.getId(),testLine.getGeneralorderinstanceid());*/
            String uploadUrl = String.format("%s/FrameWorkApi/file/doUpload?%s", interfaceConfig.getBaseUrl(), reqParams.toString());

            String reqUrl = java.net.URLDecoder.decode(file.getReportUrl(),"UTF-8");
            UrlResource resource = new UrlResource(reqUrl){
                @Override
                public String getFilename() throws IllegalStateException {
                    // fileLocation
                    return FilenameUtils.getName(reqUrl);
                }
            };

            if (!resource.exists()){
                result.setMsg("该文件不存在或已过期.");
                return result;
            }

            MultiValueMap<String, Object> params = new LinkedMultiValueMap<>();
            params.add("file", resource);

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.MULTIPART_FORM_DATA);
            HttpEntity<MultiValueMap<String, Object>> httpEntity = new HttpEntity<>(params, headers);

            // 发送rest请求往FrameworkApi接口中存储新的文件
            String jsonValue = restTemplate.postForObject(uploadUrl, httpEntity, String.class);
            logger.info("FileClient.uploadFile返回，orderId:{}, jsonValue:{}", file.getOrderId(), jsonValue);
            UploadFileRsp rspResults = JSON.parseObject(jsonValue, new TypeReference<UploadFileRsp>() {});
            List<FileInfo> files = rspResults.getData();
            if (files == null || files.isEmpty()){
                result.setMsg("上传附件失败返回对象为空.");
                return result;
            }
            reportFiles.put(file.getReportUrl(), files.get(0).getId());
        }catch (Exception ex){
            logger.error("上传文件({})异常：",file.getId(), ex);
            result.setMsg(String.format("上传文件(%s)异常：%s", file.getId(), ex.getMessage()));
        }
        return result;
    }

    /**
     *
     * @param attachments
     * @param orderId
     * @param testLineFile
     * @return
     */
    public CustomResult copyFile(ConcurrentHashMap<String, FileInfo> attachments, String orderId, Map.Entry<String, Set<String>> testLineFile){
        List<String> testLineIds = Lists.newArrayList(testLineFile.getValue());
        if (testLineIds == null || testLineIds.isEmpty()){
            return null;
        }
        CustomResult rspResult = new CustomResult();
        try{
            FileInfo fileInfo = new FileInfo();
            fileInfo.setId(testLineFile.getKey());
            fileInfo.setOrderId(orderId);
            fileInfo.setObjectID(testLineIds.get(0));
            CustomResult<FileInfo> customResult = this.copyFile(fileInfo);

//            HashMap<String, String> reqParams = new HashMap();
//            reqParams.put("id", testLineFile.getKey());
//            reqParams.put("generalOrderID", orderId);
//            reqParams.put("objectID", testLineIds.get(0));
//
//            String frameWorkApi = String.format("%s/file/copyTbfile", interfaceConfig.getFrameWorkApiUrl());
//
//            HttpResult<FileInfo> fileResult = HttpClientUtil.post(frameWorkApi, reqParams, new TypeReference<HttpResult<FileInfo>>() {});
//            if (fileResult == null || !fileResult.isSuccess()){
//                return null;
//            }
//            FileInfo rspFile = fileResult.getResult();
            FileInfo rspFile = customResult.getData();
            if (rspFile == null){
                return null;
            }
            for (String testLineId: testLineIds) {
                attachments.put(testLineId, rspFile);
            }
        }catch (Exception ex){
            logger.error("上传文件({})异常：",testLineFile.getKey(), ex);
            rspResult.setMsg(String.format("上传文件(%s)异常：%s", testLineFile.getKey(), ex.getMessage()));
        }
        return rspResult;
    }

    /**
     *
     * @param file
     * @return
     */
    public CustomResult copyFile(FileInfo file){
        CustomResult rspResult = new CustomResult();
        try{
            HashMap<String, Object> reqParams = new HashMap();
            reqParams.put("id", file.getId());
            reqParams.put("generalOrderID", file.getOrderId());
            reqParams.put("objectID", file.getObjectID());

            String frameWorkApi = String.format("%s/FrameWorkApi/file/copyTbfile", interfaceConfig.getBaseUrl());

            HttpResult<FileInfo> fileResult = HttpClientUtil.post(frameWorkApi, reqParams, new TypeReference<HttpResult<FileInfo>>() {});
            if (fileResult == null || !fileResult.isSuccess()){
                return null;
            }
            FileInfo rspFile = fileResult.getResult();
            rspResult.setData(rspFile);
            rspResult.setSuccess(rspFile != null);
        }catch (Exception ex){
            logger.error("copy文件({})异常：",file.getId(), ex);
            rspResult.setMsg(String.format("copy文件(%s)异常：%s", file.getId(), ex.getMessage()));
        }
        return rspResult;
    }

//    public CustomResult copyFile(String fileId, String orderId, String fileInstanceId) {
//        FileInfo reqFile = new FileInfo();
//        reqFile.setId(fileId);
//        reqFile.setOrderId(orderId);
//        reqFile.setObjectID(fileInstanceId);
//
//        CustomResult<com.sgs.preorder.facade.model.info.FileInfo> rspResult = fileClient.copyFile(reqFile);
//
//
//    }

    public CustomResult uploadFile(UploadTestReportInfo uploadTestReportInfo, AbstractResource resource, Function<FileInfo, CustomResult> function){
        CustomResult result = new CustomResult();
        try{
            OrderInfoDto order = orderClient.getOrderInfoByOrderNo(uploadTestReportInfo.getOrderNo());
            if (order == null){
                result.setMsg(String.format("未找到对应的OrderNo(%s)信息.", uploadTestReportInfo.getOrderNo()));
                return result;
            }

            StringBuffer reqParams = new StringBuffer();
            reqParams.append(String.format("systemID=%s", SgsSystem.GPO.getSgsSystemId()));
            reqParams.append(String.format("&buID=%s", order.getBUID()));
            reqParams.append(String.format("&locationID=%s", order.getLocationID()));
            reqParams.append(String.format("&orderID=%s", order.getID()));
            reqParams.append(String.format("&objectID=%s", uploadTestReportInfo.getId()));
            reqParams.append(String.format("&objectType=%s", 2));

            String uploadUrl = String.format("%s/FrameWorkApi/file/doUpload?%s", interfaceConfig.getBaseUrl(), reqParams.toString());
            // 发送rest请求往FrameworkApi接口中存储新的文件
            return CompletableFuture.supplyAsync(() -> {
                String jsonValue = "";
                try{
                    MultiValueMap<String, Object> params = new LinkedMultiValueMap<>();
                    params.add("file", resource);

                    HttpHeaders headers = new HttpHeaders();
                    headers.setContentType(MediaType.MULTIPART_FORM_DATA);
                    HttpEntity<MultiValueMap<String, Object>> httpEntity = new HttpEntity<>(params, headers);

                    jsonValue = restTemplate.postForObject(uploadUrl, httpEntity, String.class);
                    logger.info("FileClient.uploadFile,OrderNo:{}, jsonValue:{}", uploadTestReportInfo.getOrderNo(), jsonValue);
                    BaseResponse<List<FileInfo>> rspResults = JSON.parseObject(jsonValue, new TypeReference<BaseResponse<List<FileInfo>>>() {});
                    List<FileInfo> files = rspResults.getData();
                    if (files != null && !files.isEmpty()){
                        return files.get(0);
                    }
                }catch (Exception ex){
                    logger.error("CompletableFuture.supplyAsync->OrderId：{},JsonValue：{}", order.getID(), jsonValue, ex);
                }
                return null;
            }).thenApply(function).get(30000, TimeUnit.SECONDS);
        }catch (Exception ex){
            logger.error("上传文件({})异常：",uploadTestReportInfo.getId(), ex);
            result.setMsg(String.format("上传文件(%s)异常：%s", uploadTestReportInfo.getId(), ex.getMessage()));
        }
        return result;
    }

    /**
     *
     * @param slimJobDto
     * @param resource
     * @param function
     * @return
     */
	public CustomResult uploadFile(SlimJobDTO slimJobDto,  AbstractResource resource, Function<FileInfo, CustomResult> function) {
        CustomResult result = new CustomResult();
        try{
            OrderInfoDto order = orderClient.getOrderInfoByOrderNo(slimJobDto.getOrderNo());
            if (order == null){
                result.setMsg(String.format("未找到对应的OrderNo(%s)信息.", slimJobDto.getOrderNo()));
                return result;
            }

            StringBuffer reqParams = new StringBuffer();
            reqParams.append(String.format("systemID=%s", SgsSystem.GPO.getSgsSystemId()));
            reqParams.append(String.format("&buID=%s", order.getBUID()));
            reqParams.append(String.format("&locationID=%s", order.getLocationID()));
            reqParams.append(String.format("&orderID=%s", order.getID()));
            reqParams.append(String.format("&objectID=%s", slimJobDto.getSlimJobNo()));
            reqParams.append(String.format("&objectType=%s", 2));

            String uploadUrl = String.format("%s/FrameWorkApi/file/doUpload?%s", interfaceConfig.getBaseUrl(), reqParams.toString());
            // 发送rest请求往FrameworkApi接口中存储新的文件
            return CompletableFuture.supplyAsync(() -> {
                MultiValueMap<String, Object> params = new LinkedMultiValueMap<>();
                params.add("file", resource);

                HttpHeaders headers = new HttpHeaders();
                headers.setContentType(MediaType.MULTIPART_FORM_DATA);
                HttpEntity<MultiValueMap<String, Object>> httpEntity = new HttpEntity<>(params, headers);

                String jsonValue = restTemplate.postForObject(uploadUrl, httpEntity, String.class);
                logger.info("FileClient.uploadFile_{}：", order.getID(), jsonValue);
                UploadFileRsp rspResults = JSON.parseObject(jsonValue, new TypeReference<UploadFileRsp>() {});
                List<FileInfo> files = rspResults.getData();
                if (files != null && !files.isEmpty()){
                    return files.get(0);
                }
                return null;
            }).thenApply(function).get(30000, TimeUnit.SECONDS);
        }catch (Exception ex){
            logger.error("上传文件({})异常：", slimJobDto.getId(), ex);
            result.setMsg(String.format("上传文件(%s)异常：%s", slimJobDto.getId(), ex.getMessage()));
        }
        return result;
    }

    /**
     *
     * @param file
     * @param resource
     * @param function
     * @return
     */
    public CustomResult uploadFile(UploadFileInfo file, AbstractResource resource, Function<FileInfo, CustomResult> function) {
        CustomResult result = new CustomResult();
        try{
            OrderIdReq reqObject = new OrderIdReq();
            reqObject.setOrderId(file.getOrderId());

            OrderInfoDto order = StringUtils.isNotBlank(file.getOrderId()) ?
                    orderClient.getOrderInfoByOrderId(reqObject) :
                    orderClient.getOrderInfoByOrderNo(file.getOrderNo());
            if (order == null){
                result.setMsg(String.format("未找到对应的OrderNo(%s)信息.", file.getOrderNo()));
                return result;
            }

            StringBuffer reqParams = new StringBuffer();
            reqParams.append(String.format("systemID=%s", SgsSystem.GPO.getSgsSystemId()));
            reqParams.append(String.format("&buID=%s", order.getBUID()));
            reqParams.append(String.format("&locationID=%s", order.getLocationID()));
            reqParams.append(String.format("&orderID=%s", order.getID()));
            reqParams.append(String.format("&objectID=%s", file.getId()));
            reqParams.append(String.format("&objectType=%s", 2));

            String uploadUrl = String.format("%s/FrameWorkApi/file/doUpload?%s", interfaceConfig.getBaseUrl(), reqParams.toString());
            // 发送rest请求往FrameworkApi接口中存储新的文件
            return CompletableFuture.supplyAsync(() -> {
                MultiValueMap<String, Object> params = new LinkedMultiValueMap<>();
                params.add("file", resource);

                HttpHeaders headers = new HttpHeaders();
                headers.setContentType(MediaType.MULTIPART_FORM_DATA);
                HttpEntity<MultiValueMap<String, Object>> httpEntity = new HttpEntity<>(params, headers);

                String jsonValue = restTemplate.postForObject(uploadUrl, httpEntity, String.class);
                logger.info("FileClient.uploadFile_{}：", order.getID(), jsonValue);
                UploadFileRsp rspResults = JSON.parseObject(jsonValue, new TypeReference<UploadFileRsp>() {});
                List<FileInfo> files = rspResults.getData();
                if (files != null && !files.isEmpty()){
                    return files.get(0);
                }
                return null;
            }).thenApply(function).get(30000, TimeUnit.SECONDS);
        }catch (Exception ex){
            logger.error("上传文件({})异常：", file.getId(), ex);
            result.setMsg(String.format("上传文件(%s)异常：%s", file.getId(), ex.getMessage()));
        }
        return result;
    }


    /**
     * 这个方法 可以允许上传文件不加时间戳，使用前一定要确认是否需要覆盖之前的文件，不加时间戳会覆盖原文件
     * 使用要慎用！！！！！！！！！！
     * @param resource
     * @param function
     * @return
     */
    public CustomResult uploadFile(Boolean isTimestamp, AbstractResource resource, Function<FileInfo, CustomResult> function) {
        if(isTimestamp==null){
            isTimestamp = Boolean.TRUE;
        }
        CustomResult result = new CustomResult();
        try{
            OrderIdReq reqObject = new OrderIdReq();

            StringBuffer reqParams = new StringBuffer();
            reqParams.append(String.format("systemID=%s", SgsSystem.GPO.getSgsSystemId()));
            reqParams.append(String.format("&buID=%s", 3));
            reqParams.append(String.format("&locationID=%s", 11));
            reqParams.append(String.format("&objectType=%s", 2));
            reqParams.append(String.format("&isTimestamp=%s", isTimestamp));

            String uploadUrl = String.format("%s/FrameWorkApi/file/doUpload?%s", interfaceConfig.getBaseUrl(), reqParams.toString());
            // 发送rest请求往FrameworkApi接口中存储新的文件
            return CompletableFuture.supplyAsync(() -> {
                MultiValueMap<String, Object> params = new LinkedMultiValueMap<>();
                params.add("file", resource);

                HttpHeaders headers = new HttpHeaders();
                headers.setContentType(MediaType.MULTIPART_FORM_DATA);
                HttpEntity<MultiValueMap<String, Object>> httpEntity = new HttpEntity<>(params, headers);

                String jsonValue = restTemplate.postForObject(uploadUrl, httpEntity, String.class);
                logger.info("FileClient.uploadFile_{}：", jsonValue);
                UploadFileRsp rspResults = JSON.parseObject(jsonValue, new TypeReference<UploadFileRsp>() {});
                List<FileInfo> files = rspResults.getData();
                if (files != null && !files.isEmpty()){
                    return files.get(0);
                }
                return null;
            }).thenApply(function).get(30000, TimeUnit.SECONDS);
        }catch (Exception ex){
            logger.error("上传文件({})异常：",  ex);
            result.setMsg(String.format("上传文件异常：%s",  ex.getMessage()));
        }
        return result;
    }

    /**
     *
     * @param file
     * @return
     */
    public boolean saveTbFile(FileInfo file){
        try {
            Map<String, Object> reqParams = Maps.newHashMap();
            reqParams.put("id", file.getId());
            reqParams.put("generalOrderID", file.getOrderId());
            reqParams.put("cloudID", file.getCloudID());
            reqParams.put("objectID", file.getObjectID());
            reqParams.put("attachmentName", file.getFileName());
            reqParams.put("suffixes", file.getSuffixes());
            reqParams.put("systemID", "3");

            /*reqParams.put("size", "1023");*/

            String reqUrl = String.format("%s/FrameWorkApi/file/saveTbFile", interfaceConfig.getBaseUrl());

            String httpResult = HttpClientUtil.post(reqUrl, reqParams);

            return StringUtils.isNotBlank(httpResult) && httpResult.startsWith("success");
        }catch (Exception ex){
            logger.error("FileClient.saveTbFile 信息异常：{}.", ex);
            return false;
        }
    }

    /**
     * 临时紧急解决线上问题.
     * 目前定位是多层嵌套泛型类反序列化失败。改回原始的类型。
     *
     * @param reportFiles
     * @param file
     * @return
     */
    public CustomResult uploadFileWithoutGeneric(ConcurrentHashMap<String, String> reportFiles, UploadFileInfo file){
        CustomResult result = new CustomResult();
        try{
            StringBuffer reqParams = new StringBuffer();
            reqParams.append(String.format("systemID=%s", SgsSystem.GPO.getSgsSystemId()));
            reqParams.append(String.format("&buID=%s", file.getBuId()));
            reqParams.append(String.format("&locationID=%s", file.getLocationID()));
            reqParams.append(String.format("&orderID=%s", file.getOrderId()));
            reqParams.append(String.format("&objectID=%s", file.getId()));
            reqParams.append(String.format("&objectType=%s", 2));

            /*String uploadUrl = String.format("http://10.205.139.111/FrameWorkApi/file/doUpload?systemID=3&buID=3&locationID=11&orderID=%s&objectID=%s&objectType=2",testLine.getId(),testLine.getGeneralorderinstanceid());*/
            String uploadUrl = String.format("%s/FrameWorkApi/file/doUpload?%s", interfaceConfig.getBaseUrl(), reqParams.toString());

            String reqUrl = java.net.URLDecoder.decode(file.getReportUrl(),"UTF-8");
            UrlResource resource = new UrlResource(reqUrl){
                @Override
                public String getFilename() throws IllegalStateException {
                    // fileLocation
                    return FilenameUtils.getName(reqUrl);
                }
            };

            if (!resource.exists()){
                result.setMsg("该文件不存在或已过期.");
                return result;
            }

            MultiValueMap<String, Object> params = new LinkedMultiValueMap<>();
            params.add("file", resource);

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.MULTIPART_FORM_DATA);
            HttpEntity<MultiValueMap<String, Object>> httpEntity = new HttpEntity<>(params, headers);

            // 发送rest请求往FrameworkApi接口中存储新的文件
            String jsonValue = restTemplate.postForObject(uploadUrl, httpEntity, String.class);
            logger.info("FileClient.uploadFileWithoutGeneric返回，orderId:{}, jsonValue:{}", file.getOrderId(), jsonValue);
            UploadFileRsp rspResults = JSON.parseObject(jsonValue, new TypeReference<UploadFileRsp>() {});
            List<FileInfo> files = rspResults.getData();
            if (files == null || files.isEmpty()){
                result.setMsg("上传附件失败返回对象为空.");
                return result;
            }
            reportFiles.put(file.getReportUrl(), files.get(0).getId());
        }catch (Exception ex){
            logger.error("上传文件({})异常：",file.getId(), ex);
            result.setMsg(String.format("上传文件(%s)异常：%s", file.getId(), ex.getMessage()));
        }
        return result;
    }

    /**
     * 临时紧急解决线上问题.
     * 目前定位是多层嵌套泛型类反序列化失败。改回原始的类型。
     * @param uploadTestReportInfo
     * @param resource
     * @param function
     * @return
     */
    public CustomResult uploadFileWithoutGeneric (UploadTestReportInfo uploadTestReportInfo, AbstractResource resource, Function<FileInfo, CustomResult> function){
        CustomResult result = new CustomResult();
        try{
            OrderInfoDto order = orderClient.getOrderInfoByOrderNo(uploadTestReportInfo.getOrderNo());
            if (order == null){
                result.setMsg(String.format("未找到对应的OrderNo(%s)信息.", uploadTestReportInfo.getOrderNo()));
                return result;
            }

            StringBuffer reqParams = new StringBuffer();
            reqParams.append(String.format("systemID=%s", SgsSystem.GPO.getSgsSystemId()));
            reqParams.append(String.format("&buID=%s", order.getBUID()));
            reqParams.append(String.format("&locationID=%s", order.getLocationID()));
            reqParams.append(String.format("&orderID=%s", order.getID()));
            reqParams.append(String.format("&objectID=%s", uploadTestReportInfo.getId()));
            reqParams.append(String.format("&objectType=%s", 2));

            String uploadUrl = String.format("%s/FrameWorkApi/file/doUpload?%s", interfaceConfig.getBaseUrl(), reqParams.toString());
            // 发送rest请求往FrameworkApi接口中存储新的文件
            return CompletableFuture.supplyAsync(() -> {
                String jsonValue = "";
                try{
                    MultiValueMap<String, Object> params = new LinkedMultiValueMap<>();
                    params.add("file", resource);

                    HttpHeaders headers = new HttpHeaders();
                    headers.setContentType(MediaType.MULTIPART_FORM_DATA);
                    HttpEntity<MultiValueMap<String, Object>> httpEntity = new HttpEntity<>(params, headers);

                    jsonValue = restTemplate.postForObject(uploadUrl, httpEntity, String.class);
                    logger.info("FileClient.uploadFileWithoutGeneric,OrderNo:{}, jsonValue:{}", uploadTestReportInfo.getOrderNo(), jsonValue);
                    UploadFileRsp rspResults = JSON.parseObject(jsonValue, new TypeReference<UploadFileRsp>() {});
                    List<FileInfo> files = rspResults.getData();
                    if (files != null && !files.isEmpty()){
                        return files.get(0);
                    }
                }catch (Exception ex){
                    logger.error("CompletableFuture.supplyAsync->OrderId：{},JsonValue：{}", order.getID(), jsonValue, ex);
                }
                return null;
            }).thenApply(function).get(30000, TimeUnit.SECONDS);
        }catch (Exception ex){
            logger.error("上传文件({})异常：",uploadTestReportInfo.getId(), ex);
            result.setMsg(String.format("上传文件(%s)异常：%s", uploadTestReportInfo.getId(), ex.getMessage()));
        }
        return result;
    }

    /**
     * 根据fileId获取file对象
     * @param fileId
     * @return
     */
    public FileDTO getFramewordFileByFileId(String fileId) {
        if (StringUtils.isBlank(fileId)) {
            return null;
        }

        String url = interfaceConfig.getBaseUrl() + "/FrameWorkApi/file/query";

        Map<String,Object> params= Maps.newHashMap();
        Map<String,Object> value= Maps.newHashMap();
        params.put("queryParams", value);
        value.put("systemID", SysConstants.FRAMEWORK_SYSTEMID);
        value.put("id", fileId);

        String respStr = StringUtils.EMPTY;
        try {
            respStr = HttpClientUtil.postJson(url, JSONObject.toJSONString(params));
        } catch (Exception e) {
            logger.error("根据fileId="+fileId+"+获取file异常", e);
        }

        if(StringUtils.isBlank(respStr)) {
            return null;
        }

        List<FileDTO> fileDTOS = JSONArray.parseArray(respStr, FileDTO.class);
        if(CollectionUtils.isEmpty(fileDTOS)){
            return null;
        }

        return fileDTOS.get(0);
    }

    /**
     *
     * @param ossurl
     * @param savePath
     * @throws Exception
     */
    public void saveFileToPath(String ossurl, String savePath) throws Exception{
        URL url = new URL(ossurl);
        HttpURLConnection conn = (HttpURLConnection) url.openConnection();
        conn.setConnectTimeout(30*1000);
        conn.setRequestProperty("User-Agent", "Mozilla/4.0 (compatible; MSIE 5.0; Windows NT; DigExt)");
        try (InputStream inputStream = conn.getInputStream();){
            //获取自己数组
            byte[] getData = readInputStream(inputStream);
            FileOutputStream fos = new FileOutputStream(savePath);
            try{
                fos.write(getData);
            }catch (Exception e){
                logger.info("文件写入失败：{}",e.getMessage());
            } finally{
                fos.close();
            }
        }
    }
    /**
     * 从输入流中获取字节数组
     * @param inputStream
     * @return
     * @throws IOException
     */
    public  byte[] readInputStream(InputStream inputStream) throws IOException {
        byte[] buffer = new byte[1024];
        int len = 0;
        try(ByteArrayOutputStream bos = new ByteArrayOutputStream()){
            while((len = inputStream.read(buffer)) != -1) {
                bos.write(buffer, 0, len);
            }
            return bos.toByteArray();
        }
    }

    public UploadFileRsp uploadFile(String uploadUrl, File file, String buid) {
        UploadFileRsp rspResults = null;
        try {
            logger.info("\r\nUpload File Url:{\r\n\t" + uploadUrl);
            uploadUrl=uploadUrl.concat(String.format("systemID=%s", SgsSystem.GPO.getSgsSystemId()));
            uploadUrl=uploadUrl.concat(String.format("&buID=%s", buid));
            UrlResource resource = new UrlResource("file:///"+file.getAbsolutePath()) {
                @Override
                public String getFilename() throws IllegalStateException {
                    // fileLocation
                    return FilenameUtils.getName(file.getName());
                }
            };
            MultiValueMap<String, Object> params = new LinkedMultiValueMap<>();
            params.add("file", resource);

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.MULTIPART_FORM_DATA);
            HttpEntity<MultiValueMap<String, Object>> httpEntity = new HttpEntity<>(params, headers);
            // 发送rest请求往FrameworkApi接口中存储新的文件
            RestTemplate restTemplate=new RestTemplate();
            String jsonValue = restTemplate.postForObject(uploadUrl, httpEntity, String.class);
            logger.info("FileClient.uploadFile返回jsonValue:{}", jsonValue);
            rspResults = JSON.parseObject(jsonValue, new TypeReference<UploadFileRsp>() {});
            List<FileInfo> files = rspResults.getData();
            if (files == null || files.isEmpty()){
                rspResults.setMessage("上传附件失败返回对象为空.");
            }
        }catch(Exception e) {
            e.printStackTrace();
            logger.error("Call interface error!,url={}", uploadUrl);
        }
        return rspResults;
    }

    public JSONObject uploadFileToFramework(File file, String buid) throws Exception {
        // TODO Auto-generated method stub
        String uploadFileUrl = interfaceConfig.getBaseUrl()+"/FrameWorkApi/file/doUpload";
        MultipartEntityBuilder multipartEntityBuilder = MultipartEntityBuilder.create();
        multipartEntityBuilder.addBinaryBody("file", file);
        multipartEntityBuilder.addTextBody("systemID", SgsSystem.GPO.getSgsSystemId()+"");
        multipartEntityBuilder.addTextBody("buID", buid);
        org.apache.http.HttpEntity httpEntity = multipartEntityBuilder.build();

        logger.info("===============上传文件到framework，url:{},fileName:{}=================",uploadFileUrl,file.getName());

        HttpPost httpPost = new HttpPost(uploadFileUrl);
        httpPost.setEntity(httpEntity);

        CloseableHttpClient httpClient = HttpClientBuilder.create().build();
        HttpResponse httpResponse = httpClient.execute(httpPost);
        int statusCode = httpResponse.getStatusLine().getStatusCode();
        JSONObject jsonObject = null;
        logger.info("====上传文件到framework返回状态：{}=====",statusCode);
        if (statusCode == HttpStatus.SC_OK) {
            // 上传成功
            String resultStr = EntityUtils.toString(httpResponse.getEntity());
            jsonObject = JSONObject.parseObject(resultStr);
        }
        logger.info("====上传文件到framework返回结果：{}=====",jsonObject.toString());
        return jsonObject;

    }

    public String getFileUrlByCloudId(String cloudId){
        String filePath = null;
        String apiUrl = interfaceConfig.getBaseUrl()+"/FrameWorkApi/file/downloadByCloudID";
        try {
            Map<String, Object> mapData=new HashMap();
            mapData.put("systemID", SgsSystem.GPO.getSgsSystemId()+"");
            mapData.put("cloudID",cloudId);
            mapData.put("networkType",null);
            filePath = HttpClientUtil.post(apiUrl, mapData);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return filePath;
    }

    /**
     *
     * @return
     */
    public String doc2pdf(Doc2FdfDTO doc2FdfDTO){
        String url = interfaceConfig.getDoc2PDFApi()+"/ReportFile/PushReportDocument?appid=NGPO&token=";
        Map<String, Object> params = new HashMap<String, Object>();

        //GPO2-5177  DSS2PDF 接口优化，替换为CloudID方式
        params.put("UUID",UUID.randomUUID().toString());
        // 废弃
        //params.put("requestId",UUID.randomUUID().toString());
        params.put("fileName",doc2FdfDTO.getFileName());
        params.put("application","GPO");
        if (Func.equals(DssReportFileType.DRAFT.getCode(),doc2FdfDTO.getReportType())){
            params.put("watermark",DssWaterMarkType.DRAFT.message());
        }
        params.put("dss", doc2FdfDTO.getDss());
        params.put("copy", Func.isNotEmpty(doc2FdfDTO.getCopy())?doc2FdfDTO.getCopy():DssCopyType.NO.getCode());
        params.put("actionType", DssActionType.PUSH_DSS.getCode());
        params.put("actionTime",doc2FdfDTO.getActionTime());
        if(Func.isNotEmpty(doc2FdfDTO.getSealCode())){
            params.put("sealCode",doc2FdfDTO.getSealCode());
        }
        params.put("resolution",doc2FdfDTO.getResolution());
        params.put("SrcCloudFilePath",doc2FdfDTO.getReportFile());
        params.put("reportType",Func.equals(DssReportFileType.DRAFT.getCode(),doc2FdfDTO.getReportType())?DssReportFileType.DRAFT.getCode():DssReportFileType.FINAL.getCode());
        Map<String,Object> sourceSystemParam = new HashMap<>();
        sourceSystemParam.put("productLineCode",doc2FdfDTO.getProductLineCode());
        sourceSystemParam.put("labCode", SystemContextHolder.getLabCode());
        sourceSystemParam.put("objectNo",doc2FdfDTO.getObjectNo());
        sourceSystemParam.put("reportType",doc2FdfDTO.getReportType());
        sourceSystemParam.put("language",doc2FdfDTO.getLanguage());
        sourceSystemParam.put("fileName",doc2FdfDTO.getFileName());
        sourceSystemParam.put("dss",doc2FdfDTO.getDss());
        //GPO2-12634
        sourceSystemParam.put("requestId",Func.md5Hex(doc2FdfDTO.getReportFile()));
        params.put("sourceSystemParam",sourceSystemParam);

        logger.info("=========PushReport. URL:{},参数：{}",url, JSON.toJSON(params));
        String result = null;
        try {
            result = HttpClientUtil.doPost(url, params,String.class);
            logger.info("=========PushReport放回结果. URL:{},返回值：{}",url,result);
            return result;
        } catch (Exception e) {
            logger.error(e.getMessage(),e);
        }
        return null;
    }

    public String pullDoc2pdf(String fileName,String actionTime,String uuid,boolean res){
        String url = interfaceConfig.getDoc2PDFApi()+"/ReportFile/PullReportDocument?appid=GPO&token=";
        Map<String, Object> params = new HashMap<String, Object>();
        params.put("appid", "NGPO");
        params.put("UUID", uuid);
        params.put("FileName",fileName);
        params.put("ActionType",2);
        params.put("ActionTime",actionTime);
        params.put("Result",res);
        logger.info("=========PullReport. URL:{},参数：{}",url, JSON.toJSON(params));
        String result = null;
        try {
            result = HttpClientUtil.doPost(url, params,String.class);
            logger.info("=========PullReport放回结果. URL:{},返回值：{}",url,result);
            return result;
        } catch (Exception e) {
            logger.error(e.getMessage(),e);
        }
        return null;
    }

    public String pullReportDocument(String requestId,String actionTime,boolean isSuccess){
        String url = interfaceConfig.getDoc2PDFApi()+"/ReportFile/PullReportDocument?appid=GPO&token=";
        Map<String, Object> params = new HashMap<String, Object>();
        params.put("appid", "NGPO");
        params.put("UUID", requestId);
        params.put("ActionType",2);
        params.put("ActionTime",actionTime);
        params.put("Result",isSuccess);
        logger.info("=========PullReport. URL:{},参数：{}",url, JSON.toJSON(params));
        String result = null;
        try {
            result = HttpClientUtil.doPost(url, params,String.class);
            logger.info("=========PullReport放回结果. URL:{},返回值：{}",url,result);
            return result;
        } catch (Exception e) {
            logger.error(e.getMessage(),e);
        }
        return null;
    }

    /**
     * 批量上传文件到frameWork,只有全部成功时才返回
     * 上传过称中出现失败，则将前面上传成功的文件删除掉
     * @param frameworkUploadFileReq
     * @param multipartFiles
     * @return
     */
    public BaseResponse<List<UploadFileResult.Data>> uploadFileToFramework(FrameworkUploadFileReq frameworkUploadFileReq, MultipartFile[] multipartFiles){
        if(Func.isEmpty(frameworkUploadFileReq) || Func.isEmpty(frameworkUploadFileReq.getParentDirName()) || Func.isEmpty(frameworkUploadFileReq.getBuId())){
            return BaseResponse.newFailInstance("upload Param is Empty");
        }
        //执行上传
        String tempFilePath = gpoConfig.getRootPath() + File.separator + "file" +File.separator +"gpn"+ File.separator +"reworkfile";
        List<UploadFileResult.Data> uploadSuccessFileList = new ArrayList<>();
        if(Func.isNotEmpty(multipartFiles)){
            for (MultipartFile multipartFile : multipartFiles) {
                String originalFilename = multipartFile.getOriginalFilename();
                File fileSourcePath = new File(tempFilePath);
                File uploadFile = new File(fileSourcePath, originalFilename);
              /*  InputStream ins = null;
                OutputStream os = null;*/
                try {
//                    ins = multipartFile.getInputStream();
                    long startDate = System.currentTimeMillis();
                    if (!fileSourcePath.exists()) {
                        fileSourcePath.mkdirs();
                    }
                    if (uploadFile.exists()) {
                        uploadFile.delete();
                    }

                    multipartFile.transferTo(uploadFile);
                    logger.info(frameworkUploadFileReq.getObjectId()+"临时文件上传,耗时："+(System.currentTimeMillis()-startDate)+"毫秒");
                    long startDate2 = System.currentTimeMillis();
                    UploadFileResult uploadFileResult = frameWorkClient.doUploadFile(uploadFile, frameworkUploadFileReq.getBuId() + "", frameworkUploadFileReq.getObjectId(), frameworkUploadFileReq.getObjectType());
                    logger.info(frameworkUploadFileReq.getObjectId()+"文件上传到framework,耗时："+(System.currentTimeMillis()-startDate2)+"毫秒");

                    //上传失败
                    if(Func.isEmpty(uploadFileResult) || uploadFileResult.getStatus()!=200 || Func.isEmpty(uploadFileResult.getData()) || Func.isEmpty(uploadFileResult.getData().get(0).getCloudID())){
                        //把其他的文件删除
                        if(Func.isNotEmpty(uploadSuccessFileList)){
                            //调用接口删除
                            for (UploadFileResult.Data item : uploadSuccessFileList) {
                                if(Func.isNotEmpty(item) && Func.isNotEmpty(item.getCloudID())){
                                    int deleteFileCount = frameWorkClient.deleteFile(item.getCloudID());
                                }
                            }
                        }
                        return BaseResponse.newFailInstance("upload File Fail");
                    }else{
                        String cloudID = uploadFileResult.getData().get(0).getCloudID();
                        uploadSuccessFileList.add(uploadFileResult.getData().get(0));
                    }
                } catch (Exception e) {
                    logger.info("uploadFileToFramework exception:{}",e);
                    //把其他的文件删除
                    for (UploadFileResult.Data item : uploadSuccessFileList) {
                        if(Func.isNotEmpty(item) && Func.isNotEmpty(item.getCloudID())){
                            int deleteFileCount = frameWorkClient.deleteFile(item.getCloudID());
                        }
                    }
                } finally {
                        FileUtils.deleteQuietly(uploadFile);
                }
            }
        }
        return BaseResponse.newSuccessInstance(uploadSuccessFileList);
    }

    public BaseResponse<List<FileDTO>> queryFile(List<String> objectIdList,Integer objectType){
        if(Func.isEmpty(objectIdList)){
            logger.info("queryFile from framework fail,objectId is empty");
            return  BaseResponse.newFailInstance("objectId can not empty");
        }

        List<FileDTO> fileDTOList = new ArrayList<>();
        String url = interfaceConfig.getBaseUrl() + "/FrameWorkApi/file/query";
        Map<String,Object> params= Maps.newHashMap();
        Map<String,Object> value= Maps.newHashMap();
        List<Map<String,Object>>  objectList = new ArrayList<>();
        for (String objectId : objectIdList) {
            Map<String,Object> objectListMap= Maps.newHashMap();
            objectListMap.put("objectId",objectId);
            objectListMap.put("objectType",objectType);
            objectList.add(objectListMap);
        }
        value.put("systemID", SgsSystem.GPO.getSgsSystemId());
        value.put("objectList", JSON.toJSON(objectList));
        params.put("queryParams", value);

        String respStr = StringUtils.EMPTY;
        try {
            respStr = HttpClientUtil.postJson(url, JSONObject.toJSONString(params));
            fileDTOList = JSONArray.parseArray(respStr, FileDTO.class);
        } catch (Exception e) {
            logger.error("objectList="+JSON.toJSONString(objectIdList)+"+获取file异常:{}", e);
        }
        return BaseResponse.newSuccessInstance(fileDTOList);
    }

}
