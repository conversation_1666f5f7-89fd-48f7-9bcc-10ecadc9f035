package com.sgs.otsnotes.integration.trimslocal;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.core.base.ResponseCode;
import com.sgs.framework.model.enums.LanguageType;
import com.sgs.framework.tool.utils.Func;
import com.sgs.framework.tool.utils.ProductLineContextHolder;
import com.sgs.otsnotes.facade.model.enums.TestLineStatus;
import com.sgs.otsnotes.facade.model.gpn.testline.info.TestLineNameInfo;
import com.sgs.otsnotes.facade.model.gpn.testline.info.TestLineNameLanguageInfo;
import com.sgs.trimslocal.facade.ITestLineFacade;
import com.sgs.trimslocal.facade.model.testline.req.*;
import com.sgs.trimslocal.facade.model.testline.rsp.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

@Component
@Slf4j
public class TestLineClient {

    @Autowired
    private ITestLineFacade localTestLineFacade;
    /*@Autowired
    private TestLineMapper testLineMapper;
    @Autowired
    private OrderLanguageRelMapper orderLanguageRelMapper;
    @Autowired
    private OrderCitationRelMapper orderCitationRelMapper;*/
    public BaseResponse<List<TestLineNameInfo>> getTestLineEvaluationAlias(List<GetTestLineEvaluationAliasItemReq> testLineEvaluationAliasItemReqs){
        // 检查入参是否完整
        if(Func.isEmpty(testLineEvaluationAliasItemReqs)){
            return BaseResponse.newFailInstance("请指定查询条件");
        }
        // 组装查询条件
        GetTestLineEvaluationAliasReq testLineEvaluationAliasReq = new GetTestLineEvaluationAliasReq();
        testLineEvaluationAliasReq.setIds(testLineEvaluationAliasItemReqs);
        if(Func.isEmpty(testLineEvaluationAliasReq.getCallerBU())) {
            testLineEvaluationAliasReq.setCallerBU(ProductLineContextHolder.getProductLineCode());
        }
        if(Func.isEmpty(testLineEvaluationAliasReq.getLanguageIds())){
            Set<Integer> languageIds = new HashSet<>();
            languageIds.add(1);
            languageIds.add(2);
            testLineEvaluationAliasReq.setLanguageIds(languageIds);
        }
        // 调用接口查询
        log.info("Local Trims , getTestLineEvaluationAlias Params:{}",Func.toJson(testLineEvaluationAliasReq));
        com.sgs.trimslocal.facade.model.common.BaseResponse<List<TestLineEvaluationAliasRsp>> response = localTestLineFacade.getTestLineEvaluationAlias(testLineEvaluationAliasReq);
        log.info("Local Trims , getTestLineEvaluationAlias Response:{}",Func.toJson(response));
        if(response.getStatus() == ResponseCode.SUCCESS.getCode()){
            List<TestLineNameInfo> testLineNameInfos = new ArrayList<>();
            if(Func.isNotEmpty(response.getData())){
                response.getData().forEach(testLineEvaluationAliasRsp -> {
                    TestLineNameInfo testLineNameInfo = new TestLineNameInfo();
                    testLineNameInfo.setPpArtifactRelId((long) testLineEvaluationAliasRsp.getPpArtifactRelId());
                    testLineNameInfo.setCitationBaseId(Func.toLong(testLineEvaluationAliasRsp.getCitationBaseId()));
                    testLineNameInfo.setTestLineName(testLineEvaluationAliasRsp.getEvaluationAlias());

                    // 组装语言部分
                    List<TestLineNameLanguageInfo> languages = new ArrayList<>();
                    TestLineNameLanguageInfo testItemEN = new TestLineNameLanguageInfo();
                    testItemEN.setLanguageId(LanguageType.English.getLanguageId());
                    testItemEN.setTestLineName(testLineEvaluationAliasRsp.getEvaluationAlias());
                    languages.add(testItemEN);
                    if(Func.isNotEmpty(testLineEvaluationAliasRsp.getLanguages())){
                        testLineEvaluationAliasRsp.getLanguages().forEach(testItemLanguage -> {
                            TestLineNameLanguageInfo itemLanguageInfo = new TestLineNameLanguageInfo();
                            itemLanguageInfo.setLanguageId(testItemLanguage.getLanguageId());
                            itemLanguageInfo.setTestLineName(testItemLanguage.getEvaluationAlias());
                            languages.add(itemLanguageInfo);
                        });
                    }
                    testLineNameInfo.setLanguages(languages);
                    testLineNameInfos.add(testLineNameInfo);
                });
            }
            return BaseResponse.newSuccessInstance(testLineNameInfos);
        }else{
            return BaseResponse.newFailInstance(response.getMessage());
        }
    }

    /**
     *
     * @param queryTestLineReq
     * @return
     */
    public List<QueryTestLineRsp> getTestLineList(QueryTestLineReq queryTestLineReq) {
        List<QueryTestLineRsp> getTestLineRsps = Lists.newArrayList();
        if (queryTestLineReq == null) {
            return getTestLineRsps;
        }
        try {
            log.info("getTestLineList req:{}", JSON.toJSONString(queryTestLineReq));
            com.sgs.trimslocal.facade.model.common.BaseResponse<List<QueryTestLineRsp>> testLineList = localTestLineFacade.getTestLineList(queryTestLineReq);
//            getTestLineRsps = testLineList.getData();
            log.info("getTestLineList res:{}", JSON.toJSONString(testLineList));
            if(Func.isNotEmpty(testLineList.getData())) {
                PageInfo pageInfo = new PageInfo();
                pageInfo = (PageInfo) testLineList.getData();
                getTestLineRsps = pageInfo.getList();
            }
        } catch (Exception e) {
            log.error("TrimsLocal.getTestLineBaseInfo 获取信息失败:{}", e);
        }
        return getTestLineRsps;
    }

    /**
     *
     * @param testLineBaseId
     * @param languageType
     * @return
     */
    public GetTestLineBaseInfoRsp getTestLineBaseInfo(Long testLineBaseId, LanguageType languageType, Set<Long> langBaseIds) {
        if (testLineBaseId == null || testLineBaseId.longValue() <= 0){
            return null;
        }
        List<GetTestLineBaseInfoRsp> testLines = this.getTestLineBaseInfo(Lists.newArrayList(testLineBaseId), languageType, langBaseIds);
        if (testLines == null || testLines.isEmpty()){
            return null;
        }
        return testLines.get(0);
    }

    public List<GetTestLineBaseInfoRsp> getTestLineBaseInfo(List<Long> testLineBaseIds, LanguageType languageType, Set<Long> langBaseIds) {
        List<GetTestLineBaseInfoRsp> getTestLineBaseInfoRsps = Lists.newArrayList();
        if (CollectionUtils.isEmpty(testLineBaseIds)) {
            return getTestLineBaseInfoRsps;
        }
        GetTestLineBaseInfoReq reqObject = new GetTestLineBaseInfoReq();
        reqObject.setTestLineBaseIds(Sets.newHashSet(testLineBaseIds));
        reqObject.setLangBaseIds(langBaseIds);
        if (languageType != null){
            reqObject.setLanguageIds(Lists.newArrayList(languageType.getLanguageId()));
        }
        try {
            log.info("ITestLineFacade.getTestLineBaseInfo req:{}",JSON.toJSONString(reqObject));
            com.sgs.trimslocal.facade.model.common.BaseResponse<List<GetTestLineBaseInfoRsp>> testLineBaseInfo = localTestLineFacade.getTestLineBaseInfoList(reqObject);
            log.info("ITestLineFacade.getTestLineBaseInfo res:{}",JSON.toJSONString(testLineBaseInfo));
            getTestLineBaseInfoRsps = testLineBaseInfo.getData();
        } catch (Exception e) {
            log.error("TrimsLocal.getTestLineBaseInfo 获取信息失败:{}", e);
        }
        return getTestLineBaseInfoRsps;
    }

    public List<TestLineSimplifyInfoRsp> getTestLineSimplifyInfo(TestLineSimplifyInfoReq testLineSimplifyInfoReq){
        if(Func.isEmpty(testLineSimplifyInfoReq)){
            return null;
        }
        List<TestLineSimplifyInfoRsp> testLineSimplifyInfoRspList = new ArrayList<>();
        try {
            log.info("getTestLineSimplifyInfo req:{}", JSON.toJSONString(testLineSimplifyInfoReq));
                com.sgs.trimslocal.facade.model.common.BaseResponse<List<TestLineSimplifyInfoRsp>> testLineSimplifyInfoList = localTestLineFacade.getTestLineSimplifyInfoList(testLineSimplifyInfoReq);
            log.info("getTestLineSimplifyInfo res:{}", JSON.toJSONString(testLineSimplifyInfoList));
            testLineSimplifyInfoRspList = testLineSimplifyInfoList.getData();
        } catch (Exception e) {
            log.error("TrimsLocal.getTestLineSimplifyInfoList error:{}",e);
        }
        return testLineSimplifyInfoRspList;
    }

    public Integer formatTestLineStatus(Integer testLineStatus,Boolean pendingFlag){
        if(Func.isNotEmpty(pendingFlag) && pendingFlag){
            return TestLineStatus.Pending.getStatus();
        }else{
            return testLineStatus;
        }
    }

    public List<QueryPpTestLineRsp> getBatchPpTestLineList(Set<Integer> ppVersionIds,Integer labId,String buCode){
        List<QueryPpTestLineRsp> ppTestLineList = Lists.newArrayList();

        if(Func.isEmpty(ppVersionIds) || Func.isEmpty(labId)){
            return ppTestLineList;
        }

        QueryBatchPpTestLineReq queryBatchPpTestLineReq = new QueryBatchPpTestLineReq();
        List<QueryPpTestLineReq> ppTestLineReqs = Lists.newArrayList();
        ppVersionIds.stream().forEach(ppVersionId->{
            QueryPpTestLineReq queryPpTestLineReq = new QueryPpTestLineReq();
            queryPpTestLineReq.setCallerBU(buCode);
            queryPpTestLineReq.setLabId(labId);
            queryPpTestLineReq.setPpVersionId(ppVersionId);
            List<Integer> languageIds = Lists.newArrayList();
            languageIds.add(LanguageType.Chinese.getLanguageId());
            queryPpTestLineReq.setLanguageIds(languageIds);
            ppTestLineReqs.add(queryPpTestLineReq);
        });
        queryBatchPpTestLineReq.setPpTestLineReqs(ppTestLineReqs);
        queryBatchPpTestLineReq.setCallerBU(buCode);
        log.info("getBatchPpTestLineList req:{}", JSON.toJSONString(queryBatchPpTestLineReq));
        com.sgs.trimslocal.facade.model.common.BaseResponse<List<QueryPpTestLineRsp>> ppTestLineRes = localTestLineFacade.getBatchPpTestLineList(queryBatchPpTestLineReq);
        log.info("getBatchPpTestLineList res:{}", JSON.toJSONString(ppTestLineRes));
        if(Func.isNotEmpty(ppTestLineRes) && Func.isNotEmpty(ppTestLineRes.getData())){
            PageInfo pageInfo = (PageInfo) ppTestLineRes.getData();
            if(Func.isNotEmpty(pageInfo)){
                ppTestLineList = pageInfo.getList();
            }
        }
        return ppTestLineList;
    }

}
