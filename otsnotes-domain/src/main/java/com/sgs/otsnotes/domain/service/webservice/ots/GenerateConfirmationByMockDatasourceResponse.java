/**
 * GenerateConfirmationByMockDatasourceResponse.java
 *
 * This file was auto-generated from WSDL
 * by the Apache Axis 1.4 Apr 22, 2006 (06:55:48 PDT) WSDL2Java emitter.
 */

package com.sgs.otsnotes.domain.service.webservice.ots;

public class GenerateConfirmationByMockDatasourceResponse  implements java.io.Serializable {
    private boolean generateConfirmationByMockDatasourceResult;

    public GenerateConfirmationByMockDatasourceResponse() {
    }

    public GenerateConfirmationByMockDatasourceResponse(
           boolean generateConfirmationByMockDatasourceResult) {
           this.generateConfirmationByMockDatasourceResult = generateConfirmationByMockDatasourceResult;
    }


    /**
     * Gets the generateConfirmationByMockDatasourceResult value for this GenerateConfirmationByMockDatasourceResponse.
     * 
     * @return generateConfirmationByMockDatasourceResult
     */
    public boolean isGenerateConfirmationByMockDatasourceResult() {
        return generateConfirmationByMockDatasourceResult;
    }


    /**
     * Sets the generateConfirmationByMockDatasourceResult value for this GenerateConfirmationByMockDatasourceResponse.
     * 
     * @param generateConfirmationByMockDatasourceResult
     */
    public void setGenerateConfirmationByMockDatasourceResult(boolean generateConfirmationByMockDatasourceResult) {
        this.generateConfirmationByMockDatasourceResult = generateConfirmationByMockDatasourceResult;
    }

    private Object __equalsCalc = null;
    public synchronized boolean equals(Object obj) {
        if (!(obj instanceof GenerateConfirmationByMockDatasourceResponse)) return false;
        GenerateConfirmationByMockDatasourceResponse other = (GenerateConfirmationByMockDatasourceResponse) obj;
        if (obj == null) return false;
        if (this == obj) return true;
        if (__equalsCalc != null) {
            return (__equalsCalc == obj);
        }
        __equalsCalc = obj;
        boolean _equals;
        _equals = true && 
            this.generateConfirmationByMockDatasourceResult == other.isGenerateConfirmationByMockDatasourceResult();
        __equalsCalc = null;
        return _equals;
    }

    private boolean __hashCodeCalc = false;
    public synchronized int hashCode() {
        if (__hashCodeCalc) {
            return 0;
        }
        __hashCodeCalc = true;
        int _hashCode = 1;
        _hashCode += (isGenerateConfirmationByMockDatasourceResult() ? Boolean.TRUE : Boolean.FALSE).hashCode();
        __hashCodeCalc = false;
        return _hashCode;
    }

    // Type metadata
    private static org.apache.axis.description.TypeDesc typeDesc =
        new org.apache.axis.description.TypeDesc(GenerateConfirmationByMockDatasourceResponse.class, true);

    static {
        typeDesc.setXmlType(new javax.xml.namespace.QName("http://ots.sgs.com/", ">GenerateConfirmationByMockDatasourceResponse"));
        org.apache.axis.description.ElementDesc elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("generateConfirmationByMockDatasourceResult");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "GenerateConfirmationByMockDatasourceResult"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "boolean"));
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
    }

    /**
     * Return type metadata object
     */
    public static org.apache.axis.description.TypeDesc getTypeDesc() {
        return typeDesc;
    }

    /**
     * Get Custom Serializer
     */
    public static org.apache.axis.encoding.Serializer getSerializer(
           String mechType,
           Class _javaType,
           javax.xml.namespace.QName _xmlType) {
        return 
          new  org.apache.axis.encoding.ser.BeanSerializer(
            _javaType, _xmlType, typeDesc);
    }

    /**
     * Get Custom Deserializer
     */
    public static org.apache.axis.encoding.Deserializer getDeserializer(
           String mechType,
           Class _javaType,
           javax.xml.namespace.QName _xmlType) {
        return 
          new  org.apache.axis.encoding.ser.BeanDeserializer(
            _javaType, _xmlType, typeDesc);
    }

}
