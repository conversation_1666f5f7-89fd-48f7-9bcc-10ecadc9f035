package com.sgs.otsnotes.domain.service.digitalReport;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.beust.jcommander.internal.Lists;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.facade.domain.rsp.BuParamValueRsp;
import com.sgs.framework.model.enums.ReportLanguage;
import com.sgs.framework.model.enums.SlCaseType;
import com.sgs.framework.tool.utils.Func;
import com.sgs.framework.tool.utils.ProductLineContextHolder;
import com.sgs.otsnotes.core.config.InterfaceConfig;
import com.sgs.otsnotes.core.constants.Constants;
import com.sgs.otsnotes.core.util.HttpClientUtil;
import com.sgs.otsnotes.dbstorages.mybatis.extmapper.ReportMapper;
import com.sgs.otsnotes.dbstorages.mybatis.mapper.ReportInfoMapper;
import com.sgs.otsnotes.dbstorages.mybatis.model.ReportInfoExample;
import com.sgs.otsnotes.dbstorages.mybatis.model.ReportInfoPO;
import com.sgs.otsnotes.domain.service.digitalReport.dto.MergedNameReportVO;
import com.sgs.otsnotes.domain.service.digitalReport.dto.ReportTemplateDTO;
import com.sgs.otsnotes.domain.service.digitalReport.dto.TemplateVO;
import com.sgs.otsnotes.domain.service.digitalReport.request.ReportTemplateBatchSettingRequest;
import com.sgs.otsnotes.domain.service.digitalReport.request.ReportTemplateSettingRequest;
import com.sgs.otsnotes.domain.service.digitalReport.response.ReportTemplateBatchRes;
import com.sgs.otsnotes.domain.service.digitalReport.response.ReportTemplateRes;
import com.sgs.otsnotes.domain.service.digitalReport.response.ReportTemplateSettingResponse;
import com.sgs.otsnotes.domain.service.digitalReport.response.v2.ReportTemplateBatchRsp;
import com.sgs.otsnotes.domain.service.gpn.subcontract.ISubContractService;
import com.sgs.otsnotes.domain.service.productlineservice.ProductLineServiceHolder;
import com.sgs.otsnotes.facade.model.common.ResponseCode;
import com.sgs.otsnotes.facade.model.dto.ReportDTO;
import com.sgs.otsnotes.facade.model.enums.GpoCustomerType;
import com.sgs.otsnotes.facade.model.enums.InterfacePropertyEnum;
import com.sgs.otsnotes.facade.model.enums.LanguageTypeDigitalReport;
import com.sgs.otsnotes.facade.model.enums.TemplateSettingTypeEnum;
import com.sgs.otsnotes.facade.model.info.subcontract.SubContractInfo;
import com.sgs.otsnotes.facade.model.info.subcontract.SubContractReportTemplate;
import com.sgs.otsnotes.facade.model.req.GPOSubContractReq;
import com.sgs.otsnotes.facade.model.req.customer.CustomerPkReq;
import com.sgs.otsnotes.facade.model.rsp.customer.CustomerPkRsp;
import com.sgs.otsnotes.integration.CustomerClient;
import com.sgs.otsnotes.integration.FrameWorkClient;
import com.sgs.preorder.facade.OrderFacade;
import com.sgs.preorder.facade.model.dto.customer.CustomerInstanceDTO;
import com.sgs.preorder.facade.model.dto.order.OrderAllDTO;
import com.sgs.preorder.facade.model.dto.order.SimpleOrderDto;
import com.sgs.preorder.facade.model.enums.CaseType;
import com.sgs.preorder.facade.model.enums.CustomerWeightType;
import com.sgs.preorder.facade.model.info.TestRequestInfo;
import com.sgs.preorder.facade.model.req.BuParamReq;
import com.sgs.preorder.facade.model.req.OrderIdReq;
import com.sgs.preorder.facade.model.req.OrderNosReq;
import com.sgs.preorder.facade.model.rsp.TestRequestRsp;
import com.sgs.priceengine.facade.model.DTO.MergedNameReportDTO;
import com.sgs.priceengine.facade.model.request.QueryReportTemplateRequest;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.util.*;
import java.util.stream.Collectors;

import static com.sgs.otsnotes.core.constants.Constants.*;

@Service
public class ReportTemplateClient {
	private final static Logger logger = LoggerFactory.getLogger(ReportTemplateClient.class);

	@Autowired
	private OrderFacade orderFacade;
	@Autowired
	private InterfaceConfig interfaceConfig;
	@Autowired
	private FrameWorkClient frameWorkClient;
	@Autowired
	private CustomerClient customerClient;
	@Autowired
	private ReportMapper reportMapper;
	@Autowired
	private ReportInfoMapper reportInfoMapper;

	public ReportTemplateSettingResponse getReportTemplateSettingUrl(ReportTemplateSettingRequest reportTemplateSettingRequest){

		//需要通过customer 查询出特定模板
//		List<GOrderCustomerInstance> customerInstances =  orderServiceAgent.queryCustomerList(reportTemplateSettingRequest.getOrderId());
//		GOrderCustomerInstance customerInstance = customerInstances.stream().filter(cus -> cus.getCustomerType() == CustomerType.PAYER.getCode()).findFirst().orElse(null);

		ReportTemplateSettingResponse reportTemplateSettingResponse= new ReportTemplateSettingResponse();
		try {
			String jsonResult = HttpClientUtil.postJson(interfaceConfig.getOtsTSUrl()+ InterfacePropertyEnum.TS_reportTemplateSettingUrl.getCode(), JSON.toJSONString(reportTemplateSettingRequest));
			List<TemplateVO> templateVOs =JSONObject.parseArray(jsonResult, TemplateVO.class);
			reportTemplateSettingResponse.setTemplateList(templateVOs);
//			 reportTemplateSettingResponse.setSuccess(true);
		} catch (Exception e) {
			logger.error(e.getMessage(),e);
		}
		return reportTemplateSettingResponse;
	}


	/**
	 * 批量获取模板
	 * @param reportTemplateSettingRequest
	 * @return
	 */
	public BaseResponse<List<ReportTemplateBatchRes>> getBatchReportMergedNameTemplate(ReportTemplateSettingRequest reportTemplateSettingRequest){
		List<ReportTemplateBatchRes> reportTemplateBatchResList= Lists.newArrayList();
		//查询report信息
		ReportInfoExample reportInfoExample = new ReportInfoExample();
		reportInfoExample.createCriteria().andReportNoIn(reportTemplateSettingRequest.getReportNos());
		List<ReportInfoPO> reportInfoPOS = reportInfoMapper.selectByExample(reportInfoExample);
		if (Func.isEmpty(reportInfoPOS) || reportInfoPOS.size() != reportTemplateSettingRequest.getReportNos().size()){
			return BaseResponse.newFailInstance("存在reportId查询到不report");
		}
		List<String> orderNos=reportInfoPOS.stream().map(e->e.getOrderNo()).collect(Collectors.toList());
		//查询订单下客户信息
		OrderIdReq orderIdReq = new OrderIdReq();
		orderIdReq.setProductLineCode(ProductLineContextHolder.getProductLineCode());
		orderIdReq.setOrderNos(orderNos);
		BaseResponse<List<CustomerInstanceDTO>> baseResponse = orderFacade.batchQueryCustomerForPe(orderIdReq);
		if(baseResponse==null||baseResponse.getStatus()!=200){
			return BaseResponse.newFailInstance("查询customer失败");
		}

		//查询订单下testRequest
		OrderIdReq orderIdReq1 = new OrderIdReq();
		orderIdReq1.setProductLineCode(ProductLineContextHolder.getProductLineCode());
		orderIdReq1.setOrderNos(orderNos);
		BaseResponse<List<TestRequestInfo>> baseResponse1 = orderFacade.batchQueryTestRequestForPe(orderIdReq1);
		if(Func.isEmpty(baseResponse1) || Func.isEmpty(baseResponse1.getData())){
			return BaseResponse.newFailInstance("查询报告语言失败");
		}
		List<TestRequestInfo> testRequestInfoList=baseResponse1.getData();

		List<CustomerInstanceDTO> customerList = baseResponse.getData()==null?Lists.newArrayList():baseResponse.getData();

		BuParamReq buParamReq = new BuParamReq();
		buParamReq.setGroupCode(CUSTOMER_PK_GROUP_CODE);
		buParamReq.setParamCode(CUSTOMER_PK_PARAM_CODE);
		buParamReq.setProductLineCode(ProductLineContextHolder.getProductLineCode());
		List<BuParamValueRsp> buParamValueRsps = frameWorkClient.getBuParams(buParamReq);
		Boolean customerPk=false;
		if (Func.isNotEmpty(buParamValueRsps) && Func.equals(CUSTOMER_PK_FLAG,buParamValueRsps.get(0).getParamValue())){
			customerPk = true;
		}

		ReportTemplateBatchSettingRequest reportTemplateBatchSettingRequest = new ReportTemplateBatchSettingRequest();
		List<ReportTemplateSettingRequest> reportTemplateRequestList = new ArrayList<>();
		Map<String,ReportInfoPO> RequestIdReportMap = new HashMap<>();
		for(ReportInfoPO reportInfo:reportInfoPOS){
			List<CustomerInstanceDTO> customerInstanceDTOList=customerList.stream().filter(e->e.getOrderNo().equals(reportInfo.getOrderNo())).collect(Collectors.toList());
			TestRequestInfo objTestRequestInfo=testRequestInfoList.stream().filter(e->e.getOrderNo().equals(reportInfo.getOrderNo())).findFirst().orElse(null);
			ReportTemplateSettingRequest reportTemplateSettingRequest1 = new ReportTemplateSettingRequest();
			reportTemplateSettingRequest1.setTemplateSettingTypeID(TemplateSettingTypeEnum.REPORT.getCode());
			reportTemplateSettingRequest1.setLanguageID(1);
			if(objTestRequestInfo!=null&&Func.isNotEmpty(objTestRequestInfo.getReportLanguage())){
				reportTemplateSettingRequest1.setLanguageID(Integer.valueOf(objTestRequestInfo.getReportLanguage()));
			}
			reportTemplateSettingRequest1.setApplicationID(11);
			reportTemplateSettingRequest1.setBuCode(ProductLineContextHolder.getProductLineCode());
			reportTemplateSettingRequest1.setCustomerList(customerInstanceDTOList);
			reportTemplateSettingRequest1.setRequestId(UUID.randomUUID().toString());
			reportTemplateRequestList.add(reportTemplateSettingRequest1);
			RequestIdReportMap.put(reportTemplateSettingRequest1.getRequestId(),reportInfo);
		}
		reportTemplateBatchSettingRequest.setReportTemplateRequestList(reportTemplateRequestList);
		List<ReportTemplateRes> reportTemplateResList = this.getReportTemplateByCustomerExt(reportTemplateBatchSettingRequest,customerPk);
		if(Func.isNotEmpty(reportTemplateResList)){
			RequestIdReportMap.forEach((k,v)->{
				ReportTemplateRes objReportTemplateRes=reportTemplateResList.stream().filter(e->e.getRequestId().equals(k)).findFirst().orElse(null);
				ReportTemplateBatchRes objReportTemplateBatchRes=new ReportTemplateBatchRes();
				objReportTemplateBatchRes.setReportNo(v.getReportNo());
				if(objReportTemplateRes != null){
					objReportTemplateBatchRes.setReportTemplateDTOS(objReportTemplateRes.getReportTemplateDTOS());
				}
				reportTemplateBatchResList.add(objReportTemplateBatchRes);
			});
		}
		return BaseResponse.newSuccessInstance(reportTemplateBatchResList);
	}


	/**
	 * 获取模板
	 * @param reportTemplateSettingRequest
	 * @return
	 */
	public ReportTemplateRes getReportMergedNameTemplateSettingUrl(ReportTemplateSettingRequest reportTemplateSettingRequest){

		OrderIdReq orderIdReq =  new OrderIdReq();
		orderIdReq.setOrderNo(reportTemplateSettingRequest.getOrderNo());
		orderIdReq.setProductLineCode(ProductLineContextHolder.getProductLineCode());
		String url = interfaceConfig.getOtsTSUrl()+ InterfacePropertyEnum.TS_getMergedNameReportTemplateSetting.getCode();

		// 分包方BU
		String subContractBuCode = null;
		// 客户信息
		List<CustomerInstanceDTO> customerList = null;
		//校验report下是否选择过模板
		String oldTemplateName = null;

		// 查看Subcontract 指定模板
		List<MergedNameReportVO> subcontractAssignTemplates = new ArrayList<>();
		try {
			GPOSubContractReq gpoSubContractReq = new GPOSubContractReq();
			gpoSubContractReq.setProductLineCode(ProductLineContextHolder.getProductLineCode());
			gpoSubContractReq.setOrderNo(reportTemplateSettingRequest.getOrderNo());
			BaseResponse<OrderAllDTO> orderForPe = orderFacade.getOrderForPe(orderIdReq);
			String caseType = "";
			if(Func.isNotEmpty(orderForPe) && orderForPe.isSuccess() && Func.isNotEmpty(orderForPe.getData())){
				caseType = orderForPe.getData().getCaseType();
			}
			if(CaseType.check(caseType, CaseType.IDB)){
				//查询subcontractFrom所在bu
				OrderNosReq orderNosReq = new OrderNosReq();
				orderNosReq.setProductLineCode(ProductLineContextHolder.getProductLineCode());
				orderNosReq.setOrderNos(Arrays.asList(reportTemplateSettingRequest.getOrderNo()));
				BaseResponse<List<SimpleOrderDto>> simpleOrderRes = orderFacade.getSimpleOrderByOrderNos(orderNosReq);
				if(Func.isNotEmpty(simpleOrderRes) && Func.isNotEmpty(simpleOrderRes.getData()) && SlCaseType.checkByCode(caseType,SlCaseType.IDB)){
					SimpleOrderDto simpleOrderDto = simpleOrderRes.getData().get(0);
					subContractBuCode = simpleOrderDto.getRootBuCode();
				}
			}
			GPOSubContractReq req = new GPOSubContractReq();
			req.setProductLineCode(ProductLineContextHolder.getProductLineCode());
			req.setOrderNo(reportTemplateSettingRequest.getOrderNo());
			BaseResponse response = ProductLineServiceHolder.getProductLineService(ISubContractService.class).queryOrderIsSubOrder(req);
			if(ResponseCode.SUCCESS.getCode() == response.getStatus()) {
				if(!ObjectUtils.isEmpty(response.getData())) {
					SubContractInfo subContractInfo = (SubContractInfo) response.getData();
					String reportTemplate = subContractInfo.getReportTemplate();
					if(StringUtils.isNotEmpty(reportTemplate)) {
						SubContractReportTemplate subContractReportTemplate = JSON.parseObject(reportTemplate, SubContractReportTemplate.class);
						ReportTemplateSettingRequest reportTemplateSettingRequestNew = new ReportTemplateSettingRequest();
						BeanUtils.copyProperties(reportTemplateSettingRequest, reportTemplateSettingRequestNew);
						List<Integer> templateIdList = new ArrayList<>();
						templateIdList.add(subContractReportTemplate.getTemplateSettingIdCN());
						templateIdList.add(subContractReportTemplate.getTemplateSettingIdEN());
						reportTemplateSettingRequestNew.setTemplateSettingID(templateIdList);
						logger.info("=====call ts interface，url:{},params:{}=====", url, JSON.toJSONString(reportTemplateSettingRequestNew));
						String jsonResult =HttpClientUtil.postJson(url, JSON.toJSONString(reportTemplateSettingRequestNew));
						List<MergedNameReportVO> templateVOList1 = JSONObject.parseArray(jsonResult, MergedNameReportVO.class);
						for (MergedNameReportVO mergedNameReportVO : templateVOList1) {
							mergedNameReportVO.setProductLineCode(ProductLineContextHolder.getProductLineCode());
							if(Func.isNotEmpty(mergedNameReportVO.getTemplateSettings())){
								mergedNameReportVO.setReportType(mergedNameReportVO.getTemplateSettings().get(0).getReportType());
							}
						}
						subcontractAssignTemplates.addAll(templateVOList1);
					}
				}
			}
			//	customer
			customerList = orderFacade.queryCustomerForPe(orderIdReq).getData();
		} catch (Exception e) {
			logger.error(e.getMessage(),e);
		}
		// 查看选择的模板
		if (Func.isNotEmpty(reportTemplateSettingRequest.getReportNo())){
			//查询report模板
			ReportDTO reportDTO = reportMapper.getByReportNo(reportTemplateSettingRequest.getReportNo());
			if (Func.isNotEmpty(reportDTO) && Func.isNotEmpty(reportDTO.getCoverPageTemplateName())){
				oldTemplateName = reportDTO.getCoverPageTemplateName();
			}
		}
		String labCode = null;
		BaseResponse<OrderAllDTO> orderForPe = orderFacade.getOrderForPe(orderIdReq);
		if(Func.isNotEmpty(orderForPe.getData()) && Func.isNotEmpty(orderForPe.getData().getLabDTO())){
			labCode = orderForPe.getData().getLabDTO().getLabCode();
		}
		reportTemplateSettingRequest.setLabCode(labCode);
		reportTemplateSettingRequest.setCustomerList(customerList);
		reportTemplateSettingRequest.setSubContractBuCode(subContractBuCode);
		reportTemplateSettingRequest.setOldTemplateName(oldTemplateName);
		reportTemplateSettingRequest.setSubcontractAssignTemplates(subcontractAssignTemplates);
		// 查看客户模板
		ReportTemplateBatchSettingRequest reportTemplateBatchSettingRequest = new ReportTemplateBatchSettingRequest();
		reportTemplateBatchSettingRequest.setReportTemplateRequestList(Arrays.asList(reportTemplateSettingRequest));
		List<ReportTemplateRes> reportTemplateResList = this.getReportTemplateByCustomer(reportTemplateBatchSettingRequest);
		ReportTemplateRes reportTemplateRes = new ReportTemplateRes();
		if(Func.isNotEmpty(reportTemplateResList)){
			reportTemplateRes = reportTemplateResList.get(0);
		}
		// 查询订单上设置的GB属性，用来匹配
		OrderIdReq orderNoReq = new OrderIdReq();
		orderNoReq.setProductLineCode(ProductLineContextHolder.getProductLineCode());
		orderNoReq.setOrderNo(reportTemplateSettingRequest.getOrderNo());
		BaseResponse<TestRequestRsp> baseResponse = orderFacade.getTestRequestByOrderNo(orderNoReq);
		if(Func.isNotEmpty(baseResponse) && Func.isNotEmpty(baseResponse.getData())){
			reportTemplateRes.setBusinessProjectType(baseResponse.getData().getBusinessProjectType());
		}
		return reportTemplateRes;
	}

	/**
	 *
	 * @param reportTemplateBatchSettingRequest 基础信息
	 * @return
	 */
	public List<ReportTemplateRes> getReportTemplateByCustomer(ReportTemplateBatchSettingRequest reportTemplateBatchSettingRequest){
		return getReportTemplateByCustomerExt(reportTemplateBatchSettingRequest,null);
	}


	/**
	 *
	 * @param reportTemplateBatchSettingRequest 基础信息
	 * @return
	 */
	public List<ReportTemplateRes> getReportTemplateByCustomerExt(ReportTemplateBatchSettingRequest reportTemplateBatchSettingRequest,Boolean customerPkConfig){
		List<ReportTemplateRes> reportTemplateResList = new ArrayList<>();
		String productLineCode = reportTemplateBatchSettingRequest.getProductLineCode();
		if(Func.isNotEmpty(productLineCode)){
			productLineCode = productLineCode.toUpperCase();
		}else{
			productLineCode = ProductLineContextHolder.getProductLineCode();
		}
		List<ReportTemplateSettingRequest> reportTemplateRequestList = reportTemplateBatchSettingRequest.getReportTemplateRequestList();
		if(Func.isEmpty(reportTemplateRequestList)){
			return null;
		}

		// 查询bu下customerPK配置
		Boolean customerPk = false;

		List<ReportTemplateBatchRsp> reportTemplateBatchRspList = null;
		// 排序,客户下的 + subBuCode + GENERAL
		if(customerPkConfig==null){
			BuParamReq buParamReq = new BuParamReq();
			buParamReq.setGroupCode(CUSTOMER_PK_GROUP_CODE);
			buParamReq.setParamCode(CUSTOMER_PK_PARAM_CODE);
			buParamReq.setProductLineCode(productLineCode);
			List<BuParamValueRsp> buParamValueRsps = frameWorkClient.getBuParams(buParamReq);
			if (Func.isNotEmpty(buParamValueRsps) && Func.equals(CUSTOMER_PK_FLAG,buParamValueRsps.get(0).getParamValue())){
				customerPk = true;
			}
		}else{
			customerPk=customerPkConfig;
		}
		String url = interfaceConfig.getOtsTSUrl()+ InterfacePropertyEnum.TS_getMergedNameReportTemplateSettingList.getCode();
		List<ReportTemplateSettingRequest> getReportTemplateSettingRequestList = new ArrayList<>();

		try {
			for (ReportTemplateSettingRequest reportTemplateSettingRequest : reportTemplateRequestList) {
				reportTemplateSettingRequest.setBuCode(productLineCode);
				if(Func.isEmpty(reportTemplateSettingRequest.getRequestId())){
					reportTemplateSettingRequest.setRequestId(UUID.randomUUID().toString());
				}
				String subContractBuCode = reportTemplateSettingRequest.getSubContractBuCode();
				List<CustomerInstanceDTO> customerList = reportTemplateSettingRequest.getCustomerList();
				List<String> groupCodeAllList = new ArrayList<>();

				//获取所有groupCode
				if (Func.isNotEmpty(customerList)) {
					//OEM
					CustomerInstanceDTO oem = customerList.stream().filter(cus -> GpoCustomerType.OEM.getCode().equals(cus.getCustomerUsage())).
							findFirst().orElse(null);
					//Buyer
					CustomerInstanceDTO buyer = customerList.stream().filter(cus -> GpoCustomerType.Buyer.getCode().equals(cus.getCustomerUsage())).
							findFirst().orElse(null);
					//Agent
					CustomerInstanceDTO agent = customerList.stream().filter(cus -> GpoCustomerType.Agent.getCode().equals(cus.getCustomerUsage())).
							findFirst().orElse(null);
					//现在只支持oem、buyer和agent的pk
					if (customerPk) {
						if (Func.isNotEmpty(oem)) {
							if(Func.isNotEmpty(oem.getBuyerGroup())){
								groupCodeAllList.add(oem.getBuyerGroup());
							}
							if(Func.isNotEmpty(oem.getBossNumber())){
								groupCodeAllList.add(Func.toStr(oem.getBossNumber()));
							}
						} else {
							if (Func.isNotEmpty(buyer) && Func.isNotEmpty(buyer.getBuyerGroup()) && Func.isNotEmpty(agent) && Func.isNotEmpty(agent.getBuyerGroup())) {
								//PK参数
								CustomerPkReq customerPkReq = new CustomerPkReq();
								//pk类型
								List<String> decisionNames = new ArrayList<>();
								decisionNames.add(CustomerWeightType.TrfTemplate.getCode());
								customerPkReq.setDecisionNames(decisionNames);
								customerPkReq.setAgentGroupCode(agent.getBuyerGroup());
								customerPkReq.setBuyerGroupCode(buyer.getBuyerGroup());
								//调取pk接口
								CustomerPkRsp customerPkRsp = customerClient.getCustomerPkResult(customerPkReq);
								if (Func.isNotEmpty(customerPkRsp) && Func.isNotEmpty(customerPkRsp.getTrfTemplate())) {
									groupCodeAllList.add(customerPkRsp.getTrfTemplate());
								} else {
									groupCodeAllList.add(buyer.getBuyerGroup());
								}
							} else {
								if (Func.isNotEmpty(buyer) && Func.isNotEmpty(buyer.getBuyerGroup())) {
									groupCodeAllList.add(buyer.getBuyerGroup());
								}
								if (Func.isNotEmpty(agent) && Func.isNotEmpty(agent.getBuyerGroup())) {
									groupCodeAllList.add(agent.getBuyerGroup());
								}
							}
						}
					} else {
						if (Func.isNotEmpty(oem) && Func.isNotEmpty(oem.getBuyerGroup())) {
							groupCodeAllList.add(oem.getBuyerGroup());
						}
						if (Func.isNotEmpty(buyer) && Func.isNotEmpty(buyer.getBuyerGroup())) {
							groupCodeAllList.add(buyer.getBuyerGroup());
						}
						if (Func.isNotEmpty(agent) && Func.isNotEmpty(agent.getBuyerGroup())) {
							groupCodeAllList.add(agent.getBuyerGroup());
						}
					}
					//Applicant
					CustomerInstanceDTO applicant = customerList.stream().filter(cus -> GpoCustomerType.Applicant.getCode().equals(cus.getCustomerUsage())).
							findFirst().orElse(null);
					if (Func.isNotEmpty(applicant) && Func.isNotEmpty(applicant.getBuyerGroup())) {
						groupCodeAllList.add(applicant.getBuyerGroup());
					}
					//Payer
					CustomerInstanceDTO Payer = customerList.stream().filter(cus -> GpoCustomerType.Payer.getCode().equals(cus.getCustomerUsage())).
							findFirst().orElse(null);
					if (Func.isNotEmpty(Payer) && Func.isNotEmpty(Payer.getBuyerGroup())) {
						groupCodeAllList.add(Payer.getBuyerGroup());
					}
				}

				groupCodeAllList.add(Constants.CUSTOMER_GROUP.GENERAL);

				// groupCodeAllList去重处理，不然会返回重复template
				groupCodeAllList = groupCodeAllList.stream().distinct().collect(Collectors.toList());
				ReportTemplateSettingRequest customerRequest = new ReportTemplateSettingRequest();
				BeanUtils.copyProperties(reportTemplateSettingRequest,customerRequest);
				customerRequest.setLanguageID(0);
				customerRequest.setCustomerList(null);
				customerRequest.setCustomerGroupCode(groupCodeAllList);
				if (customerPk && Func.isNotEmpty(subContractBuCode)){
					customerRequest.setInternalSubBUCode(Arrays.asList(subContractBuCode));
				}
				getReportTemplateSettingRequestList.add(customerRequest);
			}

			logger.info("=====call ts interface，url:{},params:{}=====", url, JSON.toJSONString(getReportTemplateSettingRequestList));
			String cusJsonResult =HttpClientUtil.postJson(url, JSON.toJSONString(getReportTemplateSettingRequestList));
			reportTemplateBatchRspList = JSONObject.parseArray(cusJsonResult, ReportTemplateBatchRsp.class);
			if(Func.isNotEmpty(reportTemplateBatchRspList)){
				for (ReportTemplateBatchRsp reportTemplateBatchRsp : reportTemplateBatchRspList){
					List<ReportTemplateDTO> reportTemplateResult = new ArrayList<>();
					List<ReportTemplateDTO> reportTemplateDTOS = new ArrayList<>();
					String requestId = reportTemplateBatchRsp.getRequestId();
					ReportTemplateRes reportTemplateRes = new ReportTemplateRes();
					reportTemplateRes.setRequestId(requestId);
					ReportTemplateSettingRequest reportTemplateSettingRequest = reportTemplateRequestList.stream().filter(item -> Func.equalsSafe(item.getRequestId(), requestId)).findAny().orElse(null);
					if(Func.isNotEmpty(reportTemplateSettingRequest)){
						String oldTemplateName = reportTemplateSettingRequest.getOldTemplateName();
						String subContractBuCode = reportTemplateSettingRequest.getSubContractBuCode();
						List<MergedNameReportVO> subcontractAssignTemplates = reportTemplateSettingRequest.getSubcontractAssignTemplates();
						List<String> groupCodeAllList = reportTemplateBatchRsp.getCustomerGroupCode();
						List<MergedNameReportVO> customerTemplates = reportTemplateBatchRsp.getDetails();
						List<String> finalGroupCodeAllList = groupCodeAllList;
						// 需要使用customerGroupCode 过滤返回结果
						if(Func.isNotEmpty(customerTemplates)){
							customerTemplates.stream().forEach(customerTemplate->{
								if(Func.isNotEmpty(customerTemplate.getTemplateSettings())){
									customerTemplate.getTemplateSettings().stream().forEach(ts->{
										if(Func.isNotEmpty(ts.getCustomerGroups())){
											ts.setCustomerGroups(ts.getCustomerGroups().stream().filter(customerGroup -> finalGroupCodeAllList.contains(customerGroup.getCustomerGroupCode())).collect(Collectors.toList()));
										}
									});
								}
							});
						}
						if (Func.isNotEmpty(subcontractAssignTemplates)){
							Set<String> templateNames = new HashSet<>();
							if (Func.isNotEmpty(customerTemplates)){
								templateNames = customerTemplates.stream().collect(Collectors.groupingBy(MergedNameReportVO :: getTemplateName)).keySet();
							}
							// 为subcontract template 追加InternalSubBUCode（ subcontract From ），统一后续处理逻辑
							for (MergedNameReportVO subcontractAssignTemplate : subcontractAssignTemplates){
								if (Func.isEmpty(templateNames) || !templateNames.contains(subcontractAssignTemplate.getTemplateName())){
									customerTemplates.add(subcontractAssignTemplate);
								}
								customerTemplates.stream().filter(customerTemplateTemp->Func.equals(customerTemplateTemp.getTemplateName(),subcontractAssignTemplate.getTemplateName())).forEach(customerTemplate -> {
									if(Func.isNotEmpty(customerTemplate.getTemplateSettings())) {
										customerTemplate.getTemplateSettings().forEach(templateVO -> {
											if(Func.isNotEmpty(templateVO.getInternalSubBUCodes()) && templateVO.getInternalSubBUCodes().stream().filter(internalSubBuCode -> Func.equals(internalSubBuCode.getInternalSubBUCode(),subContractBuCode)).count()==0 ){
												InternalSubBuCode internalSubBuCode = new InternalSubBuCode();
												internalSubBuCode.setInternalSubBUCode(subContractBuCode);
												templateVO.getInternalSubBUCodes().add(internalSubBuCode);
											}
										});
									}
								});
							}
							if(Func.isEmpty(oldTemplateName) && subcontractAssignTemplates.size() == 1){
								reportTemplateRes.setDefaultTemplateName(subcontractAssignTemplates.get(0).getTemplateName());
							}
						}
						if (Func.isNotEmpty(customerTemplates)){
							//取所有数据
							List<MergedNameReportVO> mergedNameReportVOS = new ArrayList<>();
							String finalProductLineCode = productLineCode;
							Boolean finalCustomerPk = customerPk;
							customerTemplates.stream().forEach(customerTemplateVO -> {
								if (Func.isNotEmpty(customerTemplateVO.getTemplateSettings())){
//						customerTemplateVO.setReportType(customerTemplateVO.getTemplateSettings().get(0).getReportType());
									customerTemplateVO.getTemplateSettings().stream().forEach(templateVO -> {
										Boolean isOldTemplate = false;
										if (Func.isNotEmpty(oldTemplateName) && Func.equals(oldTemplateName,templateVO.getTemplateName())){
											isOldTemplate = true;
										}
										if (Func.isNotEmpty(templateVO.getCustomerGroups())){
											List<String> internalSubBUCodes = templateVO.getInternalSubBUCodes().stream().map(InternalSubBuCode :: getInternalSubBUCode).collect(Collectors.toList());
											if (!isOldTemplate){
												//需要判断这组数据满足customerGroupCode或者inernalSubBUCode，两个字段的筛选逻辑不一样
												//customerGroupCode逻辑，digital返回的模板都需要
												//inernalSubBUCode逻辑，返回的模板需要支持所有的inernalSubBUCode
												Boolean subBUCode = false;
												Boolean groupCode = false;
												//customer PK的才会返回Subcontract From Template
												if (finalCustomerPk){

													if (internalSubBUCodes.contains(subContractBuCode)){
														subBUCode = true;
													}
												}
												if (!subBUCode){//不满足inernalSubBUCode，去校验customerGroupCode
													List<String> groupCodes = templateVO.getCustomerGroups().stream().map(CustomerGroup :: getCustomerGroupCode).collect(Collectors.toList());
													for (String customerGroupCode : groupCodes){
														if (finalGroupCodeAllList.contains(customerGroupCode)){
															groupCode = true;
															break;
														}
													}
												}
												if (!subBUCode && !groupCode){
													return;
												}
											}

											templateVO.getCustomerGroups().stream().forEach(customer -> {
												//一组数据
												MergedNameReportVO mergedNameReportVO = new MergedNameReportVO();
												mergedNameReportVO.setCustomer(customer.getCustomerName());
												mergedNameReportVO.setCustomerGroupCode(customer.getCustomerGroupCode());
												mergedNameReportVO.setTemplateName(customerTemplateVO.getTemplateName());
												mergedNameReportVO.setProductLineCode(finalProductLineCode);
												mergedNameReportVO.setDisabled(false);
												mergedNameReportVO.setInternalSubBuCodes(internalSubBUCodes);
												List<TemplateVO> templateVOS = new ArrayList<>();
												templateVOS.add(templateVO);
												mergedNameReportVO.setTemplateSettings(templateVOS);
												mergedNameReportVOS.add(mergedNameReportVO);
											});
										}
									});
								}
							});
							//按照customer分组
							Map<String,List<MergedNameReportVO>> groupByCustomer = mergedNameReportVOS.stream().collect(Collectors.groupingBy(MergedNameReportVO :: getCustomerGroupCode));
							//拼接返回值
							for (String group : groupByCustomer.keySet()){
								ReportTemplateDTO reportTemplateDTO = new ReportTemplateDTO();
								reportTemplateDTO.setCustomerGroupCode(group);
								reportTemplateDTO.setCustomerName(groupByCustomer.get(group).get(0).getCustomer());
								reportTemplateDTO.setMergedNameReportVOS(groupByCustomer.get(group));
								reportTemplateDTOS.add(reportTemplateDTO);
							}
						}
						//判断是否有值，是否为report查询模板，是否不是中英文，需要筛选语言
						if (Func.isNotEmpty(reportTemplateDTOS)
								&& (Func.equals(TemplateSettingTypeEnum.REPORT.getCode(),reportTemplateSettingRequest.getTemplateSettingTypeID())||
								Func.equals(TemplateSettingTypeEnum.PRELIM.getCode(),reportTemplateSettingRequest.getTemplateSettingTypeID()))){
							List<ReportTemplateDTO> reportTemplateDTOList = new ArrayList<>();
							//中英文
							if (Func.equals(Integer.parseInt(ReportLanguage.EnglishAndChineseReport.getCode()),reportTemplateSettingRequest.getLanguageID())){
								reportTemplateDTOS.stream().forEach(reportTemplateDTO -> {
									List<MergedNameReportVO> mergedNameReportVOList = new ArrayList<>();
									List<MergedNameReportVO> mergedNameReportVOS = reportTemplateDTO.getMergedNameReportVOS();
									Map<String,List<MergedNameReportVO>> groupByTemplate = mergedNameReportVOS.stream().collect(Collectors.groupingBy(MergedNameReportVO :: getTemplateName));
									groupByTemplate.keySet().stream().forEach(templateName -> {
										List<MergedNameReportVO> mergedNameReportVOSByTemplate = groupByTemplate.get(templateName);
										if (mergedNameReportVOSByTemplate.size() > 1){
											MergedNameReportVO mergedNameReportVO = mergedNameReportVOSByTemplate.get(0);
											List<TemplateVO> templateVOS = mergedNameReportVO.getTemplateSettings();
											templateVOS.addAll(mergedNameReportVOSByTemplate.get(1).getTemplateSettings());
											mergedNameReportVO.setTemplateSettings(templateVOS);
											mergedNameReportVOList.add(mergedNameReportVO);
										}
									});
									//判断customerGroup下是否有值
									if (Func.isNotEmpty(mergedNameReportVOList)){
										ReportTemplateDTO reportTemplate = new ReportTemplateDTO();
										reportTemplate.setCustomerName(reportTemplateDTO.getCustomerName());
										reportTemplate.setCustomerGroupCode(reportTemplateDTO.getCustomerGroupCode());
										reportTemplate.setMergedNameReportVOS(mergedNameReportVOList);
										reportTemplateDTOList.add(reportTemplate);
									}
								});
							}else if (Func.equals(Integer.parseInt(ReportLanguage.ChineseReportOnly.getCode()),reportTemplateSettingRequest.getLanguageID())){//中文
								reportTemplateDTOS.stream().forEach(reportTemplateDTO -> {
									List<MergedNameReportVO> mergedNameReportVOS = reportTemplateDTO.getMergedNameReportVOS();
									//筛选中文
									List<MergedNameReportVO> mergedNameReportVOSCn =
											mergedNameReportVOS.stream().filter(mergedNameReportVO -> Func.equals(LanguageTypeDigitalReport.CN.getLanguageId(),mergedNameReportVO.getTemplateSettings().get(0).getLanguageID())).collect(Collectors.toList());
									//过滤是否有对应英文模板，一起返回
									mergedNameReportVOSCn.stream().forEach(mergedNameReportVOCn -> {
										MergedNameReportVO mergedNameReportVO =
												mergedNameReportVOS.stream().filter(mergedNameReport -> Func.equals(LanguageTypeDigitalReport.EN.getLanguageId(),mergedNameReport.getTemplateSettings().get(0).getLanguageID())
														&& Func.equals(mergedNameReportVOCn.getTemplateName(),mergedNameReport.getTemplateName())).findFirst().orElse(null);
										if (Func.isNotEmpty(mergedNameReportVO)){
											mergedNameReportVOCn.getTemplateSettings().addAll(mergedNameReportVO.getTemplateSettings());
										}
									});
									if (Func.isNotEmpty(mergedNameReportVOSCn)){
										ReportTemplateDTO reportTemplate = new ReportTemplateDTO();
										reportTemplate.setCustomerName(reportTemplateDTO.getCustomerName());
										reportTemplate.setCustomerGroupCode(reportTemplateDTO.getCustomerGroupCode());
										reportTemplate.setMergedNameReportVOS(mergedNameReportVOSCn);
										reportTemplateDTOList.add(reportTemplate);
									}
								});
							}else if (Func.equals(Integer.parseInt(ReportLanguage.EnglishReportOnly.getCode()),reportTemplateSettingRequest.getLanguageID())
									|| Func.equals(Integer.parseInt(ReportLanguage.MultilingualReport.getCode()),reportTemplateSettingRequest.getLanguageID())){//英文
								reportTemplateDTOS.stream().forEach(reportTemplateDTO -> {
									List<MergedNameReportVO> mergedNameReportVOS = reportTemplateDTO.getMergedNameReportVOS();
									//筛选中文
									List<MergedNameReportVO> mergedNameReportVOSEn =
											mergedNameReportVOS.stream().filter(mergedNameReportVO -> Func.equals(LanguageTypeDigitalReport.EN.getLanguageId(),mergedNameReportVO.getTemplateSettings().get(0).getLanguageID())).collect(Collectors.toList());
									//过滤是否有对应中文模板，一起返回
									mergedNameReportVOSEn.stream().forEach(mergedNameReportVOEn -> {
										MergedNameReportVO mergedNameReportVO =
												mergedNameReportVOS.stream().filter(mergedNameReport -> Func.equals(LanguageTypeDigitalReport.CN.getLanguageId(),mergedNameReport.getTemplateSettings().get(0).getLanguageID())
														&& Func.equals(mergedNameReportVOEn.getTemplateName(),mergedNameReport.getTemplateName())).findFirst().orElse(null);
										if (Func.isNotEmpty(mergedNameReportVO)){
											mergedNameReportVOEn.getTemplateSettings().addAll(mergedNameReportVO.getTemplateSettings());
										}
									});
									if (Func.isNotEmpty(mergedNameReportVOSEn)){
										ReportTemplateDTO reportTemplate = new ReportTemplateDTO();
										reportTemplate.setCustomerName(reportTemplateDTO.getCustomerName());
										reportTemplate.setCustomerGroupCode(reportTemplateDTO.getCustomerGroupCode());
										reportTemplate.setMergedNameReportVOS(mergedNameReportVOSEn);
										reportTemplateDTOList.add(reportTemplate);
									}
								});
							}
							//判断筛选中英文后是否有值
							reportTemplateDTOS = new ArrayList<>();
							if (Func.isNotEmpty(reportTemplateDTOList)){
								reportTemplateDTOS = reportTemplateDTOList;
							}
						}
						//根据RootBuCode过滤 Internal 模板
						if(Func.isNotEmpty(reportTemplateDTOS)){
							reportTemplateDTOS.stream().forEach(reportTemplateDTO -> {
								List<MergedNameReportVO> mergedNameReportList= reportTemplateDTO.getMergedNameReportVOS();
								if(Func.isNotEmpty(mergedNameReportList)){
									List<MergedNameReportVO> filterMergedNameReportList = Lists.newArrayList();
									mergedNameReportList.stream().forEach(mergedNameReport->{
										// 非GENERAL或者InternalSubBuCodes为空的属于非Internal 模板
										if(!Func.equals(Constants.CUSTOMER_GROUP.GENERAL,mergedNameReport.getCustomerGroupCode())
												||Func.isEmpty(mergedNameReport.getInternalSubBuCodes())
										){
											filterMergedNameReportList.add(mergedNameReport);
										}
										// Internal 模板 需要满足RootBu的过滤
										if(Func.equals(Constants.CUSTOMER_GROUP.GENERAL,mergedNameReport.getCustomerGroupCode())
												&& Func.isNotEmpty(mergedNameReport.getInternalSubBuCodes())
												&&mergedNameReport.getInternalSubBuCodes().contains(subContractBuCode)){
											filterMergedNameReportList.add(mergedNameReport);
										}
									});
									reportTemplateDTO.setMergedNameReportVOS(filterMergedNameReportList);
								}
							});
						}

						reportTemplateResult.addAll(reportTemplateDTOS);
						// 默认值设置优先级 Subcontract 客户 Internal General
						if(Func.isNotEmpty(reportTemplateDTOS) && Func.isEmpty(oldTemplateName)){
							if(Func.isEmpty(subcontractAssignTemplates)){
								// 客户模板
								List<ReportTemplateDTO> customerReportTemplateList = reportTemplateDTOS.stream()
										.filter(template->!Func.equals(Constants.CUSTOMER_GROUP.GENERAL,template.getCustomerGroupCode())).collect(Collectors.toList());
								if(Func.isNotEmpty(customerReportTemplateList) && customerReportTemplateList.size() == 1
										&& customerReportTemplateList.get(0).getMergedNameReportVOS().size()==1){
									reportTemplateRes.setDefaultTemplateName(customerReportTemplateList.get(0).getMergedNameReportVOS().get(0).getTemplateName());
								}
								// Internal
								if(Func.isEmpty(customerReportTemplateList)){
									// Internal
									List<MergedNameReportVO> mergedNameReportVOList = new ArrayList<>();
									for (ReportTemplateDTO reportTemplateDTO : reportTemplateDTOS){
										//存在分包方bu，并且CustomerGroupCode为General   GPO2-6705
										for (MergedNameReportVO mergedNameReportVO : reportTemplateDTO.getMergedNameReportVOS()){
											if (Func.isNotEmpty(mergedNameReportVO.getInternalSubBuCodes())
													&& Func.isNotEmpty(subContractBuCode)
													&& mergedNameReportVO.getInternalSubBuCodes().contains(subContractBuCode)
													&& Func.equals(Constants.CUSTOMER_GROUP.GENERAL,mergedNameReportVO.getCustomerGroupCode())){
												mergedNameReportVOList.add(mergedNameReportVO);
											}
										}
									}
									if(Func.isNotEmpty(mergedNameReportVOList) && mergedNameReportVOList.size()==1 ){
										reportTemplateRes.setDefaultTemplateName(mergedNameReportVOList.get(0).getTemplateName());
									}
									if(Func.isEmpty(mergedNameReportVOList)){
										// General
										List<MergedNameReportVO> mergedNameReportVOS = new ArrayList<>();
										for (ReportTemplateDTO reportTemplateDTO : reportTemplateDTOS){
											//General模板：CustomerGroupCode为General 且 InternalSubBu 为空；  GPO2-6705
											for (MergedNameReportVO mergedNameReportVO : reportTemplateDTO.getMergedNameReportVOS()){
												if (Func.equals(Constants.CUSTOMER_GROUP.GENERAL,mergedNameReportVO.getCustomerGroupCode()) && Func.isEmpty(mergedNameReportVO.getInternalSubBuCodes())){
													mergedNameReportVOS.add(mergedNameReportVO);
												}
											}
										}
										if (Func.isNotEmpty(mergedNameReportVOS) && mergedNameReportVOS.size() == 1){
											reportTemplateRes.setDefaultTemplateName(mergedNameReportVOS.get(0).getTemplateName());
										}
									}
								}
							}
						}
						//处理旧数据
						if (Func.isNotEmpty(oldTemplateName)){
							Boolean isHaveOld = false;
							for (ReportTemplateDTO reportTemplateDTO : reportTemplateResult){
								if (isHaveOld){
									break;
								}
								for (MergedNameReportVO mergedNameReportVO : reportTemplateDTO.getMergedNameReportVOS()){
									if (Func.equals(oldTemplateName,mergedNameReportVO.getTemplateName())){
										isHaveOld = true;
										break;
									}
								}
							}
							//不包含旧值，拼接一个新的
							if (!isHaveOld){
								for (ReportTemplateDTO reportTemplateDTO : reportTemplateDTOS){
									for (MergedNameReportVO mergedNameReportVO : reportTemplateDTO.getMergedNameReportVOS()){
										if (Func.equals(oldTemplateName,mergedNameReportVO.getTemplateName())){
											ReportTemplateDTO reportTemplate = new ReportTemplateDTO();
											reportTemplate.setCustomerName(reportTemplateDTO.getCustomerName());
											reportTemplate.setCustomerGroupCode(reportTemplateDTO.getCustomerGroupCode());
											List<MergedNameReportVO> mergedNameReportVOS = new ArrayList<>();
											mergedNameReportVO.setDisabled(true);
											mergedNameReportVOS.add(mergedNameReportVO);
											reportTemplate.setMergedNameReportVOS(mergedNameReportVOS);
											reportTemplateResult.add(reportTemplate);
											break;
										}
									}
								}
							}
						}

						// 设置ReportType
						if(Func.isNotEmpty(reportTemplateResult)){
							reportTemplateResult.stream().forEach(item->{
								List<MergedNameReportVO> mergedNameReportVOS = item.getMergedNameReportVOS();
								if(Func.isNotEmpty(mergedNameReportVOS)){
									mergedNameReportVOS.stream().forEach(template->{
										List<TemplateVO> templateSettings = template.getTemplateSettings();
										if(Func.isNotEmpty(templateSettings)){
											template.setReportType(templateSettings.get(0).getReportType());
										}
									});
								}
							});
						}
						//对reportTemplateResult排序
						List<ReportTemplateDTO> result = new ArrayList<>();
						for(String groupCode : groupCodeAllList){
							for(ReportTemplateDTO item : reportTemplateResult){
								if(Func.isNotEmpty(groupCode) && Func.isNotEmpty(item.getCustomerGroupCode()) && groupCode.equals(item.getCustomerGroupCode())){
									result.add(item);
								}
							}
						}
						reportTemplateRes.setReportTemplateDTOS(result);
					}
					reportTemplateResList.add(reportTemplateRes);
				}
			}

		}catch (Exception e){
			logger.error(e.getMessage(),e);
		}
		return reportTemplateResList;
	}


}
