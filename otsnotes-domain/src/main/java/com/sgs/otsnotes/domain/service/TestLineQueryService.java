package com.sgs.otsnotes.domain.service;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.beust.jcommander.internal.Sets;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sgs.core.domain.UserInfo;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.core.base.CustomResult;
import com.sgs.framework.core.base.ResponseCode;
import com.sgs.framework.facade.domain.rsp.BuParamValueRsp;
import com.sgs.framework.i18n.util.MessageUtil;
import com.sgs.framework.model.enums.LanguageType;
import com.sgs.framework.model.enums.SgsSystem;
import com.sgs.framework.model.enums.TestLineType;
import com.sgs.framework.tool.utils.Func;
import com.sgs.framework.tool.utils.LabCodeContextHolder;
import com.sgs.framework.tool.utils.ProductLineContextHolder;
import com.sgs.grus.async.AsyncCall;
import com.sgs.grus.async.AsyncResult;
import com.sgs.grus.async.AsyncUtils;
import com.sgs.otsnotes.core.annotation.AccessPolicyRule;
import com.sgs.otsnotes.core.constants.Constants;
import com.sgs.otsnotes.core.enums.ShareType;
import com.sgs.otsnotes.core.util.NumberUtil;
import com.sgs.otsnotes.dbstorages.mybatis.extmapper.*;
import com.sgs.otsnotes.dbstorages.mybatis.extmodel.analyte.AnalyteExtPO;
import com.sgs.otsnotes.dbstorages.mybatis.mapper.*;
import com.sgs.otsnotes.dbstorages.mybatis.model.*;
import com.sgs.otsnotes.domain.service.common.TestLineEvaluationAliasUtil;
import com.sgs.otsnotes.domain.service.gpn.analyte.IAnalyteService;
import com.sgs.otsnotes.domain.service.gpn.wi.IWorkingInstructionService;
import com.sgs.otsnotes.domain.service.localize.LocalizableTranslator;
import com.sgs.otsnotes.domain.service.localize.TestConditionInstanceLocalizeHelper;
import com.sgs.otsnotes.domain.service.testline.DTO.WIServiceDTO;
import com.sgs.otsnotes.domain.service.testline.IWIService;
import com.sgs.otsnotes.domain.util.ConditionUtil;
import com.sgs.otsnotes.facade.model.dto.*;
import com.sgs.otsnotes.facade.model.dto.analyte.TestLineAnalyteDTO;
import com.sgs.otsnotes.facade.model.dto.testline.TestLineLabSectionDTO;
import com.sgs.otsnotes.facade.model.enums.*;
import com.sgs.otsnotes.facade.model.info.MatrixTestLineRelInfo;
import com.sgs.otsnotes.facade.model.info.labteam.LabTeam;
import com.sgs.otsnotes.facade.model.info.labteam.QueryLabTeamInfoReq;
import com.sgs.otsnotes.facade.model.info.labteam.QueryLabTeamInfoRsp;
import com.sgs.otsnotes.facade.model.req.OrderTestLineListSearchReq;
import com.sgs.otsnotes.facade.model.req.analyte.AnalyteDetailListReq;
import com.sgs.otsnotes.facade.model.req.matrix.MatrixTableListReq;
import com.sgs.otsnotes.facade.model.req.order.OrderPpTestLineReq;
import com.sgs.otsnotes.facade.model.req.testLine.*;
import com.sgs.otsnotes.facade.model.rsp.LabInfo;
import com.sgs.otsnotes.facade.model.rsp.TestLineLabSectionRsp;
import com.sgs.otsnotes.facade.model.rsp.TestLineWorkInstructionRsp;
import com.sgs.otsnotes.facade.model.rsp.condition.TestLineConditionRelDTO;
import com.sgs.otsnotes.facade.model.rsp.gpn.JobTestLineRsp;
import com.sgs.otsnotes.facade.model.rsp.matrix.MatrixTableListRsp;
import com.sgs.otsnotes.facade.model.rsp.matrix.MatrixTableSampleNoListRsp;
import com.sgs.otsnotes.facade.model.rsp.matrix.MergePPTestLineRelListRsp;
import com.sgs.otsnotes.facade.model.rsp.matrix.TestMatrixWithTestLineRsp;
import com.sgs.otsnotes.facade.model.rsp.testLine.AnalyteSelectRsp;
import com.sgs.otsnotes.facade.model.rsp.testLine.LabSectionRsp;
import com.sgs.otsnotes.facade.model.rsp.testLine.QueryTestStandardRsp;
import com.sgs.otsnotes.facade.model.rsp.testLine.TestLineEditDetailRsp;
import com.sgs.otsnotes.integration.*;
import com.sgs.otsnotes.integration.trimslocal.LabSectionClient;
import com.sgs.otsnotes.integration.trimslocal.PpClient;
import com.sgs.preorder.facade.OrderFacade;
import com.sgs.preorder.facade.model.dto.order.OrderAllDTO;
import com.sgs.preorder.facade.model.info.user.UserLabBuInfo;
import com.sgs.preorder.facade.model.req.BuParamReq;
import com.sgs.preorder.facade.model.req.OrderIdReq;
import com.sgs.preorder.facade.model.req.report.ReportLangReq;
import com.sgs.preorder.facade.model.rsp.TestRequestRsp;
import com.sgs.priceengine.facade.QuotationFacade;
import com.sgs.priceengine.facade.model.DTO.TotestTestlineInstanceDTO;
import com.sgs.priceengine.facade.model.request.OrderIdRequest;
import com.sgs.trimslocal.facade.IAnalyteFacade;
import com.sgs.trimslocal.facade.IConditionFacade;
import com.sgs.trimslocal.facade.ILabSectionFacade;
import com.sgs.trimslocal.facade.model.analyte.req.QueryTestLineAnalyteReq;
import com.sgs.trimslocal.facade.model.analyte.req.TestLineAnalyteReq;
import com.sgs.trimslocal.facade.model.analyte.rsp.TestAnalyteLangReq;
import com.sgs.trimslocal.facade.model.analyte.rsp.TestLineAnalyteRsp;
import com.sgs.trimslocal.facade.model.condition.req.GetConditionListItemReq;
import com.sgs.trimslocal.facade.model.condition.req.GetConditionListReq;
import com.sgs.trimslocal.facade.model.condition.rsp.TestConditionItemRsp;
import com.sgs.trimslocal.facade.model.condition.rsp.TestConditionLangRsp;
import com.sgs.trimslocal.facade.model.condition.rsp.TestConditionRsp;
import com.sgs.trimslocal.facade.model.condition.rsp.TestConditionTypeLanguageRsp;
import com.sgs.trimslocal.facade.model.labsection.req.GetLabSectionListByTestLineVersionIdsReq;
import com.sgs.trimslocal.facade.model.labsection.req.LabSectionByLabIdReq;
import com.sgs.trimslocal.facade.model.labsection.rsp.GetLabSectionBaseInfoRsp;
import com.sgs.trimslocal.facade.model.pp.req.GetPpInfoReq;
import com.sgs.trimslocal.facade.model.pp.rsp.GetPpBaseInfoRsp;
import com.sgs.trimslocal.facade.model.workinginstruction.req.WorkingInstructionReq;
import com.sgs.trimslocal.facade.model.workinginstruction.rsp.WorkingInstructionLanRsp;
import com.sgs.trimslocal.facade.model.workinginstruction.rsp.WorkingInstructionRsp;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020/8/3 10:43
 */
@Service
@Slf4j
public class TestLineQueryService {

    //region field
    @Autowired
    private OrderMapper orderMapper;
    @Autowired
    private ReportMapper reportMapper;
    @Autowired
    private TestLineInstanceExtMapper testLineInstanceExtMapper;
    @Autowired
    private TestConditionMapper conditionMapper;
    @Autowired
    private PPTestLineRelMapper ppTestLineRelMapper;
    @Autowired
    private TestMatrixMapper testMatrixMapper;
    @Autowired
    private TestSampleExtMapper testSampleExtMapper;
    @Autowired
    private TestLineMapper testLineMapper;
    @Autowired
    private ReferDataRelationshipMapper referDataRelationshipMapper;
    @Autowired
    private OrderReportClient orderReportClient;
    @Autowired
    private OrderClient orderClient;
    @Autowired
    private TestLineCitationLangMapper testLineCitationLangMapper;
    @Autowired
    private TestLineBaseMapper testLineBaseMapper;
    @Autowired
    private OrderLanguageRelInfoMapper orderLanguageRelInfoMapper;
    @Autowired
    private TestLineService testLineService;
    @Autowired
    private TokenClient tokenClient;
    @Autowired
    private TestLineLabTeamDefaultRelationshipMapper testLineLabTeamDefaultRelationshipMapper;
    @Autowired
    private UserManagementClient userManagementClient;
    @Autowired
    private TestLineConditionRelExtMapper testLineConditionRelExtMapper;
    @Autowired
    private ILabSectionFacade labSectionFacade;
    @Autowired
    private UserManagementClient userClient;
    @Autowired
    private OrderTestLineService orderTestLineService;
    @Autowired
    private TestLineInstanceMapper testLineInstanceMapper;
    @Autowired
    private IAnalyteFacade analyteFacade;
    @Autowired
    private IConditionFacade conditionFacade;
    //endregion
    @Autowired
    private IWorkingInstructionService workingInstructionService;
    @Autowired
    private IAnalyteService analyteService;
    @Autowired
    private OrderFacade orderFacade;
    @Autowired
    private OrderCitationRelInfoMapper orderCitationRelInfoMapper;
    @Autowired
    private FrameWorkClient frameWorkClient;
    @Autowired
    private QuotationFacade quotationFacade;
    @Autowired
    private LabSectionClient labSectionClient;
    @Autowired
    private JobExtMapper jobExtMapper;

    @Autowired
    private IWIService iwiService;
    @Autowired
    private ObjectReferInfoMapper objectReferMapper;
    @Autowired
    private PpClient ppClient;
    @Autowired
    private MessageUtil messageUtil;

    /**
     * matrix表格
     * @param req
     * @return
     */
    public PageInfo<MatrixTableListRsp> queryPPTestLineList(MatrixTableListReq req) {
        String orderId = req.getOrderId();
        String orderNo = req.getOrderNo();
        GeneralOrderInstanceInfoPO orderInstanceInfoPO = null;
        if(StringUtils.isNotBlank(orderId)){
            orderInstanceInfoPO = orderMapper.getOrderInfoByOrderId(orderId);
            if(orderInstanceInfoPO==null){
                return new PageInfo<>();
            }
        }
        if(StringUtils.isNotBlank(orderNo) && orderInstanceInfoPO==null){
            orderInstanceInfoPO = orderMapper.getOrderInfo(orderNo);
            if(orderInstanceInfoPO==null){
                return new PageInfo<>();
            }
        }

        orderNo = orderInstanceInfoPO.getOrderNo();
        orderId = orderInstanceInfoPO.getID();
        ReportInfoPO reportInfo = reportMapper.getReportByOrderNo(orderNo);
        OrderIdReq orderIdReq = new OrderIdReq();
        orderIdReq.setOrderNo(orderNo);
        orderIdReq.setLabCode(LabCodeContextHolder.getLabCode());
        OrderAllDTO orderAllDTO = orderFacade.getOrderForPe(orderIdReq).getData();

        PageHelper.startPage(req.getPage(),req.getRows());
        List<MatrixTableDTO> matrixTableDTOS = ppTestLineRelMapper.queryPPTestLineListByOrderId(orderId,req.getShowJobFlag(),req.getNewJobFlag(),req.getCopyTlFlag(),req.getTestLineInstanceIds());
        PageInfo<MatrixTableDTO> page = PageInfo.of(matrixTableDTOS);
        if(CollectionUtils.isEmpty(matrixTableDTOS)){
            return new PageInfo<>();
        }
        Set<String> testLineInstanceIdSets = matrixTableDTOS.stream().map(MatrixTableDTO::getTestLineInstanceId).collect(Collectors.toSet());
        List<JobTestLineRsp> jobTestLineRspList = null;
        if(Func.isNotEmpty(testLineInstanceIdSets)){
            jobTestLineRspList = jobExtMapper.queryJobByTLInstanceIdList(testLineInstanceIdSets);
        }

        Integer productLineId = orderAllDTO.getBUID();
        buildWI(matrixTableDTOS,productLineId);
//        //判断是否需要JobNo
//        if (Func.isNotEmpty(req.getShowJobFlag()) && req.getShowJobFlag()){
//            matrixTableDTOS.stream().forEach(matrixTableDTO -> {
//                matrixTableDTO.setJobNo(null);
//            });
//        }
        //获取当前Testline 对应的analyteId

        //拼装sample，condition
        List<TestMatrixPO> matrixPOList = testMatrixMapper.getTestMatrixListByOrderId(orderId);

        Map<String, List<TestMatrixPO>> tlIdMatrixListMap = matrixPOList.stream().collect(Collectors.groupingBy(TestMatrixPO::getTestLineInstanceID));
        //所有sample，分组
        List<TestSampleInfoPO> sampleList = testSampleExtMapper.getSampleByOrderNo(orderNo);
        List<PPTLSampleInfoDTO> pptlSampleList = testSampleExtMapper.getPPTLSampleByOrderNo(orderNo);
        List<MatrixGroupSampleInfoDTO> matrixGroupSampleList = testSampleExtMapper.getMatrixGroupSampleByOrderNo(orderNo);
        //所有condition
        List<TestConditionInfoPO> allTestConditionInfoPOS = conditionMapper.getTestConditionListByOrderId(orderId);

        //获取用户语言
        /*String defaultLanguageCode = tokenClient.getUser()==null?
                tokenClient.getUser(req.getToken()).getDefaultLanguageCode():tokenClient.getUser().getDefaultLanguageCode();*/
        String defaultLanguageCode = frameWorkClient.getPrimaryLanguageCode(ProductLineContextHolder.getProductLineCode());

        if (!UserInfoDefaultLanguageCodeEnums.check(defaultLanguageCode,UserInfoDefaultLanguageCodeEnums.en_us)){
            //不是英文语言时需要翻译
            LocalizableTranslator.translate(matrixTableDTOS,UserInfoDefaultLanguageCodeEnums.getIdByCode(defaultLanguageCode));

            //额外处理一些字段名不match的字段
            for (MatrixTableDTO matrixTableDTO : matrixTableDTOS) {
                if (StringUtils.isBlank(matrixTableDTO.getCitationName())){
                    continue;
                }
                matrixTableDTO.setTestStandard(matrixTableDTO.getCitationName());
            }
        }

        boolean chineseFlag =  isChinese(orderNo,defaultLanguageCode);

        if (chineseFlag) {
            List<TestConditionInfoPO> enAllTestConditionInfoPOS=Func.copy(allTestConditionInfoPOS,TestConditionInfoPO.class,TestConditionInfoPO.class);
            TestConditionInstanceLocalizeHelper.buildConditionInstances(allTestConditionInfoPOS,LanguageType.Chinese.getLanguageId());
            allTestConditionInfoPOS.forEach(testConditionInfoPO -> {
                //如果为空则给英文
                if(Func.isEmpty(testConditionInfoPO.getTestConditionDesc())){
                    testConditionInfoPO.setTestConditionDesc(enAllTestConditionInfoPOS.stream().filter(e->e.getID().equals(testConditionInfoPO.getID())).findFirst().get().getTestConditionDesc());
                }
            });
            //CitationLocalizeHelper.build(matrixTableDTOS);
        }
        //List<MatrixTableBaseConditionDTO> baseConditionList = Lists.newArrayList();
        //List<MatrixTableBaseConditionDTO> baseConditionList = conditionMapper.getConditionBaseInfoByOrderId(orderId);

        List<Integer> testLineVersionIds = matrixTableDTOS.stream().map(MatrixTableDTO::getTestLineVersionId).collect(Collectors.toList());
        List<MatrixTableTestLineConditionDTO> trimsTestLineConditionRelList = Lists.newArrayList();
        if(CollectionUtils.isNotEmpty(testLineVersionIds)){
            trimsTestLineConditionRelList = conditionMapper.queryTestLineConditionRelByTestLineVersionIds(testLineVersionIds);
        }

        //find wi
        //List<TestLineWorkInstructionRelInfoPO> testLineWorkInstructionRelInfoPOS=getTLWIRelList(testLineVersionIds);

        //refer data，这里是到matrix级别的判断：取得source order对应的maxtrix,然后跟current order的matrix进行比对
        List<TestMatrixWithTestLineRsp> finalReferSourceMatrixs = getReferSourceMatrixes(orderNo);

        //处理labTeamName
        buildLabTeamName(orderInstanceInfoPO.getLabCode(), matrixTableDTOS);

        //基础属性赋值
        List<MatrixTableListRsp> list = Lists.newArrayList();
        List<AnalyteExtPO> testLineAnalytes=Lists.newArrayList();
        if(!req.isSearchPPTestLineListForQuotation()){//quotation by matrix报价不需要Analyte信息，不查询提升性能
            AnalyteDetailListReq analyteDetailListReq = new AnalyteDetailListReq();
            analyteDetailListReq.setOrderNo(orderNo);
            BaseResponse<List<AnalyteExtPO>> baseResponse = analyteService.getTestLineAnalytes(analyteDetailListReq);
            testLineAnalytes = baseResponse.getData();
        }
        Map<String,List<AnalyteExtPO>> testLineAnalyteDtoList = new HashMap<>();
        if(Func.isNotEmpty(testLineAnalytes)){
           testLineAnalyteDtoList = testLineAnalytes.stream().collect(Collectors.groupingBy(b -> b.getTestLineInstanceId()));
        }
        // 查询LabSectionList
        List<TestLineLabSectionDTO> allTestLineLabSectionList = testLineInstanceExtMapper.getTestLineLabSection(testLineInstanceIdSets);
        List<GetLabSectionBaseInfoRsp> labSectionBaseInfoList = new ArrayList<>();
        if(Func.isNotEmpty(allTestLineLabSectionList)){
            List<Long> labSectionBaseIdList = allTestLineLabSectionList.stream().map(TestLineLabSectionDTO::getLabSectionBaseId).collect(Collectors.toList());
            labSectionBaseInfoList = labSectionClient.getLabSectionBaseInfo(labSectionBaseIdList);
        }

        // 查询订单下的object_refer数据
        ObjectReferInfoExample objectReferExample = new ObjectReferInfoExample();
        objectReferExample.createCriteria().andOrderNoEqualTo(orderNo).andShareTypeEqualTo(ShareType.SHARE_TEST_LINE.getCode());
        List<ObjectReferInfoPO> objectReferList = objectReferMapper.selectByExample(objectReferExample);
        for (MatrixTableDTO dto : matrixTableDTOS) {
//            int sampleCount = 0;
            MatrixTableListRsp rsp = new MatrixTableListRsp();
            BeanUtils.copyProperties(dto,rsp);
            // OOB业务场景下优先显示customerTestLineName名称
            Integer testLineType = dto.getTestLineType();
            rsp.setTestItemEn(rsp.getTestItem());
            rsp.setPpNameEn(rsp.getPpName());
            rsp.setWiForCsText(dto.getWiForCsText());
            if(Func.isNotEmpty(testLineType) && ((testLineType & TestLineType.OOB_TEST.getType())==TestLineType.OOB_TEST.getType())){
                String testLineName = dto.getCustomerTestLineName();
                if(Func.isNotEmpty(testLineName)){
                    rsp.setTestItem(testLineName);
                    rsp.setTestItemEn(testLineName);
                }
                if(chineseFlag&&Func.isNotEmpty(dto.getCustomerTestLineNameCN())){
                    rsp.setTestItem(dto.getCustomerTestLineNameCN());
                }
            }
            rsp.setPpArtifactRelId(dto.getPpTlRelBaseId());
            rsp.setOrderStatus(orderAllDTO.getOrderStatus());
            if(Func.isNotEmpty(reportInfo)){
                rsp.setReportStatus(reportInfo.getReportStatus());
            }
            String testLineInstanceId = dto.getTestLineInstanceId();
            // 查询TL对应的Report状态 应该是一个集合
            List<ReportDTO> relReportList = testLineMapper.queryReportByTestLine(testLineInstanceId);
            if(Func.isNotEmpty(relReportList)){
                rsp.setReportStatusList(relReportList.stream().map(ReportDTO::getReportStatus).collect(Collectors.toSet()));
            }
            Integer matrixGroupId = dto.getMatrixGroupId();
//            Long testLineBaseId = dto.getTestLineBaseId();
            Integer testLineVersionId = dto.getTestLineVersionId();
            MatrixTableTestLineConditionDTO mtDto = trimsTestLineConditionRelList.stream().filter(tl -> tl.getTestLineVersionId().compareTo(testLineVersionId) == 0).findFirst().orElse(null);
            Long conditionCount = 0L;
            if(mtDto!=null){
                conditionCount = mtDto.getCount();
            }
            //处理standart section,在sql中拼接
/*            if(StringUtils.isNotBlank(dto.getCitationSectionName())){
                rsp.setTestStandard(rsp.getTestStandard()+","+dto.getCitationSectionName());
            }*/

            //处理matrix
            List<TestMatrixPO> matrix = tlIdMatrixListMap.get(testLineInstanceId);
            //过滤出当前matrixGroup的数据
            List<TestMatrixPO> mtList = matrix==null?null: matrix.stream()
                    .filter(m -> m.getMatrixGroupId().compareTo(matrixGroupId)==0)
                    .collect(Collectors.toList());

            if(CollectionUtils.isNotEmpty(mtList)){
                List<MatrixTableSampleNoListRsp> sampleNoList = Lists.newArrayList();
                if(matrixGroupId==0) {
                    //用for循环，为了保持sample的顺序
                    if(CollectionUtils.isNotEmpty(pptlSampleList)) {
                        for (PPTLSampleInfoDTO sampleInfo : pptlSampleList) {
                            String sampleId = sampleInfo.getID();
                            List<TestMatrixPO> assignMatrix = mtList.stream().filter(m ->
                                    (
                                        sampleId.equals(m.getTestSampleID())
                                                && (StringUtils.isNotBlank(sampleInfo.getPpTlRelId())&&sampleInfo.getPpTlRelId().equals(rsp.getPpTlRelId()))
                                                && testLineInstanceId.equals(m.getTestLineInstanceID())
                                    )
                            ).collect(Collectors.toList());
                            if (CollectionUtils.isNotEmpty(assignMatrix)) {
                                TestMatrixPO testMatrixPO = assignMatrix.get(0);
                                Boolean activeIndicator = testMatrixPO.getActiveIndicator();
                                String sampleNo = sampleInfo.getSampleNo();
                                // 过滤当前object_refer 数据
                                Boolean share = false;
                                if(Func.isNotEmpty(objectReferList)){
                                    ObjectReferInfoPO objectRefer = objectReferList.stream().filter(item->Func.equalsSafe(item.getObjectId(),testLineInstanceId) && Func.equalsSafe(item.getShareBy(),sampleId))
                                            .findAny().orElse(null);
                                    share = Func.isNotEmpty(objectRefer);
                                }
                                MatrixTableSampleNoListRsp sampleNoListRsp = new MatrixTableSampleNoListRsp();
                                sampleNoListRsp.setSampleNo(sampleNo);
                                sampleNoListRsp.setActive(activeIndicator);
                                finalReferSourceMatrixs.stream().filter(p -> StringUtils.equalsIgnoreCase(p.getCurrentSampleId(), sampleInfo.getID()) && NumberUtil.equals(p.getTestLineId(), dto.getTestLineId()))
                                        .findFirst().ifPresent(c -> {
                                    sampleNoListRsp.setRefer(true);
                                });
                                sampleNoListRsp.setShare(share);
                                sampleNoList.add(sampleNoListRsp);
                            }
                        }
                    }
                }else if(matrixGroupId>0){
                    //用for循环，为了保持sample的顺序
                    List<MatrixGroupSampleInfoDTO> groupSampleList = matrixGroupSampleList.stream().filter(m -> NumberUtil.equals(m.getMatrixGroupId(), matrixGroupId)).collect(Collectors.toList());
                    if(CollectionUtils.isNotEmpty(groupSampleList)) {
                        for (MatrixGroupSampleInfoDTO sampleInfo : groupSampleList) {
                            String sampleId = sampleInfo.getID();
                            List<TestMatrixPO> assignMatrix = mtList.stream().filter(m -> (
                                    sampleId.equals(m.getTestSampleID())
                                            && m.getMatrixGroupId().equals(sampleInfo.getMatrixGroupId())
                                            && m.getTestLineInstanceID().equals(sampleInfo.getTestLineInstanceID())
                            )).collect(Collectors.toList());
                            if (CollectionUtils.isNotEmpty(assignMatrix)) {
                                TestMatrixPO testMatrixPO = assignMatrix.get(0);
                                Boolean activeIndicator = testMatrixPO.getActiveIndicator();
                                String sampleNo = sampleInfo.getSampleNo();
                                // 过滤当前object_refer 数据
                                Boolean share = false;
                                if(Func.isNotEmpty(objectReferList)){
                                    ObjectReferInfoPO objectRefer = objectReferList.stream().filter(item->Func.equalsSafe(item.getObjectId(),sampleInfo.getTestLineInstanceID()) && Func.equalsSafe(item.getShareBy(),sampleId))
                                            .findAny().orElse(null);
                                    share = Func.isNotEmpty(objectRefer);
                                }
                                MatrixTableSampleNoListRsp sampleNoListRsp = new MatrixTableSampleNoListRsp();
                                sampleNoListRsp.setSampleNo(sampleNo);
                                sampleNoListRsp.setActive(activeIndicator);
                                sampleNoListRsp.setRefer(false);
                                sampleNoListRsp.setShare(share);
                                sampleNoList.add(sampleNoListRsp);
                            }
                        }
                    }
                }
                if(Func.isNotEmpty(sampleNoList)){
                    rsp.setSampleNo(sampleNoList.stream().map(MatrixTableSampleNoListRsp::getSampleNo).collect(Collectors.joining(",")));
                }
                rsp.setSampleNoList(sampleNoList);
            }
            //处理wi
//            if(CollectionUtils.isNotEmpty(testLineWorkInstructionRelInfoPOS)){
//                //OrderCreation
//                TestLineWorkInstructionRelInfoPO orderCreationWI=testLineWorkInstructionRelInfoPOS.stream().filter(e->e.getTestLineVersionId().longValue()==dto.getTestLineVersionId()&&e.getCategoryName().contains("OrderCreation")).findFirst().orElse(null);
//                if(orderCreationWI!=null){
//                    //get CHI
//                    rsp.setOrderCreationWIText(orderCreationWI.getWorkInstructionText());
//                    TestLineWorkInstructionLanguageInfoExample objTestLineWorkInstructionLanguageInfoExample=new TestLineWorkInstructionLanguageInfoExample();
//                    objTestLineWorkInstructionLanguageInfoExample.createCriteria().andLangStatusEqualTo(1).andWorkInstructionBaseIdEqualTo(orderCreationWI.getId());
//                    List<TestLineWorkInstructionLanguageInfoPO> testLineWorkInstructionLanguageInfoPOS=workInstructionLanguageInfoMapper.selectByExample(objTestLineWorkInstructionLanguageInfoExample);
//                    if(CollectionUtils.isNotEmpty(testLineWorkInstructionLanguageInfoPOS)){
//                        rsp.setOrderCreationWIText(testLineWorkInstructionLanguageInfoPOS.get(0).getWorkInstructionText());
//                    }
//                }
//
//                //Sample Segegration
//                List<TestLineWorkInstructionRelInfoPO> sampleSegerationWI=testLineWorkInstructionRelInfoPOS.stream().filter(e->e.getTestLineVersionId().longValue()==dto.getTestLineVersionId()&&e.getCategoryName().contains("Sample Segregation")).collect(Collectors.toList());
//                if(CollectionUtils.isNotEmpty(sampleSegerationWI)){
//                    List<TestLineWorkInstructionRsp> testLineWorkInstructionRsps=Lists.newArrayList();
//                    sampleSegerationWI.forEach(e->{
//                        TestLineWorkInstructionRsp testLineWorkInstructionRsp=new TestLineWorkInstructionRsp();
//                        testLineWorkInstructionRsp.setWorkInstructionId(e.getWorkInstructionId());
//                        testLineWorkInstructionRsp.setWorkInstructionName(e.getWorkInstructionName());
//                        testLineWorkInstructionRsp.setWorkInstructionText(e.getWorkInstructionText());
//                        testLineWorkInstructionRsps.add(testLineWorkInstructionRsp);
//                    });
//                    rsp.setTestLineWorkInstructionRspList(testLineWorkInstructionRsps);
//                }
//            }

            //处理condition
            MatrixTableConditionDTO conditionDTO = this.handlerConditionNameWithStatus(dto,mtList,sampleList,allTestConditionInfoPOS,chineseFlag);
            if(! Objects.isNull(conditionDTO) && StringUtils.isNotBlank(conditionDTO.getTestCondition())){
                rsp.setConditionName(conditionDTO.getTestCondition());
                if(conditionDTO.getConditionStatus()!=null){
                    rsp.setConditionStatus(conditionDTO.getConditionStatus());
                }
            }else{
                ConditionStatus conditionStatus = conditionCount> 0 ? ConditionStatus.UnConfirmed : ConditionStatus.NoCondition;
                rsp.setConditionStatus(conditionStatus.getStatus());
            }
            //处理labSection
            String labSectionName = buildMatrixTableLabSectionName(rsp,labSectionBaseInfoList, allTestLineLabSectionList, dto);
            rsp.setLabSection(labSectionName);

            //處理analyte
            AnalyteExtPO analyteExtPO;
            List<AnalyteExtPO> analyteExtPOList = testLineAnalyteDtoList.getOrDefault(dto.getTestLineInstanceId(), null);
            if(Func.isNotEmpty(analyteExtPOList)){
                analyteExtPO = analyteExtPOList.get(0);
            }else{
                analyteExtPO = null;
            }

            List<String> analyteNames = new ArrayList<>();
            if(Func.isNotEmpty(analyteExtPO)){
                if(Func.isEmpty(analyteExtPO.getAnalyteFlag())){
                    analyteNames = analyteExtPO.getAnalyteList().stream().map(TestLineAnalyteDTO::getTestAnalyteName).collect(Collectors.toList());
                }else{
                    analyteNames = Lists.newArrayList(AnalyteSectionTypeEnums.findType(analyteExtPO.getAnalyteFlag()).getName());
                }
            }
            rsp.setAnalyteText(analyteNames);
            if(Func.isNotEmpty(jobTestLineRspList)){
                List<JobTestLineRsp> jobTestLineRsps = jobTestLineRspList.stream().filter(item -> Func.equalsSafe(item.getTestLineInstanceId(), dto.getTestLineInstanceId())).collect(Collectors.toList());
                rsp.setJobTestLineRspList(jobTestLineRsps);
            }
            List<Long> labSectionSelectedList = new ArrayList<>();
            if(Func.isNotEmpty(allTestLineLabSectionList)){
                List<Long> labSectionIdList = allTestLineLabSectionList.stream().filter(item->Func.equalsSafe(item.getTestLineInstanceId(),dto.getTestLineInstanceId())).map(TestLineLabSectionDTO::getLabSectionId).collect(Collectors.toList());
                labSectionSelectedList = labSectionIdList;
            }
            rsp.setLabSectionSelectedList(labSectionSelectedList);
            list.add(rsp);
        }
        List<String> testLineInstanceIdList = matrixTableDTOS.stream().map(MatrixTableDTO::getTestLineInstanceId).collect(Collectors.toList());
        OrderTestLineListSearchReq testLineListSearchReq = new OrderTestLineListSearchReq();
        testLineListSearchReq.setOrderNo(orderNo);
        testLineListSearchReq.setTestLineInstanceIdList(testLineInstanceIdList);
        testLineListSearchReq.setMatrixFlag(true);
        testLineListSearchReq.setNeedShowDoubleLanguage(true);
        Long startDate = new Date().getTime();
        List<OrderTestLineListDTO> orderTestLineInfoList = orderTestLineService.getOrderTestLineInfoList(testLineListSearchReq);
        log.info("queryPPTestLineList查询getOrderTestLineInfoList,耗时："+(System.currentTimeMillis()-startDate)+"毫秒");

//        Map<String, OrderTestLineListDTO> testLineListDTOMap = null;
//        if(Func.isNotEmpty(orderTestLineInfoList)){
//            testLineListDTOMap = orderTestLineInfoList.stream().collect(Collectors.toMap(OrderTestLineListDTO :: getTestLineInstanceId, o -> o,(key1 , key2)-> key2 ));
//        }

        //多语言
        if (chineseFlag) {
            transferEvaluationAliasLang(orderId, orderNo, list);
        }

        //通过Local Trims 获取citationName
//        List<OrderTestLineCitationNameDTO> citationNames = citationUtil.getCitationNames(orderNo,defaultLanguageCode);
//        Map<String, OrderTestLineListDTO> finalTestLineListDTOMap = testLineListDTOMap;
        Long startDate3 = new Date().getTime();
        list.forEach(matrixTableListRsp -> {
            /*if(Func.isNotEmpty(citationNames)){
                OrderTestLineCitationNameDTO orderTestLineCitationNameDTO = citationNames.stream().filter(temp->{
                   return Func.equals(matrixTableListRsp.getTestLineInstanceId(),temp.getTestLineInstanceId());
                }).findFirst().orElse(null);
                if(Func.isNotEmpty(orderTestLineCitationNameDTO)) {
                    matrixTableListRsp.setTestStandard(orderTestLineCitationNameDTO.getCitationFullName());
                }
            }*/
            OrderTestLineListDTO orderTestLineListDTO = orderTestLineInfoList.stream().filter(i->Func.equals(matrixTableListRsp.getTestLineInstanceId(),i.getTestLineInstanceId()) && Func.equals(matrixTableListRsp.getPpArtifactRelId(),i.getPpArtifactRelId())).findFirst().orElse(null);
            /*if(Func.isNotEmpty(finalTestLineListDTOMap)){
                 orderTestLineListDTO = finalTestLineListDTOMap.getOrDefault(matrixTableListRsp.getTestLineInstanceId(), null);
            }*/
            if(Func.isNotEmpty(orderTestLineListDTO)){
                matrixTableListRsp.setPpNotes(orderTestLineListDTO.getPpNotes());
                matrixTableListRsp.setEvaluationAlias(orderTestLineListDTO.getTestItem());
                matrixTableListRsp.setTestItem(orderTestLineListDTO.getTestItem());
                matrixTableListRsp.setTestItemCn(orderTestLineListDTO.getTestItemCn());
                matrixTableListRsp.setLabTeamName(orderTestLineListDTO.getLabTeamCode());
                matrixTableListRsp.setTestStandard(orderTestLineListDTO.getTestStandard());
                matrixTableListRsp.setTestStandardCn(orderTestLineListDTO.getTestStandardCn());
                matrixTableListRsp.setTestLineStatus(orderTestLineListDTO.getTestLineStatus());
                matrixTableListRsp.setPpName(orderTestLineListDTO.getPpName());
                matrixTableListRsp.setPpNameCn(orderTestLineListDTO.getPpNameCn());
                matrixTableListRsp.setPpBaseId(orderTestLineListDTO.getPpBaseId());
                matrixTableListRsp.setOriginTestStandard(orderTestLineListDTO.getOriginTestStandard());
                matrixTableListRsp.setOriginTestStandardCn(orderTestLineListDTO.getOriginTestStandardCn());
            }
            // PP Notes/ WI for CS 显示控制
            matrixTableListRsp.setPpNotes(Func.isEmpty(matrixTableListRsp.getPpNotes())?matrixTableListRsp.getWiForCsText():matrixTableListRsp.getPpNotes());
        });
        log.info("queryPPTestLineList查询处理名称显示,耗时："+(System.currentTimeMillis()-startDate3)+"毫秒");
        buildVTLPPInfo(list);
        PageInfo<MatrixTableListRsp> pageInfo = PageInfo.of(list);
        pageInfo.setPageNum(page.getPageNum());
        pageInfo.setPageSize(page.getPageSize());
        pageInfo.setTotal(page.getTotal());
        return pageInfo;
    }

    private void buildVTLPPInfo(List<MatrixTableListRsp> matrixTableDTOS){
        if(Func.isEmpty(matrixTableDTOS)){
            return;
        }
        List<String> ppTLRelIds = matrixTableDTOS.stream().filter(e -> TestLineType.check(e.getTestLineType(),TestLineType.VIRTAUL_TL)).map(MatrixTableListRsp::getPpTlRelId).collect(Collectors.toList());
        if(Func.isEmpty(ppTLRelIds)){
            return;
        }
        List<PPTestLineRelationshipInfoPO> ppTestLineRelDTOS = ppTestLineRelMapper.getByIds(ppTLRelIds);
        Set<Long> ppBaseIds = ppTestLineRelDTOS.stream().map(PPTestLineRelationshipInfoPO::getPpBaseId).collect(Collectors.toSet());
        GetPpInfoReq getPpInfoReq = new GetPpInfoReq();
        getPpInfoReq.setPpBaseIds(ppBaseIds);
        List<GetPpBaseInfoRsp> ppBaseInfoRspList = ppClient.getPpBaseInfo(getPpInfoReq);
        if(Func.isEmpty(ppBaseInfoRspList)){
            return;
        }
        matrixTableDTOS.stream().forEach(matrixTableDTO -> {
            if(TestLineType.check(matrixTableDTO.getTestLineType(),TestLineType.VIRTAUL_TL) && Func.isEmpty(matrixTableDTO.getPpNo())){
                PPTestLineRelationshipInfoPO ppTestLineRelationshipInfoPO = ppTestLineRelDTOS.stream().filter(e -> Func.equals(matrixTableDTO.getPpTlRelId(),e.getID())).findFirst().orElse(null);
                if(Func.isNotEmpty(ppTestLineRelationshipInfoPO)){
                    GetPpBaseInfoRsp getPpBaseInfoRsp = ppBaseInfoRspList.stream().filter(e -> Func.equals(ppTestLineRelationshipInfoPO.getPpBaseId(),e.getPpBaseId())).findFirst().orElse(null);
                    if(Func.isNotEmpty(getPpBaseInfoRsp)){
                        matrixTableDTO.setPpNo(getPpBaseInfoRsp.getPpNo());
                        matrixTableDTO.setPpName(getPpBaseInfoRsp.getPpName());
                        matrixTableDTO.setPpBaseId(getPpBaseInfoRsp.getPpBaseId());
                    }
                }
            }
        });
    }
    private String buildMatrixTableLabSectionName(MatrixTableListRsp rsp,List<GetLabSectionBaseInfoRsp> labSectionBaseInfoList,List<TestLineLabSectionDTO> allTestLineLabSectionList,MatrixTableDTO matrixTableDTO){
        String labSectionName = "";
        String subContractLabCode = matrixTableDTO.getSubcontractLabCode();
        if(Func.isNotEmpty(labSectionBaseInfoList) && Func.isNotEmpty(allTestLineLabSectionList)){
            List<TestLineLabSectionDTO> testLineLabSectionList = allTestLineLabSectionList.stream().filter(item -> Func.equals(matrixTableDTO.getTestLineInstanceId(), item.getTestLineInstanceId())).collect(Collectors.toList());
            Set<Long> labsectionBaseIdSets = testLineLabSectionList.stream().map(TestLineLabSectionDTO::getLabSectionBaseId).collect(Collectors.toSet());
            List<String> labSectionNameList = labSectionBaseInfoList.stream().filter(item -> labsectionBaseIdSets.contains(item.getLabSectionBaseId())).distinct().map(GetLabSectionBaseInfoRsp::getLabSectionName).distinct().collect(Collectors.toList());
            rsp.setLabSectionNameList(labSectionNameList);
            labSectionName = labSectionBaseInfoList.stream().filter(item->labsectionBaseIdSets.contains(item.getLabSectionBaseId())).distinct().map(GetLabSectionBaseInfoRsp::getLabSectionName).collect(Collectors.joining(","));
        }
        if(Func.isNotEmpty(labSectionName) && Func.isNotEmpty(subContractLabCode)){
            labSectionName += "||";
            labSectionName += subContractLabCode;
        }else{
            labSectionName += Func.toStr(subContractLabCode);
        }
        labSectionName = StringUtils.defaultIfBlank(labSectionName,"/");
        return labSectionName;
    }

    /**
     * 构建WI信息
     * @param matrixTableDTOS
     */
    private void buildWI(List<MatrixTableDTO> matrixTableDTOS,Integer productLineId){
        List<Integer> testLineVersionIds = matrixTableDTOS.stream().map(MatrixTableDTO::getTestLineVersionId).distinct().collect(Collectors.toList());
        BaseResponse<List<WorkingInstructionRsp>> response = workingInstructionService.getWIForCSBymatrixTableDTOS(matrixTableDTOS,productLineId);
        List<WorkingInstructionRsp> workingInstructionRsps = response.getData();
        if(Func.isNotEmpty(workingInstructionRsps)){
            boolean chineseFlag =  isChinese(null);
            matrixTableDTOS.forEach(matrixTableDTO -> {
                WorkingInstructionRsp testLineWI= workingInstructionRsps.stream().filter(workingInstructionRsp ->NumberUtil.equals(matrixTableDTO.getTestLineVersionId(),workingInstructionRsp.getTestLineVersionId()) &&  NumberUtil.equals(Func.isEmpty(matrixTableDTO.getPpCitationBaseId())?matrixTableDTO.getCitationBaseId():matrixTableDTO.getPpCitationBaseId(),workingInstructionRsp.getReqCitationBaseId(),false)).findAny().orElse(null);
                if(Func.isNotEmpty(testLineWI)){
                    matrixTableDTO.setWiForCsBaseId(Func.toLong(testLineWI.getWorkInstructionId()));
                    matrixTableDTO.setWiForCsText(testLineWI.getWorkingInstructionText());
                    if(Func.isNotEmpty(testLineWI.getLanguages())&&chineseFlag){
                        WorkingInstructionLanRsp chineseWI = testLineWI.getLanguages().stream().filter(workingInstructionLanRsp -> Func.equals(LanguageType.Chinese.getLanguageId(),workingInstructionLanRsp.getLanguageId())).findFirst().orElse(null);
                        if(Func.isNotEmpty(chineseWI)) {
                            matrixTableDTO.setWiForCsText(chineseWI.getWorkingInstructionText());
                        }
                    }
                }
            });
        }
    }

    /**
     * 多语言处理
     * @param orderId id
     * @param orderNo no
     * @param list matrixTable
     */
    private void transferEvaluationAliasLang(String orderId, String orderNo, List<MatrixTableListRsp> list) {
        //
        List<OrderLanguageRelInfoPO> orderLanguageRelInfoPODBs=this.getOrderLanguageRelInfoPOS(orderId);
        List<TestLineLanguageInfoPO> testLineLanguageBaseInfoReqPOS = getTestLineLanguageInfoPOS(list.stream().map(e->e.getTestLineBaseId()).collect(Collectors.toList()));

        //pp notes处理
        List<ArtifactCitationLangInfoWithBLOBs> testLineCitationLanguageInfoPOS = testLineCitationLangMapper.getTestLineCitationLangList(orderId);
        for (MatrixTableListRsp matrixTableListRsp : list) {
            if (CollectionUtils.isNotEmpty(testLineLanguageBaseInfoReqPOS)) {
                orderLanguageRelInfoPODBs.stream().filter(e->e.getLangType()!=null
                        && e.getLangType()== LangTypeEnum.TestLine.getType()
                        &&e.getObjectBaseId().longValue()==matrixTableListRsp.getTestLineBaseId().longValue()
                        &&LanguageType.Chinese.getLanguageId()== e.getLanguageId())
                        .findFirst().ifPresent(orderLanguageRelInfoPO-> {
                    testLineLanguageBaseInfoReqPOS.stream().filter(e -> e.getLangId().longValue() == orderLanguageRelInfoPO.getLangBaseId()).
                            findFirst().ifPresent(objTestLineLanguageInfoPO -> {
                        matrixTableListRsp.setPpNotes(objTestLineLanguageInfoPO.getMethodDesc());
                    });
                });
            }

            testLineCitationLanguageInfoPOS.stream().filter(e-> NumberUtil.equals(e.getCitationBaseId(), matrixTableListRsp.getCitationBaseId())).findFirst().ifPresent(objTestLineCitationLanguageInfoPO->{
                matrixTableListRsp.setEvaluationAlias(objTestLineCitationLanguageInfoPO.getEvaluationAlias());
            });
        }

        //DIG-4691 处理PP-TL的情况
        TestLineEvaluationAliasUtil.buildEvaluationAliasMultiLanguage(list, LanguageType.Chinese);
        //testLineLocalService.buildReportLang(orderNo,list);

        //由于新组件使用testitem，需要把evaluationAlise -> testitem
        for (MatrixTableListRsp matrixTableListRsp : list) {
            //处理copy test的情况：如果是copy test，只要跟着testlineinstanceIdf瞳即可。
            if(matrixTableListRsp.getMatrixGroupId() > 0) {
                list.stream().filter(p -> {
                    return StringUtils.equalsIgnoreCase(p.getTestLineInstanceId(), matrixTableListRsp.getTestLineInstanceId())
                            && p.getMatrixGroupId() == 0;
                }).findFirst().ifPresent(c -> {
                    matrixTableListRsp.setEvaluationAlias(c.getEvaluationAlias());
                });
            }

            //TestItemu前面已赋值，再重新赋值EvaluationAlias时，要判断
            if (StringUtils.isNotBlank(matrixTableListRsp.getEvaluationAlias())) {
                matrixTableListRsp.setTestItem(matrixTableListRsp.getEvaluationAlias());
            }
            // OOB业务场景下 testItemName 优先显示customerTestLineName
            Integer testLineType = matrixTableListRsp.getTestLineType();
            if(Func.isNotEmpty(testLineType) && ((testLineType & TestLineType.OOB_TEST.getType())==TestLineType.OOB_TEST.getType())){
                String testLineName = matrixTableListRsp.getCustomerTestLineNameCN();
                if(Func.isNotEmpty(testLineName)){
                    matrixTableListRsp.setTestItem(testLineName);
                }
            }
        }
    }

    /**
     *
     * @param reqObject
     * @return
     */
//    @TestLinePending(filedName ="ppTestLineRels.ppTestLineRelId",type=TestLinePendingTypeEnums.PP_TL_REL_ID_List )
    @AccessPolicyRule(testLinePendingType = TestLinePendingTypeEnums.PpTestLineRelId)
    public CustomResult<List<MergePPTestLineRelListRsp>> getPPTestLineRelList(OrderPpTestLineReq reqObject){
        CustomResult<List<MergePPTestLineRelListRsp>> rspResult = new CustomResult<>();
        List<PPTestLineRelReq> ppTestLineRels = reqObject.getPpTestLineRels();
        if (ppTestLineRels == null || ppTestLineRels.isEmpty()){
            rspResult.setMsg("参数ppTestLineRels不能为空");
            return rspResult;
        }
        if(StringUtils.isBlank(reqObject.getActionType())) {
            rspResult.setMsg("参数actionType不能为空");
            return rspResult;
        }

        List<MatrixTestLineRelInfo> oldPpTestLineRels = testLineMapper.getPPTestLineRelList(reqObject.getOrderNo());
        for (PPTestLineRelReq ppTestLineRel : ppTestLineRels) {
            if (StringUtils.isBlank(ppTestLineRel.getPpTestLineRelId())) {
                continue;
            }
            oldPpTestLineRels.stream().filter(p -> StringUtils.equalsIgnoreCase(p.getPpTestLineRelId(), ppTestLineRel.getPpTestLineRelId())).findFirst().ifPresent(q -> {
                ppTestLineRel.setTestLineInstanceId(q.getTestLineInstanceId());
            });
        }

        List<MergePPTestLineRelListRsp> list;
        if (StringUtils.equalsIgnoreCase(reqObject.getActionType(), MatrixTableActionTypeEnum.UPDATE_STANDARD.getName())){
            list = ppTestLineRels.stream().map(m -> {
                MergePPTestLineRelListRsp mergePPTestLineRelListRsp = new MergePPTestLineRelListRsp();
                mergePPTestLineRelListRsp.setTestLineInstanceId(m.getTestLineInstanceId());
                mergePPTestLineRelListRsp.setMatrixGroupId(0);
                return mergePPTestLineRelListRsp;
            }).distinct().collect(Collectors.toList());
        } else if (StringUtils.equalsIgnoreCase(reqObject.getActionType(), MatrixTableActionTypeEnum.UPDATE_CONDITION.getName())
                || StringUtils.equalsIgnoreCase(reqObject.getActionType(), MatrixTableActionTypeEnum.CONFIRM_CONDITION.getName())){
            list = ppTestLineRels.stream().map(m -> {
                MergePPTestLineRelListRsp mergePPTestLineRelListRsp = new MergePPTestLineRelListRsp();
                mergePPTestLineRelListRsp.setTestLineInstanceId(m.getTestLineInstanceId());
                mergePPTestLineRelListRsp.setMatrixGroupId(m.getMatrixGroupId());
                return mergePPTestLineRelListRsp;
            }).distinct().collect(Collectors.toList());
        } else {
            rspResult.setMsg(String.format("参数actionType不支持%s",reqObject.getActionType()));
            return rspResult;
        }

        rspResult.setSuccess(CollectionUtils.isNotEmpty(list));
        rspResult.setData(list);
        return rspResult;
    }

    /**
     * 根据PPTLIDs获取去重后的TL，用于update standard
     * @param testStandardListByPPTlRelReq testStandardListByPPTlRelReq
     * @return QueryTestStandardRsp
     */
//    @TestLinePending(filedName = "ppTestLineRels.testLineInstanceId",type=TestLinePendingTypeEnums.TL_ID_List)
    @AccessPolicyRule(testLinePendingType = TestLinePendingTypeEnums.TestLineStandardQuery)
    public CustomResult<List<QueryTestStandardRsp>> extractTestStandardListByPPTlRel(TestStandardListByPPTlRelReq testStandardListByPPTlRelReq) {
        CustomResult<List<QueryTestStandardRsp>> customResult = new CustomResult<>();
        OrderPpTestLineReq orderPpTestLineReq = new OrderPpTestLineReq();
        orderPpTestLineReq.setOrderNo(testStandardListByPPTlRelReq.getOrderNo());
        orderPpTestLineReq.setActionType(MatrixTableActionTypeEnum.UPDATE_STANDARD.getName());

        List<PPTestLineRelReq> lineRelReqs = Lists.newArrayList();
        for (TestStandardListByPPTlRelItemReq testStandardListByPPTlRelItemReq : testStandardListByPPTlRelReq.getPpTestLineRels()) {
            PPTestLineRelReq ppTestLineRelReq = new PPTestLineRelReq();
            ppTestLineRelReq.setPpTestLineRelId(testStandardListByPPTlRelItemReq.getPpTestLineRelId());
            ppTestLineRelReq.setTestLineInstanceId(testStandardListByPPTlRelItemReq.getTestLineInstanceId());
            //update standard不受matrixgroup影响
            ppTestLineRelReq.setMatrixGroupId(0);
            lineRelReqs.add(ppTestLineRelReq);
        }
        orderPpTestLineReq.setPpTestLineRels(lineRelReqs);
        CustomResult<List<MergePPTestLineRelListRsp>> ppTestLineRelResult = getPPTestLineRelList(orderPpTestLineReq);
        if (! ppTestLineRelResult.isSuccess()) {
            customResult.setSuccess(false);
            customResult.setMsg(ppTestLineRelResult.getMsg());
            return customResult;
        }

        List<MergePPTestLineRelListRsp> mergePPTestLineRelListRsps = ppTestLineRelResult.getData();

        List<String> testLineInstanceIds = mergePPTestLineRelListRsps.stream().map(x -> x.getTestLineInstanceId()).distinct().collect(Collectors.toList());



        List<String> testLineInstanceIdList = testStandardListByPPTlRelReq.getPpTestLineRels().stream().map(TestStandardListByPPTlRelItemReq::getTestLineInstanceId).collect(Collectors.toList());
        if(Func.isEmpty(testLineInstanceIdList)){
            customResult.setSuccess(false);
            customResult.setMsg("Can not get testLineInstanceId");
            return customResult;
        }
        OrderTestLineListSearchReq testLineListSearchReq = new OrderTestLineListSearchReq();
        testLineListSearchReq.setTestLineInstanceIdList(testLineInstanceIdList);
        testLineListSearchReq.setOrderNo(testStandardListByPPTlRelReq.getOrderNo());
        List<OrderTestLineListDTO> orderTestLineInfoList = orderTestLineService.getOrderTestLineInfoList(testLineListSearchReq);


//        List<TestLineInstancePO> testLineInstancePOS =testLineMapper.getBaseTestLineByIds(testLineInstanceIds);
        if (CollectionUtils.isEmpty(orderTestLineInfoList)){
            customResult.setSuccess(false);
            customResult.setMsg("Can not get testLine!");
            return  customResult;
        }

        customResult.setSuccess(true);
        //获取用户语言,暂时支持中文，后边多语言拓展
        Boolean isChinese = false;
        UserInfo user = tokenClient.getUser();
//        String defaultLanguageCode = user.getDefaultLanguageCode();
        String defaultLanguageCode = frameWorkClient.getPrimaryLanguageCode(ProductLineContextHolder.getProductLineCode());
        if (!UserInfoDefaultLanguageCodeEnums.check(defaultLanguageCode,UserInfoDefaultLanguageCodeEnums.en_us)){
            isChinese = true;
        }
        List<QueryTestStandardRsp> queryTestStandardRsps = Lists.newArrayList();
        for (OrderTestLineListDTO orderTestLineListDTO : orderTestLineInfoList) {
            QueryTestStandardRsp queryTestStandardRsp = testLineService.buildQueryTestStandardRsp(orderTestLineListDTO,isChinese);
            queryTestStandardRsps.add(queryTestStandardRsp);
        }

        customResult.setData(queryTestStandardRsps);
        return customResult;
    }

    /**
     * 加载编辑窗口数据
     * @param req
     * @return
     */
//    @TestLinePending(filedName = "testLineInstanceId",type=TestLinePendingTypeEnums.TL_ID)
    @AccessPolicyRule(testLinePendingType = TestLinePendingTypeEnums.TestLineInstanceId)
    public CustomResult<TestLineEditDetailRsp> getTestLineEditDetail(SingleTestLienReq req) {
        String productLineCode = ProductLineContextHolder.getProductLineCode();
        String testLineInstanceId = req.getTestLineInstanceId();
        CustomResult<TestLineEditDetailRsp> customResult = new CustomResult<>();
        TestLineEditDetailRsp detailRsp = testLineMapper.getEditTestLineInfoById(testLineInstanceId);
        if(detailRsp==null){
            customResult.setSuccess(false);
            customResult.setMsg("TestLine No Data");
            return customResult;
        }
        //判断是否是FCM TL
        if(TestLineType.check(detailRsp.getTestLineType(), TestLineType.FCM)){
            customResult.setSuccess(false);
            customResult.setMsg(messageUtil.get("test.line.type.fcm.not.allow.operation",new Object[]{detailRsp.getTestLineId()}));
            return customResult;
        }
        String orderNo = detailRsp.getOrderNo();
        // 查询订单报告语言设置
        OrderIdReq orderIdReq = new OrderIdReq();
        orderIdReq.setOrderNo(orderNo);
        orderIdReq.setLabCode(LabCodeContextHolder.getLabCode());
        BaseResponse<TestRequestRsp> testRequestRsp = orderFacade.getTestRequestByOrderNo(orderIdReq);
        if(Func.isNotEmpty(testRequestRsp) && Func.isNotEmpty(testRequestRsp.getData())){
            detailRsp.setReportLanguage(testRequestRsp.getData().getReportLanguage());
        }
        OrderAllDTO orderAllDTO = orderFacade.getOrderForPe(orderIdReq).getData();
        boolean chinese = this.isChinese(orderNo);
        // Edit TL 时判断TL和Job/SubContract（Cancelled要排除）是否关联，已关联的置灰lab section选择框
        List<String> testLineInstanceIds = Lists.newArrayList();
        testLineInstanceIds.add(req.getTestLineInstanceId());
        List<TestLineInstancePO> testLineInstanceList = testLineInstanceExtMapper.getIndependentTestLine(testLineInstanceIds,orderNo);
        detailRsp.setLabSectionDisabled(Func.isEmpty(testLineInstanceList));

        List<TestLineLabSectionDTO> testLineLabSectionDTOS = testLineInstanceExtMapper.getTestLineLabSection(com.google.common.collect.Sets.newHashSet(testLineInstanceIds));
        if(Func.isNotEmpty(testLineLabSectionDTOS)){
            detailRsp.setLabSectionSelectedList(testLineLabSectionDTOS.stream().map(TestLineLabSectionDTO::getLabSectionId).collect(Collectors.toSet()));
        }

        Set<Integer> wiForSampleSets = Sets.newHashSet();
        wiForSampleSets.add(CategoryEnums.SamplePreparation_Cutting.getCode());
        wiForSampleSets.add(CategoryEnums.SamplePreparation_Injection.getCode());
        wiForSampleSets.add(CategoryEnums.SamplePreparation_Tableting.getCode());

        Set<Integer> wiForCsSets = Sets.newHashSet();
        wiForCsSets.add(CategoryEnums.WIForCS.getCode());



        AsyncCall asyncCall = new AsyncCall();
//        asyncCall.put(AsyncType.LabSection,()-> getLabSectionFromTrims(detailRsp));
        asyncCall.put(AsyncType.Analyte,()->getAnalyteFromTrims(detailRsp,chinese));
        asyncCall.put(AsyncType.WiForSample,()->getWorkInstructionFromTrims(productLineCode,detailRsp,wiForSampleSets,chinese,orderAllDTO.getBUID()));
        asyncCall.put(AsyncType.WiForCS,()->getWorkInstructionFromTrims(productLineCode,detailRsp,wiForCsSets,chinese,orderAllDTO.getBUID()));
        List<LabSectionRsp> labSectionList = getLabSectionFromTrims(detailRsp);
        List<AnalyteSelectRsp> analyteBaseInfoPOS = null;
        List<TestLineWorkInstructionRsp> wiForSamples = null;
        List<TestLineWorkInstructionRsp> wiForCs = null;
        List<AsyncResult> asyncResults = AsyncUtils.awaitResult(asyncCall);
        for(AsyncResult asyncResult:asyncResults){
            AsyncType asyncType = asyncResult.getTaskKey();
            switch (asyncType){
                case LabSection:
//                    labSectionList = asyncResult.getData();
                    break;
                case Analyte:
                    analyteBaseInfoPOS = asyncResult.getData();
                    break;
                case WiForSample:
                    wiForSamples = asyncResult.getData();
                    break;
                case WiForCS:
                    wiForCs = asyncResult.getData();
                    break;
            }
        }
        detailRsp.setLabSectionList(labSectionList);
        detailRsp.setAnalyteList(analyteBaseInfoPOS);
        //获取wiForSample
        detailRsp.setWiForSampleList(wiForSamples);
        if(Func.isNotEmpty(wiForCs)){
            detailRsp.setWiForCsText(wiForCs.get(0).getWorkInstructionText());
        }
        // 查询tl对应的ppNotes 查询最先关联的PP下维护的specificNotes
        OrderTestLineListSearchReq testLineListSearchReq = new OrderTestLineListSearchReq();
        testLineListSearchReq.setTestLineInstanceId(testLineInstanceId);
        List<OrderTestLineListDTO> orderTestLineList = orderTestLineService.getOrderTestLineInfoList(testLineListSearchReq);
        if(Func.isNotEmpty(orderTestLineList)){
            OrderTestLineListDTO orderTestLine = orderTestLineList.stream().sorted(Comparator.comparing(OrderTestLineListDTO::getPpTlRelCreatedDate)).collect(Collectors.toList()).get(0);
            if (Func.isNotEmpty(orderTestLine)){
                detailRsp.setTestStandard(orderTestLine.getTestStandard());
            }
        }
        if(Func.isNotEmpty(analyteBaseInfoPOS)){
            List<AnalyteSelectRsp> mandatoryAnalyte = analyteBaseInfoPOS.stream()
                    .filter(an -> AnalyteSectionTypeEnums.check(an.getSelectionType(), AnalyteSectionTypeEnums.Mandatory))
                    .collect(Collectors.toList());
            if(CollectionUtils.isNotEmpty(mandatoryAnalyte)){
                Set<Integer> mandatoryAnalyteId = mandatoryAnalyte.stream()
                        .map(an -> an.getTestAnalyteId()).collect(Collectors.toSet());
                List<Integer> analyteIds = detailRsp.getAnalyteIds();
                mandatoryAnalyteId.addAll(analyteIds);
                detailRsp.setAnalyteIds(Lists.newArrayList(mandatoryAnalyteId));
            }
           /* if(analyteBaseInfoPOS.size()==1 && Func.isNotEmpty(analyteBaseInfoPOS.get(0).getTestAnalyteId())){
                detailRsp.setAnalyteIds(Lists.newArrayList(analyteBaseInfoPOS.get(0).getTestAnalyteId()));
            }*/
        }
        //获取labTeam commonService 接口
        List<LabTeam> labTeam = this.getLabTeam(detailRsp.getTestLineId());
        detailRsp.setLabTeamList(labTeam);

        //获取BUPram配置，LabSection是否为必填
        BuParamReq buParamReq = new BuParamReq();
        buParamReq.setGroupCode(Constants.BU_PARAM.TEST.GROUP);
        buParamReq.setParamCode(Constants.BU_PARAM.TEST.CODE.LAB_SECTION_REQUIRED);
        buParamReq.setProductLineCode(ProductLineContextHolder.getProductLineCode());
        BuParamValueRsp buParamValueRsp = frameWorkClient.getBuParam(buParamReq);
        boolean labSectionRequired = true;
        if(Func.isNotEmpty(buParamValueRsp)){
            String paramValue = buParamValueRsp.getParamValue();
            paramValue = paramValue.toLowerCase();
            labSectionRequired = Boolean.parseBoolean(paramValue);
        }
        // 分包出去的TestLine不需要必填
        if(TestLineType.check(detailRsp.getTestLineType(),TestLineType.SubContractOrder)){
            labSectionRequired = false;
        }
        detailRsp.setLabSectionRequired(labSectionRequired);
        // 根据aid数组设置TL是否是PP添加
        if(Func.isNotEmpty(detailRsp.getAftifactIds())){
            detailRsp.setPpFlag(Func.isNotEmpty(detailRsp.getAftifactIds().stream().filter(aid-> Func.isNotEmpty(aid) && aid > 0).findAny().orElse(null)));
        }
        customResult.setData(detailRsp);
        customResult.setSuccess(true);
        return customResult;
    }
    private List<TlConditionDTO> getPpConditionFromTrims(GetConditionListReq getConditionListReq,boolean chinese){
        List<TlConditionDTO> testConditionList = Lists.newArrayList();
        log.info("conditionFacade---->getTestLineDefaultConditionList  request: {}", JSON.toJSONString(getConditionListReq));
        com.sgs.trimslocal.facade.model.common.BaseResponse<List<TestConditionRsp>> conditionRes =  conditionFacade.getTestLineDefaultConditionList(getConditionListReq);
        log.info("conditionFacade---->getTestLineDefaultConditionList  response: {}", JSON.toJSONString(conditionRes));
        if(Func.isEmpty(conditionRes) || Func.isEmpty(conditionRes.getData())){
            return testConditionList;
        }
        List<TestConditionRsp> testConditionRspList = conditionRes.getData();
        testConditionRspList.stream().forEach(type->{
            List<TestConditionItemRsp> testConditionItemList = type.getTestConditions();
            String testConditionTypeName = type.getTestConditionTypeName();
            if(chinese){
                List<TestConditionTypeLanguageRsp> testConditionTypeLanguageRsp = type.getLanguages();
                if(Func.isNotEmpty(testConditionTypeLanguageRsp)){
                    TestConditionTypeLanguageRsp testConditionTypeLanguage = testConditionTypeLanguageRsp.stream()
                            .filter(language -> Func.equals(LanguageType.Chinese.getLanguageId(),language.getLanguageId())).findFirst()
                            .orElse(new TestConditionTypeLanguageRsp());
                    if(Func.isNotEmpty(testConditionTypeLanguage)){
                        testConditionTypeName = testConditionTypeLanguage.getTestConditionTypeName();
                    }
                }
            }
            if(Func.isNotEmpty(testConditionItemList)){
                String finalTestConditionTypeName = testConditionTypeName;
                testConditionItemList.stream().forEach(testCondition->{
                    TlConditionDTO testConditionInfoPO = new TlConditionDTO();
                    testConditionInfoPO.setTestConditionId(testCondition.getTestConditionId().toString());
                    testConditionInfoPO.setTestConditionName(testCondition.getTestConditionName());
                    testConditionInfoPO.setTestConditionDesc(testCondition.getTestConditionDesc());
                    testConditionInfoPO.setTestConditionTypeName(finalTestConditionTypeName);

                    if(chinese){
                        List<TestConditionLangRsp> testConditionLangRspList = testCondition.getLanguages();
                        if(Func.isNotEmpty(testConditionLangRspList)){
                            TestConditionLangRsp testConditionCn = testConditionLangRspList.stream()
                                    .filter(language -> Func.equals(LanguageType.Chinese.getLanguageId(),language.getLanguageId())).findFirst()
                                    .orElse(new TestConditionLangRsp());
                            if(Func.isNotEmpty(testConditionCn)){
                                testConditionInfoPO.setTestConditionDesc(testConditionCn.getTestConditionDesc());
                                testConditionInfoPO.setTestConditionName(testConditionCn.getTestConditionName());
                            }
                        }
                    }
                    testConditionList.add(testConditionInfoPO);
                });
            }
        });
        return testConditionList;
    }

    public List<AnalyteSelectRsp> getAnalyteFromTrims(TestLineEditDetailRsp detailRsp,boolean chinese){
        List<AnalyteSelectRsp> analyteList = Lists.newArrayList();
        QueryTestLineAnalyteReq queryTestLineAnalyteReq = new QueryTestLineAnalyteReq();
        List<TestLineAnalyteReq> analytes = Lists.newArrayList();
        TestLineAnalyteReq testLineAnalyteReq = new TestLineAnalyteReq();
        testLineAnalyteReq.setTestLineVersionId(detailRsp.getTestLineVersionID());
        Set<Integer> standardVersionIds = Sets.newHashSet();
        standardVersionIds.add(detailRsp.getStandardVersionId());
        testLineAnalyteReq.setStandardVersionIds(standardVersionIds);
        List<Integer> aftifactIds = detailRsp.getAftifactIds();
        if(Func.isNotEmpty(aftifactIds)){
            Integer aid = aftifactIds.stream().filter(p->Func.isNotEmpty(p)&&0!=p).findFirst().orElse(null);
            testLineAnalyteReq.setArtifactId(Func.isEmpty(aid)?null:Long.valueOf(aid));
        }
        analytes.add(testLineAnalyteReq);
        queryTestLineAnalyteReq.setAnalytes(analytes);
        queryTestLineAnalyteReq.setCallerBU(ProductLineContextHolder.getProductLineCode());
        log.info("analyteFacade---->getAnalyteFromTrims  request: {}", JSON.toJSONString(queryTestLineAnalyteReq));
        com.sgs.trimslocal.facade.model.common.BaseResponse<List<TestLineAnalyteRsp>> res =  analyteFacade.getTestLineAnalyteList(queryTestLineAnalyteReq);
        log.info("analyteFacade---->getAnalyteFromTrims  response: {}", JSON.toJSONString(res));
        if(Func.isEmpty(res)||Func.isEmpty(res.getData())){
            return analyteList;
        }
        List<TestLineAnalyteRsp> trimsAnalyteList = res.getData();
        trimsAnalyteList.stream().forEach(analyte->{
            AnalyteSelectRsp analyteSelectRsp = new AnalyteSelectRsp();
            analyteSelectRsp.setAnalyteBaseId(analyte.getAnalyteBaseId());
            analyteSelectRsp.setTestAnalyteId(analyte.getTestAnalyteId());
            analyteSelectRsp.setSelectionType(analyte.getSelectionType());
            if(chinese){
                List<TestAnalyteLangReq> analyteLanguages = analyte.getLanguages();
                if(Func.isNotEmpty(analyteLanguages)){
                    analyteSelectRsp.setTestAnalyteDesc(analyteLanguages.stream()
                            .filter(language -> language.getLanguageId()==LanguageType.Chinese.getLanguageId()).findFirst()
                            .orElse(new TestAnalyteLangReq()).getTestAnalyteDesc());
                }
            }
            if(Func.isEmpty(analyteSelectRsp.getTestAnalyteDesc()))
            {
                analyteSelectRsp.setTestAnalyteDesc(analyte.getTestAnalyteDesc());
            }
            analyteList.add(analyteSelectRsp);
        });
        return analyteList;
    }

    private List<LabSectionRsp> getLabSectionFromTrims(TestLineEditDetailRsp detailRsp){
        List<LabSectionRsp> labSectionList = Lists.newArrayList();
        LabSectionListReq labSectionListReq = new LabSectionListReq();
        labSectionListReq.setOrderNo(detailRsp.getOrderNo());
        labSectionListReq.setExempt(true);
        List<LabSectionItemReq> labSectionItems = Lists.newArrayList();
        LabSectionItemReq itemReq = new LabSectionItemReq();
        itemReq.setTestLineInstanceId(detailRsp.getTestLineInstanceId());
        itemReq.setAid(0);
        List<Integer> aftifactIds = detailRsp.getAftifactIds();
        if(Func.isNotEmpty(aftifactIds)){
            Integer aid = aftifactIds.stream().filter(p->Func.isNotEmpty(p)&&0!=p).findFirst().orElse(null);
            itemReq.setAid(aid);
        }
        labSectionItems.add(itemReq);
        labSectionListReq.setTestLineInstanceIds(labSectionItems);
        BaseResponse<List<TestLineEditDetailRsp>> responseData = this.getLabSectionList(labSectionListReq);
        if(Func.isNotEmpty(responseData)&&Func.isNotEmpty(responseData.getData())){
            labSectionList = responseData.getData().get(0).getLabSectionList();
        }
        return labSectionList;
    }

    private List<TestLineWorkInstructionRsp> getWorkInstructionFromTrims(String productLineCode,TestLineEditDetailRsp detailRsp,Set<Integer> categoryIds,boolean chinese,Integer productLineId){
        ProductLineContextHolder.setProductLineCode(productLineCode);
        List<TestLineWorkInstructionRsp> workInstructionList = Lists.newArrayList();
        WorkingInstructionReq workingInstructionReq = new WorkingInstructionReq();
        Set<Integer> testLineVersionIds = new HashSet<>();
        testLineVersionIds.add(detailRsp.getTestLineVersionID());
        workingInstructionReq.setTestLineVersionIds(testLineVersionIds);
        workingInstructionReq.setCategoryIds(categoryIds);
        // 从Order上取
        String orderNo = detailRsp.getOrderNo();
        // 查询订单上的ProductLineId
        OrderIdReq orderIdReq = new OrderIdReq();
        orderIdReq.setOrderNo(orderNo);
        orderIdReq.setLabCode(LabCodeContextHolder.getLabCode());
        BaseResponse<OrderAllDTO> orderInfoRes = orderFacade.getOrderForPe(orderIdReq);
        if(Func.isNotEmpty(orderInfoRes) && Func.isNotEmpty(orderInfoRes.getData())){
            workingInstructionReq.setProductLineId(orderInfoRes.getData().getBUID());
        }
        // 查询CitationVersionId
        OrderCitationRelInfoExample citationRelInfoExample = new OrderCitationRelInfoExample();
        citationRelInfoExample.createCriteria().andOrderIdEqualTo(detailRsp.getOrderInstanceId());
        List<OrderCitationRelInfoPO> orderCitationRelList = orderCitationRelInfoMapper.selectByExample(citationRelInfoExample);
        if(Func.isNotEmpty(orderCitationRelList)){
            Set<Integer> testStandardVersionIds = Sets.newHashSet();
            orderCitationRelList.stream().forEach(cr ->{
                testStandardVersionIds.add(cr.getCitationBaseId().intValue());
            });
            if(Func.isNotEmpty(testStandardVersionIds)){
                Set<Long> citationBaseIds = Sets.newHashSet();
                testStandardVersionIds.stream().forEach(ts ->{
                    citationBaseIds.add(Func.toLong(ts));
                });
                workingInstructionReq.setCitationBaseId(citationBaseIds);
            }
        }
        workingInstructionReq.setCitationVersionId(new HashSet<>());


        //jira
        List<String> testLineInsIds = new ArrayList<>();
        testLineInsIds.add(detailRsp.getTestLineInstanceId());
        List<WIServiceDTO> dtos = new ArrayList<>();
        List<WIPO> wipos = testLineMapper.getWIPOinfo(testLineInsIds);
        if (Func.isNotEmpty(wipos)){
            dtos = iwiService.convertWIPOToWIServiceDTOS(wipos);
        }
        dtos.forEach(dto->{
            if (Func.isNotEmpty(categoryIds)){
                dto.setCategoryIds(categoryIds);
            }
            if (Func.isNotEmpty(productLineId)){
                dto.setProductLineId(productLineId);
            }
        });
        com.sgs.trimslocal.facade.model.common.BaseResponse<List<WorkingInstructionRsp>> response =  iwiService.getWIList(dtos);

        if(Func.isEmpty(response)||Func.isEmpty(response.getData())){
            return workInstructionList;
        }
        List<WorkingInstructionRsp> trimsWorkingInstructionList = response.getData();
        trimsWorkingInstructionList.stream().forEach(wi->{
            TestLineWorkInstructionRsp testLineWorkInstructionRsp = new TestLineWorkInstructionRsp();
            testLineWorkInstructionRsp.setCategoryId(wi.getCategoryId());
            testLineWorkInstructionRsp.setCategoryName(wi.getCategoryName());
            testLineWorkInstructionRsp.setWorkInstructionText(wi.getWorkingInstructionText());
            if(Func.isNotEmpty(wi.getWorkInstructionId())){
                testLineWorkInstructionRsp.setWorkInstructionId(Integer.valueOf(wi.getWorkInstructionId()));
            }
            // 多语言实现
            if(chinese){
                List<WorkingInstructionLanRsp> workingInstructions = wi.getLanguages();
                if(Func.isNotEmpty(workingInstructions)){
                    WorkingInstructionLanRsp workingInstructionCn = workingInstructions.stream()
                            .filter(language -> language.getLanguageId()==LanguageType.Chinese.getLanguageId()).findFirst()
                            .orElse(new WorkingInstructionLanRsp());
                    if(Func.isNotEmpty(workingInstructionCn)){
                        if(Func.isNotEmpty(workingInstructionCn.getWorkingInstructionText())){
                            testLineWorkInstructionRsp.setWorkInstructionText(workingInstructionCn.getWorkingInstructionText());
                        }
                        if(Func.isNotEmpty(workingInstructionCn.getCategoryName())){
                            testLineWorkInstructionRsp.setCategoryName(workingInstructionCn.getCategoryName());
                        }
                    }
                }
            }
            workInstructionList.add(testLineWorkInstructionRsp);
        });

        return workInstructionList;
    }
//
//    public List<AnalyteSelectRsp> getEditAnalyte(Integer testLineVersionID,Integer standardVersionId, List<Integer> ppVersionIds, boolean chinese) {
//        Map<Integer, TrimsAnalyteReq> testAnalyteMaps = Maps.newHashMap();
//
//        // 存在ppversionId的话，需要走多次查询，和confirm matrix 一样 以pp为维度
//        if(ppVersionIds == null || ppVersionIds.isEmpty()){
//            ppVersionIds = Lists.newArrayList(0);
//        }
//        for (Integer ppVersionId : ppVersionIds) {
//            TrimsAnalyteReq analyteReq = new TrimsAnalyteReq();
//            TlVersionIdentifier tlVersionIdentifier = new TlVersionIdentifier();
//            tlVersionIdentifier.setTlVersionIdentifier(testLineVersionID);
//            tlVersionIdentifier.setTsVersionIdentifier(standardVersionId);
//            analyteReq.setTlVersionIdentifiers(Lists.newArrayList(tlVersionIdentifier));
//
//            analyteReq.setPpVersionIdentifier(ppVersionId);
//
//            testAnalyteMaps.put(ppVersionId, analyteReq);
//        }
//        List<TestLineAnalyte> analyteList = trimsAnalyteService.getTestAnalyteInfoList(testAnalyteMaps);
//
//        //和confirm matrix 调用一样的查询方法
//        //转成页面需要的数据对象
//        List<AnalyteSelectRsp> analyteBaseInfoPOS = Lists.newArrayList();
//        for (TestLineAnalyte testLineAnalyte : analyteList) {
//            List<Analyte> testAnalyteItems = testLineAnalyte.getTestAnalyteItems();
//            analyteBaseInfoPOS = testAnalyteItems.stream().map(a->{
//                AnalyteSelectRsp rsp = new AnalyteSelectRsp();
//                rsp.setTestAnalyteId(a.getTestAnalyteId());
//                rsp.setAnalyteBaseId(a.getAnalyteBaseId());
//                rsp.setTestAnalyteDesc(LOStringUtil.delHTMLTag(a.getTestAnalyteDesc()));
//                rsp.setSelectionType(a.getSelectionType());
//                return rsp;
//            }).collect(Collectors.toList());
//        }
//        analyteBaseInfoPOS.sort((a,b)->NumberUtil.toInt(b.getSelectionType())-(NumberUtil.toInt(a.getSelectionType())));
//
//        if(chinese && CollectionUtils.isNotEmpty(analyteBaseInfoPOS)){
//            List<Long> baseIds = analyteBaseInfoPOS.stream().map(a -> a.getAnalyteBaseId()).collect(Collectors.toList());
//            AnalyteLanguageInfoExample example = new AnalyteLanguageInfoExample();
//            example.createCriteria().andLangStatusEqualTo(ActiveType.Enable.getStatus()).andAnalyteBaseIdIn(baseIds);
//            List<AnalyteLanguageInfoPO> languageInfoPOS = analyteLanguageInfoMapper.selectByExample(example);
//            for (AnalyteSelectRsp analyteBaseInfoPO : analyteBaseInfoPOS) {
//                Integer testAnalyteId = analyteBaseInfoPO.getTestAnalyteId();
//                languageInfoPOS.stream().filter(lan->LanguageType.check(lan.getLanguageId(),LanguageType.Chinese))
//                        .filter(lan->lan.getTestAnalyteId().compareTo(testAnalyteId)==0)
//                        .findFirst()
//                        .ifPresent(lan->{
//                            String testAnalyteDesc = lan.getTestAnalyteDesc();
//                            if(StringUtils.isNotBlank(testAnalyteDesc)){
//                                analyteBaseInfoPO.setTestAnalyteDesc(LOStringUtil.delHTMLTag(testAnalyteDesc));
//                            }
//                        });
//
//            }
//        }
//        return analyteBaseInfoPOS;
//    }

//    private List<LabSectionRsp> getLabSection(Integer testLineVersionID,Integer labId){
//        LabSectionReq labSectionReq = new LabSectionReq();
//        labSectionReq.setTestLineVersionId(testLineVersionID);
//        labSectionReq.setLabId(labId);
//        List<LabSectionRsp> labSectionRsp = labSectionMapper.getLabSectionListByTestLineVersionId(labSectionReq);
//        return labSectionRsp;
//    }

    private List<LabTeam> getLabTeam(Integer testLineId){
        String currentLabCode = tokenClient.getUser().getCurrentLabCode();
        TestLineLabTeamDefaultRelationshipExample example = new TestLineLabTeamDefaultRelationshipExample();
        example.createCriteria()
                .andTestLineIdEqualTo(testLineId)
                .andLabCodeEqualTo(currentLabCode)
                .andActiveIndicatorEqualTo(1).andMarkForDeleteEqualTo(1);
        List<TestLineLabTeamDefaultRelationshipPO> defaultLabTeamList = testLineLabTeamDefaultRelationshipMapper.selectByExample(example);
        List<LabTeam> labTeams = Lists.newArrayList();
        if(CollectionUtils.isNotEmpty(defaultLabTeamList)){
            defaultLabTeamList.forEach((labTeam) -> {
                LabTeam team = new LabTeam();
                BeanUtils.copyProperties(labTeam,team);
                labTeams.add(team);
            });
        }

        return labTeams;
    }
    //region private method

    /**
     *
     * @param dto
     * @param testMatrixPOS 当前TL对应的matrix，并且matrixGroupID也相等
     * @param sampleList
     * @param testConditionInfoPOS
     * @return
     */
    private MatrixTableConditionDTO handlerConditionNameWithStatus(MatrixTableDTO dto, List<TestMatrixPO> testMatrixPOS, List<TestSampleInfoPO> sampleList, List<TestConditionInfoPO> testConditionInfoPOS,boolean isChinese) {
        MatrixTableConditionDTO conditionDTO = null;

        String testLineInstanceId = dto.getTestLineInstanceId();
        List<TestConditionInfoPO> currentTLConditionList = null;
        if (dto.getMatrixGroupId() > 0) {
            //当为copy test时，by TestLine找到所有Matrix
            currentTLConditionList = testConditionInfoPOS.stream().filter(c -> testLineInstanceId.equals(c.getTestLineInstanceID())
                    && testMatrixPOS.stream().anyMatch( tm ->StringUtils.equalsIgnoreCase(tm.getID(), c.getTestMatrixID()))).collect(Collectors.toList());
        }else{
            currentTLConditionList = testConditionInfoPOS.stream().filter(c -> testLineInstanceId.equals(c.getTestLineInstanceID())).collect(Collectors.toList());
        }
        //当conditionInstance表有数据时，matrix 肯定有值
        if(CollectionUtils.isNotEmpty(currentTLConditionList)){

            Map<String, List<TestConditionInfoPO>> matrixIdConditionListMap = currentTLConditionList.stream().collect(Collectors.groupingBy(TestConditionInfoPO::getTestMatrixID));

            List<Integer> firstConditionIdList = Lists.newArrayList();
            List<String> firstMatrixId = Lists.newArrayList();
            List<String> mergeMatrixId = Lists.newArrayList();
            List<String> notMergeMatrixId = Lists.newArrayList();
            Map<String,String> midCondNameMap = Maps.newHashMap();
            matrixIdConditionListMap.forEach((mid,clist)->{
                //seq 排序
                clist.sort(Comparator.comparing(TestConditionInfoPO::getConditionTypeBlockLevel).thenComparing(TestConditionInfoPO::getTestConditionSeq));
                //List<String> maConditionName = clist.stream().map(TestConditionInfoPO::getTestConditionName).collect(Collectors.toList());
                List<String> maConditionName = clist.stream().map(m -> ConditionUtil.formatConditionNameOfList(m.getTestConditionTypeName(), m.getTestConditionDesc())).collect(Collectors.toList());
                midCondNameMap.put(mid,StringUtils.join(maConditionName,"||"));

                List<Integer> conditionIdList = clist.stream().map(TestConditionInfoPO::getTestConditionID).collect(Collectors.toList());
                if(CollectionUtils.isEmpty(firstConditionIdList)){
                    firstConditionIdList.addAll(conditionIdList);
                    firstMatrixId.add(mid);
                }else{
                    conditionIdList.removeAll(firstConditionIdList);
                    //两个matrix的 condition一致，后续需要合并
                    if(CollectionUtils.isEmpty(conditionIdList)){
                        mergeMatrixId.add(mid);
                    }else{
                        notMergeMatrixId.add(mid);
                    }
                }
            });
            //处理合并
            mergeMatrixId.addAll(firstMatrixId);
            //按照matrixId分组，然后进行name合并
            Map<String,List<String>> nameSample = Maps.newHashMap();
            List<String> listAssignSample = new ArrayList<>();
            List<String> listConditionSample = new ArrayList<>();
            for (TestSampleInfoPO sampleInfoPO : sampleList) {
                String sampleId = sampleInfoPO.getID();
                String sampleNo = sampleInfoPO.getSampleNo();
                TestMatrixPO testMatrixPO = testMatrixPOS.stream().filter(m -> StringUtils.equalsIgnoreCase(m.getTestSampleID(), sampleId)).findFirst().orElse(null);
                if (testMatrixPO == null){
                    continue;
                }
                String testMatrixPOID = testMatrixPO.getID();
                String name = midCondNameMap.get(testMatrixPOID);
                List<String> list = nameSample.get(name);
                if(list==null){
                    list = Lists.newArrayList();
                }
                list.add(sampleNo);
                listAssignSample.add(sampleNo);
                nameSample.put(name,list);
            }
            StringBuffer sb = new StringBuffer();
            nameSample.forEach((name,list)->{
                if(StringUtils.isNotBlank(name)){
                    listConditionSample.addAll(list);
                    sb.append(StringUtils.join(list,",")).append("--").append(name).append("<br/>");
                }
            });
            String conditionName = sb.toString();
            conditionDTO = new MatrixTableConditionDTO();
            if (listAssignSample.size() == listConditionSample.size()){
                conditionDTO.setConditionStatus(ConditionStatus.Confirmed.getStatus());
            }
            conditionDTO.setTestCondition(conditionName);
            return conditionDTO;
        }

        Long ppBaseId = dto.getPpBaseId();
        Long ppVersionId = dto.getPpVersionId();
        if (dto.getMatrixGroupId() > 0) {
            //根据testLineInstanceId取对应的第一个PP
            List<PPBaseInfoPO> ppBaseInfos = ppTestLineRelMapper.listPPBaseInfoByTestLineInstanceId(dto.getTestLineInstanceId());
            if (CollectionUtils.isNotEmpty(ppBaseInfos)) {
                ppVersionId = Long.valueOf(ppBaseInfos.get(0).getPpVersionId());
                ppBaseId = ppBaseInfos.get(0).getId();
            }
        }


        if( ppBaseId == null || ppVersionId == null){
            //pp为空，则显示TL的默认condition

            return buildDefaultConditionOfTestLine(dto.getTestLineVersionId());
        }
        Long aId = dto.getAId();
        // 从LocalTrims查询PP对应的Condition信息作为默认值
        List<TlConditionDTO> tlConditionDTOS = Lists.newArrayList();
        GetConditionListReq getConditionListReq = new GetConditionListReq();
        List<GetConditionListItemReq> conditionListItems = Lists.newArrayList();
        GetConditionListItemReq getConditionListItemReq = new GetConditionListItemReq();
        getConditionListItemReq.setTestLineVersionId(dto.getTestLineVersionId());
        getConditionListItemReq.setArtifactId(aId);
        conditionListItems.add(getConditionListItemReq);
        if(Func.isNotEmpty(conditionListItems)) {
            getConditionListReq.setConditionListItems(conditionListItems);
            getConditionListReq.setLanguageType(LanguageType.Chinese.getLanguageId());
            getConditionListReq.setCallerBU(ProductLineContextHolder.getProductLineCode());
            tlConditionDTOS = getPpConditionFromTrims(getConditionListReq,isChinese);
        }
        //从baseInfo表查询有没有condition，用来展示
//        Map<String,Object> param = new HashMap<>();
//        param.put("ppVersionId",ppVersionId);
//        param.put("testLineVersionId",dto.getTestLineVersionId());
//        List<TlConditionDTO> tlConditionDTOS = testLineMapper.getTestConditionName(param);
        if(CollectionUtils.isNotEmpty(tlConditionDTOS)){
            tlConditionDTOS.sort(Comparator.comparing(TlConditionDTO::getTestConditionId));
            //List<String> list = tlConditionDTOS.stream().map(TlConditionDTO::getTestConditionName).collect(Collectors.toList());
            List<String> list = tlConditionDTOS.stream().map(m -> ConditionUtil.formatConditionNameOfList(m.getTestConditionTypeName(), m.getTestConditionDesc())).collect(Collectors.toList());
            String conditionName = StringUtils.join(list,"||");
            conditionDTO = new MatrixTableConditionDTO();
            conditionDTO.setConditionStatus(ConditionStatus.UnConfirmed.getStatus());
            conditionDTO.setTestCondition(conditionName);
        }
        return conditionDTO;
    }
    /**
     * 构造TL的默认condition
     * @param testLineVersionId
     * @return
     */
    private MatrixTableConditionDTO buildDefaultConditionOfTestLine(Integer testLineVersionId) {
        List<TestLineConditionRelDTO> testLineConditionRelDTOS = testLineConditionRelExtMapper.getDefaultTestLineConditionRels(testLineVersionId);
        if (CollectionUtils.isEmpty(testLineConditionRelDTOS)){
            return null;
        }

        MatrixTableConditionDTO conditionDTO = new MatrixTableConditionDTO();
        testLineConditionRelDTOS.sort(Comparator.comparing(TestLineConditionRelDTO::getTestConditionID));
        //List<String> list = testLineConditionRelDTOS.stream().map(TestLineConditionRelDTO::getTestConditionName).collect(Collectors.toList());
        List<String> list = testLineConditionRelDTOS.stream().map(m -> ConditionUtil.formatConditionNameOfList(m.getTestConditionTypeName(),m.getTestConditionDesc())).collect(Collectors.toList());
        String conditionName = StringUtils.join(list,"||");
        conditionDTO.setConditionStatus(ConditionStatus.UnConfirmed.getStatus());
        conditionDTO.setTestCondition(conditionName);
        return conditionDTO;
    }

    /**
     * 获取source订的refer数据
     * @param orderNo 订单号
     * @return matrix and tl
     */
    private List<TestMatrixWithTestLineRsp> getReferSourceMatrixes(String orderNo) {
        List<TestMatrixWithTestLineRsp> referSourceMatrixs = Lists.newArrayList();

        ReferDataRelationshipExample referDataRelationshipExample = new ReferDataRelationshipExample();
        referDataRelationshipExample.createCriteria().andCurrentOrderNoEqualTo(orderNo);
        List<ReferDataRelationshipPO> referDataRelationshipPOS = referDataRelationshipMapper.selectByExample(referDataRelationshipExample);
        if (CollectionUtils.isNotEmpty(referDataRelationshipPOS)){
            List<String> sourceOrderNos = referDataRelationshipPOS.stream().map(ReferDataRelationshipPO::getSourceOrderNo).collect(Collectors.toList());
            List<String> sourceSampleIds = referDataRelationshipPOS.stream().map(ReferDataRelationshipPO::getSourceSampleId).distinct().collect(Collectors.toList());
            referSourceMatrixs = testMatrixMapper.getReferMatrixByOrderAndSamples(sourceOrderNos, sourceSampleIds);
        }

        return referSourceMatrixs;
    }

    /**
     * 根据report语言判断是否是中文
     * @param orderNo
     * @return
     */
    private boolean isReportChinese(String orderNo){
        ReportLangReq reqReport = new ReportLangReq();
        reqReport.setOrderNo(orderNo);
        List<LanguageType> languages = orderReportClient.getReportLanguageByOrderNo(reqReport);
        return languages.contains(LanguageType.Chinese);
    }

    /**
     * 根据主语言语言判断是否是中文
     */
    private boolean isChinese(String orderNo,String defaultLanguageCode) {
        //升级preorder的dubbo jar 到36版本 可用以下属性
        return UserInfoDefaultLanguageCodeEnums.check(defaultLanguageCode,UserInfoDefaultLanguageCodeEnums.zh_cn);
    }

    /**
     * 根据主语言语言判断是否是中文
     */
    private boolean isChinese(String orderNo) {
        //升级preorder的dubbo jar 到36版本 可用以下属性
//        String defaultLanguageCode = tokenClient.getUser().getDefaultLanguageCode();
        String defaultLanguageCode = frameWorkClient.getPrimaryLanguageCode(ProductLineContextHolder.getProductLineCode());
        return UserInfoDefaultLanguageCodeEnums.check(defaultLanguageCode,UserInfoDefaultLanguageCodeEnums.zh_cn);
    }

    private List<TestLineLanguageInfoPO> getTestLineLanguageInfoPOS(List<Long> testLineBaseIds) {
        TestLineLanguageDTO objTestLineLanguageDTO = new TestLineLanguageDTO();
        objTestLineLanguageDTO.setTestLineBaseIds(testLineBaseIds);
        return testLineBaseMapper.getTestlineLanguage(objTestLineLanguageDTO);
    }

    private List<OrderLanguageRelInfoPO> getOrderLanguageRelInfoPOS(String orderId) {
        //search orderLanguageRelInfoMapper
        OrderLanguageRelInfoExample objOrderLanguageRelInfoExample=new OrderLanguageRelInfoExample();
        objOrderLanguageRelInfoExample.createCriteria().andOrderIdEqualTo(orderId);
        List<OrderLanguageRelInfoPO> orderLanguageRelInfoPOS=orderLanguageRelInfoMapper.selectByExample(objOrderLanguageRelInfoExample);
        if(CollectionUtils.isEmpty(orderLanguageRelInfoPOS)) {
            orderLanguageRelInfoPOS = Lists.newArrayList();
        }
        return orderLanguageRelInfoPOS;
    }

    /**
     * 构造TeamName
     * @param matrixTableDTOS
     */
    private void buildLabTeamName(String labCode, List<MatrixTableDTO> matrixTableDTOS) {
        if (CollectionUtils.isEmpty(matrixTableDTOS)){
            return;
        }

        String productLineCode = tokenClient.getUserProductLineCodeByLab(labCode);
        if (StringUtils.equalsIgnoreCase(productLineCode, ProductLineType.SL.getProductLineAbbr())){
            return;
        }

        List<String> labTeamCodes = matrixTableDTOS.stream().filter(p -> StringUtils.isNotBlank(p.getLabTeamCode())).map(MatrixTableDTO::getLabTeamCode).distinct()
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(labTeamCodes)) {
            return;
        }

        //call CS
        QueryLabTeamInfoReq queryLabTeamInfoReq = new QueryLabTeamInfoReq();
        queryLabTeamInfoReq.setLabCode(labCode);
        queryLabTeamInfoReq.setLabTeamCodeList(labTeamCodes);
        QueryLabTeamInfoRsp queryLabTeamInfoRsp = userManagementClient.queryLabTeamInfo(queryLabTeamInfoReq);
        if (queryLabTeamInfoRsp == null){
            return;
        }
        if (CollectionUtils.isEmpty(queryLabTeamInfoRsp.getTeamList())){
            return;
        }

        for (MatrixTableDTO matrixTableDTO : matrixTableDTOS) {
            if (StringUtils.isBlank(matrixTableDTO.getLabTeamCode())){
                continue;
            }

            queryLabTeamInfoRsp.getTeamList().stream().filter(p -> StringUtils.equalsIgnoreCase(p.getLabTeamCode(), matrixTableDTO.getLabTeamCode()))
                    .findFirst().ifPresent(c -> {
                matrixTableDTO.setLabTeamName(c.getLabTeamName());
            });

        }
    }
    /**
     * 查询testLine对应的labSection
     *
     * @param labSectionListReq
     * @return
     */
    public BaseResponse<List<TestLineEditDetailRsp>> getLabSectionList(LabSectionListReq labSectionListReq){
        BaseResponse<List<TestLineEditDetailRsp>> response = new BaseResponse<>();
        UserInfo userInfo = tokenClient.getUser();
        String token = tokenClient.getToken();
        token = Func.isEmpty(token)?labSectionListReq.getSgsToken():token;
        UserLabBuInfo userLabInfo = userClient.getUserLabBuInfo(token);
        if(Func.isEmpty(userInfo)){
            response.setMessage("token 已经失效");
            response.setStatus(ResponseCode.INTERNAL_SERVER_ERROR.getCode());
            return response;
        }
        // 获取testLineInstanceId
        if(Func.isEmpty(labSectionListReq)||Func.isEmpty(labSectionListReq.getTestLineInstanceIds())){
            return response;
        }
        List<LabSectionItemReq> labSectionItemReqList = labSectionListReq.getTestLineInstanceIds();
        List<String> testLineInstanceIds =labSectionListReq.getTestLineInstanceIds().stream().map(LabSectionItemReq::getTestLineInstanceId).collect(Collectors.toList());
        List<Integer> aids =labSectionListReq.getTestLineInstanceIds().stream().map(LabSectionItemReq::getAid).filter(item->!NumberUtil.equals(item,0)).collect(Collectors.toList());
        List<TestLineInstancePO> testLineInstanceList = Lists.newArrayList();
        // 是否需要过滤
        if(labSectionListReq.isExempt()){
            TestLineInstancePO testLineInstancePO = testLineInstanceMapper.selectByPrimaryKey(labSectionListReq.getTestLineInstanceIds().get(0).getTestLineInstanceId());
            testLineInstanceList.add(testLineInstancePO);
        }else {
            // 校验TL是否关联了Job/SubContract，已关联的需要过滤掉
            // 查询订单下没有关联Job/SubContract 的test line
            testLineInstanceList = testLineInstanceExtMapper.getIndependentTestLine(testLineInstanceIds,labSectionListReq.getOrderNo());
        }
        if(Func.isEmpty(testLineInstanceList)){
            return response;
        }
        List<TestLineEditDetailRsp> testLineDetailRsp = Lists.newArrayList();
        // 查询testLine对应的具体信息
        OrderTestLineListSearchReq testLineSearchReq = new OrderTestLineListSearchReq();
        testLineSearchReq.setOrderNo(labSectionListReq.getOrderNo());
        List<OrderTestLineListDTO> testLineInfos =  orderTestLineService.getOrderTestLineInfoList(testLineSearchReq);
        Map<String, OrderTestLineListDTO> testLineInfoMap = Maps.newHashMap();
        if(Func.isNotEmpty(testLineInfos)){
            testLineInfoMap = testLineInfos.stream().collect(Collectors.toMap(OrderTestLineListDTO::getTestLineInstanceId, a->a,(k1, k2)->k1));
        }
        Map<String, OrderTestLineListDTO> finalTestLineInfoMap = testLineInfoMap;
        // 查询testLine对应的LabSection
        Set<String> testLineInstanceIdList = testLineInstanceList.stream().map(TestLineInstancePO::getID).collect(Collectors.toSet());
        List<TestLineLabSectionDTO> testLineLabSectionDTOS = testLineInstanceExtMapper.getTestLineLabSection(testLineInstanceIdList);
        testLineInstanceList.stream().forEach(testLine->{
            TestLineEditDetailRsp testLineEditDetailRsp = new TestLineEditDetailRsp();
            testLineEditDetailRsp.setTestLineInstanceId(testLine.getID());
            testLineEditDetailRsp.setTestLineVersionID(testLine.getTestLineVersionID());
            testLineEditDetailRsp.setTestItemName(finalTestLineInfoMap.get(testLine.getID()).getTestItem());
            testLineEditDetailRsp.setTestLineId(testLine.getTestLineID());
            testLineEditDetailRsp.setTestLineVersionID(testLine.getTestLineVersionID());
            testLineEditDetailRsp.setTestLineStatus(testLine.getTestLineStatus());
            if(Func.isNotEmpty(testLineLabSectionDTOS)){
                List<TestLineLabSectionDTO> testlineLabSectionList = testLineLabSectionDTOS.stream().filter(testLineLabSectionDTO -> {
                    return testLineLabSectionDTO.getTestLineInstanceId().equals(testLine.getID());
                }).collect(Collectors.toList());
                if(Func.isNotEmpty(testlineLabSectionList)) {
                    testLineEditDetailRsp.setLabSectionSelectedList(testlineLabSectionList.stream().map(TestLineLabSectionDTO::getLabSectionId).collect(Collectors.toSet()));
                }
            }
            testLineDetailRsp.add(testLineEditDetailRsp);
        });
        // 根据testLineVersionID从Trim系统查询LabSection
        List<Integer> testLineVersionIds = testLineInstanceList.stream().map(TestLineInstancePO::getTestLineVersionID).distinct().collect(Collectors.toList());
        if(Func.isEmpty(testLineVersionIds)){
            return response;
        }
        OrderIdReq orderIdReq = new OrderIdReq();
        orderIdReq.setOrderNo(labSectionListReq.getOrderNo());
        orderIdReq.setLabCode(LabCodeContextHolder.getLabCode());
        BaseResponse<OrderAllDTO> orderForPe = orderFacade.getOrderForPe(orderIdReq);
        Integer labId = Func.toInteger(userLabInfo.getLabId());
        String getLabSectionLabCode  = orderClient.getOrderTopsToLabCode(labSectionListReq.getOrderNo());
        if(Func.isEmpty(getLabSectionLabCode) && Func.isNotEmpty(orderForPe) && Func.isNotEmpty(orderForPe.getData()) && Func.isNotEmpty(orderForPe.getData().getLabDTO())){
            getLabSectionLabCode =orderForPe.getData().getLabDTO().getLabCode();
        }
        if(Func.isNotEmpty(getLabSectionLabCode)){
            LabInfo labInfoByLabCode = frameWorkClient.getLabInfoByLabCode(getLabSectionLabCode);
            if(Func.isNotEmpty(labInfoByLabCode)){
                labId = labInfoByLabCode.getLaboratoryID();
            }
        }
        // 根据tl查询LabSectionList
        GetLabSectionListByTestLineVersionIdsReq labSectionRequest = new GetLabSectionListByTestLineVersionIdsReq();
        labSectionRequest.setAids(aids);
        labSectionRequest.setTestLineVersionIds(testLineVersionIds);
        labSectionRequest.setLabId(labId);
        log.info("labSectionFacade---->getLabSectionListByTestLineVersionIds  request: {}", JSON.toJSONString(labSectionRequest));
        com.sgs.trimslocal.facade.model.common.BaseResponse<List<GetLabSectionBaseInfoRsp>> trimsRes =  labSectionFacade.getLabSectionListByTestLineVersionIds(labSectionRequest);
        log.info("labSectionFacade---->getLabSectionListByTestLineVersionIds  response: {}", JSON.toJSONString(trimsRes));
        // 根据LabId查询LabSectionList
        LabSectionByLabIdReq labSectionByLabIdReq = new LabSectionByLabIdReq();
        Set<Integer> labIds = Sets.newHashSet();
        labIds.add(labId);
        labSectionByLabIdReq.setLabIds(labIds);
        log.info("labSectionFacade---->getLabSectionByLabId  request: {}", JSON.toJSONString(labSectionByLabIdReq));
        com.sgs.trimslocal.facade.model.common.BaseResponse<List<GetLabSectionBaseInfoRsp>> trimsLabRes =  labSectionFacade.getLabSectionByLabId(labSectionByLabIdReq);
        log.info("labSectionFacade---->getLabSectionByLabId  response: {}", JSON.toJSONString(trimsLabRes));
        List<LabSectionRsp> defaultLabSectionList = Lists.newArrayList();
        if(Func.isNotEmpty(trimsLabRes)&&Func.isNotEmpty(trimsLabRes.getData())){
            List<GetLabSectionBaseInfoRsp> trimsLabSection = trimsLabRes.getData();
            defaultLabSectionList = convertToLabSectionList(trimsLabSection);
        }
        // tl优先匹配根据tl查询出的labSectionList，如果没有匹配到返回根据Lab查询出的labSectionList
        List<GetLabSectionBaseInfoRsp> trimsLabSection = trimsRes.getData();
        List<LabSectionRsp> finalDefaultLabSectionList = defaultLabSectionList;
        testLineDetailRsp.stream().forEach(testLine ->{
            List<GetLabSectionBaseInfoRsp> trimsLabSectionList = new ArrayList();
            if(CollectionUtils.isNotEmpty(trimsLabSection)){
                //非PP
                LabSectionItemReq labSectionItemReq = labSectionItemReqList.stream().filter(item -> Func.equalsSafe(item.getTestLineInstanceId(), testLine.getTestLineInstanceId())).findAny().orElse(null);
                Integer testLineAid = labSectionItemReq.getAid();
                if(Func.isEmpty(testLineAid) || NumberUtil.equals(testLineAid,0)){
                    trimsLabSectionList = trimsLabSection.stream().filter(item -> NumberUtil.equals(item.getTestLineVersionId(), testLine.getTestLineVersionID())).collect(Collectors.toList());
                }else{
                    //PP:优先匹配PP，没有PP，匹配TL
                    trimsLabSectionList = trimsLabSection.stream().filter(item -> NumberUtil.equals(testLineAid, item.getAid())).collect(Collectors.toList());
                    if(Func.isEmpty(trimsLabSectionList)){
                        trimsLabSectionList = trimsLabSection.stream().filter(item -> NumberUtil.equals(item.getTestLineVersionId(), testLine.getTestLineVersionID())).collect(Collectors.toList());
                    }
                }
            }
            if(Func.isNotEmpty(trimsLabSectionList) && !TestLineType.check(testLine.getTestLineType(),TestLineType.VIRTAUL_TL)
            ){
                testLine.setLabSectionList(convertToLabSectionList(trimsLabSectionList));
            }else {
                testLine.setLabSectionList(finalDefaultLabSectionList);
            }
        });
        response.setData(testLineDetailRsp);
        return response;
    }

    private List<LabSectionRsp> convertToLabSectionList(List<GetLabSectionBaseInfoRsp> labSectionList){
        List<LabSectionRsp> newLabSectionList = Lists.newArrayList();
        if(Func.isEmpty(labSectionList)){
            return newLabSectionList;
        }
        labSectionList.stream().forEach(labSection ->{
            LabSectionRsp labSectionRsp = new LabSectionRsp();
            labSectionRsp.setLabSectionId(labSection.getLabSectionId());
            labSectionRsp.setLabSectionCode(labSection.getLabSectionCode());
            labSectionRsp.setLabSectionBaseId(labSection.getLabSectionBaseId());
            labSectionRsp.setLabSectionName(labSection.getLabSectionName());
            newLabSectionList.add(labSectionRsp);
        });
        return newLabSectionList.stream().sorted(Comparator.comparing(LabSectionRsp::getLabSectionName)).collect(Collectors.toList());
    }

    public BaseResponse<List<TestLineLabSectionRsp>> queryAssignLabSectionTLList(QueryTestLineLabSectionReq queryTestLineLabSectionReq){
        if(Func.isEmpty(queryTestLineLabSectionReq) || Func.isEmpty(queryTestLineLabSectionReq.getOrderNo())){
            return BaseResponse.newFailInstance(ResponseCode.PARAM_VALID_ERROR);
        }
        OrderIdReq orderIdReq = new OrderIdReq();
        orderIdReq.setOrderNo(queryTestLineLabSectionReq.getOrderNo());
        orderIdReq.setLabCode(LabCodeContextHolder.getLabCode());
        BaseResponse<OrderAllDTO> orderAllDTOBaseResponse = orderFacade.getOrderForPe(orderIdReq);
        if(Func.isEmpty(orderAllDTOBaseResponse) || Func.isEmpty(orderAllDTOBaseResponse.getData()) || orderAllDTOBaseResponse.isFail()){
            return BaseResponse.newFailInstance("Not Find Order");
        }
        String defaultLanguageCode = frameWorkClient.getPrimaryLanguageCode(ProductLineContextHolder.getProductLineCode());
        boolean chineseFlag = UserInfoDefaultLanguageCodeEnums.check(defaultLanguageCode, UserInfoDefaultLanguageCodeEnums.zh_cn);

        List<TestLineLabSectionRsp> testLineLabSectionRspList = new ArrayList<>();
        OrderAllDTO orderAllDTO = orderAllDTOBaseResponse.getData();
        if(queryTestLineLabSectionReq.isConfirmOrderFlag()){
            OrderIdRequest orderIdRequest = new OrderIdRequest();
            orderIdRequest.setOrderId(orderAllDTO.getOrderId());
            orderIdRequest.setProductLineCode(ProductLineContextHolder.getProductLineCode());
            orderIdRequest.setSgsToken(tokenClient.getToken());
            orderIdRequest.setSystemId(SgsSystem.GPO.getSgsSystemId());
            BaseResponse<List<TotestTestlineInstanceDTO>> toTestTestLineRes = quotationFacade.queryTestLineMatrixByOrderId(orderIdRequest);
            if(Func.isNotEmpty(toTestTestLineRes) && toTestTestLineRes.isSuccess()){
                List<TotestTestlineInstanceDTO> toTestTestLineInstanceDTOS = toTestTestLineRes.getData();
                if(CollectionUtils.isNotEmpty(toTestTestLineInstanceDTOS)){
                    toTestTestLineInstanceDTOS = toTestTestLineInstanceDTOS.stream().filter(item->StringUtils.isEmpty(item.getPpTlRelId())).collect(Collectors.toList());
                    for (TotestTestlineInstanceDTO totestTestlineInstanceDTO : toTestTestLineInstanceDTOS) {
                        TestLineLabSectionRsp testLineLabSectionRsp = new TestLineLabSectionRsp();
                        testLineLabSectionRsp.setPpNo(Func.toInteger(totestTestlineInstanceDTO.getPpNo()));
                        testLineLabSectionRsp.setAid(totestTestlineInstanceDTO.getAid());
                        testLineLabSectionRsp.setTestLineId(Func.toInteger(totestTestlineInstanceDTO.getTestLineId()));
                        testLineLabSectionRsp.setTestLineVersionId(totestTestlineInstanceDTO.getTestLineVersionId());
                        testLineLabSectionRsp.setTestItemName(totestTestlineInstanceDTO.getChargeName());
                        if(chineseFlag && Func.isNotEmpty(totestTestlineInstanceDTO.getChargeNameCn())){
                            testLineLabSectionRsp.setTestItemName(totestTestlineInstanceDTO.getChargeNameCn());
                        }
                        testLineLabSectionRspList.add(testLineLabSectionRsp);
                    }
                }
            }
        }
        String labCode = "";
        if(Func.isNotEmpty(queryTestLineLabSectionReq.getNewTopsToLab())){
            labCode = queryTestLineLabSectionReq.getNewTopsToLab();
        }else if(Func.isNotEmpty(orderAllDTO.getLabDTO())){
            labCode = orderAllDTO.getLabDTO().getLabCode();
        }
        if(Func.isEmpty(labCode)){
            return BaseResponse.newFailInstance("Get LabCode Fail!");
        }
        //tops是否有变化，有变化返回要更新的TL
        if(!Func.equalsSafe(Func.toStr(queryTestLineLabSectionReq.getOldTopsToLab()),Func.toStr(queryTestLineLabSectionReq.getNewTopsToLab()))){
            OrderTestLineListSearchReq orderTestLineListSearchReq = new OrderTestLineListSearchReq();
            orderTestLineListSearchReq.setOrderNo(queryTestLineLabSectionReq.getOrderNo());
            List<OrderTestLineListDTO> orderTestLineInfoList = orderTestLineService.getOrderTestLineInfoList(orderTestLineListSearchReq);
            if(Func.isNotEmpty(orderTestLineInfoList)){
                orderTestLineInfoList = orderTestLineInfoList.stream().filter(item-> !com.sgs.framework.model.enums.TestLineStatus.checkCategory(item.getTestLineStatus(),Constants.TEST_LINE.STATUS_CATEGORY.INVALID)).collect(Collectors.toList());
                for (OrderTestLineListDTO orderTestLineListDTO : orderTestLineInfoList) {
                    TestLineLabSectionRsp testLineLabSectionRsp = new TestLineLabSectionRsp();
                    testLineLabSectionRsp.setAid(Func.toInteger(orderTestLineListDTO.getPpTlRelAId()));
                    testLineLabSectionRsp.setPpNo(orderTestLineListDTO.getPpNo());
                    testLineLabSectionRsp.setTestLineId(Func.toInt(orderTestLineListDTO.getTestLineId()));
                    testLineLabSectionRsp.setTestLineVersionId(orderTestLineListDTO.getTestLineVersionId());
                    testLineLabSectionRsp.setTestItemName(orderTestLineListDTO.getTestItem());
                    testLineLabSectionRspList.add(testLineLabSectionRsp);
                }
            }
        }
        LabInfo labInfo = frameWorkClient.getLabInfoByLabCode(labCode);
        if(Func.isEmpty(labInfo)){
            return BaseResponse.newFailInstance("Get LabInfo Fail!");
        }
        int labId = labInfo.getLaboratoryID();

        if(Func.isNotEmpty(testLineLabSectionRspList)){
            //查询LabSection
            List<Integer> aids = testLineLabSectionRspList.stream().map(TestLineLabSectionRsp::getAid).distinct().filter(ObjectUtil::isNotEmpty).collect(Collectors.toList());
            List<Integer> testLineVersionIds = testLineLabSectionRspList.stream().map(TestLineLabSectionRsp::getTestLineVersionId).distinct().filter(ObjectUtil::isNotEmpty).collect(Collectors.toList());
            GetLabSectionListByTestLineVersionIdsReq getLabSectionListByTestLineVersionIdsReq = new GetLabSectionListByTestLineVersionIdsReq();
            getLabSectionListByTestLineVersionIdsReq.setAids(aids);
            getLabSectionListByTestLineVersionIdsReq.setTestLineVersionIds(testLineVersionIds);
            getLabSectionListByTestLineVersionIdsReq.setLabId(labId);
            List<GetLabSectionBaseInfoRsp> labSectionBaseInfoRspList = labSectionFacade.getLabSectionListByTestLineVersionIds(getLabSectionListByTestLineVersionIdsReq).getData();
            if(CollectionUtils.isNotEmpty(labSectionBaseInfoRspList)){
                for (TestLineLabSectionRsp testLineLabSectionRsp : testLineLabSectionRspList) {
                    //非PP
                    List<GetLabSectionBaseInfoRsp> labSectionBaseInfoRsps = null;
                    if(Func.isEmpty(testLineLabSectionRsp.getAid()) || NumberUtil.equals(testLineLabSectionRsp.getAid(),0)){
                        labSectionBaseInfoRsps = labSectionBaseInfoRspList.stream().filter(item -> NumberUtil.equals(item.getTestLineVersionId(), testLineLabSectionRsp.getTestLineVersionId())).collect(Collectors.toList());
                    }else{
                        //PP:优先匹配PP，没有PP，匹配TL
                        labSectionBaseInfoRsps = labSectionBaseInfoRspList.stream().filter(item -> NumberUtil.equals(testLineLabSectionRsp.getAid(), item.getAid())).collect(Collectors.toList());
                        if(Func.isEmpty(labSectionBaseInfoRsps)){
                            labSectionBaseInfoRsps = labSectionBaseInfoRspList.stream().filter(item -> NumberUtil.equals(item.getTestLineVersionId(), testLineLabSectionRsp.getTestLineVersionId())).collect(Collectors.toList());
                        }
                    }
                    if(CollectionUtils.isNotEmpty(labSectionBaseInfoRsps)){
                        List<String> labSectionNames = labSectionBaseInfoRsps.stream().map(GetLabSectionBaseInfoRsp::getLabSectionName).distinct().collect(Collectors.toList());
                        testLineLabSectionRsp.setLabSectionNameList(labSectionNames);
                    }
                }
            }
        }
        testLineLabSectionRspList = testLineLabSectionRspList.stream().filter(item->Func.isNotEmpty(item.getLabSectionNameList()) && item.getLabSectionNameList().size()>1).collect(Collectors.toList());
        return BaseResponse.newSuccessInstance(testLineLabSectionRspList);
    }
}
