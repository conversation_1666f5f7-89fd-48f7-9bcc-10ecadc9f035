package com.sgs.otsnotes.domain.service.subcontract.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.beust.jcommander.internal.Sets;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sgs.core.domain.UserInfo;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.core.base.CustomResult;
import com.sgs.framework.core.base.ResponseCode;
import com.sgs.framework.kafka.client.MessageReq;
import com.sgs.framework.log.SystemLogHelper;
import com.sgs.framework.log.model.SystemLog;
import com.sgs.framework.model.enums.LanguageType;
import com.sgs.framework.model.enums.SgsSystem;
import com.sgs.framework.model.enums.SubReportStatusEnum;
import com.sgs.framework.model.enums.TestLineType;
import com.sgs.framework.security.context.SecurityContextHolder;
import com.sgs.framework.tool.utils.Func;
import com.sgs.framework.tool.utils.ProductLineContextHolder;
import com.sgs.grus.bizlog.BizLogClient;
import com.sgs.grus.bizlog.common.BizLogHelper;
import com.sgs.grus.bizlog.info.BizLogInfo;
import com.sgs.otsnotes.core.common.UserHelper;
import com.sgs.otsnotes.core.constants.BizLogConstant;
import com.sgs.otsnotes.core.constants.Constants;
import com.sgs.otsnotes.core.enums.ReportFlagEnums;
import com.sgs.otsnotes.core.kafka.Producer;
import com.sgs.otsnotes.core.util.*;
import com.sgs.otsnotes.dbstorages.mybatis.extmapper.*;
import com.sgs.otsnotes.dbstorages.mybatis.mapper.*;
import com.sgs.otsnotes.dbstorages.mybatis.model.*;
import com.sgs.otsnotes.domain.service.JobService;
import com.sgs.otsnotes.domain.service.OrderSubcontractRelService;
import com.sgs.otsnotes.domain.service.SubContractService;
import com.sgs.otsnotes.domain.service.TestLineService;
import com.sgs.otsnotes.domain.service.cache.ConclusionListService;
import com.sgs.otsnotes.domain.service.gpn.order.IOrderService;
import com.sgs.otsnotes.domain.service.gpn.report.IGpnReportService;
import com.sgs.otsnotes.domain.service.gpn.report.IGpnSubReportService;
import com.sgs.otsnotes.domain.service.gpn.sendEmail.ISendEmailService;
import com.sgs.otsnotes.domain.service.gpn.status.IGpnStatusService;
import com.sgs.otsnotes.domain.service.gpn.subcontract.ISubContractExternalRelService;
import com.sgs.otsnotes.domain.service.gpn.subcontract.ISubcontractRequirementService;
import com.sgs.otsnotes.domain.service.gpn.tracking.ITrackingService;
import com.sgs.otsnotes.domain.service.productlineservice.ProductLineServiceHolder;
import com.sgs.otsnotes.domain.service.subcontract.ISubcontractService;
import com.sgs.otsnotes.domain.service.subcontract.ISubcontractStatusBizService;
import com.sgs.otsnotes.domain.service.testline.ITestLineStatusService;
import com.sgs.otsnotes.facade.model.dto.EmailAutoSendDTO;
import com.sgs.otsnotes.facade.model.dto.GeneralOrderInstanceDTO;
import com.sgs.otsnotes.facade.model.dto.PPTestLineJobDto;
import com.sgs.otsnotes.facade.model.dto.ReportDTO;
import com.sgs.otsnotes.facade.model.dto.report.ReportSubReportDTO;
import com.sgs.otsnotes.facade.model.enums.KafkaActionType;
import com.sgs.otsnotes.facade.model.enums.ReportStatus;
import com.sgs.otsnotes.facade.model.enums.SystemLogType;
import com.sgs.otsnotes.facade.model.enums.*;
import com.sgs.otsnotes.facade.model.gpn.subreport.req.ReviseSubReportReq;
import com.sgs.otsnotes.facade.model.info.subcontract.SubContractInfo;
import com.sgs.otsnotes.facade.model.info.subcontract.SubcontractRequirementContactsInfo;
import com.sgs.otsnotes.facade.model.info.subcontract.SubcontractRequirementInfo;
import com.sgs.otsnotes.facade.model.kafka.KafkaTopicConsts;
import com.sgs.otsnotes.facade.model.kafka.ToDoListMessage;
import com.sgs.otsnotes.facade.model.req.SubContractReq;
import com.sgs.otsnotes.facade.model.req.gpn.GpnQuerySubContractReq;
import com.sgs.otsnotes.facade.model.req.gpn.ReportNoReq;
import com.sgs.otsnotes.facade.model.req.status.SaveGpnStatusReq;
import com.sgs.otsnotes.facade.model.req.subcontract.GenerateReportForSubcontractReq;
import com.sgs.otsnotes.facade.model.req.testLine.DelTestLineReq;
import com.sgs.otsnotes.facade.model.req.testLine.TestLineStatusUpdateReq;
import com.sgs.otsnotes.facade.model.rsp.conclusion.SubReportListRsp;
import com.sgs.otsnotes.facade.model.subcontract.req.*;
import com.sgs.otsnotes.facade.model.subcontract.res.SubContractCompleteRes;
import com.sgs.otsnotes.integration.FrameWorkClient;
import com.sgs.otsnotes.integration.OrderClient;
import com.sgs.otsnotes.integration.OrderPersonClient;
import com.sgs.otsnotes.integration.TokenClient;
import com.sgs.preorder.facade.*;
import com.sgs.preorder.facade.model.annotation.ObjectSetting;
import com.sgs.preorder.facade.model.dto.busetting.BuObjectTemplateDTO;
import com.sgs.preorder.facade.model.dto.order.LabDTO;
import com.sgs.preorder.facade.model.dto.order.OrderAllDTO;
import com.sgs.preorder.facade.model.dto.order.OrderInfoDto;
import com.sgs.preorder.facade.model.enums.OrderStatus;
import com.sgs.preorder.facade.model.enums.*;
import com.sgs.preorder.facade.model.info.expectduedate.ExpectDueDateInfo;
import com.sgs.preorder.facade.model.req.*;
import com.sgs.preorder.facade.model.req.operationHistory.InsertOperationHistoryReq;
import com.sgs.preorder.facade.model.req.order.OrderPersonInfoReq;
import com.sgs.preorder.facade.model.rsp.order.OrderPersonInfoRsp;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static com.sgs.otsnotes.facade.model.enums.TestLineStatus.Typing;

/**
 * 分包单对象相关的Service
 */
@Service
@Slf4j
@AllArgsConstructor
public class SubcontractServiceImpl implements ISubcontractService {

    private SubContractMapper subContractMapper;
    private OrderMapper orderMapper;
    private TestLineMapper testLineMapper;
    private TestLineInstanceMapper testLineInstanceMapper;
    private SubContractExtMapper subContractExtMapper;
    private SubContractTestLineMappingMapper subContractTestLineMappingMapper;
    private JobExtMapper jobExtMapper;
    private TestLineInstanceExtMapper testLineInstanceExtMapper;
    private GeneralOrderInstanceMapper generalOrderInstanceMapper;
    private GeneralOrderInstanceInfoMapper generalOrderInstanceInfoMapper;
    private TestMatrixMapper testMatrixMapper;
    private SubReportExtMapper subReportExtMapper;
    private ReportMatrixRelMapper reportMatrixRelMapper;
    private ReportMapper reportMapper;
    private ReportFileMapper reportFileMapper;
    private ReportSubReportRelationshipInfoMapper reportSubReportRelationshipInfoMapper;
    private SubcontractRequirementMapper subcontractRequirementMapper;
    private ReportInfoMapper reportInfoMapper;
    private ConclusionMapper conclusionMapper;
    private TestSampleMapper sampleMapper;
    private SubReportMapper subReportMapper;
    private PPTestLineRelationshipInfoMapper ppTestLineRelationshipInfoMapper;


    private ISubcontractStatusBizService subcontractStatusBizService;
    private ISubContractExternalRelService subContractExternalRelService;
    private ISubcontractRequirementService subcontractRequirementService;
    private ITrackingService trackingService;
    private ISendEmailService sendEmailService;
    private TestLineService testLineService;
    private SubContractService subContractService;
    private ConclusionListService conclusionListService;
    private IGpnReportService reportService;
    private IGpnSubReportService subReportService;
    private OrderSubcontractRelService orderSubcontractRelService;
    private JobService jobService;
    private TokenClient tokenClient;
    private OrderClient orderClient;
    private FrameWorkClient frameWorkClient;
    private BizLogClient bizLogClient;
    private Producer producer;
    private TransactionTemplate transactionTemplate;
    private OrderFacade orderFacade;
    private ToDoListFacade toDoListFacade;
    private ExpectDueDateFacade expectDueDateFacade;
    private StatusFacade gpoStatusFacade;
    private OperationHistoryFacade operationHistoryFacade;
    private BuParamFacade buParamFacade;
    private ITestLineStatusService testLineStatusServiceNew;
    private IGpnStatusService gpnStatusService;
    private OrderPersonClient orderPersonClient;
    private SystemLogHelper systemLogHelper;


    private static final String SystemName = "System";


    /**
     * 分包单 新增/编辑
     *
     * @return
     */
    @Override
    public CustomResult save(SaveSubContractReq req) {
        CustomResult result = new CustomResult();

        UserInfo user = SecurityContextHolder.getUserInfo();
        if (Func.isEmpty(user)) {
            result.fail(ResponseCode.TokenExpire.getMessage());
            return result;
        }

        //校验Save参数
        List<String> needDelTestLine = Lists.newArrayList();
        SubcontractRequirementInfo subcontractRequirement = req.getSubcontractRequirement();
        // 如果主单是Testing Only的类型限定为Testing Only
        Boolean testOnly = false;
        if (Func.isNotEmpty(subcontractRequirement) && Func.isNotEmpty(subcontractRequirement.getReportRequirement())) {
            testOnly = Func.equals(subcontractRequirement.getReportRequirement(), ReportRequirementEnum.Test_Only.getCode());
        }
        CustomResult<Map<String, Object>> validateResult = this.validateSaveSubcontract(req, needDelTestLine, testOnly);
        if (!validateResult.isSuccess()) {
            return validateResult;
        }
        // 已经查询的对象信息不需要重新查询
        GeneralOrderInstanceDTO otsOrder = null;
        OrderAllDTO oldOrder = null;
        SubContractPO oldSubcontract = null;
        SubcontractRequirementInfo oldSubcontractRequirement = null;
        Boolean isTranslation = false;
        if (Func.isNotEmpty(validateResult.getData())) {
            Map<String, Object> subcontractDTOMaps = validateResult.getData();
            otsOrder = (GeneralOrderInstanceDTO) subcontractDTOMaps.get("otsOrder");
            oldOrder = (OrderAllDTO) subcontractDTOMaps.get("gpoOrder");
            oldSubcontract = (SubContractPO) subcontractDTOMaps.get("subContract");
        }
        if (otsOrder == null) {
            return result.fail("获取订单信息失败");
        }
        if (Func.isEmpty(oldOrder)) {
            return result.fail("get order fail from preorder!");
        }
        String orderNo = req.getOrderNo();
        String token = req.getToken();
        String subContractId = req.getSubContractId();
        String testLineInstanceIds = req.getTestLineInstanceIds();
        List<String> ids = JSONArray.parseArray(testLineInstanceIds, String.class);
        String expectDueDate = req.getSubContractExpectDueDate();
        String slimJobNo = req.getSlimJobNo();
        String reportRequirement = subcontractRequirement.getReportRequirement();
        Integer reportQty = subcontractRequirement.getReportQty();
        Integer draftReportRequired = subcontractRequirement.getDraftReportRequired();

        OrderIdReq orderIdReq = new OrderIdReq();
        orderIdReq.setProductLineCode(ProductLineContextHolder.getProductLineCode());
        orderIdReq.setOrderNo(orderNo);

        String buCode = oldOrder.getBUCode();
        String locationCode = oldOrder.getLocationCode();
        isTranslation = OrderOperationType.check(oldOrder.getOperationType(), OrderOperationType.TranslationReport);

        if (Func.isNotEmpty(subContractId)) {
            oldSubcontractRequirement = subcontractRequirementService.getSubcontractRequirement(subContractId).getData();
        }

        if (Func.isEmpty(subContractId)) {
            try {
                req.setSubcontractRequirement(this.filterSubcontractRequirement(req.getSubcontractRequirement(), buCode));
            } catch (Exception e) {
                log.error("filterSubcontractRequirement error:{}", e.getMessage());
            }
        }

        // TL 处理
        List<String> delPpTestLineRelIds = Lists.newArrayList();
        // 需要删除的 map 关系
        List<String> needDelMapping = Lists.newArrayList();
        // 接包方对应的orderId
        String subOrderId = "";
        TestLineStatus rollBackStatus = null;
        if (CollectionUtils.isNotEmpty(needDelTestLine)) {
            // 查询当前订单对应的子单
            String exeOrderId = null;
            if (Func.isNotEmpty(oldSubcontract)) {
                SubContractExternalRelationshipPO searchPO = new SubContractExternalRelationshipPO();
                searchPO.setSubContractNo(oldSubcontract.getSubContractNo());
                searchPO.setSubContractType(SubContractType.SubContract.getType());
                BaseResponse<List<SubContractExternalRelationshipPO>> response = subContractExternalRelService.getSubContractExternalRels(searchPO);
                List<SubContractExternalRelationshipPO> subContractRelList = response.getData();
                if (Func.isNotEmpty(subContractRelList)) {
                    String exeOrderNo = subContractRelList.get(0).getExternalNo();
                    GeneralOrderInstanceDTO exeOrder = generalOrderInstanceMapper.getByNo(exeOrderNo);
                    if (Func.isNotEmpty(exeOrder)) {
                        exeOrderId = exeOrder.getId();
                    }
                }
            }
            // 获取 接包方 需要删除的所有的 PpTestLineRelIds
            List<PPTestLineJobDto> ppTestLineJob = testLineMapper.getPpTestLineJob(otsOrder.getId(), needDelTestLine, exeOrderId);
            if (CollectionUtils.isNotEmpty(ppTestLineJob)) {
                if (ppTestLineJob.stream().filter(pptl -> TestLineStatus.checkCategory(pptl.getTestLineStatus(), Constants.TEST_LINE.STATUS_CATEGORY.LOCK) || NumberUtil.equals(pptl.getPendingFlag(), 1)).collect(Collectors.toSet()).size() > 0) {
                    String editTlStatus = Arrays.stream(TestLineStatus.values()).filter(tl -> tl.getCategory().equalsIgnoreCase(Constants.TEST_LINE.STATUS_CATEGORY.EDIT)).map(TestLineStatus::getMessage).collect(Collectors.joining(","));
                    return result.fail(String.format("Removed Test Line was not in status '" + editTlStatus + "'"));
                }
                subOrderId = ppTestLineJob.get(0).getSubOrderId();
                delPpTestLineRelIds = ppTestLineJob.stream().map(PPTestLineJobDto::getPpTestLineRelID).distinct().collect(Collectors.toList());

                // 需要删除的   tre_order_subcontract_relationship_mapping 中的关系
                needDelMapping.addAll(this.buildNeedDelSubContractRelIds(ppTestLineJob));
                // 回滚TL状态为Typing
                rollBackStatus = Typing;
            }
        }
        //buildSubcontractPO
        CustomResult<SubContractPO> buildSubcontractPOResult = this.buildSubcontractPO(req, oldOrder);
        if (!buildSubcontractPOResult.isSuccess()) {
            return result.fail(buildSubcontractPOResult.getMsg());
        }

        SubContractPO subContract = buildSubcontractPOResult.getData();
        String labCode = oldOrder.getLabDTO().getLabCode();
        List<String> finalDelPpTestLineRelIds = delPpTestLineRelIds;
        String finalSubOrderId = subOrderId;
        GeneralOrderInstanceDTO finalOtsOrder = otsOrder;
        TestLineStatus finalRollBackStatus = rollBackStatus;
        boolean isSuccess = transactionTemplate.execute((trans) -> {
            // 删除TestLine逻辑??? 原有逻辑保留
            if (CollectionUtils.isNotEmpty(finalDelPpTestLineRelIds)) {
                DelTestLineReq delTestLineReq = new DelTestLineReq();
                delTestLineReq.setPpTestLineRelIds(finalDelPpTestLineRelIds);
                delTestLineReq.setOrderId(finalSubOrderId);
                delTestLineReq.setToken(token);
                delTestLineReq.setSubOrderFlag(true);
                CustomResult res = testLineService.delTestLine(delTestLineReq);
                if (!res.isSuccess()) {
                    throw new RuntimeException(res.getMsg());
                }
                // 删除 mapping关系
                if (CollectionUtils.isNotEmpty(needDelMapping)) {
                    orderSubcontractRelService.batchDeleteBySubRelIds(needDelMapping);
                }
                //如果分包出去的只有一个TL,删除该TL时，对应取消接包方的Job,subContract
                //根据分包单ID查询接包方的Job及SubContract信息
                testLineService.updateSubcontractJobStatus(subContractId, finalDelPpTestLineRelIds, user.getRegionAccount());
            }
            // 回滚tl状态
            log.info("needDelTestLine:{}",needDelTestLine);
            if (Func.isNotEmpty(needDelTestLine)) {
                testLineService.rollBackTLStatusAndType(needDelTestLine, finalRollBackStatus);
            }
            //SaveSubcontractTlMapping
            //deal JobTlRelation和SubcontractTlMapping
            if (Func.isNotEmpty(subContractId)) {
                SubContractTestLineMappingExample subContractTestLineMappingExample1 = new SubContractTestLineMappingExample();
                subContractTestLineMappingExample1.createCriteria().andSubContractIDEqualTo(subContractId);
                subContractTestLineMappingMapper.deleteByExample(subContractTestLineMappingExample1);
            }
            List<SubContractTestLineMappingPO> mapping = new ArrayList<>();
            if (Func.isNotEmpty(ids)) {
                for (String id : ids) {
//                SubContractTestLineMappingExample subContractTestLineMappingExample1 = new SubContractTestLineMappingExample();
//                subContractTestLineMappingExample1.createCriteria().andTestLineInstanceIDEqualTo(id);
//                subContractTestLineMappingMapper.deleteByExample(subContractTestLineMappingExample1);
                    SubContractTestLineMappingPO mapp = new SubContractTestLineMappingPO();
                    mapp.setID(UUID.randomUUID().toString());
                    mapp.setSubContractID(subContract.getID());
                    mapp.setTestLineInstanceID(id);
                    mapp.setCreatedDate(new Date());
                    mapp.setCreatedBy(user.getRegionAccount());
                    mapp.setModifiedDate(new Date());
                    mapp.setModifiedBy(user.getRegionAccount());
                    mapping.add(mapp);
                }
                // 更新Job Tl 关系
                jobService.updateJobTlRel(ids, user.getRegionAccount());
                // 更新TestLineInstance状态(纯分包业务增加)
                testLineService.updateTestLineTypeBatch(ids, TestLineType.SubContractOrder);
                testLineInstanceExtMapper.updateTLDueDateByIds(ids, null);
            }
            //添加subcontract_slim_job信息
            if (Func.isNotEmpty(subContractId)) {
                updateSubContractExtRel(subContract.getSubContractNo(), slimJobNo, user.getRegionAccount(), orderNo, buCode, locationCode);
            }
            //SaveSubcontract
            subContractExtMapper.saveOrUpdate(subContract);
            // 维护分包单 TL 关系
            if (Func.isNotEmpty(mapping)) {
                subContractExtMapper.batchInsertSubcontractTestLineMapping(mapping);
            }
            //Build SubcontractRequirement & subcontractRequirementContacts
            CustomResult rspResult = subcontractRequirementService.buildSubcontractRequirement(req);
            if (rspResult.isSuccess()) {
                SubcontractRequirementInfoAllPO subcontractRequirementInfoAllPO = (SubcontractRequirementInfoAllPO) rspResult.getData();
                subcontractRequirementInfoAllPO.getSubcontractRequirementInfoPO().setGeneralOrderInstanceID(finalOtsOrder.getId());
                subcontractRequirementInfoAllPO.getSubcontractRequirementInfoPO().setSubContractID(subContract.getID());
                subcontractRequirementInfoAllPO.getSubcontractRequirementInfoPO().setSubContractNo(subContract.getSubContractNo());
                subcontractRequirementService.doDeal(subcontractRequirementInfoAllPO);
            }
            return true;
        });

        // 执行成功之后更新外部系统数据
        if (isSuccess) {
            // 执行分包单完成
            if (Func.isNotEmpty(req.getSubContractId())) {
                if (subcontractStatusBizService.compareFileQty(oldSubcontract.getSubContractNo()).getData()) {
                    SubContractCompleteReq subcontractCompleteReq = new SubContractCompleteReq();
                    List<SubContractItemCompleteReq> reqs = Lists.newArrayList();
                    SubContractItemCompleteReq subcontractItemCompleteReq = new SubContractItemCompleteReq();
                    subcontractItemCompleteReq.setSubcontractNo(oldSubcontract.getSubContractNo());
                    subcontractItemCompleteReq.setExternalNo(req.getSlimJobNo());
                    subcontractItemCompleteReq.setObjectType(SubReportObjectTypeEnums.subcontract.getObjectType());
                    subcontractItemCompleteReq.setReportFlag(ReportFlagEnums.SUB_REPORT.getCode());
                    reqs.add(subcontractItemCompleteReq);
                    subcontractCompleteReq.setCompleteItems(reqs);
                    CustomResult completeResult = this.complete(subcontractCompleteReq);
                    log.info("Subcontract complete Result:{}", JSON.toJSONString(completeResult));
                }
            }
            // 校验是否修改了subcontractFee/subcontractFeeCurrency 如果修改了，需要同步到生成的Order
            if (Func.isNotEmpty(subContractId)) {
                updateSubcontractFee(subContract, subcontractRequirement);
            }
            String productLineCode = ProductLineContextHolder.getProductLineCode();
            SubContractPO newSubcontract = subContractMapper.selectByPrimaryKey(subContract.getID());
            ToDoListReq toDoListReq = new ToDoListReq();
            toDoListReq.setType(TodoType.SubContract.getCode());
            toDoListReq.setProductLineCode(productLineCode);
            toDoListReq.setLocationCode(oldOrder.getLocationCode());
            toDoListReq.setLabCode(labCode);
            toDoListReq.setObjectId(newSubcontract.getID());
            toDoListReq.setObjectNo(newSubcontract.getSubContractNo());
            if (newSubcontract.getStatus() == SubcontractStatus.New.getCode()) {
                toDoListReq.setStatus(TodoStatus.Todo.getCode());
            } else if (newSubcontract.getStatus() == SubcontractStatus.Testing.getCode()) {
                toDoListReq.setStatus(TodoStatus.Ongoing.getCode());
            } else if (newSubcontract.getStatus() == SubcontractStatus.Complete.getCode()) {
                toDoListReq.setStatus(TodoStatus.Completed.getCode());
            }
            toDoListReq.setData(newSubcontract);
            toDoListReq.setSgsToken(tokenClient.getToken());

            // 如果是分包单，to do 表不再存储
//            SubContractInfo subContractInfo = subContractExtMapper.queryOrderSubContractInfo(orderNo);
            // 如果Report Requirement 类型Testing Only，不创建Todo List记录
            if (!testOnly && !isTranslation) {
                MessageReq messageReq = new MessageReq();
                ToDoListMessage toDoListMessage = new ToDoListMessage();
                toDoListMessage.setObjectId(toDoListReq.getObjectId());
                toDoListMessage.setObjectNo(toDoListReq.getObjectNo());
                toDoListMessage.setType(KafkaActionType.SubContract.getCode());
                toDoListMessage.setProductLineCode(productLineCode);
                toDoListMessage.setLocationCode(toDoListReq.getLocationCode());
                toDoListMessage.setLabCode(toDoListReq.getLabCode());
                toDoListMessage.setStatus(toDoListReq.getStatus());
                toDoListMessage.setAssignee(user.getRegionAccount());
                toDoListMessage.setData(JSONObject.toJSONString(toDoListReq.getData()));

                messageReq.setProductLineCode(productLineCode);
                messageReq.setAction(KafkaActionType.TodoList.getCode());
                messageReq.setData(JSONObject.toJSONString(toDoListMessage));
                messageReq.setUserName(user.getRegionAccount());

                producer.doSend(KafkaTopicConsts.TOPIC_TODO, messageReq);
                log.info("CreateSubcontractToDo:{}",messageReq);
            } else if (testOnly) {
                //TestOnly,如果是修改为TestOnly,要把todo Cancel掉
                TodoStatusReq todoStatusReq = new TodoStatusReq();
                todoStatusReq.setObjectId(subContract.getID());
                todoStatusReq.setObjectNo(subContract.getSubContractNo());
                todoStatusReq.setStatus(TodoStatus.Canceled.getCode());
                todoStatusReq.setSgsToken(tokenClient.getToken());
                toDoListFacade.updateTodoStatus(todoStatusReq);
            }
            // sendSubcontract To Tracking
            if (StringUtils.isBlank(subContractId)) {
                trackingService.sendSubContract(orderNo, tokenClient.getToken(), tokenClient.getUser().getRegionAccount(), null);
            }
            //先查询出原ExpectDueDate，用以保存BizLog
            Date oldSubContractExpectDueDate = null;
            if (Func.isNotEmpty(oldSubcontract)) {
                oldSubContractExpectDueDate = oldSubcontract.getSubContractExpectDueDate();
            }
            //SendBizLog
            // SubContractExpectDueDate Change Log
            if ((Func.isEmpty(oldSubContractExpectDueDate) && Func.isNotEmpty(expectDueDate)) || (Func.isNotEmpty(oldSubContractExpectDueDate) && Func.isEmpty(expectDueDate)) || !DateUtils.format(oldSubContractExpectDueDate).equalsIgnoreCase(Func.toStr(expectDueDate))) {
                BizLogInfo bizLog = new BizLogInfo();
                bizLog.setBu(buCode);
                bizLog.setLab(locationCode);
                bizLog.setOpUser(user.getRegionAccount());
                bizLog.setBizId(orderNo);
                bizLog.setOpType("Expect due date Change");
                bizLog.setBizOpType(BizLogConstant.SUBCONTRACT_OPERATION_HOSTORY);
                bizLog.setNewVal(newSubcontract.getSubContractNo() + ",From[" + (Func.isEmpty(oldSubContractExpectDueDate) ? "" : DateUtils.format(oldSubContractExpectDueDate)) + "] To [" + (Func.isEmpty(expectDueDate) ? "" : expectDueDate) + "]");
                bizLogClient.doSend(bizLog);
            }
            if (Func.isNotEmpty(subContractId)) {
                // 记录Report Requirement、Draft Report Required、Report File Qty变化
                String oldReportRequirement = oldSubcontractRequirement.getReportRequirement();
                Integer oldReportQty = oldSubcontractRequirement.getReportQty();
                Integer oldDraftRequired = oldSubcontractRequirement.getDraftReportRequired();
                if ((Func.isNotEmpty(oldReportRequirement) || Func.isNotEmpty(reportRequirement))
                        && !Func.equalsSafe(oldReportRequirement, reportRequirement)) {
                    BizLogInfo bizLog = new BizLogInfo();
                    bizLog.setBu(buCode);
                    bizLog.setLab(locationCode);
                    bizLog.setOpUser(user.getRegionAccount());
                    bizLog.setBizId(orderNo);
                    bizLog.setOpType("Report Requirement Change");
                    bizLog.setBizOpType(BizLogConstant.SUBCONTRACT_OPERATION_HOSTORY);
                    bizLog.setNewVal(newSubcontract.getSubContractNo() + ",From[" + Strings.nullToEmpty(ReportRequirementEnum.getMessage(oldReportRequirement)) + "] To [" + Strings.nullToEmpty(ReportRequirementEnum.getMessage(reportRequirement)) + "]");
                    bizLogClient.doSend(bizLog);
                }
                if ((Func.isNotEmpty(oldReportQty) || Func.isNotEmpty(reportQty))
                        && !Func.equalsSafe(oldReportQty, reportQty)) {
                    BizLogInfo bizLog = new BizLogInfo();
                    bizLog.setBu(buCode);
                    bizLog.setLab(locationCode);
                    bizLog.setOpUser(user.getRegionAccount());
                    bizLog.setBizId(orderNo);
                    bizLog.setOpType("Report File Qty Change");
                    bizLog.setBizOpType(BizLogConstant.SUBCONTRACT_OPERATION_HOSTORY);
                    bizLog.setNewVal(newSubcontract.getSubContractNo() + ",From[" + (Func.isEmpty(oldReportQty) ? "" : oldReportQty) + "] To [" + (Func.isEmpty(reportQty) ? "" : reportQty) + "]");
                    bizLogClient.doSend(bizLog);
                }
                if ((Func.isNotEmpty(oldDraftRequired) || Func.isNotEmpty(draftReportRequired))
                        && !Func.equalsSafe(oldDraftRequired, draftReportRequired)) {
                    BizLogInfo bizLog = new BizLogInfo();
                    bizLog.setBu(buCode);
                    bizLog.setLab(locationCode);
                    bizLog.setOpUser(user.getRegionAccount());
                    bizLog.setBizId(orderNo);
                    bizLog.setOpType("Draft Report Required Change");
                    bizLog.setBizOpType(BizLogConstant.SUBCONTRACT_OPERATION_HOSTORY);
                    bizLog.setNewVal(newSubcontract.getSubContractNo() + ",From[" + (Func.isEmpty(oldDraftRequired) ? "" : oldDraftRequired) + "] To [" + (Func.isEmpty(draftReportRequired) ? "" : draftReportRequired) + "]");
                    bizLogClient.doSend(bizLog);
                }
            }
            if (Func.isNotEmpty(needDelTestLine)) {
                SystemLog systemLog = new SystemLog();
                systemLog.setObjectType(SystemLogObjectEnum.SUBCONTRACT.getType());
                systemLog.setObjectNo(subContract.getSubContractNo());
                systemLog.setProductLineCode(ProductLineContextHolder.getProductLineCode());
                systemLog.setLocationCode(ProductLineContextHolder.getLocationCode());
                systemLog.setType(SystemLogType.API.getType());
                systemLog.setRemark("TestLineInstanceId");
                systemLog.setOperationType("Deleted");
                systemLog.setCreateBy(user.getRegionAccount());
                systemLog.setRequest(JSON.toJSONString(needDelTestLine));
                systemLogHelper.save(systemLog);
            }
        }
        result.setData(subContract);
        result.setSuccess(true);
        return result;
    }

    /**
     * 新建分包单时根据ObjectSetting配置的字段过滤失效字段
     *
     * @param oldVal
     * @return
     */
    private SubcontractRequirementInfo filterSubcontractRequirement(SubcontractRequirementInfo oldVal, String buCode) throws IllegalAccessException {
        // 查询分包单对应的Setting配置
        Map<String, String> displayFields = Maps.newHashMap();
        BuObjectTemplateDTO buObjectTemplateReq = new BuObjectTemplateDTO();
        buObjectTemplateReq.setObject("subcontract");
        buObjectTemplateReq.setProductLineCode(buCode);
        log.info("buSettingReq :{}", JSON.toJSONString(buObjectTemplateReq));
        BaseResponse<Map<String, String>> buSettingRes = buParamFacade.getTemplateSettingList(buObjectTemplateReq);
        log.info("buSettingRes :{}", JSON.toJSONString(buSettingRes));
        if (Func.isNotEmpty(buSettingRes) && Func.isNotEmpty(buSettingRes.getData())) {
            displayFields = buSettingRes.getData();
        }
        if (Func.isEmpty(displayFields)) {
            return oldVal;
        }
        // subcontract serviceRequirement过滤
        final Field[] fields = oldVal.getClass().getDeclaredFields();
        if (fields.length > 0) {
            for (Field field : fields) {
                ObjectSetting objectSetting = field.getAnnotation(ObjectSetting.class);
                if (Func.isEmpty(objectSetting)) {
                    continue;
                }
                String objectSettingCode = objectSetting.code();
                if (!displayFields.containsKey(objectSettingCode)) {
                    field.setAccessible(true);
                    field.set(oldVal, null);
                }
            }
        }
        // contacts过滤
        List<SubcontractRequirementContactsInfo> subcontractRequirementContacts = oldVal.getSubcontractRequirementContactsInfos();
        if (Func.isNotEmpty(subcontractRequirementContacts)) {
            List<SubcontractRequirementContactsInfo> newSubcontractRequirementContacts = Lists.newArrayList();
            Map<String, String> finalDisplayFields = displayFields;
            subcontractRequirementContacts.stream().forEach(reqTestContact -> {
                Integer contactType = reqTestContact.getContactsType();
                switch (contactType) {
                    // SoftCopy Deliver To
                    case 1:
                        if (finalDisplayFields.containsKey("softCopyDeliverTo")) {
                            newSubcontractRequirementContacts.add(reqTestContact);
                        }
                        break;
                    // HardCopy Deliver To
                    case 2:
                        if (finalDisplayFields.containsKey("hardCopyDeliverTo")) {
                            newSubcontractRequirementContacts.add(reqTestContact);
                        }
                        break;
                    // Return Sample To
                    case 4:
                        if (finalDisplayFields.containsKey("returnSampleTo")) {
                            newSubcontractRequirementContacts.add(reqTestContact);
                        }
                        break;
                    // Invoice Deliver To
                    case 5:
                        if (finalDisplayFields.containsKey("invoiceDeliverTo")) {
                            newSubcontractRequirementContacts.add(reqTestContact);
                        }
                        break;
                    // Prelim Report
                    case 6:
                        if (finalDisplayFields.containsKey("prelimReport")) {
                            newSubcontractRequirementContacts.add(reqTestContact);
                        }
                        break;
                    // Prelim Report CC
                    case 7:
                        if (finalDisplayFields.containsKey("prelimReportCC")) {
                            newSubcontractRequirementContacts.add(reqTestContact);
                        }
                        break;
                    // SoftCopy Deliver Cc
                    case 8:
                        if (finalDisplayFields.containsKey("softCopyDeliverCC")) {
                            newSubcontractRequirementContacts.add(reqTestContact);
                        }
                        break;
                }
            });
            oldVal.setSubcontractRequirementContactsInfos(newSubcontractRequirementContacts);
        }
        return oldVal;
    }

    private int updateSubContractExtRel(String subcontractNo, String externalNo, String userName, String orderNo, String buCode, String locationCode) {
        SubContractExternalRelationshipPO searchPO = new SubContractExternalRelationshipPO();
        searchPO.setSubContractNo(subcontractNo);
        BaseResponse<List<SubContractExternalRelationshipPO>> response = subContractExternalRelService.getSubContractExternalRels(searchPO);
        List<SubContractExternalRelationshipPO> subContractSlimJobList = response.getData();
        int result = 0;
        if (Func.isEmpty(subContractSlimJobList)) {
            if (Func.isNotEmpty(externalNo)) {
                SubContractExternalRelationshipPO po = new SubContractExternalRelationshipPO();
                po.setID(UUID.randomUUID().toString());
                po.setExternalNo(externalNo);
                po.setSubContractNo(subcontractNo);
                po.setStatus(false);
                po.setCreatedBy(userName);
                po.setCreatedDate(DateUtils.getNow());
                po.setSubContractType(SubContractType.None.getType());
                log.info("添加subcontract_slim_job表,data = {}", po);
                result = subContractExternalRelService.insert(po);
            }
        } else {
            List<SubContractExternalRelationshipPO> updateList = subContractSlimJobList.stream().filter(
                    s -> Func.equalsSafe(SubContractType.None.getType(), s.getSubContractType())
                            && !s.getExternalNo().equalsIgnoreCase(externalNo)).collect(Collectors.toList());
            if (Func.isNotEmpty(updateList)) {
                // 如果externalNo 为空表示清空的场景
                if (Func.isEmpty(externalNo)) {
                    SubContractExternalRelationshipPO record = new SubContractExternalRelationshipPO();
                    record.setSubContractNo(updateList.get(0).getSubContractNo());
                    record.setExternalNo(updateList.get(0).getExternalNo());
//                    record.setSubContractType(SubContractType.None.getType());
                    subContractExternalRelService.delSubContractExternalRel(record);
                } else {
                    // 如果externalNo 不为空表示更新的场景
                    List<String> idList = updateList.stream().map(SubContractExternalRelationshipPO::getID).collect(Collectors.toList());
                    SubContractExternalRelationshipPO subContractSlimJobPO = new SubContractExternalRelationshipPO();
                    subContractSlimJobPO.setExternalNo(externalNo);
                    subContractSlimJobPO.setModifiedDate(DateUtils.getNow());
                    subContractSlimJobPO.setModifiedBy(userName);
                    subContractSlimJobPO.setSubContractType(SubContractType.None.getType());
                    result = subContractExternalRelService.updateBySelectiveByIds(idList, subContractSlimJobPO).getData();
                }
                // 记录ReferenceNo变化到BizLog
                BizLogInfo bizLog = new BizLogInfo();
                bizLog.setBu(buCode);
                bizLog.setLab(locationCode);
                bizLog.setOpUser(userName);
                bizLog.setBizId(orderNo);
                bizLog.setOpType("Subcontract ReferenceNo");
                bizLog.setBizOpType(BizLogConstant.SUBCONTRACT_OPERATION_HOSTORY);
                bizLog.setNewVal(subcontractNo + ",From[" + updateList.get(0).getExternalNo() + "] To [" + (Func.isNotEmpty(externalNo) ? externalNo : " ") + "]");
                bizLogClient.doSend(bizLog);
            }
        }
        return result;
    }

    /**
     * 分包单 Testing逻辑
     *
     * @return
     */
    @Override
//    @BizLog(bizType = BizLogConstant.SUBCONTRACT_OPERATION_HOSTORY, operType = "Testing")
    public CustomResult testing(SubContractTestingReq req) {
        CustomResult customResult = CustomResult.newSuccessInstance();
        String subcontractNo = req.getSubcontractNo();
        String externalNo = req.getExternalNo();
        Integer subcontractType = req.getSubcontractType();
        // 基本参数校验
        if (Func.isEmpty(req.getProductLineCode())) {
            customResult.fail("未获取到productLineCode");
            return customResult;
        }
//        if(Func.isEmpty(externalNo)){
//            customResult.fail("未获取到externalNo, 不做处理");
//            return customResult;
//        }
        if (Func.isEmpty(subcontractNo)) {
            customResult.fail("未获取到SubcontractNo, 不做处理");
            return customResult;
        }
        if (Func.isEmpty(subcontractType)) {
            customResult.fail("subcontractType 不能为空");
            return customResult;
        }
        // 业务状态校验
        SubContractInfo subContractInfo = subContractExtMapper.getSubContractInfo(subcontractNo);
        if (Func.isEmpty(subContractInfo)) {
            customResult.fail("没有查询到分包数据 " + subcontractNo + ", 不做处理");
            return customResult;
        }
        if (SubContractStatusEnum.check(subContractInfo.getStatus(), SubContractStatusEnum.Cancelled)) {
            customResult.fail("分包单" + subcontractNo + " 的status为Cancelled，不做任何处理");
            return customResult;
        }
        if (SubContractStatusEnum.check(subContractInfo.getStatus(), SubContractStatusEnum.Complete)) {
            customResult.fail("分包单" + subcontractNo + " 的status为Completed，不做任何处理");
            return customResult;
        }
        if (SubContractStatusEnum.check(subContractInfo.getStatus(), SubContractStatusEnum.Pend)) {
            customResult.fail("分包单" + subcontractNo + " 的status为Pend，不做任何处理");
            return customResult;
        }
        Boolean isTracingOnly = SubcontractOperationModel.check(subContractInfo.getOperationModel(), SubcontractOperationModel.TracingOnly);
        // 检查分包单下是否存在有效的TL
        SubContractTestLineMappingExample subContractTestLineMappingExample = new SubContractTestLineMappingExample();
        subContractTestLineMappingExample.createCriteria().andSubContractIDEqualTo(subContractInfo.getId());
        List<SubContractTestLineMappingPO> subContractTlList = subContractTestLineMappingMapper.selectByExample(subContractTestLineMappingExample);
        if (Func.isEmpty(subContractTlList) && !isTracingOnly) {
            customResult.fail("请检查分包单下是否存在有效的TL");
            return customResult;
        }
        // 基于Subcontract -> Test Line -> Report Matrix -> Report 状态
        if (reportMatrixRelMapper.checkReportByMatrix(subContractInfo.getId()) > 0) {
            customResult.fail("当前report状态已不允许再接收外部系统报告");
            return customResult;
        }
        // 基于subcontractType更新已经存在的绑定关系
        CustomResult checkResult = this.checkSubcontractType(subcontractType, subContractInfo, req.getProductLineCode());
        if (!checkResult.isSuccess()) {
            return checkResult;
        }
        UserInfo userInfoFillSystem = SecurityContextHolder.getUserInfoFillSystem();
        // 维护SubcontractNo、ExternalNo关系
        boolean success = transactionTemplate.execute(trans -> {
            if (createAssociation(externalNo, subcontractNo, subcontractType)) {
                Date startDate = subContractInfo.getStartDate();
                // 当前分包数据如果已经存在startDate，不论当前状态如何，都不再进行startDate的值录入，只更新externalNo
                if (Func.isEmpty(startDate)) {
                    SubContractPO subContractPO = new SubContractPO();
                    subContractPO.setID(subContractInfo.getId());
                    subContractPO.setOrderNo(subContractInfo.getOrderNo());
                    subContractPO.setSubContractNo(subContractInfo.getSubContractNo());
                    subContractPO.setStatus(SubContractStatusEnum.Testing.getStatus());
                    subContractPO.setStartDate(Func.isEmpty(req.getStartDate()) ? DateUtils.getNow() : req.getStartDate());
                    subContractPO.setModifiedBy(userInfoFillSystem.getRegionAccount());
                    subContractPO.setModifiedDate(DateUtils.getNow());
                    subContractPO.setSubContractOrder(req.getSubcontractType());
                    subContractService.updateSubContractTestingStatus(subContractPO);
                    // 更新TL状态为Entered
                    if (Func.isNotEmpty(subContractTlList)) {
                        List<String> subcontractTlIds = subContractTlList.stream().map(SubContractTestLineMappingPO::getTestLineInstanceID).collect(Collectors.toList());
                        TestLineStatusUpdateReq testLineStatusUpdateReq = new TestLineStatusUpdateReq();
                        testLineStatusUpdateReq.setOrderNo(subContractInfo.getOrderNo());
                        testLineStatusUpdateReq.setTestLineInstanceIds(subcontractTlIds);
                        testLineStatusUpdateReq.setSynMatrixStatus(true);
                        testLineStatusUpdateReq.setTrigger(Constants.OBJECT.SUBCONTRACT.name);
                        CustomResult updateResult = testLineStatusServiceNew.entered(testLineStatusUpdateReq);
                        if (!updateResult.isSuccess()) {
                            trans.setRollbackOnly();
                            customResult.fail(updateResult.getMsg());
                            return false;
                        }
//                        updateTestLineStatus(subContractInfo.getOrderNo(),subcontractTlIds);
                    }
//                    BizLogHelper.setValue(subContractPO.getOrderNo(), subContractPO.getSubContractNo());
                    String oldSubcontractStatusStr = SubcontractStatus.getMessage(subContractInfo.getStatus());
                    String newValMessage = "Testing-"+subcontractNo;
                    OrderIdReq orderIdReq = new OrderIdReq();
                    orderIdReq.setOrderNo(subContractInfo.getOrderNo());
                    OrderAllDTO orderAllDTO = orderFacade.getOrderForPe(orderIdReq).getData();
                    BizLogInfo bizLog = new BizLogInfo();
                    bizLog.setBizOpType(BizLogConstant.SUBCONTRACT_OPERATION_HOSTORY);
                    bizLog.setBu(orderAllDTO.getBUCode());
                    bizLog.setLab(orderAllDTO.getLocationCode());
                    bizLog.setOpUser(userInfoFillSystem.getRegionAccount());
                    bizLog.setBizId(subContractInfo.getOrderNo());
                    bizLog.setOpType("Subcontract Status Update");
                    bizLog.setNewVal(newValMessage);
                    bizLog.setOriginalVal(oldSubcontractStatusStr);
                    bizLogClient.doSend(bizLog);
                } else {
                    log.info("当前分包号{}状态是{}，存在startDate，只做externalNo的插入，不做分包状态和startDate的更新", subcontractNo, subContractInfo.getStatus());
                }
            }
            return true;
        });
        // 更新外部系统数据
        if (success) {
            // 查询最新的分包单信息
            SubContractPO sub = subContractMapper.selectByPrimaryKey(subContractInfo.getId());
            if (SubContractStatusEnum.check(sub.getStatus(), SubContractStatusEnum.Testing)) {
                TodoStatusReq todoStatusReq = new TodoStatusReq();
                todoStatusReq.setObjectId(sub.getID());
                todoStatusReq.setObjectNo(sub.getSubContractNo());
                todoStatusReq.setStatus(TodoStatus.Ongoing.getCode());
                BaseResponse<String> updateStatusRes = toDoListFacade.updateTodoStatus(todoStatusReq);
                log.info("更新分包单TODO结果：{}", JSON.toJSONString(updateStatusRes));
            }
        }
        return customResult;
    }

    /**
     * 校验当前分包单是否已经绑定其它系统的单据
     *
     * @return
     */
    private CustomResult checkSubcontractType(Integer subcontractType, SubContractInfo subContract, String productLineCode) {
        CustomResult checkResult = CustomResult.newSuccessInstance();
        if (!SubContractType.check(subcontractType, SubContractType.ToSlim, SubContractType.ToStarLims)) {
            return checkResult;
        }
        // 查询分包单已经绑定的关系数据
        List<SubContractExternalRelationshipPO> subContractExtRelList;
        SubContractExternalRelationshipPO searchPO = new SubContractExternalRelationshipPO();
        searchPO.setSubContractNo(subContract.getSubContractNo());
        if (SubContractType.check(subcontractType, SubContractType.ToSlim)) {
            searchPO.setSubContractType(SubContractType.ToStarLims.getType());
        }
        if (SubContractType.check(subcontractType, SubContractType.ToStarLims)) {
            searchPO.setSubContractType(SubContractType.ToSlim.getType());
        }
        subContractExtRelList = subContractExternalRelService.getSubContractExternalRels(searchPO).getData();
        // 不存在返回成功
        if (Func.isEmpty(subContractExtRelList)) {
            return checkResult;
        }
        // 存在其它绑定关系执行覆盖校验逻辑
        // 1,Subcontract Status=[New,Testing]  2,tb_sub_report 中不存在已经回写的报告
        Boolean cover = false;
        if (SubcontractStatus.New.getCode() == subContract.getStatus() ||
                SubcontractStatus.Testing.getCode() == subContract.getStatus()) {
            // 查询分包单SubReport数据
            List<SubReportPO> subReportPOList = subReportExtMapper.querySubReportBySubcontractNo(subContract.getSubContractNo());
            if (Func.isEmpty(subReportPOList)) {
                cover = true;
            } else {
                String objectType = SubContractType.check(subcontractType, SubContractType.ToSlim) ? SubReportObjectTypeEnums.starlims.getObjectType() : SubReportObjectTypeEnums.slimjob.getObjectType();
                SubReportPO subReport = subReportPOList.stream().filter(s -> Func.equals(objectType, s.getObjectType())).findAny().orElse(null);
                cover = Func.isEmpty(subReport);
            }
        }

        if (cover) {
            // 覆盖已经存在的关系
            subContractExtRelList.stream().forEach(rel -> {
                SubContractExternalRelationshipPO delRel = new SubContractExternalRelationshipPO();
                delRel.setSubContractNo(rel.getSubContractNo());
                delRel.setExternalNo(rel.getExternalNo());
                subContractExternalRelService.delSubContractExternalRel(delRel);
            });
            return checkResult;
        } else {
            // 发送邮件通知CS拒绝接收
            String orderNo = subContract.getOrderNo();
            OrderIdReq orderIdReq = new OrderIdReq();
            orderIdReq.setOrderNo(orderNo);
            orderIdReq.setProductLineCode(productLineCode);
            BaseResponse<OrderAllDTO> orderAllRes = orderFacade.getOrderForPe(orderIdReq);
            if (Func.isNotEmpty(orderAllRes) && Func.isNotEmpty(orderAllRes.getData())) {
                OrderAllDTO orderAllDTO = orderAllRes.getData();
                String fromSystem = SubContractType.check(subcontractType, SubContractType.ToSlim) ? "SLIM" : "Starlims";
                String relSystem = SubContractType.check(subcontractType, SubContractType.ToSlim) ? "Starlims" : "SLIM";
                String subject = fromSystem + "报告被GPO拒收通知";
                String templateCode = "GPO_Ext_Report_Reject";
                Set<String> mailTo = Sets.newHashSet();
                Set<String> mailToName = Sets.newHashSet();
                mailTo.add(orderAllDTO.getcSEmail());
                mailToName.add(orderAllDTO.getcSName());
                OrderPersonInfoReq reqObject = new OrderPersonInfoReq();
                reqObject.setOrderId(orderAllDTO.getOrderId());
                reqObject.setPersonType(OrderPersonType.CSA.getCode());
                CustomResult<OrderPersonInfoRsp> personResult = orderPersonClient.getPersonInfoByOrderId(reqObject);
                OrderPersonInfoRsp person = personResult.getData();
                if (Func.isNotEmpty(person) && Func.isNotEmpty(person.getEmail())) {
                    mailTo.add(person.getEmail());
                    mailToName.add(person.getRegionAccount());
                }
                EmailAutoSendDTO emailAutoSendDTO = new EmailAutoSendDTO();
                emailAutoSendDTO.setMailSubject(subject);
                emailAutoSendDTO.setTemplateCode(templateCode);
                emailAutoSendDTO.setMailTo(mailTo.stream().collect(Collectors.toList()));
                emailAutoSendDTO.setLabCode(orderAllDTO.getLabDTO().getLabCode());
                Map<String, Object> emailParamMap = Maps.newHashMap();
                emailParamMap.put("system", fromSystem);
                emailParamMap.put("relSystem", relSystem);
                emailParamMap.put("subcontractNo", subContract.getSubContractNo());
                emailParamMap.put("toName", mailToName.stream().collect(Collectors.joining(",")));
                emailAutoSendDTO.setTemplateVariables(emailParamMap);

                //保存日志
                SystemLog systemLog = new SystemLog();
                systemLog.setObjectType("GPO_Ext_Report_Reject");
                systemLog.setObjectNo(subContract.getSubContractNo());
                systemLog.setProductLineCode(productLineCode);
                systemLog.setType(SystemLogType.SYSTEM.getType());
                systemLog.setRemark("外部系统回写报告拒绝通知邮件");
                systemLog.setCreateBy(fromSystem);
                systemLog.setLocationCode(orderAllDTO.getLabDTO().getLabCode());
                systemLog.setOperationType("External Report Reject Email");
                sendEmailService.sendEmail(emailAutoSendDTO, systemLog);
            }
            checkResult.fail("分包单已经绑定其它系统的数据。");
        }
        return checkResult;
    }

    /**
     * 分包单 Complete逻辑
     *
     * @return
     */
    @Override
    public CustomResult complete(SubContractCompleteReq req) {
        CustomResult customResult = CustomResult.newSuccessInstance();
        List<SubContractItemCompleteReq> completeItems = req.getCompleteItems();
        if (Func.isEmpty(completeItems)) {
            return customResult;
        }
        String userName = SystemName;
        UserInfo user = SecurityContextHolder.getUserInfo();
        if (Func.isNotEmpty(user)) {
            userName = user.getRegionAccount();
        }
        String buCode = ProductLineContextHolder.getProductLineCode();
        //记录批量执行结果
        List<SubContractCompleteRes> subContractCompleteResList = Lists.newArrayList();
        String finalUserName = userName;
        completeItems.stream().forEach(item -> {
            SubContractCompleteRes subContractCompleteRes = new SubContractCompleteRes();
            subContractCompleteRes.setSubcontractNo(item.getSubcontractNo());
            subContractCompleteRes.setExternalNo(item.getExternalNo());
            // 参数校验
            String subcontractNo = item.getSubcontractNo();
            List<ExternalFileReq> subReports = item.getSubReports();
            Set<String> subReportNos = Sets.newHashSet();
            if (Func.isEmpty(subcontractNo)) {
                subContractCompleteRes.setStatus(ResponseCode.PARAM_VALID_ERROR.getCode());
                subContractCompleteRes.setMessage("subcontractNo 不能为空");
                subContractCompleteResList.add(subContractCompleteRes);
                return;
            }
            SubContractInfo subContractInfo = subContractExtMapper.getSubContractInfo(subcontractNo);
            if (Func.isEmpty(subContractInfo)) {
                subContractCompleteRes.setStatus(ResponseCode.PARAM_VALID_ERROR.getCode());
                subContractCompleteRes.setMessage("subcontractNo 不存在");
                subContractCompleteResList.add(subContractCompleteRes);
                return;
            }
            if (SubContractStatusEnum.check(subContractInfo.getStatus(), SubContractStatusEnum.Cancelled)) {
                subContractCompleteRes.setStatus(ResponseCode.FAIL.getCode());
                subContractCompleteRes.setMessage("subcontract already cancelled");
                subContractCompleteResList.add(subContractCompleteRes);
                return;
            }
            GeneralOrderInstanceInfoPO orderInfo = orderMapper.getOrderInfo(subContractInfo.getOrderNo());
//            OrderInfoDto orderInfoDto = orderClient.getOrderInfoByOrderNo(subContractInfo.getOrderNo());
            SubcontractRequirementInfo subcontractRequirementInfo = subcontractRequirementMapper.getSubcontractRequirementBySubcontractNo(subcontractNo);
            OrderIdReq orderIdReq = new OrderIdReq();
            orderIdReq.setOrderNo(orderInfo.getOrderNo());
            orderIdReq.setProductLineCode(buCode);
            BaseResponse<OrderAllDTO> orderForPe = orderFacade.getOrderForPe(orderIdReq);
            OrderAllDTO orderAllDTO = orderForPe.getData();
            String orderInfoID = orderInfo.getID();
            String objectType = item.getObjectType();
            Integer reportFlag = item.getReportFlag();
            String objectNo = Func.isEmpty(item.getObjectNo()) ? item.getSubcontractNo() : item.getObjectNo();
            // SubReport信息更新
            if (Func.isNotEmpty(subReports)) {
                // 需要维护的SubReport列表
                List<SubReportPO> shouldUpdateSubReports = Lists.newArrayList();
                // 需要更新的ReportFile列表
                List<ReportFilePO> shouldUpdateReportFiles = Lists.newArrayList();
                // 需要增加的ReportFile列表
                List<ReportFilePO> shouldInsertReportFiles = Lists.newArrayList();
                // Word需要维护Report_SubReport关系,用来合并报告
                List<SubReportPO> wordSubReportRelList = Lists.newArrayList();
                // PDF的场景维护Report_SubReport关系
                List<ReportSubReportRelationshipInfoPO> pdfSubReportRelList = Lists.newArrayList();
                // 需要删除的SubReport
                List<String> shouldDeleteSubReports = Lists.newArrayList();
                // 需要删除的ReportFile
                List<String> shouldDeleteFiles = Lists.newArrayList();
                // 需要更新的Report信息
                List<ReportInfoPO> shouldUpdateReportStatusList = Lists.newArrayList();

                Map<String, List<SubReportPO>> fileNameGroupReport = Maps.newHashMap();
                Map<String, List<SubReportPO>> subReportNoGroupReport = Maps.newHashMap();
                // 查询分包单下现有的有效的SubReport列表
                List<SubReportPO> subReportPOList = subReportExtMapper.querySubReportBySubcontractNo(subcontractNo);
                subReportPOList = subReportPOList.stream().filter(s ->
                        !SubReportStatusEnum.check(s.getStatus(), SubReportStatusEnum.Cancelled, SubReportStatusEnum.Reworked)).collect(Collectors.toList());
                if (Func.isNotEmpty(subReportPOList)) {
                    fileNameGroupReport = subReportPOList.stream().collect(Collectors.groupingBy(SubReportPO::getFilename));
                    subReportNoGroupReport = subReportPOList.stream().collect(Collectors.groupingBy(SubReportPO::getSubReportNo));
                }
                SubContractReq subContractReq = new SubContractReq();
                subContractReq.setSubContractNo(subContractInfo.getSubContractNo());
                subContractReq.setProductLineCode(orderAllDTO.getProductLineCode());
                String subReportTestMatrixMergeMode = subReportService.buildTestMatrixMergeMode(subContractReq).getData();
                List<ReportInfoPO> referenceReportList = new ArrayList<>();
                for (ExternalFileReq fileReq : subReports) {
                    // Conclusion 信息校验
                    String conclusion = fileReq.getConclusion();
                    String conclusionId = null;
                    if (Func.isNotEmpty(conclusion)) {
                        ConclusionListPO conclusionListPO = conclusionListService.getConclusionList(conclusion);
                        if (Func.isEmpty(conclusionListPO)) {
                            subContractCompleteRes.setSubReportNo(fileReq.getSubReportNo());
                            subContractCompleteRes.setStatus(ResponseCode.FAIL.getCode());
                            subContractCompleteRes.setMessage("根据conclusion 配置表tb_conclusion_list中未查到对应数据");
                            return;
                        }
                        conclusionId = conclusionListPO.getID();
                    }
                    //20240123 新需求GPO2-13625 StarLims回传 ExternalReportNo判断
                    String externalReportNo = fileReq.getReportNo();
                    if(Func.isNotEmpty(externalReportNo)){
                        //查询Report是否存在
                        ReportInfoExample example = new ReportInfoExample();
                        example.createCriteria().andReportNoEqualTo(externalReportNo);
                        List<ReportInfoPO> reportInfoPOList = reportInfoMapper.selectByExample(example);
                        if(Func.isEmpty(reportInfoPOList)){
                            log.info("reportNo不存在：{}",externalReportNo);
                            subContractCompleteRes.setSubReportNo(fileReq.getSubReportNo());
                            subContractCompleteRes.setStatus(ResponseCode.FAIL.getCode());
                            subContractCompleteRes.setMessage("StarLims回传的ExternalReportNo找不到对应的Report");
                            subContractCompleteRes.setReportNo(externalReportNo);
                            subContractCompleteResList.add(subContractCompleteRes);
                            break;
                        }
                        referenceReportList.addAll(reportInfoPOList);
                        ReportInfoPO oldReportInfo = reportInfoPOList.get(0);
                        if(ReportStatus.check(oldReportInfo.getReportStatus(), ReportStatus.Completed)){
                            log.info("report：{}是Completed状态,不再接受文件",externalReportNo);
                            subContractCompleteRes.setSubReportNo(fileReq.getSubReportNo());
                            subContractCompleteRes.setStatus(ResponseCode.FAIL.getCode());
                            subContractCompleteRes.setMessage("Report为Completed状态，不再接受文件！");
                            subContractCompleteRes.setReportNo(externalReportNo);
                            subContractCompleteRes.setReportId(oldReportInfo.getID());
                            subContractCompleteResList.add(subContractCompleteRes);
                            break;
                        }
                        if(ReportStatus.check(oldReportInfo.getReportStatus(), ReportStatus.Cancelled)){
                            log.info("report：{}是Cancelled状态,不再接受文件",externalReportNo);
                            subContractCompleteRes.setSubReportNo(fileReq.getSubReportNo());
                            subContractCompleteRes.setStatus(ResponseCode.FAIL.getCode());
                            subContractCompleteRes.setMessage("Report为Cancelled状态，不再接受文件！");
                            subContractCompleteRes.setReportNo(externalReportNo);
                            subContractCompleteRes.setReportId(oldReportInfo.getID());
                            subContractCompleteResList.add(subContractCompleteRes);
                            break;
                        }
                        if(ReportStatus.check(oldReportInfo.getReportStatus(), ReportStatus.Reworked)){
                            log.info("report：{}是Reworked状态,不再接受文件",externalReportNo);
                            subContractCompleteRes.setSubReportNo(fileReq.getSubReportNo());
                            subContractCompleteRes.setStatus(ResponseCode.FAIL.getCode());
                            subContractCompleteRes.setMessage("Report为Reworked状态，不再接受文件！");
                            subContractCompleteRes.setReportNo(externalReportNo);
                            subContractCompleteRes.setReportId(oldReportInfo.getID());
                            subContractCompleteResList.add(subContractCompleteRes);
                            break;
                        }
                        subContractCompleteRes.setReportId(oldReportInfo.getID());
                    }
                    // 根据FileType跟 IsDraft处理sub_report report report_file信息
                    Integer reportFileType = fileReq.getReportFileType();
                    // 根据subReportNo跟fileName判断
                    String subReportNo = fileReq.getSubReportNo();
                    String fileName = fileReq.getFileName();
                    // 维护SubReport信息
                    SubReportPO shouldUpdateSubReport = new SubReportPO();
                    //本次传递来的是否是同名文件(并且ReportFileType一致)，是：进行更新，否：进行新增
                    List<SubReportPO> subReportList = fileNameGroupReport.get(fileName);
                    if (Func.isNotEmpty(subReportList)) {
                        subReportList = subReportList.stream().filter(l -> Func.equalsSafe(l.getReportFileType(), reportFileType)).collect(Collectors.toList());
                    }
                    if (Func.isEmpty(subReportList)) {
                        if(Func.isNotEmpty(externalReportNo)){
                            shouldUpdateSubReport.setReportNo(externalReportNo);
                        }
                        shouldUpdateSubReport.setID(UUID.randomUUID().toString());
                        shouldUpdateSubReport.setGeneralOrderInstanceID(orderInfoID);
                        shouldUpdateSubReport.setObjectType(objectType);
                        shouldUpdateSubReport.setObjectNo(objectNo);
                        shouldUpdateSubReport.setSubReportNo(subReportNo);
                        shouldUpdateSubReport.setFilename(fileName);
                        shouldUpdateSubReport.setCreatedBy(finalUserName);
                        shouldUpdateSubReport.setCreatedDate(DateUtils.getNow());
                        shouldUpdateSubReport.setStatus(SubReportStatusEnum.Completed.getCode());
                        shouldUpdateSubReport.setReportVersion(1);
                        if (ReportFileType.check(reportFileType, ReportFileType.DraftPDF)) {
                            shouldUpdateSubReport.setStatus(SubReportStatusEnum.Draft.getCode());
                        }
                        shouldUpdateSubReport.setSubcontractId(subContractInfo.getId());
                        shouldUpdateSubReport.setReportFileType(reportFileType);

                        if (Func.isNotEmpty(subReportNoGroupReport.get(subReportNo))) {
                            String reportNo = subReportNoGroupReport.get(subReportNo).get(0).getReportNo();
                            if (Func.isNotEmpty(reportNo)) {
                                shouldUpdateSubReport.setReportNo(reportNo);
                                ReportDTO oldReport = reportMapper.getByReportNo(reportNo);
                                ReportFilePO newReportFile = new ReportFilePO();
                                newReportFile.setID(UUID.randomUUID().toString());
                                newReportFile.setReportNo(reportNo);
                                newReportFile.setReportID(oldReport.getId());
                                newReportFile.setReportFileType(reportFileType);
                                newReportFile.setCloudID(fileReq.getCloudId());
                                newReportFile.setFilename(fileReq.getFileName());
                                newReportFile.setFileCreatedDate(new Date());
                                newReportFile.setLanguageID(fileReq.getLanguageId());
                                newReportFile.setCreatedDate(new Date());
                                newReportFile.setModifiedDate(new Date());
                                newReportFile.setCreatedBy(finalUserName);
                                newReportFile.setModifiedBy(finalUserName);
                                newReportFile.setActiveIndicator(true);
                                newReportFile.setGenerateStatus(GenerateStatusEnums.SUCCESS.getCode());
                                shouldInsertReportFiles.add(newReportFile);
                                subContractCompleteRes.setReportId(oldReport.getId());
                                // FinalPDF 回来之后需要删除DraftPDf
                                if (ReportFileType.check(reportFileType, ReportFileType.PDF)) {
                                    // 需要删除的ReportFile
                                    ReportFileExample fileExample = new ReportFileExample();
                                    fileExample.createCriteria().andReportNoEqualTo(reportNo).andReportFileTypeEqualTo(ReportFileType.DraftPDF.getCode());
                                    List<ReportFilePO> needDeleteReportFiles = reportFileMapper.selectByExample(fileExample);
                                    if (Func.isNotEmpty(needDeleteReportFiles)) {
                                        shouldDeleteFiles.addAll(needDeleteReportFiles.stream().map(ReportFilePO::getID).collect(Collectors.toList()));
                                    }
                                    // 需要删除的SubReport
                                    SubReportExample delSubReportExample = new SubReportExample();
                                    delSubReportExample.createCriteria().andSubReportNoEqualTo(subReportNo)
                                            .andReportFileTypeEqualTo(ReportFileType.DraftPDF.getCode());
                                    List<SubReportPO> needDeleteSubReports = subReportMapper.selectByExample(delSubReportExample);
                                    if (Func.isNotEmpty(needDeleteSubReports)) {
                                        shouldDeleteSubReports.addAll(needDeleteSubReports.stream().map(SubReportPO::getID).collect(Collectors.toList()));
                                    }
                                    // 需要更新Report状态
                                    ReportInfoPO reportInfoPO = new ReportInfoPO();
                                    reportInfoPO.setID(oldReport.getId());
                                    reportInfoPO.setReportVersion(Func.toInt(oldReport.getReportVersion() + 1,1));
                                    if (Func.isNotEmpty(subcontractRequirementInfo) && Func.equals(DraftReportRequiredEnum.YES.getStatus(), subcontractRequirementInfo.getDraftReportRequired())) {
                                        reportInfoPO.setReportStatus(ReportStatus.Confirmed.getCode());
                                    } else {
                                        reportInfoPO.setReportStatus(ReportStatus.Approved.getCode());
                                    }
                                    shouldUpdateReportStatusList.add(reportInfoPO);

                                    // 需要更新Report_SubReport 关系
                                    ReportSubReportRelationshipInfoPO relationship = new ReportSubReportRelationshipInfoPO();
                                    relationship.setSubReportId(shouldUpdateSubReport.getID());
                                    relationship.setReportId(oldReport.getId());
                                    relationship.setId(UUID.randomUUID().toString());
                                    relationship.setActiveIndicator(ActiveType.Enable.getStatus().byteValue());
                                    relationship.setCreatedDate(DateUtils.now());
                                    pdfSubReportRelList.add(relationship);
                                }
                            }
                        }
                        // Word报告需要建立Report_subReport关系
                        if (ReportFileType.check(reportFileType, ReportFileType.Word)) {
                            wordSubReportRelList.add(shouldUpdateSubReport);
                        }
                        //PDF\DraftPDF 回传报告 新增 有externalReport 直接回到主报告
                        if(Func.isEmpty(subReportNoGroupReport.get(subReportNo)) && Func.isNotEmpty(externalReportNo)){
                            shouldUpdateSubReport.setReportNo(externalReportNo);
                            ReportDTO oldReport = reportMapper.getByReportNo(externalReportNo);
                            subContractCompleteRes.setReportId(oldReport.getId());
                            //非Word报告保存ReportFile
                            if(!ReportFileType.check(reportFileType, ReportFileType.Word)){
                                ReportFilePO newReportFile = new ReportFilePO();
                                newReportFile.setID(UUID.randomUUID().toString());
                                newReportFile.setReportNo(externalReportNo);
                                newReportFile.setReportID(oldReport.getId());
                                newReportFile.setReportFileType(reportFileType);
                                newReportFile.setCloudID(fileReq.getCloudId());
                                newReportFile.setFilename(fileReq.getFileName());
                                newReportFile.setFileCreatedDate(new Date());
                                newReportFile.setLanguageID(fileReq.getLanguageId());
                                newReportFile.setCreatedDate(new Date());
                                newReportFile.setModifiedDate(new Date());
                                newReportFile.setCreatedBy(finalUserName);
                                newReportFile.setModifiedBy(finalUserName);
                                newReportFile.setActiveIndicator(true);
                                newReportFile.setGenerateStatus(GenerateStatusEnums.SUCCESS.getCode());
                                String suffixes = fileReq.getCloudId().substring(fileReq.getCloudId().lastIndexOf(".") + 1);
                                newReportFile.setSuffixes(suffixes);
                                shouldInsertReportFiles.add(newReportFile);
                            }
                            if (ReportFileType.check(reportFileType, ReportFileType.PDF)) {
                                // 需要删除的ReportFile
                                ReportFileExample fileExample = new ReportFileExample();
                                fileExample.createCriteria().andReportNoEqualTo(externalReportNo).andReportFileTypeEqualTo(ReportFileType.DraftPDF.getCode());
                                List<ReportFilePO> needDeleteReportFiles = reportFileMapper.selectByExample(fileExample);
                                if (Func.isNotEmpty(needDeleteReportFiles)) {
                                    shouldDeleteFiles.addAll(needDeleteReportFiles.stream().map(ReportFilePO::getID).collect(Collectors.toList()));
                                }
                                // 需要删除的SubReport
                                SubReportExample delSubReportExample = new SubReportExample();
                                delSubReportExample.createCriteria().andSubReportNoEqualTo(subReportNo)
                                        .andReportFileTypeEqualTo(ReportFileType.DraftPDF.getCode());
                                List<SubReportPO> needDeleteSubReports = subReportMapper.selectByExample(delSubReportExample);
                                if (Func.isNotEmpty(needDeleteSubReports)) {
                                    shouldDeleteSubReports.addAll(needDeleteSubReports.stream().map(SubReportPO::getID).collect(Collectors.toList()));
                                }
                                // 需要更新Report状态
                                ReportInfoPO reportInfoPO = new ReportInfoPO();
                                reportInfoPO.setID(oldReport.getId());
                                reportInfoPO.setReportVersion(Func.toInt(oldReport.getReportVersion() + 1,1));
                                if (Func.isNotEmpty(subcontractRequirementInfo) && Func.equals(DraftReportRequiredEnum.YES.getStatus(), subcontractRequirementInfo.getDraftReportRequired())) {
                                    reportInfoPO.setReportStatus(ReportStatus.Confirmed.getCode());
                                } else {
                                    reportInfoPO.setApproverDate(new Date());
                                    reportInfoPO.setReportStatus(ReportStatus.Approved.getCode());
                                }
                                shouldUpdateReportStatusList.add(reportInfoPO);
                                // 需要更新Report_SubReport 关系
                                ReportSubReportRelationshipInfoPO relationship = new ReportSubReportRelationshipInfoPO();
                                relationship.setSubReportId(shouldUpdateSubReport.getID());
                                relationship.setReportId(oldReport.getId());
                                relationship.setId(UUID.randomUUID().toString());
                                relationship.setActiveIndicator(ActiveType.Enable.getStatus().byteValue());
                                relationship.setCreatedDate(DateUtils.now());
                                pdfSubReportRelList.add(relationship);
                            }
                            if (ReportFileType.check(reportFileType, ReportFileType.DraftPDF)){
                                // 需要更新Report状态
                                //DraftPDF 回来 report更新为approved
                                ReportInfoPO reportInfoPO = new ReportInfoPO();
                                reportInfoPO.setID(oldReport.getId());
                                reportInfoPO.setApproverDate(new Date());
                                reportInfoPO.setReportStatus(ReportStatus.Approved.getCode());
                                reportInfoPO.setReportVersion(Func.toInt(oldReport.getReportVersion() + 1,1));
                                shouldUpdateReportStatusList.add(reportInfoPO);
                                // 需要更新Report_SubReport 关系
                                ReportSubReportRelationshipInfoPO relationship = new ReportSubReportRelationshipInfoPO();
                                relationship.setSubReportId(shouldUpdateSubReport.getID());
                                relationship.setReportId(oldReport.getId());
                                relationship.setId(UUID.randomUUID().toString());
                                relationship.setActiveIndicator(ActiveType.Enable.getStatus().byteValue());
                                relationship.setCreatedDate(DateUtils.now());
                                pdfSubReportRelList.add(relationship);
                            }
                        }
                    } else {
                        shouldUpdateSubReport = subReportList.get(0);
                        if (Func.isNotEmpty(shouldUpdateSubReport.getReportNo())) {
                            ReportFilePO reportFile = new ReportFilePO();
                            reportFile.setReportNo(shouldUpdateSubReport.getReportNo());
                            reportFile.setFilename(shouldUpdateSubReport.getFilename());
                            reportFile.setCloudID(fileReq.getCloudId());
                            shouldUpdateReportFiles.add(reportFile);
                        }
                    }
                    shouldUpdateSubReport.setCloudID(fileReq.getCloudId());
                    shouldUpdateSubReport.setModifiedBy(finalUserName);
                    shouldUpdateSubReport.setModifiedDate(DateUtils.getNow());
                    if (LanguageType.check(fileReq.getLanguageId(), LanguageType.EnglishAndChinese)) {
                        shouldUpdateSubReport.setLanguageId(LanguageType.EnglishAndChinese.getLanguageId());
                    } else {
                        shouldUpdateSubReport.setLanguageId(fileReq.getLanguageId());
                    }
                    shouldUpdateSubReport.setConclusionId(conclusionId);
                    shouldUpdateSubReport.setTestMatrixMergeMode(subReportTestMatrixMergeMode);
                    shouldUpdateSubReport.setReportVersion(shouldUpdateSubReport.getReportVersion() + 1);
                    shouldUpdateSubReports.add(shouldUpdateSubReport);
                    if(Func.isEmpty(externalReportNo)){
                        subReportNos.add(shouldUpdateSubReport.getSubReportNo());
                    }
                }
                // 执行DB更新
                Boolean isSuccess = transactionTemplate.execute(trans -> {
                    if (Func.isNotEmpty(shouldUpdateSubReports)) {
                        subReportExtMapper.saveBatchSubReportInfo(shouldUpdateSubReports);
                        if (Func.isNotEmpty(orderAllDTO)) {
                            shouldUpdateSubReports.stream().forEach(subReport -> {
                                BizLogInfo bizLog = new BizLogInfo();
                                bizLog.setBu(orderAllDTO.getBUCode());
                                bizLog.setLab(orderAllDTO.getLocationCode());
                                bizLog.setOpUser(finalUserName);
                                bizLog.setBizId(orderAllDTO.getOrderNo());
                                bizLog.setOpType(subReport.getObjectType() + " Upload SubReport");
                                bizLog.setBizOpType(BizLogConstant.SUBCONTRACT_OPERATION_HOSTORY);
                                bizLog.setNewVal("SubReport ObjectNo:[" + objectNo + "],File Name:[" + subReport.getFilename() + "]");
                                bizLogClient.doSend(bizLog);
                            });
                        }
                    }

                    if (Func.isNotEmpty(shouldUpdateReportFiles)) {
                        List<String> reportNoList = shouldUpdateReportFiles.stream().map(ReportFilePO::getReportNo).distinct().collect(Collectors.toList());
                        List<ReportDTO> reportDTOList = reportMapper.getByReportNos(reportNoList);
                        Map<String, ReportDTO> reportNoMap = reportDTOList.stream().collect(Collectors.toMap(ReportDTO::getReportNo, Function.identity(), (existing, replacement) -> existing));

                        shouldUpdateReportFiles.stream().forEach(reportFile -> {
                            ReportDTO reportDTO = reportNoMap.getOrDefault(reportFile.getReportNo(), null);
                            if (Func.isNotEmpty(reportDTO) && !ReportStatus.check(reportDTO.getReportStatus(), ReportStatus.New) &&
                                    !ReportStatus.checkCategory(reportDTO.getReportStatus(), Constants.REPORT.STATUS_CATEGORY.INACTIVE)) {
                                ReportDTO updateDTO = new ReportDTO();
                                updateDTO.setSubReportReviseFlag(1);
                                updateDTO.setModifiedDate(DateUtils.getNow());
                                updateDTO.setReportNo(reportDTO.getReportNo());
                                reportMapper.updateReportReviseFlag(updateDTO);
                            }
                            // 更新条件
                            ReportFileExample example = new ReportFileExample();
                            example.createCriteria().andReportNoEqualTo(reportFile.getReportNo()).andFilenameEqualTo(reportFile.getFilename());
                            ReportFilePO reportFileRecord = new ReportFilePO();
                            reportFileRecord.setCloudID(reportFile.getCloudID());
                            reportFileRecord.setModifiedBy(finalUserName);
                            reportFileRecord.setModifiedDate(DateUtils.getNow());
                            reportFileMapper.updateByExampleSelective(reportFileRecord, example);
                        });
                    }
                    if (Func.isNotEmpty(shouldInsertReportFiles)) {
                        shouldInsertReportFiles.stream().forEach(reportFile -> {
                            reportFileMapper.insertSelective(reportFile);
                        });
                    }
                    if (Func.isNotEmpty(wordSubReportRelList)) {
                        //查询分包出去的TL所关联的Report
                        List<String> reportIds = reportMatrixRelMapper.queryReportListBySubcontract(subContractInfo.getId());
                        if (Func.isNotEmpty(reportIds)) {
                            wordSubReportRelList.stream().forEach(subReport -> {
                                if(Func.isNotEmpty(subReport.getReportNo())){
                                    ReportNoReq reportNoReq = new ReportNoReq();
                                    reportNoReq.setReportNo(subReport.getReportNo());
                                    String reportId = reportService.getReportIdByReportNo(reportNoReq).getData();
                                    ReportSubReportRelationshipInfoPO insertReportRel = new ReportSubReportRelationshipInfoPO();
                                    insertReportRel.setId(UUID.randomUUID().toString());
                                    insertReportRel.setReportId(reportId);
                                    insertReportRel.setSubReportId(subReport.getID());
                                    insertReportRel.setActiveIndicator(ActiveType.Enable.getStatus().byteValue());
                                    insertReportRel.setCreatedDate(DateUtils.now());
                                    reportSubReportRelationshipInfoMapper.insert(insertReportRel);
                                }
                                else{
                                    reportIds.stream().forEach(reportId -> {
                                        // 维护信息到tre_report_sub_report_relationship
                                        ReportSubReportRelationshipInfoPO insertReportRel = new ReportSubReportRelationshipInfoPO();
                                        insertReportRel.setId(UUID.randomUUID().toString());
                                        insertReportRel.setReportId(reportId);
                                        insertReportRel.setSubReportId(subReport.getID());
                                        insertReportRel.setActiveIndicator(ActiveType.Enable.getStatus().byteValue());
                                        insertReportRel.setCreatedDate(DateUtils.now());
                                        reportSubReportRelationshipInfoMapper.insert(insertReportRel);
                                    });
                                }
                            });
                            //只PhaseOut此次回传关联的Report且在当前分包出去TL关联的报告中
                            List<String> referenceReportIdList = referenceReportList.stream().filter(reportItem -> reportIds.contains(reportItem.getID())).map(ReportInfoPO::getID).distinct().collect(Collectors.toList());
                            if(Func.isNotEmpty(referenceReportIdList)){
                                referenceReportIdList.stream().forEach(reportId->{
                                    // 如果报告状态已经非New,需要更新报告PhaseOut标记
                                    ReportInfoPO reportInfo = reportMapper.getReportInfoByReportId(reportId);
                                    if (!ReportStatus.check(reportInfo.getReportStatus(), ReportStatus.New) &&
                                            !ReportStatus.checkCategory(reportInfo.getReportStatus(), Constants.REPORT.STATUS_CATEGORY.INACTIVE)) {
                                        ReportDTO reportDTO = new ReportDTO();
                                        reportDTO.setSubReportReviseFlag(1);
                                        reportDTO.setModifiedDate(DateUtils.getNow());
                                        reportDTO.setReportNo(reportInfo.getReportNo());
                                        reportMapper.updateReportReviseFlag(reportDTO);
                                    }
                                });
                            }
                        }
                    }
                    if (Func.isNotEmpty(pdfSubReportRelList)) {
                        pdfSubReportRelList.stream().forEach(rel -> {
                            // 维护信息到tre_report_sub_report_relationship
                            reportSubReportRelationshipInfoMapper.insert(rel);
                        });
                    }

                    if (Func.isNotEmpty(shouldDeleteFiles)) {
                        shouldDeleteFiles.stream().forEach(f -> {
                            reportFileMapper.deleteByPrimaryKey(f);
                        });
                    }
                    if (Func.isNotEmpty(shouldDeleteSubReports)) {
                        shouldDeleteSubReports.stream().forEach(s -> {
                            subReportMapper.deleteByPrimaryKey(s);
                            log.info("subContract complete delete subReport By id:{}",s);
                            ReportSubReportRelationshipInfoExample example = new ReportSubReportRelationshipInfoExample();
                            example.createCriteria().andSubReportIdEqualTo(s);
                            reportSubReportRelationshipInfoMapper.deleteByExample(example);
                        });
                    }
                    if (Func.isNotEmpty(shouldUpdateReportStatusList)) {
                        shouldUpdateReportStatusList.stream().forEach(report -> {
                            reportInfoMapper.updateByPrimaryKeySelective(report);
                            if (ReportStatus.check(report.getReportStatus(), ReportStatus.Approved)) {
                                //记录Report Status更新为Approved
                                SaveGpnStatusReq saveGpnStatusReq = new SaveGpnStatusReq();
                                saveGpnStatusReq.setOrderNo(report.getOrderNo());
                                saveGpnStatusReq.setObjectNo(report.getReportNo());
                                saveGpnStatusReq.setStatusOperation(TbStatusType.Report_Approved);
                                saveGpnStatusReq.setRemark("分包单Completed");
                                gpnStatusService.saveReportStatus(saveGpnStatusReq);
                            }
                        });
                    }
                    return true;
                });
            }
            String reportRequirement = ReportRequirementEnum.Customer_Report_Word.getCode();
            if (Func.isNotEmpty(subcontractRequirementInfo) && Func.isNotEmpty(subcontractRequirementInfo.getReportRequirement())) {
                reportRequirement = subcontractRequirementInfo.getReportRequirement();
            }
            List<ReportInfoPO> newReportList = Lists.newArrayList();
            String finalReportRequirement = reportRequirement;
            transactionTemplate.execute(trans -> {
                // 执行更新分包单完成的逻辑
                CustomResult rerunRes = subcontractStatusBizService.rerunSubContractStatus(subContractInfo.getSubContractNo(), req.isForceComplete());
                if(!rerunRes.isSuccess()){
                    trans.setRollbackOnly();
                    return false;
                }
                // 执行TestingOnly订单关单的逻辑
                BaseResponse<String> closeOrderForTestRes = ProductLineServiceHolder.getProductLineService(IOrderService.class).closeOrderForTestOnly(orderIdReq);
                log.info("closeOrderForTestRes:{}", JSON.toJSONString(closeOrderForTestRes));
                if (closeOrderForTestRes.getStatus() != ResponseCode.SUCCESS.getCode()) {
                    trans.setRollbackOnly();
                    return false;
                }
                // 更新分包单外部单号关系状态
                SubContractExternalRelationshipPO search = new SubContractExternalRelationshipPO();
                search.setExternalNo(item.getExternalNo());
                search.setSubContractNo(subcontractNo);
                BaseResponse<List<SubContractExternalRelationshipPO>> relResponse = subContractExternalRelService.getSubContractExternalRels(search);
                if (Func.isNotEmpty(relResponse) && Func.isNotEmpty(relResponse.getData())) {
                    SubContractExternalRelationshipPO subContractExternalRel = relResponse.getData().get(0);
                    subContractExternalRel.setStatus(true);
                    subContractExternalRelService.updateBySelectiveByIds(Arrays.asList(subContractExternalRel.getID()), subContractExternalRel);
                }
                // 执行生成报告的逻辑
                if (Func.equalsSafe(finalReportRequirement, ReportRequirementEnum.Customer_Report_Word.getCode()) ||
                        Func.equalsSafe(finalReportRequirement, ReportRequirementEnum.Customer_Report_PDF.getCode())
                ) {
                    if (Func.isNotEmpty(subReportNos)) {
                        // 按照 SubReport维度执行生成报告，未指定关联报告的
                        subReportNos.stream().forEach(subReport -> {
                            GenerateReportForSubcontractReq generateReportForSubcontractReq = new GenerateReportForSubcontractReq();
                            generateReportForSubcontractReq.setSubContractNo(subcontractNo);
                            generateReportForSubcontractReq.setExtReportNo(subReport);
                            generateReportForSubcontractReq.setApproveStatus(null);
                            generateReportForSubcontractReq.setUserName(finalUserName);
                            generateReportForSubcontractReq.setReportFlag(reportFlag);
                            BaseResponse<List<ReportInfoPO>> newReportRes = reportService.generateReportForSubContract(generateReportForSubcontractReq);
                            if (Func.isNotEmpty(newReportRes) && Func.isNotEmpty(newReportRes.getData())) {
                                newReportList.addAll(newReportRes.getData());
                            }
                        });
                    } else {
                        if(Func.isNotEmpty(subReports)){
                            for (ExternalFileReq subReport : subReports) {
                                GenerateReportForSubcontractReq generateReportForSubcontractReq = new GenerateReportForSubcontractReq();
                                generateReportForSubcontractReq.setSubContractNo(subcontractNo);
                                generateReportForSubcontractReq.setUserName(finalUserName);
                                generateReportForSubcontractReq.setReportFlag(reportFlag);
                                generateReportForSubcontractReq.setReferenceReportNo(subReport.getReportNo());
                                generateReportForSubcontractReq.setExtReportNo(subReport.getSubReportNo());
                                BaseResponse<List<ReportInfoPO>> newReportRes = reportService.generateReportForSubContract(generateReportForSubcontractReq);
                                if (Func.isNotEmpty(newReportRes) && Func.isNotEmpty(newReportRes.getData())) {
                                    newReportList.addAll(newReportRes.getData());
                                }
                            }
                        }else{
                            GenerateReportForSubcontractReq generateReportForSubcontractReq = new GenerateReportForSubcontractReq();
                            generateReportForSubcontractReq.setSubContractNo(subcontractNo);
                            generateReportForSubcontractReq.setUserName(finalUserName);
                            generateReportForSubcontractReq.setReportFlag(reportFlag);
                            BaseResponse<List<ReportInfoPO>> newReportRes = reportService.generateReportForSubContract(generateReportForSubcontractReq);
                            if (Func.isNotEmpty(newReportRes) && Func.isNotEmpty(newReportRes.getData())) {
                                newReportList.addAll(newReportRes.getData());
                            }
                        }
                    }
                    reportService.delReportForSubContract(subcontractNo);
                }
                return true;
            });
            subContractCompleteRes.setStatus(ResponseCode.SUCCESS.getCode());
            subContractCompleteRes.setMessage(ResponseCode.SUCCESS.getMessage());
            AtomicBoolean hasReport = new AtomicBoolean(false);
            // PDF 场景返回报告号
            if (Func.equalsSafe(reportRequirement, ReportRequirementEnum.Customer_Report_Word.getCode()) ||
                    Func.equalsSafe(reportRequirement, ReportRequirementEnum.Customer_Report_PDF.getCode())
            ) {
                if (Func.isNotEmpty(req) && Func.isNotEmpty(req.getCompleteItems())) {
                    req.getCompleteItems().forEach(reqItem -> {
                        if (Func.isNotEmpty(reqItem.getSubReports())) {
                            Set<String> subReportNoSets = reqItem.getSubReports().stream().map(sub -> sub.getSubReportNo()).collect(Collectors.toSet());
                            subReportNoSets.stream().forEach(subReportNo -> {
                                ReportSubReportDTO reportSubReport = reportMapper.getReportBySubReportNoForPdf(subReportNo);
                                if (Func.isNotEmpty(reportSubReport)) {
                                    subContractCompleteRes.setReportNo(reportSubReport.getActualReportNo());
                                    subContractCompleteRes.setReportId(reportSubReport.getReportId());
                                    // PDF 报告记录
                                    subContractCompleteResList.add(subContractCompleteRes);
                                    hasReport.set(true);
                                }
                            });
                        }
                    });
                }
            }
            // Word 场景 如果订单下只有一份有效的报告
            if (Func.equalsSafe(reportRequirement, ReportRequirementEnum.Sub_Report_Word.getCode())) {
                String orderNo = orderInfo.getOrderNo();
                List<ReportInfoPO> reportList = reportMapper.getReportListByOrderNo(orderNo);
                reportList = reportList.stream().filter(report -> ReportFlagEnums.check(report.getReportFlag(), ReportFlagEnums.REPORT)
                        && !ReportStatus.checkCategory(report.getReportStatus(), Constants.REPORT.STATUS_CATEGORY.INACTIVE))
                        .collect(Collectors.toList());
                if (Func.isNotEmpty(reportList) && reportList.size() == 1) {
                    subContractCompleteRes.setReportNo(reportList.get(0).getActualReportNo());
                    subContractCompleteRes.setReportId(reportList.get(0).getID());
                    subContractCompleteResList.add(subContractCompleteRes);
                    hasReport.set(true);
                }
            }
            if (!hasReport.get()) {
                subContractCompleteRes.setStatus(ResponseCode.SUCCESS.getCode());
                subContractCompleteRes.setMessage(ResponseCode.SUCCESS.getMessage());
                // PDF 报告记录
                subContractCompleteResList.add(subContractCompleteRes);
            }
        });
        customResult.setData(subContractCompleteResList);
        return customResult;
    }

    /**
     * 分包单 Cancel逻辑
     *
     * @return
     */
    @Override
    public CustomResult cancel(GpnQuerySubContractReq req) {
        CustomResult result = new CustomResult();
        if (StringUtils.isBlank(req.getSubContractId())) {
            result.setSuccess(false);
            return result;
        }
        SubContractPO subContractPO = subContractMapper.selectByPrimaryKey(req.getSubContractId());
        Integer oldStatus = subContractPO.getStatus();
        Integer subcontractOrder = subContractPO.getSubContractOrder();
        //分包单允许删除逻辑
        //订单状态不等于 Closed、Pending、Cancelled、Completed
        //分包单状态 New 、Testing（并且分包单类型为Slim、StarLims、Dml）
        if (!SubContractStatus.check(oldStatus, SubContractStatus.NEW, SubContractStatus.TESTING)) {
            result.setMsg("当前分包单状态不允许执行分包单cancel");
            result.setSuccess(false);
            return result;
        }
        if (SubContractStatus.check(oldStatus, SubContractStatus.TESTING) &&
                !(SubContractType.check(subcontractOrder, SubContractType.ToSlim) ||
                        SubContractType.check(subcontractOrder, SubContractType.ToStarLims) ||
                        SubContractType.check(subcontractOrder, SubContractType.ToDML))) {
            result.setMsg("当前分包单类型不允许执行分包单cancel");
            result.setSuccess(false);
            return result;

        }
        OrderIdReq orderIdReq = new OrderIdReq();
        orderIdReq.setOrderNo(subContractPO.getOrderNo());
        orderIdReq.setProductLineCode(ProductLineContextHolder.getProductLineCode());
        BaseResponse<OrderAllDTO> orderForPe = orderFacade.getOrderForPe(orderIdReq);
        if (Func.isEmpty(orderForPe) || Func.isEmpty(orderForPe.getData())) {
            result.setMsg("订单信息查询异常");
            result.setSuccess(false);
            return result;
        }
        Integer orderStatus = orderForPe.getData().getOrderStatus();
        if (com.sgs.preorder.facade.model.enums.OrderStatus.checkStatus(orderStatus,
                com.sgs.preorder.facade.model.enums.OrderStatus.Closed,
                com.sgs.preorder.facade.model.enums.OrderStatus.Pending,
                com.sgs.preorder.facade.model.enums.OrderStatus.Cancelled,
                com.sgs.preorder.facade.model.enums.OrderStatus.Completed)) {
            result.setMsg("当前订单状态不允许执行分包单cancel");
            result.setSuccess(false);
            return result;
        }
        //内部分包校验执行开始
        //1 先校验当前order的preorder端的状态
        String subContractNo = subContractPO.getSubContractNo();
        Integer subContractOrder = subContractPO.getSubContractOrder();
        CopyOrderUtils.setSourceProductLineCode(ProductLineContextHolder.getProductLineCode());//分包方
        String subContractLabCode = subContractPO.getSubContractLabCode();
        LabDTO labDTO = frameWorkClient.queryLabByLabCode(subContractLabCode);
        if (Func.isEmpty(labDTO)) {
            result.setMsg("查询Lab信息失败，LabCode: " + subContractLabCode);
            result.setSuccess(false);
            return result;
        }
        CopyOrderUtils.setTargetProductLineCode(labDTO.getProductLineCode());//接包方
//        String[] s = subContractLabCode.split(" ");
//        CopyOrderUtils.setTargetProductLineCode(s[1]);//接包方
        //subContractOrder=1 是内部分包
        UserInfo userInfo = tokenClient.getUser();
        if (userInfo == null) {
            throw new RuntimeException("Get User Fail!");
        }
        //根据subcontractNo找slimjob
        SubContractExternalRelationshipPO searchPO = new SubContractExternalRelationshipPO();
        searchPO.setSubContractNo(subContractNo);
        BaseResponse<List<SubContractExternalRelationshipPO>> subContractExternalRelationship = subContractExternalRelService.getSubContractExternalRels(searchPO);
        SubContractExternalRelationshipPO subContractSlimJob = null;
        if (subContractExternalRelationship.isSuccess() && Func.isNotEmpty(subContractExternalRelationship.getData()) && subContractExternalRelationship.getData().size() > 0) {
            subContractSlimJob = subContractExternalRelationship.getData().get(0);
        }
        if (subContractOrder != null && subContractOrder.compareTo(1) == 0) {
            //校验当前分包单的状态

            if (subContractSlimJob == null) {
                log.info("subContractNo：{} cancel操作，确定是内部分包，但是找不到对应的slimJobNo(orderNo)数据", subContractNo);
                throw new RuntimeException("Query subContract info fail!");
            }
            //找到内部分包对应的orderNo
           /* OrderIdReq orderIdReq = new OrderIdReq();
            orderIdReq.setOrderNo(subContractSlimJob.getExternalNo());
            orderIdReq.setProductLineCode(ProductLineContextHolder.getProductLineCode());
            BaseResponse<Integer> orderStatusByOrderNo = orderFacade.getOrderStatusByOrderNo(orderIdReq);
            Integer integer = orderStatusByOrderNo.getData();
            if(integer!=null && !Lists.newArrayList(1,3,8).contains(integer.intValue())){
                throw new RuntimeException("Order Status is not New、Confirm、Testing");
            }*/

            //请求preorder 准备cancel order
            CopyOrderUtils.changeDbToTarget();
            cancelSubOrder(subContractPO, subContractSlimJob.getExternalNo(), userInfo);
        }
        //删除tb_subcontract_external_relationship
        if (Func.isNotEmpty(subContractSlimJob)) {
            subContractExtMapper.delSubContractSlimJobId(subContractSlimJob.getID());
        }
        //内部分包校验执行结束↑
        CopyOrderUtils.changeDbToSource();
        // 将SubContract状态改为Cancel
        subContractPO.setStatus(SubContractStatus.CANCELLED.getCode());
        subContractExtMapper.saveOrUpdate(subContractPO);
        //保存bizLog，subcontract状态变化
        String userLabCode = userInfo.getCurrentLabCode();
        if (Func.isNotEmpty(userLabCode)) {
            LabDTO userLabDTO = frameWorkClient.queryLabByLabCode(userLabCode);
            try {
                BizLogInfo bizLog = new BizLogInfo();
                bizLog.setBizOpType(BizLogConstant.SUBCONTRACT_STATUS_CHANGE_HISTORY);
                bizLog.setLab(userLabDTO.getLocationCode());
                bizLog.setBu(userLabDTO.getProductLineCode());
                bizLog.setOpUser(userInfo.getRegionAccount());
                bizLog.setBizId(subContractPO.getSubContractNo());
                bizLog.setOpType(OperationTypeEnums.CancelSubcontract.getValue());
                bizLog.setNewVal(SubcontractStatus.Cancelled.getCode());
                bizLog.setOriginalVal(oldStatus);
                log.info("[{}],bizlog:Subcontract_Status_Change_log,Cancelled,{}", subContractPO.getSubContractNo(), JSON.toJSONString(bizLog));
                bizLogClient.doSend(bizLog);
            } catch (Exception e) {
                log.error("subContract status log err:{}", e);
            }
            //保存bizLog history
            try {
                BizLogInfo bizLog = new BizLogInfo();
                bizLog.setBizOpType(BizLogConstant.SUBCONTRACT_OPERATION_HOSTORY);
                bizLog.setLab(StringUtils.isNotBlank(orderForPe.getData().getLocationCode()) ? orderForPe.getData().getLocationCode() : userLabDTO.getLocationCode());
                bizLog.setBu(StringUtils.isNotBlank(orderForPe.getData().getBUCode()) ? orderForPe.getData().getBUCode() : userLabDTO.getProductLineCode());
                bizLog.setOpUser(userInfo.getRegionAccount());
                bizLog.setBizId(subContractPO.getOrderNo());
                bizLog.setOpType(OperationTypeEnums.CancelSubcontract.getValue());
                bizLog.setNewVal(SubcontractStatus.Cancelled.message());
                if (Func.isNotEmpty(oldStatus)) {
                    bizLog.setOriginalVal(SubcontractStatus.getCode(oldStatus).message());
                }
                log.info("[{}],bizlog:SUBCONTRACT_OPERATION_HOSTORY,Cancelled,{}", subContractPO.getSubContractNo(), JSON.toJSONString(bizLog));
                bizLogClient.doSend(bizLog);
            } catch (Exception e) {
                log.error("subContract cancel history bizlog err:{}", e);
            }
        }

        // 删除tb_sub_contract_test_line_mapping相关数据
        SubContractTestLineMappingExample subContractTestLineMappingExample = new SubContractTestLineMappingExample();
        subContractTestLineMappingExample.createCriteria().andSubContractIDEqualTo(req.getSubContractId());
        // 取消tb_test_line_instance subContract 状态
        List<SubContractTestLineMappingPO> subContractTestLineMappingS = subContractTestLineMappingMapper.selectByExample(subContractTestLineMappingExample);
        List<String> testLines = Lists.newArrayList();
        if (Func.isNotEmpty(subContractTestLineMappingS)) {
            testLines = subContractTestLineMappingS.stream().map(m -> m.getTestLineInstanceID()).collect(Collectors.toList());
            testLineInstanceExtMapper.updateTestLineTypeCancelBatch(testLines, TestLineType.SubContractOrder.getType());
            testLineInstanceExtMapper.updateTestLineStatusBatch(testLines, Typing.getStatus());
        }
        subContractTestLineMappingMapper.deleteByExample(subContractTestLineMappingExample);
        //DIG-3337  UPDATE SLIM临表的syncStatus=0 这一块的整体功能未完成，暂时先注释
//        slimSubContractExtMapper.updateSlimSubcontractSyncStatus(subContractPO.getSubContractNo());

        // 取消tb_status_log的记录
//        StatusLogReq statusLogReq = new StatusLogReq();
//        statusLogReq.setOrderNo(subContractPO.getOrderNo());
//        statusLogReq.setObjectType(ObjectType.SubContract.getCode());
//        statusLogReq.setObjectId(req.getSubContractId());
//        statusLogService.unPending(statusLogReq);

        SubContractPO subContract = subContractMapper.selectByPrimaryKey(subContractPO.getID());
        TodoStatusReq todoStatusReq = new TodoStatusReq();
        todoStatusReq.setObjectId(subContract.getID());
        todoStatusReq.setObjectNo(subContract.getSubContractNo());
        todoStatusReq.setStatus(TodoStatus.Canceled.getCode());
        todoStatusReq.setSgsToken(tokenClient.getToken());
        toDoListFacade.updateTodoStatus(todoStatusReq);
        BizLogHelper.setValue(subContractPO.getOrderNo(), subContract.getSubContractNo());
        BizLogHelper.setLabCode(orderForPe.getData().getBUCode(), orderForPe.getData().getLocationCode());
        //记录operationHistory
        //保存operationHistory
        InsertOperationHistoryReq insertOperationHistoryReq = new InsertOperationHistoryReq();
        insertOperationHistoryReq.setObjectId(req.getSubContractId());
        insertOperationHistoryReq.setObjectNo(subContractNo);
        insertOperationHistoryReq.setOperationType(OperationTypeEnums.CancelSubcontract.getStatus());
        insertOperationHistoryReq.setReasonType(req.getReasonTypeInfo());
        insertOperationHistoryReq.setRemark(req.getRemark());
        insertOperationHistoryReq.setToken(tokenClient.getToken());
        BaseResponse baseResponse = operationHistoryFacade.operationHistorySave(insertOperationHistoryReq);
        result.setSuccess(true);
        return result;
    }

    /**
     * 更新 TestLine Status
     *
     * @param orderId
     * @param orderNo
     * @param testLineIds
     */
    public void updateTestLineStatus(String orderId, String orderNo, List<String> testLineIds) {
        if (testLineIds == null || testLineIds.isEmpty()) {
            return;
        }
        boolean isValidTestLine = false;
        int count = 0;
        List<TestLineInstancePO> updateStatus = Lists.newArrayList();
        List<TestLineInstancePO> testLines = testLineMapper.getTestLineByOrderNo(orderNo);
        int testLineCount = testLines.size();
        for (TestLineInstancePO testLine : testLines) {
            // 过滤掉DR的Test Line
            if (TestLineStatus.check(testLine.getTestLineStatus(), TestLineStatus.DR)) {
                testLineCount--;
                continue;
            }
            if (testLineIds.contains(testLine.getID())) {
                testLine.setTestLineStatus(TestLineStatus.Completed.getStatus());
                updateStatus.add(testLine);
            }
            Integer testLineStatus = testLine.getTestLineStatus();
            if (TestLineStatus.check(testLineStatus, TestLineStatus.Cancelled, TestLineStatus.NC)) {
                count++;
                continue;
            }
            isValidTestLine = true;
            Boolean isPretreatment = testLine.getIsPretreatment();
            if ((isPretreatment != null && isPretreatment.booleanValue()) || TestLineStatus.check(testLineStatus, TestLineStatus.Completed)) {
                count++;
            }
        }
        if (!updateStatus.isEmpty()) {
            testLineMapper.batchUpdateTestLineStatus(updateStatus);
        }

        // 所有的testline都为Completed或Subcontracted或cancelled状态   且 至少有一条有效的TL
        if (isValidTestLine && count == testLineCount) {
            GeneralOrderInstanceInfoPO order = new GeneralOrderInstanceInfoPO();
            order.setID(orderId);
            order.setOrderStatus(OrderStatus.Reporting.getStatus());
            order.setModifiedBy("System");
            order.setModifiedDate(DateUtils.getNow());
            orderMapper.updateOrderStatus(order);

            OrderNoReq orderNoReq = new OrderNoReq();
            orderNoReq.setUserName("System");
            orderNoReq.setOrderNo(orderNo);
            orderNoReq.setProductLineCode(ProductLineContextHolder.getProductLineCode());
            gpoStatusFacade.orderReporting(orderNoReq);
        }
    }


    /**
     * 建立分包单跟外部系统单号关系
     * 如果绑定的分包单发生变化需要邮件通知处理
     *
     * @param externalNo
     * @param subContractNo
     * @return
     */
    private boolean createAssociation(String externalNo, String subContractNo, Integer subcontractType) {
        if (Func.isEmpty(externalNo)) {
            return true;
        }
        //Slim场景
        if (SubContractType.check(subcontractType, SubContractType.ToSlim)) {
            return createAssociationForSLim(externalNo, subContractNo, subcontractType);
        }
        // 其它场景
        SubContractExternalRelationshipPO searchPO = new SubContractExternalRelationshipPO();
        searchPO.setExternalNo(externalNo);
        List<SubContractExternalRelationshipPO> subContractExtRelList = subContractExternalRelService.getSubContractExternalRels(searchPO).getData();
        if (Func.isEmpty(subContractExtRelList)) {
            insertSubContractExternalRel(externalNo, subContractNo, subcontractType);
        }
        return true;
    }

    private boolean createAssociationForSLim(String externalNo, String subContractNo, Integer subcontractType) {
        // 根据slimJobNo查询关联关系
        SubContractExternalRelationshipPO searchPO = new SubContractExternalRelationshipPO();
        searchPO.setExternalNo(externalNo);
        searchPO.setSubContractType(subcontractType);
        List<SubContractExternalRelationshipPO> subContractExtRelList = subContractExternalRelService.getSubContractExternalRels(searchPO).getData();
        Boolean association = true;
        // 如果关联关系不存在,绑定关系
        if (CollectionUtils.isEmpty(subContractExtRelList)) {
            insertSubContractExternalRel(externalNo, subContractNo, SubContractType.ToSlim.getType());
        } else {
            // 判断绑定的分包单是否发生变化
            List<SubContractExternalRelationshipPO> currentRelList = subContractExtRelList.stream().filter(po ->
                    StringUtils.equalsIgnoreCase(po.getExternalNo(), externalNo) &&
                            StringUtils.equalsIgnoreCase(po.getSubContractNo(), subContractNo)).collect(Collectors.toList());
            if (Func.isEmpty(currentRelList)) {
                // 发送邮件参数
                String templateCode;
                String subject;
                String labCode = null;
                List<String> mailToList = Lists.newArrayList();
                Map<String, Object> emailParamMap = Maps.newHashMap();
                // 分包单发生变化
                // 判断报告状态，是否存在complete状态
                String oldSubcontractNo = subContractExtRelList.get(0).getSubContractNo();
                SubContractInfo subContractInfo = subContractExtMapper.getSubContractInfo(oldSubcontractNo);
                String orderNo = "";
                emailParamMap.put("oldSubcontractNo", oldSubcontractNo);
                emailParamMap.put("subcontractNo", subContractNo);
                emailParamMap.put("slimJobNo", externalNo);
                if (Func.isNotEmpty(subContractInfo)) {
                    orderNo = subContractInfo.getOrderNo();
                    emailParamMap.put("orderNo", orderNo);
                    OrderIdReq orderIdReq = new OrderIdReq();
                    orderIdReq.setOrderNo(orderNo);
                    BaseResponse<OrderAllDTO> orderRes = orderFacade.getOrderForPe(orderIdReq);
                    if (Func.isNotEmpty(orderRes) && Func.isNotEmpty(orderRes.getData())) {
                        mailToList.add(orderRes.getData().getcSEmail());
                        emailParamMap.put("csName", orderRes.getData().getcSName());
                        labCode = orderRes.getData().getLabCode();
                    }
                }
                SubReportPO subReportPO = subReportExtMapper.checkReportStatusBySubReport(externalNo);
                if (Func.isEmpty(subReportPO)) {
                    // 执行subReportRevise
                    List<SubReportListRsp> subReportList = subReportExtMapper.getSubReportByObjectNo(externalNo);
                    if (Func.isNotEmpty(subReportList)) {
                        String finalOrderNo = orderNo;
                        subReportList.stream().forEach(subReport -> {
                            ReviseSubReportReq reviseSubReportReq = new ReviseSubReportReq();
                            reviseSubReportReq.setSubReportNo(subReport.getSubReportNo());
                            reviseSubReportReq.setObjectNo(subReport.getObjectNo());
                            reviseSubReportReq.setOrderNo(finalOrderNo);
                            BaseResponse reviseRes = subReportService.reviseSubReport(reviseSubReportReq);
                            log.info("reviseRes: " + JSON.toJSONString(reviseRes));
                        });
                    }
                    // 保存新的关联关系
                    insertSubContractExternalRel(externalNo, subContractNo, SubContractType.ToSlim.getType());
                    // 更新现有的关系
                    SubContractExternalRelationshipPO subContractExternalDelPO = new SubContractExternalRelationshipPO();
                    subContractExternalDelPO.setExternalNo(externalNo);
                    subContractExternalDelPO.setSubContractNo(oldSubcontractNo);
                    subContractExternalRelService.delSubContractExternalRel(subContractExternalDelPO);
                    // 邮件通知Slim绑定错误我们修正了关系
                    templateCode = "GPO_SlimSubcontractRelUpdateSuccess";
                    subject = "[GPO]-" + orderNo + "-" + oldSubcontractNo + "-" + externalNo + " Update Relationship";
                } else {
                    // 处理失败，邮件通知CS 由于Subcontract已经Completed无法修正Slim Job最新关系
                    templateCode = "GPO_SlimSubcontractRelUpdateFail";
                    subject = "[GPO]-" + orderNo + "-" + oldSubcontractNo + "-" + externalNo + " Update Relationship Failed";
                    association = false;
                }
                if (Func.isNotEmpty(mailToList)) {
                    EmailAutoSendDTO emailAutoSendDTO = new EmailAutoSendDTO();
                    emailAutoSendDTO.setMailSubject(subject);
                    emailAutoSendDTO.setTemplateCode(templateCode);
                    emailAutoSendDTO.setMailTo(mailToList);
                    emailAutoSendDTO.setMailCc(new ArrayList<>());
                    emailAutoSendDTO.setLabCode(labCode);
                    emailAutoSendDTO.setTemplateVariables(emailParamMap);
                    //保存日志
                    SystemLog systemLog = new SystemLog();
                    systemLog.setObjectType("slimJobNoUpdateRelationship");
                    systemLog.setObjectNo(orderNo);
                    systemLog.setProductLineCode(ProductLineContextHolder.getProductLineCode());
                    systemLog.setType(SystemLogType.API.getType());
                    systemLog.setRemark("SLim Subcontract 绑定关系校验");
                    systemLog.setCreateBy("System");
                    systemLog.setLocationCode(labCode);
                    systemLog.setOperationType(" SlimJobNo Update Relationship Email");
                    sendEmailService.sendEmail(emailAutoSendDTO, systemLog);
                }
            } else {
                // 分包单没有发生变化 如果Type = 9 需要覆盖，且把Type更新为0
                currentRelList = currentRelList.stream().filter(po -> (po.getSubContractType() != null && po.getSubContractType() == SubContractType.None.getType())).collect(Collectors.toList());
                List<String> ids = currentRelList.stream().map(SubContractExternalRelationshipPO::getID).collect(Collectors.toList());
                if (Func.isNotEmpty(ids)) {
                    SubContractExternalRelationshipPO subContractExternalRelationshipPO = new SubContractExternalRelationshipPO();
                    subContractExternalRelationshipPO.setSubContractType(SubContractType.ToSlim.getType());
                    subContractExternalRelService.updateBySelectiveByIds(ids, subContractExternalRelationshipPO);
                }
            }
        }
        return association;
    }

    private int insertSubContractExternalRel(String externalNo, String subContractNo, Integer subContractType) {
        SubContractExternalRelationshipPO newSubContractExternalRelPO = new SubContractExternalRelationshipPO();
        newSubContractExternalRelPO.setID(UUID.randomUUID().toString());
        newSubContractExternalRelPO.setExternalNo(externalNo);
        newSubContractExternalRelPO.setSubContractNo(subContractNo);
        newSubContractExternalRelPO.setStatus(false);
        newSubContractExternalRelPO.setCreatedBy("system");
        newSubContractExternalRelPO.setCreatedDate(DateUtils.getNow());
        newSubContractExternalRelPO.setSubContractType(subContractType);
        log.info("添加tb_subcontract_external_relationship表,data = {}", JSON.toJSONString(newSubContractExternalRelPO));
        return subContractExternalRelService.insert(newSubContractExternalRelPO);
    }


    private CustomResult<Map<String, Object>> validateSaveSubcontract(SaveSubContractReq req, List<String> needDelTestLine, Boolean testOnly) {
        CustomResult result = new CustomResult();
        // 存储查询的业务对象
        Map<String, Object> subcontractDTOMaps = Maps.newHashMap();

        String subContractId = req.getSubContractId();
        String testLineInstanceIds = req.getTestLineInstanceIds();
        String orderNo = req.getOrderNo();
        String slimJobNo = req.getSlimJobNo();

        OrderIdReq orderIdReq = new OrderIdReq();
        orderIdReq.setProductLineCode(ProductLineContextHolder.getProductLineCode());
        orderIdReq.setOrderNo(orderNo);
        OrderAllDTO oldOrder = orderFacade.getOrderForPe(orderIdReq).getData();
        if (Func.isEmpty(oldOrder)) {
            return result.fail("请检查订单(" + orderNo + ")信息有效性");
        }
        GeneralOrderInstanceDTO generalOrderInstanceDTO =ProductLineServiceHolder.getProductLineService(IOrderService.class).getOrderByNo(orderNo);
        if (Func.isEmpty(generalOrderInstanceDTO)) {
            return result.fail("请检查订单(" + orderNo + ")信息有效性");
        }
        subcontractDTOMaps.put("gpoOrder", oldOrder);
        // translation 生成的订单
        //获取tre_pp_test_line_relationship 去除订单下所有的PP TL 关系
        PPTestLineRelationshipInfoExample example = new PPTestLineRelationshipInfoExample();
        example.createCriteria().andGeneralOrderInstanceIDEqualTo(generalOrderInstanceDTO.getId());
        List<PPTestLineRelationshipInfoPO> ppTestLineRelationshipInfoPOList = ppTestLineRelationshipInfoMapper.selectByExampleWithBLOBs(example);
        Map<String,List<String>> csppRel = this.findCSPPOpen(ppTestLineRelationshipInfoPOList);
        Boolean isTranslation = OrderOperationType.check(oldOrder.getOperationType(), OrderOperationType.TranslationReport);
        //Create Subcontract的时候，校验订单完成了confirm 操作（用Confirm Date 不为空判断）
        if (Func.isEmpty(oldOrder.getOrderConfirmDate())) {
            return result.fail("please confirm order first!");
        }
        if (Func.isEmpty(testLineInstanceIds) && !isTranslation) {
            return result.fail("请选择需要分包的测试项目！");
        }
        Integer operationMode = oldOrder.getOperationMode();
        if (Func.isNotEmpty(operationMode) && Func.equals(operationMode, OperationModeEnums.TESTING_ONLY.getStatus()) && !testOnly) {
            return result.fail("Testing Only 类型订单对应的分包单 Report Requirement 只能为Testing Only！");
        }
        GeneralOrderInstanceDTO otsOrder = generalOrderInstanceMapper.getByNo(orderNo);
        subcontractDTOMaps.put("otsOrder", otsOrder);

        List<String> ids = JSONArray.parseArray(testLineInstanceIds, String.class);
        // Check TestLine 有效性
        List<TestLineInstancePO> testLineInstancePOS = Lists.newArrayList();
        if (Func.isNotEmpty(ids)) {
            TestLineInstanceExample testLineInstanceExample = new TestLineInstanceExample();
            testLineInstanceExample.createCriteria().andIDIn(ids);
            testLineInstancePOS = testLineInstanceMapper.selectByExample(testLineInstanceExample);
        }
        if (ids.size() != testLineInstancePOS.size() && !isTranslation) {
            return result.fail("请检查TestLine的有效性！");
        }
        // 校验选中的TL 是否有展开的CSPP 如果有 校验被展开的CSPP的TL是否全部被选中
        if(Func.isNotEmpty(testLineInstancePOS)) {
            //过滤出cspp的TL
            List<TestLineInstancePO> csppOpened = testLineInstancePOS.stream().filter(e -> TestLineType.check(e.getTestLineType(),TestLineType.CSPP_OPEN)).collect(Collectors.toList());
            if(Func.isNotEmpty(csppOpened)){
                //存在被展开的CSPP 找到被CSPP展开的TL
                for (TestLineInstancePO testLine : csppOpened){
                    List<String> csppOpenedTL = csppRel.get(testLine.getID());
                    if(Func.isNotEmpty(csppOpenedTL)){
                        TestLineInstanceExample testLineInstanceExample = new TestLineInstanceExample();
                        testLineInstanceExample.createCriteria().andIDIn(csppOpenedTL);
                        List<TestLineInstancePO> testLineList = testLineInstanceMapper.selectByExample(testLineInstanceExample);
                        if(Func.isNotEmpty(testLineList)){
                            List<TestLineInstancePO> noCancelNATL = testLineList.stream().filter(e ->!com.sgs.framework.model.enums.TestLineStatus.check(e.getTestLineStatus(),com.sgs.framework.model.enums.TestLineStatus.Cancelled, com.sgs.framework.model.enums.TestLineStatus.NA)).collect(Collectors.toList());
                            if(Func.isEmpty(noCancelNATL)){
                                return result.fail("cspp展开下的TL已经全部Cancel Or NA, 不允许添加到分包单！");
                            }
                            for(String id : csppOpenedTL){
                                if(!ids.contains(id)){
                                    TestLineInstancePO testLinePO = testLineInstanceMapper.selectByPrimaryKey(id);
                                    if(Func.isNotEmpty(testLinePO) && !com.sgs.framework.model.enums.TestLineStatus.check(testLinePO.getTestLineStatus(),com.sgs.framework.model.enums.TestLineStatus.Cancelled, com.sgs.framework.model.enums.TestLineStatus.NA)){
                                        return result.fail("cspp展开下的TL没有完全选中！");
                                    }
                                }
                            }
                        }

                    }
                }

            }
        }
        // Check Assign Sample
        List<TestMatrixPO> testMatrixPOS = Lists.newArrayList();
        if (Func.isNotEmpty(ids)) {
            testMatrixPOS = testMatrixMapper.getTestMatrixListByTestLineInstanceIds(ids);
        }
        if (Func.isNotEmpty(testMatrixPOS)) {
            //增加校验 matrix必须有confirm date
            List<TestMatrixPO> noConfirmMatrixList = testMatrixPOS.stream().filter(e -> Func.isEmpty(e.getMatrixConfirmDate())).collect(Collectors.toList());
            if(Func.isNotEmpty(noConfirmMatrixList)){
                return result.fail("Please confirm matrix before save Subcontract!");
            }
            List<String> assignSampleIds = testMatrixPOS.stream().map(TestMatrixPO::getTestLineInstanceID).collect(Collectors.toList());
            List<String> notAssignSampleIds = ids.stream().filter(id -> {
                return !assignSampleIds.contains(id);
            }).collect(Collectors.toList());
            if (Func.isNotEmpty(notAssignSampleIds)) {
                List<Integer> notAssignTestLineIds = testLineInstancePOS.stream()
                        .filter(testLineInstancePO -> {
                            return notAssignSampleIds.contains(testLineInstancePO.getID());
                        })
                        .map(TestLineInstancePO::getTestLineID).collect(Collectors.toList());
                return result.fail("TestLines " + Func.toStr(notAssignTestLineIds) + " not assign sample!");
            }
        } else if (!isTranslation) {
            return result.fail("请先（Assign Sample）后再进行分包！");
        }
        List<SubContractTestLineMappingPO> subContractTestLineMappingPOS = Lists.newArrayList();
        if (Func.isNotEmpty(ids)) {
            SubContractTestLineMappingExample subContractTestLineMappingExample = new SubContractTestLineMappingExample();
            subContractTestLineMappingExample.createCriteria().andTestLineInstanceIDIn(ids);
            subContractTestLineMappingPOS = subContractTestLineMappingMapper.selectByExample(subContractTestLineMappingExample);
        }

        if (StringUtils.isBlank(subContractId)) {
            if (CollectionUtils.isNotEmpty(subContractTestLineMappingPOS)) {
                List<String> subTLIDList = subContractTestLineMappingPOS.stream().map(sub -> sub.getTestLineInstanceID()).collect(Collectors.toList());
                List<TestLineInstancePO> subTLList = testLineInstancePOS.stream().filter(tl -> subTLIDList.contains(tl.getID())).collect(Collectors.toList());
                List<Integer> list = subTLList.stream().map(tl -> tl.getTestLineID()).collect(Collectors.toList());
                String join = StringUtils.join(list, ",");
                throw new RuntimeException("TestLine " + join + " already been SunContracted");
            }
        } else {
            //判断slimJobNo是否已经bind
            SubContractPO subContractPO = subContractMapper.selectByPrimaryKey(subContractId);
            subcontractDTOMaps.put("subContract", subContractPO);
            // translation 生成的订单 创建分包单可以不选择TL
            if (isTranslation) {
                result.setData(subcontractDTOMaps);
                result.setSuccess(true);
                return result;
            }
            List<SubContractTestLineMappingPO> otherSub = subContractTestLineMappingPOS.stream().filter(sub -> !sub.getSubContractID().equalsIgnoreCase(subContractId)).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(otherSub)) {
                List<String> subTLIDList = otherSub.stream().map(sub -> sub.getTestLineInstanceID()).collect(Collectors.toList());
                List<TestLineInstancePO> subTLList = testLineInstancePOS.stream().filter(tl -> subTLIDList.contains(tl.getID())).collect(Collectors.toList());
                List<Integer> list = subTLList.stream().map(tl -> tl.getTestLineID()).collect(Collectors.toList());
                String join = StringUtils.join(list, ",");
                throw new RuntimeException(join + " already been SubContracted");
            }
            if (Func.isNotEmpty(subContractId) && Func.isNotEmpty(slimJobNo)) {
                SubContractExternalRelationshipPO searchExternal = new SubContractExternalRelationshipPO();
                searchExternal.setExternalNo(slimJobNo);
                BaseResponse<List<SubContractExternalRelationshipPO>> subContractExternalRelationshipPOList = subContractExternalRelService.getSubContractExternalRels(searchExternal);
                if (Func.isNotEmpty(subContractExternalRelationshipPOList.getData()) && !subContractExternalRelationshipPOList.getData().get(0).getSubContractNo().equalsIgnoreCase(subContractPO.getSubContractNo())) {
                    return result.fail("OrderNo:" + slimJobNo + " has been bound");
                }
            }
//            if(Func.isEmpty(slimJobNo) && !SubContractStatus.check(subContractPO.getStatus(),SubContractStatus.NEW)){
//                return result.fail("ReferenceNo 不允许为空");
//            }
            SubContractTestLineMappingExample subContractTestLineMappingExample1 = new SubContractTestLineMappingExample();
            subContractTestLineMappingExample1.createCriteria().andSubContractIDEqualTo(subContractId);
            List<SubContractTestLineMappingPO> currentMappingTestLineList = subContractTestLineMappingMapper.selectByExample(subContractTestLineMappingExample1);
            TestLineInstanceExample testLineInstanceExample1 = new TestLineInstanceExample();
            testLineInstanceExample1.createCriteria().andGeneralOrderInstanceIDEqualTo(otsOrder.getId());
            List<TestLineInstancePO> allTL = testLineInstanceMapper.selectByExample(testLineInstanceExample1);
            // 记录被删除的TL
            List<String> cancelTLIds;
            List<String> subTLIDList;
            if(Func.isNotEmpty(currentMappingTestLineList)){
                 cancelTLIds = allTL.stream()
                        .filter(tl -> TestLineStatus.check(tl.getTestLineStatus(), TestLineStatus.Cancelled))
                        .map(tl -> tl.getID())
                        .collect(Collectors.toList());
                 subTLIDList = currentMappingTestLineList.stream().map(sub -> sub.getTestLineInstanceID()).collect(Collectors.toList());
                subTLIDList.removeAll(cancelTLIds);
                subTLIDList.removeAll(ids);
                if (CollectionUtils.isNotEmpty(subTLIDList)) {
                    needDelTestLine.addAll(subTLIDList);
                }
            }
            //当前sub是内部分包，需要校验状态 如果不是new test状态 不能继续保存
            Integer subContractOrder = subContractPO.getSubContractOrder();
            if (subContractOrder != null && (subContractOrder.compareTo(1) == 0 || subContractOrder.compareTo(32) == 0)) {
                int status = subContractPO.getStatus();
                if (status != 0 && status != 1 && status != 2) {
                    throw new RuntimeException(subContractPO.getSubContractNo() + " status not new or testing,can't save!");
                }
                //校验TL ，只能增加，不能减少
                if (CollectionUtils.isNotEmpty(currentMappingTestLineList)) {
                    //校验新增TL 状态是否是typing 否则添加没有意义
                    subTLIDList = currentMappingTestLineList.stream().map(sub -> sub.getTestLineInstanceID()).collect(Collectors.toList());
                    List<String> pageIds = Lists.newArrayList(ids.toArray(new String[ids.size()]));
                    pageIds.removeAll(subTLIDList);
                    if (CollectionUtils.isNotEmpty(pageIds)) {
                        long count = allTL.stream()
                                .filter(tl -> pageIds.contains(tl.getID()) && TestLineStatus.check(tl.getTestLineStatus(), TestLineStatus.Typing))
                                .count();
                        if (count != pageIds.size()) {
                            throw new RuntimeException(subContractPO.getSubContractNo() + " added TestLine status must be typing!");
                        }
                    }
                }
            }
            if (SubContractType.check(subContractOrder, SubContractType.NewSubContract)) {
                throw new RuntimeException("Exec SubContract can't save!");
            }
        }
        result.setData(subcontractDTOMaps);
        result.setSuccess(true);
        return result;
    }

    private Map<String,List<String>> findCSPPOpen(List<PPTestLineRelationshipInfoPO> ppTestLineRelationshipInfoPOList){
        Map<String, List<String>> testLineMap = new HashMap<>();
        if(Func.isNotEmpty(ppTestLineRelationshipInfoPOList)){
            ppTestLineRelationshipInfoPOList.stream().forEach(po ->{
                if(Func.isNotEmpty(po.getExtFields())){
                    String parentTestLineInstanceId = Func.toStr(JSONObject.parseObject(po.getExtFields()).getOrDefault("parentTestLineInstanceId", ""));
                    if(Func.isNotEmpty(parentTestLineInstanceId)){
                        if(testLineMap.containsKey(parentTestLineInstanceId)){
                            testLineMap.get(parentTestLineInstanceId).add(po.getTestLineInstanceID());
                        } else {
                            testLineMap.put(parentTestLineInstanceId,Lists.newArrayList(po.getTestLineInstanceID()));
                        }
                    }
                }
            });
        }
        return testLineMap;
    }

    private List<String> buildNeedDelSubContractRelIds(List<PPTestLineJobDto> ppTestLineJob) {
        if (CollectionUtils.isEmpty(ppTestLineJob)) {
            return Lists.newArrayList();
        }
        // 需要删除的 map 关系
        List<String> needDelMapping = Lists.newArrayList();

        List<String> needDelTestLines = ppTestLineJob.stream().map(PPTestLineJobDto::getTestLineInstanceId).distinct().collect(Collectors.toList());
        List<String> delPpTestLineRelIds = ppTestLineJob.stream().map(PPTestLineJobDto::getPpTestLineRelID).distinct().collect(Collectors.toList());
        List<String> conditionIds = ppTestLineJob.stream().map(PPTestLineJobDto::getTestConditionId).distinct().collect(Collectors.toList());
        List<String> conditionGroupIds = ppTestLineJob.stream().map(PPTestLineJobDto::getTestConditionGroupId).distinct().collect(Collectors.toList());
        List<String> matrixIds = ppTestLineJob.stream().map(PPTestLineJobDto::getMatrixId).distinct().collect(Collectors.toList());
        List<String> reportMatrixIds = ppTestLineJob.stream().map(PPTestLineJobDto::getReportMatrixRelId).distinct().collect(Collectors.toList());
        // 需要删除的 tre_order_subcontract_relationship_mapping map 关系
        // 删除testLine
        if (CollectionUtils.isNotEmpty(needDelTestLines)) {
            needDelMapping.addAll(needDelTestLines);
        }
        // 删除 PPTestLine
        if (CollectionUtils.isNotEmpty(delPpTestLineRelIds)) {
            needDelMapping.addAll(delPpTestLineRelIds);
        }
        if (CollectionUtils.isNotEmpty(conditionIds)) {
            needDelMapping.addAll(conditionIds);
        }
        if (CollectionUtils.isNotEmpty(conditionGroupIds)) {
            needDelMapping.addAll(conditionGroupIds);
        }
        if (CollectionUtils.isNotEmpty(matrixIds)) {
            needDelMapping.addAll(matrixIds);
        }
        if (CollectionUtils.isNotEmpty(reportMatrixIds)) {
            needDelMapping.addAll(reportMatrixIds);
        }

        return needDelMapping;
    }

    private CustomResult<SubContractPO> buildSubcontractPO(SaveSubContractReq req, OrderAllDTO orderDTO) {
        CustomResult<SubContractPO> result = new CustomResult<>();
        String subContractId = req.getSubContractId();
        String orderNo = req.getOrderNo();
        String subContractLabCode = req.getSubContractLabCode();
        Integer subContractLabId = req.getSubContractLabId();
        String subcontractServiceType = req.getSubcontractServiceType();
        Integer subcontractTat = req.getSubcontractTat();
        String subContractTo = req.getSubContractLabName();
        String subContractTel = req.getSubContractContractTel();
        String subContractEmail = req.getSubContractContractEmail();
        String startDate = req.getStartDate();
        String completeDate = req.getCompleteDate();
        String expectDueDate = req.getSubContractExpectDueDate();
        String subContractContract = req.getSubContractContract();
        String additionalInfo = req.getAdditionalInfo();
        String subContractRemark = req.getSubContractRemark();
        String otherCode = Func.toStr(req.getOtherCode());
        SubContractPO subContract = null;
        try {
            UserInfo user = tokenClient.getUser();
            if (StringUtils.isBlank(subContractId)) {
                subContract = new SubContractPO();
                subContract.setID(UUID.randomUUID().toString());
                subContract.setCreatedDate(new Date());
                subContract.setCreatedBy(user.getRegionAccount());
                // 涉及到生成分包单号，保存及用到新生成的分包单号的代码逻辑放在后面处理，防止提前生成号码但是处理失败有跳号的问题出现
                //  2022/1/25 使用RootExternalOrderNo生成分包单号
                String externalRootOrderNoByOrderNo = orderClient.getExternalRootOrderNoByOrderNo(orderNo);
                if (Func.isEmpty(externalRootOrderNoByOrderNo)) {
                    String externalOrderNo = orderClient.getExternalOrderNo(orderNo);
                    if (Func.isNotEmpty(externalOrderNo)) {
                        externalRootOrderNoByOrderNo = externalOrderNo;
                    } else {
                        externalRootOrderNoByOrderNo = orderNo;
                    }
                }
                log.info("[{}],getSubContractNumber Req", orderNo);
                String subContractNo = getSubContractNumber(externalRootOrderNoByOrderNo, otherCode, orderDTO);
                log.info("[{}],getSubContractNumber Res:{}", orderNo, subContractNo);
                subContract.setSubContractNo(subContractNo);
                log.info("订单(OrderNo_{})生成新的分包单(SubContractNo_{}).", orderNo, subContract.getSubContractNo());
                // GPO2-8035 新建分包单时根据订单operationType 维护subcontract operationModel
                Integer operationType = orderDTO.getOperationType();
                Integer subcontractOperationModel = OrderOperationType.check(operationType, OrderOperationType.TranslationReport) ? SubcontractOperationModel.TracingOnly.getStatus() : SubcontractOperationModel.FullCycle.getStatus();
                subContract.setOperationModel(subcontractOperationModel);
            } else {
                subContract = subContractMapper.selectByPrimaryKey(subContractId);
                subContract.setModifiedDate(new Date());
                subContract.setModifiedBy(user.getRegionAccount());
            }
            subContract.setOrderNo(orderNo);
            subContract.setLabId(orderDTO.getLabDTO().getLabId());
            subContract.setSubContractServiceType(subcontractServiceType);
            subContract.setSubContractLabName(subContractTo);
            subContract.setSubcontractTat(subcontractTat);
            subContract.setSubContractLabCode(subContractLabCode);
            subContract.setSubContractLabId(subContractLabId);
            subContract.setSubContractContract(subContractContract);
            subContract.setSubContractContractTel(subContractTel);
            subContract.setSubContractContractEmail(subContractEmail);
            if (subContract.getStatus() == null || subContract.getStatus() < 2) {// new or testing
                if (subContract.getStartDate() == null && StringUtils.isBlank(startDate)
                        && subContract.getCompleteDate() == null && StringUtils.isBlank(completeDate)) {
                    subContract.setStatus(SubContractStatusEnum.Created.getStatus());
                }
                if (subContract.getStartDate() == null && StringUtils.isNoneBlank(startDate)) {
                    subContract.setStatus(SubContractStatusEnum.Testing.getStatus());
                }
                if (subContract.getCompleteDate() == null && StringUtils.isNoneBlank(completeDate)) {
                    subContract.setStatus(SubContractStatusEnum.Complete.getStatus());
                }
            }

            if (subContract.getStartDate() == null && StringUtils.isNoneBlank(startDate)) {
                subContract.setStartDate(DateUtils.parseDate(startDate, "yyyy-MM-dd HH:mm"));
            }
            if (subContract.getCompleteDate() == null && StringUtils.isNoneBlank(completeDate)) {
                subContract.setCompleteDate(DateUtils.parseDate(completeDate, "yyyy-MM-dd HH:mm"));
            }
            if (Func.isEmpty(expectDueDate)) {
                ExpectDueDateReq expectDueDateReq = new ExpectDueDateReq();
                expectDueDateReq.setOrderNo(orderNo);
                expectDueDateReq.setExpectDueDateType(ExpectDueDateType.SubContract.getCode());
                expectDueDateReq.setProductLineCode(orderDTO.getBUCode());
                log.info("ExpectDueDateFacade.calculateExpectDueDateByType Request:{}", JSON.toJSONString(expectDueDateReq));
                BaseResponse<ExpectDueDateInfo> responseData = expectDueDateFacade.calculateExpectDueDateByType(expectDueDateReq);
                log.info("ExpectDueDateFacade.calculateExpectDueDateByType Result:{}", JSON.toJSONString(responseData));
                if (Func.isNotEmpty(responseData) && Func.isNotEmpty(responseData.getData())) {
                    subContract.setSubContractExpectDueDate(responseData.getData().getSubcontractExpectDueDate());
                }
            } else {
                subContract.setSubContractExpectDueDate(DateUtils.parseDate(expectDueDate, "yyyy-MM-dd HH:mm"));
            }
            subContract.setAdditionalInfo(additionalInfo);
            subContract.setSubContractRemark(subContractRemark);
            subContract.setSyncStatus(0);
        } catch (Exception e) {
            result.setMsg(e.getMessage());
            result.setSuccess(false);
            return result;
        }
        result.setData(subContract);
        result.setSuccess(true);
        return result;
    }

    private void updateSubcontractFee(SubContractPO subContract, SubcontractRequirementInfo subcontractRequirement) {
        String productLineCode = ProductLineContextHolder.getProductLineCode();
        // 判断是纯分包并且有关联的子单
        Integer subContractOrder = subContract.getSubContractOrder();
        if (SubContractType.check(subContractOrder, SubContractType.SubContract) || SubContractType.check(subContractOrder, SubContractType.LightSubContract) ||
                SubContractType.check(subContractOrder, SubContractType.NewSubContract)) {
            // 查询是否有生成的子单
            SubContractExternalRelationshipPO searchPO = new SubContractExternalRelationshipPO();
            searchPO.setSubContractNo(subContract.getSubContractNo());
            BaseResponse<List<SubContractExternalRelationshipPO>> subContractExternalRelRes = subContractExternalRelService.getSubContractExternalRels(searchPO);
            List<SubContractExternalRelationshipPO> subContractRelList = subContractExternalRelRes.getData();
            if (Func.isNotEmpty(subContractRelList)) {
                String exeOrderNo = subContractRelList.get(0).getExternalNo();
                OrderIdReq exeOrderIdReq = new OrderIdReq();
                exeOrderIdReq.setProductLineCode(productLineCode);
                exeOrderIdReq.setOrderNo(exeOrderNo);
                BaseResponse<OrderAllDTO> orderRes = orderFacade.getOrderForPe(exeOrderIdReq);
                if (Func.isNotEmpty(orderRes) && Func.isNotEmpty(orderRes.getData())) {
                    Integer exeOrderStatus = orderRes.getData().getOrderStatus();
                    Integer operationType = orderRes.getData().getOperationType();
                    if (OperationType.check(operationType, OperationType.SubContract) &&
                            !com.sgs.preorder.facade.model.enums.OrderStatus.checkStatus(exeOrderStatus, com.sgs.preorder.facade.model.enums.OrderStatus.Closed, com.sgs.preorder.facade.model.enums.OrderStatus.Cancelled)) {
                        BigDecimal newSubcontractFee = subcontractRequirement.getSubcontractFee();
                        String newSubcontractFeeCurrency = subcontractRequirement.getSubcontractFeeCurrency();
                        BaseResponse<SubcontractRequirementInfo> oldSubcontractRequirementInfo = subcontractRequirementService.getSubcontractRequirement(subContract.getID());
                        if (Func.isNotEmpty(oldSubcontractRequirementInfo) && Func.isNotEmpty(oldSubcontractRequirementInfo.getData())) {
                            BigDecimal oldSubcontractFee = oldSubcontractRequirementInfo.getData().getSubcontractFee();
                            String oldSubcontractFeeCurrency = oldSubcontractRequirementInfo.getData().getSubcontractFeeCurrency();
                            if (!Func.equalsSafe(newSubcontractFeeCurrency, oldSubcontractFeeCurrency) ||
                                    decimalChanged(oldSubcontractFee, newSubcontractFee)) {
                                UpdateSubcontractFeeReq updateSubcontractFeeReq = new UpdateSubcontractFeeReq();
                                updateSubcontractFeeReq.setOrderNo(exeOrderNo);
                                updateSubcontractFeeReq.setProductLineCode(productLineCode);
                                updateSubcontractFeeReq.setSubcontractFee(newSubcontractFee);
                                updateSubcontractFeeReq.setSubcontractFeeCurrency(newSubcontractFeeCurrency);
                                try {
                                    BaseResponse response = orderFacade.updateSubcontractFee(updateSubcontractFeeReq);
                                    log.info("updateSubcontractFee response :{}", JSON.toJSONString(response));
                                } catch (Exception e) {
                                    e.printStackTrace();
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    private boolean decimalChanged(BigDecimal oldVal, BigDecimal newVal) {
        DecimalFormat decimalFormat = new DecimalFormat("0.00");
        String oldValStr = Func.isNotEmpty(oldVal) ? decimalFormat.format(oldVal) : "";
        String newValSrt = Func.isNotEmpty(newVal) ? decimalFormat.format(newVal) : "";
        return !Func.equalsSafe(oldValStr, newValSrt);
    }

    private void doCompleteAllTestLines(SubContractPO subContractPO) {
        if (subContractPO.getStatus() != SubContractStatusEnum.Complete.getStatus()) {
            return;
        }
        testLineMapper.updateTestLineStatusBySubContractId(subContractPO.getID(), TestLineStatus.Completed.getStatus());
    }

    public void checkPreOrderStatus(String token, OrderInfoDto order) {
        Map<String, Object> params = new HashMap<String, Object>();
        params.put("generalOrderInstanceID", order.getID());
        params.put("testLineStatus", 708);

        // 验证是否所有的testLine都被validate
        List<TestLineInstancePO> testLineLists = testLineMapper.queryByParams(params);

        int count = 0;
        int validCount = 0;
        for (TestLineInstancePO testLine : testLineLists) {
            Integer testLineType = testLine.getTestLineType();
            if (testLineType == null) {
                testLineType = 0;
            }
            if (testLine.getTestLineStatus() == 703
                    || testLine.getTestLineStatus() == 707
                    || testLine.getTestLineStatus() == 706
                    || (testLineType.intValue() & 1) > 0) {
                count++;
            }
            if (testLine.getTestLineStatus() != 707
                    && testLine.getTestLineStatus() != 706) {
                validCount++;
            }
        }
        // 所有的testline都为Completed或Subcontracted或cancelled状态   且 至少有一条有效的TL
        if (validCount > 0 && count == testLineLists.size()) {

            GeneralOrderInstanceInfoPO generalOrderInstanceInfoPO = new GeneralOrderInstanceInfoPO();
            generalOrderInstanceInfoPO.setID(order.getID());
            generalOrderInstanceInfoPO.setOrderStatus(1603);
            orderMapper.updateOrderStatus(generalOrderInstanceInfoPO);
            //用orderNo获取orderStatus
            try {
                OrderIdReq orderIdReq = new OrderIdReq();
                orderIdReq.setOrderNo(order.getOrderNo());
                orderIdReq.setProductLineCode(ProductLineContextHolder.getProductLineCode());
                BaseResponse<OrderInfoDto> orderInfoByOrderNo = orderFacade.getOrderInfoByOrderNo(orderIdReq);
                Integer str = orderInfoByOrderNo.getData().getOrderStatus();
                log.info("call preorder interface getorderstatus : " + str);
                if (StringUtils.isNotBlank(str + "")) {
                    log.info("OrderNo:{} 【dataEntry validate操作】执行preorder状态更新，odlStatus:{}-newStatus-{}", order.getOrderNo(), CommUtil.null2Int(str), 9);
                    Boolean success = preorderInsertChangeLog(token, "order", order.getOrderNo(), CommUtil.null2Int(str), 9, new Date());
                    /*if(order1==null){
                        log.info("OrderNo:{} 【dataEntry validate操作】执行preorder状态更新【Fail】，odlStatus:{}-newStatus-{},Preorder接口返回响应为null",order.getOrderNo(),CommUtil.null2Int(str),9);
                    }else{
                        log.info("OrderNo:{} 【dataEntry validate操作】执行preorder状态更新【{}】，odlStatus:{}-newStatus-{}",order.getOrderNo(),order1!=null && order1.getIsSuccess()?"Success":"Fail",CommUtil.null2Int(str),9);
                    }*/
                }
            } catch (Exception e) {
                log.error("OrderNo:{}", order.getOrderNo(), e.getMessage());
            }

        }
    }

    public Boolean preorderInsertChangeLog(String token, String objectType, String objectNo,
                                           Integer oldStatus, Integer newStatus, Date operationDate) {
        SysStatusReq sysStatusReq = new SysStatusReq();
        sysStatusReq.setProductLineCode(ProductLineContextHolder.getProductLineCode());
        sysStatusReq.setObjectType(objectType);
        sysStatusReq.setObjectNo(objectNo);
        sysStatusReq.setOldStatus(oldStatus);
        sysStatusReq.setNewStatus(newStatus);
        sysStatusReq.setFromSystemId(SgsSystem.GPO.getSgsSystemId());
        sysStatusReq.setToSystemId(SgsSystem.GPO.getSgsSystemId());
        BaseResponse baseResponse = gpoStatusFacade.insertStatusInfo(sysStatusReq);
        int status = baseResponse.getStatus();
        return status == 1 ? true : false;
    }

    /**
     * 生成分包单号码
     *
     * @param orderNo
     * @param otherCode
     * @param oldOrder
     * @return
     */
    public String getSubContractNumber(String orderNo, String otherCode, OrderInfoDto oldOrder) {
        log.info("getSubContractNumber req orderNo,{}", orderNo);
        String orderNoRegex = "^(?<locationBu>[A-Za-z]{4,})(?<date>[0-9]{4,})(?<seq>\\d{6,})";
        Matcher matcher = Pattern.compile(orderNoRegex).matcher(orderNo);
        if (!matcher.find()) throw new RuntimeException("get OrderNoRegex Error!");

        // 获取订单号前缀
        AtomicReference<String> prefix = new AtomicReference<>("");
        prefix.set(matcher.group("locationBu"));

        // 截取掉订单号尾缀
        if (Pattern.compile(".*[A-Za-z]$").matcher(orderNo).find()) {
            orderNo = orderNo.substring(0, orderNo.length() - 2);
        }

        //组装transactionNo
        AtomicReference<String> transactionNo = new AtomicReference<>("");
        transactionNo.set(orderNo + "_" + matcher.group("seq") + "S");
        otherCode = otherCode.replaceAll("[^(a-zA-Z0-9)]", "");
        otherCode = otherCode.length() <= 6 ? otherCode : otherCode.substring(0, 6);
        return frameWorkClient.getNumber(oldOrder.getLocationID(), "GPOSubcontractNoNew", otherCode, prefix.get(), oldOrder.getBUID(), transactionNo.get());
    }

    private void cancelSubOrder(SubContractPO subcontract, String execOrderNo, UserInfo user) {
        String subContractNo = subcontract.getSubContractNo();
        //cancel 子单的tl/sample等数据
        cancelChildOrder(Lists.newArrayList(execOrderNo), user);
        log.info("subContractNo：{} cancel操作 orderNo:{}", subContractNo, execOrderNo);
        //通知preorder 进行cancel操作
        OrderIdReq orderIdReq = new OrderIdReq();
        orderIdReq.setOrderNo(execOrderNo);
        BaseResponse baseResponse = orderFacade.updateOrderStatusBySubContract(orderIdReq);
        if (baseResponse.getStatus() != 200) {
            log.error("内部分包cancle，请求preorder异常,返回result为null");
        }
    }

    public void cancelChildOrder(List<String> childOrderNo, UserInfo user) {
        if (CollectionUtils.isNotEmpty(childOrderNo)) {
            for (String orderNo : childOrderNo) {
                GeneralOrderInstanceInfoPO order = orderMapper.getOrderInfo(orderNo);
                UserHelper.setLocalUser(user);

                cancelTestLine(order, user, "");
                cancelSample(order, user, "order");
                cancelReport(order, user);

                // 更新JOB状态为1104
                Map<String, Object> params = new HashMap<>();
                params.put("generalOrderInstanceID", order.getID());
                params.put("modifiedBy", user.getRegionAccount());
                jobExtMapper.updateJobStateByGeneralOrderInstanceID(params);
                order.setActiveIndicator(false);
                generalOrderInstanceInfoMapper.updateByPrimaryKeySelective(order);

                // 放到redis中，页面每次查询的时候不用都请求DB
                String redisKey = "order_cancel_:" + orderNo;
                // 存储24h
                CacheAgent.getInstance().set(redisKey, order, 60 * 60 * 24);
            }
        }
    }

    private void cancelReport(GeneralOrderInstanceInfoPO order, UserInfo user) {
        ReportInfoExample reportInfoExample = new ReportInfoExample();
        reportInfoExample.createCriteria().andOrderNoEqualTo(order.getOrderNo());
        List<ReportInfoPO> RpList = reportInfoMapper.selectByExample(reportInfoExample);
        if (RpList != null && RpList.size() > 0) {
            for (ReportInfoPO reportPO : RpList) {
                reportPO.setActiveIndicator(false);
                reportPO.setReportStatus(202);
                reportInfoMapper.updateByPrimaryKeySelective(reportPO);
            }
        }
    }

    private void cancelSample(GeneralOrderInstanceInfoPO order, UserInfo user, String type) {
        List<TestSampleGroupPO> sampleGroupPOs = sampleMapper.querySampleGroupByOrderNo(order.getOrderNo());

        if (CollectionUtils.isNotEmpty(sampleGroupPOs)) {
            for (TestSampleGroupPO sampleGroupPO : sampleGroupPOs) {
                sampleGroupPO.setActiveIndicator(false);
                sampleGroupPO.setModifiedBy(user.getRegionAccount());
            }
            sampleMapper.batchUpdateSampleGroupStatus(sampleGroupPOs);
        }
        TestSampleInfoPO samplePO = new TestSampleInfoPO();
        samplePO.setActiveIndicator(false);
        samplePO.setModifiedBy(user.getRegionAccount());
        samplePO.setOrderNo(order.getOrderNo());
        sampleMapper.cancelSampleByOrderNO(samplePO);
    }

    private void cancelTestLine(GeneralOrderInstanceInfoPO order, UserInfo user, String token) {
        String generalOrderInstanceID = order.getID();
        String modifiedBy = user.getRegionAccount();
        TestLineInstancePO testLineInstancePO = new TestLineInstancePO();
        testLineInstancePO.setTestLineStatus(706);
        testLineInstancePO.setActiveIndicator(false);
        testLineInstancePO.setGeneralOrderInstanceID(generalOrderInstanceID);
        testLineInstancePO.setModifiedBy(modifiedBy);
        testLineMapper.cancelTestLineByGeneralOrderId(testLineInstancePO);

        TestMatrixPO testMatrixPO = new TestMatrixPO();
        testMatrixPO.setActiveIndicator(false);
        testMatrixPO.setGeneralOrderInstanceID(generalOrderInstanceID);
        testMatrixPO.setModifiedBy(modifiedBy);
        testMatrixMapper.cancelMatrixByGeneralOrderId(testMatrixPO);

        ConclusionInfoPO conclusionPO = new ConclusionInfoPO();
        conclusionPO.setActiveIndicator(false);
        conclusionPO.setGeneralOrderInstanceID(generalOrderInstanceID);
        conclusionPO.setModifiedBy(modifiedBy);
        conclusionMapper.cancelConclusionByGeneralOrderInstanceID(conclusionPO);

        Map<String, Object> params = new HashMap<String, Object>();
        params.put("modifiedBy", modifiedBy);
        params.put("generalOrderInstanceID", generalOrderInstanceID);
        jobExtMapper.updateJobStateByGeneralOrderInstanceID(params);
    }

}
