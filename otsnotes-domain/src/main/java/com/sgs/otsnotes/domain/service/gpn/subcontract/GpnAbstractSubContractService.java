package com.sgs.otsnotes.domain.service.gpn.subcontract;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.sgs.core.domain.UserInfo;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.core.base.CustomResult;
import com.sgs.framework.core.base.ResponseCode;
import com.sgs.framework.core.context.SystemContextHolder;
import com.sgs.framework.facade.domain.rsp.BuParamValueRsp;
import com.sgs.framework.log.SystemLogHelper;
import com.sgs.framework.log.enums.SystemLogType;
import com.sgs.framework.log.model.SystemLog;
import com.sgs.framework.model.enums.OperationType;
import com.sgs.framework.model.enums.ProductLineType;
import com.sgs.framework.model.enums.TestLineType;
import com.sgs.framework.model.enums.*;
import com.sgs.framework.security.context.SecurityContextHolder;
import com.sgs.framework.tool.utils.BeanUtil;
import com.sgs.framework.tool.utils.CollectionUtil;
import com.sgs.framework.tool.utils.Func;
import com.sgs.framework.tool.utils.ProductLineContextHolder;
import com.sgs.grus.bizlog.BizLogClient;
import com.sgs.grus.bizlog.common.BizLog;
import com.sgs.grus.bizlog.common.BizLogHelper;
import com.sgs.grus.bizlog.info.BizLogInfo;
import com.sgs.otsnotes.core.common.UserHelper;
import com.sgs.otsnotes.core.config.InterfaceConfig;
import com.sgs.otsnotes.core.config.SubcontractConfig;
import com.sgs.otsnotes.core.constants.BizLogConstant;
import com.sgs.otsnotes.core.constants.Constants;
import com.sgs.otsnotes.core.constants.LabCodeConsts;
import com.sgs.otsnotes.core.constants.SampleCategoryConsts;
import com.sgs.otsnotes.core.enums.ReportFlagEnums;
import com.sgs.otsnotes.core.util.*;
import com.sgs.otsnotes.dbstorages.mybatis.config.DatabaseContextHolder;
import com.sgs.otsnotes.dbstorages.mybatis.enums.CustomerUsage;
import com.sgs.otsnotes.dbstorages.mybatis.enums.ReportTypeEnum;
import com.sgs.otsnotes.dbstorages.mybatis.extmapper.*;
import com.sgs.otsnotes.dbstorages.mybatis.extmodel.ValidateTLInfo;
import com.sgs.otsnotes.dbstorages.mybatis.extmodel.subcontract.SubContractSyncDTO;
import com.sgs.otsnotes.dbstorages.mybatis.mapper.*;
import com.sgs.otsnotes.dbstorages.mybatis.model.*;
import com.sgs.otsnotes.domain.service.*;
import com.sgs.otsnotes.domain.service.digitalReport.DigitalReportService;
import com.sgs.otsnotes.domain.service.gpn.report.IGpnReportService;
import com.sgs.otsnotes.domain.service.gpn.report.IGpnSubReportService;
import com.sgs.otsnotes.domain.service.localize.TestConditionInstanceLocalizeHelper;
import com.sgs.otsnotes.domain.service.productlineservice.ProductLineService;
import com.sgs.otsnotes.domain.service.reportFile.IReportFileService;
import com.sgs.otsnotes.domain.service.subcontract.GetReportTestDataDto;
import com.sgs.otsnotes.domain.service.subcontract.ISubcontractService;
import com.sgs.otsnotes.domain.service.testline.ITestLineStatusService;
import com.sgs.otsnotes.domain.service.testline.TestLineStatusService;
import com.sgs.otsnotes.domain.service.webservice.ots.Subcontract.OtsSubContractWebservice;
import com.sgs.otsnotes.domain.service.webservice.ots.gpoSubcontract.GpoSubContractWebservice;
import com.sgs.otsnotes.facade.model.comparator.TestSampleSimplifyComparator;
import com.sgs.otsnotes.facade.model.comparator.TestSampleSimplifyMixComparator;
import com.sgs.otsnotes.facade.model.dto.*;
import com.sgs.otsnotes.facade.model.dto.report.ReportSubReportDTO;
import com.sgs.otsnotes.facade.model.dto.subcontract.SubContractMatrixInfo;
import com.sgs.otsnotes.facade.model.dto.subreport.SubReportDTO;
import com.sgs.otsnotes.facade.model.dto.testdata.AnalyteLangDTO;
import com.sgs.otsnotes.facade.model.dto.testdata.GetTestDataDTO;
import com.sgs.otsnotes.facade.model.dto.testdata.TestDataResultDTO;
import com.sgs.otsnotes.facade.model.dto.testdata.TestDataTestMatrixDTO;
import com.sgs.otsnotes.facade.model.enums.JobStatus;
import com.sgs.otsnotes.facade.model.enums.ObjectType;
import com.sgs.otsnotes.facade.model.enums.OrderStatus;
import com.sgs.otsnotes.facade.model.enums.ReportStatus;
import com.sgs.otsnotes.facade.model.enums.SampleType;
import com.sgs.otsnotes.facade.model.enums.SubContractType;
import com.sgs.otsnotes.facade.model.enums.TableType;
import com.sgs.otsnotes.facade.model.enums.TestLineStatus;
import com.sgs.otsnotes.facade.model.enums.*;
import com.sgs.otsnotes.facade.model.gpn.subreport.info.SubReportInfo;
import com.sgs.otsnotes.facade.model.gpn.testline.info.TestLineNameInfo;
import com.sgs.otsnotes.facade.model.gpn.testline.info.TestLineNameLanguageInfo;
import com.sgs.otsnotes.facade.model.info.FileInfo;
import com.sgs.otsnotes.facade.model.info.SubContractTestLineInfo;
import com.sgs.otsnotes.facade.model.info.report.GetReportInfo;
import com.sgs.otsnotes.facade.model.info.report.GetReportListInfo;
import com.sgs.otsnotes.facade.model.info.subcontract.SubContractInfo;
import com.sgs.otsnotes.facade.model.info.subcontract.SubcontractRequirementInfo;
import com.sgs.otsnotes.facade.model.ordercopy.SysCopyInfo;
import com.sgs.otsnotes.facade.model.po.SubcontractRelInfoPO;
import com.sgs.otsnotes.facade.model.po.xml.*;
import com.sgs.otsnotes.facade.model.req.*;
import com.sgs.otsnotes.facade.model.req.customer.CustomerDiscountInfoReq;
import com.sgs.otsnotes.facade.model.req.gpn.*;
import com.sgs.otsnotes.facade.model.req.starlims.SubContractReportInfo;
import com.sgs.otsnotes.facade.model.req.subReport.ReturnSubContractReportReq;
import com.sgs.otsnotes.facade.model.req.subcontract.GenerateReportForSubcontractReq;
import com.sgs.otsnotes.facade.model.req.testLine.TestLineStatusUpdateReq;
import com.sgs.otsnotes.facade.model.req.workSheet.ToSlimBody;
import com.sgs.otsnotes.facade.model.req.workSheet.ToSlimReq;
import com.sgs.otsnotes.facade.model.rsp.CustomerDiscountInfoRsp;
import com.sgs.otsnotes.facade.model.rsp.PageInfoList;
import com.sgs.otsnotes.facade.model.rsp.QuerySubContractRsp;
import com.sgs.otsnotes.facade.model.rsp.conclusion.SubReportListRsp;
import com.sgs.otsnotes.facade.model.rsp.gpn.GpnSubContractRsp;
import com.sgs.otsnotes.facade.model.rsp.sample.TestSampleSimplifyInfo;
import com.sgs.otsnotes.facade.model.rsp.subcontract.SampleRsp;
import com.sgs.otsnotes.facade.model.rsp.subcontract.SubContractPrintRsp;
import com.sgs.otsnotes.facade.model.rsp.subcontract.TestLineInstanceSubContractRsp;
import com.sgs.otsnotes.facade.model.rsp.testLine.ExecTestLineStatusRsp;
import com.sgs.otsnotes.facade.model.rsp.testLine.GetSubContractTestLineRsp;
import com.sgs.otsnotes.facade.model.subcontract.req.ExternalFileReq;
import com.sgs.otsnotes.facade.model.subcontract.req.SubContractCompleteReq;
import com.sgs.otsnotes.facade.model.subcontract.req.SubContractItemCompleteReq;
import com.sgs.otsnotes.facade.model.subcontract.req.SubContractTestingReq;
import com.sgs.otsnotes.facade.model.subcontract.res.SubContractCompleteRes;
import com.sgs.otsnotes.integration.*;
import com.sgs.otsnotes.integration.trimslocal.CitationClient;
import com.sgs.otsnotes.integration.trimslocal.PpClient;
import com.sgs.otsnotes.integration.trimslocal.TestLineClient;
import com.sgs.preorder.facade.*;
import com.sgs.preorder.facade.model.dto.customer.CustomerInstanceDTO;
import com.sgs.preorder.facade.model.dto.order.*;
import com.sgs.preorder.facade.model.enums.ActiveType;
import com.sgs.preorder.facade.model.enums.CustomerType;
import com.sgs.preorder.facade.model.enums.*;
import com.sgs.preorder.facade.model.info.TestRequestInfo;
import com.sgs.preorder.facade.model.info.externalOrder.GpoExternalOrderInfo;
import com.sgs.preorder.facade.model.req.*;
import com.sgs.preorder.facade.model.req.externalOrder.GpoExternalOrderNoReq;
import com.sgs.preorder.facade.model.req.operationHistory.InsertOperationHistoryReq;
import com.sgs.preorder.facade.model.rsp.ExternalReportNoRsp;
import com.sgs.preorder.facade.model.rsp.ReportReceiverRsp;
import com.sgs.preorder.facade.model.rsp.TestRequestRsp;
import com.sgs.priceengine.facade.QuotationFacade;
import com.sgs.priceengine.facade.model.DTO.TLAmountDTO;
import com.sgs.priceengine.facade.model.request.QueryTLAmountRequest;
import com.sgs.testdatabiz.facade.model.testdata.ReportTestDataInfo;
import com.sgs.testdatabiz.facade.model.testdata.TestDataConditionInfo;
import com.sgs.testdatabiz.facade.model.testdata.TestDataConditionLangInfo;
import com.sgs.testdatabiz.facade.model.testdata.TestDataResultInfo;
import com.sgs.testdatabiz.facade.model.testdata.TestDataResultLangInfo;
import com.sgs.testdatabiz.facade.model.testdata.TestDataTestMatrixInfo;
import com.sgs.testdatabiz.facade.model.testdata.TestDataTestMatrixLangInfo;
import com.sgs.trimslocal.facade.model.pp.rsp.GetPpBaseInfoRsp;
import com.sgs.trimslocal.facade.model.testline.req.GetTestLineEvaluationAliasItemReq;
import com.sgs.trimslocal.facade.model.testline.rsp.GetCitationBaseInfoRsp;
import com.sgs.trimslocal.facade.model.testline.rsp.GetTestLineBaseInfoRsp;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static com.sgs.otsnotes.facade.model.enums.SampleType.MixSample;
import static com.sgs.otsnotes.facade.model.enums.TableType.Matrix;
import static com.sgs.otsnotes.facade.model.enums.TestLineStatus.Completed;
import static com.sgs.otsnotes.facade.model.enums.TestLineStatus.Typing;

@Slf4j
public abstract class GpnAbstractSubContractService extends ProductLineService implements ISubContractService {

    private final Logger logger = LoggerFactory.getLogger(GpnAbstractSubContractService.class);

    @Resource
    private SubContractMapper subContractMapper;

    @Resource
    private SubContractExtMapper subContractExtMapper;

    @Resource
    private TestLineTatConfigMapper testLineTatConfigMapper;

    @Resource
    private TokenClient tokenClient;

    @Resource
    private SubContractService subContractService;

    @Resource
    private OrderFacade orderFacade;

    @Resource
    private SubContractTestLineMappingMapper subContractTestLineMappingMapper;

    @Resource
    private InterfaceConfig interfaceConfig;

    @Resource
    private JobExtMapper jobExtMapper;

    @Resource
    private JobInfoMapper jobInfoMapper;

    @Resource
    private TestLineMapper testLineMapper;

    @Resource
    private OrderMapper orderMapper;

    @Resource
    private StatusFacade gpoStatusFacade;

    @Resource
    private TestMatrixMapper testMatrixMapper;

    @Resource
    private ConclusionMapper conclusionMapper;

    @Resource
    private TestSampleMapper sampleMapper;

    @Resource
    private ReportInfoMapper reportInfoMapper;

    @Resource
    private GeneralOrderInstanceInfoMapper generalOrderInstanceInfoMapper;

    @Resource
    private BizLogClient bizLogClient;

    @Resource
    private SubcontractConfig subcontractConfig;

    @Resource
    private TestLineService testLineService;

    @Resource
    private ToDoListFacade toDoListFacade;

    @Resource
    private OrderFacade gpoOrderFacade;

    @Resource
    private ISubContractExternalRelService subContractExternalRelService;

    @Resource
    private FileClient fileClient;

    @Resource
    private SubReportMapper subReportMapper;

    @Resource
    private OrderSyncService orderSyncService;

    @Resource
    private TestLineValidateService testLineValidateService;

    @Resource
    private StatusClient statusClient;

    @Resource
    private FrameWorkClient frameWorkClient;

    @Resource
    private ISubcontractRequirementService subcontractRequirementService;

    @Resource
    private SubcontractRequirementMapper subcontractRequirementMapper;

    @Resource
    private OrderClient orderClient;

    @Autowired
    private SubcontractRequirementInfoMapper subcontractRequirementInfoMapper;

    @Autowired
    private TestLineInstanceExtMapper testLineInstanceExtMapper;
    @Autowired
    private TestSampleMapper testSampleMapper;
    @Autowired
    private OrderReportFacade orderReportFacade;
    @Autowired
    private CustomerClient customerClient;
    @Autowired
    private OrderTestLineService orderTestLineService;
    @Autowired
    private OtsSubContractWebservice otsSubContractWebservice;
    @Autowired
    private GpoSubContractWebservice gpoSubContractWebservice;
    @Autowired
    private OperationHistoryFacade operationHistoryFacade;
    @Autowired
    private ReportMapper reportMapper;
    @Autowired
    private SubReportExtMapper subReportExtMapper;
    @Autowired
    private IGpnReportService reportService;
    @Autowired
    private ReportSubReportRelationshipInfoMapper reportSubReportRelationshipInfoMapper;
    @Autowired
    private ReportMatrixRelMapper reportMatrixRelMapper;
    @Autowired
    private SystemLogHelper systemLogHelper;
    @Autowired
    private TestConditionMapper testConditionMapper;
    @Autowired
    private ConclusionService conclusionService;
    @Autowired
    private QuotationFacade quotationFacade;
    @Autowired
    private DigitalReportService digitalReportService;
    @Autowired
    private IGpnSubReportService subReportService;
    @Autowired
    private IReportFileService reportFileService;
    @Autowired
    private ISubcontractService subcontractService;
    @Autowired
    private SearchListValidFacade searchListValidFacade;
    private TransactionTemplate transactionTemplate;
    @Autowired
    private TestLineStatusService testLineStatusService;
    @Autowired
    private ReportFileExtMapper reportFileExtMapper;
    @Autowired
    private TestDataMapper testDataMapper;
    @Autowired
    private TestConditionLanguageMapper testConditionLanguageMapper;
    @Autowired
    private SampleExtMapper sampleExtMapper;
    @Autowired
    private TestSampleLangMapper testSampleLangMapper;
    @Autowired
    private TestSampleLangService testSampleLangService;
    @Autowired
    private PpClient ppClient;
    @Autowired
    private CitationClient citationClient;
    @Autowired
    private TestLineClient testLineClient;
    @Autowired
    private OrderSubcontractRelMapper subcontractRelMapper;
    @Autowired
    private ITestLineStatusService testLineStatusServiceNew;

    @Override
    public BaseResponse<List<QuerySubContractRsp>> querySubContractByReport(QuerySubContractReq querySunContractReq) {
        BaseResponse<List<QuerySubContractRsp>> response = new BaseResponse<>();
        String reportNo = querySunContractReq.getReportNo();
        if (Func.isEmpty(reportNo)) {
            response.setStatus(500);
            response.setMessage("reportNo 不能为空");
            return response;
        }
        List<String> subcontractNos = conclusionService.getSubcontractNosByReport(reportNo);
        if (Func.isEmpty(subcontractNos)) {
            return response;
        }
        //
        SubContractExample example = new SubContractExample();
        example.createCriteria().andSubContractNoIn(subcontractNos).andStatusNotEqualTo(SubContractStatus.CANCELLED.getCode());
        List<SubContractPO> subContractList = subContractMapper.selectByExample(example);
        if (Func.isNotEmpty(subContractList)) {
            List<QuerySubContractRsp> subContractRspList = Lists.newArrayList();
            subContractList.stream().forEach(subContract -> {
                QuerySubContractRsp querySubContractRsp = new QuerySubContractRsp();
                querySubContractRsp.setSubcontractNo(subContract.getSubContractNo());
                querySubContractRsp.setId(subContract.getID());
                querySubContractRsp.setSubcontractLabCode(subContract.getSubContractLabCode());
                List<SubContractTestLineInfo> testLineList = subContractExtMapper.getSubContractTestLineMappingList(subContract.getSubContractNo());
                if (Func.isNotEmpty(testLineList)) {
                    querySubContractRsp.setTestLineList(testLineList);
                    querySubContractRsp.setTestLineListStr(testLineList.stream().map(tl -> tl.getTestLineNo()).collect(Collectors.joining(",")));
                }
                subContractRspList.add(querySubContractRsp);
            });
            response.setData(subContractRspList);
        }
        return response;
    }

    @Override
    public CustomResult querySubContractList(QuerySubContractReq querySunContractReq, String page, String rows) {
        CustomResult customResult = new CustomResult();
        if(Func.isEmpty(querySunContractReq)){
            customResult.setSuccess(false);
            customResult.setMsg(ResponseCode.PARAM_MISS.getMessage());
            return customResult;
        }
        if (page != null && rows != null) {
            querySunContractReq.setPageNum(Integer.valueOf(page));
            querySunContractReq.setPageSize(Integer.valueOf(rows));
        }
        UserInfo userInfo = SecurityContextHolder.getUserInfo();
        if(Func.isEmpty(userInfo)||Func.isEmpty(userInfo.getCurrentLabCode())){
            customResult.setSuccess(false);
            customResult.setMsg(ResponseCode.TokenExpire.getMessage());
            return customResult;
        }
        if(Func.isEmpty(ProductLineContextHolder.getProductLineCode())){
            customResult.setSuccess(false);
            customResult.setMsg(ResponseCode.PARAM_MISS.getMessage());
            return customResult;
        }
        querySunContractReq.setLabCode(userInfo.getCurrentLabCode());
        Map<String, List<String>> dimensionMap = userInfo.getDimensionMap();
        //有没有tops权限，没有值是有权限
        String tops = "";
        if (dimensionMap != null) {
            List<String> block_tops = dimensionMap.get("block_tops");
            if (block_tops != null && block_tops.size() > 0) {
                tops = block_tops.get(0);
            }
        }
//        querySunContractReq.setBlockTops(tops);
        if (Func.isNotEmpty(querySunContractReq.getBatchOrderNo())) {
            String[] split = querySunContractReq.getBatchOrderNo().split("\n");
            List<String> list = new ArrayList<>();
            for (String item : split) {
                list.add(item);
            }
            if (Func.isNotEmpty(list)) {
                if (Func.isNotEmpty(list)){
                    List<String> batchOrderNoList = new ArrayList<>();
                    batchOrderNoList = orderClient.batchGetOrderNoByExternalOrderNo(list,ProductLineContextHolder.getProductLineCode());
                    if(batchOrderNoList==null){
                        batchOrderNoList = new ArrayList<>();
                    }
                    batchOrderNoList.addAll(list);
                    if(Func.isNotEmpty(batchOrderNoList)){
                        batchOrderNoList = batchOrderNoList.stream().distinct().collect(Collectors.toList());
                        querySunContractReq.setBatchOrderNoList(batchOrderNoList);
                    }
                }

            }
        }

        //校验查询条件
        SearchListValidReq searchListValidReq = new SearchListValidReq();
        searchListValidReq.setRequestReqObj(querySunContractReq);
        searchListValidReq.setObjectCode("subcontractList");
        searchListValidReq.setSearchCode("subcontractSearch");

        BaseResponse validRes = searchListValidFacade.validSearchRule(searchListValidReq);
        if(Func.isNotEmpty(validRes) && !validRes.isSuccess()){
            customResult.setSuccess(false);
            customResult.setMsg(validRes.getMessage());
            return customResult;
        }
        //不查询Cancelled状态的数据
        if(Func.isEmpty(querySunContractReq.getBatchOrderNo()) && Func.isEmpty(querySunContractReq.getSubcontractNo()) && Func.isEmpty(querySunContractReq.getOrderNo())){
            querySunContractReq.setCancelStatus(SubContractStatus.CANCELLED.getCode());
        }

        PageInfo<QuerySubContractRsp> querySubContractRspPageInfo = subContractService.querySubcontract(querySunContractReq);
        customResult.setData(querySubContractRspPageInfo);
        customResult.setSuccess(true);
        // 记录推送日志
        SystemLog systemLog = new SystemLog();
        systemLog.setObjectType(SystemLogObjectEnum.SUBCONTRACT.getType());
        systemLog.setObjectNo(null);
        systemLog.setProductLineCode(ProductLineContextHolder.getProductLineCode());
        systemLog.setType(com.sgs.otsnotes.facade.model.enums.SystemLogType.BURIED_POINT.getType());
        systemLog.setRemark(SystemLogObjectEnum.SUBCONTRACT.getDesc());
        systemLog.setRequest(JSON.toJSONString(querySunContractReq));
        systemLog.setCreateBy(userInfo.getRegionAccount());
        systemLog.setLocationCode(userInfo.getCurrentLabCode().split(" ")[0]);
        systemLog.setOperationType(SystemLogObjectEnum.SUBCONTRACT.getOpType());
        systemLog.setCreateBy(userInfo.getRegionAccount());
        systemLogHelper.save(systemLog);
        return customResult;
    }

    @Override
    public CustomResult getSubContractTestLine(GpnSubContractTestLineReq req) {
        CustomResult rspResult = new CustomResult();

        //用户语言类型
//        String defaultLanguageCode = tokenClient.getUser().getDefaultLanguageCode();
        List<SubcontractDTO> list = new ArrayList<>();
        OrderTestLineListSearchReq orderTestLineListSearchReq = new OrderTestLineListSearchReq();
        orderTestLineListSearchReq.setOrderNo(req.getOrderNo());

        List<OrderTestLineListDTO> defaultOrderTestLineList = orderTestLineService.getOrderTestLineInfoList(orderTestLineListSearchReq);
        List<SubcontractDTO> defaultlist = new ArrayList<>();
        if (Func.isNotEmpty(defaultOrderTestLineList)) {
            defaultOrderTestLineList = defaultOrderTestLineList.stream().filter(i -> (Func.isEmpty(i.getPendingFlag()) || i.getPendingFlag() == false) && Func.isEmpty(i.getSubContractID()) && Func.isEmpty(i.getJobRelId()) && !TestLineStatus.check(i.getTestLineStatus(),TestLineStatus.Cancelled,TestLineStatus.NC,TestLineStatus.NA,TestLineStatus.DR)).collect(Collectors.toList());
            if (Func.isEmpty(req.getSubContractId())) {
                defaultOrderTestLineList = defaultOrderTestLineList.stream().filter(i -> !Func.equals(TestLineStatus.Completed.getStatus(), i.getTestLineStatus())).collect(Collectors.toList());
            }
            defaultlist = transOrderTestLine(defaultOrderTestLineList);
        }
        List<SubcontractDTO> subContractlist = new ArrayList<>();
        if (Func.isNotEmpty(req.getSubContractId())) {
            orderTestLineListSearchReq = new OrderTestLineListSearchReq();
            orderTestLineListSearchReq.setSubContractId(req.getSubContractId());
            List<OrderTestLineListDTO> subContractTestLineList = orderTestLineService.getOrderTestLineInfoList(orderTestLineListSearchReq);
            if (Func.isNotEmpty(subContractTestLineList)) {
                subContractTestLineList = subContractTestLineList.stream().filter(i -> (Func.isEmpty(i.getPendingFlag()) || i.getPendingFlag() == false) && !TestLineStatus.check(i.getTestLineStatus(),TestLineStatus.Cancelled,TestLineStatus.NC,TestLineStatus.DR)).collect(Collectors.toList());
                subContractlist = transOrderTestLine(subContractTestLineList);
            }
        }
        if (req.getAllFlag() == 1) {// 展示所有TL
            OrderTestLineListSearchReq orderTestLineListSearchReq2 = new OrderTestLineListSearchReq();
            orderTestLineListSearchReq2.setOrderNo(req.getOrderNo());
            List<OrderTestLineListDTO> subContractTestLineList = orderTestLineService.getOrderTestLineInfoList(orderTestLineListSearchReq2);
            if (Func.isNotEmpty(subContractTestLineList)) {
                subContractTestLineList = subContractTestLineList.stream().filter(i -> (Func.isEmpty(i.getPendingFlag()) || i.getPendingFlag() == false) && !TestLineStatus.check(i.getTestLineStatus(),TestLineStatus.Cancelled,TestLineStatus.NC,TestLineStatus.DR)).collect(Collectors.toList());
                if (Func.isEmpty(req.getSubContractId())) {
                    subContractTestLineList = subContractTestLineList.stream().filter(i -> !Func.equals(TestLineStatus.Completed.getStatus(), i.getTestLineStatus())).collect(Collectors.toList());
                }
                list = transOrderTestLine(subContractTestLineList);
            }
            if (Func.isNotEmpty(defaultlist) && Func.isNotEmpty(list)) {
                for (SubcontractDTO sub : list) {
                    for (SubcontractDTO dd : defaultlist) {
                        if (!dd.getTestLineInstanceID().equals(sub.getTestLineInstanceID())) {
                            sub.setFlag(true);
                            break;
                        }
                    }
                }
            } else if(Func.isNotEmpty(list)){
                for (SubcontractDTO sub : list) {
                    sub.setFlag(true);
                }
            }
            if(Func.isNotEmpty(list)){
                for (SubcontractDTO dd : subContractlist) {
                    for (SubcontractDTO sub : list) {
                        if (dd.getTestLineInstanceID().equals(sub.getTestLineInstanceID())) {
                            sub.setSelectFlag(true);
                            sub.setFlag(false);
                            break;
                        }
                    }
                }
            }

        } else {
            list = new ArrayList<>();
            list.addAll(defaultlist);
            if (!subContractlist.isEmpty()) {
                for (SubcontractDTO sub : subContractlist) {
                    sub.setSelectFlag(true);
                    list.add(sub);
                }
            }
        }

        for (SubcontractDTO dto : list) {
            if (StringUtils.isBlank(dto.getLabSection())) {
                if (StringUtils.isBlank(dto.getSubContractName())) {
                    dto.setLabSection("/");
                } else {
                    dto.setLabSection(dto.getSubContractName());
                }
            }
        }
        TestLineTatConfigExample testLineTatConfigPONull = new TestLineTatConfigExample();
        List<TestLineTatConfigPO> testLineTATConfigPOS = testLineTatConfigMapper.selectByExample(testLineTatConfigPONull);
        list.forEach(x -> {
            List<TestLineTatConfigPO> collect = testLineTATConfigPOS.stream().filter(y -> y.getTestLineID().equals(Integer.valueOf(x.getTestLineId()))).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(collect)) {

                try {
                    OrderIdReq orderIdReq = new OrderIdReq();
                    orderIdReq.setOrderNo(req.getOrderNo());
                    orderIdReq.setProductLineCode(ProductLineContextHolder.getProductLineCode());
                    BaseResponse<OrderAllDTO> orderForPe = gpoOrderFacade.getOrderForPe(orderIdReq);
                    if (orderForPe.getData() == null) {
                        throw new RuntimeException("");
                    }
                    OrderAllDTO orderAllDTO = orderForPe.getData();
                    if (collect.get(0).getTat() > Integer.valueOf(orderAllDTO.getTAT())) {
                        x.setLongTatMsg("TestLine TAT:" + collect.get(0).getTat() + " Days");
                    }
                } catch (Exception e) {
                    logger.error("调用OrderApi出错：", e);
                }
            }
        });
        List<GetSubContractTestLineRsp> getSubContractTestLineRsps = new ArrayList<GetSubContractTestLineRsp>(list.size());
        list.forEach(subcontractDTO -> {
            GetSubContractTestLineRsp objGetSubContractTestLineRsp = new GetSubContractTestLineRsp();
            BeanUtils.copyProperties(subcontractDTO, objGetSubContractTestLineRsp);
            objGetSubContractTestLineRsp.setTestLineInstanceId(subcontractDTO.getTestLineInstanceID());
            getSubContractTestLineRsps.add(objGetSubContractTestLineRsp);
        });
        /*List<Long> ids = new ArrayList<>();
        getSubContractTestLineRsps.forEach(item ->{
            if (Func.isNotEmpty(item.getCitationBaseId())){
                ids.add(item.getCitationBaseId());
            }
        });*/
//        String defaultLanguageCode = tokenClient.getUser().getDefaultLanguageCode();
//        boolean check = UserInfoDefaultLanguageCodeEnums.check(defaultLanguageCode, UserInfoDefaultLanguageCodeEnums.zh_cn);
        /*if (check){
            testLineLocalService.build(req.getOrderNo(),list,true,false);
            List<ArtifactCitationLangInfoPO> testLineCitationLanguageInfoPOS = getTestLineCitationLanguageInfoPOS(ids);
            for (ArtifactCitationLangInfoPO item : testLineCitationLanguageInfoPOS){
                list.forEach(item2 ->{
                    if (Func.isNotEmpty(item.getCitationBaseId()) && Func.isNotEmpty(item.getCitationName()) && item.getCitationBaseId() == item2.getCitationBaseId()){
                        item2.setTestStandard(item.getCitationName());
                    }
                });
            }
        }*/
        // OOB业务场景下优先显示CustomerTestLineName 需要放到中英文映射的后边
       /* list.forEach(item ->{
            Integer testLineType = item.getTestLineType();
            if(Func.isNotEmpty(testLineType) && ((testLineType & TestLineType.OOB_TEST.getType())==TestLineType.OOB_TEST.getType())){
                String testLineName = item.getCustomerTestLineName();
                item.setTestItem(testLineName);
            }
        });*/
        //处理 tlamouont
        if (Func.isNotEmpty(list)) {
            List<TestLineInstanceDTO> testLineInstanceDTOList = testLineInstanceExtMapper.getTlAndStandardByTlInstanceIds(list.stream().map(e -> e.getTestLineInstanceID()).collect(Collectors.toList()));
            OrderNosReq orderNosReq = new OrderNosReq();
            orderNosReq.setOrderNos(Lists.newArrayList(testLineInstanceDTOList.get(0).getOrderNo()));
            List<OrderAllDTO> orderAllDTOList = orderFacade.getOrderAllListByOrderNos(orderNosReq).getData();

            QueryTLAmountRequest objQueryTLAmountRequest = new QueryTLAmountRequest();
            objQueryTLAmountRequest.setSystemId(15);
            objQueryTLAmountRequest.setProductLineCode(ProductLineContextHolder.getProductLineCode());
            objQueryTLAmountRequest.setOrderIdList(orderAllDTOList.stream().map(e -> e.getID()).collect(Collectors.toList()));
            logger.info("get tl amount req for subcontract:" + JSON.toJSONString(objQueryTLAmountRequest));
            BaseResponse<List<TLAmountDTO>> tlAmountReponse = quotationFacade.queryTLAmount(objQueryTLAmountRequest);
            logger.info("get tl amount rsp for subcontract:" + JSON.toJSONString(tlAmountReponse));
            // 根据接包方TL判断状态
            List<ExecTestLineStatusRsp> execTestLineStatusRspList = subContractExtMapper.checkSubcontractTestLine(req.getSubContractId());
            SubContractPO subContractPO = subContractMapper.selectByPrimaryKey(req.getSubContractId());
            for (SubcontractDTO objSubcontractDTO : list) {
                //find testlineversionid and standardversionid
                TestLineInstanceDTO objTestLineInstanceDTO = testLineInstanceDTOList.stream().filter(e -> e.getId().equals(objSubcontractDTO.getTestLineInstanceID())).findFirst().orElse(null);
                if (objTestLineInstanceDTO != null) {
                    this.setSubcontractTLAmount(tlAmountReponse.getData(), objTestLineInstanceDTO, objSubcontractDTO);
                }
                if(Func.isEmpty(req.getSubContractId())){
                    objSubcontractDTO.setCheck(true);
                }else if(Func.isNotEmpty(execTestLineStatusRspList)){
                    ExecTestLineStatusRsp execTestLineStatusRsp = execTestLineStatusRspList.stream().filter(i -> Func.equals(i.getId(), objSubcontractDTO.getTestLineInstanceID())).findFirst().orElse(null);
                    objSubcontractDTO.setCheck(this.checkTestLine(subContractPO, execTestLineStatusRsp,objSubcontractDTO));
                }else if(SubContractStatus.check(subContractPO.getStatus(), SubContractStatus.REPORT_COMPLETED)){
                    objSubcontractDTO.setCheck(false);
                } else{
                    objSubcontractDTO.setCheck(true);
                }
            }
        }
        rspResult.setData(getSubContractTestLineList(list));
        rspResult.setSuccess(true);
        return rspResult;
    }

    private Boolean checkTestLine(SubContractPO subContractPO, ExecTestLineStatusRsp execTestLineStatusRsp,SubcontractDTO subcontractDTO) {
        // 新增分包单场景
        if (Func.isEmpty(subContractPO)) {
            return true;
        }
        if (Func.isNotEmpty(subContractPO)) {
            Integer subcontractStatus = subContractPO.getStatus();
            Integer subContractOrder = subContractPO.getSubContractOrder();
            Integer dataLock = subContractPO.getDataLock();
            if(Func.isNotEmpty(dataLock) && dataLock == 1){
                return false;
            }
            if (SubContractStatus.check(subcontractStatus, SubContractStatus.NEW)) {
                return true;
            } else if (SubContractStatus.check(subcontractStatus, SubContractStatus.TESTING)) {
                if (Func.equalsSafe(subContractOrder, 1) && subcontractDTO.isSelectFlag()) {
                    // 根据接包方TL判断状态
                    if (Func.isEmpty(execTestLineStatusRsp)) {
                        return false;
                    }
                    if(JobStatus.check(execTestLineStatusRsp.getExcJobStatus(), JobStatus.Completed,JobStatus.Closed,JobStatus.PENDING)){
                        return false;
                    }

                    if (Func.isNotEmpty(execTestLineStatusRsp.getTestLineStatus()) && !TestLineStatus.checkCategory(execTestLineStatusRsp.getTestLineStatus(),Constants.TEST_LINE.STATUS_CATEGORY.EDIT)) {
                        return false;
                    }
                }
                if (Func.equalsSafe(subContractOrder, 0)) {
                    return false;
                }
                return true;
            }else if(SubContractStatus.check(subcontractStatus, SubContractStatus.REPORT_COMPLETED)){
                return false;
            }
        }
        return false;
    }

    private void setSubcontractTLAmount(List<TLAmountDTO> tlAmountDTOList, TestLineInstanceDTO objTestLineInstanceDTO, SubcontractDTO querySubContractRsp) {
        if (CollectionUtils.isEmpty(tlAmountDTOList)) {
            return;
        }

        TLAmountDTO objTLAmountDTO = tlAmountDTOList.stream().filter(e -> {
                return Func.equalsSafe(e.getPpTlRelId(),objTestLineInstanceDTO.getPpTlRelId());
        }
        ).findFirst().orElse(null);
        querySubContractRsp.setTlTotalAmountDisplay("Unable to calculate");
        querySubContractRsp.setTlTotalAmount(BigDecimal.ZERO);
        if (objTLAmountDTO != null && objTLAmountDTO.getAmount() != null) {
            DecimalFormat df = new DecimalFormat(",###,###.00");
            querySubContractRsp.setTlTotalAmount(objTLAmountDTO.getAmount());
            String amountText=objTLAmountDTO.getAmount().compareTo(BigDecimal.ZERO)==0?"0.00":df.format(objTLAmountDTO.getAmount());
            querySubContractRsp.setTlTotalAmountDisplay(objTLAmountDTO.getCurrencyCodeDisplay() + amountText);
        }
    }

    private List<SubcontractDTO> transOrderTestLine(List<OrderTestLineListDTO> orderTestLinePageListDTOS) {
        List<SubcontractDTO> list = new ArrayList<>();
        for (OrderTestLineListDTO orderTestLinePageListDTO : orderTestLinePageListDTOS) {
            SubcontractDTO subcontractDTO = new SubcontractDTO();
            //1:EN 2:CHI
            subcontractDTO.setSubContractNo(orderTestLinePageListDTO.getSubContractNo());
            subcontractDTO.setTestLineId(Func.isEmpty(orderTestLinePageListDTO.getTestLineId()) ? "" : orderTestLinePageListDTO.getTestLineId().toString());
            subcontractDTO.setLabSection(orderTestLinePageListDTO.getLabSectionName());
            subcontractDTO.setSubContractId(orderTestLinePageListDTO.getSubContractID());
            subcontractDTO.setSubContractName(orderTestLinePageListDTO.getSubContractLabName());
            subcontractDTO.setSubContractLabId(orderTestLinePageListDTO.getSubContractLabId());
            subcontractDTO.setSubContractLabCode(orderTestLinePageListDTO.getSubContractLabCode());
            subcontractDTO.setTestLineInstanceID(orderTestLinePageListDTO.getTestLineInstanceId());
            subcontractDTO.setProductLineAbbr(orderTestLinePageListDTO.getProductLineCode());
            subcontractDTO.setCitationBaseId(orderTestLinePageListDTO.getCitationBaseId());
            subcontractDTO.setCustomerTestLineName(orderTestLinePageListDTO.getCustomerTestLineName());
            subcontractDTO.setTestLineType(orderTestLinePageListDTO.getTestLineType());
            subcontractDTO.setStandardSectionName(orderTestLinePageListDTO.getCitationSectionName());
            subcontractDTO.setTestItem(orderTestLinePageListDTO.getTestItem());
            subcontractDTO.setTestStandard(orderTestLinePageListDTO.getTestStandard());
            subcontractDTO.setTestLineVersionId(Func.isEmpty(orderTestLinePageListDTO.getTestLineVersionId())?null:orderTestLinePageListDTO.getTestLineVersionId().toString());
            subcontractDTO.setPpNo(orderTestLinePageListDTO.getPpNo());
            subcontractDTO.setPpName(orderTestLinePageListDTO.getPpName());
            list.add(subcontractDTO);
        }
        return list;
    }

    @Override
    public CustomResult querySubContract(GpnQuerySubContractReq req) {
        CustomResult result = new CustomResult();
        SubContractPO subContractPO = subContractMapper.selectByPrimaryKey(req.getSubContractId());
        String subContractNo = subContractPO.getSubContractNo();
        String orderNo = subContractPO.getOrderNo();
        GeneralOrderInstanceInfoPO generalOrderInstanceInfoPO = orderMapper.getOrderInfo(orderNo);
        SubContractExternalRelationshipPO searchPO = new SubContractExternalRelationshipPO();
        searchPO.setSubContractNo(subContractNo);
        List<SubContractExternalRelationshipPO> subContractSlimJobPOS = subContractExternalRelService.getSubContractExternalRels(searchPO).getData();
        if (subContractSlimJobPOS.size() > 0) {
            String collect = subContractSlimJobPOS.stream().map(SubContractExternalRelationshipPO::getExternalNo).collect(Collectors.joining(","));
            subContractPO.setSlimJobNo(collect);
        }
//        OrderIdReq orderIdReq = new OrderIdReq();
//        orderIdReq.setOrderNo(req.getOrderNo());
//        BaseResponse<MasterOrderInfoDTO> orderInfo = orderFacade.getOrderInfo(orderIdReq);
//        String serviceTypeTAT=null;
//        if(orderInfo.getStatus()==200){
//            MasterOrderInfoDTO data = orderInfo.getData();
//            serviceTypeTAT=data.getServiceTypeTAT();
//        }
//        subContractPO.setSubContractServiceType(StringUtils.isBlank(subContractPO.getSubContractServiceType())
//                ? serviceTypeTAT : subContractPO.getSubContractServiceType());

        //获取Subcontract Requirement
        SubcontractRequirementInfo subcontractRequirementInfo = subcontractRequirementMapper.getSubcontractRequirementBySubcontractId(req.getSubContractId());
        if (Func.isNull(subcontractRequirementInfo)) subcontractRequirementInfo = new SubcontractRequirementInfo();
        subcontractRequirementInfo.setSubcontractRequirementContactsInfos(subcontractRequirementMapper.getSubcontractRequirementContactsBySubcontractId(req.getSubContractId()));
        subcontractRequirementService.setSubcontractRequirementContactsInfo(subcontractRequirementInfo.getSubcontractRequirementContactsInfos(), subcontractRequirementInfo);

        GpnSubContractRsp gpnSubContractRsp = new GpnSubContractRsp();
        BeanUtil.copyProperties(subContractPO, gpnSubContractRsp);
        gpnSubContractRsp.setId(subContractPO.getID());

        //
        OrderIdReq orderIdReq = new OrderIdReq();
        orderIdReq.setOrderNo(orderNo);
        TestRequestRsp testRequestRsp = orderClient.getTestRequestByOrderNo(orderIdReq);
        subcontractRequirementInfo.setReportLanguage((Func.isNotEmpty(testRequestRsp) && Func.isNotEmpty(testRequestRsp.getReportLanguage()))?Func.toStr(testRequestRsp.getReportLanguage()):ReportLanguage.EnglishReportOnly.getCode());
        gpnSubContractRsp.setOrderId((Func.isNull(testRequestRsp) || Func.isEmpty(testRequestRsp)) ? null : testRequestRsp.getOrderId());
        gpnSubContractRsp.setGeneralOrderId(generalOrderInstanceInfoPO.getID());
        gpnSubContractRsp.setOrderNo(orderNo);
        gpnSubContractRsp.setSubcontractRequirement(subcontractRequirementInfo);
        gpnSubContractRsp.setSubContractType(subContractSlimJobPOS.size() > 0 ? subContractSlimJobPOS.get(0).getSubContractType() : null);

        // 查询相关的报告信息
        GetReportListInfo getReportListInfo = new GetReportListInfo();
        getReportListInfo.setOrderNo(orderNo);
        List<GetReportInfo> reportInfos = reportMapper.getReportList(getReportListInfo);
        //设置外部单号
        List<ExternalReportNoRsp> externalReportNoList = null;
        if (Func.isNotEmpty(reportInfos)) {
            List<String> reportNos = reportInfos.stream().map(GetReportInfo::getReportNo).filter(i -> Func.isNotEmpty(i)).distinct().collect(Collectors.toList());
            if (Func.isNotEmpty(reportNos)) {
                externalReportNoList = orderClient.getExternalReportNoByReportNo(reportNos, ProductLineContextHolder.getProductLineCode());
            }
        }
        for (GetReportInfo reportInfo : reportInfos) {
            if(Func.isNotEmpty(reportInfo.getActualReportNo())){
                reportInfo.setExternalReportNo(reportInfo.getActualReportNo());
            }else{
                if (Func.isNotEmpty(externalReportNoList)) {
                    String externalSubReportNo = externalReportNoList.stream().filter(i -> Func.equals(i.getReportNo(), reportInfo.getReportNo())).findFirst().orElse(new ExternalReportNoRsp()).getExternalReportNo();
                    if (Func.isNotEmpty(externalSubReportNo)) {
                        reportInfo.setExternalReportNo(externalSubReportNo);
                    } else {
                        reportInfo.setExternalReportNo(reportInfo.getReportNo());
                    }
                } else {
                    reportInfo.setExternalReportNo(reportInfo.getReportNo());
                }
            }
        }
        gpnSubContractRsp.setReports(reportInfos);
        List<GetReportInfo> currentSubContractReports = new ArrayList<>();
        List<SubReportPO> subReportPOList = subReportService.querySubReportBySubcontractNo(subContractPO.getSubContractNo()).getData();
        if(Func.isNotEmpty(subReportPOList)){
            List<String> reportNoList = subReportPOList.stream().map(SubReportPO::getReportNo).filter(Func::isNotEmpty).distinct().collect(Collectors.toList());
            currentSubContractReports = reportInfos.stream().filter(item->reportNoList.contains(item.getReportNo())).collect(Collectors.toList());;
        }
        gpnSubContractRsp.setCurrentSubContractReports(currentSubContractReports);
        // 查询主单状态
        OrderInfoDto orderInfoDto = orderClient.getOrderInfoByOrderNo(orderNo);
        if (Func.isNotEmpty(orderInfoDto)) {
            gpnSubContractRsp.setOrderStatus(orderInfoDto.getOrderStatus());
            gpnSubContractRsp.setOrderTat(orderInfoDto.getTAT());
            gpnSubContractRsp.setOrderServiceType(orderInfoDto.getServiceLevel());
        }
        result.setData(gpnSubContractRsp);
        result.setSuccess(true);
        return result;
    }

    @Override
//    @BizLog(bizType = BizLogConstant.SUBCONTRACT_OPERATION_HOSTORY, operType = "Cancel")
    public CustomResult cancel(GpnQuerySubContractReq req) {
        CustomResult result = new CustomResult();
        if (StringUtils.isBlank(req.getSubContractId())) {
            result.setSuccess(false);
            return result;
        }
        SubContractPO subContractPO = subContractMapper.selectByPrimaryKey(req.getSubContractId());
        Integer oldStatus = subContractPO.getStatus();
        Integer subcontractOrder = subContractPO.getSubContractOrder();
        //分包单允许删除逻辑
        //订单状态不等于 Closed、Pending、Cancelled、Completed
        //分包单状态 New 、Testing（并且分包单类型为Slim、StarLims、Dml）
        if(!SubContractStatus.check(oldStatus,SubContractStatus.NEW,SubContractStatus.TESTING)){
            result.setMsg("当前分包单状态不允许执行分包单cancel");
            result.setSuccess(false);
            return result;
        }
        if(SubContractStatus.check(oldStatus,SubContractStatus.TESTING) &&
                !(SubContractType.check(subcontractOrder,SubContractType.ToSlim)||
                        SubContractType.check(subcontractOrder,SubContractType.ToStarLims)||
                        SubContractType.check(subcontractOrder,SubContractType.ToDML))){
            result.setMsg("当前分包单类型不允许执行分包单cancel");
            result.setSuccess(false);
            return result;

        }
        OrderIdReq orderIdReq = new OrderIdReq();
        orderIdReq.setOrderNo(subContractPO.getOrderNo());
        orderIdReq.setProductLineCode(ProductLineContextHolder.getProductLineCode());
        BaseResponse<OrderAllDTO> orderForPe = gpoOrderFacade.getOrderForPe(orderIdReq);
        if(Func.isEmpty(orderForPe) || Func.isEmpty(orderForPe.getData())){
            result.setMsg("订单信息查询异常");
            result.setSuccess(false);
            return result;
        }
        OrderAllDTO orderAllDTO = orderForPe.getData();
        Integer orderStatus = orderAllDTO.getOrderStatus();
        if(com.sgs.preorder.facade.model.enums.OrderStatus.checkStatus(orderStatus,
                com.sgs.preorder.facade.model.enums.OrderStatus.Closed,
                com.sgs.preorder.facade.model.enums.OrderStatus.Pending,
                com.sgs.preorder.facade.model.enums.OrderStatus.Cancelled,
                com.sgs.preorder.facade.model.enums.OrderStatus.Completed)){
            result.setMsg("当前订单状态不允许执行分包单cancel");
            result.setSuccess(false);
            return result;
        }
        //内部分包校验执行开始
        //1 先校验当前order的preorder端的状态
        String subContractNo = subContractPO.getSubContractNo();
        Integer subContractOrder = subContractPO.getSubContractOrder();
        CopyOrderUtils.setSourceProductLineCode(ProductLineContextHolder.getProductLineCode());//分包方
        String subContractLabCode = subContractPO.getSubContractLabCode();
        String[] s = subContractLabCode.split(" ");
        CopyOrderUtils.setTargetProductLineCode(s[1]);//接包方
        //subContractOrder=1 是内部分包
        UserInfo userInfo = tokenClient.getUser();
        if (userInfo == null) {
            throw new RuntimeException("Get User Fail!");
        }
        //根据subcontractNo找slimjob
        SubContractExternalRelationshipPO searchPO = new SubContractExternalRelationshipPO();
        searchPO.setSubContractNo(subContractNo);
        BaseResponse<List<SubContractExternalRelationshipPO>> subContractExternalRelationship = subContractExternalRelService.getSubContractExternalRels(searchPO);
        SubContractExternalRelationshipPO subContractSlimJob = null;
        if (subContractExternalRelationship.isSuccess() && Func.isNotEmpty(subContractExternalRelationship.getData()) && subContractExternalRelationship.getData().size() > 0) {
            subContractSlimJob = subContractExternalRelationship.getData().get(0);
        }
        if (subContractOrder != null && subContractOrder.compareTo(1) == 0) {
            //校验当前分包单的状态

            if (subContractSlimJob == null) {
                logger.info("subContractNo：{} cancel操作，确定是内部分包，但是找不到对应的slimJobNo(orderNo)数据", subContractNo);
                throw new RuntimeException("Query subContract info fail!");
            }
            //找到内部分包对应的orderNo
           /* OrderIdReq orderIdReq = new OrderIdReq();
            orderIdReq.setOrderNo(subContractSlimJob.getExternalNo());
            orderIdReq.setProductLineCode(ProductLineContextHolder.getProductLineCode());
            BaseResponse<Integer> orderStatusByOrderNo = orderFacade.getOrderStatusByOrderNo(orderIdReq);
            Integer integer = orderStatusByOrderNo.getData();
            if(integer!=null && !Lists.newArrayList(1,3,8).contains(integer.intValue())){
                throw new RuntimeException("Order Status is not New、Confirm、Testing");
            }*/

            //请求preorder 准备cancel order
            CopyOrderUtils.changeDbToTarget();
            cancelSubOrder(subContractPO, subContractSlimJob.getExternalNo(), userInfo);
        }
        //删除tb_subcontract_external_relationship
        if (Func.isNotEmpty(subContractSlimJob)) {
            subContractExtMapper.delSubContractSlimJobId(subContractSlimJob.getID());
        }
        //内部分包校验执行结束↑
        CopyOrderUtils.changeDbToSource();
        // 将SubContract状态改为Cancel
        subContractPO.setStatus(SubContractStatus.CANCELLED.getCode());
        subContractExtMapper.saveOrUpdate(subContractPO);
        //保存bizLog，subcontract状态变化
        try {
            BizLogInfo bizLog = new BizLogInfo();
            bizLog.setBizOpType(BizLogConstant.SUBCONTRACT_STATUS_CHANGE_HISTORY);
            bizLog.setLab(userInfo.getCurrentLabCode().split(" ")[0]);
            bizLog.setBu(userInfo.getCurrentLabCode().split(" ")[1]);
            bizLog.setOpUser(userInfo.getRegionAccount());
            bizLog.setBizId(subContractPO.getSubContractNo());
            bizLog.setOpType(OperationTypeEnums.CancelSubcontract.getValue());
            bizLog.setNewVal(SubcontractStatus.Cancelled.getCode());
            bizLog.setOriginalVal(oldStatus);
            logger.info("[{}],bizlog:Subcontract_Status_Change_log,Cancelled,{}", subContractPO.getSubContractNo(), JSON.toJSONString(bizLog));
            bizLogClient.doSend(bizLog);
        } catch (Exception e) {
            logger.error("subContract status log err:{}", e);
        }
        //保存bizLog history
        try {
            BizLogInfo bizLog = new BizLogInfo();
            bizLog.setBizOpType(BizLogConstant.SUBCONTRACT_OPERATION_HOSTORY);
            bizLog.setLab(Func.isNotEmpty(orderAllDTO.getLocationCode()) ? orderAllDTO.getLocationCode() : userInfo.getCurrentLabCode().split(" ")[0]);
            bizLog.setBu(Func.isNotEmpty(orderAllDTO.getBUCode()) ? orderAllDTO.getBUCode() : userInfo.getCurrentLabCode().split(" ")[1]);
            bizLog.setOpUser(userInfo.getRegionAccount());
            bizLog.setBizId(subContractPO.getOrderNo());
            bizLog.setOpType(OperationTypeEnums.CancelSubcontract.getValue());
            bizLog.setNewVal(SubcontractStatus.Cancelled.message());
            if (Func.isNotEmpty(oldStatus)) {
                bizLog.setOriginalVal(SubcontractStatus.getCode(oldStatus).message());
            }
            logger.info("[{}],bizlog:SUBCONTRACT_OPERATION_HOSTORY,Cancelled,{}", subContractPO.getSubContractNo(), JSON.toJSONString(bizLog));
            bizLogClient.doSend(bizLog);
        } catch (Exception e) {
            logger.error("subContract cancel history bizlog err:{}", e);
        }

        // 删除tb_sub_contract_test_line_mapping相关数据
        SubContractTestLineMappingExample subContractTestLineMappingExample = new SubContractTestLineMappingExample();
        subContractTestLineMappingExample.createCriteria().andSubContractIDEqualTo(req.getSubContractId());
        // 取消tb_test_line_instance subContract 状态
        List<SubContractTestLineMappingPO> subContractTestLineMappingS = subContractTestLineMappingMapper.selectByExample(subContractTestLineMappingExample);
        if (Func.isNotEmpty(subContractTestLineMappingS)) {
            List<String> testLines = subContractTestLineMappingS.stream().map(m -> m.getTestLineInstanceID()).collect(Collectors.toList());
            testLineInstanceExtMapper.updateTestLineTypeCancelBatch(testLines, TestLineType.SubContractOrder.getType());
            testLineInstanceExtMapper.updateTestLineStatusBatch(testLines, Typing.getStatus());
        }
        subContractTestLineMappingMapper.deleteByExample(subContractTestLineMappingExample);
        //DIG-3337  UPDATE SLIM临表的syncStatus=0 这一块的整体功能未完成，暂时先注释
//        slimSubContractExtMapper.updateSlimSubcontractSyncStatus(subContractPO.getSubContractNo());


        SubContractPO subContract = subContractMapper.selectByPrimaryKey(subContractPO.getID());
        TodoStatusReq todoStatusReq = new TodoStatusReq();
        todoStatusReq.setObjectId(subContract.getID());
        todoStatusReq.setObjectNo(subContract.getSubContractNo());
        todoStatusReq.setStatus(TodoStatus.Canceled.getCode());
        todoStatusReq.setSgsToken(tokenClient.getToken());
        toDoListFacade.updateTodoStatus(todoStatusReq);
        BizLogHelper.setValue(subContractPO.getOrderNo(), subContract.getSubContractNo());
        BizLogHelper.setLabCode(orderForPe.getData().getBUCode(), orderForPe.getData().getLocationCode());
        //记录operationHistory
        //保存operationHistory
        InsertOperationHistoryReq insertOperationHistoryReq = new InsertOperationHistoryReq();
        insertOperationHistoryReq.setObjectId(req.getSubContractId());
        insertOperationHistoryReq.setObjectNo(subContractNo);
        insertOperationHistoryReq.setOperationType(OperationTypeEnums.CancelSubcontract.getStatus());
        insertOperationHistoryReq.setReasonType(req.getReasonTypeInfo());
        insertOperationHistoryReq.setRemark(req.getRemark());
        insertOperationHistoryReq.setToken(tokenClient.getToken());
        BaseResponse baseResponse = operationHistoryFacade.operationHistorySave(insertOperationHistoryReq);
        result.setSuccess(true);
        return result;
    }


    @Override
    public CustomResult toSlim(GpnSubContractReq req) {
        UserInfo user = tokenClient.getUser();
        SubContractExample subContractExample = new SubContractExample();
        subContractExample.createCriteria().andSubContractNoEqualTo(req.getSubContractNo());
        List<SubContractPO> subContractPOS = subContractMapper.selectByExample(subContractExample);
        SubContractPO subContractPO = subContractPOS.get(0);
        SubContractReq subContractReq = new SubContractReq();
        subContractReq.setLabCode(user.getCurrentLabCode());
        subContractReq.setSubContractLabCode(subContractPO.getSubContractLabCode());
        BeanUtils.copyProperties(req, subContractReq);
        CustomResult checkResult = this.toSlimCheck(subContractPO);
        if (!checkResult.isSuccess()) {
            return checkResult;
        }
//        CustomResult result = subContractService.toSlim(subContractReq);
        log.info("开始ToSlim, 参数：{}", JSON.toJSONString(req));
        // 更新表的状态...
        CustomResult result = this.doGenerteAndUploadXml(subContractReq);

        if (result.isSuccess()) {
            subContractService.updateStatus(subContractReq);
            subContractPO.setSyncStatus(1);
            subContractMapper.updateByPrimaryKeySelective(subContractPO);
            OrderInfoDto orderInfo = orderClient.getOrderInfoByOrderNo(req.getOrderNo());
            BizLogInfo bizLog = new BizLogInfo();
            bizLog.setBizId(req.getOrderNo());
            bizLog.setBu((Func.isNotEmpty(orderInfo) && Func.isNotEmpty(orderInfo.getBUCode())) ? orderInfo.getBUCode() : user.getCurrentLabCode().split(" ")[1]);
            bizLog.setLab((Func.isNotEmpty(orderInfo) && Func.isNotEmpty(orderInfo.getLocationCode())) ? orderInfo.getLocationCode() : user.getCurrentLabCode().split(" ")[0]);
            bizLog.setBizOpType(BizLogConstant.SUBCONTRACT_OPERATION_HOSTORY);
            bizLog.setOpType("To Slim");
            bizLog.setOpUser(user.getRegionAccount());
            bizLog.setNewVal(req.getSubContractNo());
            bizLogClient.doSend(bizLog);
        }
        return result;
    }
    public CustomResult toSlimCheck(SubContractPO subContractPO){
        CustomResult result = new CustomResult();
        BuParamReq buParamReq = new BuParamReq();
        buParamReq.setGroupCode(Constants.BU_PARAM.SLIM.GROUP);
        buParamReq.setParamCode(Constants.BU_PARAM.SLIM.CODE.NEED_TO_SLIM);
        buParamReq.setProductLineCode(ProductLineContextHolder.getProductLineCode());
        BuParamValueRsp buParam = frameWorkClient.getBuParam(buParamReq);
        if(Func.isEmpty(subContractPO)){
            result.setMsg("未找到有效的Subcontract");
            result.setSuccess(false);
            return result;
        }
        if(!StringUtils.containsIgnoreCase(subContractPO.getSubContractLabName(),"Central Chemical Lab")){
            result.setMsg("SubcontractTo 非CCL,无法ToSlim");
            result.setSuccess(false);
            return result;
        }
        if(Func.isNotEmpty(buParam) && !Func.equals(buParam.getParamValue(),"1")){
            result.setMsg("未配置开启ToSlim");
            result.setSuccess(false);
            return result;
        }
        return CustomResult.newSuccessInstance();
    }
    public CustomResult doGenerteAndUploadXml(SubContractReq req) {
        CustomResult result = new CustomResult();
        try {
            result = this.generateXml(result, req);
        } catch (Exception ex) {
            log.error("订单{}生成xml异常：{}", req.getOrderNo(), ex);
            result.setMsg("To Slim失败：" + ex.getMessage());
            result.setSuccess(false);
            return result;
        }
        if (result.getData() == null) {
            result.setSuccess(false);
            result.setMsg(Func.isNotEmpty(result.getMsg())?result.getMsg():"To SLIM失败，请稍后重试。");
            return result;
        }

        // 调用toSlim接口
        try {
            Map<String, Object> reqData = new HashMap<>();
            reqData.put("data", result.getData());
            log.info("toSlim param:{}", JSON.toJSONString(reqData));
            //记录请求及响应信息到systemLog
            try {
                SystemLog systemLog = new SystemLog();
                systemLog.setObjectType("subContract");
                systemLog.setObjectNo(req.getSubContractNo());
                systemLog.setProductLineCode(ProductLineContextHolder.getProductLineCode());
                systemLog.setType(SystemLogType.API.getType());
                systemLog.setRemark("SubContract To Slim Request Param");
                systemLog.setResponse(JSON.toJSONString(reqData));
                systemLog.setOperationType("SubContract To Slim");
                systemLog.setLocationCode(req.getLabCode().split(" ")[0]);
                systemLog.setCreateBy("system");
                systemLogHelper.save(systemLog);
            } catch (Exception e) {
                logger.error("record SubContract ToSlim Request to systemLog error:{}", e);
            }

            String toSlimUrl = String.format("%s/openapi/Sync/process", interfaceConfig.getLocalIlayerUrl());
            String resultObj = HttpClientUtil.doPost(toSlimUrl, reqData, String.class);
            log.info("toSlim result:{}", resultObj);
            try {
                SystemLog systemLog = new SystemLog();
                systemLog.setObjectType("subContract");
                systemLog.setObjectNo(req.getSubContractNo());
                systemLog.setProductLineCode(ProductLineContextHolder.getProductLineCode());
                systemLog.setType(SystemLogType.API.getType());
                systemLog.setRemark("SubContract To Slim Response");
                systemLog.setResponse(JSON.toJSONString(reqData));
                systemLog.setOperationType("SubContract To Slim Response");
                systemLog.setLocationCode(req.getLabCode().split(" ")[0]);
                systemLog.setCreateBy("system");
                systemLogHelper.save(systemLog);
            } catch (Exception e) {
                logger.error("record SubContract ToSlim Response to systemLog error:{}", e);
            }
            JSONObject resultJosn = JSONObject.parseObject(resultObj);
            if (resultJosn == null || resultJosn.isEmpty()) {
                result.setSuccess(false);
                result.setMsg("To Slim接口返回数据为空");
            }
            int status = (int) resultJosn.get("status");
            if (status != 200) {
                result.setSuccess(false);
                result.setMsg("To Slim失败:" + resultJosn.get("message").toString());
            }
            return result;
        } catch (Exception e) {
            log.error("subContractNo:{} to Slim接口异常:{}", req.getSubContractNo(), e);
            result.setSuccess(false);
            result.setMsg("调用to Slim接口异常：" + e.getMessage());
            return result;
        }
    }


    /**
     * 生成SLIM XML
     *
     * @param result
     * @param req
     * @return
     */
    private CustomResult generateXml(CustomResult result, SubContractReq req) {
//        result.setSuccess(true);
        // 创建JOB节点实体对象
        Job job = new Job();
        List<Sample> sampleList = Lists.newArrayList();
        job.setSamples(sampleList);
        // 创建JOBBIOFIELD节点实体对象
        List<JobBioField> jobBioFieldList = Lists.newArrayList();


        Map<String, TestSampleSimplifyInfo> testSampleIds = Maps.newHashMap();
        List<TestSampleSimplifyInfo> allTestSamples = Lists.newArrayList();
        if (Func.isNotEmpty(req.getSubContractNo())) {
            List<SubContractTestLineInfo> subContractTestLineList = subContractExtMapper.getSubContractTestLineMappingList(req.getSubContractNo());
            if (Func.isNotEmpty(subContractTestLineList)) {
                List<String> testLineIds = subContractTestLineList.stream().map(tl -> tl.getTestLineInstanceId()).collect(Collectors.toList());
                allTestSamples = testSampleMapper.getSampleInfoListByTl(req.getOrderNo(), testLineIds);
            }
        } else {
            allTestSamples = testSampleMapper.getSampleInfoListByOrderNo(req.getOrderNo());
        }
        if (allTestSamples.isEmpty()) {
            result.setSuccess(false);
            this.updateErrorInfo(req, null, "分包的Test Line未分配样品，不允许To SLIM。");
            result.setMsg("分包的Test Line未分配样品，不允许To SLIM。");
            return result;
        }
        allTestSamples.sort(new TestSampleSimplifyComparator(true));

        for (TestSampleSimplifyInfo testSample : allTestSamples) {
            testSampleIds.put(testSample.getSampleId(), testSample);
            if (!SampleType.check(testSample.getSampleType(), SampleType.MixSample)) {
                continue;
            }
            List<String> sampleGroupIds = testSample.getSampleGroupIds();
            if (sampleGroupIds == null || sampleGroupIds.isEmpty()) {
                continue;
            }
            List<TestSampleSimplifyInfo> testSampleGroupIds = Lists.newArrayList();
            for (String sampleGroupId : sampleGroupIds) {
                TestSampleSimplifyInfo testSampleGroup = allTestSamples.stream().filter(ts -> StringUtils.equalsIgnoreCase(ts.getSampleId(), sampleGroupId)).findFirst().get();
                if (testSampleGroup == null) {
                    continue;
                }
                testSampleGroupIds.add(testSampleGroup);
            }
            testSampleGroupIds.sort(new TestSampleSimplifyComparator(true));
            testSample.setSampleGroupNos(testSampleGroupIds.stream().map(x -> (SampleType.check(testSample.getSampleType(), SampleType.MixSample) && StringUtils.equalsIgnoreCase(testSample.getCategory(), SampleType.MixSample.getCategoryChem()) ? x.getSampleNo().substring(1) : x.getSampleNo())).collect(Collectors.toList()));
        }

        Map<String, TestSampleSimplifyInfo> mixShareSampleMaps = Maps.newHashMap();
        Map<String, TestSampleSimplifyInfo> testSampleMaps = Maps.newHashMap();
        boolean hasOriginalSample = false;
        // 为了去判断 A+B 和 B 的场景
        for (TestSampleSimplifyInfo testSample : allTestSamples) {
           /* if (!testSampleIds.containsKey(subContract.getSampleId())) {
                continue;
            }*/
            if (SampleType.check(testSample.getSampleType(), SampleType.OriginalSample)) {
                hasOriginalSample = true;
            }
            List<String> sampleGroupIds = testSample.getSampleGroupIds();
            if (sampleGroupIds == null || sampleGroupIds.isEmpty()) {
                testSampleMaps.put(testSample.getSampleId(), testSample);
                continue;
            }
            mixShareSampleMaps.put(testSample.getSampleId(), testSample);

            for (String sampleGroupId : sampleGroupIds) {
                testSample = testSampleIds.get(sampleGroupId);
                if (testSample == null) {
                    continue;
                }
                if (SampleType.check(testSample.getSampleType(), SampleType.OriginalSample)) {
                    hasOriginalSample = true;
                }
                testSampleMaps.put(sampleGroupId, testSample);
            }
        }


        List<TestSampleSimplifyInfo> testSamples = Lists.newArrayList(testSampleMaps.values());
        testSamples.sort(new TestSampleSimplifyComparator(true));

        // TODO 这是什么鬼逻辑 testSamples.get(0).getSampleType()
        int sampleType = testSamples.get(0).getSampleType(); //原则上，传过来的样品类型保持一致
        for (TestSampleSimplifyInfo testSamplePO : testSamples) {
            if (SampleType.check(testSamplePO.getSampleType(), SampleType.SubSample)) {
                result.setMsg("分包的Sample中存在多个类型的样品（样品类型：原样，子样，混测样），不允许To SLIM。");
                result.setSuccess(false);
                this.updateErrorInfo(req, null, "分包的Sample中存在多个类型的样品（样品类型：原样，子样，混测样），不允许To SLIM。");
                return result;
            }
        }
        for (TestSampleSimplifyInfo testSamplePO : testSamples) {
            if (testSamplePO.getSampleType() != sampleType) {
                result.setMsg("分包的Sample中存在多个类型的样品（样品类型：原样，子样，混测样），不允许To SLIM。");
                this.updateErrorInfo(req, null, "分包的Sample中存在多个类型的样品（样品类型：原样，子样，混测样），不允许To SLIM。");
                result.setSuccess(false);
                return result;
            }
        }
//        if (StringUtils.isNotBlank(result.getMsg())) {
//            result.setSuccess(false);
//            this.updateErrorInfo(req, null, result.getMsg().split("null,")[1]);
//            return result;
//        }
//        SlimSubcontractInfo slimSubcontractPO1 = StringUtil.checkNull(slimSubcontracts.get(0));

        CustomResult buildResult = buildSlimJob(job, jobBioFieldList, req);
        if(!buildResult.isSuccess()){
            return buildResult;
        }
        job.setJobBIOField(jobBioFieldList);
       /* List<String> subContractSamples = slimSubcontracts.stream().map(x -> x.getSampleNo()).collect(Collectors.toList());

        List<String> allSubContractSampleNos = Stream.concat(flatSamples.stream(), subContractSamples.stream())
                .distinct().collect(Collectors.toList());
        List<TestSampleInfoPO> allSubContractSamplePOs = testSampleMapper.getSamplesByOrderNoAndSampleNos(req.getOrderNo(), allSubContractSampleNos);
*/
        // 如果to slim中有原样，则需要全排列
        /*List<TestSampleInfoPO> allSamplePOs = Lists.newArrayList();
        boolean hasOriginalSample = allSubContractSamplePOs.stream().anyMatch(p -> p.getSampleType() == SampleTypeConsts.SAMPLE_TYPE_ORIGINAL.intValue());
        if(hasOriginalSample){
            allSamplePOs = testSampleMapper.getSampleByOrderNo(req.getOrderNo());
        }*/
        if (!hasOriginalSample) {
            allTestSamples = Lists.newArrayList();
        }
        List<TestSampleSimplifyInfo> allSubContractSamplePOs = Lists.newArrayList(testSamples);
        if (!mixShareSampleMaps.isEmpty()) {
            allSubContractSamplePOs.addAll(mixShareSampleMaps.values());
        }
        for (int rowIndex = 0; rowIndex < allSubContractSamplePOs.size(); rowIndex++) {
            TestSampleSimplifyInfo subContractSample = allSubContractSamplePOs.get(rowIndex);
            switch (SampleType.findType(subContractSample.getSampleType())) {
                case OriginalSample:
                    for (int sampleIndex = 0; sampleIndex < allTestSamples.size(); sampleIndex++) {
                        if (StringUtils.equalsIgnoreCase(allTestSamples.get(sampleIndex).getSampleId(), subContractSample.getSampleId())) {
                            subContractSample.setIdent(StringUtils.leftPad(Integer.toString(sampleIndex + 1), 3, "0"));
                        }
                    }
                    break;
                case Sample:
                    if (StringUtils.equalsIgnoreCase(subContractSample.getCategory(), SampleCategoryConsts.SAMPLE_CATEGORY_C)) {
                        //化学样去C
                        subContractSample.setIdent(StringUtils.leftPad(subContractSample.getSampleNo().substring(1), 3, "0"));
                    } else {
                        subContractSample.setIdent(StringUtils.leftPad(subContractSample.getSampleNo(), 3, "0"));
                    }
                    break;
                default:
                    subContractSample.setIdent(StringUtils.leftPad(Integer.toString(rowIndex + 1), 3, "0"));
                    break;
            }
        }
        for (int i = 1; i <= allSubContractSamplePOs.size(); i++) {
            TestSampleSimplifyInfo testSample = allSubContractSamplePOs.get(i - 1);
            if (!SampleType.check(testSample.getSampleType(), SampleType.MixSample)) {
                continue;
            }
            List<String> sampleGroupIds = testSample.getSampleGroupIds();
            if (sampleGroupIds == null || sampleGroupIds.isEmpty()) {
                continue;
            }
            List<TestSampleSimplifyInfo> sampleGroups = Lists.newArrayList();
            sampleGroupIds.forEach(sampleGroupId -> {
                TestSampleSimplifyInfo testSampleGroup = testSampleIds.get(sampleGroupId);
                if (testSampleGroup == null) {
                    return;
                }
                sampleGroups.add(testSampleGroup);
            });
            sampleGroups.sort(new TestSampleSimplifyComparator(true));
            testSample.setIdent(StringUtils.join(sampleGroups.stream().map(sampleGroup -> sampleGroup.getIdent()).collect(Collectors.toList()), "+"));
        }

        buildSlimSampleList(allSubContractSamplePOs, job, req);

//        if (!result.isSuccess()) {
//            this.updateErrorInfo(req, null, result.getMsg());
//            return result;
//        }
        ToSlimReq toSlimReq = new ToSlimReq();
        OrderAllDTO gpoOrderInfo = this.getGpoOrderInfo(req.getOrderNo());
        toSlimReq.setBU(gpoOrderInfo.getBUCode());
        toSlimReq.setCustomerGroupCode("");
        toSlimReq.setCustomerGroupName("");
        toSlimReq.setDFFFormID("");
        toSlimReq.setDFFFormName("");
        toSlimReq.setObjectNumber(req.getSubContractNo());
        toSlimReq.setSourceAppID(Constants.TO_SLIM_SOURCE_APP_ID);
        toSlimReq.setSourceDataSchema(Constants.TO_SLIM_SOURCE_DATA_SCHEMA);
        toSlimReq.setTargetAppID(Constants.TO_SLIM_TARGET_APP_ID);
        toSlimReq.setTargetDataSchema(Constants.TO_SLIM_TARGET_DATA_SCHEMA);
        toSlimReq.setObjectCode(req.getSubContractLabCode());
        ToSlimBody toSlimBody = new ToSlimBody();
        toSlimBody.setJob(job);
        toSlimReq.setBody(toSlimBody);
        result.setData(toSlimReq);
        result.setSuccess(true);
        return result;
    }


    private OrderAllDTO getGpoOrderInfo(String orderNo) {
        OrderIdReq orderIdReq = new OrderIdReq();
        orderIdReq.setOrderNo(orderNo);
        BaseResponse<OrderAllDTO> orderForPe = orderFacade.getOrderForPe(orderIdReq);
        if (Func.isNotEmpty(orderForPe) && Func.isNotEmpty(orderForPe.getData())) {
            return orderForPe.getData();
        } else {
            return null;
        }
    }

    /**
     * 构造SlimSampleList
     *
     * @param allSubContractSamplePOs 分包单中所有samplePO
     * @param job
     */
    private void buildSlimSampleList(List<TestSampleSimplifyInfo> allSubContractSamplePOs, Job job, SubContractReq req) {
        allSubContractSamplePOs.sort(new TestSampleSimplifyComparator(true));
        for (TestSampleSimplifyInfo currentTestSamplePO : allSubContractSamplePOs) {
            Sample sample = new Sample();
            sample.setDescription("Specimen");
            sample.setSampleIdent(currentTestSamplePO.getIdent());
            sample.setIdent(sample.getSampleIdent());
            buildSlimSample(sample, currentTestSamplePO, req);
            job.getSamples().add(sample);
        }
    }

    /**
     * 构造slim sample
     */
    private void buildSlimSample(Sample sample, TestSampleSimplifyInfo testSamplePO, SubContractReq req) {
        OrderAllDTO orderAllDTO = this.getOrderForPe(req.getOrderNo());
        List<ProductInstanceDTO> productInstanceDTOList = new ArrayList<>();
        String reportLanguage = "";
        if (Func.isNotEmpty(orderAllDTO)) {
            OrderIdReq orderIdReq = new OrderIdReq();
            orderIdReq.setProductLineCode(ProductLineContextHolder.getProductLineCode());
            orderIdReq.setOrderId(orderAllDTO.getOrderId());
            BaseResponse<List<ProductInstanceDTO>> ProductInstanceRsp = orderFacade.queryProductInstaceForPe(orderIdReq);
            if (Func.isNotEmpty(ProductInstanceRsp) && Func.isNotEmpty(ProductInstanceRsp.getData())) {
                productInstanceDTOList = ProductInstanceRsp.getData();
                productInstanceDTOList = productInstanceDTOList.stream().filter(p -> p.getRefSampleID().equalsIgnoreCase(testSamplePO.getSampleId())).collect(Collectors.toList());
            }
            reportLanguage = orderAllDTO.getReportLanguage();
        }

        String SAM_DESCRIPTION_BIOVALUE = "";
        String constructionEn = "";
        String constructionCn = "";
        String styleNoEn = "";
        String styleNoCn = "";
        String specialproductAttribute1En = "";
        String specialproductAttribute1Cn = "";

        ProductInstanceDTO productInstanceDTO = null;
        if (ReportLanguage.EnglishReportOnly.getCode().equalsIgnoreCase(reportLanguage)) {
            productInstanceDTO = productInstanceDTOList.stream().filter(p -> p.getLanguageID() == 1).findFirst().orElse(null);
            if (Func.isNotEmpty(productInstanceDTO)) {
                SAM_DESCRIPTION_BIOVALUE += productInstanceDTO.getProductDescription();
                constructionEn = productInstanceDTO.getConstruction();
                styleNoEn = productInstanceDTO.getStyleNo();
                specialproductAttribute1En = productInstanceDTO.getSpecialProductAttribute1();
            }
        } else if (ReportLanguage.ChineseReportOnly.getCode().equalsIgnoreCase(reportLanguage)) {
            productInstanceDTO = productInstanceDTOList.stream().filter(p -> p.getLanguageID() == 2).findFirst().orElse(null);
            if (Func.isNotEmpty(productInstanceDTO)) {
                SAM_DESCRIPTION_BIOVALUE += productInstanceDTO.getProductDescription();
                constructionCn = productInstanceDTO.getConstruction();
                styleNoCn = productInstanceDTO.getStyleNo();
                specialproductAttribute1Cn = productInstanceDTO.getSpecialProductAttribute1();
            }
        } else if (ReportLanguage.EnglishAndChineseReport.getCode().equalsIgnoreCase(reportLanguage)) {
            productInstanceDTO = productInstanceDTOList.stream().filter(p -> p.getLanguageID() == 1).findFirst().orElse(null);
            if (Func.isNotEmpty(productInstanceDTO)) {
                SAM_DESCRIPTION_BIOVALUE += productInstanceDTO.getProductDescription();
                constructionEn = productInstanceDTO.getConstruction();
                styleNoEn = productInstanceDTO.getStyleNo();
                specialproductAttribute1En = productInstanceDTO.getSpecialProductAttribute1();
            }
            productInstanceDTO = productInstanceDTOList.stream().filter(p -> p.getLanguageID() == 2).findFirst().orElse(null);
            if (Func.isNotEmpty(productInstanceDTO)) {
                SAM_DESCRIPTION_BIOVALUE += "|";
                SAM_DESCRIPTION_BIOVALUE += productInstanceDTO.getProductDescription();
                constructionCn = productInstanceDTO.getConstruction();
                styleNoCn = productInstanceDTO.getStyleNo();
                specialproductAttribute1Cn = productInstanceDTO.getSpecialProductAttribute1();
            }
        }

        sample.setSamDescription(SAM_DESCRIPTION_BIOVALUE);
        sample.setDescription1("");
        sample.setDescription2("");
        sample.setDescription3("");
        sample.setDescription4("");
        sample.setDescription5("");
        sample.setDescription6("");
        sample.setSampRemark("");
        sample.setProductCode("");

        // 创建SAMPLEBIOFIELD节点
        List<SampleBioField> sampleBioFieldList = new ArrayList<>();
        SampleBioField sampleBioField = new SampleBioField();
        sampleBioField.setBIOField("MATERIAL & MARK");
        sampleBioField.setBIOValue(constructionEn);
        sampleBioField.setUBIOValue(constructionCn);
        sampleBioFieldList.add(sampleBioField);

        SampleBioField sampleBioField2 = new SampleBioField();
        sampleBioField2.setBIOField("MODEL NO");
        //        中文传UBIOVALUE， 英文传BIOVALUE
        sampleBioField2.setBIOValue(styleNoEn);
        sampleBioField2.setUBIOValue(styleNoCn);
        sampleBioFieldList.add(sampleBioField2);

        SampleBioField sampleBioField3 = new SampleBioField();
        sampleBioField3.setBIOField("CLIENT REF INFOR");
        //        中文传UBIOVALUE， 英文传BIOVALUE
        sampleBioField3.setBIOValue(specialproductAttribute1En);
        sampleBioField3.setUBIOValue(specialproductAttribute1Cn);
        sampleBioFieldList.add(sampleBioField3);


        List<Scheme> schemeList = new ArrayList<>();

        // 创建SCHEME节点
        Scheme scheme = new Scheme();

        UserInfo user = tokenClient.getUser();
        BuParamValueRsp buParamValue = frameWorkClient.getBuParamValue(ProductLineContextHolder.getProductLineCode(), user.getCurrentLabCode(), "slim", "procedureCode");
        log.info("bu param ProcedureCode:{}", JSON.toJSONString(buParamValue));
        scheme.setProcedureCode(Func.isEmpty(buParamValue) ? "" : buParamValue.getParamValue());
        schemeList.add(scheme);

        sample.setScheme(schemeList);
        //处理IsActive,如果没有值传0
        for (SampleBioField bioField : sampleBioFieldList) {
            if (Func.isEmpty(bioField.getBIOValue()) && Func.isEmpty(bioField.getUBIOValue())) {
                bioField.setIsActive("0");
            } else {
                bioField.setIsActive("-1");
            }
        }
        sample.setSampleBIOField(sampleBioFieldList);
    }

    private void updateErrorInfo(SubContractReq req, SlimSubcontractPO slimSubcontractPO, String errorMsg) {
        // 更新tb_sub_contract表错误信息
        SubContractPO record = new SubContractPO();
        if (errorMsg == null) {
            record.setErrorMsg("当前分单号：[" + slimSubcontractPO.getSubcontractNo() + "]，Procedure：[" + slimSubcontractPO.getProcedure() + "]、Scheme：[" + slimSubcontractPO.getScheme() + "] 获取Analytes失败");
        } else {
            record.setErrorMsg(errorMsg);
        }
        SubContractExample example = new SubContractExample();
        example.createCriteria().andSubContractNoEqualTo(req.getSubContractNo().toString()).andOrderNoEqualTo(req.getOrderNo());
        subContractMapper.updateByExampleSelective(record, example);
        // result.setMsg(record.getErrormsg());
        log.info(record.getErrorMsg());
    }

    /**
     * 构造slim job info
     *
     * @param job
     * @param jobBioFieldList
     */
    private CustomResult buildSlimJob(Job job, List<JobBioField> jobBioFieldList, SubContractReq req) {
        CustomResult customResult = CustomResult.newSuccessInstance();
        job.setOMDestinationctry(StringUtils.EMPTY);
        job.setRegisteredBy(StringUtils.EMPTY);
        job.setOMOriginctry(StringUtils.EMPTY);
        job.setOMCustomerRef(StringUtils.EMPTY);
        job.setOMProductDescription(StringUtils.EMPTY);
        OrderIdReq orderIdReq = new OrderIdReq();
        orderIdReq.setOrderNo(req.getOrderNo());
        orderIdReq.setProductLineCode(ProductLineContextHolder.getProductLineCode());
        SubcontractRequirementInfoExample reExample = new SubcontractRequirementInfoExample();
        reExample.createCriteria().andSubContractNoEqualTo(req.getSubContractNo());
        List<SubcontractRequirementInfoPO> requirements = subcontractRequirementInfoMapper.selectByExample(reExample);
        SubcontractRequirementInfoPO subcontractRequirementInfoPO = new SubcontractRequirementInfoPO();
        if (Func.isNotEmpty(requirements)) {
            subcontractRequirementInfoPO = requirements.get(0);
        }
        String reportLanguage = "";
        OrderAllDTO orderAllDTO = this.getOrderForPe(req.getOrderNo());
        if(Func.isNotEmpty(orderAllDTO)){
            reportLanguage = orderAllDTO.getReportLanguage();
        }

        List<CustomerInstanceDTO> customerDTOList = orderFacade.queryCustomerForPe(orderIdReq).getData();
        CustomerInstanceDTO customerBuyer = new CustomerInstanceDTO();
        CustomerInstanceDTO customerManufacture = new CustomerInstanceDTO();
        if (CollectionUtils.isNotEmpty(customerDTOList)) {
            customerBuyer = customerDTOList.stream().filter(customerInstanceDTO1 ->
                    CustomerUsage.Buyer.getCode().equals(customerInstanceDTO1.getCustomerUsage())).findFirst().orElse(null);
            customerManufacture = customerDTOList.stream().filter(e -> e.getCustomerUsage().equals(CustomerUsage.Manufacture.getCode())).findFirst().orElse(null);

            job.setOMManufacturer(Func.isNotEmpty(customerBuyer) ? "Buyer:" + (Func.isEmpty(customerBuyer.getBuyerGroup()) ? "" : customerBuyer.getBuyerGroup()) + "(" + (Func.isEmpty(customerBuyer.getBuyerGroupName()) ? "" : customerBuyer.getBuyerGroupName()) + ")" : "");
        }

        job.setNotes1(Func.toStr(subcontractRequirementInfoPO.getReportQty().toString()));

        // 获取分包单 expect due date
        SubContractExample subContractExample = new SubContractExample();
        subContractExample.createCriteria().andSubContractNoEqualTo(req.getSubContractNo());
        List<SubContractPO> subContractPOS = subContractMapper.selectByExample(subContractExample);
        SubContractPO subContractPO = new SubContractPO();
        if (Func.isNotEmpty(subContractPOS)) {
            subContractPO = subContractPOS.get(0);
            if (Func.isEmpty(subContractPO.getSubContractExpectDueDate())) {
                customResult.setSuccess(false);
                customResult.setMsg("分包单未选定Expect Due Date，不允许To SLIM。");
                return customResult;
            }
            job.setRequired(formatDate(subContractPO.getSubContractExpectDueDate()));
            //取以+分隔的第一个字符串
            List<String> serviceTypes = Splitter.on('+').splitToList(subContractPOS.get(0).getSubContractServiceType());
            if (CollectionUtils.isNotEmpty(serviceTypes)) {
                job.setOMServiceLevel(serviceTypes.get(0));
            }
        } else {
            job.setRequired("");
            job.setOMServiceLevel("");
        }
        OrderAllDTO gpoOrderInfo = this.getGpoOrderInfo(req.getOrderNo());
        BuParamValueRsp toSlimCostCenter = null;
        if(Func.isNotEmpty(gpoOrderInfo)){
            toSlimCostCenter = frameWorkClient.getBuParamValue(gpoOrderInfo.getBUCode(), gpoOrderInfo.getLocationCode(), "toSlimCostCenter", gpoOrderInfo.getBUCode());
            log.info("bu param costCode value:{}", JSON.toJSONString(toSlimCostCenter));
        }
        String costCode = "";
        if (Func.isNotEmpty(toSlimCostCenter)) {
            costCode = toSlimCostCenter.getParamValue();
        }
        job.setCostCode(costCode);
        job.setProJob(req.getOrderNo());
        job.setProjCode("DEFAULT");
        job.setLabCode("BOSS_NO_ORDER");
        job.setProductCode("");
        job.setOwnerCode("");
        String jobComments = "report language:" + ReportLanguage.findName(reportLanguage) + ";report file Qty:" + subcontractRequirementInfoPO.getReportQty() + ";Report requirment:" + (Func.isEmpty(ReportRequirementEnum.getMessage(subcontractRequirementInfoPO.getReportRequirement())) ? "" : ReportRequirementEnum.getMessage(subcontractRequirementInfoPO.getReportRequirement()));
        jobComments = jobComments.replace("&", " ");
        job.setJobComments(jobComments);
        JobBioField jbf1 = new JobBioField();
        jbf1.setBIOField("GPO.OrderNo");

        GpoExternalOrderNoReq gpoExternalOrderNoReq = new GpoExternalOrderNoReq();
        gpoExternalOrderNoReq.setOrderNoList(Lists.newArrayList(req.getOrderNo()));
        gpoExternalOrderNoReq.setProductLineCode(ProductLineContextHolder.getProductLineCode());
        BaseResponse<List<GpoExternalOrderInfo>> externalOrderRes = orderFacade.getExternalOrder(gpoExternalOrderNoReq);
        List<GpoExternalOrderInfo> externalOrderList = externalOrderRes.getData();
        if (Func.isNotEmpty(externalOrderList)) {
            jbf1.setBIOValue(externalOrderList.get(0).getExternalOrderNo());
            job.setProJob(externalOrderList.get(0).getExternalOrderNo());
        }
        jobBioFieldList.add(jbf1);

        JobBioField jbf2 = new JobBioField();
        jbf2.setBIOField("GPO.SubcontractNo");
        jbf2.setBIOValue(subContractPO.getSubContractNo() == null ? "" : subContractPO.getSubContractNo());
        jobBioFieldList.add(jbf2);

        if (Func.isNotEmpty(customerBuyer)) {
            JobBioField jbf4 = new JobBioField();
            jbf4.setBIOField("BUYER");
            if (ReportLanguage.EnglishReportOnly.getCode().equalsIgnoreCase(reportLanguage)) {
                String customerName = Func.isEmpty(customerBuyer.getCustomerNameEN()) ? "" : customerBuyer.getCustomerNameEN().replace("&", " ");
                jbf4.setBIOValue(customerName);
            } else if (ReportLanguage.ChineseReportOnly.getCode().equalsIgnoreCase(reportLanguage)) {
                String customerName = Func.isEmpty(customerBuyer.getCustomerNameCN()) ? "" : customerBuyer.getCustomerNameCN().replace("&", " ");
                jbf4.setUBIOValue(customerName);
            } else if (ReportLanguage.EnglishAndChineseReport.getCode().equalsIgnoreCase(reportLanguage)) {
                String customerNameEn = Func.isEmpty(customerBuyer.getCustomerNameEN()) ? "" : customerBuyer.getCustomerNameEN().replace("&", " ");
                String customerNameCn = Func.isEmpty(customerBuyer.getCustomerNameCN()) ? "" : customerBuyer.getCustomerNameCN().replace("&", " ");
                jbf4.setBIOValue(customerNameEn);
                jbf4.setUBIOValue(customerNameCn);
            }
            jobBioFieldList.add(jbf4);
        }

        if (Func.isNotEmpty(customerManufacture)) {
            JobBioField jbf5 = new JobBioField();
            jbf5.setBIOField("MANUFACTURER");
            if (ReportLanguage.EnglishReportOnly.getCode().equalsIgnoreCase(reportLanguage)) {
                jbf5.setBIOValue(customerManufacture.getCustomerNameEN());
            } else if (ReportLanguage.ChineseReportOnly.getCode().equalsIgnoreCase(reportLanguage)) {
                jbf5.setUBIOValue(customerManufacture.getCustomerNameCN());
            } else if (ReportLanguage.EnglishAndChineseReport.getCode().equalsIgnoreCase(reportLanguage)) {
                jbf5.setBIOValue(customerManufacture.getCustomerNameEN());
                jbf5.setUBIOValue(customerManufacture.getCustomerNameCN());
            }
            jobBioFieldList.add(jbf5);
        }

        //获取reportHead信息
        OrderIdReq orderIdReq2 = new OrderIdReq();
        orderIdReq2.setOrderId(gpoOrderInfo.getOrderId());
        orderIdReq2.setProductLineCode(ProductLineContextHolder.getProductLineCode());
        OrderReportReceiverDTO orderReportReceiverDTO = orderReportFacade.getOrderReportReceiveByOrderId(orderIdReq2).getData();
        String reportHeadEn = "";
        String reportHeadCn = "";
        String reportHeadAddressEn = "";
        String reportHeadAddressCn = "";
        if (StringUtils.isNotEmpty(orderReportReceiverDTO.getReportHeader())) {
            int headInx = orderReportReceiverDTO.getReportHeader().indexOf("|||");
            String headTemp = orderReportReceiverDTO.getReportHeader();
            if (headInx != -1) {
                reportHeadEn = headTemp.substring(headInx + 3);
                reportHeadCn = headTemp.substring(0, headInx);
            } else {
                if (ReportLanguage.checkLanguage(reportLanguage,ReportLanguage.EnglishReportOnly)) {
                    reportHeadEn = headTemp;
                } else {
                    reportHeadCn = headTemp;
                }
            }
        }
        if (StringUtils.isNotEmpty(orderReportReceiverDTO.getReportDeliveredTo())) {
            int headInx = orderReportReceiverDTO.getReportDeliveredTo().indexOf("|||");
            String deliverTemp = orderReportReceiverDTO.getReportDeliveredTo();
            if (headInx != -1) {
                reportHeadAddressEn = deliverTemp.substring(headInx + 3);
                reportHeadAddressCn = deliverTemp.substring(0, headInx);
            } else {
                if (ReportLanguage.checkLanguage(reportLanguage,ReportLanguage.EnglishReportOnly)) {
                    reportHeadAddressEn = deliverTemp;
                } else {
                    reportHeadAddressCn = deliverTemp;
                }
            }
        }

        JobBioField jbf6 = new JobBioField();
        jbf6.setBIOField("REPORT TO CLIENT NAME");
        jbf6.setBIOValue(reportHeadEn);
        jbf6.setUBIOValue(reportHeadCn);
        jobBioFieldList.add(jbf6);

        JobBioField jbf7 = new JobBioField();
        jbf7.setBIOField("REPORT TO CLIENT ADDRESS 1");
        jbf7.setBIOValue(reportHeadAddressEn);
        jbf7.setUBIOValue(reportHeadAddressCn);
        jobBioFieldList.add(jbf7);


        JobBioField jbf9 = new JobBioField();
        jbf9.setBIOField("GPO.BU");
        jbf9.setBIOValue(req.getLabCode().split(" ")[1]);
        jobBioFieldList.add(jbf9);
        /*设置DFF Form信息*/
        BaseResponse<List<ProductInstanceDTO>> productSampleRes = orderFacade.getSampleByOrderNo(orderIdReq);
        List<ProductInstanceDTO> productInstanceDTOList = productSampleRes.getData();
        ProductInstanceDTO productInstanceEnDTO = null;
        ProductInstanceDTO productInstanceCnDTO = null;
        if (Func.isNotEmpty(productInstanceDTOList)) {
            productInstanceEnDTO = productInstanceDTOList.stream().filter(i -> Func.isEmpty(i.getHeaderID()) && LanguageType.check(i.getLanguageID(), LanguageType.English)).findFirst().orElse(new ProductInstanceDTO());
            productInstanceCnDTO = productInstanceDTOList.stream().filter(i -> Func.isEmpty(i.getHeaderID()) && LanguageType.check(i.getLanguageID(), LanguageType.Chinese)).findFirst().orElse(new ProductInstanceDTO());
        }
        JobBioField jbf10 = new JobBioField();
        jbf10.setBIOField("STYLE NO");
        jbf10.setBIOValue(productInstanceEnDTO.getStyleNo());
        jbf10.setUBIOValue(productInstanceCnDTO.getStyleNo());
        jobBioFieldList.add(jbf10);

        JobBioField jbf11 = new JobBioField();
        jbf11.setBIOField("ITEM NO");
        jbf11.setBIOValue(productInstanceEnDTO.getItemNo());
        jbf11.setUBIOValue(productInstanceCnDTO.getItemNo());
        jobBioFieldList.add(jbf11);

        JobBioField jbf12 = new JobBioField();
        jbf12.setBIOField("PO NO");
        jbf12.setBIOValue(productInstanceEnDTO.getpONo());
        jbf12.setUBIOValue(productInstanceCnDTO.getpONo());
        jobBioFieldList.add(jbf12);

        JobBioField jbf13 = new JobBioField();
        jbf13.setBIOField("SKU NO");
        jbf13.setBIOValue(productInstanceEnDTO.getSpecialProductAttribute10());
        jbf13.setUBIOValue(productInstanceCnDTO.getSpecialProductAttribute10());
        jobBioFieldList.add(jbf13);

        JobBioField jbf14 = new JobBioField();
        jbf14.setBIOField("COUNTRY OF ORIGIN");
        jbf14.setBIOValue(productInstanceEnDTO.getCountryOfOrigin());
        jbf14.setUBIOValue(productInstanceCnDTO.getCountryOfOrigin());
        jobBioFieldList.add(jbf14);

        JobBioField jbf15 = new JobBioField();
        jbf15.setBIOField("COUNTRY OF DEST");
        jbf15.setBIOValue(productInstanceEnDTO.getCountryOfDestination());
        jbf15.setUBIOValue(productInstanceCnDTO.getCountryOfDestination());
        jobBioFieldList.add(jbf15);

        JobBioField jbf16 = new JobBioField();
        jbf16.setBIOField("AGE GRADING REQUESTED");
        jbf16.setBIOValue(productInstanceEnDTO.getAgeGroup());
        jbf16.setUBIOValue(productInstanceCnDTO.getAgeGroup());
        jobBioFieldList.add(jbf16);

        JobBioField jbf17 = new JobBioField();
        jbf17.setBIOField("CLIENT REF INFOR");
        jbf17.setBIOValue(productInstanceEnDTO.getSpecialProductAttribute1());
        jbf17.setUBIOValue(productInstanceCnDTO.getSpecialProductAttribute1());
        jobBioFieldList.add(jbf17);
        //处理IsActive,如果没有值传0
        for (JobBioField jobBioField : jobBioFieldList) {
            //{GPO.OrderNo、GPO.SubcontractNo、GPO.BU} 的isActive暂时暂时设置为0
            if (Func.equals("GPO.OrderNo", jobBioField.getBIOField()) || Func.equals("GPO.SubcontractNo", jobBioField.getBIOField()) || Func.equals("GPO.BU", jobBioField.getBIOField())) {
                jobBioField.setIsActive("0");
            } else {
                if (Func.isEmpty(jobBioField.getBIOValue()) && Func.isEmpty(jobBioField.getUBIOValue())) {
                    jobBioField.setIsActive("0");
                } else {
                    jobBioField.setIsActive("-1");
                }
            }
        }
        return customResult;
    }

    private String formatDate(Date date) {
        SimpleDateFormat smf = new SimpleDateFormat("yyyy/MM/dd HH:mm:ss");
        return smf.format(date);
    }

    @Override
    @BizLog(bizType = BizLogConstant.SUBCONTRACT_OPERATION_HOSTORY, operType = "Complete")
    public BaseResponse complete(SubcontractCompleteReq req) {
        if(Func.isEmpty(req.getSubContractNo())){
            return BaseResponse.newFailInstance("subContractNo 不能为空");
        }
        if(Func.isEmpty(req.getProductLineCode())){
            return BaseResponse.newFailInstance("productLineCode 不能为空");
        }
        SubContractInfo subContractInfo = subContractExtMapper.getSubContractInfo(req.getSubContractNo());
        if(Func.isEmpty(subContractInfo)){
            return BaseResponse.newFailInstance("subContractNo 在GPO ");
        }
        if(!SubContractStatus.check(subContractInfo.getStatus(),SubContractStatus.TESTING)){
            return BaseResponse.newFailInstance("Unable to complete subcontract because subcontract status is "+SubContractStatus.enumOf(subContractInfo.getStatus()).getName());
        }
        // 校验是否有回写的StarLims报告
        BaseResponse<List<SubReportPO>> subReportListRes = subReportService.querySubReportBySubcontractNo(subContractInfo.getSubContractNo());
        if(Func.isEmpty(subReportListRes) || Func.isEmpty(subReportListRes.getData())){
            return BaseResponse.newFailInstance("Unable to complete subcontract because subreport is not exist");
        }
        GpnUpdateDateReq updateDateReq = new GpnUpdateDateReq();
        if(Func.isNotEmpty(req.getDatetime())){
            updateDateReq.setDatetime(DateUtils.parseDate(req.getDatetime()));
        }
        updateDateReq.setForceComplete(true);
        updateDateReq.setSubContractIds(subContractInfo.getId());
        return this.updateDate(updateDateReq);
    }

    @Override
//    @BizLog(bizType = BizLogConstant.SUBCONTRACT_OPERATION_HOSTORY, operType = "Complete")
    public BaseResponse updateDate(GpnUpdateDateReq req) {
        if (StringUtils.isBlank(req.getSubContractIds())) {
            return BaseResponse.newFailInstance(ResponseCode.ILLEGAL_ARGUMENT);
        }
        BaseResponse response = this.checkSubContractTl(req);
        if (response.getStatus() != com.sgs.framework.facade.domain.utils.ResponseCode.SUCCESS.getCode()) {
            return response;
        }
        String subContractIds = req.getSubContractIds();
        List<String> Ids = Lists.newArrayList(subContractIds.split(","));
        SubContractExample subContractExample = new SubContractExample();
        subContractExample.createCriteria().andIDIn(Ids);
        List<SubContractPO> subContractPOS = subContractMapper.selectByExample(subContractExample);
        if(Func.isEmpty(subContractPOS)){
            return BaseResponse.newFailInstance(ResponseCode.ILLEGAL_ARGUMENT);
        }
        // 组装Complete参数
        SubContractCompleteReq subcontractCompleteReq = new SubContractCompleteReq();
        List<SubContractItemCompleteReq> reqs = Lists.newArrayList();
        List<SubContractItemCompleteReq> completeItems = req.getCompleteItems();
        if(Func.isEmpty(completeItems)){
            subContractPOS.stream().forEach(subcontract->{
                SubContractItemCompleteReq subcontractItemCompleteReq = new SubContractItemCompleteReq();
                subcontractItemCompleteReq.setSubcontractNo(subcontract.getSubContractNo());
                subcontractItemCompleteReq.setExternalNo(subcontract.getSlimJobNo());
                subcontractItemCompleteReq.setObjectType(SubReportObjectTypeEnums.subcontract.getObjectType());
                subcontractItemCompleteReq.setReportFlag(ReportFlagEnums.SUB_REPORT.getCode());
                reqs.add(subcontractItemCompleteReq);
            });
            subcontractCompleteReq.setCompleteItems(reqs);
        }else{
            for (SubContractItemCompleteReq completeItem : completeItems) {
                List<ExternalFileReq> subReports = completeItem.getSubReports();
                if(Func.isNotEmpty(subReports)){
                    for (ExternalFileReq subReport : subReports) {
                        String fileName = subReport.getFileName();
                        if(Func.isEmpty(subReport.getReportFileType()) && Func.isNotEmpty(fileName)){
                            //根据fileName判断是PDF还是Word
                            if(fileName.toLowerCase().endsWith(".pdf")){
                                if(Func.equalsSafe(subReport.getIsDraft(),1)){
                                    subReport.setReportFileType(ReportFileType.DraftPDF.getCode());
                                }else{
                                    subReport.setReportFileType(ReportFileType.PDF.getCode());
                                }
                            }else if(fileName.toLowerCase().endsWith(".docx") || fileName.toLowerCase().endsWith(".doc")){
                                subReport.setReportFileType(ReportFileType.Word.getCode());
                            }
                        }
                    }
                }
            }

            subcontractCompleteReq.setCompleteItems(req.getCompleteItems());
        }

        subcontractCompleteReq.setForceComplete(req.isForceComplete());
        CustomResult completeResult = subcontractService.complete(subcontractCompleteReq);
        List<SubContractCompleteRes> subContractCompleteResList = (List<SubContractCompleteRes>)completeResult.getData();
        if(Func.isNotEmpty(subContractCompleteResList)){
            List<String> errorMessage = Lists.newArrayList();
            subContractCompleteResList.stream().forEach(res->{
                if(res.getStatus() != ResponseCode.SUCCESS.getCode()){
                    errorMessage.add("分包单【"+res.getSubcontractNo()+"】complete失败："+res.getMessage());
                }
            });
            if(Func.isNotEmpty(errorMessage)){
                return  BaseResponse.newFailInstance(errorMessage.stream().collect(Collectors.joining("<br>")));
            }
        }
        return BaseResponse.newSuccessInstance(true);
    }

    /**
     * @param req
     * @return
     */
    @Override
    public BaseResponse checkSubContractTl(GpnUpdateDateReq req) {
        if (StringUtils.isBlank(req.getSubContractIds())) {
            return BaseResponse.newFailInstance(ResponseCode.ILLEGAL_ARGUMENT);
        }
        if(Func.isEmpty(ProductLineContextHolder.getProductLineCode())){
            return BaseResponse.newFailInstance(ResponseCode.PARAM_MISS);
        }
        String subContractIds = req.getSubContractIds();
        List<String> ids = Lists.newArrayList(subContractIds.split(","));
        SubContractExample subContractExample = new SubContractExample();
        subContractExample.createCriteria().andIDIn(ids);
        List<SubContractPO> subContractPOS = subContractMapper.selectByExample(subContractExample);
        // 过滤掉 tracing only 模式的分包单
        subContractPOS = subContractPOS.stream()
                .filter(subcontract-> !SubcontractOperationModel.check(subcontract.getOperationModel(),SubcontractOperationModel.TracingOnly))
                .collect(Collectors.toList());
        ids = subContractPOS.stream()
                .filter(subcontract-> !SubcontractOperationModel.check(subcontract.getOperationModel(),SubcontractOperationModel.TracingOnly))
                .map(SubContractPO::getID)
                .collect(Collectors.toList());
        //校验TL
        if(Func.isNotEmpty(subContractPOS) && Func.isNotEmpty(ids)){
            SubContractTestLineMappingExample subContractTestLineMappingExample = new SubContractTestLineMappingExample();
            subContractTestLineMappingExample.createCriteria().andSubContractIDIn(ids);
            List<SubContractTestLineMappingPO> subContractTestLineMappingPOS = subContractTestLineMappingMapper.selectByExample(subContractTestLineMappingExample);
            if (Func.isEmpty(subContractTestLineMappingPOS)) {
                return BaseResponse.newFailInstance("请检查分包单下是否存在有效的TL!");
            }
            //分组再校验一遍，防止批量操作多个分包单有的存在TL，有的不存在TL
            Map<String, List<SubContractTestLineMappingPO>> subContractTlMappingList = subContractTestLineMappingPOS.stream().collect(Collectors.groupingBy(i -> i.getSubContractID()));
            for (String subContractId : subContractTlMappingList.keySet()) {
                List<SubContractTestLineMappingPO> subContractTestLineMappingPOS1 = subContractTlMappingList.get(subContractId);
                if (Func.isEmpty(subContractTestLineMappingPOS1)) {
                    SubContractPO subContractPO = subContractPOS.stream().filter(i -> Func.equals(i.getID(), subContractId)).findFirst().orElse(new SubContractPO());
                    return BaseResponse.newFailInstance("请检查分包单" + subContractPO.getSubContractNo() + "下是否存在有效的TL!");
                }
            }
        }
        return BaseResponse.newSuccessInstance(true);
    }

    /**
     * subContract toTesting操作
     *
     * @param req
     * @return
     */
    @Override
//    @BizLog(bizType = BizLogConstant.SUBCONTRACT_OPERATION_HOSTORY, operType = "Testing")
    public com.sgs.framework.facade.domain.utils.BaseResponse toTesting(GpnUpdateDateReq req) {
        if (StringUtils.isBlank(req.getSubContractIds())) {
            return com.sgs.framework.facade.domain.utils.BaseResponse.newFailInstance(com.sgs.framework.facade.domain.utils.ResponseCode.ILLEGAL_ARGUMENT);
        }
//        com.sgs.framework.facade.domain.utils.BaseResponse response = this.checkSubContractTl(req);
//        if (response.getStatus() != com.sgs.framework.facade.domain.utils.ResponseCode.SUCCESS.getCode()) {
//            return response;
//        }
        String subContractIds = req.getSubContractIds();
        List<String> Ids = Lists.newArrayList(subContractIds.split(","));
        SubContractExample subContractExample = new SubContractExample();
        subContractExample.createCriteria().andIDIn(Ids);
        List<SubContractPO> subContractPOS = subContractMapper.selectByExample(subContractExample);
        if(Func.isEmpty(subContractPOS)){
            return com.sgs.framework.facade.domain.utils.BaseResponse.newFailInstance(com.sgs.framework.facade.domain.utils.ResponseCode.ILLEGAL_ARGUMENT);
        }
        SubContractTestingReq subContractTestingReq = new SubContractTestingReq();
        subContractTestingReq.setSubcontractType(SubContractType.None.getType());
        subContractTestingReq.setStartDate(req.getDatetime());
        subContractTestingReq.setProductLineCode(ProductLineContextHolder.getProductLineCode());
        List<String> errorMessage = Lists.newArrayList();
        subContractPOS.forEach(subcontract->{
            subContractTestingReq.setSubcontractNo(subcontract.getSubContractNo());
            // 根据分包单查询关联的外部系统单号
            SubContractExternalRelationshipPO subContractExternalRel = new SubContractExternalRelationshipPO();
            subContractExternalRel.setSubContractNo(subcontract.getSubContractNo());
            subContractExternalRel.setSubContractType(SubContractType.None.getType());
            BaseResponse<List<SubContractExternalRelationshipPO>> relRes = subContractExternalRelService.getSubContractExternalRels(subContractExternalRel);
            if(Func.isNotEmpty(relRes) && Func.isNotEmpty(relRes.getData())){
                subContractTestingReq.setExternalNo(relRes.getData().get(0).getExternalNo());
            }
            CustomResult testResult = subcontractService.testing(subContractTestingReq);
            if(!testResult.isSuccess()){
                errorMessage.add("分包单【"+subcontract.getSubContractNo()+"】testing失败："+testResult.getMsg());
            }
        });
        if(Func.isNotEmpty(errorMessage)){
            return  com.sgs.framework.facade.domain.utils.BaseResponse.newFailInstance(errorMessage.stream().collect(Collectors.joining("<br>")));
        }
        return com.sgs.framework.facade.domain.utils.BaseResponse.newSuccessInstance(true);
    }

    @Override
    public CustomResult getSubContractInfo(GpnPrintSubContractReq req) {
        CustomResult rspResult = new CustomResult();
        if(Func.isEmpty(req)||Func.isEmpty(req.getSubContractIds())){
            rspResult.setSuccess(false);
            rspResult.setMsg("please select subcontract!");
            return rspResult;
        }
        String subContractIds = req.getSubContractIds();
        List<String> ids = Lists.newArrayList(subContractIds.split(","));
        ArrayList<SubContractPrintRsp> SubContractPrintRspList = Lists.newArrayList();
        //sampleForLiquid
        BuParamReq buParamReq = new BuParamReq();
        buParamReq.setGroupCode(Constants.BU_PARAM.SUBCONTRACT.GROUP);
        buParamReq.setParamCode(Constants.BU_PARAM.SUBCONTRACT.CODE.SAMPLE_FOR_LIQUID);
        buParamReq.setProductLineCode(ProductLineContextHolder.getProductLineCode());
        BuParamValueRsp sampleForLiquidBuParam = frameWorkClient.getBuParam(buParamReq);
//        String defaultLanguageCode = tokenClient.getUser().getDefaultLanguageCode();
        String defaultLanguageCode = frameWorkClient.getPrimaryLanguageCode(ProductLineContextHolder.getProductLineCode());
        OrderTestLineListSearchReq testLineListSearchReq = new OrderTestLineListSearchReq();
        testLineListSearchReq.setSubContractIdList(ids);
        List<OrderTestLineListDTO> orderTestLineInfoList = orderTestLineService.getOrderTestLineInfoList(testLineListSearchReq);
        Map<String, List<OrderTestLineListDTO>> subContractTestLineMap = null;
        if (Func.isNotEmpty(orderTestLineInfoList)) {
            subContractTestLineMap = orderTestLineInfoList.stream().collect(Collectors.groupingBy(b -> b.getSubContractID()));
        }
        if (Func.isEmpty(subContractTestLineMap)) {
            subContractTestLineMap = new HashMap<>();
        }
        Map<String, List<OrderTestLineListDTO>> finalSubContractTestLineMap = subContractTestLineMap;
        ids.forEach(id -> {
            // 获取各个系统的参数拼装
            SubContractPrintRsp vo = new SubContractPrintRsp();
            SubContractPO subContractPO = subContractMapper.selectByPrimaryKey(id);
            GpnLabListReq labListReq = new GpnLabListReq();
            labListReq.setLabCode(subContractPO.getSubContractLabCode());
            labListReq.setPage("1");
            labListReq.setRows("10");
            PageInfoList pageInfoList = frameWorkClient.getLabInfo(labListReq);
            if(Func.isNotEmpty(pageInfoList)&&Func.isNotEmpty(pageInfoList.getRows())){
                vo.setSubLabType(pageInfoList.getRows().get(0).getLaboratoryTypeID());
            }
            GeneralOrderInstanceInfoPO orderByOrderNo = orderMapper.getOrderInfo(subContractPO.getOrderNo());
            String externalOrderNo = orderClient.getExternalOrderNo(orderByOrderNo.getOrderNo());
            String labCode = StringUtils.trim(orderByOrderNo.getLabCode());
            //设置分包单价格
            SubcontractRequirementInfo subcontractRequirementInfo = subcontractRequirementMapper.selectSubcontractPrice(id);
            if (subcontractRequirementInfo != null) {
                vo.setSubContractFee(subcontractRequirementInfo.getSubcontractFee());
                vo.setSubContractFeeCurrency(subcontractRequirementInfo.getSubcontractFeeCurrency());
            }
            if (LabCodeConsts.LAB_CODE_NJ.equalsIgnoreCase(labCode)) {
                vo.setAddress(subcontractConfig.getLabNjAddress());
                vo.setEmail(subcontractConfig.getNjEmail());
            } else if (LabCodeConsts.LAB_CODE_GZ.equalsIgnoreCase(labCode)) {
                vo.setAddress(subcontractConfig.getLabGzAddress());
                vo.setEmail(subcontractConfig.getGzEmail());
            } else if (StringUtils.equalsIgnoreCase(labCode, LabCodeConsts.LAB_CODE_QD)) {
                vo.setAddress(subcontractConfig.getLabQdAddress());
                vo.setEmail(subcontractConfig.getQdEmail());
            } else if (StringUtils.equalsIgnoreCase(labCode, LabCodeConsts.LAB_CODE_XM)) {
                vo.setAddress(subcontractConfig.getLabXmAddress());
                vo.setEmail(subcontractConfig.getXmEmail());
            } else if (StringUtils.equalsIgnoreCase(labCode, LabCodeConsts.LAB_CODE_SH)) {
                vo.setAddress(subcontractConfig.getLabShAddress());
            } else if (StringUtils.equalsIgnoreCase(labCode, LabCodeConsts.LAB_CODE_HZ)) {
                vo.setAddress(subcontractConfig.getLabHzAddress());
                vo.setEmail(subcontractConfig.getHzEmail());
            } else if (StringUtils.equalsIgnoreCase(labCode, LabCodeConsts.LAB_CODE_CZ)) {
                vo.setAddress(subcontractConfig.getLabCzAddress());
                vo.setEmail(subcontractConfig.getCzEmail());
            } else if (StringUtils.equalsIgnoreCase(labCode, LabCodeConsts.LAB_CODE_NB)) {
                vo.setAddress(subcontractConfig.getLabNbAddress());
                vo.setEmail(subcontractConfig.getNbEmail());
            } else if (StringUtils.equalsIgnoreCase(labCode, LabCodeConsts.LAB_CODE_TJ)) {
                vo.setEmail(subcontractConfig.getTjEmail());
            }
            vo.setTo(subContractPO.getSubContractLabName() + " / " + Func.toStr(subContractPO.getSubContractContract()) + " / " + Func.toStr(subContractPO.getSubContractContractTel()) + " / " + Func.toStr(subContractPO.getSubContractContractEmail()));
            vo.setOrderNo(subContractPO.getOrderNo());
            vo.setExternalOrderNo(externalOrderNo);
            vo.setAdditionalInfo(subContractPO.getAdditionalInfo());
            vo.setServiceType(subContractPO.getSubContractServiceType());


            //zhangtao DIG-3242: 设置分包单备注
            vo.setRemark(subContractPO.getSubContractRemark());

            //String expectedDueDate = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(subContractPO.getSubContractExpectDueDate());
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            vo.setExpectedDueDate(DateUtils.format(subContractPO.getSubContractExpectDueDate()));
            vo.setSubContractNo(subContractPO.getSubContractNo());
            OrderIdReq orderIdReq = new OrderIdReq();
            orderIdReq.setOrderNo(subContractPO.getOrderNo());
            orderIdReq.setProductLineCode(ProductLineContextHolder.getProductLineCode());
            BaseResponse<OrderAllDTO> orderForPe = gpoOrderFacade.getOrderForPe(orderIdReq);
            if (orderForPe.getData() == null) {
                throw new RuntimeException("");
            }

            OrderAllDTO orderAllDTO = orderForPe.getData();
            vo.setOrderCreateDate(simpleDateFormat.format(orderAllDTO.getCreatedDate()));
            String cSContactTel = StringUtil.isBlank(orderAllDTO.getcSContact(), StringUtils.EMPTY);
            String responsibleCS = StringUtil.isBlank(orderAllDTO.getcSName(), StringUtils.EMPTY);
            String cSEmailAddress = StringUtil.isBlank(orderAllDTO.getcSEmail(), StringUtils.EMPTY);
            vo.setCustomerReferenceNo(orderAllDTO.getCustomerRefNo());
            vo.setFm(String.format("%s / %s %s,%s",orderByOrderNo.getLabCode(), responsibleCS, cSContactTel, cSEmailAddress));
            BaseResponse<List<CustomerInstanceDTO>> listBaseResponse = gpoOrderFacade.queryCustomerForPe(orderIdReq);
            if (orderForPe.getData() == null) {
                throw new RuntimeException("");
            }
            List<CustomerInstanceDTO> data = listBaseResponse.getData();
            CustomerInstanceDTO buyer = data.stream().filter(customer -> CustomerType.Buyer.getCode().equals(customer.getCustomerUsage())).findAny().orElse(null);
            if (buyer != null) {
                if (UserInfoDefaultLanguageCodeEnums.check(defaultLanguageCode, UserInfoDefaultLanguageCodeEnums.zh_cn)) {
                    if (Func.isNotEmpty(buyer.getCustomerNameCN())) {
                        vo.setBuyer(buyer.getCustomerNameCN());
                    } else {
                        vo.setBuyer(buyer.getCustomerNameEN());
                    }
                } else {
                    vo.setBuyer(buyer.getCustomerNameEN());
                }
                //获取buyerDiscount
                CustomerDiscountInfoReq customerDiscountInfoReq = new CustomerDiscountInfoReq();
                customerDiscountInfoReq.setLocationCode(orderAllDTO.getLocationCode());
                if (Func.isEmpty(buyer.getBossNumber())) {
                    customerDiscountInfoReq.setCustomerID(buyer.getCustomerId());
                }
                customerDiscountInfoReq.setNumber(Func.isEmpty(buyer.getBossNumber()) ? "" : buyer.getBossNumber() + "");
                customerDiscountInfoReq.setBuCode(ProductLineContextHolder.getProductLineCode());
                CustomerDiscountInfoRsp customerDiscountInfoRsp = customerClient.queryCustomerDiscountInfo(customerDiscountInfoReq);
                if (Func.isNotEmpty(customerDiscountInfoRsp)) {
                    vo.setBuyerDiscount(customerDiscountInfoRsp.getDiscount());
                }
            }
            vo.setResponsibleTeam(orderAllDTO.getResponsibleTeamCode());
            BaseResponse<TestRequestInfo> testRequestInfoBaseResponse = gpoOrderFacade.queryRestRequestForPe(orderIdReq);
            if (testRequestInfoBaseResponse.getData() == null) {
                throw new RuntimeException("");
            }
            TestRequestInfo testRequestInfo = testRequestInfoBaseResponse.getData();
            ReportLanguage reportLanguage = ReportLanguage.findName(testRequestInfo.getReportLanguage());
            if (reportLanguage != null) {
                vo.setReportLanguage(reportLanguage.getName());
            }
            if (Func.isNotEmpty(testRequestInfo.getCommentFlag()) && testRequestInfo.getCommentFlag() == 1) {
                vo.setCommentRequired("YES");
            } else {
                vo.setCommentRequired("No");
            }

            String returnResidueSample = "";
            String returnResidueSampleFlag = Func.toStr(testRequestInfo.getReturnResidueSampleFlag());
            if (StringUtils.isNotBlank(returnResidueSampleFlag)) {
                returnResidueSampleFlag = (returnResidueSampleFlag.equals("1")) ? "YES" : "NO";
            }
            if (Constants.YES_OR_NO.YES.equalsIgnoreCase(returnResidueSampleFlag)) {
                /**
                 * TODO Hengyuan
                 * 1、改为：returnResidueSample=String.format("Residue Sample: %s", StringUtil.isBlank(preOrder.getReturnResidueSampleRemark(), ""));
                 */
                if (StringUtils.isNoneBlank(testRequestInfo.getReturnResidueSampleRemark())) {
                    returnResidueSample = String.format("Residue Sample: %s", StringUtil.isBlank(testRequestInfo.getReturnResidueSampleRemark(), ""));
                } else {
                    returnResidueSample = "Residue Sample: ";
                }
            }

            vo.setReturnResidueSample(returnResidueSample);
            String returnTestedSample = "";
            String returnTestedSampleFlag = Func.toStr(testRequestInfo.getReturnTestedSampleFlag());
            if (StringUtils.isNotBlank(returnTestedSampleFlag)) {
                returnTestedSampleFlag = returnTestedSampleFlag.equals("1") ? "YES" : "NO";
            }
            if (Constants.YES_OR_NO.YES.equalsIgnoreCase(returnTestedSampleFlag)) {
                /**
                 * TODO Hengyuan
                 * 1、改为：returnTestedSample=String.format("Tested Sample: %s", StringUtil.isBlank(preOrder.getReturnTestedSampleRemark(), ""));
                 */
                if (StringUtils.isNoneBlank(testRequestInfo.getReturnTestedSampleRemark())) {
                    //returnTestedSample="Tested Sample: "+preOrder.getReturnTestedSampleRemark();
                    returnTestedSample = String.format("Tested Sample:  %s", StringUtil.isBlank(testRequestInfo.getReturnTestedSampleRemark(), ""));
                } else {
                    returnTestedSample = "Tested Sample:  ";
                }
            }
            vo.setReturnTestedSample(returnTestedSample);

            List<TestLineInstanceSubContractDTO> testLineInstanceSubContractDTOS =
                    testLineService.queryTestlineList(id, orderAllDTO.getOrderNo());

            List<OrderTestLineListDTO> orderTestLineListDTOList = finalSubContractTestLineMap.getOrDefault(id, new ArrayList<>());
            Map<String, OrderTestLineListDTO> testLineListDTOMap = orderTestLineListDTOList.stream().collect(Collectors.toMap(OrderTestLineListDTO::getTestLineInstanceId, o -> o, (key1, key2) -> key2));
            List<TestLineInstanceSubContractDTO> testLineInstanceSubContractDTOs = subContractExtMapper.queryTestlineList(id);
            if (Func.isNotEmpty(testLineInstanceSubContractDTOs)) {
                List<String> testLineInstanceIdList = testLineInstanceSubContractDTOs.stream().map(TestLineInstanceSubContractDTO::getId).collect(Collectors.toList());
                List<TestConditionInfoPO> testConditionInfoPOS = testConditionMapper.getTestConditionByTestLineInstanceId(testLineInstanceIdList);
                if (UserInfoDefaultLanguageCodeEnums.check(defaultLanguageCode, UserInfoDefaultLanguageCodeEnums.zh_cn)) {
                    TestConditionInstanceLocalizeHelper.buildConditionInstances(testConditionInfoPOS,LanguageType.Chinese.getLanguageId());
                }
                Map<String, List<TestConditionInfoPO>> TestConditionInfoMap = testConditionInfoPOS.stream().collect(Collectors.groupingBy(b -> b.getTestLineInstanceID()));
                if (Func.isNotEmpty(TestConditionInfoMap)) {
                    testLineInstanceSubContractDTOS.forEach(item -> {
                        List<TestConditionInfoPO> conditionInfoPOList = TestConditionInfoMap.getOrDefault(item.getId(), null);
                        if (Func.isNotEmpty(conditionInfoPOList)) {
                            String testCondition = conditionInfoPOList.stream().map(testConditionInfoPO -> testConditionInfoPO.getTestConditionTypeName() + ":" + testConditionInfoPO.getTestConditionDesc()).distinct().collect(Collectors.joining(","));
                            item.setTestConditionNameCN(testCondition);
                        }
                    });
                }
            }


            OrderIdReq SampleByOrderNoReq = new OrderIdReq();
            SampleByOrderNoReq.setOrderNo(subContractPO.getOrderNo());
            BaseResponse<List<ProductInstanceDTO>> productInstanceDTORes = orderFacade.getSampleByOrderNo(SampleByOrderNoReq);
            List<ProductInstanceDTO> productInstanceDTOList = productInstanceDTORes.getData();
            productInstanceDTOList = productInstanceDTOList.stream().filter(i -> Func.isNotEmpty(i.getSampleID())).collect(Collectors.toList());
            Map<String, List<ProductInstanceDTO>> productInstanceDTOMap = productInstanceDTOList.stream().collect(Collectors.groupingBy(ProductInstanceDTO::getSampleID));
            List<SampleDTO> sampleDTOS = subContractExtMapper.querySampleList(id);
            List<SampleRsp> sampleRsps = new ArrayList<>();
            for (SampleDTO sampleDTO : sampleDTOS) {
                SampleRsp newRes = new SampleRsp();
                BeanUtils.copyProperties(sampleDTO, newRes);
                if (productInstanceDTOMap.containsKey(sampleDTO.getSampleNo())) {
                    List<ProductInstanceDTO> productInstanceDTOS = productInstanceDTOMap.get(sampleDTO.getSampleNo());
                    ProductInstanceDTO productInstanceDTO = productInstanceDTOS.stream().filter(i -> Func.equals(i.getLanguageID(), UserInfoDefaultLanguageCodeEnums.getIdByCode(defaultLanguageCode))).findFirst().orElse(null);
                    if (Func.isEmpty(productInstanceDTO)) {
                        productInstanceDTO = productInstanceDTOS.get(0);
                    }
                    if (Func.isNotEmpty(productInstanceDTO)) {
                        newRes.setSampleDescription(productInstanceDTO.getProductDescription());
                        newRes.setStyleNo(productInstanceDTO.getStyleNo());
                        newRes.setOtherSampleInfo(productInstanceDTO.getOtherSampleInformation());
                    }
                }
                sampleRsps.add(newRes);
            }
            List<TestLineInstanceSubContractRsp> testLineInstanceSubContractRsps = new ArrayList<>();
            for (TestLineInstanceSubContractDTO subContractDTO : testLineInstanceSubContractDTOS) {
                TestLineInstanceSubContractRsp newRes = new TestLineInstanceSubContractRsp();
                BeanUtils.copyProperties(subContractDTO, newRes);
                if (testLineListDTOMap.containsKey(subContractDTO.getId())) {
                    OrderTestLineListDTO orderTestLineListDTO = testLineListDTOMap.get(subContractDTO.getId());
                    vo.setOrdertestLineRemark(orderTestLineListDTO.getRemark());
                    newRes.setStandardName(orderTestLineListDTO.getTestStandard());
                    newRes.setEvaluationAlias(orderTestLineListDTO.getTestItem());
                    newRes.setEvaluationAliasCN(orderTestLineListDTO.getTestItem());
                    newRes.setOrdertestLineRemark(orderTestLineListDTO.getRemark());
                    newRes.setTestConditionNameCN(subContractDTO.getTestConditionNameCN());
                }
                testLineInstanceSubContractRsps.add(newRes);
            }
            vo.setTestItemList(testLineInstanceSubContractRsps);
            vo.setSampleList(sampleRsps);


            SubcontractRequirementInfo subcontractRequirementBySubcontractNo = subcontractRequirementMapper.getSubcontractRequirementBySubcontractNo(subContractPO.getSubContractNo());
            // 需要优化
            if (Func.isNotEmpty(subcontractRequirementBySubcontractNo) && Func.isNotEmpty(subcontractRequirementBySubcontractNo.getReportRequirement())) {
                switch (subcontractRequirementBySubcontractNo.getReportRequirement()) {
                    case "1":
                        vo.setReportType(ReportTypeEnum.PDF.getCode());
                        break;
                    case "2":
                        vo.setReportType(ReportTypeEnum.WORD.getCode());
                        break;
                    case "4":
                        vo.setReportType(ReportTypeEnum.TESTING_ONLY.getCode());
                        break;
                    default:
                        vo.setReportType(" ");
                }
            } else {
                vo.setReportType(" ");
            }

            vo.setSubLabCode(subContractPO.getSubContractLabCode());
            vo.setSubContract(subContractPO.getSubContractContract());
            vo.setSubcontractTel(subContractPO.getSubContractContractTel());
            vo.setOrderLabCode(labCode);
            vo.setOrderContract(orderByOrderNo.getCSName());
            vo.setOrderTel(cSContactTel);
            vo.setOrderEmail(cSEmailAddress);
            if (Func.isNotEmpty(subcontractRequirementBySubcontractNo)) {
                vo.setQualificationType(subcontractRequirementBySubcontractNo.getQualificationType());
                Integer needConclusionVal = subcontractRequirementBySubcontractNo.getNeedConclusion();
                String needConclusion = "";
                if (Func.isNotEmpty(needConclusionVal)) {
//                    if(needConclusionVal.compareTo(0) == 0){
//                        needConclusion = "No";
//                    }else if(needConclusionVal.compareTo(1) == 0){
//                        needConclusion = "Yes";
//                    }else if(needConclusionVal.compareTo(2) == 0){
//                        needConclusion = "Part";
//                    }
                    if (needConclusionVal.compareTo(0) == 0) {
                        needConclusion = "Not Need Conclusion";
                    } else if (needConclusionVal.compareTo(1) == 0) {
                        needConclusion = "Report Conclusion";
                    } else if (needConclusionVal.compareTo(2) == 0) {
                        needConclusion = "Need TL Conclusion";
                    } else if (needConclusionVal.compareTo(3) == 0) {
                        needConclusion = "Not Need TL Conclusion";
                    } else if (needConclusionVal.compareTo(4) == 0) {
                        needConclusion = "Part TL Conclusion";
                    }
                }
                vo.setConclusion(needConclusion);
                vo.setQrCode("Y".equals(subcontractRequirementBySubcontractNo.getQrcodeFlag()) ? "Yes" : "NO");
                vo.setReportQty(subcontractRequirementBySubcontractNo.getReportQty() + "");
                vo.setPhotoRemark(subcontractRequirementBySubcontractNo.getTakePhotoRemark());
                if (Func.equals(subcontractRequirementBySubcontractNo.getReturnTestedSampleFlag(), 1) || Func.equals(subcontractRequirementBySubcontractNo.getReturnResidueSampleFlag(), 1)) {
                    vo.setSampleReturn("Yes");
                } else {
                    vo.setSampleReturn("NO");
                }
                vo.setDraftRequired(subcontractRequirementBySubcontractNo.getDraftReportRequired() == 1 ? "Yes" : "NO");
                vo.setLiquidTestSample(subcontractRequirementBySubcontractNo.getLiquidTestSample());
                if (subcontractRequirementBySubcontractNo.getTakePhotoFlag() != null && subcontractRequirementBySubcontractNo.getTakePhotoFlag() == 1) {
                    vo.setPhotoRequired("YES");
                } else {
                    vo.setPhotoRequired("NO");
                }
            } else {
                vo.setQualificationType(" ");
                vo.setConclusion(" ");
                vo.setQrCode(" ");
                vo.setReportQty(" ");
                vo.setPhotoRemark(" ");
                vo.setSampleReturn(" ");
            }
            vo.setDeadline(subContractPO.getSubContractExpectDueDate());
            vo.setSampleQty(vo.getSampleList().size() + "");
            vo.setSubcontractRemark(subContractPO.getSubContractRemark());

            vo.setCreatedby(subContractPO.getCreatedBy());
            vo.setDate(Func.formatDateTime(new Date()));
            vo.setSampleForLiquid(sampleForLiquidBuParam.getParamValue());
            SubContractPrintRspList.add(vo);
        });
        rspResult.setData(SubContractPrintRspList);
        rspResult.setSuccess(true);
        return rspResult;
    }


    @Override
    public CustomResult searchLabList(GpnLabListReq req) {
        CustomResult result = new CustomResult();
        String url = interfaceConfig.getBaseUrl() + "/FrameWorkApi/trims/api/v1/queryLabListForPage";
        Map<String, Object> params = new HashMap<>();
        params.put("page", req.getPage());
        params.put("rows", req.getRows());
        params.put("labCode", req.getLabCode());
        params.put("labName", req.getLabName());
        params.put("contactName", req.getContactName());
        logger.info(url);
        logger.info(params.toString());
        String data = HttpClientUtil.post(url, params);
        logger.info(data);
        PageInfoList info = JSONObject.parseObject(data, PageInfoList.class);
        result.setData(info);
        result.setSuccess(true);
        return result;
    }

    private void cancelSubOrder(SubContractPO subcontract, String execOrderNo, UserInfo user) {
        String subContractNo = subcontract.getSubContractNo();
        //cancel 子单的tl/sample等数据
        cancelChildOrder(Lists.newArrayList(execOrderNo), user);
        logger.info("subContractNo：{} cancel操作 orderNo:{}", subContractNo, execOrderNo);
        //通知preorder 进行cancel操作
        OrderIdReq orderIdReq = new OrderIdReq();
        orderIdReq.setOrderNo(execOrderNo);
        BaseResponse baseResponse = orderFacade.updateOrderStatusBySubContract(orderIdReq);
        if (baseResponse.getStatus() != 200) {
            logger.error("内部分包cancle，请求preorder异常,返回result为null");
        }
       /* String url = interfaceConfig.getPreorderApiUrl()+"/order/updateOrderStatusBySubContract";
        //url = "http://localhost:8082/OrderApi/order/updateOrderStatusBySubContract";
        Map<String,Object> param = new HashMap<>();
        param.put("orderNo",execOrderNo);
        param.put("userInfo", JSONObject.toJSONString(user));
        try{
            String result = HttpClientUtil.post(url, param);
            logger.info("subContractNo：{} cancel操作 orderNo:{} preorder响应：{}",subContractNo,execOrderNo,result);
            if(org.apache.commons.lang.StringUtils.isBlank(result)){
                logger.error("内部分包cancle，请求preorder异常,返回result为null");
            }

        }catch (Exception e){
            logger.error("内部分包cancle，请求preorder异常：",e);
        }*/

    }


    public void cancelChildOrder(List<String> childOrderNo, UserInfo user) {
        if (CollectionUtils.isNotEmpty(childOrderNo)) {
            for (String orderNo : childOrderNo) {
                GeneralOrderInstanceInfoPO order = orderMapper.getOrderInfo(orderNo);
                UserHelper.setLocalUser(user);

                cancelTestLine(order, user, "");
                cancelSample(order, user, "order");
                cancelReport(order, user);

                // 更新JOB状态为1104
                Map<String, Object> params = new HashMap<>();
                params.put("generalOrderInstanceID", order.getID());
                params.put("modifiedBy", user.getRegionAccount());
                jobExtMapper.updateJobStateByGeneralOrderInstanceID(params);
                order.setActiveIndicator(false);
                generalOrderInstanceInfoMapper.updateByPrimaryKeySelective(order);

                // 放到redis中，页面每次查询的时候不用都请求DB
                String redisKey = "order_cancel_:" + orderNo;
                // 存储24h
                CacheAgent.getInstance().set(redisKey, order, 60 * 60 * 24);
            }
        }
    }

    private void cancelReport(GeneralOrderInstanceInfoPO order, UserInfo user) {
        ReportInfoExample reportInfoExample = new ReportInfoExample();
        reportInfoExample.createCriteria().andOrderNoEqualTo(order.getOrderNo());
        List<ReportInfoPO> RpList = reportInfoMapper.selectByExample(reportInfoExample);
        if (RpList != null && RpList.size() > 0) {
            for (ReportInfoPO reportPO : RpList) {
                reportPO.setActiveIndicator(false);
                reportPO.setReportStatus(202);
                reportInfoMapper.updateByPrimaryKeySelective(reportPO);
            }
        }
    }

    private void cancelSample(GeneralOrderInstanceInfoPO order, UserInfo user, String type) {
        List<TestSampleGroupPO> sampleGroupPOs = sampleMapper.querySampleGroupByOrderNo(order.getOrderNo());

        if (CollectionUtils.isNotEmpty(sampleGroupPOs)) {
            for (TestSampleGroupPO sampleGroupPO : sampleGroupPOs) {
                sampleGroupPO.setActiveIndicator(false);
                sampleGroupPO.setModifiedBy(user.getRegionAccount());
            }
            sampleMapper.batchUpdateSampleGroupStatus(sampleGroupPOs);
        }
        TestSampleInfoPO samplePO = new TestSampleInfoPO();
        samplePO.setActiveIndicator(false);
        samplePO.setModifiedBy(user.getRegionAccount());
        samplePO.setOrderNo(order.getOrderNo());
        sampleMapper.cancelSampleByOrderNO(samplePO);
    }

    private void cancelTestLine(GeneralOrderInstanceInfoPO order, UserInfo user, String token) {
        String generalOrderInstanceID = order.getID();
        String modifiedBy = user.getRegionAccount();
        TestLineInstancePO testLineInstancePO = new TestLineInstancePO();
        testLineInstancePO.setTestLineStatus(706);
        testLineInstancePO.setActiveIndicator(false);
        testLineInstancePO.setGeneralOrderInstanceID(generalOrderInstanceID);
        testLineInstancePO.setModifiedBy(modifiedBy);
        testLineMapper.cancelTestLineByGeneralOrderId(testLineInstancePO);

        TestMatrixPO testMatrixPO = new TestMatrixPO();
        testMatrixPO.setActiveIndicator(false);
        testMatrixPO.setGeneralOrderInstanceID(generalOrderInstanceID);
        testMatrixPO.setModifiedBy(modifiedBy);
        testMatrixMapper.cancelMatrixByGeneralOrderId(testMatrixPO);

        ConclusionInfoPO conclusionPO = new ConclusionInfoPO();
        conclusionPO.setActiveIndicator(false);
        conclusionPO.setGeneralOrderInstanceID(generalOrderInstanceID);
        conclusionPO.setModifiedBy(modifiedBy);
        conclusionMapper.cancelConclusionByGeneralOrderInstanceID(conclusionPO);

        Map<String, Object> params = new HashMap<String, Object>();
        params.put("modifiedBy", modifiedBy);
        params.put("generalOrderInstanceID", generalOrderInstanceID);
        jobExtMapper.updateJobStateByGeneralOrderInstanceID(params);
    }

    public String getNewSubContractNo(String oldSubContractNo, String subContractLabCode) {
        String[] subContracts = oldSubContractNo.split("[\\d]");
        int lastIndex = 0;
        if (subContracts.length > 1) {
            lastIndex = StringUtils.lastIndexOf(oldSubContractNo, subContracts[subContracts.length - 1]);
        }
        String newSubContractNo = oldSubContractNo;
        if (lastIndex > 0) {
            newSubContractNo = StringUtils.substring(oldSubContractNo, 0, lastIndex);
        }
        if (StringUtils.isEmpty(subContractLabCode)) {
            subContractLabCode = "";
        }
        subContractLabCode = subContractLabCode.replace(" ", "");
        if (subContractLabCode.length() > 5) {
            subContractLabCode = subContractLabCode.substring(0, 5);
        }
        return newSubContractNo + subContractLabCode;
    }

    public void checkPreOrderStatus(String token, OrderInfoDto order) {
        Map<String, Object> params = new HashMap<String, Object>();
        params.put("generalOrderInstanceID", order.getID());
        params.put("testLineStatus", 708);

        // 验证是否所有的testLine都被validate
        List<TestLineInstancePO> testLineLists = testLineMapper.queryByParams(params);

        int count = 0;
        int validCount = 0;
        for (TestLineInstancePO testLine : testLineLists) {
            Integer testLineType = testLine.getTestLineType();
            if (testLineType == null) {
                testLineType = 0;
            }
            if (testLine.getTestLineStatus() == 703
                    || testLine.getTestLineStatus() == 707
                    || testLine.getTestLineStatus() == 706
                    || (testLineType.intValue() & 1) > 0) {
                count++;
            }
            if (testLine.getTestLineStatus() != 707
                    && testLine.getTestLineStatus() != 706) {
                validCount++;
            }
        }
        // 所有的testline都为Completed或Subcontracted或cancelled状态   且 至少有一条有效的TL
        if (validCount > 0 && count == testLineLists.size()) {

            GeneralOrderInstanceInfoPO generalOrderInstanceInfoPO = new GeneralOrderInstanceInfoPO();
            generalOrderInstanceInfoPO.setID(order.getID());
            generalOrderInstanceInfoPO.setOrderStatus(1603);
            orderMapper.updateOrderStatus(generalOrderInstanceInfoPO);
            //用orderNo获取orderStatus
            try {
                OrderIdReq orderIdReq = new OrderIdReq();
                orderIdReq.setOrderNo(order.getOrderNo());
                orderIdReq.setProductLineCode(ProductLineContextHolder.getProductLineCode());
                BaseResponse<OrderInfoDto> orderInfoByOrderNo = orderFacade.getOrderInfoByOrderNo(orderIdReq);
                Integer str = orderInfoByOrderNo.getData().getOrderStatus();
                logger.info("call preorder interface getorderstatus : " + str);
                if (StringUtils.isNotBlank(str + "")) {
                    logger.info("OrderNo:{} 【dataEntry validate操作】执行preorder状态更新，odlStatus:{}-newStatus-{}", order.getOrderNo(), CommUtil.null2Int(str), 9);
                    Boolean success = preorderInsertChangeLog(token, "order", order.getOrderNo(), CommUtil.null2Int(str), 9, new Date());
                    /*if(order1==null){
                        logger.info("OrderNo:{} 【dataEntry validate操作】执行preorder状态更新【Fail】，odlStatus:{}-newStatus-{},Preorder接口返回响应为null",order.getOrderNo(),CommUtil.null2Int(str),9);
                    }else{
                        logger.info("OrderNo:{} 【dataEntry validate操作】执行preorder状态更新【{}】，odlStatus:{}-newStatus-{}",order.getOrderNo(),order1!=null && order1.getIsSuccess()?"Success":"Fail",CommUtil.null2Int(str),9);
                    }*/
                }
            } catch (Exception e) {
                logger.error("OrderNo:{}", order.getOrderNo(), e.getMessage());
            }

        }
    }

    public Boolean preorderInsertChangeLog(String token, String objectType, String objectNo,
                                           Integer oldStatus, Integer newStatus, Date operationDate) {
        SysStatusReq sysStatusReq = new SysStatusReq();
        sysStatusReq.setProductLineCode(ProductLineContextHolder.getProductLineCode());
        sysStatusReq.setObjectType(objectType);
        sysStatusReq.setObjectNo(objectNo);
        sysStatusReq.setOldStatus(oldStatus);
        sysStatusReq.setNewStatus(newStatus);
        sysStatusReq.setFromSystemId(SgsSystem.GPO.getSgsSystemId());
        sysStatusReq.setToSystemId(SgsSystem.GPO.getSgsSystemId());
        BaseResponse baseResponse = gpoStatusFacade.insertStatusInfo(sysStatusReq);
        int status = baseResponse.getStatus();
        return status == 1 ? true : false;
    }


    @Override
    public CustomResult QuerySubContractBySubContractNo(GPOSubContractReq req) {
        CustomResult result = new CustomResult();
        SubContractExample subContractExample = new SubContractExample();
        subContractExample.createCriteria().andSubContractNoEqualTo(req.getSubContractNo());
        List<SubContractPO> subContractPOS = subContractMapper.selectByExample(subContractExample);
        result.setData(subContractPOS.get(0));
        return result;
    }

    @Override
    public BaseResponse<SubContractInfo> queryOrderIsSubOrder(GPOSubContractReq req) {
        BaseResponse result = new BaseResponse();

        if (Func.isEmpty(req.getOrderNo())) {
            result.setData(null);
            result.setStatus(ResponseCode.UNKNOWN.getCode());
            return result;
        }

        SubContractInfo subContractInfo = subContractExtMapper.queryOrderSubContractInfo(req.getOrderNo());
        result.setData(subContractInfo);

        result.setStatus(ResponseCode.SUCCESS.getCode());
        return result;
    }
    /**
     *
     * @param subContractNo
     * @param reportId
     * @param subReportPOListNew
     * @param subcontractId
     */
    private void insertSubReportRel(String subContractNo, String reportId, List<SubReportPO> subReportPOListNew, String subcontractId) {
        if (StringUtils.isEmpty(reportId) || CollectionUtils.isEmpty(subReportPOListNew)) {
            return;
        }
        logger.info("subContractNo:{}, {}Word文件需要建立关系:", subContractNo, reportId);
        for (SubReportPO subReportPO : subReportPOListNew){
            List<String> reportIds;
            if (StringUtils.isNotEmpty(subcontractId)) {
                // Bind关联发包方需要根据subcontractNo查找关联的report
                reportIds = reportMatrixRelMapper.queryReportListBySubcontract(subcontractId);
                List<SubContractMatrixInfo> subContractMatrixInfos = reportMatrixRelMapper.queryMatrixIdBySubcontract(subcontractId);
                logger.info("subContractNo:{}, {} 查询后的 reportMatrix 信息:{}", subContractNo, reportId, JSON.toJSONString(subContractMatrixInfos));
            } else {
                // 根据matrix查询对应的reportId
                reportIds = reportMatrixRelMapper.queryReportListByMatrix(reportId);
                List<SubContractMatrixInfo> subContractMatrixInfos = reportMatrixRelMapper.queryMatrixIdByReportId(reportId);
                logger.info("subContractNo:{}, {} 查询后的 reportMatrix 信息:{}", subContractNo, reportId, JSON.toJSONString(subContractMatrixInfos));
            }
            logger.info("subContractNo:{}, {}Word文件需要建立关系:{}", subContractNo, reportId, JSON.toJSONString(reportIds));
            if (CollectionUtils.isNotEmpty(reportIds)) {
                reportIds.stream().forEach(id -> {
                    // 维护信息到tre_report_sub_report_relationship
                    ReportSubReportRelationshipInfoPO insertReportRel = new ReportSubReportRelationshipInfoPO();
                    insertReportRel.setId(UUID.randomUUID().toString());
                    insertReportRel.setReportId(id);
                    insertReportRel.setSubReportId(subReportPO.getID());
                    insertReportRel.setActiveIndicator(ActiveType.Enable.getStatus().byteValue());
                    insertReportRel.setCreatedDate(DateUtils.now());
                    reportSubReportRelationshipInfoMapper.insert(insertReportRel);
                });
            }
        }

    }



    @Override
    public CustomResult getSubReportList(GpnSubReportReq req) {
        SubContractPO subContractPO = subContractMapper.selectByPrimaryKey(req.getSubContractId());
        SubContractExternalRelationshipPO searchPO = new SubContractExternalRelationshipPO();
        searchPO.setSubContractNo(subContractPO.getSubContractNo());
        List<SubContractExternalRelationshipPO> subContractSlimJobPOS = subContractExternalRelService.getSubContractExternalRels(searchPO).getData();
        SubReportExample subReportExample = new SubReportExample();
        List<String> subContractNoList = new ArrayList<>();
        List<String> objectTypeList = new ArrayList<>();
        subContractNoList.add(subContractPO.getSubContractNo());
        objectTypeList.add(req.getObjectType());
        if (Func.isNotEmpty(subContractSlimJobPOS)) {
            objectTypeList.add("slimjob");
            subContractNoList.add(subContractSlimJobPOS.get(0).getExternalNo());
        }
        subReportExample.createCriteria().andObjectNoIn(subContractNoList).andObjectTypeIn(objectTypeList);
        subReportExample.setOrderByClause("CreatedDate desc");
        List<SubReportPO> subReportPOs = subReportMapper.selectByExample(subReportExample);
        List<SubReportListRsp> subReportListRsps = new ArrayList<>();
        subReportPOs.forEach(item -> {
            SubReportListRsp subReportListRsp = new SubReportListRsp();
            BeanUtil.copyProperties(item, subReportListRsp);
            subReportListRsp.setSubContractLabCode(subContractPO.getSubContractLabCode());
            subReportListRsp.setSubContractLabName(subContractPO.getSubContractLabName());
            subReportListRsps.add(subReportListRsp);
        });
        CustomResult result = new CustomResult();
        result.setData(subReportListRsps);
        result.setSuccess(true);
        return result;
    }

    @Override
    public BaseResponse<List<SubReportInfo>> getSubReportList(com.sgs.otsnotes.facade.model.gpn.subreport.req.SubReportReq req) {
//        SubContractPO subContractPO = subContractMapper.selectByPrimaryKey(req.getSubContractId());
        List<SubReportInfo> subReports = subReportExtMapper.getSubReports(req);
        //处理外部号展示
        List<ExternalReportNoRsp> externalSubReportNoList = null;
        List<ExternalReportNoRsp> externalReportNoList = null;
        if(Func.isEmpty(subReports)){
            return BaseResponse.newSuccessInstance(subReports);
        }
        if (Func.isNotEmpty(subReports)) {
            List<String> subReportNos = subReports.stream().map(SubReportInfo::getSubReportNo).distinct().collect(Collectors.toList());
            externalSubReportNoList = orderClient.getExternalReportNoByReportNo(subReportNos, ProductLineContextHolder.getProductLineCode());
            List<GetReportInfo> reportInfoList = subReports.stream().flatMap(tl -> tl.getReports().stream()).collect(Collectors.toList());
            if (Func.isNotEmpty(reportInfoList)) {
                List<String> reportNos = reportInfoList.stream().map(GetReportInfo::getReportNo).distinct().collect(Collectors.toList());
                externalReportNoList = orderClient.getExternalReportNoByReportNo(reportNos, ProductLineContextHolder.getProductLineCode());
            }
        }
        for (SubReportInfo subReport : subReports) {
            if (Func.isNotEmpty(externalSubReportNoList)) {
                String externalSubReportNo = externalSubReportNoList.stream().filter(i -> Func.equals(i.getReportNo(), subReport.getSubReportNo())).findFirst().orElse(new ExternalReportNoRsp()).getExternalReportNo();
                if(Func.isNotEmpty(externalSubReportNo)){
                    subReport.setExternalSubReportNo(externalSubReportNo);
                }else{
                    subReport.setExternalSubReportNo(subReport.getSubReportNo());
                }
            } else {
                subReport.setExternalSubReportNo(subReport.getSubReportNo());
            }
            for (GetReportInfo report : subReport.getReports()) {
                if (Func.isNotEmpty(externalReportNoList)) {
                    String externalReportNo = externalReportNoList.stream().filter(i -> Func.equals(i.getReportNo(), report.getReportNo())).findFirst().orElse(new ExternalReportNoRsp()).getExternalReportNo();
                    if(Func.isNotEmpty(externalReportNo)){
                        report.setExternalReportNo(externalReportNo);
                    }else{
                        report.setExternalReportNo(subReport.getExternalSubReportNo());
                    }
                }else{
                    report.setExternalReportNo(subReport.getExternalSubReportNo());
                }
            }
            subReport.setUploadDate(subReport.getCreatedDate());
            if(SubReportObjectTypeEnums.check(subReport.getObjectType(),SubReportObjectTypeEnums.starlims)){
                subReport.setUploadDate(Func.isNotEmpty(subReport.getModifiedDate())?subReport.getModifiedDate():subReport.getCreatedDate());
            }
            // 基于SubReport关联的报告状态设置Combine禁用状态
            List<ReportSubReportDTO> reportList =  reportMapper.getReportBySubReportID(subReport.getSubReportId());
            subReport.setDisableCombine(reportList.stream().anyMatch(item -> !ReportStatus.checkCategory(item.getReportStatus(), Constants.REPORT.STATUS_CATEGORY.INACTIVE)
                    && !ReportStatus.check(item.getReportStatus(), ReportStatus.New)));
        }
        String reportRequirement = "";

        List<SubReportInfo> resultList = new ArrayList<>();
        Integer draftReportRequired = null;
        if (SubReportObjectTypeEnums.check(req.getObjectType(), SubReportObjectTypeEnums.job) && Func.isNotEmpty(req.getJobId())) {
            JobInfoPO jobInfoPO = jobInfoMapper.selectByPrimaryKey(req.getJobId());
            if(Func.isNotEmpty(jobInfoPO)){
                OrderIdReq orderIdReq = new OrderIdReq();
                orderIdReq.setOrderNo(jobInfoPO.getOrderNo());
                OrderAllDTO orderAllDTO = orderFacade.getOrderForPe(orderIdReq).getData();
                if(Func.isNotEmpty(orderAllDTO)){
                    draftReportRequired = orderAllDTO.getDraftReportRequired();
                    reportRequirement = orderAllDTO.getReportRequirement();
                }
            }
        }else{
            String subContractId = req.getSubContractId();
            SubcontractRequirementInfo subcontractRequirement = subcontractRequirementMapper.getSubcontractRequirementBySubcontractId(subContractId);
            if(Func.isNotEmpty(subcontractRequirement)){
                draftReportRequired = subcontractRequirement.getDraftReportRequired();
                reportRequirement = subcontractRequirement.getReportRequirement();
            }else{
                reportRequirement = ReportRequirementEnum.Customer_Report_Word.getCode();
                draftReportRequired = 0;
            }
        }
        String finalReportRequirement = reportRequirement;
        Map<String, List<SubReportInfo>> subReportMap = subReports.stream().collect(Collectors.groupingBy(b ->   Func.isEmpty(b.getSubReportNo())  ? "" : b.getSubReportNo()));
        for (Map.Entry<String, List<SubReportInfo>> valueMap : subReportMap.entrySet()) {
            String key = valueMap.getKey();
            List<SubReportInfo> matchSubReportList = subReports.stream().filter(item->Func.equalsSafe(item.getSubReportNo(),key)).collect(Collectors.toList());
            List<SubReportInfo> subDraftReportListRsps = new ArrayList<>();
            List<SubReportInfo> subFinalReportListRsps = new ArrayList<>();
            if(!Func.equalsSafe(req.getObjectType(),"job")){
                subDraftReportListRsps = matchSubReportList.stream().filter(reportFilePO -> reportFileService.isDraftReportFile(finalReportRequirement,Func.toStr(reportFilePO.getReportFileType())) && StringUtils.isNotEmpty(reportFilePO.getCloudId()))
                        .collect(Collectors.toList());
                if(Func.isNotEmpty(draftReportRequired) && draftReportRequired != 1){
                    subDraftReportListRsps = subDraftReportListRsps.stream().filter(i->!SubReportStatusEnum.check(i.getSubReportStatus(),SubReportStatusEnum.Approved)).collect(Collectors.toList());
                }
                subFinalReportListRsps = matchSubReportList.stream().filter(reportFilePO -> reportFileService.isFinalReportFile(finalReportRequirement,Func.toStr(reportFilePO.getReportFileType())) && StringUtils.isNotEmpty(reportFilePO.getCloudId()))
                        .collect(Collectors.toList());
                if(Func.isNotEmpty(draftReportRequired) && draftReportRequired != 1){
                    subFinalReportListRsps = subFinalReportListRsps.stream().filter(i->!SubReportStatusEnum.check(i.getSubReportStatus(),SubReportStatusEnum.Approved)).collect(Collectors.toList());
                }
                if(Func.isNotEmpty(subFinalReportListRsps)){
                    resultList.addAll(subFinalReportListRsps);
                }else{
                    resultList.addAll(subDraftReportListRsps);
                }
            }
            else {
                resultList.addAll(matchSubReportList);
            }
        }
        return BaseResponse.newSuccessInstance(resultList);
    }

    @Override
    public BaseResponse saveSubContractReport(GpnBatchSaveSubReportReq batchSaveSubReportReq) {
        if (Func.isEmpty(batchSaveSubReportReq) || Func.isEmpty(batchSaveSubReportReq.getSaveSubReportReqList())) {
            return BaseResponse.newFailInstance("Request Param can not be null");
        }
        UserInfo user = tokenClient.getUser();
        if (Func.isEmpty(user)) {
            user = tokenClient.getUser(batchSaveSubReportReq.getToken());
        }
        if (Func.isEmpty(user)) {
            return BaseResponse.newFailInstance("Get User Fail!");
        }
        List<GpnSaveSubReportReq> saveSubReportReqList = batchSaveSubReportReq.getSaveSubReportReqList();
        for (GpnSaveSubReportReq req : saveSubReportReqList) {
            if (StringUtils.isBlank(req.getObjectNo())) {
                return BaseResponse.newFailInstance("ObjectNo can not be null");
            }
            if (StringUtils.isBlank(req.getObjectType())) {
                return BaseResponse.newFailInstance("ObjectType can not be null");
            }
            String objectType = req.getObjectType();
            List<SubReportReq> subReports = req.getSubReports();
            if (Func.isEmpty(subReports)) {
                return BaseResponse.newFailInstance("please select file");
            }
            if (SubReportObjectTypeEnums.check(objectType, SubReportObjectTypeEnums.job)) {
                JobInfoPO jobInfoPO = jobExtMapper.getByJobNo(req.getObjectNo());
                if (Func.isEmpty(jobInfoPO)) {
                    return BaseResponse.newFailInstance("Not Find Job Info");
                }
                if (JobStatus.check(jobInfoPO.getJobStatus(), JobStatus.New, JobStatus.Cancelled)) {
                    return BaseResponse.newFailInstance(String.format("Job Status[%s] Not Allow Upload Report", JobStatus.getJobStatus(jobInfoPO.getJobStatus())));
                }
                req.setOrderNo(jobInfoPO.getOrderNo());
                req.setOrderId(jobInfoPO.getGeneralOrderInstanceID());
            } else {
//                QuerySubContractReq querySunContractReq = new QuerySubContractReq();
//                querySunContractReq.setSubcontractNo(req.getObjectNo());;
//                List<QuerySubContractRsp> subContractRsps = subContractExtMapper.querySubcontract(querySunContractReq);
                SubContractInfo subContractInfo =  subContractExtMapper.getSubContractInfo(req.getObjectNo());
                String orderId = null;
                if (Func.isNotEmpty(subContractInfo)) {
                    orderId = subContractInfo.getGeneralOrderId();
                    req.setOrderNo(subContractInfo.getOrderNo());
                }
                if (StringUtils.isBlank(orderId)) {
                    return BaseResponse.newFailInstance("SubContractNo can not be null");
                }
                req.setOrderId(orderId);
            }
                if (!SubReportObjectTypeEnums.check(objectType, SubReportObjectTypeEnums.job)) {
                    List<String> subReportNoList = subReports.stream().map(SubReportReq::getSubReportNo).collect(Collectors.toList());
                    Set<String> subReportNoSet = subReports.stream().map(SubReportReq::getSubReportNo).collect(Collectors.toSet());
                    if (!Func.equals(subReportNoList.size(), subReportNoSet.size())) {
                        return BaseResponse.newFailInstance("SubReport No must be unique");
                    }
                }
                for (SubReportReq subReportReq : subReports) {
                    if (StringUtils.isBlank(subReportReq.getFileID()) || StringUtils.isBlank(subReportReq.getSubReportNo())) {
                        return BaseResponse.newFailInstance("fileId or subReportNo can not be null");
                    }
                    String subReportNo = subReportReq.getSubReportNo();
                    if(subReportNo.length()>50){
                        return BaseResponse.newFailInstance("SubReportNo["+subReportNo+"]requires a maximum of 50, please re-enter!");
                    }
                    if (Func.isNotEmpty(subReportExtMapper.getSubReportBySubReportNo(subReportReq.getSubReportNo()))) {
                        return BaseResponse.newFailInstance("SubReport No " + subReportReq.getSubReportNo() + "  Already exists ");
                    }
                    subReportReq.setReportSource("Upload");
                }
        }
        CustomResult rspResult = subContractService.saveSubContractReport(saveSubReportReqList, user);
        return BaseResponse.newSuccessInstance(rspResult);
    }

    @Override
    public BaseResponse deleteSubReportById(GpnDelSubReportByIdReq req) {
        return subContractService.deleteSubReportById(req);
    }

    @Override
    public String getSubContractNumber(String token, String labCode, String orderNo, String subContractLabCode, String otherCode, OrderInfoDto oldOrder) {
        log.info("getSubContractNumber req orderNo,{}", orderNo);
        String orderNoRegex = "^(?<locationBu>[A-Za-z]{4,})(?<date>[0-9]{4,})(?<seq>\\d{6,})";
        Matcher matcher = Pattern.compile(orderNoRegex).matcher(orderNo);
        if (!matcher.find()) throw new RuntimeException("get OrderNoRegex Error!");

        // 获取订单号前缀
        AtomicReference<String> prefix = new AtomicReference<>("");
        prefix.set(matcher.group("locationBu"));

        // 截取掉订单号尾缀
        if (Pattern.compile(".*[A-Za-z]$").matcher(orderNo).find()) {
            orderNo = orderNo.substring(0, orderNo.length() - 2);
        }

        //组装transactionNo
        AtomicReference<String> transactionNo = new AtomicReference<>("");
        transactionNo.set(orderNo + "_" + matcher.group("seq") + "S");
        otherCode = otherCode.replaceAll("[^(a-zA-Z0-9)]", "");
        otherCode = otherCode.length() <= 6 ? otherCode : otherCode.substring(0, 6);
        return frameWorkClient.getNumber(oldOrder.getLocationID(), "GPOSubcontractNoNew", otherCode, prefix.get(), oldOrder.getBUID(), transactionNo.get());
    }

    @Override
    public CustomResult checkReportQty(GpnSaveSubContractReportReq req) {
        CustomResult result = new CustomResult();
//        校验Report Qty的数量是否 < Report Requiment （忽略语言）|| qty = 0
        if (com.sgs.framework.tool.utils.StringUtil.isEmpty(req.getSubcontractNo())) {
            result.setSuccess(false);
            result.setMsg("SubContractNo can not be null");
            return result;
        }
        List<String> getSubcontractNos = Lists.newArrayList(req.getSubcontractNo().split(","));
        //查询Report Requirement类型
        SubcontractRequirementInfoExample subcontractRequirementInfoExample = new SubcontractRequirementInfoExample();
        subcontractRequirementInfoExample.createCriteria().andSubContractNoIn(getSubcontractNos);
        List<SubcontractRequirementInfoPO> subcontractRequirementInfoPOS = subcontractRequirementInfoMapper.selectByExample(subcontractRequirementInfoExample);
        if (CollectionUtils.isEmpty(subcontractRequirementInfoPOS)) {
            result.setSuccess(false);
            result.setMsg("By SubContractNo can not find SubcontractRequirementInfo");
            return result;
        }

        List<String> needConfirmSubContractList = new ArrayList<>();
        for (SubcontractRequirementInfoPO subcontractRequirementInfoPO : subcontractRequirementInfoPOS) {
            String reportRequirement = subcontractRequirementInfoPO.getReportRequirement();
            //获取当前subReport的数量
            SubReportExample subReportExample = new SubReportExample();
            subReportExample.createCriteria().andObjectNoEqualTo(subcontractRequirementInfoPO.getSubContractNo());
            List<SubReportPO> subReportPOS = subReportMapper.selectByExample(subReportExample);
            List<String> subReportIds = Lists.newArrayList();
            String fileType = StringUtils.equals(SubContractReportRequirement.CUSTOMER_REPORT_PDF.getCode(), reportRequirement) ? "pdf" : "doc";
            subReportPOS.stream().forEach(subReport -> {
                String suffix = subReport.getFilename().substring(subReport.getFilename().lastIndexOf(".") + 1);
                if (Func.isNotEmpty(suffix) && suffix.contains(fileType) && SubReportStatusEnum.check(subReport.getStatus(),SubReportStatusEnum.Completed)) {
                    subReportIds.add(subReport.getID());
                }
            });
            //比较Requirement Report Qty
            Integer requirementReportQty = subcontractRequirementInfoPO.getReportQty();
            if (Func.isNotEmpty(requirementReportQty) && subReportIds.size() < requirementReportQty) {
                needConfirmSubContractList.add(subcontractRequirementInfoPO.getSubContractNo());
            }
        }
        result.setSuccess(true);
        result.setData(needConfirmSubContractList);
        return result;
    }

    @Override
    public com.sgs.framework.facade.domain.utils.BaseResponse<Boolean> checkWhetherCompleted(GpnSaveSubContractReportReq req) {
        com.sgs.framework.facade.domain.utils.BaseResponse<Boolean> result = new com.sgs.framework.facade.domain.utils.BaseResponse();
//        SubReportExample subReportExample = new SubReportExample();
//        subReportExample.createCriteria().andObjectNoEqualTo(req.getSubcontractNo()).andStatusNotIn(Lists.newArrayList(Func.toStr(SubReportStatusEnum.Reworked.getCode(), Func.toStr(SubReportStatusEnum.Cancelled.getCode()))));
//        boolean checkReportCount = false;
//        List<SubReportPO> subReportPOList = subReportMapper.selectByExample(subReportExample);
        BaseResponse<List<SubReportPO>> subReportListRes = subReportService.querySubReportBySubcontractNo(req.getSubcontractNo());
        if(Func.isEmpty(subReportListRes)|| Func.isEmpty(subReportListRes.getData())){
            logger.info("subReport count is zero,can not auto Completed");
            result.setSuccess(true);
            result.setMessage("subReport count is zero,can not auto Completed");
            result.setData(false);
            return result;
        }
        List<SubReportPO> subReportList = subReportListRes.getData();
        subReportList = subReportList.stream().filter(i->!SubReportStatusEnum.check(i.getStatus(),SubReportStatusEnum.Reworked,SubReportStatusEnum.Cancelled)).collect(Collectors.toList());
        if (Func.isEmpty(subReportList)) {
            logger.info("subReport count is zero,can not auto Completed");
            result.setSuccess(true);
            result.setMessage("subReport count is zero,can not auto Completed");
            result.setData(false);
            return result;
        }
        //查询subContract
        String subcontractNo = req.getSubcontractNo();
        SubContractInfo subContractInfo = subContractExtMapper.getSubContractInfo(subcontractNo);
        if(Func.isEmpty(subContractInfo)){
            result.setSuccess(true);
            result.setMessage("Get subContractInfo Fail");
            result.setData(false);
            return result;
        }

        SubcontractRequirementInfo subcontractRequirementInfo = subcontractRequirementMapper.getSubcontractRequirementBySubcontractNo(subcontractNo);
        Integer reportQty = 1;
        //所有语言的报告数量全部满足才允许Completed
        String reportRequirement = ReportRequirementEnum.Customer_Report_Word.getCode();
        if(Func.isNotEmpty(subcontractRequirementInfo)){
            if(Func.isNotEmpty(subcontractRequirementInfo.getReportRequirement())){
                reportRequirement = subcontractRequirementInfo.getReportRequirement();
            }
            if(Func.isNotEmpty(subcontractRequirementInfo.getReportQty())){
                reportQty = subcontractRequirementInfo.getReportQty();
            }
        }
        List<String> subReportIds = Lists.newArrayList();
        if(StringUtils.equals(SubContractReportRequirement.CUSTOMER_REPORT_PDF.getCode(),reportRequirement)){
            subReportList.stream().forEach(subReport -> {
                Integer reportFileType = subReport.getReportFileType();
                if(Func.isNotEmpty(reportFileType) && reportFileType == ReportFileType.PDF.getCode()){
                    subReportIds.add(subReport.getID());
                }
            });
        }else {
            subReportList.stream().forEach(subReport -> {
                Integer reportFileType = subReport.getReportFileType();
                if(Func.isNotEmpty(reportFileType) && reportFileType == ReportFileType.Word.getCode()){
                    subReportIds.add(subReport.getID());
                }
            });
        }
        result.setSuccess(true);
        logger.info("分包单(SubContractNo_{}).判断是否满足Complete条件，统计到SubReport总数：{};ReportQty:{}",req.getSubcontractNo(),subReportIds.size(),reportQty);
        boolean b = subReportIds.size() >= reportQty;
        if(!b){
            logger.info("SubReport总数[{}]小于ReportQty[{}],不满足SubContract Completed条件",subReportIds.size(),reportQty);
            result.setMessage(String.format("SubReport总数[%s]小于ReportQty[{%s}],不满足SubContract Completed条件", subReportIds.size(), reportQty));
        }
        result.setData(b);
//        if (subReportIds.size() >= reportQty) {
//            result.setData(true);
//        } else {
//            result.setData(false);
//        }


        /*String orderNo = subContractInfo.getOrderNo();
        OrderIdReq orderIdReq = new OrderIdReq();
        orderIdReq.setOrderNo(orderNo);
        BaseResponse<TestRequestRsp> testRequestByOrderNo = orderFacade.getTestRequestByOrderNo(orderIdReq);
        if(Func.isEmpty(testRequestByOrderNo) || Func.isEmpty(testRequestByOrderNo.getData())){
            result.setSuccess(true);
            result.setMessage("Get ReportLanguage Fail");
            result.setData(false);
            return result;
        }

        TestRequestRsp testRequestRsp = testRequestByOrderNo.getData();
        Integer reportLanguage = testRequestRsp.getReportLanguage();
        if(ReportLanguage.checkLanguage(Func.toStr(reportLanguage),ReportLanguage.EnglishReportOnly)) {
            //英文报告的数量
            List<SubReportPO> enSubReportList = subReportPOList.stream().filter(i -> Func.equalsSafe(i.getLanguageId(), 1)).collect(Collectors.toList());
            if (Func.isNotEmpty(enSubReportList) && enSubReportList.size() >= reportQty) {
                result.setData(true);
            } else {
                result.setMessage("英文报告数量不满足");
                result.setData(false);
            }
        }else if(ReportLanguage.checkLanguage(Func.toStr(reportLanguage),ReportLanguage.ChineseReportOnly)) {
            List<SubReportPO> cnSubReportList = subReportPOList.stream().filter(i -> Func.equalsSafe(i.getLanguageId(), 2)).collect(Collectors.toList());
            if (Func.isNotEmpty(cnSubReportList) && cnSubReportList.size() >= reportQty) {
                result.setData(true);
            } else {
                result.setMessage("中文报告数量不满足");
                result.setData(false);
            }
        }else if(ReportLanguage.checkLanguage(Func.toStr(reportLanguage),ReportLanguage.EnglishAndChineseReport)) {
            List<SubReportPO> enSubReportList = subReportPOList.stream().filter(i -> Func.equalsSafe(i.getLanguageId(), 1)).collect(Collectors.toList());
            List<SubReportPO> cnSubReportList = subReportPOList.stream().filter(i -> Func.equalsSafe(i.getLanguageId(), 2)).collect(Collectors.toList());
            if (Func.isNotEmpty(enSubReportList) && Func.isNotEmpty(cnSubReportList) && enSubReportList.size() >= reportQty &&  cnSubReportList.size() >= reportQty) {
                result.setData(true);
            } else {
                result.setMessage("中/英文报告数量不满足");
                result.setData(false);
            }
        }else{
            result.setMessage("未知的reportLanguage");
            result.setData(false);
        }*/
//        result.setSuccess(true);
        return result;
    }

    private OrderAllDTO getOrderForPe(String orderNo) {
        OrderIdReq orderIdReq = new OrderIdReq();
        orderIdReq.setOrderNo(orderNo);
        BaseResponse<OrderAllDTO> baseResponse = gpoOrderFacade.getOrderForPe(orderIdReq);
        return baseResponse.getData();
    }

    @Override
    public BaseResponse queryOtsSubcontractListByCaseNo(String caseNo) {
        return otsSubContractWebservice.getSubcontractByCaseNo(caseNo);
    }

    @Override
    public BaseResponse getGPOSubContractInfo(String subContractNo, String labCode) {
        return gpoSubContractWebservice.getOtsSubContractInfo(subContractNo, labCode);
    }


    @Override
    public BaseResponse generateReportBySubContract(SubContractReq subContractReq) {
        //返回值定义
        BaseResponse baseResponse = new BaseResponse();
        baseResponse.setStatus(ResponseCode.SUCCESS.getCode());
        //参数校验
        if (Func.isEmpty(subContractReq) || Func.isEmpty(subContractReq.getSubContractNo())) {
            baseResponse.setStatus(ResponseCode.FAIL.getCode());
            baseResponse.setMessage("参数缺失");
        }

        UserInfo userInfo = SystemContextHolder.getUserInfoFillSystem();
        //调取生成report方法
        UserInfo finalUserInfo = userInfo;
        transactionTemplate.execute((trans)->{
            GenerateReportForSubcontractReq generateReportForSubcontractReq = new GenerateReportForSubcontractReq();
            generateReportForSubcontractReq.setSubContractNo(subContractReq.getSubContractNo());
            generateReportForSubcontractReq.setApproveStatus(ReportStatus.Approved.getCode());
            generateReportForSubcontractReq.setUserName(finalUserInfo.getRegionAccount());
            generateReportForSubcontractReq.setReportFlag(ReportFlagEnums.SUB_REPORT.getCode());
            generateReportForSubcontractReq.setReportFileType(ReportFileType.PDF.getCode());
            reportService.generateReportForSubContract(generateReportForSubcontractReq);
            return 1;
        });
        return baseResponse;
    }

    @Override
    public BaseResponse printSubContractForm(SubContractPrintReq req) {
        BaseResponse response = new BaseResponse();
        if(Func.isEmpty(req) || Func.isEmpty(req.getSubcontractNos())){
            return response;
        }
        PrintObjectFormReq printObjectFormReq = new PrintObjectFormReq();
        printObjectFormReq.setPrintType(PrintType.SUBCONTRACT_FORM.getType());
        printObjectFormReq.setProductLineCode(req.getProductLineCode());
        printObjectFormReq.setSubContractPrintReq(req);
        CustomResult<String>  printRes = digitalReportService.printFormRequest(printObjectFormReq);
        if(Func.isEmpty(printRes)||Func.isEmpty(printRes.getData())){
            response.setStatus(500);
            response.setMessage(printRes.getMsg());
            return response;
        }
        response.setData(printRes.getData());
        response.setMessage(printRes.getMsg());
        return response;
    }

    @Override
    public BaseResponse<SubContractReportInfo> getReportReceiver(SubContractReq req) {
        if(Func.isEmpty(req) || Func.isEmpty(req.getSubContractNo())){
            return BaseResponse.newFailInstance("subcontractNo is required");
        }
        SubContractInfo subContractInfo = subContractExtMapper.getSubContractInfo(req.getSubContractNo());
        if(Func.isEmpty(subContractInfo)){
            return BaseResponse.newFailInstance("not find Subcontract by "+req.getSubContractNo());
        }
        SubcontractRequirementInfo subcontractRequirement = subcontractRequirementMapper.getSubcontractRequirementBySubcontractNo(req.getSubContractNo());
        if(Func.isEmpty(subcontractRequirement)){
            if(Func.equalsSafe(ProductLineContextHolder.getProductLineCode(),ProductLineType.SL.getProductLineAbbr())){
                SubcontractRequirementInfo requirementInfo = new SubcontractRequirementInfo();
                requirementInfo.setReportRequirement(ReportRequirementEnum.Sub_Report_Word.getCode());
                subcontractRequirement = requirementInfo;
            } else{
                return BaseResponse.newFailInstance("not find Subcontract Requirement by "+req.getSubContractNo());
            }
        }
        String subcontractReportRequirement = subcontractRequirement.getReportRequirement();
        String orderNo = subContractInfo.getOrderNo();
        SubContractReportInfo subContractReportInfo = new SubContractReportInfo();
        if(Func.equalsSafe(subcontractReportRequirement, ReportRequirementEnum.Customer_Report_PDF.getCode()) ||Func.equalsSafe(subcontractReportRequirement, ReportRequirementEnum.Customer_Report_Word.getCode())){
            ReportReceiverReq receiver = new ReportReceiverReq();
            receiver.setOrderNo(orderNo);
            receiver.setProductLineCode(ProductLineContextHolder.getProductLineCode());
            BaseResponse<ReportReceiverRsp> reportReceiverRes = orderFacade.getReportReceiverInfo(receiver);
            if(Func.isNotEmpty(reportReceiverRes)) {
                ReportReceiverRsp reportReceiverRsp = reportReceiverRes.getData();
                subContractReportInfo = new SubContractReportInfo();
                subContractReportInfo.setReportAddressCN(reportReceiverRsp.getReportAddressCN());
                subContractReportInfo.setReportAddressEN(reportReceiverRsp.getReportAddressEN());
                subContractReportInfo.setReportHeaderCN(reportReceiverRsp.getReportHeaderCN());
                subContractReportInfo.setReportHeaderEN(reportReceiverRsp.getReportHeaderEN());

                if(Func.isNotEmpty(reportReceiverRsp.getReportLanguage())){
                    subContractReportInfo.setLanguageId(Integer.valueOf(reportReceiverRsp.getReportLanguage()));
                }
            }
        }else if(Func.equalsSafe(subcontractReportRequirement, ReportRequirementEnum.Sub_Report_Word.getCode())){
            //
            OrderIdReq orderIdReq = new OrderIdReq();
            orderIdReq.setOrderNo(orderNo);
            BaseResponse<OrderAllDTO> orderForPe = orderFacade.getOrderForPe(orderIdReq);
            if(Func.isNotEmpty(orderForPe) && Func.isNotEmpty(orderForPe.getData())){
                OrderAllDTO order = orderForPe.getData();
                String labCode = order.getLabDTO().getLabCode();
                LabDTO labDTO = frameWorkClient.queryLabByLabCode(labCode);
                if(Func.isNotEmpty(labDTO)){
                    subContractReportInfo = new SubContractReportInfo();
                    subContractReportInfo.setReportAddressCN(labDTO.getLaboratoryAddressCn());
                    subContractReportInfo.setReportAddressEN(labDTO.getLaboratoryAddress());
                    subContractReportInfo.setReportHeaderCN(labDTO.getLaboratoryNameCn());
                    subContractReportInfo.setReportHeaderEN(labDTO.getLaboratoryName());
                }
            }

        }
        // TODO-Trevor.Yuan 2022/9/11


        String fullReportHeader = "";
        String fullReportAddress = "";

        OrderIdReq orderIdReq = new OrderIdReq();
        orderIdReq.setOrderNo(orderNo);
        orderIdReq.setProductLineCode(req.getProductLineCode());
        BaseResponse<TestRequestRsp> testRequestRsp= orderFacade.getTestRequestByOrderNo(orderIdReq);
        if(Func.isNotEmpty(testRequestRsp) && Func.isNotEmpty(testRequestRsp)){
            Integer reportLanguage = testRequestRsp.getData().getReportLanguage();
            if(ReportLanguage.EnglishReportOnly.getCode().equalsIgnoreCase(Func.toStr(reportLanguage))) {
                fullReportHeader = subContractReportInfo.getReportHeaderEN();
                fullReportAddress = subContractReportInfo.getReportAddressEN();
            }else if(ReportLanguage.ChineseReportOnly.getCode().equalsIgnoreCase(Func.toStr(reportLanguage))) {
                fullReportHeader = subContractReportInfo.getReportHeaderCN();
                fullReportAddress = subContractReportInfo.getReportAddressCN();
            }else if(ReportLanguage.EnglishAndChineseReport.getCode().equalsIgnoreCase(Func.toStr(reportLanguage))) {
                if(Func.isNotEmpty(subContractReportInfo.getReportHeaderCN()) && Func.isNotEmpty(subContractReportInfo.getReportHeaderEN())){
                    fullReportHeader = subContractReportInfo.getReportHeaderCN() + "|||" + subContractReportInfo.getReportHeaderEN();
                }else if(Func.isNotEmpty(subContractReportInfo.getReportHeaderCN())){
                    fullReportHeader = subContractReportInfo.getReportHeaderCN();
                }else if(Func.isNotEmpty(subContractReportInfo.getReportHeaderEN())){
                    fullReportHeader =  subContractReportInfo.getReportHeaderEN();
                }
                if(Func.isNotEmpty(subContractReportInfo.getReportAddressCN()) && Func.isNotEmpty(subContractReportInfo.getReportAddressEN())){
                    fullReportAddress = subContractReportInfo.getReportAddressCN() + "|||" + subContractReportInfo.getReportAddressEN();
                }else if(Func.isNotEmpty(subContractReportInfo.getReportAddressCN())){
                    fullReportAddress = subContractReportInfo.getReportAddressCN();
                }else if(Func.isNotEmpty(subContractReportInfo.getReportAddressEN())){
                    fullReportAddress =  subContractReportInfo.getReportAddressEN();
                }
            }
        }

        subContractReportInfo.setFullReportHeader(Func.toStr(fullReportHeader));
        subContractReportInfo.setFullReportAddress(Func.toStr(fullReportAddress));
        return BaseResponse.newSuccessInstance(subContractReportInfo);
    }
    /**
     * 0.tre_sub 表保存父单的分包单和子单orderNo对应关系
     * 1.找到有效report 的 1501 报告
     * 2.直接将cloudid 给 tb_sub_report ，insert操作
     * 3.子单对应分包单的complete date 更新为当前时间 ，触发状态的修改
     * 4.分包单对应的所有TL 状态更新成complete
     * @param subContractReq
     * @return
     */
    @Override
    public CustomResult updateSubStatusForInternalSubFromPreorder(GPOSubContractReq subContractReq) {
        CustomResult customResult = new CustomResult(false);
        String orderNo = subContractReq.getOrderNo();
        String reportId = subContractReq.getReportId();
        String externalReportNo = subContractReq.getExternalReportNo();
        Integer reportVersion = subContractReq.getReportVersion();
        String userName = subContractReq.getUserName();
        String subContractNo = subContractReq.getSubContractNo();
        // 指定report数量
        Integer reportQty = subContractReq.getReportQty();
        Integer reportFileType = subContractReq.getReportFileType();
//        String reportNo = subContractReq.getReportNo();
        logger.info("preorder端order：{}状态为complete，触发notes这边进行内部分包/newSubContract的后续处理",orderNo);
        ProductLineContextHolder.setProductLineCode(subContractReq.getSourceProductLineCode(), subContractReq.getProductLineCode());

        Boolean isSourceGPO = false;
        Boolean isTargetGPO = false;
        // 判断是否是GPO
        if (!StringUtil.judgeProductLineCode(subContractReq.getSourceProductLineCode(), com.sgs.framework.model.enums.ProductLineType.SL)) {
            isSourceGPO = true;
        }

        if (!StringUtil.judgeProductLineCode(subContractReq.getProductLineCode(), com.sgs.framework.model.enums.ProductLineType.SL)) {
            isTargetGPO = true;
        }
        // todo 跨 BU 单独处理
        CustomResult<SubContractSyncDTO> res = this.buildSubContractData(orderNo,  subContractReq, reportQty, reportVersion, isSourceGPO, isTargetGPO);
        if (!res.isSuccess() || res.getData() == null) {
            customResult.setMsg(res.getMsg());
            return customResult;
        }
        logger.info("回传报告准备完成:{}", subContractNo);

        SubContractSyncDTO subContractSyncDTO = res.getData();
        //开始进行数据更新
        logger.info("根据orderNo:{} 处理内部分包数据完成，准备执行更新、保存",orderNo);

        CustomResult rspResult = new CustomResult(false);
        SysCopyInfo reqObject = new SysCopyInfo();
        reqObject.setSubContractNo(subContractSyncDTO.getSubcontractNo());
        reqObject.setOrderNo(orderNo);
        reqObject.setReportNo(subContractSyncDTO.getReportNo());

        // 设置回调
        CustomResult finalRspResult = rspResult;
        Boolean finalIsTargetGPO = isTargetGPO;
        Boolean finalIsSourceGPO = isSourceGPO;
        List<String> subReportIdList = new ArrayList<>();
        reqObject.setTask(()->{
            //1.更新subcontract 为complete
            if (!finalIsTargetGPO || (finalIsTargetGPO && !finalIsSourceGPO)) {
                logger.info("回传报告准备完成:{}, 处理tb_sub_contract：{}", subContractNo, JSON.toJSONString(subContractSyncDTO.getUpdateSubContractPO()));
                // GPO 自行处理
                subContractMapper.updateByPrimaryKeySelective(subContractSyncDTO.getUpdateSubContractPO());
            }

            //2.更新subreport 放入cloudid
            if (CollectionUtils.isNotEmpty(subContractSyncDTO.getSubReportPOListNew())) {
                List<String> newSubReportIdList = subContractSyncDTO.getSubReportPOListNew().stream().map(SubReportPO::getID).collect(Collectors.toList());
                subReportIdList.addAll(newSubReportIdList);
                List<SubReportPO> subReportPOListNew = subContractSyncDTO.getSubReportPOListNew();
                logger.info("回传报告准备完成:{}, 处理tb_sub_report：{}", subContractNo, JSON.toJSONString(subReportPOListNew));
                subReportExtMapper.saveBatchSubReportInfo(subContractSyncDTO.getSubReportPOListNew());
                //Word文件需要建立关系
                if (finalIsTargetGPO && SubContractReportRequirement.check(subContractSyncDTO.getReportRequirement(), SubContractReportRequirement.CUSTOMER_REPORT_WORD)) {
                    logger.info("回传报告准备完成:{}, 处理 insertSubReportRel:{}", subContractNo, reportId);
                    this.insertSubReportRel(subContractNo, reportId, subReportPOListNew, subContractSyncDTO.getBindOrLightSubContractId());
                }
            }
            if (CollectionUtils.isNotEmpty(subContractSyncDTO.getSubReportPOListUpdate())) {
                List<String> updateSubReportIdList = subContractSyncDTO.getSubReportPOListUpdate().stream().map(SubReportPO::getID).collect(Collectors.toList());
                subReportIdList.addAll(updateSubReportIdList);
                logger.info("回传报告准备完成:{}, 处理 update tb_sub_report：{}", subContractNo, JSON.toJSONString(subContractSyncDTO.getSubReportPOListUpdate()));
//                subReportExtMapper.saveBatchSubReportInfo(subContractSyncDTO.getSubReportPOListUpdate());
                subReportExtMapper.updateBatchSubReportInfo(subContractSyncDTO.getSubReportPOListUpdate());
            }
            if (subContractSyncDTO.getConditionSync() && subContractSyncDTO.getReportQtyIsFull()) {
                logger.info("回传报告准备完成:{}, 处理 update testLine状态：{}", subContractNo, JSON.toJSONString(subContractSyncDTO.getUpdateTestLineList()));
                //3.更新对应tl 状态为complete
                for (TestLineInstancePO testLineInstancePO : subContractSyncDTO.getUpdateTestLineList()) {
                    testLineStatusService.updateTestLineStatus(TestLineModuleType.SyncSubContract, testLineInstancePO);
                }
            }
            if(Func.isNotEmpty(subContractSyncDTO.getOldOrderInfoPre())){
                TestLineStatusUpdateReq req  = new TestLineStatusUpdateReq();
                req.setOrderNo(subContractSyncDTO.getOldOrderInfoPre().getOrderNo());
                testLineStatusServiceNew.onChange(req);
            }


            GeneralOrderInstanceInfoPO orderInfo = subContractSyncDTO.getOrderInfo();
            //4.更新Order与Report Status
            if(StringUtils.isNotBlank(orderInfo.getID()) && subContractSyncDTO.getReportQtyIsFull()){
                logger.info("回传报告准备完成:{}, 更新Order与Report Status", subContractNo);
                if(orderMapper.updateOrderStatus(orderInfo) < 0){
                    return finalRspResult;
                }
            }
            finalRspResult.setSuccess(true);
            return finalRspResult;
        });
        CustomResult rspRes = orderSyncService.syncConditionInfo(reqObject);

        //  GPO 修改订单状态
        if (isSourceGPO && subContractSyncDTO.getReportQtyIsFull() && subContractSyncDTO.getStatusReq() != null && StringUtils.isNotBlank(subContractSyncDTO.getStatusReq().getObjectNo())) {
            logger.info("回传报告完成:{}, 需要修改发包方订单状态", subContractNo, subContractSyncDTO.getStatusReq().getObjectNo());
            OrderNoReq orderNoReq = new OrderNoReq();
            orderNoReq.setUserName(userName);
            orderNoReq.setOrderNo(orderNo);
            orderNoReq.setProductLineCode(ProductLineContextHolder.getTargetProductLineCode());
            statusClient.orderReporting(orderNoReq);
        }

        if (isSourceGPO || isTargetGPO) {
            //  调用后续逻辑
            logger.info("回传报告准备完成:{}, 调用GPO接口继续后续逻辑", subContractNo);
            ReturnSubContractReportReq returnSubContractReportReq = new ReturnSubContractReportReq();
            returnSubContractReportReq.setReportId(reportId);
            returnSubContractReportReq.setExternalReportNo(externalReportNo);
            returnSubContractReportReq.setUserName(userName);
            returnSubContractReportReq.setSubContractNo(subContractNo);
            returnSubContractReportReq.setSourceProductLineCode(ProductLineContextHolder.getTargetProductLineCode()); // 发包方
            returnSubContractReportReq.setProductLineCode(ProductLineContextHolder.getProductLineCode()); // 接包方
            returnSubContractReportReq.setReportFileType(reportFileType);
            returnSubContractReportReq.setSubReportIdList(subReportIdList);
            reportService.returnSubContractReport(returnSubContractReportReq);
        }
        if (!rspRes.isSuccess()) {
            rspResult.setMsg(rspRes.getMsg());
            return rspResult;
        }
        logger.info("回传报告准备完成:{}, END======", subContractNo);
        rspResult.setSuccess(true);
        return rspResult;
    }
    /**
     *
     * @param orderNo
     * @param subContractReq
     * @param isSourceGPO
     * @param isTargetGPO
     * @return
     */
    private CustomResult<SubContractSyncDTO> buildSubContractData(String orderNo, GPOSubContractReq subContractReq, Integer reportQty, Integer reportVersion, Boolean isSourceGPO, Boolean isTargetGPO) {
        CustomResult<SubContractSyncDTO> rspResult = new CustomResult(false);
        String reportId = subContractReq.getReportId();
        String subContractNo = subContractReq.getSubContractNo();
        String userName = subContractReq.getUserName();
        boolean repairDraftSubReport = subContractReq.isRepairDraftSubReport();
        UserInfo userInfoFillSystem = SystemContextHolder.getUserInfoFillSystem();
        String regionAccount = userInfoFillSystem.getRegionAccount();
        if(Func.isNotEmpty(userName)){
            regionAccount = userName;
        }
        OrderInfoDto orderInfoPre = orderClient.getOrderInfoByOrderNo(orderNo, ProductLineContextHolder.getProductLineCode());
        ReportInfoPO reportInfo = reportMapper.getReportInfoByReportId(reportId);
        if(reportInfo == null){
            logger.info("根据reportId:{}查询不到report",reportId);
            return rspResult.fail(String.format("根据reportId:{%s}查询不到report",reportId));
        }
        String reportNo = reportInfo.getReportNo();
        TestRequestInfo testRequestInfo = orderClient.queryRestRequestForPe(orderNo, ProductLineContextHolder.getProductLineCode());
        if(testRequestInfo == null){
            logger.info("根据orderNo:{}查询不到report",orderNo);
            return rspResult.fail(String.format("根据orderNo:{%s}查询不到report",orderNo));
        }
        Integer reportFileTypeTemp = ReportFileType.Word.getCode();

        if (!ObjectUtils.isEmpty(testRequestInfo) && !ObjectUtils.isEmpty(testRequestInfo.getReportRequirement())) {
            if (ReportRequirementEnum.check(testRequestInfo.getReportRequirement(), ReportRequirementEnum.Customer_Report_PDF)) {
                reportFileTypeTemp = ReportFileType.PDF.getCode();
            }
        }
        if(repairDraftSubReport){
            reportFileTypeTemp = ReportFileType.DraftPDF.getCode();

        }
        List<ReportFilePO> reportFiles = reportFileExtMapper.getReportFileByType(reportId, reportFileTypeTemp);
        if(CollectionUtils.isEmpty(reportFiles)){
            logger.info("根据orderNo:{} ,reportNo:{}查询不到reportFile信息", orderNo, reportNo);
            return rspResult.fail(String.format("根据orderNo:{%s} ,reportNo:{%s}查询不到reportFile信息",orderNo, reportNo));
        }
        logger.info("回传报告准备参数:{}==reportNo:{},reportFileTypeTemp:{}", subContractNo, reportNo, reportFileTypeTemp);

//        DatabaseContextHolder.setTargetDataSource();
        AtomicReference<String> oldGeneralOrderId = new AtomicReference<>(StringUtils.EMPTY);
        AtomicReference<OrderInfoDto> oldOrderInfoPre = new AtomicReference<>(new OrderInfoDto());
        AtomicReference<ValidateTLInfo> validateTLInfo = new AtomicReference<>(new ValidateTLInfo());
        AtomicReference<GeneralOrderInstanceInfoPO> oldOrderInfo = new AtomicReference<>(new GeneralOrderInstanceInfoPO());
        AtomicReference<ReportInfoPO> oldReportInfo = new AtomicReference<>(new ReportInfoPO());
        SubContractPO updateSubContractPO = new SubContractPO();
        AtomicReference<String> oldOrderNo = new AtomicReference<>(StringUtils.EMPTY);
        AtomicReference<SubContractPO> subContractPO = new AtomicReference<>(new SubContractPO());
        List<String> currentTLIdList = com.google.common.collect.Lists.newArrayList();
        List<TestLineInstancePO> updateTestLineList = com.google.common.collect.Lists.newArrayList();
        AtomicReference<SysStatusReq> statusReq = new AtomicReference<>(new SysStatusReq());
        AtomicReference<GeneralOrderInstanceInfoPO> orderInfo = new AtomicReference<>(new GeneralOrderInstanceInfoPO());
        AtomicReference<SubContractInfo> subContractInfo = new AtomicReference<>(new SubContractInfo());;
        String finalRegionAccount = regionAccount;
        DatabaseContextHolder.setTargetDataSource(()-> {
            //去找分包单
            SubContractExample subContractExample = new SubContractExample();
            subContractExample.createCriteria().andSubContractNoEqualTo(subContractNo);
            List<SubContractPO> subContractPOS = this.subContractMapper.selectByExample(subContractExample);
            if(CollectionUtils.isEmpty(subContractPOS)){
                logger.info("根据orderNo:{}查询到分包单号:{}，但是查询不到分包单数据",orderNo,subContractNo);
                return rspResult.fail(String.format("根据orderNo:{%s}查询到分包单号:{%s}，但是查询不到分包单数据",orderNo,subContractNo));
            }
            logger.info("回传报告准备参数:{}==查询分包信息：{}", subContractNo, JSON.toJSONString(subContractPOS));
            //设置completedate为当前时间，同时状态改为complete
            subContractPO.set(subContractPOS.get(0));
            //Cancel校验 和subContract分包单页面cancel 执行操作是一致的，如果分包单是cancel的，不能继续同步后续数据
            Integer status = subContractPO.get().getStatus();
            if(SubContractStatusEnum.Cancelled.getStatus()==status.intValue()){
                logger.info("根据orderNo:{}获取当前分包单:{}状态已经是cancel，不做后续处理",orderNo,subContractNo);
                rspResult.setSuccess(true);
                return rspResult;
            }

            subContractInfo.set(subContractExtMapper.getSubContractInfo(subContractNo));

            updateSubContractPO.setID(subContractPO.get().getID());
            updateSubContractPO.setCompleteDate(new Date());
            updateSubContractPO.setStatus(SubContractStatusEnum.Complete.getStatus());
            updateSubContractPO.setModifiedBy(finalRegionAccount);
            updateSubContractPO.setModifiedDate(new Date());

            //需要找到父单，用到generalId
            oldOrderNo.set(subContractPO.get().getOrderNo());
            oldOrderInfo.set(orderMapper.getOrderInfo(oldOrderNo.get()));
            oldReportInfo.set(reportMapper.getReportByOrderNo(oldOrderNo.get()));
            oldGeneralOrderId.set(oldOrderInfo.get().getID());
            oldOrderInfoPre.set(orderClient.getOrderInfoByOrderNo(oldOrderNo.get(), ProductLineContextHolder.getTargetProductLineCode()));


            logger.info("回传报告准备参数:{}==orderNo:{},更新当前分包单对应的所有tl，将其状态更新成complete", subContractNo, oldOrderInfo.get().getOrderNo());
            //最后，需要更新当前分包单对应的所有tl，将其状态更新成complete
            OrderSubContractReq orderSubContractReq = new OrderSubContractReq();
            orderSubContractReq.setSubContractNo(subContractNo);
            orderSubContractReq.setOrderNo(oldOrderInfo.get().getOrderNo());
            List<String> subContractTestLineList = subContractExtMapper.getSubContractTestLineList(orderSubContractReq);
            if(CollectionUtils.isEmpty(subContractTestLineList)){
                logger.info("根据orderNo:{}找到父单:{}后查询不到testline信息",orderNo, oldOrderInfo.get().getOrderNo());
                return rspResult.fail(String.format("根据orderNo:{%s}找到父单:{%s}后查询不到testline信息",orderNo, oldOrderInfo.get().getOrderNo()));
            }

            List<TestLineInstancePO> testLineInstancePOS = testLineMapper.getBaseTestLineByIds(subContractTestLineList);
            updateTestLineList.addAll(testLineInstancePOS.stream().map(po -> {
                TestLineInstancePO testLineInstancePO = new TestLineInstancePO();
                testLineInstancePO.setID(po.getID());
                testLineInstancePO.setTestLineStatus(Completed.getStatus());
                testLineInstancePO.setModifiedBy(finalRegionAccount);
                testLineInstancePO.setModifiedDate(new Date());
                return testLineInstancePO;
            }).collect(Collectors.toList()));

            //DIG-4507 All TL Complete后需要更改Order状态为Reporting
            currentTLIdList.addAll(updateTestLineList.stream().map(TestLineInstancePO::getID).distinct().collect(Collectors.toList()));

            ValidateTLInfo validateTLInfoRes  = testLineValidateService.checkAllTestLineStatusUpdateOrderStatus(oldGeneralOrderId.get(), currentTLIdList);
            if(validateTLInfoRes != null){
                validateTLInfo.set(validateTLInfoRes);
            }
            logger.info("回传报告准备参数:{}==orderNo:{},更新当前分包单对应的所有tl，将其状态更新成complete:{}", subContractNo, oldOrderInfo.get().getOrderNo(), JSON.toJSONString(validateTLInfo));
            return true;
        });
        statusReq.set(new SysStatusReq());
        orderInfo.set(new GeneralOrderInstanceInfoPO());
        // POSL-4062 原单（Complete、Close），无需更新状态为Reporting
        if (validateTLInfo != null && validateTLInfo.get() != null && validateTLInfo.get().getUpdateOrderStatus() != null && validateTLInfo.get().getUpdateOrderStatus()
                && !com.sgs.preorder.facade.model.enums.OrderStatus.checkStatus(validateTLInfo.get().getCurrentPreorderOrderStatus(), com.sgs.preorder.facade.model.enums.OrderStatus.Completed, com.sgs.preorder.facade.model.enums.OrderStatus.Closed)) {
            orderInfo.get().setID(oldGeneralOrderId.get());
            orderInfo.get().setOrderStatus(OrderStatus.Completed.getStatus());
            orderInfo.get().setModifiedBy(regionAccount);
            orderInfo.get().setModifiedDate(DateUtils.getNow());
            //status 准备数据
            statusReq.get().setOldStatus(validateTLInfo.get().getCurrentPreorderOrderStatus());
            statusReq.get().setNewStatus(9);
            statusReq.get().setObjectNo(oldOrderNo.get());
            statusReq.get().setUserName(regionAccount);
        }

        List<SubReportPO> subReportPOListNew = new ArrayList<>();
        List<SubReportPO> subReportPOListUpdate = new ArrayList<>();
        logger.info("回传报告准备参数:{}==orderNo:{},组装reportFiles：{}", subContractNo, oldOrderInfo.get().getOrderNo(), JSON.toJSONString(reportFiles));

        SubReportExample subReportExample = new SubReportExample();
        SubReportExample.Criteria c = subReportExample.createCriteria();
        c.andObjectTypeEqualTo(ObjectType.SubContract.getCode());
        c.andObjectNoEqualTo(subContractNo);
        List<SubReportPO> subReportPOS = subReportMapper.selectByExample(subReportExample);

        for (ReportFilePO reportFilePO : reportFiles) {
            FileInfo file = new FileInfo();

            file.setId(UUID.randomUUID().toString());
            file.setOrderId(oldGeneralOrderId.get());
            file.setObjectID(reportFilePO.getID());
            file.setCloudID(reportFilePO.getCloudID());
            file.setSuffixes(reportFilePO.getSuffixes());
            file.setFileName(reportFilePO.getFilename());
            if (!fileClient.saveTbFile(file)){
                return rspResult.fail(String.format("未找到该orderNo:{%s}分包单CloudID对应的FileId.",orderNo));
            }
            String fileId = file.getId();
            String filename = reportFilePO.getFilename();
            logger.info("回传报告准备参数:{}, filename处理前:{}, isSourceGPO:{}, isTargetGPO:{}", subContractNo, filename, isSourceGPO, isTargetGPO);
            logger.info("回传报告准备参数:{}, filename处理后:{}", subContractNo, filename);

            //需要更新或插入到tb_sub_report表中
            SubReportPO editSubReportPO = new SubReportPO();
            SubReportPO existSubReportPo = null;

            LanguageType languageType = LanguageType.findLanguageId(reportFilePO.getLanguageID());
            if (languageType == null && StringUtils.isNotBlank(testRequestInfo.getReportLanguage())) {
                // reportFile中没有语言的情况下 获取报告语言
                languageType = LanguageType.findLanguageId(NumberUtil.toInt(testRequestInfo.getReportLanguage()));
            }
            Integer languageId =  languageType == null ? LanguageType.English.getLanguageId() : languageType.getLanguageId();
//            String languageCode = LanguageType.check(reportFilePO.getLanguageID(), LanguageType.CHI) ? LanguageType.CHI.getCode() : LanguageType.EN.getCode();
            logger.info("回传报告准备参数:{}, subReport languageCode:{}", subContractNo, languageId);
            if(CollectionUtils.isNotEmpty(subReportPOS)) {
                existSubReportPo = subReportPOS.stream().filter(report ->
                                NumberUtil.equals(languageId, report.getLanguageId())
//                                && StringUtils.isNotBlank(report.getSubReportNo())
//                                && report.getSubReportNo().contains(reportFilePO.getReportNo().split("-")[0])
                                        && StringUtils.equalsIgnoreCase(report.getSubReportNo(), reportFilePO.getReportNo())
                                        && NumberUtil.equals(NumberUtil.toInt(report.getReportFileType()), reportFilePO.getReportFileType())
                ).findFirst().orElse(null);
            }

            editSubReportPO.setModifiedBy(finalRegionAccount);
            editSubReportPO.setModifiedDate(new Date());
            editSubReportPO.setReportVersion(reportVersion);
            editSubReportPO.setSubcontractId(subContractPO.get().getID());
            editSubReportPO.setTestMatrixMergeMode(SubReportTestMatrixMergeMode.Ignore.getCode());
            // 分包回传 设置SubReport 的 Merge
            if (com.sgs.framework.model.enums.OperationType.check(orderInfoPre.getOperationType(), com.sgs.framework.model.enums.OperationType.BindSubContract, com.sgs.framework.model.enums.OperationType.ExecSubContract, com.sgs.framework.model.enums.OperationType.LightSubContract)) {
                editSubReportPO.setTestMatrixMergeMode(SubReportTestMatrixMergeMode.Merge.getCode());
            }

            // 后期Code放弃，采用LanguageId
            editSubReportPO.setLanguageCode(LanguageType.findLanguageId(languageId).getCode());
            // GPO2-7302 报告回传 SubReport 添加reportFileType
            editSubReportPO.setReportFileType(reportFilePO.getReportFileType());
            editSubReportPO.setLanguageId(languageId);

            // GPO2-8083 GPO 要求 报告回传 Status = Complete
            editSubReportPO.setStatus(SubReportStatus.Completed.getCode());
            editSubReportPO.setReportNo(subContractReq.getExternalReportNo());

            if(existSubReportPo == null){
                editSubReportPO.setID(UUID.randomUUID().toString());
                editSubReportPO.setGeneralOrderInstanceID(oldGeneralOrderId.get());
                editSubReportPO.setObjectNo(subContractNo);
                editSubReportPO.setObjectType(ObjectType.SubContract.getCode());
                // TODO GPO 与SL 不一致
                editSubReportPO.setCloudID(isTargetGPO ? file.getCloudID() : fileId);
                editSubReportPO.setFilename(filename);
                editSubReportPO.setCreatedBy(finalRegionAccount);
                editSubReportPO.setCreatedDate(new Date());
                // 2021-05-20 add  纯分包业务的场景增加字段
                editSubReportPO.setSubReportNo(reportNo);
                subReportPOListNew.add(editSubReportPO);
            }else{
                String subReportId = existSubReportPo.getID();
                editSubReportPO.setID(subReportId);
                editSubReportPO.setCloudID(isTargetGPO ? file.getCloudID() : fileId);
                editSubReportPO.setFilename(filename);
                // 2021-05-20 add  纯分包业务的场景增加字段
                editSubReportPO.setSubReportNo(reportNo);
                subReportPOListUpdate.add(editSubReportPO);
            }
        }

        Boolean isConditionSync = true;
        if (com.sgs.framework.model.enums.OperationType.check(orderInfoPre.getOperationType(), com.sgs.framework.model.enums.OperationType.ExecSubContract)) {
            isConditionSync = false;
        }

        Boolean bindSubContract = com.sgs.framework.model.enums.OperationType.check(orderInfoPre.getOperationType(), com.sgs.framework.model.enums.OperationType.BindSubContract, OperationType.LightSubContract);
        String bindSubContractId = "";
        if(bindSubContract && subContractInfo != null && StringUtils.isNotBlank(subContractInfo.get().getId())){
            bindSubContractId = subContractInfo.get().getId();
        }

        SubContractSyncDTO subContractSyncDTO = new SubContractSyncDTO();
        subContractSyncDTO.setConditionSync(isConditionSync);
        subContractSyncDTO.setReportNo(reportNo);
        subContractSyncDTO.setUpdateSubContractPO(updateSubContractPO);
        subContractSyncDTO.setSubReportPOListNew(subReportPOListNew);
        subContractSyncDTO.setSubcontractNo(subContractNo);
        subContractSyncDTO.setSubContractId(subContractPO.get().getID());
        subContractSyncDTO.setSubReportPOListUpdate(subReportPOListUpdate);
        subContractSyncDTO.setUpdateTestLineList(updateTestLineList);
        subContractSyncDTO.setOrderInfo(orderInfo.get());
        subContractSyncDTO.setOldOrderInfoPre(oldOrderInfoPre.get());
        subContractSyncDTO.setStatusReq(statusReq.get());
        subContractSyncDTO.setBindOrLightSubContract(bindSubContract);
        subContractSyncDTO.setBindOrLightSubContractId(bindSubContractId);
        subContractSyncDTO.setReportRequirement(testRequestInfo.getReportRequirement());
        if (NumberUtil.toInt(reportQty) > 0) {
            // 获取所有SubReport 数量
            //排除Draft文件的数量
            long subReportPOSCount = subReportPOS.stream().filter(item -> !ReportFileType.check(Func.toInteger(item.getFileType()), ReportFileType.DraftPDF)).count();
            long subReportPOListNewCount = subReportPOListNew.stream().filter(item -> !ReportFileType.check(Func.toInteger(item.getFileType()), ReportFileType.DraftPDF)).count();
            Integer count = NumberUtil.toInt(subReportPOSCount) + NumberUtil.toInt(subReportPOListNewCount);
            logger.info("回传报告准备参数:{}, 获取所有SubReport:{}, reportQtyCount:{}", subContractNo, count, reportQty);
            if (count < NumberUtil.toInt(reportQty)) {
                subContractSyncDTO.setReportQtyIsFull(false);
            }
        }

        // POSL-5099 组装数据
        subContractSyncDTO.setSubReportDTO(this.buildSubReportDTO(oldOrderNo.get(), oldReportInfo.get().getReportNo(), subReportPOListNew, subReportPOListUpdate, orderNo, reportNo, subContractNo, oldOrderInfo.get().getLabCode()));

        rspResult.setData(subContractSyncDTO);
        rspResult.setSuccess(true);
        logger.info("回传报告准备参数:{}==orderNo:{},组装数据完成subContractSyncDTO：{}", subContractNo, oldOrderInfo.get().getOrderNo(), JSON.toJSONString(subContractSyncDTO));
        return rspResult;

    }
    /**
     * POSL-5099 定义需要组装的数据
     * @param oldOrderNo  发包方 订单号
     * @param oldReportNo 发包方 报告号
     * @param subReportPOListNew subReport 新增
     * @param subReportPOListUpdate subReport 修改
     * @param orderNo 接包方订单号
     * @param reportNo 接包方报告号
     * @param subContractNo 分包号subContractNo
     * @param labCode labCode
     * @return
     */
    private SubReportDTO buildSubReportDTO(String oldOrderNo, String oldReportNo,
                                           List<SubReportPO> subReportPOListNew, List<SubReportPO> subReportPOListUpdate,
                                           String orderNo, String reportNo, String subContractNo, String labCode) {

        SubReportDTO subReportDTO = new SubReportDTO();
        SubReportDTO.SubContractInfo subContract = new SubReportDTO.SubContractInfo();
        subContract.setOrderNo(oldOrderNo);
        subContract.setExternalNo(orderNo);
        subContract.setSubContractNo(subContractNo);
        subContract.setSubContractType(com.sgs.framework.model.enums.SubContractType.SubContract.getType());
        subReportDTO.setSubContract(subContract);

        SubReportPO subReportPO = new SubReportPO();
        if (CollectionUtils.isNotEmpty(subReportPOListNew)) {
            subReportPO = subReportPOListNew.get(0);
        } else if (CollectionUtils.isNotEmpty(subReportPOListUpdate)){
            subReportPO = subReportPOListUpdate.get(0);
        }
        SubReportDTO.ReportFileInfo reportFile = SubReportDTO.ReportFileInfo.fromArzure(subReportPO.getFilename(), subReportPO.getCloudID());
        subReportDTO.setReportFile(reportFile);

        GetReportTestDataDto req = new GetReportTestDataDto();
        req.setReportNo(reportNo);
        ReportTestDataInfo reportTestData = this.getReportTestDataInfo(req);
        reportTestData.setOrderNo(oldOrderNo);
        reportTestData.setReportNo(oldReportNo);
        reportTestData.setSubContractNo(subContractNo);
        reportTestData.setObjectNo(subContractNo);
        reportTestData.setOriginalReportNo(oldReportNo);
        reportTestData.setExternalNo(orderNo);
        reportTestData.setLabCode(labCode);
        subReportDTO.setReportTestData(reportTestData);
        subReportDTO.setProductLineCode(ProductLineContextHolder.getProductLineCode());

        return subReportDTO;
    }

    /**
     * POSL-5099
     * @return
     */
    private ReportTestDataInfo getReportTestDataInfo(GetReportTestDataDto getReportTestDataDto) {

        ReportTestDataInfo reportTestData = new ReportTestDataInfo();
        GetTestDataDTO getTestDataDTO = testDataMapper.queryTestDataMatrixInfoByReportNo(getReportTestDataDto.getReportNo());
        if (getTestDataDTO == null) {
            return reportTestData;
        }
        reportTestData.setLabCode(getTestDataDTO.getLabCode());
        reportTestData.setCompletedDate(DateUtils.getNow());
        reportTestData.setSourceType(SourceTypeEnum.SUBCONTRACT.getCode());
        reportTestData.setLanguageId(LanguageType.English.getLanguageId());
        reportTestData.setRegionAccount("System");
        reportTestData.setProductLineCode(ProductLineContextHolder.getProductLineCode());

        if (CollectionUtils.isEmpty(getTestDataDTO.getMatrixDTOList())) {
            return reportTestData;
        }
        List<TestDataTestMatrixDTO> matrixDTOList = getTestDataDTO.getMatrixDTOList();
        // 获取 pp数据
        Set<Long> ppBaseIds = matrixDTOList.stream().map(TestDataTestMatrixDTO::getPpBaseId).collect(Collectors.toSet());
        Map<Long, GetPpBaseInfoRsp> ppBaseIdMaps = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(ppBaseIds)) {
            List<GetPpBaseInfoRsp> ppBaseInfoList = ppClient.getPpBaseInfoList(ppBaseIds);
            if (CollectionUtils.isNotEmpty(ppBaseInfoList)) {
                ppBaseIdMaps = ppBaseInfoList.stream().collect(Collectors.toMap(GetPpBaseInfoRsp::getPpBaseId, k -> k, (k1, k2) -> k1));
            }
        }

        // 获取 Citation数据
        Set<Long> citationBaseIds = matrixDTOList.stream().map(TestDataTestMatrixDTO::getCitationBaseId).collect(Collectors.toSet());
        Map<Long, GetCitationBaseInfoRsp> citationBaseMaps = Maps.newHashMap();
        List<GetCitationBaseInfoRsp> citationBaseInfo = citationClient.getCitationBaseInfo(citationBaseIds, null, LanguageType.Chinese);
        if (CollectionUtil.isNotEmpty(citationBaseInfo)) {
            citationBaseInfo.forEach(ciBase -> {
                if (citationBaseMaps.containsKey(ciBase.getCitationBaseId())) {
                    return;
                }
                citationBaseMaps.put(ciBase.getCitationBaseId(), ciBase);
            });
        }

        // 获取 EvaluationAlias
        List<GetTestLineEvaluationAliasItemReq> ids = com.google.common.collect.Lists.newArrayList();
        matrixDTOList.forEach(item -> {
            GetTestLineEvaluationAliasItemReq req = new GetTestLineEvaluationAliasItemReq();
            req.setPpArtifactRelId(NumberUtil.toLong(item.getPpArtifactRelId()));
            req.setCitationBaseId(item.getCitationBaseId());
            ids.add(req);
        });
        List<TestLineNameInfo> testLineEvaluationAlias = testLineClient.getTestLineEvaluationAlias(ids).getData();
        Map<String, TestLineNameInfo> evaluationAliasMap = Maps.newHashMap();
        testLineEvaluationAlias.forEach(item -> {
            String key = String.format("%s_%s", NumberUtil.toInt(item.getPpArtifactRelId()) > 0 ? 0 : item.getCitationBaseId(), item.getPpArtifactRelId());
            if (evaluationAliasMap.containsKey(key)) {
                return;
            }
            evaluationAliasMap.put(key, item);
        });

        // 获取 methodDesc
        List<Long> testLineBaseIds = matrixDTOList.stream().map(TestDataTestMatrixDTO::getTestLineBaseId).collect(Collectors.toList());
        Map<Long, GetTestLineBaseInfoRsp> testLineBaseInfoMap = Maps.newHashMap();
        List<GetTestLineBaseInfoRsp> testLineBaseInfoRspList = testLineClient.getTestLineBaseInfo(testLineBaseIds, LanguageType.Chinese, null);
        if (CollectionUtil.isNotEmpty(testLineBaseInfoRspList)) {
            testLineBaseInfoMap = testLineBaseInfoRspList.stream().collect(Collectors.toMap(u -> u.getTestLineBaseId(), o -> o, (o1, o2) -> o1));
        }


        // 获取 TestCondition
        List<String> matrixIds = matrixDTOList.stream().map(TestDataTestMatrixDTO::getTestMatrixId).collect(Collectors.toList());
        List<TestConditionInfoPO> testConditionInfos = testConditionMapper.getTestConditionListByMatrixIds(matrixIds);
        Map<String, List<TestConditionInfoPO>> matrixConditionMap = Maps.newHashMap();
        Map<String, List<TestConditionInstanceMultipleLanguagePO>> testConditionLangMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(testConditionInfos)) {
            matrixConditionMap = testConditionInfos.stream().collect(Collectors.groupingBy(TestConditionInfoPO::getTestMatrixID));

            List<String> testConditionIds = testConditionInfos.stream().map(TestConditionInfoPO::getID).collect(Collectors.toList());
            List<TestConditionInstanceMultipleLanguagePO> languageDTOList = testConditionLanguageMapper.getTestConditionLanguageInfoList(testConditionIds);
            if (CollectionUtils.isNotEmpty(languageDTOList)) {
                testConditionLangMap = languageDTOList.stream().collect(Collectors.groupingBy(TestConditionInstanceMultipleLanguagePO::getTestConditionInstanceID));
            }

        }

        // 获取testdata
        List<TestDataResultDTO> testDataByMatrixIds = testDataMapper.getTestDataByMatrixIds(matrixIds);
        Map<String, List<TestDataResultDTO>> testDataMaps = Maps.newHashMap();
        if (CollectionUtil.isNotEmpty(testDataByMatrixIds)) {
            testDataMaps = testDataByMatrixIds.stream().collect(Collectors.groupingBy(TestDataResultDTO::getTestMatrixId));
        }


        Map<Long, GetPpBaseInfoRsp> finalPpBaseIdMaps = ppBaseIdMaps;
        Map<String, List<TestConditionInfoPO>> finalMatrixConditionMap = matrixConditionMap;
        Map<String, List<TestConditionInstanceMultipleLanguagePO>> finalMatrixConditionLangMap = testConditionLangMap;
        Map<String, List<TestDataResultDTO>> finalTestDataMaps = testDataMaps;
        Map<Long, GetTestLineBaseInfoRsp> finalTestLineBaseInfoMap = testLineBaseInfoMap;
        matrixDTOList.forEach(item -> {
            GetPpBaseInfoRsp getPpBaseInfoRsp = finalPpBaseIdMaps.get(item.getPpBaseId());
            if (getPpBaseInfoRsp != null) {
                item.setPpVersionId(getPpBaseInfoRsp.getPpVersionId());
                item.setPpNo(getPpBaseInfoRsp.getPpNo());
            }

            // 设置citation数据
            GetCitationBaseInfoRsp getCitationBaseInfoRsp = citationBaseMaps.get(item.getCitationBaseId());
            if (getCitationBaseInfoRsp != null) {
                item.setCitationName(getCitationBaseInfoRsp.getCitationName());
                item.setCitationType(getCitationBaseInfoRsp.getCitationType());
            }

            // 设置material数据
            String material = getMaterial(getTestDataDTO.getOrderNo(), item.getTestSampleId());
            item.setMaterialName(material);

            String key = String.format("%s_%s", NumberUtil.toLong(item.getPpArtifactRelId()) > 0 ? 0 : item.getCitationBaseId(), NumberUtil.toInt(item.getPpArtifactRelId()));
            TestLineNameInfo evaluationAlias = evaluationAliasMap.get(key);
            if (evaluationAlias != null) {
                item.setEvaluationAlias(evaluationAlias.getTestLineName());

                // 设置Languages
                List<TestLineNameLanguageInfo> langs = evaluationAlias.getLanguages();
                TestLineNameLanguageInfo lang = langs.stream().filter(u -> u.getLanguageId() == LanguageType.Chinese.getLanguageId()).findFirst().orElse(null);
                if (lang != null) {
                    List<TestDataTestMatrixLangInfo> langInfoList = com.google.common.collect.Lists.newArrayList();
                    TestDataTestMatrixLangInfo langInfo = new TestDataTestMatrixLangInfo();
                    langInfo.setLanguageId(lang.getLanguageId());
                    langInfo.setEvaluationAlias(lang.getTestLineName());
                    langInfo.setMaterialName(material);
                    langInfoList.add(langInfo);
                    item.setLanguages(langInfoList);
                }
            }

            List<TestDataConditionInfo> conditions = com.google.common.collect.Lists.newArrayList();
            List<TestConditionInfoPO> testConditionInfoPOS = finalMatrixConditionMap.get(item.getTestMatrixId());
            List<TestDataResultDTO> testDataResultDTOS = finalTestDataMaps.get(item.getTestMatrixId());
            // condition 取block的condition
            if (CollectionUtils.isNotEmpty(testConditionInfoPOS) && CollectionUtils.isNotEmpty(testDataResultDTOS)) {
                Set<Integer> coonditionIds = Sets.newHashSet();
                // 获取matrix下的所有condition
                testDataResultDTOS.forEach(testdata -> {
                    if (NumberUtil.toInt(testdata.getTestConditionId()) > 0) {
                        coonditionIds.add(NumberUtil.toInt(testdata.getTestConditionId()));
                    }
                    if (NumberUtil.toInt(testdata.getConditionParentId()) > 0) {
                        coonditionIds.add(NumberUtil.toInt(testdata.getConditionParentId()));
                    }
                    if (NumberUtil.toInt(testdata.getProcedureTestConditionId()) > 0) {
                        coonditionIds.add(NumberUtil.toInt(testdata.getProcedureTestConditionId()));
                    }
                });

                for (TestConditionInfoPO conditionItem : testConditionInfoPOS){
                    if (!coonditionIds.contains(conditionItem.getTestConditionID())
                            || NumberUtil.equals(item.getTestConditionId(), conditionItem.getTestConditionID())) {
                        continue;
                    }
                    TestDataConditionInfo conditionDTO = new TestDataConditionInfo();
                    conditionDTO.setTestConditionId(conditionItem.getTestConditionID());
                    conditionDTO.setTestConditionName(conditionItem.getTestConditionDesc());
                    conditionDTO.setTestConditionSeq(conditionItem.getTestConditionSeq());

                    List<TestConditionInstanceMultipleLanguagePO> langs = finalMatrixConditionLangMap.get(conditionItem.getID());
                    List<TestDataConditionLangInfo> languages = com.google.common.collect.Lists.newArrayList();
                    if (langs != null) {
                        for (TestConditionInstanceMultipleLanguagePO lang : langs) {
                            TestDataConditionLangInfo langInfo = new TestDataConditionLangInfo();
                            langInfo.setLanguageId(lang.getLanguageId());
                            langInfo.setTestConditionName(lang.getTestConditionDesc());
                            languages.add(langInfo);
                        }
                    }
                    conditionDTO.setLanguages(languages);
                    conditions.add(conditionDTO);
                }
                // TODO 临时处理  补充样式7 的 condition 待DIG-8613完成后去掉
                if (coonditionIds.contains(2) && NumberUtil.equals(item.getTestConditionId(), 2)) {
                    TestDataConditionInfo conditionDTO = new TestDataConditionInfo();
                    conditionDTO.setTestConditionId(11121);
                    conditionDTO.setTestConditionName("Colorant Extraction");
                    conditionDTO.setTestConditionSeq(2);
                    TestDataConditionLangInfo langInfo = new TestDataConditionLangInfo();
                    langInfo.setLanguageId(LanguageType.Chinese.getLanguageId());
                    langInfo.setTestConditionName("染料提取");
                    conditionDTO.setLanguages(com.google.common.collect.Lists.newArrayList(langInfo));
                    conditions.add(conditionDTO);
                }
                if (coonditionIds.contains(1) && NumberUtil.equals(item.getTestConditionId(), 1)) {
                    TestDataConditionInfo conditionDTO = new TestDataConditionInfo();
                    conditionDTO.setTestConditionId(11122);
                    conditionDTO.setTestConditionName("Direct Reduction");
                    conditionDTO.setTestConditionSeq(1);
                    TestDataConditionLangInfo langInfo = new TestDataConditionLangInfo();
                    langInfo.setLanguageId(LanguageType.Chinese.getLanguageId());
                    langInfo.setTestConditionName("直接还原");
                    conditionDTO.setLanguages(com.google.common.collect.Lists.newArrayList(langInfo));
                    conditions.add(conditionDTO);
                }
                item.setTestConditions(conditions);
            }

            if (testDataResultDTOS != null) {
                List<TestDataResultInfo> resultDTOS = com.google.common.collect.Lists.newArrayList();
                for (TestDataResultDTO resultItem : testDataResultDTOS){
                    if (NumberUtil.toInt(item.getTestConditionId()) > 0 && !NumberUtil.equals(resultItem.getTestConditionId(), item.getTestConditionId())) {
                        continue;
                    }
                    TestDataResultInfo resultDTO = new TestDataResultInfo();
                    resultDTO.setTestAnalyteId(Integer.valueOf(resultItem.getAnalyteId()));
                    resultDTO.setAnalyteCode(resultItem.getAnalyteCode());
                    resultDTO.setTestAnalyteName(resultItem.getAnalyteName());
                    resultDTO.setAnalyteType(resultItem.getAnalyteType());
                    resultDTO.setReportUnit(resultItem.getReportUnit());
                    resultDTO.setTestValue(resultItem.getTestValue());
                    resultDTO.setCasNo(resultItem.getCasNo());
                    resultDTO.setLimitUnit(resultItem.getReportUnit());
                    resultDTO.setReportLimit(resultItem.getReportLimit());
                    resultDTO.setConclusionId(resultItem.getConclusionId());
                    resultDTO.setAnalyteSeq(resultItem.getAnalyteSeq());

                    List<AnalyteLangDTO> langs = resultItem.getLanguages();
                    List<TestDataResultLangInfo> languages = com.google.common.collect.Lists.newArrayList();
                    for (AnalyteLangDTO lang : langs) {
                        TestDataResultLangInfo langInfo = new TestDataResultLangInfo();
                        langInfo.setLanguageId(lang.getLanguageId());
                        langInfo.setTestAnalyteName(lang.getAnalyteName());
                        langInfo.setReportUnit(lang.getReportUnit());
                        langInfo.setLimitUnit(lang.getLimitUnit());
                        languages.add(langInfo);
                    }
                    resultDTO.setLanguages(languages);
                    resultDTOS.add(resultDTO);
                }
                item.setTestResults(resultDTOS);
            }

            // 设置MethodDesc
            GetTestLineBaseInfoRsp testLineBaseInfo = finalTestLineBaseInfoMap.get(item.getTestLineBaseId());
            if (testLineBaseInfo != null) {
                item.setMethodDesc(testLineBaseInfo.getMethodDesc());
            }

        });

        SubcontractRelReq reqRel = new SubcontractRelReq();
        reqRel.setOrderId(getTestDataDTO.getOrderId());
        reqRel.setRelTypes(com.google.common.collect.Lists.newArrayList(
                Matrix.getTableId(),
                TableType.Sample.getTableId()
        ));
        List<SubcontractRelInfoPO> subcontractRels = subcontractRelMapper.getSubcontractRelList(reqRel);
//        OrderSubcontractRelInfoExample orderSubcontractRelInfoExample = new OrderSubcontractRelInfoExample();
//        orderSubcontractRelInfoExample.createCriteria().andSubRelIdIn(matrixIds)
//                .andRelTypeEqualTo(TableType.Matrix.getTableId());
//        List<OrderSubcontractRelInfoPO> subcontractRelInfoPOS = orderSubcontractRelInfoMapper.selectByExample(orderSubcontractRelInfoExample);
//        Map<String, OrderSubcontractRelInfoPO> orderSubcontractRelInfoPOMap = subcontractRelInfoPOS.stream().collect(Collectors.toMap(u -> u.getSubRelId(), o -> o, (o1, o2) -> o1));
        Map<String, SubcontractRelInfoPO> orderSubcontractRelInfoPOMap = subcontractRels.stream().collect(Collectors.toMap(u -> u.getSubRelId(), o -> o, (o1, o2) -> o1));
        // 替换id
        matrixDTOList.forEach(item -> {
            SubcontractRelInfoPO mmatrixRel = orderSubcontractRelInfoPOMap.get(item.getTestMatrixId());
            if (mmatrixRel != null) {
                item.setTestMatrixId(mmatrixRel.getOriginalRelId());
            }
            SubcontractRelInfoPO sampleRel = orderSubcontractRelInfoPOMap.get(item.getTestSampleId());
            if (sampleRel != null) {
                item.setTestSampleId(sampleRel.getOriginalRelId());
            }
        });

        String testMatrixsJson = JSONObject.toJSONString(getTestDataDTO.getMatrixDTOList());
        reportTestData.setTestMatrixs(JSONArray.parseArray(testMatrixsJson, TestDataTestMatrixInfo.class));


        return reportTestData;
    }


    private String getMaterial(String orderNo, String testSampleId) {

        Map<String, TestSampleSimplifyInfo> sourceSampleMaps = Maps.newHashMap();
        List<TestSampleSimplifyInfo> testSamples = sampleExtMapper.getSampleInfoListByOrderNo(orderNo);
        testSamples.sort(new TestSampleSimplifyComparator(true));
        testSamples.forEach(testSample->{
            sourceSampleMaps.put(testSample.getSampleId(), testSample);
        });

        List<TestSampleLangInfoPO> sampleLangInfos = testSampleLangMapper.queryByOrderNo(orderNo);
        Map<String, List<TestSampleLangInfoPO>> sampleLangMap = sampleLangInfos.stream().collect(Collectors.groupingBy(TestSampleLangInfoPO::getSampleId));

        TestSampleSimplifyInfo sourceSample = sourceSampleMaps.get(testSampleId);
        if (sourceSample == null){
            return null;
        }
        SampleType sampleType = SampleType.findType(sourceSample.getSampleType());
        if (sampleType == null){
            return null;
        }
        LinkedList<List<TestSampleLangInfoPO>> langs = com.google.common.collect.Lists.newLinkedList();

        String material = StringUtils.EMPTY;
        if(SampleType.check(sampleType, MixSample)){
            List<TestSampleSimplifyInfo> groupSamples = com.google.common.collect.Lists.newArrayList();
            sourceSample.getSampleGroupIds().forEach(x->{
                groupSamples.add(sourceSampleMaps.get(x));
            });
            groupSamples.sort(new TestSampleSimplifyMixComparator(true));
            groupSamples.forEach(x->{
                if (!sampleLangMap.containsKey(x.getSampleId())) {
                    return;
                }
                langs.add(sampleLangMap.get(x.getSampleId()));
            });
            material = testSampleLangService.getMixMaterialText(langs,"/", com.google.common.collect.Lists.newArrayList(LanguageType.Chinese.getLanguageId()),"/");
        }else {
            List<TestSampleLangInfoPO> sampleLangs = sampleLangMap.get(testSampleId);
            if (sampleLangs != null){
                langs.add(sampleLangs);
            }
            if(CollectionUtils.isNotEmpty(sampleLangs)){
                material = testSampleLangService.getMaterialText(sampleLangs,"/", com.google.common.collect.Lists.newArrayList(LanguageType.English.getLanguageId()),"/");
            }
        }

        return material;
    }
}