package com.sgs.otsnotes.domain.service.gpn.report.impl;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.beust.jcommander.internal.Lists;
import com.sgs.core.domain.UserInfo;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.core.context.SystemContextHolder;
import com.sgs.framework.core.exception.Assert;
import com.sgs.framework.facade.domain.rsp.BuParamValueRsp;
import com.sgs.framework.log.model.SystemLog;
import com.sgs.framework.model.enums.LanguageType;
import com.sgs.framework.model.enums.ReportLanguage;
import com.sgs.framework.model.enums.SgsSystem;
import com.sgs.framework.tool.utils.CollectionUtil;
import com.sgs.framework.tool.utils.Func;
import com.sgs.framework.tool.utils.ProductLineContextHolder;
import com.sgs.framework.tool.utils.StringPool;
import com.sgs.grus.bizlog.common.BizLog;
import com.sgs.grus.bizlog.common.BizLogHelper;
import com.sgs.otsnotes.core.config.GPOConfig;
import com.sgs.otsnotes.core.config.InterfaceConfig;
import com.sgs.otsnotes.core.constants.BizLogConstant;
import com.sgs.otsnotes.core.constants.Constants;
import com.sgs.otsnotes.core.enums.ReportFlagEnums;
import com.sgs.otsnotes.core.thread.ThreadPoolContextTaskExecutor;
import com.sgs.otsnotes.core.util.DateUtils;
import com.sgs.otsnotes.dbstorages.mybatis.enums.CustomerUsage;
import com.sgs.otsnotes.dbstorages.mybatis.extmapper.ReportMapper;
import com.sgs.otsnotes.dbstorages.mybatis.extmapper.SubReportExtMapper;
import com.sgs.otsnotes.dbstorages.mybatis.mapper.*;
import com.sgs.otsnotes.dbstorages.mybatis.model.*;
import com.sgs.otsnotes.domain.service.gpn.report.IGpnBatchReportService;
import com.sgs.otsnotes.domain.service.gpn.report.IGpnReportService;
import com.sgs.otsnotes.domain.service.gpn.sendEmail.ISendEmailService;
import com.sgs.otsnotes.domain.service.gpn.subcontract.ISubContractExternalRelService;
import com.sgs.otsnotes.domain.service.gpn.tracking.ITrackingService;
import com.sgs.otsnotes.domain.service.reportFile.IReportFileService;
import com.sgs.otsnotes.facade.model.dto.*;
import com.sgs.otsnotes.facade.model.enums.*;
import com.sgs.otsnotes.facade.model.req.ReportForBatchGenerateReq;
import com.sgs.otsnotes.facade.model.req.ReportForGenerateRequest;
import com.sgs.otsnotes.facade.model.req.dataentry.GetCustomerConclusionReq;
import com.sgs.otsnotes.facade.model.req.report.*;
import com.sgs.otsnotes.facade.model.rsp.dataentry.GetCustomerConclusionRsp;
import com.sgs.otsnotes.facade.model.rsp.gpn.GpnGenerateReportRsp;
import com.sgs.otsnotes.integration.*;
import com.sgs.preorder.facade.DffFacade;
import com.sgs.preorder.facade.OrderFacade;
import com.sgs.preorder.facade.model.dto.customer.CustomerInstanceDTO;
import com.sgs.preorder.facade.model.dto.dff.TestrptCoverpageDffSimpleDTO;
import com.sgs.preorder.facade.model.dto.order.OrderAllDTO;
import com.sgs.preorder.facade.model.dto.order.OrderInfoDto;
import com.sgs.preorder.facade.model.enums.CaseType;
import com.sgs.preorder.facade.model.info.externalOrder.GpoExternalOrderInfo;
import com.sgs.preorder.facade.model.info.externalOrder.GpoExternalReportInfo;
import com.sgs.preorder.facade.model.req.OrderIdReq;
import com.sgs.preorder.facade.model.req.OrderNosReq;
import com.sgs.preorder.facade.model.req.UnpivotProductReq;
import com.sgs.preorder.facade.model.req.customer.CustomerDetailReq;
import com.sgs.preorder.facade.model.req.externalOrder.GpoExternalOrderNoReq;
import com.sgs.preorder.facade.model.rsp.ExternalReportNoRsp;
import com.sgs.preorder.facade.model.rsp.customer.CustomerExtNewListRsp;
import com.sgs.preorder.facade.model.rsp.order.OrderReferenceNoRsp;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.URL;
import java.net.URLConnection;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

/**
 * <AUTHOR> Huang
 * @date 2020/9/14
 * @email <EMAIL>
 */
@Service
@AllArgsConstructor
@Slf4j
public class GpnBatchReportServiceImpl implements IGpnBatchReportService {
    private ReportMapper reportMapper;
    private TokenClient tokenClient;
    private OrderFacade orderFacade;
    private ReportInfoMapper reportInfoMapper;
    private ConclusionInfoMapper conclusionInfoMapper;
    private ReportFileMapper reportFileMapper;
    private IGpnReportService reportService;
    private FrameWorkClient frameWorkClient;
    private SubReportExtMapper subReportExtMapper;
    private ReportTemplateInfoMapper reportTemplateInfoMapper;
    private GPOConfig gpoConfig;
    private OrderClient orderClient;
    private ITrackingService trackingService;
    private DffFacade dffFacade;
    private final PrelimResultInfoMapper prelimResultInfoMapper;
    private DataEntryClient dataEntryClient;
    private OrderFacade gpoOrderFacade;
    private CustomerClient customerClient;
    private InterfaceConfig interfaceConfig;
    private ISendEmailService sendEmailService;
    private ThreadPoolContextTaskExecutor taskExecutor;
    private IReportFileService reportFileService;
    private ISubContractExternalRelService subContractExternalRelService;

    @Override
    public int batchSaveReport(ReportBatchActionReq request) {
        List<String> reportNoList = Arrays.asList(request.getReportNoList().split(","));
        ReportInfoExample example = new ReportInfoExample();
        example.createCriteria().andReportNoIn(reportNoList);

        List<ReportInfoPO> labReportList = reportInfoMapper.selectByExample(example);
        labReportList.forEach(report ->{
            //update template
            ReportTemplateInfoExample reportTemplateInfoExample = new ReportTemplateInfoExample();
            reportTemplateInfoExample.createCriteria().andReportIdEqualTo(report.getID());
            List<ReportTemplateInfoPO> reportTemplateInfoPOList = reportTemplateInfoMapper.selectByExample(reportTemplateInfoExample);
            if(!ObjectUtils.isEmpty(request.getOtsTemplateCnId()))
            {
                saveReportTemplate(LanguageType.Chinese.getLanguageId(),report.getID(), request, tokenClient.getUser(),reportTemplateInfoPOList);
            }

            if(!ObjectUtils.isEmpty(request.getOtsTemplateEnId()))
            {
                saveReportTemplate(LanguageType.English.getLanguageId(),report.getID(), request, tokenClient.getUser(),reportTemplateInfoPOList);
            }
            if(StringUtils.isNotEmpty(request.getApprovedBy()))
            {
                SaveReportRequest objSaveReportRequest=new SaveReportRequest();
                objSaveReportRequest.setApprovedBy(request.getApprovedBy());
                String approverStr=reportService.initApproverFilePath(objSaveReportRequest,tokenClient.getUser(),report, SignTypeEnums.APPROVER.getCode());
                ReportInfoPO reportInfoPO=new ReportInfoPO();
                reportInfoPO.setID(report.getID());
                reportInfoPO.setApproverBy(request.getApprovedBy());
                reportInfoPO.setApprover(approverStr);
                reportInfoPO.setModifiedBy(tokenClient.getUser().getRegionAccount());
                reportInfoPO.setModifiedDate(DateUtils.getCurrentDate());
                reportInfoMapper.updateByPrimaryKeySelective(reportInfoPO);
            }


        });



        return 1;
    }



    private void saveReportTemplate(Integer languageId,String reportId,ReportBatchActionReq request, UserInfo userInfo,List<ReportTemplateInfoPO> reportTemplateInfoPOList) {

        ReportTemplateInfoPO reportTemplateInfoPO =   reportTemplateInfoPOList.stream().filter(template->languageId==template.getLanguageId())
                .findFirst().orElse(new ReportTemplateInfoPO());
        reportTemplateInfoPO.setReportId(reportId);
        if(LanguageType.English.getLanguageId() == languageId)
        {
            reportTemplateInfoPO.setTemplateSettingId(request.getOtsTemplateEnId());
        }else
        {
            reportTemplateInfoPO.setTemplateSettingId(request.getOtsTemplateCnId());
        }
        reportTemplateInfoPO.setLanguageId(languageId);
        reportTemplateInfoPO.setModifiedDate(new Date());
        reportTemplateInfoPO.setModifiedBy(userInfo.getRegionAccount());
        if(Func.isEmpty(reportTemplateInfoPO.getId()))
        {
            reportTemplateInfoPO.setId(Func.randomUUID());
            reportTemplateInfoPO.setActiveIndicator(true);
            reportTemplateInfoPO.setCreatedBy(userInfo.getRegionAccount());
            reportTemplateInfoPO.setCreatedDate(new Date());
            reportTemplateInfoMapper.insert(reportTemplateInfoPO);
        }else
        {
            reportTemplateInfoMapper.updateByPrimaryKeySelective(reportTemplateInfoPO);
        }
    }


    @BizLog(bizType = BizLogConstant.REPORT_OPERATION_HISTORY, operType = "Approve")
    @Override
    public BaseResponse batchApproveReport(ReportBatchActionReq request) {
        Date date = new Date();
        // TODO Auto-generated method stub
        BaseResponse response = new BaseResponse();
        List<String> reportNoList = Arrays.asList(request.getReportNoList().split(","));
        List<ReportInfoPO> orderLabReportList = getReportInfoPOSByOrderNos(reportNoList);

        orderLabReportList.forEach(report ->{
            //更新 report
            if(Func.isNotEmpty(request.getApprovedBy()))
            {
                report.setApproverBy(request.getApprovedBy());
            }
            if(Func.isNotEmpty(request.getConclusionSettingID()))
            {
                reportService.saveOrUpdateConclusion(report.getID(),report.getReportNo(),request.getConclusionId(),request.getConclusionSettingID());
            }
            report.setReportStatus(ReportStatus.Approved.getCode());
            report.setApproverDate(new Date());
            reportInfoMapper.updateByPrimaryKeySelective(report);
            //report对应订单信息
            OrderIdReq orderIdReq = new OrderIdReq();
            orderIdReq.setOrderNo(report.getOrderNo());
            BaseResponse<OrderAllDTO> orderRes = gpoOrderFacade.getOrderForPe(orderIdReq);

            Assert.notNull(orderRes, "查询订单信息失败" + report.getOrderNo());
            Assert.notNull(orderRes.getData(), "查询订单信息失败" + report.getOrderNo());

            OrderAllDTO orderAllDTO = orderRes.getData();
            //GPO2-14351 查询Buyer配置是否not need dss
            //查询Customer Buyer
            Boolean buyerNotNeedDssFlag = false;
            BaseResponse<List<CustomerInstanceDTO>> orderCustomerRes = gpoOrderFacade.queryCustomerForPe(orderIdReq);
            if(Func.isNotEmpty(orderCustomerRes) && Func.isNotEmpty(orderCustomerRes.getData())){
                List<CustomerInstanceDTO> customerList = orderCustomerRes.getData();
                CustomerInstanceDTO buyer = customerList.stream().filter(e ->
                        Func.isNotEmpty(e.getCustomerUsage()) && Func.equalsSafe(e.getCustomerUsage(), CustomerUsage.Buyer.getCode())).findAny().orElse(null);
                if(Func.isNotEmpty(buyer)){
                    //查询客户配置
                    CustomerDetailReq customerDetailReq = new CustomerDetailReq();
                    customerDetailReq.setCustomerId(buyer.getCustomerId());
                    customerDetailReq.setBuCode(orderAllDTO.getBUCode());
                    customerDetailReq.setLocationCode(orderAllDTO.getLocationCode());
                    CustomerExtNewListRsp customerExt = customerClient.getCustomerExtInfoByBu(customerDetailReq);
                    if(Func.isNotEmpty(customerExt) && Func.isNotEmpty(customerExt.getNotNeedDSS())){
                        if(customerExt.getNotNeedDSS().equals(1)){
                            buyerNotNeedDssFlag = true;
                        }
                    }
                }
            }
            Boolean notNeedFlag = buyerNotNeedDssFlag;
            //获取gpo order中的sealCode
            //线程异步处理
//            new Thread(new Runnable() {
//                @Override
//                public void run() {
            GenerateDssPdfReq generateDssPdfReq = new GenerateDssPdfReq();
            generateDssPdfReq.setReportId(report.getID());
            generateDssPdfReq.setOrderId(report.getOrderNo());
            generateDssPdfReq.setBuCode(report.getOrderNo());
            generateDssPdfReq.setIsNeedDraft(false);
            generateDssPdfReq.setNotNeedDSS(notNeedFlag);
            reportService.approveReportToDss(generateDssPdfReq);
//                }
//            }).start();


            //Track更新
            //orderService.pushTrack(report.getOrderId(), 9,date);
//            kafkaService.sendTracking(KafkaActionType.approve,report.getOrderNo(),tokenClient.getToken(),tokenClient.getUser().getRegionAccount(),null);
            trackingService.sendApprove(report.getOrderNo(),tokenClient.getToken(),tokenClient.getUser().getRegionAccount(),report.getReportNo());

            BizLogHelper.setValue(report.getOrderNo(),"Batch Approve["+String.join(",",orderClient.getExternalReportNos(reportNoList))+"]");
            BizLogHelper.setLabCode(orderAllDTO.getBUCode(), orderAllDTO.getLocationCode());
        });

        return response;
    }

    private List<ReportInfoPO> getReportInfoPOSByOrderNos(List<String> reportNoList) {
        ReportInfoExample example = new ReportInfoExample();
        example.createCriteria().andReportNoIn(reportNoList);
        List<ReportInfoPO> orderLabReportList = reportInfoMapper.selectByExample(example);
        return orderLabReportList;
    }



    private List<ReportFilePO> getReportFilePOS(String reportId) {
        ReportFileExample objReportFileExample=new ReportFileExample();
        objReportFileExample.createCriteria().andReportIDEqualTo(reportId);
        List<ReportFilePO> reportFilePOS=reportFileMapper.selectByExample(objReportFileExample);
        return reportFilePOS;
    }

    private List<ReportFilePO> getReportFilePOSByReportNo(List<String> reportNos) {
        ReportFileExample objReportFileExample=new ReportFileExample();
        objReportFileExample.createCriteria().andReportNoIn(reportNos);
        List<ReportFilePO> reportFilePOS=reportFileMapper.selectByExample(objReportFileExample);
        return reportFilePOS;
    }


    @Override
    public String batchGetPdf(ReportBatchActionReq request, HttpServletResponse response) {
        log.info("============batchGetPdf start==============");
        long start = System.currentTimeMillis();
        BaseResponse baseResponse = new BaseResponse();
        List<File> fileList = new ArrayList<File>();
        String tempFilePath = gpoConfig.getRootPath() + Constants.CATALOG.FILE + File.separator+ UUID.randomUUID().toString();
        File dir = new File(tempFilePath);
        try {
            FileUtils.forceMkdir(dir);
        } catch (IOException e1) {
            // TODO Auto-generated catch block
            log.error(e1.getMessage(),e1);
        }
        if(Func.isEmpty(request) || Func.isEmpty(request.getReportNoList())){
            return "请选择需要下载PDF的报告号！";
        }
        List<String> reportNoList = Arrays.asList(request.getReportNoList().split(","));
        try {
            // 基于报告号查询所有报告文件
            List<ReportFilePO> reportFilePOS = getReportFilePOSByReportNo(reportNoList);
            if(Func.isEmpty(reportFilePOS)){
                return "未查询到有效的报告!";
            }
            // 查询PDF报告
            List<ReportFilePO> pdfs =reportFilePOS.stream().filter(e->e.getReportFileType().intValue()==ReportFileType.PDF.getCode()).collect(Collectors.toList());
            if(Func.isEmpty(pdfs)){
                return "未查询到有效的报告!";
            }
            for(ReportFilePO pdf : pdfs) {
                String reportPdfUrl = frameWorkClient.downloadByCloudID(SgsSystem.GPO.getSgsSystemId(), pdf.getCloudID(), "2",pdf.getFilename());
                URL httpurl = new URL(reportPdfUrl);
                File reportPdf = new File(tempFilePath, pdf.getFilename());
                FileUtils.copyURLToFile(httpurl, reportPdf);
                fileList.add(reportPdf);
            }
        } catch (Exception e) {
            // TODO: handle exception
            log.error("=======download pdf error:{}==========",e);
        }
        if(CollectionUtils.isEmpty(fileList))
        {
            return "NO_FILE";
        }

        String zipFileName = "BatchGetPdf.zip";
        //压缩文件，输出文件
        reportService.outputZipFile(response, fileList, tempFilePath, dir, zipFileName);
        long end = System.currentTimeMillis();
        log.info("============batchGetPdf end, cost Time：{} s==============",(end-start)/1000);
        return null;
    }

    @Override
    public BaseResponse batchGetPdfCheck(ReportBatchActionReq request) {
        if(Func.isEmpty(request) || Func.isEmpty(request.getReportNoList())){
            return BaseResponse.newFailInstance("请选择需要下载PDF的报告号！");
        }

        List<String> reportNoList = Arrays.asList(request.getReportNoList().split(","));
        //校验Order Status GPO2-6935
        ReportInfoExample example = new ReportInfoExample();
        example.createCriteria().andReportNoIn(reportNoList);
        List<ReportInfoPO> reportInfoPOS = reportInfoMapper.selectByExample(example);
        List<String> orderNoList = reportInfoPOS.stream().map(ReportInfoPO::getOrderNo).filter(Func::isNotEmpty).distinct().collect(Collectors.toList());
        com.sgs.preorder.facade.model.req.OrderNosReq orderNosReq = new com.sgs.preorder.facade.model.req.OrderNosReq();
        orderNosReq.setOrderNos(orderNoList);
        BaseResponse<List<OrderAllDTO>> orderListBaseResponse = orderFacade.getOrderAllListByOrderNos(orderNosReq);
        List<String> errOrderNos = new ArrayList<>();
        for (ReportInfoPO reportInfoPO : reportInfoPOS) {
            if (Func.isNotEmpty(orderListBaseResponse) && Func.isNotEmpty(orderListBaseResponse.getData())) {
                List<OrderAllDTO> orderAllDTOS = orderListBaseResponse.getData();
                OrderAllDTO orderAllDTO = orderAllDTOS.stream().filter(i -> Func.equalsSafe(i.getOrderNo(), reportInfoPO.getOrderNo())).findFirst().orElse(null);
                if (Func.isNotEmpty(orderAllDTO) && com.sgs.preorder.facade.model.enums.OrderStatus.checkStatus(orderAllDTO.getOrderStatus(), com.sgs.preorder.facade.model.enums.OrderStatus.Pending)) {
                    errOrderNos.add(reportInfoPO.getReportNo());
                }
            }
        }
        if(Func.isNotEmpty(errOrderNos)){
            return  BaseResponse.newFailInstance(errOrderNos.toString()+" is pending order , please check again");
        }

        // 基于报告号查询所有报告文件
        List<ReportFilePO> reportFilePOS = getReportFilePOSByReportNo(reportNoList);
        if(Func.isEmpty(reportFilePOS)){
            return BaseResponse.newFailInstance("未查询到有效的报告!");
        }
        // 查询PDF报告
        List<ReportFilePO> pdfs =reportFilePOS.stream().filter(e->e.getReportFileType().intValue()==ReportFileType.PDF.getCode() && Func.isNotEmpty(e.getCloudID())).collect(Collectors.toList());
        if(Func.isEmpty(pdfs)){
            return BaseResponse.newFailInstance("未查询到有效的报告!");
        }
        return BaseResponse.newSuccessInstance(true);
    }


    @Override
    public BaseResponse batchDeliverEmailModel(ReportBatchActionReq request) {
        EmailModelDTO response = new EmailModelDTO();
        Set<String> customerIds = new HashSet<>();
        String token = SystemContextHolder.getSgsToken();
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

        List<String> reportNoList = Arrays.asList(request.getReportNoList().split(","));
        List<String> reportIdList = request.getReportIdList();

        ReportQueryPageRequest reportQueryPageRequest=new ReportQueryPageRequest();
        if (Func.equals(ReportTemplateType.DRAFT_REPORT.getCode(),request.getTemplateType())){
            reportQueryPageRequest.setReportIdList(reportIdList);
        }else {
            reportQueryPageRequest.setReportNoList(reportNoList);
        }
        if(Func.isEmpty(reportQueryPageRequest.getReportNoList())&&Func.isEmpty(reportQueryPageRequest.getReportIdList())){
            String labCode = tokenClient.getUser().getCurrentLabCode();
            reportQueryPageRequest.setLabCode(labCode);
        }

        List<GpnOrderReportDTO> orderReportDTOList=reportMapper.queryReportList(reportQueryPageRequest);
        reportNoList = orderReportDTOList.stream().map(GpnOrderReportDTO :: getReportNo).collect(Collectors.toList());
        reportIdList = orderReportDTOList.stream().map(GpnOrderReportDTO :: getReportId).collect(Collectors.toList());

        // 考虑内部分包场景，报告没有回传情况下，获取邮件收集人等信息以发包方为准
        List<String> orderNoList = request.getOrderNoList();
        if(Func.isEmpty(orderNoList)){
            orderNoList = orderReportDTOList.stream().map(labreport -> labreport.getOrderNo()).collect(Collectors.toList());
        }

        OrderNosReq objOrderNosReq=new OrderNosReq();
        objOrderNosReq.setOrderNos(orderNoList);
        objOrderNosReq.setProductLineCode(ProductLineContextHolder.getProductLineCode());
        BaseResponse<List<OrderAllDTO>> orderBaseResponse= orderFacade.getOrderAllListByOrderNos(objOrderNosReq);
        List<OrderAllDTO> orderList=orderBaseResponse.getData();
        GpoExternalOrderNoReq gpoExternalOrderNoReq = new GpoExternalOrderNoReq();
        gpoExternalOrderNoReq.setProductLineCode(ProductLineContextHolder.getProductLineCode());
        gpoExternalOrderNoReq.setOrderNoList(orderNoList);
        List<GpoExternalOrderInfo> externalOrderInfoList = orderFacade.getExternalOrder(gpoExternalOrderNoReq).getData();
        OrderIdReq orderIdReq = new OrderIdReq();
        orderIdReq.setOrderNos(orderNoList);
        orderIdReq.setProductLineCode(ProductLineContextHolder.getProductLineCode());
        List<OrderReferenceNoRsp> orderReferenceNoRspList = orderFacade.getReferenceNoByOrderNo(orderIdReq).getData();


        String allOrderCreateDate = orderList.stream().map(order-> simpleDateFormat.format(order.getCreatedDate())).collect(Collectors.joining(StringPool.COMMA));
        String allOrderCsName = orderList.stream().map(order -> order.getcSName()).distinct().collect(Collectors.joining(StringPool.COMMA));
        String allOrderCsPhone = orderList.stream().map(order -> order.getcSContact()).distinct().collect(Collectors.joining(StringPool.COMMA));
        String allOrderCsEmail = orderList.stream().map(order -> order.getcSEmail()).distinct().collect(Collectors.joining(StringPool.COMMA));
        String allReportApproveDate = orderReportDTOList.stream().map(
                repoprt -> ObjectUtils.isEmpty(repoprt.getApprovedDate()) ? StringPool.EMPTY:simpleDateFormat.format(repoprt.getApprovedDate())).collect(Collectors.joining(","));

        String reportConclusion = orderReportDTOList.stream().map(
                repoprt -> ObjectUtils.isEmpty(repoprt.getConclusion()) ? StringPool.EMPTY :repoprt.getConclusion()).distinct().collect(Collectors.joining(","));

        String applicantEmail=StringPool.EMPTY;
        String payerEmail=StringPool.EMPTY;
        String buyerEmail=StringPool.EMPTY;
        Boolean checkBuyer = checkBuyer(orderNoList);
        for(String orderNo:orderNoList){
            List<CustomerInstanceDTO> customerInstanceDTOS = getCustomerInstanceDTOS(orderNo);
            CustomerInstanceDTO applicant=customerInstanceDTOS.stream().filter(e->e.getCustomerUsage().equals(CustomerUsage.Applicant.getCode())).findFirst().orElse(null);
            CustomerInstanceDTO payer=customerInstanceDTOS.stream().filter(e->e.getCustomerUsage().equals(CustomerUsage.Payer.getCode())).findFirst().orElse(null);
            CustomerInstanceDTO buyer=customerInstanceDTOS.stream().filter(e->e.getCustomerUsage().equals(CustomerUsage.Buyer.getCode())).findFirst().orElse(null);
            if(applicant!=null){
                if(StringUtils.isNotBlank(applicant.getContactPersonEmail())){
                    applicantEmail=applicantEmail+applicant.getContactPersonEmail()+";";
                }
            }
            if(payer!=null){
                if(StringUtils.isNotBlank(payer.getContactPersonEmail())){
                    payerEmail=payerEmail+payer.getContactPersonEmail()+";";
                }
            }
            if(buyer!=null){
                if(StringUtils.isNotBlank(buyer.getContactPersonEmail())){
                    buyerEmail=buyerEmail+buyer.getContactPersonEmail()+";";
                }
            }
        }
        //取第一个order，report

        List<CustomerInstanceDTO> customerInstanceDTOS = getCustomerInstanceDTOS(orderNoList.get(0));
        CustomerInstanceDTO firstApplicant=customerInstanceDTOS.stream().filter(e->e.getCustomerUsage().equals(CustomerUsage.Applicant.getCode())).findFirst().orElse(null);
        CustomerInstanceDTO firstPayer=customerInstanceDTOS.stream().filter(e->e.getCustomerUsage().equals(CustomerUsage.Payer.getCode())).findFirst().orElse(null);
        CustomerInstanceDTO firstBuyer=customerInstanceDTOS.stream().filter(e->e.getCustomerUsage().equals(CustomerUsage.Buyer.getCode())).findFirst().orElse(null);
        CustomerInstanceDTO firstSubContract=customerInstanceDTOS.stream().filter(e->e.getCustomerUsage().equals(CustomerUsage.SUBCONTRACTFROM.getCode())).findFirst().orElse(null);

        // 取所有的buyer的集合
        for(String orderNo: orderNoList){
            List<CustomerInstanceDTO> customerInstances = getCustomerInstanceDTOS(orderNo);
            CustomerInstanceDTO buyer = customerInstances.stream().filter(e->e.getCustomerUsage().equals(CustomerUsage.Buyer.getCode())).findFirst().orElse(null);
            if(Func.isNotEmpty(buyer)){
                customerIds.add(buyer.getCustomerId());
            }
        }

        // 2、获取GeneralFields
        OrderAllDTO objOrderAllDTO=orderList.get(0);
        Map<String, Object> generalFields = new HashMap<>();

        generalFields.put("${OrderNo}", StringUtils.join(orderNoList,","));
        List<String> externalReportNoList = new ArrayList<>();
        List<EmailContextDTO> emailContextDTOList = new ArrayList<>();
        BuParamValueRsp objBuParamValueDTO=frameWorkClient.getBuParamValue(objOrderAllDTO.getBUCode(),objOrderAllDTO.getLocationCode(),"Report","ReportDeliverySender");
        //获取用户语言
        String defaultLanguageCode = frameWorkClient.getPrimaryLanguageCode(ProductLineContextHolder.getProductLineCode());
        for (GpnOrderReportDTO orderReportDTO : orderReportDTOList) {
            String reportNo = orderReportDTO.getReportNo();
            String orderNo = orderReportDTO.getOrderNo();
            String reportId = orderReportDTO.getReportId();
            if (request.isBatchFlag() && Func.equals(ReportTemplateType.DRAFT_REPORT.getCode(),request.getTemplateType())){
                EmailContextDTO emailContext = new EmailContextDTO();
                emailContext.setReportId(reportId);
                emailContext.setActualReportNo(orderReportDTO.getActualReportNo());
                Map<String, Object> generalFieldItem = new HashMap<>();
                //判断是接包方操作还是分包方操作：接包方取自己的取Report的OrderNo，分包方取Report的OrderNo对应的主订单的OrderNo
                if(!orderNoList.contains(orderNo)){
                    GeneralOrderInstanceInfoPO mainOrderByOrderNo = subContractExternalRelService.getMainOrderByOrderNo(orderNo);
                    if (Func.isNotEmpty(mainOrderByOrderNo) && Func.isNotEmpty(mainOrderByOrderNo.getOrderNo())){
                        orderNo = mainOrderByOrderNo.getOrderNo();
                    }
                }
                String finalOrderNo = orderNo;
                GpoExternalOrderInfo gpoExternalOrderInfo = externalOrderInfoList.stream().filter(item -> Func.equalsSafe(item.getOrderNo(), finalOrderNo)).findAny().orElse(null);
                generalFieldItem.put("${OrderNo}", Func.isNotEmpty(gpoExternalOrderInfo) && Func.isNotEmpty(gpoExternalOrderInfo.getExternalOrderNo())?gpoExternalOrderInfo.getExternalOrderNo():finalOrderNo);
                OrderReferenceNoRsp orderReferenceNoRsp = orderReferenceNoRspList.stream().filter(item -> Func.equalsSafe(item.getOrderNo(), finalOrderNo)).findAny().orElse(null);
                generalFieldItem.put("${ReferenceOrderNo}",Func.isEmpty(orderReferenceNoRsp)?StringPool.EMPTY:Func.toStr(orderReferenceNoRsp.getReferenceOrderNo()));
                generalFieldItem.put("${CustomerReferenceNo}", Func.isEmpty(orderReferenceNoRsp)?StringPool.EMPTY:Func.toStr(orderReferenceNoRsp.getCustomerReferenceNo()));
                generalFieldItem.put("${SubcontractOrderNo}", Func.isEmpty(orderReferenceNoRsp)?StringPool.EMPTY:Func.toStr(orderReferenceNoRsp.getReferenceOrderNo()));
                generalFieldItem.put("${ReportNo}", Func.toStr(orderReportDTO.getActualReportNo(),reportNo));
                Map<String, String> customerMap = reportService.initCustomerGeneralFiled(finalOrderNo);
                generalFieldItem.putAll(customerMap);
                generalFieldItem.put("${ReportConclusion}", Func.toStr(orderReportDTO.getConclusion()));
                generalFieldItem.put("${LabName}", orderReportDTO.getLabCode());

                generalFieldItem.put("${emailTitle}",Func.isEmpty(request.getEmailTitle()) ? StringPool.EMPTY: request.getEmailTitle());

                String reportId_base64 = Base64.getEncoder().encodeToString(reportId.getBytes(StandardCharsets.UTF_8));
                buildApproveReportUrls(reportId_base64, ProductLineContextHolder.getProductLineCode(), "Email", generalFieldItem,true);
                StringBuffer csOjbBuff = new StringBuffer();
                String orderCsPhone =StringPool.EMPTY;
                String orderCsEmail =StringPool.EMPTY;
                String orderCsName = StringPool.EMPTY;
                String orderCreateDate = StringPool.EMPTY;
                String reportApproveDate = Func.toStr(simpleDateFormat.format(orderReportDTO.getApprovedDate()));
                Map<String, Object> dffFields = new HashMap<>();
                if(Func.isNotEmpty(orderList)){
                    OrderAllDTO orderAllDTO = orderList.stream().filter(item -> Func.equalsSafe(item.getOrderNo(), finalOrderNo)).findAny().orElse(null);
                    if(Func.isNotEmpty(orderAllDTO)){
                        orderCsPhone = Func.toStr(orderAllDTO.getcSContact());
                        csOjbBuff.append("<br>").append(orderAllDTO.getcSName()).append("<br>").append(orderAllDTO.getcSEmail()).append("<br>").append(orderAllDTO.getcSContact()).append("<br>");

                        String reportLanguage = orderAllDTO.getReportLanguage();
                        Integer languageId = null;
                        if (ReportLanguage.EnglishReportOnly.getCode().equals(reportLanguage)){
                            languageId =LanguageType.English.getLanguageId();
                        }else if (ReportLanguage.ChineseReportOnly.getCode().equals(reportLanguage)){
                            languageId =LanguageType.Chinese.getLanguageId();
                        }
                        if (Func.isEmpty(languageId)){
                            if (UserInfoDefaultLanguageCodeEnums.check(defaultLanguageCode,UserInfoDefaultLanguageCodeEnums.zh_cn)){
                                languageId =LanguageType.Chinese.getLanguageId();
                            }else {
                                languageId =LanguageType.English.getLanguageId();
                            }
                        }
                        if(Func.isNotEmpty(languageId)){
                            UnpivotProductReq unpivotProductReq = new UnpivotProductReq();
                            unpivotProductReq.setReportNo(reportNo);
                            unpivotProductReq.setOrderNo(finalOrderNo);
                            BaseResponse<List<TestrptCoverpageDffSimpleDTO>> dffResult = dffFacade.unpivotProduct(unpivotProductReq);
                            List<TestrptCoverpageDffSimpleDTO> dffForms = dffResult.getData();
                            if (dffForms == null){
                                dffForms = com.google.common.collect.Lists.newArrayList();
                            }
                            try {
                                Integer finalLanguageId = languageId;
                                dffForms = dffForms
                                        .stream()
                                        .filter(dff -> dff.getLanguageID() != null && dff.getLanguageID().compareTo(finalLanguageId) == 0)
                                        .collect(Collectors.toList());
                            } catch (Exception ex) {
                                String errmsg = String.format("queryReportDetails.dffForms过滤异常,orderNo=%s,reportNo=%s",finalOrderNo, reportNo);
                                log.error(errmsg, ex);
                            }

                            for (TestrptCoverpageDffSimpleDTO dff : dffForms) {
                                if (StringUtils.isNoneBlank(dff.getDisplayData())) {
                                    dffFields.put("$DFF{" + dff.getDffFormID() + "}.{" + dff.getFieldCode() + "}", dff.getDisplayData());
                                }
                                if(Constants.DFF_FILED_LIST.contains(dff.getFieldCode())){
                                    generalFieldItem.put("${" +dff.getFieldCode()+ "}", Func.toStr(dff.getDisplayData()));
                                }
                            }
                        }
                    }
                }
                generalFieldItem.put("${ResponsibleCSTel}",orderCsPhone);
                generalFieldItem.put("${ResponsibleCSEmail}",orderCsEmail);
                generalFieldItem.put("${ResponsibleCSName}", orderCsName);
                generalFieldItem.put("${OrderCreatedDate}",orderCreateDate);
                generalFieldItem.put("${ApprovedDate}", reportApproveDate);
                generalFieldItem.put("${CSObject}", csOjbBuff.toString());
                generalFieldItem.put("${PrelimResultConclusion}", StringPool.EMPTY);
                emailContext.setGeneralFields(generalFieldItem);
                emailContext.setSgsToken(token); // 等待页面赋值
                emailContext.setFrom(null); // 等待页面赋值
                emailContext.setSender(objBuParamValueDTO==null?StringPool.EMPTY:objBuParamValueDTO.getParamValue()); //by lab sender
                emailContext.setRespCs(null); // 等待页面赋值
                emailContext.setRespCsName(null); // 等待页面赋值
                emailContext.setLab(SystemContextHolder.getLabCode());

                CustomerInstanceDTO applicant=customerInstanceDTOS.stream().filter(e->e.getCustomerUsage().equals(CustomerUsage.Applicant.getCode())).findFirst().orElse(null);
                CustomerInstanceDTO payer=customerInstanceDTOS.stream().filter(e->e.getCustomerUsage().equals(CustomerUsage.Payer.getCode())).findFirst().orElse(null);
                CustomerInstanceDTO buyer=customerInstanceDTOS.stream().filter(e->e.getCustomerUsage().equals(CustomerUsage.Buyer.getCode())).findFirst().orElse(null);
                CustomerInstanceDTO subcontractFrom=customerInstanceDTOS.stream().filter(e->e.getCustomerUsage().equals(CustomerUsage.SUBCONTRACTFROM.getCode())).findFirst().orElse(null);
                EmailAddressDTO emailAddress = new EmailAddressDTO();
                if(applicant!=null && Func.isNotEmpty(applicant.getContactPersonEmail())){
                    emailAddress.setApplicant(applicant.getContactPersonEmail());
                }
                if(payer!=null && Func.isNotEmpty(payer.getContactPersonEmail())){
                    emailAddress.setPayer(payer.getContactPersonEmail());
                }
                if(buyer!=null && Func.isNotEmpty(buyer.getContactPersonEmail())){
                    emailAddress.setBuyer(buyer.getContactPersonEmail());
                }
                if(subcontractFrom!=null && Func.isNotEmpty(subcontractFrom.getContactPersonEmail())){
                    emailAddress.setSubcontract(subcontractFrom.getContactPersonEmail());
                }
                emailContext.setEmailAddress(emailAddress);
                emailContext.setDffFields(dffFields);
                emailContext.setOrderNos(Lists.newArrayList(finalOrderNo));
                emailContextDTOList.add(emailContext);
            }



            if(Func.isNotEmpty(externalOrderInfoList)){
                String externalOrderNoList = externalOrderInfoList.stream().map(GpoExternalOrderInfo::getExternalOrderNo).filter(Func::isNotEmpty).distinct().collect(Collectors.joining(","));
                generalFields.put("${OrderNo}", externalOrderNoList);
                List<GpoExternalReportInfo> externalReportInfoList = externalOrderInfoList.stream().flatMap(tl->tl.getExternalReportInfoList().stream()).collect(Collectors.toList());
                if(Func.isNotEmpty(externalReportInfoList)){
                    GpoExternalReportInfo gpoExternalReportInfo = externalReportInfoList.stream().filter(i -> Func.equals(i.getReportNo(), reportNo)).findFirst().orElse(null);
                    String externalReportNo = reportNo;
                    if(Func.isNotEmpty(gpoExternalReportInfo) && Func.isNotEmpty(gpoExternalReportInfo.getExternalReportNo())){
                        externalReportNo = Func.toStr(gpoExternalReportInfo.getExternalReportNo(),reportNo);
                    }
                    if (Func.equals(ReportTemplateType.DRAFT_REPORT.getCode(),request.getTemplateType())){
                        List<String> subReportNos = new ArrayList<>();
                        subReportNos.add(reportNo);
                        List<ExternalReportNoRsp> externalSubReportNoByReportNo = orderClient.getExternalReportNoByReportNo(subReportNos, ProductLineContextHolder.getProductLineCode());
                        if (Func.isNotEmpty(externalSubReportNoByReportNo)){
                            externalReportNoList.add(externalSubReportNoByReportNo.get(0).getExternalReportNo());
                        }else {
                            externalReportNoList.add(reportNo);
                        }
                    }else {
                        GpnOrderReportDTO gpnOrderReportDTO = orderReportDTOList.stream().filter(item -> Func.equalsSafe(item.getReportNo(), reportNo)).findAny().orElse(null);
                        if(!ReportFlagEnums.check(gpnOrderReportDTO.getReportFlag(),ReportFlagEnums.REPORT)){
                            //查询是否是分包回来的报告
                            SubReportPO subReportPO = subReportExtMapper.querySubReportByReportNo(reportNo);
                            if (Func.isNotEmpty(subReportPO) && Func.isNotEmpty(subReportPO.getSubReportNo())){
                                List<String> subReportNos = new ArrayList<>();
                                subReportNos.add(subReportPO.getSubReportNo());
                                List<ExternalReportNoRsp> externalSubReportNoByReportNo = orderClient.getExternalReportNoByReportNo(subReportNos, ProductLineContextHolder.getProductLineCode());
                                if (Func.isNotEmpty(externalSubReportNoByReportNo)){
                                    externalReportNoList.add(externalSubReportNoByReportNo.get(0).getExternalReportNo());
                                }else {
                                    externalReportNoList.add(subReportPO.getSubReportNo());
                                }
                            }else{
                                externalReportNoList.add(externalReportNo);
                            }
                        }
                        else {
                            externalReportNoList.add(externalReportNo);
                        }
                    }

                }
            }
        }

        if(Func.isNotEmpty(orderReferenceNoRspList)){
            List<String> externalReferenceOrderNos = orderReferenceNoRspList.stream().map(OrderReferenceNoRsp::getSgsExternalReferenceNo).distinct().collect(Collectors.toList());
            List<String> customerReferenceOrderNos = orderReferenceNoRspList.stream().map(OrderReferenceNoRsp::getCustomerReferenceNo).distinct().collect(Collectors.toList());
            List<String> subcontractOrderNoList = orderReferenceNoRspList.stream().map(OrderReferenceNoRsp::getReferenceOrderNo).filter(s -> Func.isNotEmpty(s)).distinct().collect(Collectors.toList());
            generalFields.put("${ReferenceOrderNo}",Func.isEmpty(externalReferenceOrderNos)?StringPool.EMPTY:StringUtils.join(externalReferenceOrderNos,","));
            generalFields.put("${CustomerReferenceNo}", Func.isEmpty(customerReferenceOrderNos)?StringPool.EMPTY:StringUtils.join(customerReferenceOrderNos,","));
            generalFields.put("${SubcontractOrderNo}", Func.isEmpty(subcontractOrderNoList)?StringPool.EMPTY:StringUtils.join(subcontractOrderNoList,","));
        }


        generalFields.put("${ReportNo}", StringUtils.join(externalReportNoList,","));
        // Prelim场景直接取ActualReportNo
        if(request.getTemplateType()==4 && Func.isNotEmpty(orderReportDTOList)){
            generalFields.put("${ReportNo}", orderReportDTOList.stream().map(GpnOrderReportDTO::getActualReportNo).collect(Collectors.joining(",`")));
        }
        Map<String, String> customerMap = reportService.initCustomerGeneralFiled(objOrderAllDTO.getOrderNo());
        generalFields.putAll(customerMap);
        generalFields.put("${ReportConclusion}", reportConclusion);
        generalFields.put("${LabName}", objOrderAllDTO.getLocationCode()+" "+objOrderAllDTO.getBUCode());
        generalFields.put("${ResponsibleCSTel}",Func.toStr(allOrderCsPhone));
        generalFields.put("${ResponsibleCSEmail}",Func.toStr(allOrderCsEmail));
        generalFields.put("${ResponsibleCSName}", Func.toStr(allOrderCsName));
        generalFields.put("${OrderCreatedDate}",Func.toStr(allOrderCreateDate));
        generalFields.put("${ApprovedDate}", Func.toStr(allReportApproveDate));
        generalFields.put("${emailTitle}",Func.isEmpty(request.getEmailTitle()) ? StringPool.EMPTY: request.getEmailTitle());

        //设置 CSObjcet
        StringBuffer csOjbBuff = new StringBuffer();
        orderList.forEach(order->{
            csOjbBuff.append("<br>").append(order.getcSName()).append("<br>").append(order.getcSEmail()).append("<br>").append(order.getcSContact()).append("<br>");
        });

        generalFields.put("${CSObject}", csOjbBuff.toString());
        if(Func.isNotEmpty(reportIdList)){
            String reportId = reportIdList.get(0);
            String reportId_base64 = Base64.getEncoder().encodeToString(reportId.getBytes(StandardCharsets.UTF_8));
            buildApproveReportUrls(reportId_base64, ProductLineContextHolder.getProductLineCode(), "Email", generalFields,true);
        }
        // 3、获取DffFields
        Map<String, Object> dffFields = new HashMap<>();
        if(reportNoList.size()==1){
            String reportLanguage = objOrderAllDTO.getReportLanguage();
            Integer languageId = null;
            if (ReportLanguage.EnglishReportOnly.getCode().equals(reportLanguage)){
                languageId =LanguageType.English.getLanguageId();
            }else if (ReportLanguage.ChineseReportOnly.getCode().equals(reportLanguage)){
                languageId =LanguageType.Chinese.getLanguageId();
            }
            if (Func.isEmpty(languageId)){
                //获取用户语言
                if (UserInfoDefaultLanguageCodeEnums.check(defaultLanguageCode,UserInfoDefaultLanguageCodeEnums.zh_cn)){
                    languageId =LanguageType.Chinese.getLanguageId();
                }else {
                    languageId =LanguageType.English.getLanguageId();
                }
            }
            if(Func.isNotEmpty(languageId)){
                UnpivotProductReq unpivotProductReq = new UnpivotProductReq();
                unpivotProductReq.setReportNo(reportNoList.get(0));
                unpivotProductReq.setOrderNo(objOrderAllDTO.getOrderNo());
                BaseResponse<List<TestrptCoverpageDffSimpleDTO>> dffResult = dffFacade.unpivotProduct(unpivotProductReq);
                List<TestrptCoverpageDffSimpleDTO> dffForms = dffResult.getData();
                if (dffForms == null){
                    dffForms = com.google.common.collect.Lists.newArrayList();
                }
                try {
                    Integer finalLanguageId = languageId;
                    dffForms = dffForms
                            .stream()
                            .filter(dff -> dff.getLanguageID() != null && dff.getLanguageID().compareTo(finalLanguageId) == 0)
                            .collect(Collectors.toList());
                } catch (Exception ex) {
                    String errmsg = String.format("queryReportDetails.dffForms过滤异常,orderNo=%s,reportNo=%s",objOrderAllDTO.getOrderNo(), reportNoList.get(0));
                    log.error(errmsg, ex);
                }

                for (TestrptCoverpageDffSimpleDTO dff : dffForms) {
                    if (StringUtils.isNoneBlank(dff.getDisplayData())) {
                        dffFields.put("$DFF{" + dff.getDffFormID() + "}.{" + dff.getFieldCode() + "}", dff.getDisplayData());
                    }
                    if(Constants.DFF_FILED_LIST.contains(dff.getFieldCode())){
                        generalFields.put("${" +dff.getFieldCode()+ "}", Func.toStr(dff.getDisplayData()));
                    }
                }
            }
        }else{
            OrderIdReq objOrderIdReq=new OrderIdReq();
            objOrderIdReq.setProductLineCode(ProductLineContextHolder.getProductLineCode());
            objOrderIdReq.setOrderNo(objOrderAllDTO.getOrderNo());
            BaseResponse<String> stringBaseResponse = orderFacade.queryProductInsForPeSendEmailStr(objOrderIdReq);
            List<Map<String, Object>> proSampleList = JSON.parseObject(stringBaseResponse.getData(), new TypeReference<List<Map<String, Object>>>() {
            });

            // 1.找到集合的head 信息
            for (Map<String, Object> proSample : proSampleList) {
//	            // 判断 headId 是否为空,业务逻辑只有一个head新
                if ( Func.isEmpty(proSample.get("HeaderID"))) {
                    // DFF FROM,GRID 数据都传
//		        	String  dffFormId = proSample.get("DFFFormID").toString();
                    for (String key : proSample.keySet()) {
                        String newKey = "$DFF{*}.{"+key+"}";
                        dffFields.put(newKey, proSample.get(key));
                    }
                    break;
                }
            }
        }

        // 、获取EmailAddress
        EmailAddressDTO emailAddress = new EmailAddressDTO();
        if(applicantEmail!=null){
            emailAddress.setApplicant(applicantEmail);
        }
        if(payerEmail!=null){
            emailAddress.setPayer(payerEmail);
        }
        if(buyerEmail!=null){
            emailAddress.setBuyer(buyerEmail);
        }
        if (firstApplicant != null) {
            String contractEmail = firstApplicant.getContactPersonEmail();
            emailAddress.setApplicant(contractEmail);
        }
        if (firstPayer != null) {
            String contractEmail = firstPayer.getContactPersonEmail();
            emailAddress.setPayer(contractEmail);
        }
        if (firstSubContract != null) {
            String contractEmail = firstSubContract.getContactPersonEmail();
            emailAddress.setSubcontract(contractEmail);
        }
        if (firstBuyer != null) {
            String contractEmail = firstBuyer.getContactPersonEmail();
            emailAddress.setBuyer(contractEmail);
        }

        // 、获取AttachmentList
        List<EmailAttachmentDTO> attachmentList = new ArrayList<EmailAttachmentDTO>();
        DeliverReportEmailReq deliverReportEmailRequest = new DeliverReportEmailReq();
        deliverReportEmailRequest.setReportType(request.getTemplateType());
        deliverReportEmailRequest.setDeliverReportFormatList(Arrays.asList(new String[]{"PDF"})); // 默认发送PDF文件
        List<String> emailTo = new ArrayList<>();
        List<String> emailCc =  new ArrayList<>();
        String prelimResultConclusion = StringPool.EMPTY;
        String reportLanguage = objOrderAllDTO.getReportLanguage();
        String prelimResultNo = request.getPrelimResultNo();
        if(request.getTemplateType()==4)
        {
            PrelimResultInfoPO prelimResultInfoPO = null;
            // prelim report
            PrelimResultInfoExample prelimResultInfoExample = new PrelimResultInfoExample();
            prelimResultInfoExample.createCriteria().andPrelimResultNoEqualTo(prelimResultNo);
            List<PrelimResultInfoPO> prelimResultInfoPOS = prelimResultInfoMapper.selectByExample(prelimResultInfoExample);
            if (Func.isNotEmpty(prelimResultInfoPOS)){
                prelimResultInfoPO = prelimResultInfoPOS.get(0);
            }
            if(Func.isNotEmpty(prelimResultInfoPO)){
                //查询Conclusion
                ConclusionInfoExample conclusionInfoExample = new ConclusionInfoExample();
                conclusionInfoExample.createCriteria().andObjectIDEqualTo(prelimResultInfoPO.getId()).andConclusionLevelIDEqualTo(ConclusionType.PrelimResult.getCode());
                List<ConclusionInfoPO> prelimConclusionInfoPOS = conclusionInfoMapper.selectByExample(conclusionInfoExample);
                if (Func.isNotEmpty(prelimConclusionInfoPOS)) {
                    String conclusionSettingId = prelimConclusionInfoPOS.get(0).getConclusionSettingID();
                    GetCustomerConclusionReq getCustomerConclusionReq = new GetCustomerConclusionReq();
                    getCustomerConclusionReq.setConclusionSettingIds(Lists.newArrayList(conclusionSettingId));
                    getCustomerConclusionReq.setProductLineCode(ProductLineContextHolder.getProductLineCode());
                    List<GetCustomerConclusionRsp> customerConclusionRspList = dataEntryClient.getCustomerConclusionByConclusionSettingId(getCustomerConclusionReq);
                    prelimResultConclusion = this.getOverallDescriptionBySetting(conclusionSettingId,customerConclusionRspList,reportLanguage);
                }
            }

        }
        generalFields.put("${PrelimResultConclusion}", Func.toStr(prelimResultConclusion));
        generalFields.put("${PrelimResultNo}", Func.toStr(prelimResultNo));

        for(GpnOrderReportDTO orderLabReport:orderReportDTOList)
        {
            //todo kevin
/*            if(StringUtils.isNotEmpty(orderLabReport.getDeliverReportFormat()))
            {
                String[] formartTemp = orderLabReport.getDeliverReportFormat().split(",");
                deliverReportEmailRequest.setDeliverReportFormatList(Arrays.asList(formartTemp));
            }*/

            reportService.initEmailAddress(objOrderAllDTO, emailTo,emailCc,firstApplicant,firstPayer,firstBuyer,firstSubContract,request.getTemplateType());
            //初始化附件
            reportService.initReportAttList(deliverReportEmailRequest.getReportType(), orderLabReport.getReportId(), attachmentList,prelimResultNo);
        }
//        orderReferenceNoRspList


        if(Func.isNotEmpty(attachmentList)){
            for (EmailAttachmentDTO emailAttachmentDTO : attachmentList) {
                String sgsExternalReferenceNo = StringPool.EMPTY;
                String customerReferenceNo = StringPool.EMPTY;
                if(Func.isNotEmpty(orderReportDTOList)){
                    GpnOrderReportDTO orderReportDTO = orderReportDTOList.stream().filter(reportDTO -> Func.equals(reportDTO.getReportId(), emailAttachmentDTO.getReportId())).findFirst().orElse(null);
                    if(Func.isNotEmpty(orderReportDTO) && Func.isNotEmpty(orderReferenceNoRspList)){
                        OrderAllDTO orderAllDTO = orderList.stream().filter(o -> Func.equals(o.getOrderNo(), orderReportDTO.getOrderNo())).findFirst().orElse(null);
                        if(Func.isNotEmpty(orderAllDTO) && (CaseType.check(orderAllDTO.getCaseType(),CaseType.IDB) || CaseType.check(orderAllDTO.getCaseType(),CaseType.IDN) || CaseType.check(orderAllDTO.getCaseType(),CaseType.IDNTJ))){
                            OrderReferenceNoRsp orderReferenceNoRsp = orderReferenceNoRspList.stream().filter(i -> Func.equals(i.getOrderNo(), orderReportDTO.getOrderNo())).findFirst().orElse(null);
                            if(Func.isNotEmpty(orderReferenceNoRsp)){
                                sgsExternalReferenceNo = Func.toStr(orderReferenceNoRsp.getSgsExternalReferenceNo());
                                customerReferenceNo = Func.toStr(orderReferenceNoRsp.getCustomerReferenceNo());
                            }
                            String originFileName = emailAttachmentDTO.getFileName();
                            if(Func.isNotEmpty(originFileName)&& originFileName.split("\\.").length==2){
                                String prefix = originFileName.split("\\.")[0];
                                String suffix = originFileName.split("\\.")[1];
                                String fileNamePrefix = prefix;
                                String fileNameSuffix = StringPool.EMPTY;
                                if(StringUtils.lastIndexOfIgnoreCase(prefix,"_Draft") !=-1 ){
                                    fileNamePrefix = originFileName.substring(0,StringUtils.lastIndexOfIgnoreCase(prefix,"_Draft"));
                                    fileNameSuffix = originFileName.substring(StringUtils.lastIndexOfIgnoreCase(prefix,"_Draft"),prefix.length());
                                }else  if(StringUtils.lastIndexOfIgnoreCase(prefix,"_CN") !=-1 ){
                                    fileNamePrefix = originFileName.substring(0,StringUtils.lastIndexOfIgnoreCase(prefix,"_CN"));
                                    fileNameSuffix = originFileName.substring(StringUtils.lastIndexOfIgnoreCase(prefix,"_CN"),prefix.length());
                                }
                                String fullFileName = StringPool.EMPTY;
                                fullFileName += fileNamePrefix;
                                if(Func.isNotEmpty(sgsExternalReferenceNo)){
                                    fullFileName += "-";
                                    fullFileName += sgsExternalReferenceNo;
                                }
                                if(Func.isNotEmpty(customerReferenceNo)){
                                    fullFileName += "-";
                                    fullFileName += customerReferenceNo;
                                }
                                fullFileName += fileNameSuffix;
                                fullFileName += ".";
                                fullFileName += suffix;
                                emailAttachmentDTO.setFileName(fullFileName);
                            }
                        }
                    }
                }
            }
        }
        String customized_to = emailTo.stream().distinct().collect(Collectors.joining(";"));
        String customized_cc = emailCc.stream().distinct().collect(Collectors.joining(";"));
        emailAddress.setCustomized_to(customized_to);
        emailAddress.setCustomized_cc(customized_cc);
        // 5、获取EmailContext
        EmailContextDTO emailContext = new EmailContextDTO();
        emailContext.setSgsToken(null); // 等待页面赋值
        emailContext.setFrom(null); // 等待页面赋值
        emailContext.setSender(null); // 等待页面赋值
        emailContext.setSender(objBuParamValueDTO==null?StringPool.EMPTY:objBuParamValueDTO.getParamValue()); //by lab sender
        emailContext.setRespCs(null); // 等待页面赋值
        emailContext.setRespCsName(null); // 等待页面赋值
        emailContext.setLab(tokenClient.getUserLabCode());
        emailContext.setEmailAddress(emailAddress);
        emailContext.setGeneralFields(generalFields);
        emailContext.setDffFields(dffFields);
        emailContext.setAttachmentList(attachmentList);
        emailContext.setOrderNos(orderNoList);

        // 6、获取EmailModelResponsez
        String customerId = (checkBuyer) ? firstBuyer==null?StringPool.EMPTY:firstBuyer.getCustomerId() : firstApplicant==null?StringPool.EMPTY:firstApplicant.getCustomerId();
        if(Func.isNotEmpty(firstApplicant)){
            customerIds.add(firstApplicant.getCustomerId());
        }
        response.setCustomerId(customerId);
        response.setCustomerIds(customerIds);
        response.setSystemId(Func.toStr(SgsSystem.GPO.getSgsSystemId()));
        if (Func.equals(ReportTemplateType.DRAFT_REPORT.getCode(),request.getTemplateType())){
            response.setTemplateType(ReportTemplateType.DRAFT_REPORT.getMessage());
        }else if(Func.equals(ReportTemplateType.PRELIM_REPORT.getCode(),request.getTemplateType())) {
            response.setTemplateType(ReportTemplateType.PRELIM_REPORT.getMessage());
        }else {
            response.setTemplateType(ReportTemplateType.REPORT.getMessage());
        }

        response.setEmailContext(emailContext);
        for (EmailContextDTO emailContextDTO : emailContextDTOList) {
            List<EmailAttachmentDTO> attachmentDTOS = attachmentList.stream().filter(a -> Func.equalsSafe(a.getReportId(), emailContextDTO.getReportId())).collect(Collectors.toList());
            emailContextDTO.setAttachmentList(attachmentDTOS);
        }
        response.setEmailContextList(emailContextDTOList);
        return BaseResponse.newSuccessInstance(response);
    }
    private void buildApproveReportUrls(String reportId_base64, String buCode, String customized_to, Map<String, Object> generalFields,boolean useExpression) {
        String baseDraftApproveUrl = buildDraftApproveUrl("confirm", reportId_base64, buCode, customized_to);
        String baseDraftRejectUrl = buildDraftApproveUrl("reject", reportId_base64, buCode, customized_to);

        String draftApproveUrl = interfaceConfig.getBaseUrl() + baseDraftApproveUrl;
        String externalDraftApproveUrl = interfaceConfig.getExtranetUrl() + baseDraftApproveUrl;
        String draftRejectUrl = interfaceConfig.getBaseUrl() + baseDraftRejectUrl;
        String externalDraftRejectUrl = interfaceConfig.getExtranetUrl() + baseDraftRejectUrl;

        String approveUrlKey = useExpression ? "${approveUrl}" : "approveUrl";
        String extranetApproveUrlKey = useExpression ? "${extranetApproveUrl}" : "extranetApproveUrl";
        String rejectUrlKey = useExpression ? "${rejectUrl}" : "rejectUrl";
        String extranetRejectUrlKey = useExpression ? "${extranetRejectUrl}" : "extranetRejectUrl";

        generalFields.put(approveUrlKey, draftApproveUrl);
        generalFields.put(extranetApproveUrlKey, externalDraftApproveUrl);
        generalFields.put(rejectUrlKey, draftRejectUrl);
        generalFields.put(extranetRejectUrlKey, externalDraftRejectUrl);

    }

    private String buildDraftApproveUrl(String action, String reportId_base64, String buCode, String customized_to) {
        return "/gpn-api/report/" + action + "/email/report/" + reportId_base64 + "/" + buCode + "/" + customized_to;
    }

    // 使用示例

    private Boolean checkBuyer(List<String> orderNos) {
        HashMap<String, CustomerInstanceDTO> map = new HashMap<>();
        for(String orderNo:orderNos){
            List<CustomerInstanceDTO> customerInstanceDTOS = getCustomerInstanceDTOS(orderNo);
            if(CollectionUtil.isNotEmpty(customerInstanceDTOS)){
                CustomerInstanceDTO tOrderCustomerInstance=customerInstanceDTOS.stream().filter(e->e.getCustomerUsage().equals("buyer")).findFirst().orElse(null);
                if(tOrderCustomerInstance!=null) {
                    map.put(tOrderCustomerInstance.getCustomerNameCN() != null ? tOrderCustomerInstance.getCustomerNameCN() : tOrderCustomerInstance.getCustomerNameEN(), tOrderCustomerInstance);
                }else{
                    return false;
                }
            }

        }
        if(map.size()>1){
            return false;
        }
        return true;
    }

    private List<CustomerInstanceDTO> getCustomerInstanceDTOS(String orderNo) {
        OrderIdReq objOrderIdReq=new OrderIdReq();
        objOrderIdReq.setOrderNo(orderNo);
        objOrderIdReq.setProductLineCode(ProductLineContextHolder.getProductLineCode());
        BaseResponse<List<CustomerInstanceDTO>> baseResponse=orderFacade.queryCustomerForPe(objOrderIdReq);
        List<CustomerInstanceDTO> customerInstanceDTOS=baseResponse.getData();
        return customerInstanceDTOS;
    }

    @BizLog(bizType = BizLogConstant.REPORT_OPERATION_HISTORY, operType = "Generate")
    @Override
    public BaseResponse batchSaveGenerateReport(ReportForBatchGenerateReq request, HttpServletResponse response) {
        Date date = new Date();
        // TODO Auto-generated method stub
        log.info("============batchSaveGenerateReport start==============");
        long start = System.currentTimeMillis();
        BaseResponse baseResponse = new BaseResponse();
        //1.批量保存
        List<String> fileList = new ArrayList<String>();

        //2.批量 生成word
        try {
            //保存
            ReportBatchActionReq req =Func.copy(request,ReportBatchActionReq.class);
            batchSaveReport(req);

            List<String> reportNoList = Arrays.asList(request.getReportNoList().split(","));
            ReportQueryPageRequest reportQueryPageRequest=new ReportQueryPageRequest();
            reportQueryPageRequest.setReportNoList(reportNoList);
            List<GpnOrderReportDTO> orderReportDTOList=reportMapper.queryReportList(reportQueryPageRequest);

            List<String> orderNos = orderReportDTOList.stream().map(GpnOrderReportDTO::getOrderNo).distinct().collect(Collectors.toList());
            List<OrderInfoDto> orderList = orderClient.getOrderListByOrderNos(orderNos);
            for(GpnOrderReportDTO orderReport:orderReportDTOList)
            {

                ReportForGenerateRequest objReportForGenerateRequest=Func.copy(request,ReportForGenerateRequest.class);
                objReportForGenerateRequest.setOrderNo(orderReport.getOrderNo());
                objReportForGenerateRequest.setReportId(orderReport.getReportId());
                if(Func.isNotEmpty(request.getOtsTemplateFileCnPath())) {
                    GenerateTemplateDTO generateTemplateDtoCN = new GenerateTemplateDTO();
                    generateTemplateDtoCN.setTemplateSettingID(request.getOtsTemplateCnId());
                    generateTemplateDtoCN.setTemplateName(request.getOtsTemplateCnName());
                    generateTemplateDtoCN.setContainer(request.getContainerCnId());
                    generateTemplateDtoCN.setContainerTemplateFilePath(request.getOtsTemplateFileCnPath());
                    objReportForGenerateRequest.setGenerateTemplateDtoCN(generateTemplateDtoCN);
                }
                if(Func.isNotEmpty(request.getOtsTemplateFileEnPath())) {
                    GenerateTemplateDTO generateTemplateDtoEN = new GenerateTemplateDTO();
                    generateTemplateDtoEN.setTemplateSettingID(request.getOtsTemplateEnId());
                    generateTemplateDtoEN.setTemplateName(request.getOtsTemplateEnName());
                    generateTemplateDtoEN.setContainer(request.getContainerEnId());
                    generateTemplateDtoEN.setContainerTemplateFilePath(request.getOtsTemplateFileEnPath());
                    objReportForGenerateRequest.setGenerateTemplateDtoEN(generateTemplateDtoEN);
                }
                if(Func.isNotEmpty(request.getOtsTemplateFileEnPath())||Func.isNotEmpty(request.getOtsTemplateFileCnPath())) {
                    BaseResponse<GpnGenerateReportRsp> baseResponse1 = reportService.generateReport(objReportForGenerateRequest);
                    GpnGenerateReportRsp objGpnGenerateReportRsp = baseResponse1.getData();
                    if(Func.isNotEmpty(objGpnGenerateReportRsp.getWordFileCnUrl()))
                    {
                        fileList.add(objGpnGenerateReportRsp.getWordFileCnUrl());
                    }
                    if(Func.isNotEmpty(objGpnGenerateReportRsp.getWordFileEnUrl()))
                    {
                        fileList.add(objGpnGenerateReportRsp.getWordFileEnUrl());
                    }

                    BizLogHelper.setValue(orderReport.getOrderNo(),"Batch Generate["+String.join(",",orderClient.getExternalReportNos(reportNoList))+"]");
                    OrderInfoDto orderInfoDto = null;
                    if(Func.isNotEmpty(orderList)) {
                        orderInfoDto = orderList.stream().filter(e -> Func.equals(e.getOrderNo(), orderReport.getOrderNo())).findFirst().orElse(null);
                    }
                    if (Func.isNotEmpty(orderInfoDto)){
                        BizLogHelper.setLabCode(orderInfoDto.getBUCode(),orderInfoDto.getLocationCode());
                    }
                }
            }
        } catch (Exception e) {
            // TODO: handle exception
            log.error("======批量general word 异常：{}========",e);
        }

        String zipFileName = "BatchWord.zip";
        //压缩文件，输出文件
        String tempFilePath = gpoConfig.getRootPath() + Constants.CATALOG.FILE + File.separator+ UUID.randomUUID().toString();
        File dir = new File(tempFilePath);
        try {
            FileUtils.forceMkdir(dir);
        } catch (IOException e1) {
            // TODO Auto-generated catch block
            log.error(e1.getMessage(),e1);
        }
        this.outputZipFile(response, fileList, tempFilePath, dir, zipFileName);
        long end = System.currentTimeMillis();
        log.info("============batchSaveGenerateReport end, cost Time：{} s==============",(end-start)/1000);
        return baseResponse;
    }


    public void outputZipFile(HttpServletResponse response, List<String> filepathlist, String tempFilePath, File dir, String zipFileName) {
        //3. 压缩文件
        File zipFile = new File(tempFilePath, zipFileName);
        ZipOutputStream zos = null;
        FileOutputStream out = null;
        try {
            out = new FileOutputStream(zipFile);
            zos = new ZipOutputStream(out);
            for (String srcFile : filepathlist) {
                URL url = new URL(srcFile);
                URLConnection connection = url.openConnection();
                InputStream inputStream = connection.getInputStream();
                byte[] buf = new byte[4098];
                zos.putNextEntry(new ZipEntry(srcFile.substring(srcFile.lastIndexOf("/")+1,srcFile.lastIndexOf("?"))));
                int len;
                //java.io.FileInputStream in = new java.io.FileInputStream(srcFile);
                while ((len = inputStream.read(buf)) != -1) {
                    zos.write(buf, 0, len);
                }
//	                zos.closeEntry();
                inputStream.close();
            }

        } catch (Exception e) {
            // TODO: handle exception
            log.error("=====文件压缩异常：{}======",e);
        }finally {

            if(zos!=null)
            {
                try {
                    zos.close();
                } catch (IOException e) {
                    // TODO Auto-generated catch block
                    e.printStackTrace();
                }
            }
            if(out!=null)
            {
                try {
                    out.close();
                } catch (IOException e) {
                    // TODO Auto-generated catch block
                    e.printStackTrace();
                }
            }
        }

        // 清空response
        response.reset();
        // 4. 以流的形式下载文件。
        try (InputStream fis = new BufferedInputStream(new FileInputStream(zipFile.getPath()));
             OutputStream toClient = new BufferedOutputStream(response.getOutputStream())
        ){
            byte[] buffer = new byte[fis.available()];
            fis.read(buffer);
            response.setContentType("application/octet-stream");
            // 如果输出的是中文名的文件，在此处就要用URLEncoder.encode方法进行处理
            response.setHeader("Content-Disposition","attachment;filename="+zipFile.getName());
            toClient.write(buffer);
            toClient.flush();
        } catch (Exception e) {
            // TODO: handle exception
        } finally {
            FileUtils.deleteQuietly(dir);
        }
    }
    @Override
    public BaseResponse batchDeliverDraft(BatchDeliverReportReq batchDeliverReportReq) {
        Assert.isTrue(Func.isNotEmpty(batchDeliverReportReq), "param.miss", null);
        List<DeliverReportReq> reportDataList = batchDeliverReportReq.getReportData();
        Assert.isTrue(Func.isNotEmpty(reportDataList), "param.miss.placeholder", new Object[]{"Report Data"});
        UserInfo userInfoFillSystem = SystemContextHolder.getUserInfoFillSystem();

        // 定义常量
        final String OBJECT_TYPE = "batchSendDraftMail";
        final String OPERATION_TYPE = "BatchDeliverDraft SendEmail";

        // 格式化后邮箱地址
        List<String> formattedTo = formatEmailAddress(batchDeliverReportReq.getTo());
        List<String> formattedCc = formatEmailAddress(batchDeliverReportReq.getCc());
        List<String> formattedBcc = formatEmailAddress(batchDeliverReportReq.getBcc());
        UserInfo userInfo = SystemContextHolder.getUserInfoFillSystem();
        reportDataList.stream().forEach(deliverReportReq -> taskExecutor.execute(() -> {
            try {
                // 检查 reportId 是否为空
                if (deliverReportReq.getReportId() == null || deliverReportReq.getReportId().isEmpty()) {
                    throw new IllegalArgumentException("ReportId cannot be null or empty");
                }
                EmailAutoSendDTO emailAutoSendDTO = new EmailAutoSendDTO();
                emailAutoSendDTO.setTemplateType(EmailTemplateTypeEnums.DRAFT_REPORT.getCode());
                emailAutoSendDTO.setMailSubject(deliverReportReq.getSubject());
                emailAutoSendDTO.setTemplateId(batchDeliverReportReq.getTemplateId());
                emailAutoSendDTO.setMailTo(formattedTo);
                emailAutoSendDTO.setMailCc(formattedCc);
                emailAutoSendDTO.setMailBcc(formattedBcc);
                emailAutoSendDTO.setLabCode(batchDeliverReportReq.getLab());
                List<EmailAttachmentDTO> emailAttachmentList = deliverReportReq.getEmailAttachmentList();
                if(Func.isNotEmpty(emailAttachmentList)){
                    emailAutoSendDTO.setOssFiles(emailAttachmentList);
                }
                Map<String, Object> templateVariables = new HashMap<>();
                String reportId_base64 = Base64.getEncoder().encodeToString(deliverReportReq.getReportId().getBytes(StandardCharsets.UTF_8));
                buildApproveReportUrls(reportId_base64, batchDeliverReportReq.getBuCode(), batchDeliverReportReq.getTo(), templateVariables,false);
                templateVariables.put("EmailSender", Func.toStr(userInfo.getEmail()));
                emailAutoSendDTO.setTemplateVariables(templateVariables);

                SystemLog systemLog = new SystemLog();
                systemLog.setObjectType(OBJECT_TYPE);
                systemLog.setObjectNo(deliverReportReq.getReportNo());
                systemLog.setProductLineCode(batchDeliverReportReq.getBuCode());
                systemLog.setType(SystemLogType.SYSTEM.getType());
                systemLog.setRemark(OPERATION_TYPE);
                systemLog.setCreateBy(userInfoFillSystem.getRegionAccount());
                systemLog.setOperationType(OPERATION_TYPE);
                sendEmailService.sendEmail(emailAutoSendDTO, systemLog);
            } catch (Exception e) {
                log.error("Failed to send email for reportNo: {}", deliverReportReq.getReportNo(), e);
            }
        }));

        return BaseResponse.newSuccessInstance(true);
    }


    private List<String> formatEmailAddress(String address){
        if(Func.isEmpty(address)){
            return new ArrayList<>();
        }
        List<String> addressList = Func.toStrList(StringPool.SEMICOLON,address)
                .stream().filter(Func::isNotEmpty)
                .distinct().collect(Collectors.toList());
        return  addressList;
    }

    private String getOverallDescriptionBySetting(String conclusionSettingId,List<GetCustomerConclusionRsp> customerConclusionRspList,String reportLanguage){
        String overallDescription = StringPool.EMPTY;
        List<GetCustomerConclusionRsp.ConclusionSettings> conclusionSettings = null;
        if(Func.isNotEmpty(customerConclusionRspList)){
            conclusionSettings = customerConclusionRspList.get(0).getConclusionSettings();
            if(Func.isNotEmpty(conclusionSettings)){
                GetCustomerConclusionRsp.ConclusionSettings conclusionSetting = conclusionSettings.stream().filter(i -> Func.equals(i.getConclusionSettingId(), conclusionSettingId)).findAny().orElse(null);
                if(Func.isNotEmpty(conclusionSetting)){
                    String overallDescriptionEn = conclusionSetting.getOverallDesc();
                    String overallDescriptionCn = null;
                    if (Func.isNotEmpty(conclusionSetting.getLanguages())){
                        overallDescriptionCn = conclusionSetting.getLanguages().get(0).getOverallDesc();
                    }
                    if (Func.equals(ReportLanguage.EnglishAndChineseReport.getCode(),reportLanguage)){
                        overallDescription = overallDescriptionEn + " " + (Func.isEmpty(overallDescriptionCn)?StringPool.EMPTY:overallDescriptionCn);
                    }else if (Func.equals(ReportLanguage.ChineseReportOnly.getCode(),reportLanguage)){
                        overallDescription = Func.isEmpty(overallDescriptionCn)?overallDescriptionEn:overallDescriptionCn;
                    }else{
                        overallDescription = overallDescriptionEn;
                    }
                }
            }
        }
        return overallDescription;
    }

}
