package com.sgs.otsnotes.domain.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.sgs.core.domain.UserInfo;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.core.base.CustomResult;
import com.sgs.framework.log.model.SystemLog;
import com.sgs.framework.model.enums.ReportLanguage;
import com.sgs.framework.model.enums.TestLineType;
import com.sgs.framework.tool.utils.Func;
import com.sgs.framework.tool.utils.ProductLineContextHolder;
import com.sgs.gpo.facade.model.otsnotes.testline.req.LabSectionReq;
import com.sgs.gpo.facade.model.otsnotes.testline.req.LabSectionUpdateItemReq;
import com.sgs.gpo.facade.model.otsnotes.testline.req.LabSectionUpdateReq;
import com.sgs.grus.bizlog.BizLogClient;
import com.sgs.grus.bizlog.info.BizLogInfo;
import com.sgs.otsnotes.core.constants.BizLogConstant;
import com.sgs.otsnotes.core.constants.Constants;
import com.sgs.otsnotes.core.util.DateUtils;
import com.sgs.otsnotes.core.util.LOStringUtil;
import com.sgs.otsnotes.core.util.NumberUtil;
import com.sgs.otsnotes.dbstorages.mybatis.extmapper.*;
import com.sgs.otsnotes.dbstorages.mybatis.extmodel.masterlist.JobPO;
import com.sgs.otsnotes.dbstorages.mybatis.mapper.AnalyteLanguageInfoMapper;
import com.sgs.otsnotes.dbstorages.mybatis.mapper.UnitLanguageInfoMapper;
import com.sgs.otsnotes.dbstorages.mybatis.model.*;
import com.sgs.otsnotes.domain.service.gpn.sendEmail.ISendEmailService;
import com.sgs.otsnotes.domain.service.lims.statusLog.IStatusLogService;
import com.sgs.otsnotes.facade.model.dto.EmailAutoSendDTO;
import com.sgs.otsnotes.facade.model.dto.OrderLanguageDTO;
import com.sgs.otsnotes.facade.model.dto.TestLineStatusDTO;
import com.sgs.otsnotes.facade.model.enums.*;
import com.sgs.otsnotes.facade.model.info.labteam.LabTeamSimpleInfo;
import com.sgs.otsnotes.facade.model.info.labteam.QueryLabTeamInfoReq;
import com.sgs.otsnotes.facade.model.info.labteam.QueryLabTeamInfoRsp;
import com.sgs.otsnotes.facade.model.info.trims.LabSectionBaseInfo;
import com.sgs.otsnotes.facade.model.req.statusLog.StatusLogReq;
import com.sgs.otsnotes.facade.model.req.testLine.*;
import com.sgs.otsnotes.facade.model.rsp.testLine.TestLineEditDetailRsp;
import com.sgs.otsnotes.integration.OrderClient;
import com.sgs.otsnotes.integration.TokenClient;
import com.sgs.otsnotes.integration.UserManagementClient;
import com.sgs.otsnotes.integration.gpo.GPOTestLineTempClient;
import com.sgs.preorder.facade.OperationHistoryFacade;
import com.sgs.preorder.facade.OrderFacade;
import com.sgs.preorder.facade.model.dto.order.OrderAllDTO;
import com.sgs.preorder.facade.model.dto.order.OrderInfoDto;
import com.sgs.preorder.facade.model.enums.FullCyclePendingFlag;
import com.sgs.preorder.facade.model.enums.OperationTypeEnums;
import com.sgs.preorder.facade.model.req.OrderIdReq;
import com.sgs.preorder.facade.model.req.UpdateFullCyclePendingFlagReq;
import com.sgs.preorder.facade.model.req.operationHistory.InsertOperationHistoryReq;
import com.sgs.trimslocal.facade.IAnalyteFacade;
import com.sgs.trimslocal.facade.model.analyte.req.QueryTestLineAnalyteReq;
import com.sgs.trimslocal.facade.model.analyte.req.TestLineAnalyteReq;
import com.sgs.trimslocal.facade.model.analyte.rsp.AnalyteUnitLangRsp;
import com.sgs.trimslocal.facade.model.analyte.rsp.TestAnalyteLangReq;
import com.sgs.trimslocal.facade.model.analyte.rsp.TestLineAnalyteRsp;
import com.sgs.trimslocal.facade.model.analyte.rsp.TestLineAnalyteUnitRsp;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import java.awt.*;
import java.util.*;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 修改TL Service
 * <AUTHOR>
 * @date 2021/1/4 13:43
 */
@Slf4j
@Service
public class TestLineCmdService {

    @Autowired
    private TestLineService testLineService;

    @Autowired
    private TestConditionService testConditionService;

    @Autowired
    private TestLineMapper testLineMapper;

    @Autowired
    private TokenClient tokenClient;
    @Autowired
    private TransactionTemplate transactionTemplate;
    @Autowired
    private AnalyteMapper analyteMapper;
    @Autowired
    private AnalyteLanguageMapper analyteLanguageMapper;
    @Autowired
    private OrderLanguageRelMapper orderLanguageRelMapper;
    @Autowired
    private AnalyteLanguageInfoMapper trimsAnalyteLanguageInfoMapper;
    @Autowired
    private UnitLanguageInfoMapper trimsUnitLanguageInfoMapper;
    @Autowired
    private OrderMapper orderMapper;
    @Autowired
    private BizLogClient bizLogClient;
    @Autowired
    private IAnalyteFacade analyteFacade;
    @Autowired
    private OrderFacade orderFacade;
    @Autowired
    private SubContractExtMapper subContractExtMapper;
    @Autowired
    private JobExtMapper jobExtMapper;
    @Autowired
    private OrderClient orderClient;
    @Autowired
    private IStatusLogService statusLogService;
    @Autowired
    private GPOTestLineTempClient gpoTestLineTempClient;
    @Autowired
    private OperationHistoryFacade operationHistoryFacade;
    @Autowired
    private UserManagementClient userManagementClient;
    @Autowired
    private ISendEmailService sendEmailService;
    /**
     * 保存TL信息
     * @param saveTestLineDetailReq
     */
    public CustomResult<Boolean> saveTestLineDetail(SaveTestLineDetailReq saveTestLineDetailReq) {
        log.info("SaveTestLineDetail,Req:{}",Func.isEmpty(saveTestLineDetailReq)?"":Func.toJson(saveTestLineDetailReq));
        CustomResult<Boolean> customResult = new CustomResult<>();
        //TL查询
        String testLineInstanceId = saveTestLineDetailReq.getTestLineInstanceId();
        TestLineEditDetailRsp oldTestLine = testLineMapper.getEditTestLineInfoById(testLineInstanceId);
        if(Func.isEmpty(oldTestLine)){
            customResult.setSuccess(false);
            customResult.setMsg("TL不存在，请刷新后重新操作！");
            return customResult;
        }
        OrderInfoDto orderInfoDto = orderClient.getOrderInfoByOrderNo(oldTestLine.getOrderNo());
        if (Func.isEmpty(orderInfoDto)){
            customResult.setSuccess(false);
            customResult.setMsg("订单信息查询失败！");
            return customResult;
        }
        if (!Func.equals(orderInfoDto.getBUCode(),ProductLineContextHolder.getProductLineCode())){
            customResult.setSuccess(false);
            customResult.setMsg("当前BU不允许操作当前订单！");
            return customResult;
        }
        // PP TL 不校验Standard是否为空
        if ((Func.isEmpty(oldTestLine.getAftifactIds()) || oldTestLine.getAftifactIds().get(0)==0 )  &&(saveTestLineDetailReq.getSaveTestStandardReq() == null || CollectionUtils.isEmpty(saveTestLineDetailReq.getSaveTestStandardReq().getStandards()))){
            customResult.setSuccess(false);
            customResult.setMsg("Standard参数不能为空！");
            return customResult;
        }

        for (StandardReq standard : saveTestLineDetailReq.getSaveTestStandardReq().getStandards()) {
            standard.setTestLineInstanceId(saveTestLineDetailReq.getTestLineInstanceId());
        }

        //更新tl的standard
        if (Func.isEmpty(oldTestLine.getAftifactIds()) || oldTestLine.getAftifactIds().get(0)==0 ) {
            customResult = testLineService.updateStandard(saveTestLineDetailReq.getSaveTestStandardReq());
            if (! customResult.isSuccess()) {
                return customResult;
            }
        }

        //
//        if(Func.isNotEmpty(oldTestLine) && !Func.equalsSafe(oldTestLine.getLabSectionBaseId(),saveTestLineDetailReq.getLabSectionBaseId())){
//            CustomResult<String> checkLabSectionRes = this.checkLabSection(testLineInstanceId);
//            if(Func.isNotEmpty(checkLabSectionRes)&&Func.isNotEmpty(checkLabSectionRes.getData())){
//                customResult.setSuccess(false);
//                customResult.setMsg(checkLabSectionRes.getData());
//                return customResult;
//            }
//        }
        // TODO-Trevor.Yuan labSection 2023/8/25 保存TL增加labSectionId传参
        if(Func.isNotEmpty(saveTestLineDetailReq.getSaveLabSectionList())) {
            LabSectionUpdateReq labSectionUpdateReq = new LabSectionUpdateReq();
            labSectionUpdateReq.setOrderId(oldTestLine.getOrderInstanceId());
            List<LabSectionUpdateItemReq> testLineLabSectionList = Lists.newArrayList();
            LabSectionUpdateItemReq testLineLabSectionReq = new LabSectionUpdateItemReq();
            testLineLabSectionReq.setTestLineInstanceId(testLineInstanceId);
//            testLineLabSectionReq.setLabSectionBaseIdList(saveTestLineDetailReq.getLabSectionBaseIdList());
            // TODO-Trevor.Yuan labSection 2023/8/25 保存TL增加labSectionId传参
            List<LabSectionReq> labSectionList = new ArrayList<>();
            for (LabSectionBaseInfo labSectionBaseInfo : saveTestLineDetailReq.getSaveLabSectionList()) {
                LabSectionReq labSectionReq  = new LabSectionReq();
                labSectionReq.setLabSectionId(labSectionBaseInfo.getLabSectionId());
                labSectionReq.setLabSectionBaseId(labSectionBaseInfo.getLabSectionBaseId());
                labSectionList.add(labSectionReq);
            }
            testLineLabSectionReq.setLabSectionList(labSectionList);
            testLineLabSectionList.add(testLineLabSectionReq);
            labSectionUpdateReq.setTestLineLabSectionList(testLineLabSectionList);
            BaseResponse updateLabSectionRsp = gpoTestLineTempClient.updateLabSection(labSectionUpdateReq);
            if(updateLabSectionRsp.isFail()){
                customResult.setSuccess(false);
                customResult.setMsg(updateLabSectionRsp.getMessage());
                return customResult;
            }
        }

        //保存condition ，和update condition的接口一个方法 保持逻辑一致
        if (saveTestLineDetailReq.getSaveConditionReq() != null) {
            saveTestLineDetailReq.getSaveConditionReq().setTestLineInstanceId(saveTestLineDetailReq.getTestLineInstanceId());
            if (CollectionUtils.isNotEmpty(saveTestLineDetailReq.getSaveConditionReq().getConditionList())) {
                customResult = testConditionService.saveCondition(saveTestLineDetailReq.getSaveConditionReq());
                if (!customResult.isSuccess()) {
                    return customResult;
                }
            }
        }

        String regionAccount = tokenClient.getUser().getRegionAccount();

        //获取 ，装analyte instance 对象
        List<SaveAnalyteReq> saveAnalyteReq = saveTestLineDetailReq.getSaveAnalyteReq();

        GeneralOrderInstanceInfoPO order = orderMapper.getOrderByTestLineInstanceId(testLineInstanceId);
        // 查询订单报告语言，如果是单语言报告，需要同时更新对应CN/EN报告
        String reportLanguage ="";
        OrderIdReq orderIdReq = new OrderIdReq();
        orderIdReq.setOrderNo(order.getOrderNo());
        BaseResponse<OrderAllDTO> orderRes =  orderFacade.getOrderForPe(orderIdReq);
        if(Func.isNotEmpty(orderRes)&&Func.isNotEmpty(orderRes.getData())){
            reportLanguage = orderRes.getData().getReportLanguage();
        }

        List<AnalyteMultipleLanguageInfoPO> saveAnalyteLanguages = Lists.newArrayList();
        List<OrderLanguageDTO> delDtoList = Lists.newArrayList();
        List<OrderLanguageRelInfoPO> saveDtoList = Lists.newArrayList();
        //获取需要保存的analyte对象
        List<AnalyteInfoPO> analyteSaveList = this.getSaveAnalyteInstance(regionAccount,saveAnalyteReq,testLineInstanceId,saveAnalyteLanguages,delDtoList,saveDtoList);
        for (OrderLanguageDTO orderLanguageDTO : delDtoList) {
            orderLanguageDTO.setOrderId(order.getID());
        }
        for (OrderLanguageRelInfoPO orderLanguageRelInfoPO : saveDtoList) {
            orderLanguageRelInfoPO.setOrderId(order.getID());
        }
        TestLineInstancePO testLineInstancePO = new TestLineInstancePO();
        testLineInstancePO.setID(saveTestLineDetailReq.getTestLineInstanceId());
        testLineInstancePO.setOrdertestLineRemark(saveTestLineDetailReq.getOrdertestLineRemark());
        testLineInstancePO.setSampleSegegrationWIID(saveTestLineDetailReq.getSampleSegegrationWIID());
        testLineInstancePO.setSampleSegegrationWIText(saveTestLineDetailReq.getSampleSegegrationWIText());
        testLineInstancePO.setModifiedBy(regionAccount);
        testLineInstancePO.setModifiedDate(DateUtils.getNow());
        testLineInstancePO.setLabTeamCode(saveTestLineDetailReq.getLabTeamCode());
        testLineInstancePO.setClientStandard(saveTestLineDetailReq.getClientStandard());
        String customerTestLineNameCN = saveTestLineDetailReq.getCustomerTestLineNameCN();
        String customerTestLineName = saveTestLineDetailReq.getCustomerTestLineName();
        if(Func.equalsSafe(reportLanguage, ReportLanguage.EnglishReportOnly.getCode())){
            customerTestLineNameCN = customerTestLineName;
        }
        if(Func.equalsSafe(reportLanguage,ReportLanguage.ChineseReportOnly.getCode())){
            customerTestLineName = customerTestLineNameCN;
        }
        testLineInstancePO.setCustomerTestLineNameCN(customerTestLineNameCN);
        testLineInstancePO.setCustomerTestLineName(customerTestLineName);
        testLineInstancePO.setEngineer(oldTestLine.getEngineer());

        if(Func.isNotEmpty(saveTestLineDetailReq.getLabTeamCode()) && Func.isEmpty(oldTestLine.getEngineer())){
//            调用接口查询是否有engineer
            QueryLabTeamInfoReq queryLabTeamInfoReq = new QueryLabTeamInfoReq();
            queryLabTeamInfoReq.setLabCode(orderRes.getData().getLabDTO().getLabCode());
            queryLabTeamInfoReq.setLabTeamCodeList(Lists.newArrayList(saveTestLineDetailReq.getLabTeamCode()));
            QueryLabTeamInfoRsp queryLabTeamInfoRsp = userManagementClient.queryLabTeamInfo(queryLabTeamInfoReq);
            if(Func.isNotEmpty(queryLabTeamInfoRsp) && Func.isNotEmpty(queryLabTeamInfoRsp.getTeamList())){
                List<LabTeamSimpleInfo> teamList = queryLabTeamInfoRsp.getTeamList();
                List<String> engineers = teamList.get(0).getEngineers();
                if(Func.isNotEmpty(engineers)){
                    engineers = engineers.stream().filter(Func::isNotEmpty).distinct().collect(Collectors.toList());
                    if(Func.isNotEmpty(engineers) && engineers.size()==1){
                        testLineInstancePO.setEngineer(engineers.get(0));
                    }
                }
            }
        }
        transactionTemplate.execute((tranStatus)->{
            int i = testLineMapper.updateTestLineInstanceDetail(testLineInstancePO);
            if(i<0){
                tranStatus.setRollbackOnly();
            }
            //同时删除analyte 和analyte 多语言
            i = analyteMapper.deleteByTestLineInstance(testLineInstanceId);
            if(i<0){
                tranStatus.setRollbackOnly();
            }
            if(CollectionUtils.isNotEmpty(analyteSaveList)){
                i = analyteMapper.batchInsert(analyteSaveList);
            }
            if(i<0){
                tranStatus.setRollbackOnly();
            }
            if(CollectionUtils.isNotEmpty(saveAnalyteLanguages)){
                i = analyteLanguageMapper.batchInsert(saveAnalyteLanguages);
            }
            if(i<0){
                tranStatus.setRollbackOnly();
            }
            if(CollectionUtils.isNotEmpty(delDtoList)){
                i = orderLanguageRelMapper.batchDelLangRels(delDtoList);
            }
            if(i<0){
                tranStatus.setRollbackOnly();
            }
            if(CollectionUtils.isNotEmpty(saveDtoList)){
                i = orderLanguageRelMapper.batchInsertOrUpdate(saveDtoList);
            }
            if(i<0){
                tranStatus.setRollbackOnly();
            }
            return i;
        });

        customResult.setSuccess(true);
        return customResult;
    }

    public CustomResult<String> checkLabSection(String testLineInstanceId){
        CustomResult<String> customResult = new CustomResult<>();
        String checkResult = null;
        // 查询TL 是否存在对应的Subcontract
        SubContractPO subContractPO = subContractExtMapper.querySubContractByTestLineInstanceId(testLineInstanceId);
        if(Func.isNotEmpty(subContractPO) && !SubContractStatus.check(subContractPO.getStatus(),SubContractStatus.CANCELLED)){
            checkResult = "关联的分包单："+subContractPO.getSubContractNo()+",无法修改LabSection";
        }
        // 查询TL 是否存在对应的Job
        JobPO jobPO = jobExtMapper.queryJobByTestLineInstanceId(testLineInstanceId);
        if(Func.isNotEmpty(jobPO) && !JobStatus.check(jobPO.getJobStatus(),JobStatus.New,JobStatus.Testing,JobStatus.Cancelled)){
            checkResult = "关联的Job："+jobPO.getJobNo()+",状态["+JobStatus.getJobStatus(jobPO.getJobStatus())+"],无法修改LabSection!";
        }
        // 关联的Job状态为New、Testing、Cancelled时允许编辑LabSection
        customResult.setData(checkResult);
        return customResult;
    }

    /**
     *
     * @param regionAccount
     * @param pageSaveAnalyteReq
     * @param testLineInstanceId
     * @return
     */
    public List<AnalyteInfoPO> getSaveAnalyteInstance(String regionAccount, List<SaveAnalyteReq> pageSaveAnalyteReq ,String testLineInstanceId,
                                                   List<AnalyteMultipleLanguageInfoPO> saveAnalyteLanguages,List<OrderLanguageDTO> delDtoList,
                                                       List<OrderLanguageRelInfoPO> saveDtoList) {
        List<AnalyteInfoPO> result = Lists.newArrayList();
        /*if(CollectionUtils.isEmpty(pageSaveAnalyteReq)){
            //return result;
        }*/
        TestLineEditDetailRsp detailRsp = testLineMapper.getEditTestLineInfoById(testLineInstanceId);
        //存在ppversionId的话，需要走多次查询，和confirm matrix 一样 以pp为维度
        List<Integer> aftifactIds = detailRsp.getAftifactIds();
        // 存在ppversionId的话，需要走多次查询，和confirm matrix 一样 以pp为维度
        if(aftifactIds == null || aftifactIds.isEmpty()){
            aftifactIds = Lists.newArrayList(0);
        }
        //调用localTrims接口获取analyte信息
        QueryTestLineAnalyteReq queryTestLineAnalyteReq = new QueryTestLineAnalyteReq();
        List<TestLineAnalyteReq> analytes = Lists.newArrayList();
        TestLineAnalyteReq testLineAnalyteReq = new TestLineAnalyteReq();
        testLineAnalyteReq.setTestLineVersionId(detailRsp.getTestLineVersionID());
        Set<Integer> standardVersionIds = com.beust.jcommander.internal.Sets.newHashSet();
        standardVersionIds.add(detailRsp.getStandardVersionId());
        testLineAnalyteReq.setStandardVersionIds(standardVersionIds);
        if(Func.isNotEmpty(aftifactIds)){
            Integer aid =aftifactIds.stream().filter(p->Func.isNotEmpty(p)).findFirst().orElse(null);
            testLineAnalyteReq.setArtifactId(Func.isEmpty(aid)?null:Long.valueOf(aid));
        }
        analytes.add(testLineAnalyteReq);
        queryTestLineAnalyteReq.setAnalytes(analytes);
        queryTestLineAnalyteReq.setCallerBU(ProductLineContextHolder.getProductLineCode());
        com.sgs.trimslocal.facade.model.common.BaseResponse<List<TestLineAnalyteRsp>>  testLineAnalyteRsp = analyteFacade.getTestLineAnalyteList(queryTestLineAnalyteReq);
        List<TestLineAnalyteRsp> testLineAnalyteList = testLineAnalyteRsp.getData();


        if(CollectionUtils.isEmpty(testLineAnalyteList)){
            log.info("OrderNo:{}_TestLine:{}_ EditTLsave,获取analyte为null,param:{}",detailRsp.getOrderNo(),detailRsp.getTestLineInstanceId(), JSONObject.toJSONString(queryTestLineAnalyteReq));
            return result;
        }

//        List<Analyte> testAnalyteItems = analyteList.get(0).getTestAnalyteItems();

        OrderLanguageDTO delAnalyteDto = new OrderLanguageDTO();
        delAnalyteDto.setObjectBaseIds(Sets.newHashSet());
        delAnalyteDto.setLangType(OrderLanguageTypeEnums.Analyte.getType());
        OrderLanguageDTO delUnitDto = new OrderLanguageDTO();
        delUnitDto.setObjectBaseIds(Sets.newHashSet());
        delUnitDto.setLangType(OrderLanguageTypeEnums.AnalyteUnit.getType());
        testLineAnalyteList.stream().forEach(analyte->{
            Long analyteBaseId = analyte.getAnalyteBaseId();
            delAnalyteDto.getObjectBaseIds().add(analyteBaseId);
            List<TestLineAnalyteUnitRsp> mutipleUnit = analyte.getUnits();
            if(CollectionUtils.isNotEmpty(mutipleUnit)){
                Set<Long> collect = mutipleUnit.stream().map(an -> an.getUnitBaseId() == null ? 0 : an.getUnitBaseId()).collect(Collectors.toSet());
                delUnitDto.getObjectBaseIds().addAll(collect);
            }
        });
        delDtoList.add(delAnalyteDto);
        delDtoList.add(delUnitDto);

        //过滤掉 selectionType=1的analyte
        //DIG-5779 g 本次修改需要保存=1的数据
        List<TestLineAnalyteRsp> canSaveAnalyte = testLineAnalyteList;
        //页面需要保存的，根据baseId 再过滤出真正需要入库的
        //通过baseId 查询先关unit 等数据
        List<Integer> pageSaveAnalyteIds = pageSaveAnalyteReq.stream().map(pageA -> pageA.getTestAnalyteId()).collect(Collectors.toList());
        //取交集，获取需要保存的analyte
        //DIG-5779 g 本次修改需要保存=1的数据
        canSaveAnalyte = canSaveAnalyte.stream().filter(db->AnalyteSectionTypeEnums.check(db.getSelectionType(), AnalyteSectionTypeEnums.Mandatory)
                || pageSaveAnalyteIds.contains(db.getTestAnalyteId())).collect(Collectors.toList());
        AnalyteInfoPO analytePO = null;
        List<Long> analyteBaseIdList = Lists.newArrayList();
        List<Long> unitBaseIdList = Lists.newArrayList();
        //这里处理对象有点繁琐，analyte 下面有多语言，然后analyte下的unit还有多语言，就得循环创建对象
        Set<String> repeatUnit = Sets.newHashSet();
        for (TestLineAnalyteRsp analyte : canSaveAnalyte) {
            analyteBaseIdList.add(analyte.getAnalyteBaseId());

            Integer testAnalyteId = analyte.getTestAnalyteId();
            List<TestLineAnalyteUnitRsp> mutipleUnit = analyte.getUnits();
            if(CollectionUtils.isNotEmpty(mutipleUnit)){
                mutipleUnit: for (TestLineAnalyteUnitRsp unit : mutipleUnit) {
                    Long unitBaseId = unit.getUnitBaseId();
                    String key = String.format("%s_%s",testAnalyteId,unitBaseId);
                    if(repeatUnit.contains(key)){
                        continue ;
                    }
                    unitBaseIdList.add(unitBaseId);
                    repeatUnit.add(key);
                    analytePO = new AnalyteInfoPO();
                    String analyteInstanceId = UUID.randomUUID().toString();
                    analytePO.setID(analyteInstanceId);
                    analytePO.setActiveIndicator(true);
                    analytePO.setCreatedBy(regionAccount);
                    analytePO.setCreatedDate(DateUtils.getNow());
                    analytePO.setModifiedBy(regionAccount);
                    analytePO.setModifiedDate(DateUtils.getNow());

                    analytePO.setUnitBaseId(unitBaseId);
                    analytePO.setGeneralOrderInstanceID(detailRsp.getOrderInstanceId());
                    analytePO.setAnalyteBaseId(analyte.getAnalyteBaseId());
                    analytePO.setTestLineInstanceID(testLineInstanceId);
                    analytePO.setAnalyteID(analyte.getTestAnalyteId());
                    analytePO.setTestAnalyteName(LOStringUtil.delHTMLTag(analyte.getTestAnalyteDesc()));
                    analytePO.setCasNo(analyte.getTestAnalyteCasNumber());
                    analytePO.setTestAnalyteSeq(analyte.getTestAnalyteSeq());
                    //设置unit相关

                    analytePO.setReportUnit(LOStringUtil.delHTMLTag(unit.getUnitShortDepiction()));
                    result.add(analytePO);
                    List<TestAnalyteLangReq> otherLanguageItems =analyte.getLanguages();
                    if(CollectionUtils.isEmpty(otherLanguageItems)){
                        continue mutipleUnit;
                    }
                    otherLanguageItems.forEach(anO->{
                        String multiTestAnalyteDesc = anO.getTestAnalyteDesc();
                        AnalyteUnitLangRsp unitReportLan = unit.getLanguage();
                        AnalyteMultipleLanguageInfoPO po = new AnalyteMultipleLanguageInfoPO();
                        po.setID(UUID.randomUUID().toString());
                        po.setAnalyteInstanceID(analyteInstanceId);
                        po.setLanguageId(anO.getLanguageId());
                        po.setTestAnalyteName(LOStringUtil.delHTMLTag(multiTestAnalyteDesc));
                        po.setCreatedBy(regionAccount);
                        po.setModifiedBy(regionAccount);
                        po.setCreatedDate(DateUtils.getNow());
                        po.setModifiedDate(DateUtils.getNow());
                        if(Func.isNotEmpty(unitReportLan)){
                            String multiUnitShortDepiction = unitReportLan.getUnitShortDepiction();
                            po.setReportUnit(multiUnitShortDepiction);
                            saveAnalyteLanguages.add(po);
                        }else{
                            saveAnalyteLanguages.add(po);
                        }
                    });

                }
            }else{
                analytePO = new AnalyteInfoPO();
                String analyteInstanceId = UUID.randomUUID().toString();
                analytePO.setID(analyteInstanceId);
                analytePO.setActiveIndicator(true);
                analytePO.setCreatedBy(regionAccount);
                analytePO.setCreatedDate(DateUtils.getNow());
                analytePO.setModifiedBy(regionAccount);
                analytePO.setModifiedDate(DateUtils.getNow());

                analytePO.setGeneralOrderInstanceID(detailRsp.getOrderInstanceId());
                analytePO.setAnalyteBaseId(analyte.getAnalyteBaseId());
                analytePO.setTestLineInstanceID(testLineInstanceId);
                analytePO.setAnalyteID(analyte.getTestAnalyteId());
                analytePO.setTestAnalyteName(LOStringUtil.delHTMLTag(analyte.getTestAnalyteDesc()));
                analytePO.setCasNo(analyte.getTestAnalyteCasNumber());
                analytePO.setTestAnalyteSeq(analyte.getTestAnalyteSeq());
                result.add(analytePO);


                List<TestAnalyteLangReq> otherLanguageItems = analyte.getLanguages();
                if(CollectionUtils.isEmpty(otherLanguageItems)){
                    continue;
                }
                otherLanguageItems.forEach(anO->{
                    String multiTestAnalyteDesc = anO.getTestAnalyteDesc();
                    AnalyteMultipleLanguageInfoPO po = new AnalyteMultipleLanguageInfoPO();
                    po.setID(UUID.randomUUID().toString());
                    po.setAnalyteInstanceID(analyteInstanceId);
                    po.setLanguageId(anO.getLanguageId());
                    po.setTestAnalyteName(LOStringUtil.delHTMLTag(multiTestAnalyteDesc));
                    po.setCreatedBy(regionAccount);
                    po.setModifiedBy(regionAccount);
                    po.setCreatedDate(DateUtils.getNow());
                    po.setModifiedDate(DateUtils.getNow());
                    saveAnalyteLanguages.add(po);
                });
            }
        }

        //查找响应的多语言数据，可以进行数据保存
        List<AnalyteLanguageInfoPO> trimsAnalyteLanguageList = Lists.newArrayList();
        if(CollectionUtils.isNotEmpty(analyteBaseIdList)){
            AnalyteLanguageInfoExample analyteLanguageInfoExample = new AnalyteLanguageInfoExample();
            analyteLanguageInfoExample.createCriteria().andLangStatusEqualTo(1).andAnalyteBaseIdIn(analyteBaseIdList);
            trimsAnalyteLanguageList = trimsAnalyteLanguageInfoMapper.selectByExample(analyteLanguageInfoExample);
        }

        List<UnitLanguageInfoPO> trimsUnitLanguageList = Lists.newArrayList();
        if(CollectionUtils.isNotEmpty(unitBaseIdList)){
            UnitLanguageInfoExample unitLanguageInfoExample = new UnitLanguageInfoExample();
            unitLanguageInfoExample.createCriteria().andLangStatusEqualTo(1).andUnitBaseIdIn(unitBaseIdList);
            trimsUnitLanguageList = trimsUnitLanguageInfoMapper.selectByExample(unitLanguageInfoExample);
        }

        List<OrderLanguageRelInfoPO> saveOrderLanguageList = this.createOrderLanguageRelationshipPO( trimsAnalyteLanguageList,trimsUnitLanguageList);
        saveDtoList.addAll(saveOrderLanguageList);
        return result;
    }

    private List<OrderLanguageRelInfoPO> createOrderLanguageRelationshipPO(List<AnalyteLanguageInfoPO> trimsAnalyteLanguageList,
                                                                           List<UnitLanguageInfoPO> trimsUnitLanguageList) {
        List<OrderLanguageRelInfoPO> list = Lists.newArrayList();
        for (AnalyteLanguageInfoPO po : trimsAnalyteLanguageList) {
            OrderLanguageRelInfoPO relInfoPO = new OrderLanguageRelInfoPO();
            relInfoPO.setObjectBaseId(po.getAnalyteBaseId());
            relInfoPO.setLangBaseId(po.getLangId());
            relInfoPO.setLangType(OrderLanguageTypeEnums.Analyte.getType());
            relInfoPO.setLanguageId(po.getLanguageId());
            relInfoPO.setLangStatus(1);
            relInfoPO.setCreatedDate(DateUtils.getNow());
            relInfoPO.setModifiedDate(DateUtils.getNow());
            list.add(relInfoPO);
        }
        for (UnitLanguageInfoPO po : trimsUnitLanguageList) {
            OrderLanguageRelInfoPO relInfoPO = new OrderLanguageRelInfoPO();
            relInfoPO.setObjectBaseId(po.getUnitBaseId());
            relInfoPO.setLangBaseId(po.getLangId());
            relInfoPO.setLangType(OrderLanguageTypeEnums.AnalyteUnit.getType());
            relInfoPO.setLanguageId(po.getLanguageId());
            relInfoPO.setLangStatus(1);
            relInfoPO.setCreatedDate(DateUtils.getNow());
            relInfoPO.setModifiedDate(DateUtils.getNow());
            list.add(relInfoPO);
        }
        return list;
    }

    /**
     * 更新ReportSeq
     * @param updateTestLineReportSeq
     * @return
     */
    public CustomResult<Boolean> updateTestLineReportSeq(UpdateTestLineReportSeq updateTestLineReportSeq){
        updateTestLineReportSeq.setModifiedBy(tokenClient.getUser().getRegionAccount());
        updateTestLineReportSeq.setModifiedDate(DateUtils.getNow());
        int i = testLineMapper.updateTestLineReportSeq(updateTestLineReportSeq);
        CustomResult<Boolean> customResult = new CustomResult<>();
        customResult.setSuccess(i>0);
        return customResult;
    }

    public CustomResult updatePendingFlag(UpdateTestLinePendingFlagReq req) {
        CustomResult<Object> result = new CustomResult<>();
        List<TestLineInstancePO> tlList = testLineMapper.selectTestLineStatusByIds(req.getTestLineInstanceIds());
        //过滤出已经是pending 或者unpending的数据，不再进行处理
        List<TestLineInstancePO> allTlList = Func.copy(tlList,TestLineInstancePO.class,TestLineInstancePO.class);
        tlList = tlList.stream().filter(tl->req.getPendingFlag()!=(tl.getPendingFlag()==null?false:tl.getPendingFlag())).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(tlList)){
            Set<Integer> testLineIdSet = allTlList.stream().map(tl -> tl.getTestLineID()).collect(Collectors.toSet());
            result.setSuccess(false);
            result.setMsg(String.format("There are no testline[TestLineId:%s] should be to %s", StringUtils.join(testLineIdSet,","),req.getPendingFlag()?"Pending":"UnPending"));
            return result;
        }
        //目前只有type的TL可以pending
        // refactor(GPO2-15946) 作为MR用户，我希望非Completed状态时可以Pending TL，以满足流程要求
        if(req.getPendingFlag()){
            List<TestLineInstancePO> notLimitStatusTL = tlList.stream()
                    .filter(tl ->
                            !TestLineStatus.checkCategory(tl.getTestLineStatus(), Constants.TEST_LINE.STATUS_CATEGORY.EDIT)
                    ).collect(Collectors.toList());
            if(CollectionUtils.isNotEmpty(notLimitStatusTL)){
                Set<Integer> testLineIdSet = notLimitStatusTL.stream().map(TestLineInstancePO::getTestLineID).collect(Collectors.toSet());
                Set<String> testLineLimitStatus = notLimitStatusTL.stream()
                        .map(tl -> TestLineStatus.getMessage(tl.getTestLineStatus()))
                        .collect(Collectors.toSet());
                result.setSuccess(false);
                result.setMsg(String.format("There are some testline[TestLineId:%s] status that are Limit status:%s",
                        StringUtils.join(testLineIdSet,","), StringUtils.join(testLineLimitStatus,",")));
                return result;
            }
        }
        //增加是否有job 和 分包
//        GPO2-6192中,去掉job 和 分包的TestLine的判断逻辑，只判断TestLineStatus是否是Typing
       /* List<JobTestLineRelationshipInfoPO> jobList = jobTestLineRelExtMapper.getJobTestLineByTestLineInstanceId(req.getTestLineInstanceIds());
        if(CollectionUtils.isNotEmpty(jobList)){
            List<String> collect = jobList.stream().map(job -> job.getTestlineinstanceid()).collect(Collectors.toList());
            Set<Integer> testLineIdSet = tlList.stream().filter(tl -> collect.contains(tl.getID())).map(tl -> tl.getTestLineID()).collect(Collectors.toSet());
            result.setSuccess(false);
            result.setMsg(String.format("There are some testline[TestLineId:%s] that are create job", StringUtils.join(testLineIdSet,",")));
            return result;
        }
        SubContractTestLineMappingExample example = new SubContractTestLineMappingExample();
        example.createCriteria().andTestLineInstanceIDIn(req.getTestLineInstanceIds());
        List<SubContractTestLineMappingPO> subList = subContractTestLineMappingMapper.selectByExample(example);
        if(CollectionUtils.isNotEmpty(subList)){
            List<String> collect = subList.stream().map(sub -> sub.getTestLineInstanceID()).collect(Collectors.toList());
            Set<Integer> testLineIdSet = tlList.stream().filter(tl -> collect.contains(tl.getID())).map(tl -> tl.getTestLineID()).collect(Collectors.toSet());
            result.setSuccess(false);
            result.setMsg(String.format("There are some testline[TestLineId:%s] that are do subContract", StringUtils.join(testLineIdSet,",")));
            return result;
        }
*/
        int i = testLineMapper.updatePendingFlagByIds(req);

        if(i>0){
            List<TestLineInstancePO> testLineInstancePOS = testLineMapper.selectTestLineStatusByIds(req.getTestLineInstanceIds());
            List<String> testLineIds = testLineInstancePOS.stream().map(m -> m.getTestLineID().toString()).collect(Collectors.toList());

            String generalOrderInstanceID = testLineInstancePOS.get(0).getGeneralOrderInstanceID();

            GeneralOrderInstanceInfoPO generalOrderInstanceInfoPO = orderMapper.getOrderInfoByOrderId(generalOrderInstanceID);
            // 设置订单上的FullCyclePendingFlag
            Integer operationType = null;
            if(req.getPendingFlag()){
                operationType = 1;
            }else {
                // 判断订单下是否存在Pending状态的TL
                List<TestLineStatusDTO> testLineList = testLineMapper.getTestLineStatusByOrderNo(generalOrderInstanceInfoPO.getOrderNo());
                if(Func.isNotEmpty(testLineList)){
                    TestLineStatusDTO pendingTestLine = testLineList.stream().filter(tl->tl.isPendingFlag()).findAny().orElse(null);
                    if(Func.isEmpty(pendingTestLine)){
                        operationType = 0;
                    }
                }
            }
            if(Func.isNotEmpty(operationType)){
                UpdateFullCyclePendingFlagReq updateFullCyclePendingFlagReq = new UpdateFullCyclePendingFlagReq();
                updateFullCyclePendingFlagReq.setOperationType(operationType);
                updateFullCyclePendingFlagReq.setFullCyclePendingFlag(FullCyclePendingFlag.TestLine.getCode());
                updateFullCyclePendingFlagReq.setProductLineCode(ProductLineContextHolder.getProductLineCode());
                // 记录需要更新Flag的order
                List<String> orderNos = Lists.newArrayList();
                orderNos.add(generalOrderInstanceInfoPO.getOrderNo());
                updateFullCyclePendingFlagReq.setOrderNos(orderNos);
                BaseResponse updatePendingFlagRes = orderFacade.updateFullCyclePendingFlag(updateFullCyclePendingFlagReq);
                log.info("UpdateFullCyclePendingFlag: {}",JSON.toJSONString(updatePendingFlagRes));
            }
            // 更新StatusLog表
            List<String> testLineInstanceIds = req.getTestLineInstanceIds();
            if(Func.isNotEmpty(testLineInstanceIds)){
                testLineInstanceIds.stream().forEach(tl->{
                    StatusLogReq statusLogReq = new StatusLogReq();
                    statusLogReq.setOrderNo(generalOrderInstanceInfoPO.getOrderNo());
                    statusLogReq.setObjectType(ObjectType.TestLine.getCode());
                    statusLogReq.setObjectId(tl);
                    if(req.getPendingFlag()){
                        statusLogService.pending(statusLogReq);
                    }else {
                        statusLogService.unPending(statusLogReq);
                    }
                });
            }
            OrderInfoDto orderInfo = orderClient.getOrderInfoByOrderNo(generalOrderInstanceInfoPO.getOrderNo());
            UserInfo userInfo = tokenClient.getUser();
            BizLogInfo bizLog = new BizLogInfo();
            bizLog.setBizId(generalOrderInstanceInfoPO.getOrderNo());
            bizLog.setBu((Func.isNotEmpty(orderInfo) && Func.isNotEmpty(orderInfo.getBUCode())) ? orderInfo.getBUCode() : userInfo.getCurrentLabCode().split(" ")[1]);
            bizLog.setLab((Func.isNotEmpty(orderInfo) && Func.isNotEmpty(orderInfo.getLocationCode())) ? orderInfo.getLocationCode() : userInfo.getCurrentLabCode().split(" ")[0]);
            bizLog.setBizOpType(BizLogConstant.TEST_HISTORY);
            String newVal = "TL Ids["+StringUtils.join(testLineIds,",")+"]";
            if(req.getPendingFlag()){
                bizLog.setOpType("Pending TL");
                if(Func.isNotEmpty(req.getPendingReason())){
                    newVal = newVal.concat(" PendingReason:"+req.getPendingReason());
                }
                if(Func.isNotEmpty(req.getPendingRemark())){
                    newVal = newVal.concat(" Remark:"+req.getPendingRemark());
                }
            }else{
                bizLog.setOpType("UnPending TL");
            }
            bizLog.setOpUser(userInfo.getRegionAccount());
            bizLog.setNewVal(newVal);
            bizLogClient.doSend(bizLog);
            String token = tokenClient.getToken();
            tlList.stream().forEach(item->{
                try {
                    //保存bizLog， TL Pending状态变化
                    BizLogInfo bizLogClosed = new BizLogInfo();
                    bizLogClosed.setBizOpType(BizLogConstant.TESTLINE_STATUS_CHANGE_HISTORY);
                    bizLogClosed.setLab(userInfo.getCurrentLabCode().split(" ")[0]);
                    bizLogClosed.setBu(userInfo.getCurrentLabCode().split(" ")[1]);
                    bizLogClosed.setOpUser(userInfo.getRegionAccount());
                    bizLogClosed.setBizId(item.getTestItemNo());
                    if(req.getPendingFlag()){
                        bizLogClosed.setOpType("Pending TL");
                        bizLogClosed.setNewVal("Pending");
                    }else{
                        bizLogClosed.setOpType("UnPending TL");
                        bizLogClosed.setNewVal("UnPending");
                    }
                    bizLogClosed.setOriginalVal(item.getTestLineStatus());
                    //log.info("[{}],bizlog:Tl_Status_Change_log,{},{}",item.getTestItemNo(),bizLog.getOpType(), JSON.toJSONString(bizLogClosed));
                    bizLogClient.doSend(bizLogClosed);
                    if(req.getPendingFlag()){
                        InsertOperationHistoryReq insertOperationHistoryReq = new InsertOperationHistoryReq();
                        insertOperationHistoryReq.setObjectId(item.getID());
                        insertOperationHistoryReq.setObjectNo(item.getTestItemNo());
                        insertOperationHistoryReq.setOperationType(OperationTypeEnums.PendTestLine.getStatus());
                        insertOperationHistoryReq.setReasonType(req.getPendingReason());
                        insertOperationHistoryReq.setRemark(req.getPendingRemark());
                        insertOperationHistoryReq.setToken(token);
                        operationHistoryFacade.operationHistorySave(insertOperationHistoryReq);
                        //发送邮件Pending通知
                    }
                } catch (Exception e) {
                    log.error("bizlog:TestLine_Status_Change_log,{} error:{}",bizLog.getOpType(), e);
                }
            });
            //Pending发送邮件
            if (Func.isNotEmpty(userInfo.getEmail()) && req.getPendingFlag() && Func.isNotEmpty(tlList)) {
                //同一个操作发送一份邮件
                String orderInstanceId = tlList.get(0).getGeneralOrderInstanceID();
                GeneralOrderInstanceInfoPO order = orderMapper.getOrderInfoByOrderId(orderInstanceId);
                if(Func.isNotEmpty(order)){
                    OrderIdReq orderIdReq = new OrderIdReq();
                    orderIdReq.setOrderNo(order.getOrderNo());
                    orderIdReq.setProductLineCode(ProductLineContextHolder.getProductLineCode());
                    OrderAllDTO orderAllDTO = orderFacade.getOrderForPe(orderIdReq).getData();
                    if(Func.isNotEmpty(orderAllDTO)){
                        //参数
                        Map<String, Object> params = new HashMap<>();
                        EmailAutoSendDTO emailAutoSendDTO = new EmailAutoSendDTO();
                        emailAutoSendDTO.setMailSubject(order.getOrderNo() + " - Test Line Pending通知");
                        emailAutoSendDTO.setTemplateCode("GPO_PendingTestLineTemplate");
                        emailAutoSendDTO.setMailFrom(userInfo.getEmail());
                        List<String> mailTo = Lists.newArrayList();
                        mailTo.add(orderAllDTO.getcSEmail());
                        mailTo.add(userInfo.getEmail());
                        emailAutoSendDTO.setMailTo(mailTo);
                        params.put("operateUser", userInfo.getRegionAccount());
                        params.put("csName", orderAllDTO.getcSName());
                        StringBuilder testItemNo = new StringBuilder();
                        for(TestLineInstancePO testLine : tlList){
                            testItemNo.append(testLine.getTestItemNo()).append("<br/>");
                        }
                        params.put("testItemNo",testItemNo.toString());
                        params.put("reason", req.getPendingReason());
                        params.put("remark", req.getPendingRemark());
                        emailAutoSendDTO.setTemplateVariables(params);
                        //保存日志
                        SystemLog systemLog = new SystemLog();
                        systemLog.setObjectType("PendingEmailSend");
                        systemLog.setObjectNo(order.getOrderNo());
                        systemLog.setProductLineCode(ProductLineContextHolder.getProductLineCode());
                        systemLog.setType(SystemLogType.API.getType());
                        systemLog.setRemark("PendingEmail自动发送邮件");
                        systemLog.setCreateBy("system");
                        systemLog.setLocationCode(ProductLineContextHolder.getLocationCode());
                        systemLog.setOperationType("Auto Send Email Pending TestLine");
                        emailAutoSendDTO.setTemplateType(EmailTemplateTypeEnums.GENERAL.getCode());
                        //log所需参数
                        emailAutoSendDTO.setLabCode(userInfo.getCurrentLabCode());
                        //log.info("发送Pending通知邮件Param:{}",JSON.toJSONString(emailAutoSendDTO));
                        sendEmailService.sendEmail(emailAutoSendDTO, systemLog);
                    }
                }
            }
        }


        result.setSuccess(i>0);
        return result;
    }

    public String getSaveTestLineKey(GetSaveTestLineKeyReq getSaveTestLineKeyReq) {
        if(Func.isEmpty(getSaveTestLineKeyReq)){
            getSaveTestLineKeyReq = new GetSaveTestLineKeyReq();
        }
        // TestLineVersionId,CitationVersionId, citationSectionId  CitationType ,Aid
//        TestLineType.findType()
        //不需要合并 且是ByPP添加+aid
        String testLineKey = String.format("%s_%s_%s_%s"
                , NumberUtil.toLong(getSaveTestLineKeyReq.getTestLineVersionId()), NumberUtil.toLong(getSaveTestLineKeyReq.getCitationVersionId()), NumberUtil.toLong(getSaveTestLineKeyReq.getCitationSectionId()),NumberUtil.toLong(getSaveTestLineKeyReq.getCitationType()));
        /*
         * 1 校验表中是否存在相同TL
         * 加aid
         *
         * 2 校验能否合并
         *  a、需要合并,且是PP
         * 不加Aid
         *  b、不需要合并且是PP
         *  加aid
         * */
        if(!getSaveTestLineKeyReq.isCheckMergeTl() || (!getSaveTestLineKeyReq.isMergePpTl() && Func.isNotEmpty(getSaveTestLineKeyReq.getAid()) && getSaveTestLineKeyReq.getAid()>0)){
            testLineKey = String.format("%s_%s", testLineKey,NumberUtil.toLong(getSaveTestLineKeyReq.getAid()));
        }
        if(TestLineType.check(getSaveTestLineKeyReq.getTestLineType(),TestLineType.SUB_PP) || TestLineType.check(getSaveTestLineKeyReq.getTestLineType(),TestLineType.VIRTAUL_TL)){
            testLineKey = String.format("%s_%s", testLineKey,Func.toStr(testLineService.buildTestLineTypeKey(getSaveTestLineKeyReq.getTestLineType())));
        }
        return testLineKey;
    }

}
