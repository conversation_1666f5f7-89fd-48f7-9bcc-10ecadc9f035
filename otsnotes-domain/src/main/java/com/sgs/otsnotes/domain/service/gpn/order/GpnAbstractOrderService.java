package com.sgs.otsnotes.domain.service.gpn.order;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.sgs.core.domain.UserInfo;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.core.base.CustomResult;
import com.sgs.framework.core.base.ResponseCode;
import com.sgs.framework.core.exception.BizException;
import com.sgs.framework.log.SystemLogHelper;
import com.sgs.framework.log.model.SystemLog;
import com.sgs.framework.model.enums.LanguageType;
import com.sgs.framework.model.enums.ReportTestResultStatus;
import com.sgs.framework.security.context.SecurityContextHolder;
import com.sgs.framework.security.utils.SecurityUtil;
import com.sgs.framework.tool.utils.Func;
import com.sgs.framework.tool.utils.ProductLineContextHolder;
import com.sgs.gpo.facade.model.report.req.ReportDefaultAccreditationQueryReq;
import com.sgs.gpo.facade.model.report.rsp.DefaultAccreditationRsp;
import com.sgs.gpo.facade.temp.ReportTempFacade;
import com.sgs.otsnotes.core.config.Prompt;
import com.sgs.otsnotes.core.constants.Constants;
import com.sgs.otsnotes.core.enums.ReportFlagEnums;
import com.sgs.otsnotes.core.util.CacheAgent;
import com.sgs.otsnotes.core.util.DateUtils;
import com.sgs.otsnotes.dbstorages.mybatis.enums.CustomerUsage;
import com.sgs.otsnotes.dbstorages.mybatis.enums.TestLineRequestType;
import com.sgs.otsnotes.dbstorages.mybatis.extmapper.*;
import com.sgs.otsnotes.dbstorages.mybatis.mapper.*;
import com.sgs.otsnotes.dbstorages.mybatis.model.*;
import com.sgs.otsnotes.domain.service.*;
import com.sgs.otsnotes.domain.service.gpn.report.IGpnReportService;
import com.sgs.otsnotes.domain.service.gpn.subcontract.ISubContractExternalRelService;
import com.sgs.otsnotes.domain.service.productlineservice.ProductLineService;
import com.sgs.otsnotes.domain.service.testmatrix.ITestMatrixService;
import com.sgs.otsnotes.facade.model.dto.*;
import com.sgs.otsnotes.facade.model.dto.mq.SyncPreOrderForJobDTO;
import com.sgs.otsnotes.facade.model.enums.ReportStatus;
import com.sgs.otsnotes.facade.model.enums.SystemLogType;
import com.sgs.otsnotes.facade.model.enums.*;
import com.sgs.otsnotes.facade.model.gpn.duedate.info.*;
import com.sgs.otsnotes.facade.model.req.OrderReq;
import com.sgs.otsnotes.facade.model.req.*;
import com.sgs.otsnotes.facade.model.req.customer.CustomerReportReviewerReq;
import com.sgs.otsnotes.facade.model.req.gpn.ToTestAndAssignSampleReq;
import com.sgs.otsnotes.facade.model.req.matrix.ConfirmMatrixReq;
import com.sgs.otsnotes.facade.model.req.order.CreateOrderReq;
import com.sgs.otsnotes.facade.model.req.order.SampleDTO;
import com.sgs.otsnotes.facade.model.req.report.SaveReportRequest;
import com.sgs.otsnotes.facade.model.req.report.UserSignatureQueryRequest;
import com.sgs.otsnotes.facade.model.req.testLine.OrderTestLineReq;
import com.sgs.otsnotes.facade.model.req.testLine.ToTLAndAssignSampleReq;
import com.sgs.otsnotes.facade.model.rsp.SampleAndSampleGroupRsp;
import com.sgs.otsnotes.facade.model.rsp.SampleGroupRsp;
import com.sgs.otsnotes.facade.model.rsp.SampleRsp;
import com.sgs.otsnotes.facade.model.rsp.barcode.ObjectBarcodeRsp;
import com.sgs.otsnotes.facade.model.rsp.matrix.ToTestAndAssignSampleRsp;
import com.sgs.otsnotes.facade.model.rsp.testLine.SaveTestLineMatrixRsp;
import com.sgs.otsnotes.facade.model.rsp.testLine.TestLineListReq;
import com.sgs.otsnotes.facade.model.testmatrix.info.OrderTestMatrixInfo;
import com.sgs.otsnotes.facade.model.testmatrix.req.OrderTestMatrixReq;
import com.sgs.otsnotes.integration.*;
import com.sgs.preorder.facade.OrderFacade;
import com.sgs.preorder.facade.OrderReportFacade;
import com.sgs.preorder.facade.TestLineInstanceFacadeForEM;
import com.sgs.preorder.facade.ToDoListFacade;
import com.sgs.preorder.facade.model.dto.customer.CustomerInstanceDTO;
import com.sgs.preorder.facade.model.dto.order.MasterOrderInfoDTO;
import com.sgs.preorder.facade.model.dto.order.OrderAllDTO;
import com.sgs.preorder.facade.model.dto.order.OrderInfoDto;
import com.sgs.preorder.facade.model.enums.*;
import com.sgs.preorder.facade.model.info.TestRequestInfo;
import com.sgs.preorder.facade.model.req.*;
import com.sgs.preorder.facade.model.req.report.ReportStatusReq;
import com.sgs.preorder.facade.model.rsp.TestLineAttachmentRsp;
import com.sgs.priceengine.facade.QuotationFacade;
import com.sgs.priceengine.facade.model.DTO.TLAmountDTO;
import com.sgs.priceengine.facade.model.request.QueryTLAmountRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.beanutils.BeanUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections.Predicate;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.lang.reflect.InvocationTargetException;
import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Huang
 * @date 2020/9/12
 * @email <EMAIL>
 */
@Slf4j
public abstract class GpnAbstractOrderService extends ProductLineService implements IOrderService {

    private final Logger logger = LoggerFactory.getLogger(GpnAbstractOrderService.class);
    @Resource
    private OrderCrossLabRelMapper orderCrossLabRelMapper;
    @Resource
    private SyncPreOrderForJobMapper syncPreOrderForJobMapper;
    @Resource
    private TestLineService testLineService;
    @Resource
    private GeneralOrderInstanceMapper generalOrderInstanceMapper;
    @Resource
    private GeneralOrderInstanceInfoMapper generalOrderInstanceInfoMapper;
    @Resource
    private ReportTypeMapper reportTypeMapper;
    @Resource
    private ReportInfoMapper reportInfoMapper;
    @Resource
    private ReportMapper reportMapper;
    @Resource
    private TestSampleInfoMapper testSampleInfoMapper;
    @Resource
    private TestSampleMapper testSampleMapper;
    @Resource
    private SampleService sampleService;
    @Resource
    private SubContractExtMapper subContractExtMapper;
    @Resource
    private ISubContractExternalRelService subContractExternalRelService;
    @Resource
    private IGpnReportService gpnReportService;
    @Autowired
    private TokenClient tokenClient;
    @Autowired
    private TestLineMapper testLineMapper;
    @Autowired
    private TestMatrixMapper testMatrixMapper;
    @Autowired
    private ConclusionMapper conclusionMapper;
    @Autowired
    private JobExtMapper jobExtMapper;
    @Autowired
    private SubContractMapper subContractMapper;
    @Autowired
    private TestLineInstanceMapper testLineInstanceMapper;
    @Autowired
    private OrderFacade orderFacade;
    @Autowired
    private ToDoListFacade toDoListFacade;
    @Autowired
    private OrderMapper orderMapper;
    @Autowired
    private OrderClient orderClient;
    @Autowired
    private OrderQueryService orderQueryService;
    @Resource
    private OrderReportFacade orderReportFacade;
    @Autowired
    private OrderTestLineService orderTestLineService;
    @Autowired
    private OrderTestLineVMExtMapper orderTestLineVMExtMapper;
    @Autowired
    private IGpnReportService iGpnReportService;
    @Autowired
    private TestLineInstanceFacadeForEM testLineInstanceFacadeForEM;
    @Autowired
    private TestMatrixInfoMapper testMatrixInfoMapper;
    @Autowired
    private QuotationFacade quotationFacade;
    @Autowired
    private SystemLogHelper systemLogHelper;
    @Autowired
    private ExternalNoClient externalNoClient;
    @Autowired
    private ITestMatrixService iTestMatrixService;
    @Autowired
    private TestMatrixService testMatrixService;
    @Autowired
    private ReportTempFacade reportTempFacade;
    @Autowired
    private CustomerClient customerClient;
    @Autowired
    private FrameWorkClient frameWorkClient;

    @Override
    public int deleteCrossLabRel(String orderNo){
        OrderCrossLabRelExample example = new OrderCrossLabRelExample();
        example.createCriteria().andOrderNoEqualTo(orderNo);
        return orderCrossLabRelMapper.deleteByExample(example);
    }

    @Override
    public Integer insertSync(SyncPreOrderForJobDTO syncPreOrderForJobDTO) {
        return syncPreOrderForJobMapper.insertSync(syncPreOrderForJobDTO);
    }

    @Override
    public Integer updateSync(SyncPreOrderForJobDTO syncPreOrderForJobDTO) {
        return syncPreOrderForJobMapper.updateSync(syncPreOrderForJobDTO);
    }

    @Override
    public Integer deleteSync(SyncPreOrderForJobDTO syncPreOrderForJobDTO) {
        return syncPreOrderForJobMapper.deleteSync(syncPreOrderForJobDTO);
    }

    @Override
    public List<SyncPreOrderForJobDTO> selectSync(SyncPreOrderForJobDTO syncPreOrderForJobDTO) {
        return syncPreOrderForJobMapper.selectSync(syncPreOrderForJobDTO);
    }
    @Override
    public BaseResponse getTestLineList(OrderTestLineReq orderTestLineReq) {
        CustomResult testLineList = testLineService.getTestLineList(orderTestLineReq);
        return BaseResponse.newInstance(testLineList);
    }
    @Override
    public BaseResponse<PageInfo<TestLinePageListDTO>> getTestLineListNew(TestLineListReq orderTestLineReq, Integer page, Integer rows) {
        BaseResponse baseResponse = new BaseResponse();
//        PageHelper.startPage(page, rows, null);
        //orderTestLineReq.setProductLineCode(ProductLineContextHolder.getProductLineCode());
        orderTestLineReq.setLabCode(tokenClient.getUserLabCode());
        // 解析前端传入的批量NO
        if(StringUtils.isNotEmpty(orderTestLineReq.getTestItemNos())){
            orderTestLineReq.setTestItemArray(Arrays.asList(orderTestLineReq.getTestItemNos().trim().split("\n")));
        }
        //复制查询参数
        TestLineListSearchReq testLineListSearchReq = new TestLineListSearchReq();
        Func.copy(orderTestLineReq,testLineListSearchReq);
        testLineListSearchReq.setRows(rows);
        testLineListSearchReq.setPage(page);
        //设置查询类型
        testLineListSearchReq.setRequestType(TestLineRequestType.pageList.getStatus());
        PageInfo<TestLinePageListDTO> testLineList = testLineService.testLineListSearch(testLineListSearchReq);

        // 新增返回结果判断，如果查询出list为空直接返回
        if(CollectionUtils.isEmpty(testLineList.getList())){
            baseResponse.setData(testLineList);
            baseResponse.setStatus(200);
            return BaseResponse.newInstance(baseResponse);
        }
        OrderNosReq orderNosReq = new OrderNosReq();
        List<String> orderNos = new ArrayList<>();
        testLineList.getList().forEach(item -> {
            orderNos.add(item.getOrderNo());
        });
        Map<String, List<TestLinePageListDTO>> collect = testLineList.getList().stream().collect(Collectors.groupingBy(TestLinePageListDTO::getOrderNo));
        orderNosReq.setOrderNos(orderNos);
        List<OrderAllDTO> orderAllDTOList = orderFacade.getOrderAllListByOrderNos(orderNosReq).getData();
        testLineList.getList().forEach(testLineListRsp->{
            //去掉如果是中文会导致多出英文的sectionName，在sql中拼接
/*            if (Func.isNotEmpty(testLineListRsp.getStandardSectionName())){
                testLineListRsp.setTestStandard(testLineListRsp.getTestStandard() + "," + testLineListRsp.getStandardSectionName());
            }*/
            orderAllDTOList.forEach(orderAllDTO->{
                if (orderAllDTO.getOrderNo().equals(testLineListRsp.getOrderNo())){
                    testLineListRsp.setOrderId(orderAllDTO.getID());
                    TestLineListDetailRsp testLineListDetailRsp = new TestLineListDetailRsp();
                    List<TestLineListDetailRsp> testLineListDetailRsps = new ArrayList<>();
                    testLineListDetailRsp.setCsName(orderAllDTO.getcSName());
                    testLineListDetailRsps.add(testLineListDetailRsp);
                    testLineListRsp.setCsName(orderAllDTO.getcSName());
                    testLineListRsp.setTestLineListDetailRsps(testLineListDetailRsps);
                    testLineListRsp.setPreOrderStatus(orderAllDTO.getOrderStatus());
                }

            });
        });
//        String defaultLanguageCode = tokenClient.getUser().getDefaultLanguageCode();
//        boolean check = UserInfoDefaultLanguageCodeEnums.check(defaultLanguageCode, UserInfoDefaultLanguageCodeEnums.zh_cn);
//        if (check){
//            collect.forEach((k, v) -> {
//                testLineLocalService.build(k,v,true,false);
//            });
//            testLineList.getList().forEach(item ->{
//                if (Func.isNotEmpty(item.getCitationName())){
//                    item.setTestStandard(item.getCitationName());
//                }
//            });
//        }

        baseResponse.setData(testLineList);
        baseResponse.setStatus(200);
        return BaseResponse.newInstance(baseResponse);
    }

    @Override
    public BaseResponse<PageInfo<OrderTestLineListDTO>> getOrderTestLinePage(OrderTestLineListSearchReq testLineListSearchReq, Integer page, Integer rows) {
        logger.info("接口开始:{}",new Date());
        BaseResponse baseResponse = new BaseResponse();
        testLineListSearchReq.setLabCode(tokenClient.getUserLabCode());
        testLineListSearchReq.setProductLineCode(ProductLineContextHolder.getProductLineCode());

        UserInfo userInfo = SecurityContextHolder.getUserInfo();
        if(Func.isEmpty(userInfo) || Func.isEmpty(userInfo.getCurrentLabCode())){
            return BaseResponse.newFailInstance(ResponseCode.TokenExpire.getMessage());
        }
        // 解析前端传入的批量NO
        if(StringUtils.isNotEmpty(testLineListSearchReq.getTestItemNos())){
            testLineListSearchReq.setTestItemArray(Arrays.asList(testLineListSearchReq.getTestItemNos().trim().split("\n")));
        }
        if (Func.isNotEmpty(testLineListSearchReq.getOrderNoBatch())){
            String[] split = testLineListSearchReq.getOrderNoBatch().split("\n");
            List<String> list = new ArrayList<>();
            for (String item : split){
                list.add(item);
            }
            if (Func.isNotEmpty(list)){
                logger.info("batchGetOrderNoByExternalOrderNo开始:{}",new Date());
                testLineListSearchReq.setBatchOrderList(orderClient.batchGetOrderNoByExternalOrderNo(list,ProductLineContextHolder.getProductLineCode()));
                logger.info("batchGetOrderNoByExternalOrderNo开始:{}",new Date());
            }
        }
        testLineListSearchReq.setRows(rows);
        testLineListSearchReq.setPage(page);
        //设置查询类型
        testLineListSearchReq.setRequestType(TestLineRequestType.pageList.getStatus());
        logger.info("getPageOrderTestLineList开始:{}",new Date());
        PageInfo<OrderTestLineListDTO> testLineList = orderTestLineService.getPageOrderTestLineList(testLineListSearchReq);
        logger.info("getPageOrderTestLineList结束:{}",new Date());


        // 新增返回结果判断，如果查询出list为空直接返回
        if(CollectionUtils.isEmpty(testLineList.getList())){
            baseResponse.setData(testLineList);
            baseResponse.setStatus(200);
            return BaseResponse.newInstance(baseResponse);
        }
        OrderNosReq orderNosReq = new OrderNosReq();
        List<String> orderNos = new ArrayList<>();
        testLineList.getList().forEach(item -> {
            orderNos.add(item.getOrderNo());
        });
//        Map<String, List<OrderTestLinePageListDTO>> collect = testLineList.getList().stream().collect(Collectors.groupingBy(OrderTestLinePageListDTO::getOrderNo));
        orderNosReq.setOrderNos(orderNos);
        List<OrderAllDTO> orderAllDTOList = orderFacade.getOrderAllListByOrderNos(orderNosReq).getData();
        //查询是否有对应的附件
        List<TestLineAttachmentRsp> testLineAttachmentRsps = new ArrayList<>();
        if (Func.isNotEmpty(testLineListSearchReq.getIsToDoList()) && !testLineListSearchReq.getIsToDoList()){
            List<String> testLineIds = testLineList.getList().stream().map(OrderTestLineListDTO :: getTestLineInstanceId).collect(Collectors.toList());
            TestLineReq testLineReq = this.getTestLineReqParam(testLineIds);
            BaseResponse<List<TestLineAttachmentRsp>> listBaseResponse =testLineInstanceFacadeForEM.getTestLineAttachment(testLineReq);
            if (Func.isEmpty(listBaseResponse) || !Func.equals(ResponseCode.SUCCESS.getCode(),listBaseResponse.getStatus())){
                return BaseResponse.newFailInstance("查询附件信息失败");
            }
            testLineAttachmentRsps = listBaseResponse.getData();
        }

        //查询tl amount
        QueryTLAmountRequest objQueryTLAmountRequest = new QueryTLAmountRequest();
        objQueryTLAmountRequest.setSystemId(15);
        objQueryTLAmountRequest.setProductLineCode(ProductLineContextHolder.getProductLineCode());
        objQueryTLAmountRequest.setOrderIdList(orderAllDTOList.stream().map(e -> e.getID()).collect(Collectors.toList()));
        logger.info("get tl amount req:" + JSON.toJSONString(objQueryTLAmountRequest));
        BaseResponse<List<TLAmountDTO>> tlAmountReponse = quotationFacade.queryTLAmount(objQueryTLAmountRequest);
        logger.info("get tl amount rsp:" + JSON.toJSONString(tlAmountReponse));
        List<TLAmountDTO> tlAmountDTOList=Lists.newArrayList();
        if (tlAmountReponse.getStatus() == 200 && Func.isNotEmpty(tlAmountReponse.getData())) {
            tlAmountDTOList = tlAmountReponse.getData();
        }
        List<TLAmountDTO> tlResultAmountDTOList=tlAmountDTOList;
        List<TestLineAttachmentRsp> finalTestLineAttachmentRsps = testLineAttachmentRsps;
        testLineList.getList().forEach(testLineListRsp->{
            orderAllDTOList.forEach(orderAllDTO->{
                if (orderAllDTO.getOrderNo().equals(testLineListRsp.getOrderNo())){
                    testLineListRsp.setOrderId(orderAllDTO.getID());
                    TestLineListDetailRsp testLineListDetailRsp = new TestLineListDetailRsp();
                    List<TestLineListDetailRsp> testLineListDetailRsps = new ArrayList<>();
                    testLineListDetailRsp.setCsName(orderAllDTO.getcSName());
                    testLineListDetailRsps.add(testLineListDetailRsp);
                    testLineListRsp.setCsName(orderAllDTO.getcSName());
                    testLineListRsp.setTestLineListDetailRsps(testLineListDetailRsps);
                    testLineListRsp.setPreOrderStatus(orderAllDTO.getOrderStatus());
                    testLineListRsp.setExternalOrderNo(orderAllDTO.getExternalOrderNo());
                }
            });
            TestLineAttachmentRsp testLineAttachmentRsp = finalTestLineAttachmentRsps.stream().
                    filter(testLineAttachment -> Func.equals(testLineListRsp.getTestLineInstanceId(),testLineAttachment.getTestLineId())).findFirst().orElse(null);
            if (Func.isEmpty(testLineAttachmentRsp)){
                testLineListRsp.setHavingAttachment(false);
            }else {
                testLineListRsp.setHavingAttachment(testLineAttachmentRsp.getHavingAttachment());
            }
            //处理tl
            doSetTlAmount(tlResultAmountDTOList, testLineListRsp);
        });

        logger.info("接口返回:{}",new Date());
        baseResponse.setData(testLineList);
        baseResponse.setStatus(200);

        // 记录推送日志
        SystemLog systemLog = new SystemLog();
        systemLog.setObjectType(SystemLogObjectEnum.TEST_LINE.getType());
        systemLog.setObjectNo(null);
        systemLog.setProductLineCode(ProductLineContextHolder.getProductLineCode());
        systemLog.setType(SystemLogType.BURIED_POINT.getType());
        systemLog.setRemark(SystemLogObjectEnum.TEST_LINE.getDesc());
        systemLog.setRequest(JSON.toJSONString(testLineListSearchReq));
        systemLog.setCreateBy(userInfo.getRegionAccount());
        systemLog.setLocationCode(userInfo.getCurrentLabCode().split(" ")[0]);
        systemLog.setOperationType(SystemLogObjectEnum.TEST_LINE.getOpType());
        systemLog.setCreateBy(userInfo.getRegionAccount());
        systemLogHelper.save(systemLog);
        return BaseResponse.newInstance(baseResponse);
    }

    private void doSetTlAmount(List<TLAmountDTO> tlResultAmountDTOList, OrderTestLineListDTO testLineListRsp) {
        if(Func.isNotEmpty(tlResultAmountDTOList)){
            List<TLAmountDTO> tlOrderAmountDTOList= tlResultAmountDTOList.stream().filter(e->
            {
                return Func.equalsSafe(e.getPpTlRelId(),testLineListRsp.getPpTlRelId());
            }

            ).collect(Collectors.toList());
            BigDecimal tlBigAmount=null;
            if(Func.isNotEmpty(tlOrderAmountDTOList)){
                Double d=tlOrderAmountDTOList.stream().mapToDouble(e->e.getAmount()==null?0:e.getAmount().doubleValue()).sum();
                tlBigAmount=BigDecimal.valueOf(d).setScale(2,BigDecimal.ROUND_HALF_UP);
            }
            DecimalFormat df = new DecimalFormat(",###,###.00");
            String amountText=tlBigAmount==null||tlBigAmount.compareTo(BigDecimal.ZERO)==0?"0.00":df.format(tlBigAmount);
            testLineListRsp.setTotalAmountDisplay(tlBigAmount!=null ? tlOrderAmountDTOList.get(0).getCurrencyCodeDisplay() + amountText : "Unable to calculate");
        }
    }

    private TestLineReq getTestLineReqParam(List<String> testLineIds){
        TestLineReq testLineReq = new TestLineReq();
        testLineReq.setProductLineCode(ProductLineContextHolder.getProductLineCode());

        TestMatrixInfoExample testMatrixInfoExample = new TestMatrixInfoExample();
        testMatrixInfoExample.createCriteria().andTestLineInstanceIDIn(testLineIds);
        List<TestMatrixInfoPO> testMatrixInfoPOS = testMatrixInfoMapper.selectByExample(testMatrixInfoExample);

        List<TestLineAttachmentHavingReq> testLineAttachmentHavingReqs = new ArrayList<>();
        for (String testLineId : testLineIds){
            TestLineAttachmentHavingReq testLineAttachmentHavingReq = new TestLineAttachmentHavingReq();
            testLineAttachmentHavingReq.setTestLineId(testLineId);
            List<TestMatrixInfoPO> testMatrixInfoPOList = testMatrixInfoPOS.stream().filter(testMatrixInfoPO ->
                    Func.equals(testMatrixInfoPO.getTestLineInstanceID(),testLineId)).collect(Collectors.toList());
            if (Func.isNotEmpty(testMatrixInfoPOList)){
                List<String> sampleIds = testMatrixInfoPOList.stream().map(TestMatrixInfoPO :: getTestSampleID).collect(Collectors.toList());
                testLineAttachmentHavingReq.setSampleIds(sampleIds);
            }
            testLineAttachmentHavingReqs.add(testLineAttachmentHavingReq);
        }
        testLineReq.setTestLineAttachmentHavingReqs(testLineAttachmentHavingReqs);
        return testLineReq;
    }



    @Override
    public GeneralOrderInstanceInfoPO getOrderByOrderNo(String orderNo) {
        GeneralOrderInstanceInfoExample objGeneralOrderInstanceInfoExample=new GeneralOrderInstanceInfoExample();
        objGeneralOrderInstanceInfoExample.createCriteria().andOrderNoEqualTo(orderNo);
        List<GeneralOrderInstanceInfoPO> generalOrderInstanceInfoPOS=generalOrderInstanceInfoMapper.selectByExample(objGeneralOrderInstanceInfoExample);
        if(Func.isNotEmpty(generalOrderInstanceInfoPOS)){
            generalOrderInstanceInfoPOS.get(0);
        }
        return null;
    }

    @Override
    public int createGeneralOrder(CreateOrderReq createOrderReq) {
        log.info("Starting createGeneralOrder......");
        log.info("Currency PreOrder is [" + JSON.toJSONString(createOrderReq) + "]");
        // 1.转换order对象, reportPO对象
        GeneralOrderInstanceInfoPO order = convertOrderPO(createOrderReq);
        log.info("After transform   GeneralOrderInstancePO is [" + JSON.toJSONString(order) + "]");

        ReportInfoPO reportPO = convertReportPO(createOrderReq);
        log.info("createGeneralOrder convertReportPO:{}",JSON.toJSONString(reportPO));
        GeneralOrderInstanceInfoPO generalOrderInstanceDTO=generalOrderInstanceMapper.selectGeneralOrderInstanceByOrderNo(createOrderReq.getOrderNo());

        // 自动判断insert/update
        String action = ObjectUtils.isEmpty(generalOrderInstanceDTO) ? "NEW" : "UPDATE";
        // 2.处理order，report信息
        if (StringUtils.equals("NEW", action)) {
            order.setID(Func.isNotEmpty(createOrderReq.getOrderId())?createOrderReq.getOrderId():Func.randomUUID());
            order.setConclusionMode(ConclusionMode.NoLimit.getId());
            order.setCreatedDate(new Date());
            order.setCreatedBy(SecurityUtil.getUserAccount());
            order.setActiveIndicator(true);
            generalOrderInstanceInfoMapper.insert(order);
            //generalOrderInstanceDAO.save(order);
            // 新增report
            // 设置reportTypeID
            Map<String, Object> params = new HashMap<>();
            // 查询Original对应的type类型id
            params.put("description", "Original");
            ReportTypeExample reportTypeExample=new ReportTypeExample();
            reportTypeExample.createCriteria().andDescriptionEqualTo("Original");
            List<ReportTypePO> reportTypePOs=reportTypeMapper.selectByExample(reportTypeExample);
            String typeID = "";
            if (CollectionUtils.isNotEmpty(reportTypePOs)) {
                typeID = reportTypePOs.get(0).getID();
            }

            String reportId = Func.randomUUID();
            reportPO.setReportTypeID(typeID);
            reportPO.setID(Func.isEmpty(createOrderReq.getReportId())?reportId:createOrderReq.getReportId());
            reportPO.setActiveIndicator(true);
            reportPO.setIsDeliveryApprove(false);
            reportPO.setLabId(order.getLabId());
            reportPO.setLabCode(order.getLabCode());
            reportPO.setIsToDm(0);
            reportPO.setCoverPageTemplateNewMappingFlag(false);
            reportPO.setRecalculationFlag(0);
            reportPO.setCreatedDate(new Date());
            reportPO.setTypingFinishedFlag(ActiveType.Disable.getStatus());
            reportPO.setCreatedBy(SecurityUtil.getUserAccount());
            reportPO.setReportFlag(1);
            reportPO.setRootReportNo(createOrderReq.getReportNo());
            reportPO.setTestResultStatus(ReportTestResultStatus.New.getCode());

            if (StringUtils.equals("2", createOrderReq.getOperationType())) {
                reportPO.setReportStatus(207);
            } else {
                reportPO.setReportStatus(201);
            }

            //设置report template,
            // MR 特殊模式，默认选中第一个 general 模板
//            注释代码，设置ReportTemplate已经有单独方法再处理了
//            if(DigitalReportBuTypeEnum.MR.getBuCode().equals(createOrderReq.getProductLineCode()))
//            {
//                QueryReportTemplateRequest queryReportTemplateRequest = new QueryReportTemplateRequest();
//                queryReportTemplateRequest.setBuSubTypeID(DigitalReportBuTypeEnum.MR.getDigReportBuId());
//                queryReportTemplateRequest.setTemplateSettingTypeID(1605);
//                queryReportTemplateRequest.setProductLineCode(createOrderReq.getProductLineCode());
//                queryReportTemplateRequest.setSystemId(15);
//                List<MergedNameReportDTO> mergedNameReportDTOS = generateReportFacade.queryReportTemplate(queryReportTemplateRequest).getData();
//                if(CollectionUtils.isNotEmpty(mergedNameReportDTOS))
//                {
//                    MergedNameReportDTO reportTemplate = mergedNameReportDTOS.get(0);
//                    for(DigitalReportTemplateDTO templateDTO :reportTemplate.getTemplateSettings())
//                    {
//                        ReportTemplateInfoPO reportTemplateInfoPO = new ReportTemplateInfoPO();
//                        reportTemplateInfoPO.setId(Func.randomUUID());
//                        reportTemplateInfoPO.setReportId(reportId);
//                        int languageId  = DigitalReportLanguageType.CN.getCode().intValue() == templateDTO.getLanguageID().intValue() ?
//                                LanguageType.Chinese.getLanguageId():LanguageType.English.getLanguageId();
//                        reportTemplateInfoPO.setLanguageId(languageId);
//                        reportTemplateInfoPO.setTemplateSettingId(templateDTO.getTemplateSettingID());
//                        reportTemplateInfoPO.setActiveIndicator(true);
//                        reportTemplateInfoPO.setCreatedDate(new Date());
//                        reportTemplateInfoPO.setCreatedBy(SecurityUtil.getUserAccount());
//                        reportTemplateInfoMapper.insert(reportTemplateInfoPO);
//                    }
//                    reportPO.setCoverPageTemplateName(reportTemplate.getTemplateName());
//                    reportPO.setCoverPageTemplatePath(DigitalReportBuTypeEnum.MR.getDigReportBuId().toString());
//                }
//            }
            reportPO.setSubReportReviseFlag(0);
            // Report SealFlag 默认取 Order SealFlag
            OrderIdReq orderIdReq = new OrderIdReq();
            orderIdReq.setOrderNo(reportPO.getOrderNo());
            orderIdReq.setProductLineCode(createOrderReq.getProductLineCode());
            BaseResponse<TestRequestInfo> testRequestRsp = orderFacade.queryRestRequestForPe(orderIdReq);
            if(Func.isNotEmpty(testRequestRsp) && Func.isNotEmpty(testRequestRsp.getData().getSealFlag())){
                reportPO.setSealFlag(testRequestRsp.getData().getSealFlag());
            }
            if(Func.isNotEmpty(testRequestRsp) && Func.isNotEmpty(testRequestRsp.getData())) {
                List<ReportCountryOfDestinationDTO> reportCountryOfDestinationDTOS = gpnReportService.generateReportCountryOfDestination(testRequestRsp.getData().getReportLanguage(), testRequestRsp.getData().getOrderId()).getData();
                if(Func.isNotEmpty(reportCountryOfDestinationDTOS)){
                    reportPO.setCountryOfDestination(JSON.toJSONString(reportCountryOfDestinationDTOS));
                }
            }
            //默认资质
            ReportDefaultAccreditationQueryReq reportDefaultAccreditationQueryReq = new ReportDefaultAccreditationQueryReq();
            reportDefaultAccreditationQueryReq.setOrderNoList(Sets.newHashSet(reportPO.getOrderNo()));
            reportDefaultAccreditationQueryReq.setProductLineCode(createOrderReq.getProductLineCode());
            log.info("DefaultAccreditationReq:{}", JSONObject.toJSONString(reportDefaultAccreditationQueryReq));
            BaseResponse<List<DefaultAccreditationRsp>> accreditationRsp = reportTempFacade.defaultAccreditationQuery(reportDefaultAccreditationQueryReq);
            log.info("DefaultAccreditationRsp:{}", JSONObject.toJSONString(accreditationRsp));
            if(accreditationRsp.isSuccess() && Func.isNotEmpty(accreditationRsp.getData())){
                List<DefaultAccreditationRsp> defaultAccreditationRspList = accreditationRsp.getData();
                DefaultAccreditationRsp defaultAcc = defaultAccreditationRspList.stream().filter(e -> Func.equalsSafe(e.getOrderNo(),reportPO.getOrderNo())).findAny().orElse(null);
                if(Func.isNotEmpty(defaultAcc)){
                    reportPO.setCertificateId(defaultAcc.getCertificateId());
                    reportPO.setCertificateFileCloudKey(defaultAcc.getCertificateFileCloudKey());
                    reportPO.setCertificateName(defaultAcc.getCertificateName());
                }
            }
            //默认Report Reviewer
            CustomerReportReviewerReq customerReportReviewerReq = new CustomerReportReviewerReq();
            customerReportReviewerReq.setLabCode(reportPO.getLabCode());
            customerReportReviewerReq.setRoleType("reportReviewer");
            //查询Customer信息
            BaseResponse<List<CustomerInstanceDTO>> customerRsp = orderFacade.queryCustomerForPe(orderIdReq);
            if(customerRsp.isSuccess() && Func.isNotEmpty(customerRsp.getData())){
                List<CustomerInstanceDTO> customerInstanceDTOList = customerRsp.getData();
                CustomerInstanceDTO applicant = customerInstanceDTOList.stream().filter(e -> Func.equalsSafe(e.getCustomerUsage(), CustomerUsage.Applicant.getCode())).findAny().orElse(null);
                CustomerInstanceDTO buyer = customerInstanceDTOList.stream().filter(e -> Func.equalsSafe(e.getCustomerUsage(),CustomerUsage.Buyer.getCode())).findAny().orElse(null);
                if(Func.isNotEmpty(applicant)){
                    customerReportReviewerReq.setApplicantCustomerNo(applicant.getBossNumber());
                }
                if(Func.isNotEmpty(buyer)){
                    customerReportReviewerReq.setBuyerCustomerNo(buyer.getBossNumber());
                }
                List<String> customers = customerClient.queryCustomerReportReviewer(customerReportReviewerReq);
                if(Func.isNotEmpty(customers)){
                    reportPO.setReviewerBy(customers.get(0));
                    // 查询中英文签名信息
                    UserSignatureQueryRequest userSignReq = new UserSignatureQueryRequest();
                    List<String> languageCodes = Lists.newArrayList(LanguageType.English.getCode(), LanguageType.Chinese.getCode());
                    userSignReq.setLanguageCodes(languageCodes);
                    userSignReq.setLabCode(reportPO.getLabCode());
                    userSignReq.setIsSignature("1");
                    userSignReq.setAuthSignTypes(Lists.newArrayList(SignTypeEnums.APPROVER.getCode(), SignTypeEnums.EDITOR.getCode(), SignTypeEnums.REVIEWER.getCode()));
                    List<UserSignatureDTO> userSignatureList = frameWorkClient.queryUserSinature(userSignReq);
                    SaveReportRequest request = new SaveReportRequest();
                    request.setReviewerBy(customers.get(0));
                    gpnReportService.setReportSignature(request, reportPO, SignTypeEnums.REVIEWER.getCode(), userSignatureList);
                }
            }
            log.info("createGeneralOrder report info:{}",JSON.toJSONString(reportPO));
            reportPO.setReportVersion(1);
            reportInfoMapper.insert(reportPO);
            //自动保存模板信息
            iGpnReportService.autoSearchTemplateSave(reportPO.getID(),ProductLineContextHolder.getProductLineCode(),SecurityUtil.getUserAccount());

        } else if (StringUtils.equals("UPDATE", action)) {
            log.info("update  general Order and report ......");
            // 更新操作
            log.info("old  general Order is [" + JSON.toJSONString(generalOrderInstanceDTO) + "]");
            GeneralOrderInstanceInfoPO orderInstancePO = new GeneralOrderInstanceInfoPO();
            org.springframework.beans.BeanUtils.copyProperties(order, orderInstancePO);
            orderInstancePO.setID(generalOrderInstanceDTO.getID());
            orderInstancePO.setActiveIndicator(generalOrderInstanceDTO.getActiveIndicator());
            log.info("After transform   orderInstancePO is [" + JSON.toJSONString(orderInstancePO) + "]");
            orderInstancePO.setConclusionMode(generalOrderInstanceDTO.getConclusionMode());
            generalOrderInstanceInfoMapper.updateByPrimaryKeySelective(orderInstancePO);
            ReportInfoPO reportPO2=reportMapper.getReportInfoByReportNo(reportPO.getReportNo());
            if (ObjectUtils.isEmpty(reportPO2)) {
                // 新增report
                ReportTypeExample reportTypeExample=new ReportTypeExample();
                reportTypeExample.createCriteria().andDescriptionEqualTo("Original");
                List<ReportTypePO> reportTypePOs=reportTypeMapper.selectByExample(reportTypeExample);

                String typeID = "";
                if (CollectionUtils.isNotEmpty(reportTypePOs)) {
                    typeID = reportTypePOs.get(0).getID();
                }
                reportPO.setReportTypeID(typeID);
//                reportPO.setID(Func.randomUUID());
                String reportId = Func.isEmpty(createOrderReq.getReportId())?Func.randomUUID():createOrderReq.getReportId();
                reportPO.setID(reportId);
                reportPO.setActiveIndicator(true);
                reportPO.setIsDeliveryApprove(false);
                reportPO.setIsToDm(0);
                reportPO.setReportFlag(ReportFlagEnums.REPORT.getCode());
                reportPO.setCoverPageTemplateNewMappingFlag(false);
                reportPO.setRecalculationFlag(0);
                reportPO.setCreatedDate(new Date());
                reportPO.setTypingFinishedFlag(ActiveType.Disable.getStatus());
                reportPO.setCreatedBy(SecurityUtil.getUserAccount());
                if (StringUtils.equals("2", createOrderReq.getOperationType())) {
                    reportPO.setReportStatus(207);
                } else {
                    reportPO.setReportStatus(201);
                }
                reportPO.setSubReportReviseFlag(0);
                reportPO.setRootReportNo(createOrderReq.getReportNo());
                reportPO.setLabId(order.getLabId());
                reportPO.setTestResultStatus(ReportTestResultStatus.New.getCode());
                reportPO.setLabCode(order.getLabCode());
                //默认资质
                ReportDefaultAccreditationQueryReq reportDefaultAccreditationQueryReq = new ReportDefaultAccreditationQueryReq();
                reportDefaultAccreditationQueryReq.setOrderNoList(Sets.newHashSet(reportPO.getOrderNo()));
                reportDefaultAccreditationQueryReq.setProductLineCode(createOrderReq.getProductLineCode());
                BaseResponse<List<DefaultAccreditationRsp>> accreditationRsp = reportTempFacade.defaultAccreditationQuery(reportDefaultAccreditationQueryReq);
                if(accreditationRsp.isSuccess() && Func.isNotEmpty(accreditationRsp.getData())){
                    List<DefaultAccreditationRsp> defaultAccreditationRspList = accreditationRsp.getData();
                    DefaultAccreditationRsp defaultAcc = defaultAccreditationRspList.stream().filter(e -> Func.equalsSafe(e.getOrderNo(),reportPO.getOrderNo())).findAny().orElse(null);
                    if(Func.isNotEmpty(defaultAcc)){
                        reportPO.setCertificateId(defaultAcc.getCertificateId());
                        reportPO.setCertificateFileCloudKey(defaultAcc.getCertificateFileCloudKey());
                        reportPO.setCertificateName(defaultAcc.getCertificateName());
                    }
                }
                //默认Report Reviewer
                CustomerReportReviewerReq customerReportReviewerReq = new CustomerReportReviewerReq();
                customerReportReviewerReq.setLabCode(reportPO.getLabCode());
                customerReportReviewerReq.setRoleType("reportReviewer");
                //查询Customer信息
                OrderIdReq orderIdReq = new OrderIdReq();
                orderIdReq.setOrderNo(reportPO.getOrderNo());
                orderIdReq.setProductLineCode(createOrderReq.getProductLineCode());
                BaseResponse<List<CustomerInstanceDTO>> customerRsp = orderFacade.queryCustomerForPe(orderIdReq);
                if(customerRsp.isSuccess() && Func.isNotEmpty(customerRsp.getData())){
                    List<CustomerInstanceDTO> customerInstanceDTOList = customerRsp.getData();
                    CustomerInstanceDTO applicant = customerInstanceDTOList.stream().filter(e -> Func.equalsSafe(e.getCustomerUsage(), CustomerUsage.Applicant.getCode())).findAny().orElse(null);
                    CustomerInstanceDTO buyer = customerInstanceDTOList.stream().filter(e -> Func.equalsSafe(e.getCustomerUsage(),CustomerUsage.Buyer.getCode())).findAny().orElse(null);
                    if(Func.isNotEmpty(applicant)){
                        customerReportReviewerReq.setApplicantCustomerNo(applicant.getBossNumber());
                    }
                    if(Func.isNotEmpty(buyer)){
                        customerReportReviewerReq.setBuyerCustomerNo(buyer.getBossNumber());
                    }
                    List<String> customers = customerClient.queryCustomerReportReviewer(customerReportReviewerReq);
                    if(Func.isNotEmpty(customers)){
                        reportPO.setReviewerBy(customers.get(0));
                        // 查询中英文签名信息
                        UserSignatureQueryRequest userSignReq = new UserSignatureQueryRequest();
                        List<String> languageCodes = Lists.newArrayList(LanguageType.English.getCode(), LanguageType.Chinese.getCode());
                        userSignReq.setLanguageCodes(languageCodes);
                        userSignReq.setLabCode(reportPO.getLabCode());
                        userSignReq.setIsSignature("1");
                        userSignReq.setAuthSignTypes(Lists.newArrayList(SignTypeEnums.APPROVER.getCode(), SignTypeEnums.EDITOR.getCode(), SignTypeEnums.REVIEWER.getCode()));
                        List<UserSignatureDTO> userSignatureList = frameWorkClient.queryUserSinature(userSignReq);
                        SaveReportRequest request = new SaveReportRequest();
                        request.setReviewerBy(customers.get(0));
                        gpnReportService.setReportSignature(request, reportPO, SignTypeEnums.REVIEWER.getCode(), userSignatureList);
                    }
                }
                reportPO.setReportVersion(1);
                reportInfoMapper.insert(reportPO);
                //自动保存模板信息
                iGpnReportService.autoSearchTemplateSave(reportPO.getID(),ProductLineContextHolder.getProductLineCode(),SecurityUtil.getUserAccount());
            } else {
                // 更新report
                reportPO.setID(reportPO2.getID());
                reportPO.setModifiedDate(new Date());
                reportPO.setModifiedBy(SecurityUtil.getUserAccount());
                reportInfoMapper.updateByPrimaryKeySelective(reportPO);
            }
        }

        // 3. 处理sample
        Map<String, TestSampleInfoPO> sampleNoMap = new HashMap<String, TestSampleInfoPO>();
        if (StringUtils.isNotEmpty(createOrderReq.getOldOrderNo())) {
            TestSampleInfoExample objTestSampleInfoExample=new TestSampleInfoExample();
            objTestSampleInfoExample.createCriteria().andOrderNoEqualTo(createOrderReq.getOldOrderNo());
            List<TestSampleInfoPO> sampleList=testSampleInfoMapper.selectByExample(objTestSampleInfoExample);
            for (TestSampleInfoPO samplePO : sampleList) {
                if (StringUtils.equals(samplePO.getSampleType().toString(), "101")) {
                    sampleNoMap.put(samplePO.getSampleNo(), samplePO);
                }
            }
        }

        Map<String, TestSampleInfoPO> thisOrderSampleMap = new HashMap<String, TestSampleInfoPO>();
        if (StringUtils.isNotEmpty(createOrderReq.getOrderNo())) {
            TestSampleInfoExample objTestSampleInfoExample=new TestSampleInfoExample();
            objTestSampleInfoExample.createCriteria().andOrderNoEqualTo(createOrderReq.getOrderNo());
            List<TestSampleInfoPO> sampleList=testSampleInfoMapper.selectByExample(objTestSampleInfoExample);

            for (TestSampleInfoPO samplePO : sampleList) {
                if (StringUtils.equals(samplePO.getSampleType().toString(), "101")) {
                    thisOrderSampleMap.put(samplePO.getID(), samplePO);
                }
            }
        }

        List<SampleDTO> sampleDTOs = createOrderReq.getSampleList();
        List<TestSampleInfoPO> samplePOs = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(sampleDTOs)) {
            TestSampleInfoPO samplePO = new TestSampleInfoPO();
            // 1.新增的时候 action = NEW，排序后直接添加
            if ("NEW".equals(createOrderReq.getAction())) {
                // 排序
                sortSampleList(sampleDTOs);
                // 设置type和sampleSeq
                for (int i = 0; i < sampleDTOs.size(); i++) {
                    SampleDTO sampleDTO = sampleDTOs.get(i);
                    int tempO = 101 + i;
                    sampleDTO.setSampleType(101);
                    sampleDTO.setCategory("O");
                    sampleDTO.setOrderNo(createOrderReq.getOrderNo());
                    sampleDTO.setActiveIndicator(1);
                    sampleDTO.setSampleSeq(Integer.parseInt(tempO + "000000"));
                    if (MapUtils.isNotEmpty(sampleNoMap)) {
                        if (sampleNoMap.containsKey(sampleDTO.getSampleNo())) {
                            sampleDTO.setApplicable(sampleNoMap.get(sampleDTO.getSampleNo()).getApplicable()?1:0);
                        }
                    }
                }

                // sampleDTO转化成PO
                samplePOs = getTestSampleInfoPOS(sampleDTOs);
                samplePOs.forEach(e->{e.setActiveIndicator(true);e.setApplicable(false);});
                testSampleMapper.saveOrUpdateSamplesBatch(samplePOs);

            } else if ("UPDATE".equals(createOrderReq.getAction()) && createOrderReq.getSampleNOChange() == 0) {
                // 没有sampleNo的更新，只是更新sample其他字段信息
                // sampleDTO转化成PO
                samplePOs = getTestSampleInfoPOS(sampleDTOs);
                // 待优化成批量更新
                for (TestSampleInfoPO sam : samplePOs) {
                    sam.setCategory("O");
                    if(null!= sampleNoMap.get(sam.getSampleNo())){
                        sam.setApplicable(sampleNoMap.get(sam.getSampleNo()).getApplicable());
                    }
                    testSampleInfoMapper.updateByPrimaryKeySelective(sam);
                }
                // sampleDAO.updateSampleBatch(samplePOs);
            } else {
                // 排序并更新sample
                samplePOs = updateSample(createOrderReq, sampleDTOs, thisOrderSampleMap);
                testSampleMapper.saveOrUpdateSamplesBatch(samplePOs);
            }
        }
        if (action == "UPDATE"){
            sampleService.updateMixSampleNo(createOrderReq.getOrderNo());
        }
        // 4. 处理TestLine
/*        if(Func.isNotEmpty(createOrderReq.getTestLines())){
            ToTestReq toTestReq = new ToTestReq();
            toTestReq.setOrderNo(createOrderReq.getOrderNo());
            toTestReq.setToken(createOrderReq.getSgsToken());
            toTestReq.setTestLines(createOrderReq.getTestLines());
            CustomResult customResult=matrixService.toTest(toTestReq);
            if(!customResult.isSuccess()){
                log.info("totest is failed");
                return 0;
            }
        }*/

        log.info("End createGeneralOrder......");
        return 1;
    }

    @Override
    public BaseResponse queryReportConclusionForSplitReport(String reportNo,String isFrom) {
        BaseResponse<Object> objectBaseResponse = gpnReportService.queryReportConclusionForSplitReport(reportNo,isFrom);
        return objectBaseResponse;
    }
    @Override
    public BaseResponse getOrderInfo(String orderNo) {
        BaseResponse baseResponse = new BaseResponse();
        OrderIdReq orderIdReq = new OrderIdReq();
        orderIdReq.setOrderNo(orderNo);
        BaseResponse<MasterOrderInfoDTO> masterOrderInfo = orderFacade.getMasterOrderInfo(orderIdReq);
        baseResponse.setData(masterOrderInfo);
        return baseResponse;
    }

    @Override
    public GeneralOrderInstanceDTO getOrderByNo(String orderNo) {
        return orderQueryService.getOrderByNo(orderNo);
    }

    private List<TestSampleInfoPO> updateSample(CreateOrderReq preOrder, List<SampleDTO> sampleDTOs, Map<String, TestSampleInfoPO> sampleNoMap) {
        // 需要处理的sample信息，包括修改，更新，
        List<SampleDTO> actionSamples = new ArrayList<>();
        // 1.数据库order下的所以sample信息
        Map<String, Object> params = new HashMap<>();
        if (StringUtils.isEmpty(preOrder.getOrderNo())) {
            throw new BizException("updateSample fail!");
        }

        TestSampleInfoExample objTestSampleInfoExample=new TestSampleInfoExample();
        objTestSampleInfoExample.createCriteria().andOrderNoEqualTo(preOrder.getOrderNo());
        List<TestSampleInfoPO> orderSamples=testSampleInfoMapper.selectByExample(objTestSampleInfoExample);

        List<TestSampleInfoPO> samplePOs = new ArrayList<>();
        // 给接收到的sample集合，通过sampleNo排序
        sortSampleList(sampleDTOs);
        for (int i = 0; i < sampleDTOs.size(); i++) {
            SampleDTO sampleDTO = sampleDTOs.get(i);
            int tempO = 101 + i;
            sampleDTO.setSampleType(101);
            sampleDTO.setCategory("O");
            sampleDTO.setSampleSeq(Integer.parseInt(tempO + "000000"));
            sampleDTO.setOrderNo(preOrder.getOrderNo());
            sampleDTO.setActiveIndicator(1);
            sampleDTO.setApplicable(0);
            // id存在，需要修改当前的sampleSeq，还需要修改对应旗下的component，subcomponent，
            List<TestSampleInfoPO> components = changeSampleSeq(sampleDTO.getId(), tempO, orderSamples);
            if (CollectionUtils.isNotEmpty(components)) {
                samplePOs.addAll(components);
            }
            if(null!= sampleNoMap.get(sampleDTO.getId())){
                sampleDTO.setApplicable(sampleNoMap.get(sampleDTO.getId()).getApplicable()!=null&&sampleNoMap.get(sampleDTO.getId()).getApplicable()?1:0);
            }
            actionSamples.add(sampleDTO);
        }
        TestSampleInfoPO samplePO = new TestSampleInfoPO();
        // sampleDTO转化成PO
        List<TestSampleInfoPO> actionSamplePOs =getTestSampleInfoPOS(actionSamples);
        samplePOs.addAll(actionSamplePOs);

        return samplePOs;
    }

    /**
     * 更新sampleSeq
     *
     * @param id
     * @param sampleSeqPrefix
     * @param orderSamplesSou
     * @return
     */
    private List<TestSampleInfoPO> changeSampleSeq(String id, int sampleSeqPrefix, List<TestSampleInfoPO> orderSamplesSou) {
        // copy 集合
        List<TestSampleInfoPO> orderSamples = new ArrayList<>();
        for (TestSampleInfoPO spo : orderSamplesSou) {
            TestSampleInfoPO temp = new TestSampleInfoPO();
            try {
                temp = (TestSampleInfoPO) BeanUtils.cloneBean(spo);
            } catch (IllegalAccessException e) {
                // TODO Auto-generated catch block
                e.printStackTrace();
            } catch (InstantiationException e) {
                // TODO Auto-generated catch block
                e.printStackTrace();
            } catch (InvocationTargetException e) {
                // TODO Auto-generated catch block
                e.printStackTrace();
            } catch (NoSuchMethodException e) {
                // TODO Auto-generated catch block
                e.printStackTrace();
            }
            orderSamples.add(temp);
        }
        // Collections.copy(orderSamples, orderSamplesSou);

        // 1.找到主键id对应的Original 原样的sample信息
        List<TestSampleInfoPO> oriSamples = (List<TestSampleInfoPO>) CollectionUtils.select(orderSamples, new Predicate() {
            @Override
            public boolean evaluate(Object arg0) {
                // TODO Auto-generated method stub
                TestSampleInfoPO samplePO = (TestSampleInfoPO) arg0;
                return samplePO.getID().equals(id);
            }
        });
        // 2。理论上该原样一定存在
        if (CollectionUtils.isEmpty(oriSamples)) {
            return null;
        }
        // 3.获得的原样只有一条记录,获得原样对应的sampleSeq前缀
        String oriSamplePrefix = oriSamples.get(0).getSampleSeq().toString().substring(0, 3);
        // 4.通过原样前缀，过滤出原样对应下的component，subcomponent，,原样也会过滤出来
        List<TestSampleInfoPO> allSamples = (List<TestSampleInfoPO>) CollectionUtils.select(orderSamples, new Predicate() {
            @Override
            public boolean evaluate(Object arg0) {
                // TODO Auto-generated method stub
                TestSampleInfoPO sampleDTO = (TestSampleInfoPO) arg0;
                return oriSamplePrefix.equals(sampleDTO.getSampleSeq().toString().substring(0, 3));
            }
        });

        // 5.去掉原样信息
        List<TestSampleInfoPO> components = (List<TestSampleInfoPO>) CollectionUtils.subtract(allSamples, oriSamples);
        // 6.修改sampleSeq的前缀
        for (TestSampleInfoPO samplePO : components) {
            String seqTemp = samplePO.getSampleSeq().toString();

            samplePO.setSampleSeq(Integer.parseInt(sampleSeqPrefix + seqTemp.substring(3, 9)));
        }
        return components;
    }

    public List getTestSampleInfoPOS(List<SampleDTO> oldObjList) {

        List<TestSampleInfoPO> result = new ArrayList();
        for(SampleDTO objSampleDTO:oldObjList){
            TestSampleInfoPO objTestSampleInfoPO=new TestSampleInfoPO();
            Func.copy(objSampleDTO,objTestSampleInfoPO);
            objTestSampleInfoPO.setID(objSampleDTO.getId());
            objTestSampleInfoPO.setActiveIndicator(objSampleDTO.getActiveIndicator()==1);
            objTestSampleInfoPO.setApplicable(objSampleDTO.getApplicable()==1);
            result.add(objTestSampleInfoPO);
        }

        return result;
    }

    private void sortSampleList(List<SampleDTO> sampleDTOs) {
        // 对集合排序
        sampleDTOs.sort(Comparator.comparing(SampleDTO::getSort,Comparator.nullsLast(BigDecimal::compareTo)));
        logger.info("排序后结果2：{}",JSON.toJSONString(sampleDTOs));
//        Collections.sort(sampleDTOs, new Comparator<SampleDTO>() {
//            @Override
//            public int compare(SampleDTO arg0, SampleDTO arg1) {
//                // TODO Auto-generated method stub
//                // sampleNo 一定有值，转换成hashcode进行排序
//                // 返回正数代表arg0排在arg1后面，返回负数表示arg0排在arg1前面
//                return arg0.getSampleNo().hashCode() - arg1.getSampleNo().hashCode();
//            }
//        });
    }
    private GeneralOrderInstanceInfoPO convertOrderPO(CreateOrderReq preOrder) {
        GeneralOrderInstanceInfoPO order = new GeneralOrderInstanceInfoPO();
        order.setOrderStatus(1601);
        if (preOrder.getClient() != null) {
            order.setCustomerCode(preOrder.getClient().getClientCode());
            order.setCustomerName(preOrder.getClient().getClientName());
            order.setCustomerGroupCode(preOrder.getClient().getGroupCode());
            order.setCustomerGroupName(preOrder.getClient().getGroupName());
        }
        order.setApplicantCustomerNameEn(preOrder.getApplicantCustomerNameEn());
        order.setApplicantCustomerNameCN(preOrder.getApplicantCustomerNameCN());
        order.setApplicantCustomerCode(preOrder.getApplicantCustomerCode());
        order.setApplicantCustomerGroupCode(preOrder.getApplicantCustomerGroupCode());
        order.setApplicantCustomerGroupName(preOrder.getApplicantCustomerGroupName());
        order.setResponsibleTeamCode(preOrder.getResponsibleTeamCode());
        order.setCSName(preOrder.getCsName());
        order.setOrderNo(preOrder.getOrderNo());
        order.setLabId(preOrder.getLab().getLabId());
        order.setLabCode(preOrder.getLab().getLabCode());
        order.setOrderLaboratoryID(preOrder.getLab().getLabId());
        order.setSuffixNum(preOrder.getSuffixNum());
        return order;
    }

    private ReportInfoPO convertReportPO(CreateOrderReq preOrder) {
        ReportInfoPO reportPO = new ReportInfoPO();
        reportPO.setReportDueDate(preOrder.getReportDueDate());
        if (StringUtils.isBlank(preOrder.getReportNo()) || StringUtils.isBlank(preOrder.getOrderNo())) {
            throw new BizException("orderNo or reportNo is null");
        }
        reportPO.setReportNo(preOrder.getReportNo());

        String externalReportNo = externalNoClient.getExternalReportNo(preOrder.getReportNo(), ProductLineContextHolder.getProductLineCode());
        if(Func.isNotEmpty(externalReportNo)){
            reportPO.setActualReportNo(externalReportNo);
        }
        reportPO.setOrderNo(preOrder.getOrderNo());
        reportPO.setCustomerCode(preOrder.getClient() == null ? null : preOrder.getClient().getClientCode());
        reportPO.setCustomerGroupCode(preOrder.getClient() == null ? null : preOrder.getClient().getGroupCode());
        return reportPO;
    }

    @Override
    public CustomResult getSampleAndSampleGroupByOrderNo(OrderReq req){
        CustomResult result = new CustomResult();
        TestSampleInfoExample example = new TestSampleInfoExample();
        example.createCriteria().andOrderNoEqualTo(req.getOrderNo());
        List<TestSampleInfoPO> testSamplePOS = testSampleInfoMapper.selectByExample(example);
        List<TestSampleGroupPO> testSampleGroupPOS = testSampleMapper.querySampleGroupByOrderNo(req.getOrderNo());
        List<SampleRsp> testSampleRsps = new ArrayList<>();
        List<SampleGroupRsp> sampleGroupRsps = new ArrayList<>();
        testSamplePOS.forEach(item ->{
            SampleRsp testSampleRsp = new SampleRsp();
            org.springframework.beans.BeanUtils.copyProperties(item,testSampleRsp);
            testSampleRsp.setId(item.getID());
            testSampleRsps.add(testSampleRsp);
        });
        testSampleGroupPOS.forEach(item ->{
            SampleGroupRsp sampleGroupReq = new SampleGroupRsp();
            org.springframework.beans.BeanUtils.copyProperties(item,sampleGroupReq);
            sampleGroupRsps.add(sampleGroupReq);
        });
        SampleAndSampleGroupRsp sampleAndSampleGroupRsp = new SampleAndSampleGroupRsp();
        sampleAndSampleGroupRsp.setSampleGroupRsps(sampleGroupRsps);
        sampleAndSampleGroupRsp.setSampleRsps(testSampleRsps);
        result.setData(sampleAndSampleGroupRsp);
        result.setSuccess(true);
        return result;
    }
    @Override
    public CustomResult GPOUpdateOrderInfo(GpoUpdateOrderReq req){
        CustomResult result = new CustomResult();
        GeneralOrderInstanceDTO byNo = generalOrderInstanceMapper.getByNo(req.getOrderNo());
        GeneralOrderInstanceInfoPO generalOrderInstanceInfoPO = new GeneralOrderInstanceInfoPO();
        generalOrderInstanceInfoPO.setID(byNo.getId());
        generalOrderInstanceInfoPO.setOrderNo(req.getOrderNo());
        generalOrderInstanceInfoPO.setResponsibleTeamCode(req.getResponsibleTeamCode());
        generalOrderInstanceInfoPO.setCSName(req.getCsName());
        generalOrderInstanceInfoPO.setOrderStatus(req.getOrderStatus());
        generalOrderInstanceInfoPO.setModifiedBy(req.getUserName());
        generalOrderInstanceInfoPO.setModifiedDate(new Date());
        generalOrderInstanceInfoPO.setCustomerNameCn(req.getCustomerNameCn());
        generalOrderInstanceInfoPO.setCustomerGroupCode(req.getApplicantCustomerCode());
        generalOrderInstanceInfoPO.setCustomerGroupName(req.getApplicantCustomerGroupName());
        generalOrderInstanceInfoPO.setApplicantCustomerNameCN(req.getApplicantCustomerNameCN());
        generalOrderInstanceInfoPO.setApplicantCustomerNameEn(req.getApplicantCustomerNameEn());
        generalOrderInstanceInfoPO.setSampleDescription(req.getSampleDescription());
        generalOrderInstanceInfoPO.setReturnSample(req.getReturnSample());
        generalOrderInstanceInfoPO.setTechnicalSupporter(req.getTechnicalSupporter());
        generalOrderInstanceInfoMapper.updateByPrimaryKeySelective(generalOrderInstanceInfoPO);
        result.setSuccess(true);
        return result;
    }

    private void cancleTestLine(GeneralOrderInstanceInfoPO order, UserInfo user, String token)  {
        String generalOrderInstanceID=order.getID();
        String modifiedBy=user.getRegionAccount();
        TestLineInstancePO testLineInstancePO = new TestLineInstancePO();
        testLineInstancePO.setTestLineStatus(706);
        testLineInstancePO.setActiveIndicator(false);
        testLineInstancePO.setGeneralOrderInstanceID(generalOrderInstanceID);
        testLineInstancePO.setModifiedBy(modifiedBy);
        testLineMapper.cancelTestLineByGeneralOrderId(testLineInstancePO);

        TestMatrixPO testMatrixPO=new TestMatrixPO();
        testMatrixPO.setActiveIndicator(false);
        testMatrixPO.setGeneralOrderInstanceID(generalOrderInstanceID);
        testMatrixPO.setModifiedBy(modifiedBy);
        testMatrixMapper.cancelMatrixByGeneralOrderId(testMatrixPO);

        ConclusionInfoPO conclusionInfoPO = new ConclusionInfoPO();
        conclusionInfoPO.setActiveIndicator(false);
        conclusionInfoPO.setGeneralOrderInstanceID(generalOrderInstanceID);
        conclusionInfoPO.setModifiedBy(modifiedBy);
        conclusionMapper.cancelConclusionByGeneralOrderInstanceID(conclusionInfoPO);

        Map<String, Object> params=new HashMap<String, Object>();
        params.put("modifiedBy", modifiedBy);
        params.put("generalOrderInstanceID", generalOrderInstanceID);
        jobExtMapper.updateJobStateByGeneralOrderInstanceID(params);
    }

    private void cancleSample(GeneralOrderInstanceInfoPO order, UserInfo user,String type)  {
        List<TestSampleGroupPO> testSampleGroupPOS = testSampleMapper.querySampleGroupByOrderNo(order.getOrderNo());
        if (CollectionUtils.isNotEmpty(testSampleGroupPOS)) {
            for(TestSampleGroupPO sampleGroupPO:testSampleGroupPOS){
                sampleGroupPO.setActiveIndicator(false);
                sampleGroupPO.setModifiedBy(user.getRegionAccount());
            }
            testSampleMapper.batchUpdateSampleGroupStatus(testSampleGroupPOS);
        }

        TestSampleInfoPO testSampleInfoPO = new TestSampleInfoPO();
        testSampleInfoPO.setActiveIndicator(false);
        testSampleInfoPO.setModifiedBy(user.getRegionAccount());
        testSampleInfoPO.setOrderNo(order.getOrderNo());
        testSampleMapper.cancelSampleByOrderNO(testSampleInfoPO);
    }

    private void cancleReport(GeneralOrderInstanceInfoPO order, UserInfo user) {
        ReportInfoExample reportInfoExample = new ReportInfoExample();
        reportInfoExample.createCriteria().andOrderNoEqualTo(order.getOrderNo());
        List<ReportInfoPO> RpList = reportInfoMapper.selectByExample(reportInfoExample);
        if (RpList != null && RpList.size() > 0) {
            for (ReportInfoPO reportPO : RpList) {
                reportPO.setActiveIndicator(false);
                reportPO.setReportStatus(202);
                reportInfoMapper.updateByPrimaryKeySelective(reportPO);
            }
        }
    }

    private CustomResult<List<String>>  updateReductionSubContract(GeneralOrderInstanceInfoPO order, UserInfo user) {
        CustomResult result = new CustomResult<>();
        String execOrderNo = order.getOrderNo();
        //去slimsub表中查找是否有绑定sub
        SubContractExternalRelationshipPO subContractSlimJobPO = new SubContractExternalRelationshipPO();
        subContractSlimJobPO.setExternalNo(execOrderNo);
        List<SubContractExternalRelationshipPO> list = subContractExternalRelService.getSubContractExternalRels(subContractSlimJobPO).getData();

        SubContractExternalRelationshipPO po = null;
        if(Func.isNotEmpty(list)&&list.size()>0){
            po = list.get(0);
        }

        //说明没有内部分包，可能是父单，需要查询下是否有子单内部分包
        if(po==null){
            List<String> orderList =  this.checkAndGetExecOrder(order.getOrderNo());
            result.setSuccess(true);
            result.setData(orderList);
            return result;
        }
        String subContractNo = po.getSubContractNo();
        //根据分包单号，找到父单
        SubContractExample subContractExample = new SubContractExample();
        subContractExample.createCriteria().andSubContractNoEqualTo(subContractNo);
        List<SubContractPO> subContractPOList = subContractMapper.selectByExample(subContractExample);
        if(CollectionUtils.isEmpty(subContractPOList)){
            logger.info("{} Cancel,执行内部分包单cancel异常，slimJob表有关联数据 subNO:{}，但是找不到subContract数据",execOrderNo,subContractNo);
            //throw new SGSException("EC-0001","Query subContract response null by "+subContractNo);
            result.setSuccess(false);
            result.setMsg("Query subContract response null by "+subContractNo);
            return result;
        }
        SubContractPO subContractPO = subContractPOList.get(0);
        String parentOrderNo = subContractPO.getOrderNo();
        //校验当前父单是否已经cancel，因为进入cancel的接口的可能是父单执行cancel，然后preorder执行循环cancel子单，这个时候就不能再继续执行后续方法
        GeneralOrderInstanceInfoExample generalOrderInstanceInfoExample = new GeneralOrderInstanceInfoExample();
        generalOrderInstanceInfoExample.createCriteria().andOrderNoEqualTo(parentOrderNo);
        List<GeneralOrderInstanceInfoPO> generalOrderInstanceInfoPOS = generalOrderInstanceInfoMapper.selectByExample(generalOrderInstanceInfoExample);
        GeneralOrderInstanceInfoPO parentOrder = generalOrderInstanceInfoPOS.get(0);
        Boolean activeIndicator = parentOrder.getActiveIndicator();
        if(!activeIndicator){
            logger.info("orderNo{}的父单：{}已经是cancel状态，不继续更新父单的sub,tl等信息", execOrderNo,parentOrderNo);
            result.setSuccess(true);
            return result;
        }

        String subContractPOId = subContractPO.getID();
        List<String> tlIds = subContractExtMapper.queryTestLineIdBySubContractId(subContractPOId);
        if(CollectionUtils.isEmpty(tlIds)){
            logger.info("{} cancel,执行内部分包cancel异常，找到了分包单:{}，但是找不到分包单关联的tl",execOrderNo,subContractNo);
            //throw new SGSException("EC-0001","Query Testline response null by subcontractNo:"+subContractNo);
            result.setSuccess(false);
            result.setMsg("Query Testline response null by subcontractNo:"+subContractNo);
            return result;
        }
        //数据准备完毕，执行更新

        //更新当前分包单状态为new ,就是重置数据
        subContractExtMapper.updateStatusForCancelOrder(subContractPO.getID());
        SubContractPO sub = subContractMapper.selectByPrimaryKey(subContractPO.getID());
        TodoStatusReq todoStatusReq = new TodoStatusReq();
        todoStatusReq.setObjectId(sub.getID());
        todoStatusReq.setObjectNo(sub.getSubContractNo());
        todoStatusReq.setSgsToken(tokenClient.getToken());
        todoStatusReq.setProductLineCode(ProductLineContextHolder.getProductLineCode());
        // Martin.Wang 2020-12-14 Update
        // 判断是分包方还是接包方进行的Cancel，如果是分包方，则将状态设置为 40(Canceled)；如果是接包方，则将状态设置为 10(Todo)
        OrderInfoDto orderInfoDto = orderClient.getOrderInfoByOrderNo(order.getOrderNo());
        if (Func.isNotEmpty(orderInfoDto) && OperationType.SubContract.getStatus() == orderInfoDto.getOperationType()) {
            todoStatusReq.setStatus(TodoStatus.Todo.getCode());
        } else {
            todoStatusReq.setStatus(TodoStatus.Canceled.getCode());
        }
        toDoListFacade.updateTodoStatus(todoStatusReq);
        //批量更新tl状态为typing 701
        for (String tlId : tlIds) {
            TestLineInstancePO testLineInstancePO = new TestLineInstancePO();
            testLineInstancePO.setID(tlId);
            testLineInstancePO.setTestLineStatus(TestLineStatus.Typing.getStatus());
            testLineInstanceMapper.updateByPrimaryKeySelective(testLineInstancePO);
        }
        //删除slim的数据
        subContractExtMapper.delSubContractSlimJobId(po.getID());
        result.setSuccess(true);
        return result;
    }

    private List<String> checkAndGetExecOrder(String oldOrderNo){
        //根据分包单号，找到父单
        SubContractExample subContractExample = new SubContractExample();
        subContractExample.createCriteria().andOrderNoEqualTo(oldOrderNo);
        List<SubContractPO> subContractPOList = subContractMapper.selectByExample(subContractExample);
        //可能没有分包数据
        if(CollectionUtils.isEmpty(subContractPOList)){
            return new ArrayList<>();
        }

        List<String> orderNoList = new ArrayList<>();
        for (SubContractPO subContractPO : subContractPOList) {
            //只找当前单是内部分包的父单
            Integer subContractOrder = subContractPO.getSubContractOrder();
            if(subContractOrder == null || subContractOrder.compareTo(1)!=0){
                continue;
            }
            String subContractNo = subContractPO.getSubContractNo();
            SubContractExternalRelationshipPO subContractSlimJobPO = new SubContractExternalRelationshipPO();
            subContractSlimJobPO.setSubContractNo(subContractNo);
            List<SubContractExternalRelationshipPO> list = subContractExternalRelService.getSubContractExternalRels(subContractSlimJobPO).getData();
            //去slimsub表中查找是否有绑定sub
            SubContractExternalRelationshipPO po = null;
            if(Func.isNotEmpty(list)&&list.size()>0){
                po = list.get(0);
                String slimJobNo = po.getExternalNo();
                orderNoList.add(slimJobNo);
            }
        }
        return orderNoList;
    }

    public void cancelChildOrder(List<String> childOrderNo,UserInfo user){
        if(CollectionUtils.isNotEmpty(childOrderNo)){
            for (String orderNo : childOrderNo) {
                GeneralOrderInstanceInfoExample generalOrderInstanceInfoExample = new GeneralOrderInstanceInfoExample();
                generalOrderInstanceInfoExample.createCriteria().andOrderNoEqualTo(orderNo);
                List<GeneralOrderInstanceInfoPO> generalOrderInstanceInfoPOS = generalOrderInstanceInfoMapper.selectByExample(generalOrderInstanceInfoExample);
                GeneralOrderInstanceInfoPO order = generalOrderInstanceInfoPOS.get(0);
                if (order == null) {
                    throw new RuntimeException(Prompt.CANCEL_ORDER_E0);
                }

                this.cancleTestLine(order, user, "");
                this.cancleSample(order, user,"order");
                this.cancleReport(order, user);

                // 更新JOB状态为1104
                Map<String, Object> params = new HashMap<>();
                params.put("generalOrderInstanceID", order.getID());
                params.put("modifiedBy", user.getRegionAccount());
                jobExtMapper.updateJobStateByGeneralOrderInstanceID(params);

                GeneralOrderInstanceInfoPO orderPo = new GeneralOrderInstanceInfoPO();
                org.springframework.beans.BeanUtils.copyProperties(order, orderPo);
                orderPo.setActiveIndicator(false);
                generalOrderInstanceInfoMapper.updateByPrimaryKeySelective(orderPo);

                // 放到redis中，页面每次查询的时候不用都请求DB
                String redisKey = "order_cancel_:" + orderNo;
                // 存储24h
                CacheAgent.getInstance().set(redisKey, orderPo, 60 * 60 * 24);
            }
        }
    }


    @Override
    public BaseResponse<String> closeOrderForTestOnly(OrderIdReq orderIdReq){
        // 判断TestOnly

        BaseResponse<OrderAllDTO> orderInfoRepBaseResponse = orderFacade.getOrderForPe(orderIdReq);
        OrderAllDTO data = orderInfoRepBaseResponse.getData();
        if (Func.isNotEmpty(data) && Func.equals(OperationModeEnums.TESTING_ONLY.getStatus(),data.getOperationMode())) {
            // 判断ALL TL Complated
            GeneralOrderInstanceInfoPO orderInfo = orderMapper.getOrderInfo(orderIdReq.getOrderNo());
            List<Integer> TestLineStatusList = com.beust.jcommander.internal.Lists.newArrayList();
            TestLineStatusList.add(TestLineStatus.Cancelled.getStatus());
            TestLineStatusList.add(TestLineStatus.NC.getStatus());
            TestLineStatusList.add(TestLineStatus.NA.getStatus());
            TestLineInstanceExample testLineInstanceExample = new TestLineInstanceExample();
            testLineInstanceExample.createCriteria().andGeneralOrderInstanceIDEqualTo(orderInfo.getID()).andTestLineStatusNotIn(TestLineStatusList);
            List<TestLineInstancePO> testLineInstancePOS1 = testLineInstanceMapper.selectByExample(testLineInstanceExample);
            boolean flag = testLineInstancePOS1.stream().anyMatch(item -> TestLineStatus.Completed.getStatus() != item.getTestLineStatus().intValue());
            if (!flag) {
                String regionAccount = "system";
                UserInfo user = tokenClient.getUser();
                if(Func.isNotEmpty(user)){
                    regionAccount = user.getRegionAccount();
                }
                // Cancel 所有Report
                // Cancel OTSNotes Report
                ReportInfoPO reportInfoPO = new ReportInfoPO();
                reportInfoPO.setOrderNo(orderIdReq.getOrderNo());
                reportInfoPO.setModifiedBy(regionAccount);
                reportInfoPO.setModifiedDate(DateUtils.getNow());
                reportMapper.cancelReportByOrderNo(reportInfoPO);
                // Cancel PreOrder Report
                com.sgs.preorder.facade.model.req.report.ReportStatusReq reportStatusReq = new ReportStatusReq();
                reportStatusReq.setOrderNo(orderIdReq.getOrderNo());
                reportStatusReq.setProductLineCode(ProductLineContextHolder.getProductLineCode());
                reportStatusReq.setToken(SecurityUtil.getSgsToken());
                reportStatusReq.setModifiedBy(SecurityUtil.getUserAccount());
                reportStatusReq.setNewReportStatus(ReportStatus.Cancelled.getCode());
                BaseResponse baseResponse1 = orderReportFacade.updateReportStatusByOrderNo(reportStatusReq);
                if(ResponseCode.SUCCESS.getCode()==baseResponse1.getStatus()){
                    // 更新OTSNotes关单
                    UpdateOrderStatusReq updateOrderStatusReq = new UpdateOrderStatusReq();
                    updateOrderStatusReq.setOrderNo(orderIdReq.getOrderNo());
                    updateOrderStatusReq.setOrderStatus(PreOrderStatus.Completed.getStatus());
                    updateOrderStatusReq.setToken(Func.isNotEmpty(orderIdReq.getToken())?orderIdReq.getToken():SecurityContextHolder.getSgsToken());
                    orderFacade.updateOrderStatus(updateOrderStatusReq);
                    // 执行关单逻辑
                    BaseResponse baseResponse = orderFacade.closeOrder(orderIdReq);
                    if(ResponseCode.SUCCESS.getCode()!=baseResponse.getStatus()){
                        return BaseResponse.newFailInstance(baseResponse.getMessage());
                    }
                }else{
                    return BaseResponse.newFailInstance(baseResponse1.getMessage());
                }
            }
        }
        return BaseResponse.newSuccessInstance(ResponseCode.SUCCESS.getMessage());
    }

    /**
     * todolist数量查询
     * @return
     */
    @Override
    public BaseResponse getToDoListCount(OrderTestLineListSearchReq orderTestLineListSearchReq) {
        BaseResponse baseResponse = new BaseResponse();
        baseResponse.setStatus(ResponseCode.SUCCESS.getCode());
        orderTestLineListSearchReq.setTestLineStatus(TestLineStatus.Typing.getStatus());
        orderTestLineListSearchReq.setLabCode(tokenClient.getUserLabCode());
        Long count = orderTestLineVMExtMapper.getOrderTestLineListCount(orderTestLineListSearchReq);
        if (Func.isNotEmpty(count)){
            baseResponse.setData(count);
        }else {
            baseResponse.setData(0);
        }
        return baseResponse;
    }

    @Override
    public BaseResponse<MaxExpectDueDateInfo> getMaxDueDate(MaxExpectDueDateInfo dueDateReq) {
        BaseResponse<MaxExpectDueDateInfo> response = new BaseResponse<>();
        if(Func.isEmpty(dueDateReq.getOrderNo())){
            return response;
        }
        // 查询Report Max Due 并按照TL分组
        String orderNo = dueDateReq.getOrderNo();
        List<GpnQuotationReportDTO> reportList = reportMapper.queryReportByOrderNo(orderNo);
        if(Func.isNotEmpty(reportList)){
            reportList = reportList.stream().filter(report->Func.isNotEmpty(report.getReportDueDate())
                    &&ReportStatus.checkCategory(report.getReportStatus(), Constants.REPORT.STATUS_CATEGORY.EDIT, Constants.REPORT.STATUS_CATEGORY.LOCK))
                    .collect(Collectors.toList());
            if(Func.isNotEmpty(reportList)){
                List<ReportExpectDueDateInfo> reportExpectDueDateList = Lists.newArrayList();
                reportList.stream().forEach(report ->{
                    ReportExpectDueDateInfo reportExpectDueDateInfo = new ReportExpectDueDateInfo();
                    reportExpectDueDateInfo.setReportId(reportExpectDueDateInfo.getReportId());
                    reportExpectDueDateInfo.setExpectDueDateInfo(report.getReportDueDate());
                    // 查询Report对应的Tl
                    List<TestLineInstancePO> tlList = reportMapper.getTestLineListByReport(report.getReportId());
                    if(Func.isNotEmpty(tlList)){
                        tlList = tlList.stream().filter(tl -> Func.isNotEmpty(tl.getTestDueDate())&&
                                !TestLineStatus.check(tl.getTestLineStatus(),TestLineStatus.Completed,TestLineStatus.Cancelled)).collect(Collectors.toList());
                        if(Func.isNotEmpty(tlList)){
                            reportExpectDueDateInfo.setTestLineInstanceIdList(tlList.stream().map(tl->tl.getID()).collect(Collectors.toList()));
                        }
                    }
                    reportExpectDueDateList.add(reportExpectDueDateInfo);
                });
                dueDateReq.setReportExpectDueDateList(reportExpectDueDateList);
            }
        }
        List<JobStatusDTO> jobList = jobExtMapper.getJobStatus(orderNo);
        if(Func.isNotEmpty(jobList)){
            jobList = jobList.stream().filter(job->Func.isNotEmpty(job.getExpectedDueDate())
                    &&!JobStatus.check(job.getJobStatus(),JobStatus.Cancelled,JobStatus.Completed,JobStatus.Closed)).collect(Collectors.toList());
            if(Func.isNotEmpty(jobList)){
                List<JobExpectDueDateInfo> jobExpectDueDateList = Lists.newArrayList();
                jobList.stream().forEach(job->{
                    JobExpectDueDateInfo jobExpectDueDateInfo = new JobExpectDueDateInfo();
                    jobExpectDueDateInfo.setJobId(job.getJobId());
                    jobExpectDueDateInfo.setExpectDueDateInfo(job.getExpectedDueDate());
                    JobInfoDTO jobInfoDTO = new JobInfoDTO();
                    jobInfoDTO.setOrderNo(dueDateReq.getOrderNo());
                    jobInfoDTO.setJobNo(job.getJobNo());
                    List<JobDTO> jobTlList = jobExtMapper.queryJobTLStatus(jobInfoDTO);
                    if(Func.isNotEmpty(jobTlList)){
                        jobTlList = jobTlList.stream().filter(tl -> Func.isNotEmpty(tl.getTestDueDate())&&
                                !TestLineStatus.check(tl.getTestLineStatus(),TestLineStatus.Completed,TestLineStatus.Cancelled)).collect(Collectors.toList());
                        if(Func.isNotEmpty(jobTlList)){
                            jobExpectDueDateInfo.setTestLineInstanceIdList(jobTlList.stream().map(tl->tl.getTestLineInstanceId()).collect(Collectors.toList()));
                        }
                    }
                    jobExpectDueDateList.add(jobExpectDueDateInfo);
                });
                dueDateReq.setJobExpectDueDateList(jobExpectDueDateList);
            }
        }
        List<EmSubContractDTO> subContractList = subContractExtMapper.getSubContractInfoList(orderNo);
        if(Func.isNotEmpty(subContractList)){
            List<SubcontractExpectDueDateInfo> subcontractExpectDueDateList = Lists.newArrayList();
            subContractList.stream().forEach(subcontract->{
                if(SubContractStatus.check(subcontract.getStatus(),SubContractStatus.PENDING,SubContractStatus.CANCELLED,SubContractStatus.REPORT_COMPLETED)){
                    return;
                }
                SubcontractExpectDueDateInfo subcontractExpectDueDateInfo = new SubcontractExpectDueDateInfo();
                subcontractExpectDueDateInfo.setExpectDueDateInfo(subcontract.getSubContractExpectDueDate());
                List<String> testLineIds = subContractExtMapper.queryTestLineIdBySubContractId(subcontract.getId());
                if(Func.isNotEmpty(testLineIds)){
                    subcontractExpectDueDateInfo.setTestLineInstanceIdList(testLineIds);
                }
                subcontractExpectDueDateList.add(subcontractExpectDueDateInfo);
            });
            dueDateReq.setSubcontractExpectDueDateList(subcontractExpectDueDateList);
        }

        List<TestLineInstancePO> tlList = testLineMapper.getTestLineByOrderNo(orderNo);
        if(Func.isNotEmpty(tlList)){
            tlList = tlList.stream().filter(tl -> Func.isNotEmpty(tl.getTestDueDate())&&
                    !TestLineStatus.check(tl.getTestLineStatus(),TestLineStatus.Completed,TestLineStatus.Cancelled)).collect(Collectors.toList());
            if(Func.isNotEmpty(tlList)){
                List<TestLineExpectDueDateInfo> testLineExpectDueDateList = Lists.newArrayList();
                tlList.stream().forEach(tl->{
                    TestLineExpectDueDateInfo testLineExpectDueDateInfo = new TestLineExpectDueDateInfo();
                    testLineExpectDueDateInfo.setTestLineExpectDueDate(tl.getTestDueDate());
                    testLineExpectDueDateInfo.setTestLineInstanceId(tl.getID());
                    testLineExpectDueDateList.add(testLineExpectDueDateInfo);
                });
                dueDateReq.setTestLineExpectDueDateList(testLineExpectDueDateList);
            }
        }
        response.setData(dueDateReq);
        return response;
    }

    @Override
    @Transactional(rollbackFor = {Exception.class})
    public BaseResponse<List<SaveTestLineMatrixRsp>> saveTestLineAndMatrix(SaveTestLineAndMatrixReq saveTestLineAndMatrixReq) {
        log.info("saveTestLineAndMatrix req:{}",JSON.toJSONString(saveTestLineAndMatrixReq));
        if(Func.isEmpty(saveTestLineAndMatrixReq)){
            return BaseResponse.newFailInstance("param can not null");
        }
        if(Func.isEmpty(saveTestLineAndMatrixReq.getOrderNo())){
            return BaseResponse.newFailInstance("param OrderNo is required!");
        }
        if(Func.isEmpty(saveTestLineAndMatrixReq.getTestLineList())){
            return BaseResponse.newFailInstance("param testLineList is required!");
        }
        ToTestAndAssignSampleReq toTestAndAssignSampleReq = new ToTestAndAssignSampleReq();
        toTestAndAssignSampleReq.setOrderNo(saveTestLineAndMatrixReq.getOrderNo());
        List<SaveTestLineAndMatrixReq.TestLineList> testLineList = saveTestLineAndMatrixReq.getTestLineList();
        List<ToTLAndAssignSampleReq> toTLAndAssignSampleReqs = new ArrayList<>();
        //根据MatrixSample查询Sample
        List<TestSampleInfoPO> testSampleList = null;
        if(Func.isNotEmpty(testLineList)){
            List<String> testSampleIdList = testLineList.stream().filter(tl->Func.isNotEmpty(tl.getTestMatrixSampleList())).flatMap(tl -> tl.getTestMatrixSampleList().stream()).map(SaveTestLineAndMatrixReq.TestLineList.TestMatrixSampleList::getSampleId).filter(Func::isNotEmpty).distinct().collect(Collectors.toList());
            if(Func.isNotEmpty(testSampleIdList)){
                testSampleList = testSampleMapper.getTestSampleIdList(testSampleIdList);
            }
        }
        for (SaveTestLineAndMatrixReq.TestLineList saveTestLineItem : testLineList) {
            ToTLAndAssignSampleReq toTLAndAssignSampleReq =  new ToTLAndAssignSampleReq();
            SaveTestLineAndMatrixReq.TestLineList.PpTestLineRel ppTestLineRel = saveTestLineItem.getPpTestLineRel();
            toTLAndAssignSampleReq.setTestLineId(saveTestLineItem.getTestLineId());
            toTLAndAssignSampleReq.setTestLineVersionId(saveTestLineItem.getTestLineVersionId());
            toTLAndAssignSampleReq.setTestLineInstanceId(saveTestLineItem.getTestLineInstanceId());
            if(Func.isNotEmpty(ppTestLineRel)){
                toTLAndAssignSampleReq.setPpBaseId(ppTestLineRel.getPpBaseId());
                toTLAndAssignSampleReq.setPpVersionId(ppTestLineRel.getPpVersionId());
                toTLAndAssignSampleReq.setAid(ppTestLineRel.getAid());

                toTLAndAssignSampleReq.setSectionName(ppTestLineRel.getSectionName());
                toTLAndAssignSampleReq.setSectionId(ppTestLineRel.getSectionId());
                List<SaveTestLineAndMatrixReq.TestLineList.TestMatrixSampleList> testMatrixSampleList = saveTestLineItem.getTestMatrixSampleList();
                if(Func.isNotEmpty(testMatrixSampleList)){
                    List<String> sampleIdList = testMatrixSampleList.stream().map(SaveTestLineAndMatrixReq.TestLineList.TestMatrixSampleList::getSampleId).distinct().collect(Collectors.toList());
                    if(Func.isNotEmpty(testSampleList) && Func.isNotEmpty(sampleIdList)){
                        List<String> sampleNos = testSampleList.stream().filter(item -> sampleIdList.contains(item.getID())).map(TestSampleInfoPO::getSampleNo).collect(Collectors.toList());
                        toTLAndAssignSampleReq.setSampleNos(sampleNos);
                    }
                }
            }
            if(Func.isNotEmpty(saveTestLineItem.getCitation()) && Func.isNotEmpty(saveTestLineItem.getCitation().getCitationBaseId())){
                toTLAndAssignSampleReq.setCitationBaseId(saveTestLineItem.getCitation().getCitationBaseId());
            }
            toTLAndAssignSampleReqs.add(toTLAndAssignSampleReq);
        }
        toTestAndAssignSampleReq.setPpTestLineInfos(toTLAndAssignSampleReqs);
        toTestAndAssignSampleReq.setUpdateMatrixFlag(true);
        BaseResponse<ToTestAndAssignSampleRsp> toTestAndAssignSampleResponse = iTestMatrixService.toTestAndAssignSampleReq(toTestAndAssignSampleReq);
        logger.info("toTestAndAssignSampleResponse result:{}",JSON.toJSONString(toTestAndAssignSampleResponse));
        if(toTestAndAssignSampleResponse.isFail()){
            return BaseResponse.newFailInstance(toTestAndAssignSampleResponse.getMessage());
        }
        ConfirmMatrixReq confirmMatrixReq = new ConfirmMatrixReq();
        confirmMatrixReq.setOrderNo(saveTestLineAndMatrixReq.getOrderNo());
        CustomResult confirmMatrixResult = testMatrixService.confirmMatrix(confirmMatrixReq);
        if(!confirmMatrixResult.isSuccess()){
            throw new BizException(confirmMatrixResult.getMsg());
        }
        List<SaveTestLineMatrixRsp> saveTestLineMatrixRsps = new ArrayList<>();
        //查询TestLine
        OrderTestMatrixReq orderTestMatrixReq = new OrderTestMatrixReq();
        orderTestMatrixReq.setOrderNo(saveTestLineAndMatrixReq.getOrderNo());
        CustomResult<List<OrderTestMatrixInfo>> orderTestMatriesResult = iTestMatrixService.getOrderTestMatrixes(orderTestMatrixReq);
        if(Func.isNotEmpty(orderTestMatriesResult) && Func.isNotEmpty(orderTestMatriesResult.getData())){
            List<OrderTestMatrixInfo> orderTestMatrixInfoList = orderTestMatriesResult.getData();
            for (OrderTestMatrixInfo orderTestMatrixInfo : orderTestMatrixInfoList) {
                SaveTestLineMatrixRsp saveTestLineMatrixRsp = new SaveTestLineMatrixRsp();
                saveTestLineMatrixRsp.setTestMatrixId(orderTestMatrixInfo.getMatrixId());
                saveTestLineMatrixRsp.setTestLineInstanceId(orderTestMatrixInfo.getTestLineInstanceId());
                saveTestLineMatrixRsp.setTestLineId(orderTestMatrixInfo.getTestLineId());
                saveTestLineMatrixRsp.setTestLineVersionId(orderTestMatrixInfo.getTestLineVersionId());
                saveTestLineMatrixRsp.setTestSampleInstanceId(orderTestMatrixInfo.getSampleId());
                saveTestLineMatrixRsp.setSampleNo(orderTestMatrixInfo.getSampleNo());
                saveTestLineMatrixRsps.add(saveTestLineMatrixRsp);
            }
        }
        logger.info("confirmMatrix result:{}",JSON.toJSONString(confirmMatrixResult));
        return BaseResponse.newSuccessInstance(saveTestLineMatrixRsps);
    }
    @Override
    public BaseResponse<List<ObjectBarcodeRsp>> queryObjectNoList(OrderIdReq orderIdReq){
        if(Func.isEmpty(orderIdReq) || Func.isEmpty(orderIdReq.getOrderNos())){
            return BaseResponse.newFailInstance("param.miss.placeholder",new Object[]{"OrderNos"});
        }
        return BaseResponse.newSuccessInstance(generalOrderInstanceMapper.queryObjectNoList(orderIdReq));
    }
}
