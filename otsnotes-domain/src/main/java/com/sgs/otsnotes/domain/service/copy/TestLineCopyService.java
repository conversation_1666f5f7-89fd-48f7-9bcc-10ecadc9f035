package com.sgs.otsnotes.domain.service.copy;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.sgs.core.domain.UserInfo;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.core.base.CustomResult;
import com.sgs.framework.core.exception.BizException;
import com.sgs.framework.facade.domain.rsp.BuParamValueRsp;
import com.sgs.framework.model.enums.LanguageType;
import com.sgs.framework.model.enums.TestLineType;
import com.sgs.framework.security.utils.SecurityUtil;
import com.sgs.framework.tool.utils.Func;
import com.sgs.framework.tool.utils.ProductLineContextHolder;
import com.sgs.otsnotes.core.common.UserHelper;
import com.sgs.otsnotes.core.constants.Constants;
import com.sgs.otsnotes.core.thread.ThreadPoolContextTaskExecutor;
import com.sgs.otsnotes.core.util.DateUtils;
import com.sgs.otsnotes.core.util.NumberUtil;
import com.sgs.otsnotes.core.util.StringUtil;
import com.sgs.otsnotes.dbstorages.mybatis.extmapper.*;
import com.sgs.otsnotes.dbstorages.mybatis.extmodel.TestLineCopyInfo;
import com.sgs.otsnotes.dbstorages.mybatis.model.*;
import com.sgs.otsnotes.domain.service.OrderLangRelService;
import com.sgs.otsnotes.domain.service.ProtocolPackageTestLineRelationshipService;
import com.sgs.otsnotes.domain.service.TestLineCmdService;
import com.sgs.otsnotes.domain.service.TestLineQueryService;
import com.sgs.otsnotes.domain.service.testline.TestLineStatusService;
import com.sgs.otsnotes.domain.service.trimslocal.TrimsLocalTestLineService;
import com.sgs.otsnotes.domain.util.CodeUtil;
import com.sgs.otsnotes.facade.model.annotation.CopyServiceType;
import com.sgs.otsnotes.facade.model.enums.*;
import com.sgs.otsnotes.facade.model.info.FileInfo;
import com.sgs.otsnotes.facade.model.info.artifact.NewPPArtifactRelInfo;
import com.sgs.otsnotes.facade.model.info.citation.NewArtifactCitationRelInfo;
import com.sgs.otsnotes.facade.model.info.testline.NewPpTestLineInfo;
import com.sgs.otsnotes.facade.model.info.testline.NewTestLineInfo;
import com.sgs.otsnotes.facade.model.info.user.UserLabBuInfo;
import com.sgs.otsnotes.facade.model.ordercopy.NoCopyTestLineInfo;
import com.sgs.otsnotes.facade.model.ordercopy.SysCopyInfo;
import com.sgs.otsnotes.facade.model.req.artifact.PPArtifactRelInfoReq;
import com.sgs.otsnotes.facade.model.req.testLine.GetSaveTestLineKeyReq;
import com.sgs.otsnotes.facade.model.req.testLine.LabSectionItemReq;
import com.sgs.otsnotes.facade.model.req.testLine.LabSectionListReq;
import com.sgs.otsnotes.facade.model.rsp.testLine.LabSectionRsp;
import com.sgs.otsnotes.facade.model.rsp.testLine.TestLineEditDetailRsp;
import com.sgs.otsnotes.integration.FileClient;
import com.sgs.otsnotes.integration.FrameWorkClient;
import com.sgs.otsnotes.integration.StatusClient;
import com.sgs.otsnotes.integration.trimslocal.CitationClient;
import com.sgs.otsnotes.integration.trimslocal.TestLineClient;
import com.sgs.preorder.facade.model.enums.ObjectType;
import com.sgs.preorder.facade.model.req.BuParamReq;
import com.sgs.preorder.facade.model.req.SysStatusReq;
import com.sgs.trimslocal.facade.model.testline.req.QueryTestLineReq;
import com.sgs.trimslocal.facade.model.testline.rsp.*;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.concurrent.ListenableFutureCallback;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.sgs.otsnotes.facade.model.enums.ConditionStatus.Confirmed;
import static com.sgs.otsnotes.facade.model.enums.ConditionStatus.UnConfirmed;
import static com.sgs.otsnotes.facade.model.enums.OrderCopyType.*;

@Service
@CopyServiceType(copyType = {
    NewCopy,
    ChargeOrder,
    CCOTS,
    Extract,
    Replace,
    AmendReport,
    Sample,
    Conclusion,
    TestLine,
    SubContract,
    NewSubContract,
    SubContractSync,
    LightSubContract,
    NewSubCompleteSync,
    CopyReport,
    EnquiryCopy
}, bizType = OrderBizType.TestLine)
public class TestLineCopyService extends BaseCopyService<String, TestLineCopyInfo> {
    private static final Logger logger = LoggerFactory.getLogger(TestLineCopyService.class);
    @Autowired
    private TestLineMapper testLineMapper;
    @Autowired
    private PPArtifactRelMapper ppArtifactRelMapper;
    @Autowired
    private OrderLangRelService orderLangRelService;
    @Autowired
    private PPTestLineRelMapper ppTestLineRelMapper;
    @Autowired
    private PPSectionMapper ppSectionMapper;
    @Autowired
    private PPBaseMapper ppBaseMapper;
    @Autowired
    private StatusClient statusClient;
    @Autowired
    private ThreadPoolContextTaskExecutor taskExecutor;
    @Autowired
    private FileClient fileClient;
    @Autowired
    private OrderLanguageRelMapper orderLanguageRelMapper;
    @Autowired
    private ProtocolPackageTestLineRelationshipService protocolPackageTestLineRelationshipService;
    @Autowired
    private OrderMapper orderMapper;
    @Autowired
    private WorkInstructionRelMapper workInstructionRelMapper;
    @Autowired
    private LabSectionMapper labSectionMapper;
    @Autowired
    private CodeUtil codeUtil;
    @Autowired
    private TrimsLocalTestLineService trimsLocalTestLineService;
    @Autowired
    private TestLineStatusService testLineStatusService;
    @Autowired
    private TestMatrixMapper matrixMapper;
    @Autowired
    private TestLineClient testLineClient;
    @Autowired
    private TestLineQueryService testLineQueryService;
    @Autowired
    private TestLineCmdService testLineCmdService;
    @Autowired
    private CitationClient citationClient;
    @Autowired
    private FrameWorkClient frameWorkClient;

    /**
     * 1、Add PP、Add TL 没有RootPpBaseId值
     * 19168003
     *
     * @param reqObject1
     * @param reqObject
     * @return
     */
    @Override
    public CustomResult<TestLineCopyInfo> doInvoke(String reqObject1, SysCopyInfo reqObject) {
        CustomResult rspResult = new CustomResult(false);
        OrderCopyType orderCopyType = reqObject.getCopyType();
        switch (orderCopyType){
            case NewCopy:
            case CCOTS:
            case ChargeOrder:
            case EnquiryCopy:
                return this.doNewTestLineInfo(reqObject);
            case Extract:
            case Replace:
            case AmendReport:
            case Sample:
            case Conclusion:
            case TestLine:
            case SubContract:
            case NewSubContract:
            case SubContractSync:
            case LightSubContract:
                return this.doCopyDbTestLineInfo(reqObject);
            case CopyReport:
                return this.doCopyTestLineFile(reqObject);
            case NewSubCompleteSync:
                return this.doCompleteSync(reqObject);
            default:
                rspResult.setMsg(String.format("未找到对应的CopyType(%s)类型.", orderCopyType));
                return rspResult;
        }
    }


    private Map<Integer,List<NewArtifactCitationRelInfo>> queryTlCitationFromTrims(String productLineCode,List<Integer> testLineVersionIds){
        Map<Integer,List<NewArtifactCitationRelInfo>> tlCitationMap = Maps.newHashMap();
        QueryTestLineReq queryTestLineReq=new QueryTestLineReq();
        queryTestLineReq.setCallerBU(productLineCode);
        queryTestLineReq.setTestLineVersionIds(testLineVersionIds);
        List<Integer> languageIds = Lists.newArrayList();
        languageIds.add(LanguageType.Chinese.getLanguageId());
        queryTestLineReq.setLanguageIds(languageIds);
        queryTestLineReq.setStatues(Arrays.asList(1,2));
        List<QueryTestLineRsp> testLineRsp = testLineClient.getTestLineList(queryTestLineReq);
        if(Func.isNotEmpty(testLineRsp)){
            testLineRsp.stream().forEach(tl->{
                List<ArtifactCitationRelInfoRsp> citationRelInfoRspList = tl.getCitations();
                if(Func.isNotEmpty(citationRelInfoRspList)){
                    List<NewArtifactCitationRelInfo> tlCitationList = Lists.newArrayList();
                    citationRelInfoRspList.stream().forEach(c->{
                        NewArtifactCitationRelInfo newArtifactCitationRelInfo = new NewArtifactCitationRelInfo();
                        newArtifactCitationRelInfo.setCitationId(c.getCitationId());
                        newArtifactCitationRelInfo.setCitationBaseId(c.getCitationBaseId());
                        newArtifactCitationRelInfo.setCitationName(c.getCitationName());
                        newArtifactCitationRelInfo.setCitationVersionId(c.getCitationVersionId());
                        newArtifactCitationRelInfo.setCitationType(c.getCitationType());
                        newArtifactCitationRelInfo.setCitationSectionId(c.getCitationSectionId());
                        newArtifactCitationRelInfo.setCitationSectionName(c.getCitationSectionName());
                        newArtifactCitationRelInfo.setEvaluationAlias(c.getEvaluationAlias());
                        List<TestLineCitationLanguagesRsp> testLineCitationLanguagesRspList = c.getLanguages();
                        if(Func.isNotEmpty(testLineCitationLanguagesRspList)){
                            TestLineCitationLanguagesRsp testLineCitationCnRes =  testLineCitationLanguagesRspList.stream().filter(lan->Func.equalsSafe(lan.getLanguageId(), LanguageType.Chinese.getLanguageId())).findAny().orElse(null);
                            if(Func.isNotEmpty(testLineCitationCnRes)&&Func.isNotEmpty(testLineCitationCnRes.getCitationLangBaseId())){
                                newArtifactCitationRelInfo.setLangBaseId(testLineCitationCnRes.getCitationLangBaseId().longValue());
                                newArtifactCitationRelInfo.setLanguageId(LanguageType.Chinese.getLanguageId());
                            }
                        }
                        tlCitationList.add(newArtifactCitationRelInfo);
                    });
                    tlCitationMap.put(tl.getTestLineVersionId(),tlCitationList);
                }
            });
        }
        return tlCitationMap;
    }

    /**
     * 只针对
     * NewCopy
     * CCOTS
     * @param reqObject
     * @return
     */
    public CustomResult<TestLineCopyInfo> doNewTestLineInfo(SysCopyInfo reqObject) {
        CustomResult rspResult = new CustomResult(false);

        UserLabBuInfo labBu = reqObject.getLabBu();
        OrderCopyType orderCopyType = reqObject.getCopyType();
        String newOrderId = reqObject.getOrderId();
        String oldOrderNo = reqObject.getOldOrderNo();
        boolean isChargeOrder = orderCopyType.check(ChargeOrder);
        if (isChargeOrder && StringUtils.equalsIgnoreCase(labBu.getProductLineCode(), ProductLineType.SL.getProductLineAbbr())){
            rspResult.setSuccess(true);
            return rspResult;
        }
        List<NewTestLineInfo> newTestLines = testLineMapper.getNewTestLineInfoList(reqObject.getOldOrderId());
        List<String> oldTestLineInstanceIdList = newTestLines.stream().map(NewTestLineInfo::getTestLineInstanceId).collect(Collectors.toList());

        //基于TestLineVersionID查询Citation集合
        List<Integer> testLineVersionIdList = newTestLines.stream().map(NewTestLineInfo::getOldTestLineVersionId).collect(Collectors.toList());
        Map<Integer,List<NewArtifactCitationRelInfo>> tlCitationMap = queryTlCitationFromTrims(labBu.getProductLineCode(),testLineVersionIdList);

        Set<Integer> newTestLineVersionIds = newTestLines.stream().map(tl -> tl.getTestLineVersionId()).collect(Collectors.toSet());

        // DIG-6001 根据newTestLineVersionIds 获取Claim
        Set<Integer> newClaimFromTLVersionIdSet = Sets.newHashSet();
        List<TestLineWorkInstructionRelInfoPO> newClaimFromTLVersionIds = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(newTestLineVersionIds)) {
            newClaimFromTLVersionIds = workInstructionRelMapper.getWorkInstructionInfoIdsFromTLVersionId(newTestLineVersionIds);
        }
        if (CollectionUtils.isNotEmpty(newClaimFromTLVersionIds)) {
            newClaimFromTLVersionIdSet = newClaimFromTLVersionIds.stream().collect(Collectors.groupingBy(TestLineWorkInstructionRelInfoPO::getTestLineVersionId)).keySet();
        }

        // region 【获取Pp TestLine Rel Info】
        Map<String, List<NewPpTestLineInfo>> ppTestLineMaps = Maps.newHashMap();
        Set<Integer> ppVersionIds = Sets.newHashSet();
        List<NewPpTestLineInfo> ppTestLineInfoList = ppTestLineRelMapper.getNewPpTestLineInfoList(reqObject.getOldOrderId());
        ppTestLineInfoList.forEach(ppTestLine->{
            String testLineInstanceId = ppTestLine.getTestLineInstanceId();
            if (!ppTestLineMaps.containsKey(testLineInstanceId)){
                ppTestLineMaps.put(testLineInstanceId, Lists.newArrayList());
            }
            ppTestLineMaps.get(testLineInstanceId).add(ppTestLine);
            if(Func.isNotEmpty(ppTestLine.getOldPpVersionId()) && ppTestLine.getOldPpVersionId()>0){
                ppVersionIds.add(ppTestLine.getOldPpVersionId());
            }
        });
        //基于ppVersionID查询Citation集合
        List<QueryPpTestLineRsp> ppTlList = Lists.newArrayList();
        if(Func.isNotEmpty(ppVersionIds)){
            ppTlList = testLineClient.getBatchPpTestLineList(ppVersionIds,labBu.getLabId().intValue(),labBu.getProductLineCode());
        }

        // endregion

        List<OrderLanguageRelInfoPO> oldLanguageRels = orderLanguageRelMapper.getOrderLanguageInfoList(reqObject.getOldOrderId(), null);
        Map<String, OrderLanguageRelInfoPO> oldLanguageRelMaps = Maps.newHashMap();
        oldLanguageRels.forEach(langRel->{
            oldLanguageRelMaps.put(String.format("%s_%s_%s", langRel.getObjectBaseId(), langRel.getLanguageId(), langRel.getLangType()), langRel);
        });

        Map<String, Integer> testLineVersionIds = reqObject.getTestLineVersionIds();
        Map<String, Integer> citationVersionIds = reqObject.getCitationVersionIds();
        Map<Integer, NoCopyTestLineInfo> noCopyTestLineMaps = reqObject.getNoCopyTestLineMaps();

        Map<String, String> oldTestLineInstanceIds = Maps.newHashMap();
        Map<String, String> oldPpTestLineRelIds = Maps.newHashMap();
        Map<String, Set<Long>> testLineInstanceIdAids = Maps.newHashMap();
        Map<String, PPTestLineRelationshipInfoPO> ppTestLineRelMaps = Maps.newHashMap();
        Map<String, TestLineInstancePO> testLineMaps = Maps.newHashMap();
        Map<Integer, Set<Integer>> subPpVersionIdMaps = Maps.newHashMap();
        Map<Integer, List<PPSectionBaseInfoPO>> rootSectionMaps = Maps.newHashMap();
        Map<Long, PPBaseInfoPO> ppMaps = Maps.newHashMap();
        Map<Long, Long> oldPpBaseIds = Maps.newHashMap();
        List<OrderLanguageRelInfoPO> langRels = Lists.newArrayList();

        for (NewTestLineInfo newTestLine: newTestLines) {
            // 如果TestLineVersionID不变
            newTestLine.setLabSectionBaseId(newTestLine.getOldLabSectionBaseId());

            newTestLine.setPpTestLines(ppTestLineMaps.get(newTestLine.getTestLineInstanceId()));

            newTestLine.setOrderNo(oldOrderNo);
            if(Func.isNotEmpty(newTestLine.getOldLabSectionBaseId())&&!this.checkLabSectionBaseId(newTestLine,reqObject.getSgsToken())){
                this.setNoCopyTestLineInfo(noCopyTestLineMaps, newTestLine.getTestLineId(), newTestLine.getTestItemName(), NoCopyTestLineType.TestLine,"TL 的Lab section失效，不会被Copy");
                continue;
            }

            boolean isOldCitationRel = false;

            List<NewPpTestLineInfo> ppTestLines = newTestLine.getPpTestLines();
            if (ppTestLines == null || ppTestLines.isEmpty()){
                logger.info("{}订单({})的TestLineId({})未找到PP TestLine Rel信息.", orderCopyType, oldOrderNo, newTestLine.getTestLineId());
                this.setNoCopyTestLineInfo(noCopyTestLineMaps, newTestLine.getTestLineId(), newTestLine.getTestItemName(), NoCopyTestLineType.TestLine, "The PP TL has been changed in Trims(TL didn't exist in PP)");
                continue;
            }
            ArtifactType artifactType = ArtifactType.findType(newTestLine.getArtifactType());
            if (artifactType == null){
                logger.info("{}订单({})的TestLineId({})TestLineVersionId({})未找到ArtifactType({}).", orderCopyType, oldOrderNo, newTestLine.getTestLineId(), newTestLine.getTestLineVersionId(), newTestLine.getArtifactType());
                this.setNoCopyTestLineInfo(noCopyTestLineMaps, newTestLine.getTestLineId(), newTestLine.getTestItemName(), NoCopyTestLineType.TestLine, "The TL has been changed in Trims.(Inactive Artifact Type)");
                continue;
            }
            boolean isUpgradeTestLine = true;
            /**
             * 1、Root PpVersionId 是否存在Sub PpVersionId
             * 2、SectionId 是否相同，且是否该PP下
             * 3、PP上的TestLineVersionId是否是最新的
             */
            for (NewPpTestLineInfo ppTestLine: ppTestLines) {
                NewPPArtifactRelInfo ppArtifactRel = null;
                long rootPpBaseId = 0L;
                Integer testLineVersionId = newTestLine.getTestLineVersionId();
                long ppBaseId = NumberUtil.toLong(ppTestLine.getPpBaseId());
                // 如果大于零，则示为PP下的TestLine
                int oldPpNo = NumberUtil.toInt(ppTestLine.getOldPpNo());
                // 查询TL对应的citation集合
                NewArtifactCitationRelInfo citationRel;
                if (oldPpNo > 0){
                    int ppVersionId = NumberUtil.toInt(ppTestLine.getPpVersionId());
                    if (ppVersionId <= 0){
                        // TODO 如果OldPpNo的，且PpVersionId为零，则不该PP以Inactive
                        logger.info("{}订单({})Old PP({})下不存在该TestLineId({}).", orderCopyType, oldOrderNo, ppTestLine.getOldPpNo(), newTestLine.getTestLineId());
                        this.setNoCopyTestLineInfo(noCopyTestLineMaps, newTestLine.getTestLineId(), newTestLine.getTestItemName(), NoCopyTestLineType.PpTestLine, "The PP TL has been changed in Trims(TL didn't exist in PP)");
                        continue;
                    }
                    // region【检查是否存在Root PP，且当前Root PP下是否存在Sub PP】
                    rootPpBaseId = NumberUtil.toLong(ppTestLine.getRootPpBaseId());  // 默认与新取的PpBaseId一致
                    if (!isChargeOrder&&rootPpBaseId <= 0){
                        logger.info("{}订单({})RootPpBaseId({})无效.", orderCopyType, oldOrderNo, rootPpBaseId);
                        this.setNoCopyTestLineInfo(noCopyTestLineMaps, newTestLine.getTestLineId(), newTestLine.getTestItemName(), NoCopyTestLineType.PpTestLine,"The PP TL has been changed in Trims(TL didn't exist in PP of RootPP)");
                        continue;
                    }
                    long oldPpBaseId = NumberUtil.toLong(ppTestLine.getOldPpBaseId());
                    Integer rootPpVersionId = 0;
                    if (!isChargeOrder&&rootPpBaseId > 0 && rootPpBaseId != oldPpBaseId){
                        PPBaseInfoPO newPpInfo = this.getNewPpInfo(rootPpBaseId, ppMaps);
                        if (newPpInfo == null){
                            logger.info("{}订单({})RootPpBaseId({})下不存在该TestLineId({}).", orderCopyType, oldOrderNo, rootPpBaseId, newTestLine.getTestLineId());
                            this.setNoCopyTestLineInfo(noCopyTestLineMaps, newTestLine.getTestLineId(), newTestLine.getTestItemName(), NoCopyTestLineType.PpTestLine,"The PP TL has been changed in Trims(TL didn't exist in PP of RootPP)");
                            continue;
                        }
                        // 重置为最新的PpBaseId
                        rootPpBaseId = newPpInfo.getId();
                        rootPpVersionId = newPpInfo.getPpVersionId();
                        if(!orderCopyType.check(EnquiryCopy)){
                            if (!this.checkSubPpVersionId(newPpInfo.getPpVersionId(), ppTestLine.getOldPpVersionId(), subPpVersionIdMaps) && rootPpBaseId != ppTestLine.getPpBaseId()){ // 两个值相同时，root就是sub 不用去找关系
                                logger.info("{}订单({})RootPpVersionId({})下不存在SubPpVersionId({})的TestLineId({}).", orderCopyType, newPpInfo.getPpVersionId(), ppTestLine.getPpVersionId(), newTestLine.getTestLineId());
                                this.setNoCopyTestLineInfo(noCopyTestLineMaps, newTestLine.getTestLineId(), newTestLine.getTestItemName(), NoCopyTestLineType.PpTestLine, "The PP TL has been changed in Trims(TL didn't exist in PP of RootPP)");
                                continue;
                            }
                        }
                    }
                    // endregion
                    if(Func.isEmpty(ppTlList)){
                        logger.info("{}订单({}) PP({})下不存在该TestLineId({}).", orderCopyType, oldOrderNo, ppTestLine.getOldPpNo(), newTestLine.getTestLineId());
                        this.setNoCopyTestLineInfo(noCopyTestLineMaps, newTestLine.getTestLineId(), newTestLine.getTestItemName(), NoCopyTestLineType.PpTestLine, "The PP TL has been changed in Trims(TL didn't exist in PP)");
                        continue;
                    }
                    QueryPpTestLineRsp ppTestLineRsp = ppTlList.stream().filter(tl->Func.equalsSafe(tl.getPpVersionId(),ppTestLine.getOldPpVersionId()) &&
                            Func.equalsSafe(tl.getAid(),ppTestLine.getAid()))
                            .sorted(Comparator.comparing(QueryPpTestLineRsp::getCitationVersionId).reversed()).findFirst().orElse(null);
                    if(Func.isEmpty(ppTestLineRsp)){
                        logger.info("{}订单({}) PP({})下 TestLineId({})未找到对应的Citation.", orderCopyType, oldOrderNo,ppTestLine.getOldPpNo(), newTestLine.getTestLineId());
                        this.setNoCopyTestLineInfo(noCopyTestLineMaps, newTestLine.getTestLineId(), newTestLine.getTestItemName(), NoCopyTestLineType.PpTestLine,"The PP TL has been changed in Trims.");
                        continue;
                    }
                    citationRel = new NewArtifactCitationRelInfo();
                    citationRel.setCitationId(ppTestLineRsp.getCitationId());
                    citationRel.setCitationBaseId(ppTestLineRsp.getCitationBaseId());
                    citationRel.setCitationName(ppTestLineRsp.getCitationName());
                    citationRel.setCitationVersionId(ppTestLineRsp.getCitationVersionId());
                    citationRel.setCitationSectionId(ppTestLineRsp.getCitationSectionId());
                    citationRel.setCitationType(ppTestLineRsp.getCitationType());
                    citationRel.setCitationSectionName(ppTestLineRsp.getCitationSectionName());
                    citationRel.setEvaluationAlias(ppTestLineRsp.getEvaluationAlias());
                    if(Func.isNotEmpty(ppTestLineRsp.getLanguages())){
                        PpTestLineLangRsp ppTestLineCNRsp =  ppTestLineRsp.getLanguages().stream().filter(la->Func.equalsSafe(la.getLanguageId(),LanguageType.Chinese.getLanguageId())).findAny().orElse(null);
                        if(Func.isNotEmpty(ppTestLineCNRsp) && Func.isNotEmpty(ppTestLineCNRsp.getCitationLangBaseId())){
                            citationRel.setLanguageId(LanguageType.Chinese.getLanguageId());
                            citationRel.setLangBaseId(ppTestLineCNRsp.getCitationLangBaseId().longValue());
                        }
                    }

                    // ppVersionId+testLineVersionId
                    PPArtifactRelInfoReq reqArtifactRel = new PPArtifactRelInfoReq();
                    reqArtifactRel.setPpVersionId(ppVersionId);
                    // 当TestLine 升级时，有可能PP还没有升级，要考虑这种场景
                    // PpVersionId 如果相等说明没有升级，这时testLineVersionId如果不一致则说明PP还没升级，则以PP上的testLineVersionId为准
                    if ((NumberUtil.equals(ppVersionId, ppTestLine.getOldPpVersionId()) || isOldCitationRel) && !NumberUtil.equals(testLineVersionId, ppTestLine.getOldTestLineVersionId())){
                        testLineVersionId = ppTestLine.getOldTestLineVersionId();
                        isUpgradeTestLine = false;
                    }
                    reqArtifactRel.setTestLineVersionId(testLineVersionId);
                    reqArtifactRel.setArtifactType(artifactType.getType());
                    reqArtifactRel.setCitationVersionId(citationRel.getCitationVersionId());
                    reqArtifactRel.setCitationType(citationRel.getCitationType());

                    ppArtifactRel = ppArtifactRelMapper.getPPArtifactRelInfo(reqArtifactRel);
                    if (ppArtifactRel == null){
                        logger.info("{}订单({})PP VersionId({})下ArtifactId({})未找到TestLineId({})，TestLineVersionId({}).", orderCopyType, oldOrderNo, ppVersionId, ppTestLine.getArtifactId(), newTestLine.getTestLineId(), testLineVersionId);
                        this.setNoCopyTestLineInfo(noCopyTestLineMaps, newTestLine.getTestLineId(), newTestLine.getTestItemName(), NoCopyTestLineType.PpTestLine, "The PP TL has been changed in Trims or Citation of TL in Order was changed. (TL+Citation didn't exist in PP. ）");
                        continue;
                    }
                    int sectionId = NumberUtil.toInt(ppTestLine.getOldSectionId(), ppTestLine.getSectionId());

                    // POSL-3678 同一个Pp下 TLVersionId & citationVersionId & Section 相等时 提示 TLXXX 在Pp中匹配到多条
                    // POSL-3818 同一个PP下同一个TL有多条记录时提示不Copy需要涵盖无Section的情况
                    List<PPArtifactRelInfoPO> ppArtifactInfoForDistinct = ppArtifactRelMapper.getPPArtifactInfoForDistinct(ppVersionId, testLineVersionId, citationRel.getCitationVersionId());
                    if (!orderCopyType.check(EnquiryCopy) && !isChargeOrder&&ppArtifactInfoForDistinct != null && ppArtifactInfoForDistinct.size() > 1) {
                        logger.info("{}订单({})，PP VersionId({}) 下找到多条相同的TestLineId({})，citation({}).", orderCopyType, oldOrderNo, ppVersionId, newTestLine.getTestLineId(), citationRel.getCitationVersionId());
                        this.setNoCopyTestLineInfo(noCopyTestLineMaps, newTestLine.getTestLineId(), newTestLine.getTestItemName(), NoCopyTestLineType.PpTestLine, "The PP TL has multiple records in Trims");
                        continue;
                    }

                    if (!isChargeOrder&&sectionId > 0){
                        ppVersionId = NumberUtil.toInt(rootPpVersionId, ppVersionId);
                        PPSectionBaseInfoPO ppSection = ppSectionMapper.getPPSectionInfo(ppVersionId, sectionId);
                        if(!orderCopyType.check(EnquiryCopy)){
                            if (ppSection == null){
                                logger.info("{}订单({})RootPpVersionId({})，PP VersionId({})下未找到TestLineId({})，ArtifactId({},{})、SectionId({}).", orderCopyType, oldOrderNo, rootPpVersionId, ppVersionId, newTestLine.getTestLineId(), ppTestLine.getArtifactId(), ppArtifactRel.getArtifactId(), sectionId);
                                this.setNoCopyTestLineInfo(noCopyTestLineMaps, newTestLine.getTestLineId(), newTestLine.getTestItemName(), NoCopyTestLineType.PpTestLine, "The PP TL SectionId has been changed in Trims(TL SectionId didn't exist in PP)");
                                continue;
                            }

                            int sectionLevel = NumberUtil.toInt(ppTestLine.getOldSectionLevel(), ppTestLine.getSectionLevel());
                            // 获取Root Section Info
                            this.setRootSectionInfo(ppVersionId, sectionLevel, rootSectionMaps, ppArtifactRel);
                        }
                    }

                    if (ppBaseId > 0){
                        oldPpBaseIds.put(ppTestLine.getOldPpBaseId(), ppBaseId);
                    }
                }else {
                    // 基于TestLineVersionId查询CitationList
                    List<NewArtifactCitationRelInfo> citationList = tlCitationMap.get(newTestLine.getOldTestLineVersionId());
                    // 非PP的场景，基于citationId查找最新的citationVersion
                    if(Func.isEmpty(citationList)){
                        logger.info("{}订单({})的TestLineId({})未找到对应的CitationList.", orderCopyType, oldOrderNo, newTestLine.getTestLineId());
                        this.setNoCopyTestLineInfo(noCopyTestLineMaps, newTestLine.getTestLineId(), newTestLine.getTestItemName(), NoCopyTestLineType.TestLine,"The TL has been changed in Trims.(Inactive Citation)");
                        continue;
                    }
                    // 首先根据citationId + citationVersionID 查找数据 如果不存在再根据citationId 查找最新数据
                    citationRel = citationList.stream().filter(citation->
                            Func.equals(citation.getCitationId(), newTestLine.getCitationId()) && Func.equals(citation.getCitationVersionId(),newTestLine.getOldCitationVersionId())).findFirst().orElse(null);
                    if(Func.isEmpty(citationRel)){
                        citationRel = citationList.stream().filter(citation-> StringUtil.equals(citation.getCitationId(), newTestLine.getCitationId()))
                                .sorted(Comparator.comparing(NewArtifactCitationRelInfo::getCitationVersionId).reversed()).findFirst().orElse(null);
                        if(Func.isEmpty(citationRel)){
                            logger.info("{}订单({})的TestLineId({})未找到对应的Citation.", orderCopyType, oldOrderNo, newTestLine.getTestLineId());
                            this.setNoCopyTestLineInfo(noCopyTestLineMaps, newTestLine.getTestLineId(), newTestLine.getTestItemName(), NoCopyTestLineType.TestLine,"The TL has been changed in Trims.(Inactive Citation)");
                            continue;
                        }
                    }
                }
                // 生成Test Line Info，TODO 当拆分两条时，则靠考虑oldTestLineInstanceId的问题
                String oldTestLineInstanceId = newTestLine.getTestLineInstanceId();
                String testLineInstanceId = this.getTestLineInfo(reqObject,newOrderId, newTestLine, citationRel, testLineMaps, testLineVersionIds, newClaimFromTLVersionIdSet, citationVersionIds, oldLanguageRelMaps, langRels, isUpgradeTestLine,ppTestLine,orderCopyType);
                oldTestLineInstanceIds.put(oldTestLineInstanceId, testLineInstanceId);

                String oldPpTestLineRelId = ppTestLine.getPpTestLineRelId();
                String ppTestLineRelId = this.getPpTestLineRelInfo(newOrderId, testLineInstanceId, rootPpBaseId, ppTestLineRelMaps, langRels, ppArtifactRel,ppTestLine.getSeq(), testLineInstanceIdAids);
                oldPpTestLineRelIds.put(oldPpTestLineRelId, ppTestLineRelId);

            }
        }
        //
        reqObject.setOldTestLineInstanceIds(oldTestLineInstanceIds);
        reqObject.setOldPpTestLineRelIds(oldPpTestLineRelIds);
        reqObject.setTestLineInstanceAids(testLineInstanceIdAids);
        reqObject.setTestLineVersionIds(testLineVersionIds);
        reqObject.setCitationVersionIds(citationVersionIds);
        reqObject.setOldPpBaseIds(oldPpBaseIds);
        List<TestLineInstanceMultipleLanguageInfoPO> testLineInstanceMultipleLanguageInfoPOS = this.buildTestLineLanguage(oldTestLineInstanceIdList,oldTestLineInstanceIds);
        TestLineCopyInfo rspObject = new TestLineCopyInfo();
        rspObject.setOrderId(reqObject.getOrderId());
        rspObject.setTestLines(Lists.newArrayList(testLineMaps.values()));
        rspObject.setPpTestLineRels(Lists.newArrayList(ppTestLineRelMaps.values()));
        rspObject.setOrderLanguages(langRels);
        rspObject.setLanguages(testLineInstanceMultipleLanguageInfoPOS);
        rspResult.setData(rspObject);
        rspResult.setSuccess(true);
        return rspResult;
    }

    private List<TestLineInstanceMultipleLanguageInfoPO> buildTestLineLanguage(List<String> testLineInstanceIdList,Map<String, String> oldTestLineInstanceIds){
        if(Func.isEmpty(testLineInstanceIdList)){
            return null;
        }
        UserInfo localUser = UserHelper.getLocalUser();
        Date now = DateUtils.getNow();
        List<TestLineInstanceMultipleLanguageInfoPO> testLineLanguages = testLineMapper.getTestLineLanguages(testLineInstanceIdList);
        if(Func.isNotEmpty(testLineLanguages)){
            for (TestLineInstanceMultipleLanguageInfoPO testLineLanguage : testLineLanguages) {
                testLineLanguage.setID(UUID.randomUUID().toString());
                testLineLanguage.setTestLineInstanceID(oldTestLineInstanceIds.get(testLineLanguage.getTestLineInstanceID()));
                testLineLanguage.setCreatedBy(Func.isNotEmpty(localUser)?localUser.getRegionAccount():"system");
                testLineLanguage.setCreatedDate(now);
                testLineLanguage.setModifiedBy(Func.isNotEmpty(localUser)?localUser.getRegionAccount():"system");
                testLineLanguage.setModifiedDate(now);
            }
        }
        return testLineLanguages;
    }

    /**
     *  GPO2-6877
     *  如果TL.LabSection是无效的,不允许COPY
     */
    private Boolean checkLabSectionBaseId(NewTestLineInfo newTestLine,String token){
        // 查询最新的LabSection集合
        LabSectionListReq labSectionListReq = new LabSectionListReq();
        labSectionListReq.setOrderNo(newTestLine.getOrderNo());
        List<LabSectionItemReq> testLineInstanceIds = Lists.newArrayList();
        LabSectionItemReq labSectionItemReq = new LabSectionItemReq();
        labSectionItemReq.setTestLineInstanceId(newTestLine.getTestLineInstanceId());
        testLineInstanceIds.add(labSectionItemReq);
        labSectionListReq.setTestLineInstanceIds(testLineInstanceIds);
        labSectionListReq.setSgsToken(token);
        labSectionListReq.setExempt(true);
        BaseResponse<List<TestLineEditDetailRsp>> labSectionRes = testLineQueryService.getLabSectionList(labSectionListReq);
        logger.info("{}订单checkLabSectionBaseId->labSectionRes:{}",newTestLine.getOrderNo(), JSON.toJSONString(labSectionRes));
        if(Func.isEmpty(labSectionRes)||Func.isEmpty(labSectionRes.getData())){
            return false;
        }
        TestLineEditDetailRsp testLineEditDetail = labSectionRes.getData().get(0);
        if(Func.isEmpty(testLineEditDetail.getLabSectionList())){
            return false;
        }
        List<LabSectionRsp> labSectionList = testLineEditDetail.getLabSectionList();
        return Func.isNotEmpty(
                labSectionList.stream().filter(labSection->
                Func.equalsSafe(newTestLine.getOldLabSectionBaseId(),labSection.getLabSectionBaseId())).findAny().orElse(null));
    }

    /**
     * AmendReport
     * SubContract
     * NewSubContract
     * SubContractSync
     * @param reqParams
     * @return
     */
    public CustomResult<TestLineCopyInfo> doCopyDbTestLineInfo(SysCopyInfo reqParams){
        CustomResult<TestLineCopyInfo> rspResult = new CustomResult(false);
        Map<String, String> oldTestLineInstanceIds = reqParams.getOldTestLineInstanceIds();
        Map<String, Boolean> cancelTestLineIds = reqParams.getCancelTestLineIds();
        Map<String, Boolean> oldTestLineNewFlags = reqParams.getOldTestLineNewFlags();

//        List<TestLineInstancePO> oldTestLines = testLineMapper.getTestLineByOrderId(reqParams.getOldOrderId());
        // DIG-6677 getTestLineByOrderId接口开发
        List<TestLineInstancePO> oldTestLines = trimsLocalTestLineService.getTestLineByOrderId(reqParams.getOldOrderId());
        if (oldTestLines == null || oldTestLines.isEmpty()){
            rspResult.setIgnore(true);
            return rspResult;
        }
        List<String> oldTestLineInstanceIdList = oldTestLines.stream().map(TestLineInstancePO::getID).collect(Collectors.toList());
        UserInfo localUser = UserHelper.getLocalUser();
        OrderCopyType copyType = reqParams.getCopyType();
        boolean isSubContractSync = copyType.check(SubContractSync);
        boolean isSyncOrderStatus = false, isNewTestLine = false;

        Map<String, Integer> oldSubTestLineMaps = Maps.newHashMap();
        Map<String, String> oldSubTLFileIDMaps = Maps.newHashMap();
        // 内部分包 同步数据使用
        Map<String, TestLineInstancePO> oldSubTLMaps = Maps.newHashMap();
        // 分包单的remark map
        Map<String, String> oldSubTestLineRemarkMaps = Maps.newHashMap();
        if (isSubContractSync){
            for (TestLineInstancePO oldSubTestLine: testLineMapper.getTestLineByOrderId(reqParams.getOrderId())) {
                oldSubTestLineMaps.put(oldSubTestLine.getID(), oldSubTestLine.getTestLineStatus());
                oldSubTLFileIDMaps.put(oldSubTestLine.getID(), oldSubTestLine.getFileID());
                oldSubTLMaps.put(oldSubTestLine.getID(), oldSubTestLine);
                if (StringUtils.isNotEmpty(oldSubTestLine.getOrdertestLineRemark())) {
                    oldSubTestLineRemarkMaps.put(oldSubTestLine.getID(), oldSubTestLine.getOrdertestLineRemark());
                }
            }
        }
        ConcurrentHashMap<String, Set<String>> testLineFiles = new ConcurrentHashMap();
        List<TestLineInstancePO> testLines = Lists.newArrayList();
        for (TestLineInstancePO oldTestLine : oldTestLines) {
            String oldTestLineInstanceId = oldTestLine.getID();
            if (!oldTestLineInstanceIds.containsKey(oldTestLineInstanceId)) {
                continue;
            }
            if(this.isCancelTestLine(reqParams, oldTestLineInstanceId)){
                continue;
            }
            String testLineInstanceId = oldTestLineInstanceIds.get(oldTestLineInstanceId);
            isNewTestLine = this.isNewRelId(reqParams, oldTestLineInstanceId, TableType.TestLine);
            if (isSubContractSync && !isNewTestLine){
                isSyncOrderStatus = true;
            }
            oldTestLineNewFlags.put(oldTestLineInstanceId, isNewTestLine);
            oldTestLine.setID(testLineInstanceId);
            oldTestLine.setGeneralOrderInstanceID(reqParams.getOrderId());
            oldTestLine.setOrderNo(reqParams.getOrderNo());
            oldTestLine.setLabId(NumberUtil.toInt(reqParams.getLabBu().getLabId()));
            if (StringUtils.isNotBlank(oldTestLine.getFileID())) {
                if (!testLineFiles.containsKey(oldTestLine.getFileID())){
                    testLineFiles.put(oldTestLine.getFileID(), Sets.newHashSet());
                }
                testLineFiles.get(oldTestLine.getFileID()).add(testLineInstanceId);
            }
            Integer testLineType = oldTestLine.getTestLineType();
            TestLineType subContractOrder = TestLineType.SubContractOrder;
            if (TestLineType.check(testLineType, subContractOrder)){
                oldTestLine.setTestLineType(testLineType.intValue() ^ subContractOrder.getType());
            }
            if(TestLineType.check(testLineType, TestLineType.OOB_TEST)){
                oldTestLine.setTestLineType(testLineType.intValue() | TestLineType.OOB_TEST.getType());
            }
            // Create Order By SubContract 需要重置TestLine 的 subcontract位
            if (TestLineType.check(testLineType, TestLineType.SubContractOrder)){
                oldTestLine.setTestLineType(testLineType.intValue() ^ TestLineType.SubContractOrder.getType());
            }
            oldTestLine.setDocumentReviewFlag(DocumentReviewFlag.None.getStatus());
            boolean isCancelTestLine = cancelTestLineIds.containsKey(oldTestLineInstanceId);
            // POSL-2738
            if (copyType.check(SubContract, NewSubContract, LightSubContract) || isSubContractSync){
                Integer testLineStatus = oldTestLine.getTestLineStatus();
                // cancel的 copy 但是状态不变 2020年7月23日18:13:13
                if (oldSubTestLineMaps.containsKey(testLineInstanceId) && !isCancelTestLine){
                    testLineStatus = oldSubTestLineMaps.get(testLineInstanceId);
                }else if (isNewTestLine && !isCancelTestLine){ // 是否新增TestLine，且是第一次同步，并且该TestLine是Cancel的
                    //testLineStatus = TestLineStatus.Entered.getStatus();
                    // modify by vincent DIG-4521
                    testLineStatus = TestLineStatus.Typing.getStatus();
                }
                oldTestLine.setTestLineStatus(testLineStatus);
            }else{
                String documentReviewMode = this.getDocumentReviewMode();
                if(TestLineStatus.check(oldTestLine.getTestLineStatus(),TestLineStatus.DR) || DocumentReviewFlag.check(oldTestLine.getDocumentReviewFlag(),DocumentReviewFlag.Testing_Conclusion,DocumentReviewFlag.Conclusion) ){
                    if(StringUtils.equalsIgnoreCase(documentReviewMode,Constants.BU_PARAM.TEST_LINE.DocumentReviewMode.VALUES.CONCLUSION)){
                        oldTestLine.setDocumentReviewFlag(DocumentReviewFlag.Conclusion.getStatus());
                        oldTestLine.setTestLineStatus(TestLineStatus.Completed.getStatus());
                    }else{
                        oldTestLine.setDocumentReviewFlag(DocumentReviewFlag.Testing_Conclusion.getStatus());
                    }
                }
            }
            // DIG-5100 修改TL Remark
            String remark = oldTestLine.getOrdertestLineRemark();
            GeneralOrderInstanceInfoPO oldOrder = orderMapper.getOrderInfo(reqParams.getOldOrderNo());
            if(copyType.check(SubContract, NewSubContract, LightSubContract)
                    && StringUtils.isNotEmpty(remark)){
                oldTestLine.setOrdertestLineRemark(String.format("%s : %s ||", oldOrder.getLabCode(), remark));
            }
            // 同步回发包方 remark
            if (isSubContractSync) {
                this.setTestLineRemark(oldSubTestLineRemarkMaps, oldOrder, oldTestLine, remark);
            }
            // 同步 设置FileID
            if (isSubContractSync && oldSubTLFileIDMaps.containsKey(testLineInstanceId)) {
                oldTestLine.setFileID(oldSubTLFileIDMaps.get(testLineInstanceId));
            }

            // 内部分包初始化设置为null
            if (copyType.check(SubContract, NewSubContract, LightSubContract) || (isSubContractSync && isNewTestLine)) {
                oldTestLine.setSampleSegegrationWIText(null);
                oldTestLine.setSampleSegegrationWIID(null);
                oldTestLine.setLabTeamCode(null);
            }
            // 内部分包同步 Sample for WI 处理
            if (isSubContractSync && !isNewTestLine && oldSubTLMaps.containsKey(oldTestLine.getID())) {
                TestLineInstancePO testLineInstance =  oldSubTLMaps.get(oldTestLine.getID());
                oldTestLine.setSampleSegegrationWIText(testLineInstance.getSampleSegegrationWIText());
                oldTestLine.setSampleSegegrationWIID(testLineInstance.getSampleSegegrationWIID());
                oldTestLine.setLabTeamCode(testLineInstance.getLabTeamCode());
            }

            oldTestLine.setActiveIndicator(!isCancelTestLine);
            oldTestLine.setCreatedBy(localUser.getRegionAccount());
            oldTestLine.setCreatedDate(DateUtils.getNow());
            oldTestLine.setModifiedBy(localUser.getRegionAccount());
            oldTestLine.setModifiedDate(DateUtils.getNow());
            // DIG-5476 validate 处理   amend split 直接copy  SubContract 不copy  sync数据 不同步
            if (copyType.check(SubContract, NewSubContract, LightSubContract)) {
                oldTestLine.setTestStartDate(null);
                oldTestLine.setValidateDate(null);
                oldTestLine.setValidateBy(null);
            }
            if (isSubContractSync && oldSubTLMaps.containsKey(oldTestLine.getID())) {
                TestLineInstancePO testLineInstancePO = oldSubTLMaps.get(oldTestLine.getID());
                oldTestLine.setValidateDate(testLineInstancePO.getValidateDate());
                oldTestLine.setValidateBy(testLineInstancePO.getValidateBy());
            }
            if (isNewTestLine) {
                if (copyType.check(SubContract, SubContractSync, NewSubContract, LightSubContract)) {
                    oldTestLine.setLabSectionBaseId(null);
                    oldTestLine.setEngineer(null);
                }
            }
            if (isSubContractSync && !isNewTestLine && oldSubTLMaps.containsKey(oldTestLine.getID())) {
                oldTestLine.setLabSectionBaseId(oldSubTLMaps.get(oldTestLine.getID()).getLabSectionBaseId());
            }
            //DIG-6949
            if (! copyType.check(AmendReport, Extract, Replace, Sample, Conclusion, TestLine)) {
                oldTestLine.setStyleVersionId(0);
            }
            //获取编码
            if (isNewTestLine){
                String externalOrderNo = reqParams.getExternalOrderNo();
                if(Func.isEmpty(externalOrderNo)){
                    externalOrderNo = reqParams.getOrderNo();
                }
                String number = null;
                try {
                    number = codeUtil.getTestItemNo(externalOrderNo,oldTestLine.getTestLineID());
                } catch (Exception e) {
                    rspResult.setSuccess(false);
                    rspResult.setMsg("获取TestItemNo失败:"+e.getMessage());
                    return rspResult;
                }
                oldTestLine.setTestItemNo(number);
            }else if(oldSubTLMaps.containsKey(testLineInstanceId)){
                oldTestLine.setTestItemNo(oldSubTLMaps.get(testLineInstanceId).getTestItemNo());
            }
            testLines.add(oldTestLine);
        }


        rspResult.setSuccess(testLines.isEmpty());
        if (rspResult.isSuccess()){
            rspResult.setIgnore(true);
            return rspResult;
        }

        // Copy File
        rspResult = this.doCopyTestLineFile(reqParams, testLines, testLineFiles, isSubContractSync);
        if (!rspResult.isSuccess()){
            return rspResult;
        }

        TestLineCopyInfo reqObject = new TestLineCopyInfo();
        reqObject.setCopyType(reqParams.getCopyType());
        reqObject.setTestLines(testLines);
        reqObject.setOldTestLineIds(oldTestLineInstanceIds);
        List<TestLineInstanceMultipleLanguageInfoPO> testLineInstanceMultipleLanguageInfoPOS = this.buildTestLineLanguage(oldTestLineInstanceIdList,oldTestLineInstanceIds);
        reqObject.setLanguages(testLineInstanceMultipleLanguageInfoPOS);
        // 是否需要同步 preorder order状态
        if(isSyncOrderStatus){
            // 需要更新状态为testling
            SysStatusReq reqStatus = new SysStatusReq();
            reqStatus.setObjectNo(reqParams.getOrderNo());
            reqStatus.setObjectType(ObjectType.Order.getCode());
            reqStatus.setIgnoreOldStatus(true);
            reqStatus.setUserName(localUser.getRegionAccount());
            reqStatus.setNewStatus(com.sgs.preorder.facade.model.enums.OrderStatus.Testing.getStatus());
            reqObject.setSyncStatus(reqStatus);
        }

        rspResult.setData(reqObject);
        rspResult.setSuccess(true);
        return rspResult;
    }

    /**
     * 新版分包 Complete 回传
     * 修改主单数据
     * @param reqObject
     * @return
     */
    private CustomResult<TestLineCopyInfo> doCompleteSync(SysCopyInfo reqObject) {
        CustomResult<TestLineCopyInfo> rspResult = new CustomResult(false);
        UserInfo localUser = UserHelper.getLocalUser();
        String orderNo = reqObject.getOrderNo();
        Map<String, String> oldTestLineInstanceIds = reqObject.getOldTestLineInstanceIds();
        Map<String, String> oldPpTestLineRelIds = reqObject.getOldPpTestLineRelIds();

        // 获取子单全部testLine信息
        List<TestLineInstancePO> oldTestLineInfos = testLineMapper.getTestLineByOrderNo(orderNo);
        List<PPTestLineRelationshipInfoPO> oldPpTlRelList = ppTestLineRelMapper.getPPTestLineRelListByOrderId(reqObject.getOrderId());

        TestLineCopyInfo rspObject = new TestLineCopyInfo();

        List<TestLineInstancePO> saveTestLineInfo = Lists.newArrayList();
        for (TestLineInstancePO testLineInstancePO : oldTestLineInfos){
            String testLineId = UUID.randomUUID().toString();
            if (!oldTestLineInstanceIds.containsKey(testLineInstancePO.getID()) || StringUtils.isBlank(oldTestLineInstanceIds.get(testLineInstancePO.getID()))){
                oldTestLineInstanceIds.put(testLineInstancePO.getID(), testLineId);
            }
            testLineInstancePO.setID(this.getPrimaryKeyId(reqObject, testLineInstancePO.getID(), oldTestLineInstanceIds.get(testLineInstancePO.getID()), TableType.TestLine));
            testLineInstancePO.setGeneralOrderInstanceID(reqObject.getOldOrderId());
            testLineInstancePO.setOrderNo(reqObject.getOldOrderNo());
            testLineInstancePO.setLabId(NumberUtil.toInt(reqObject.getLabBu().getLabId()));
            // 设置为Complete
            if (!TestLineStatus.check(testLineInstancePO.getTestLineStatus(), TestLineStatus.NC, TestLineStatus.DR, TestLineStatus.Cancelled)) {
                testLineInstancePO.setTestLineStatus(TestLineStatus.Completed.getStatus());
            }
            testLineInstancePO.setFileID(null);
            testLineInstancePO.setDataEntryCloudID(null);
            testLineInstancePO.setModifiedDate(DateUtils.getNow());
            testLineInstancePO.setModifiedBy(localUser.getRegionAccount());
            saveTestLineInfo.add(testLineInstancePO);
        }
        rspObject.setTestLines(saveTestLineInfo);
        List<String> allTestLineRels = oldTestLineInstanceIds.values().stream().collect(Collectors.toList());
        List<String> needDelIds = allTestLineRels.stream().filter(rel -> !saveTestLineInfo.stream().map(TestLineInstancePO::getID).collect(Collectors.toList()).contains(rel)).collect(Collectors.toList());
        // DIG-7348 子单 如果删除了  主单分的TestLine，回传时 将该testLine 设置为Cancel 状态
        if (CollectionUtils.isNotEmpty(needDelIds)) {
            List<TestLineInstancePO> testLineByIds = testLineMapper.getTestLineByIds(needDelIds);
            if (CollectionUtils.isNotEmpty(testLineByIds)) {
                testLineByIds.forEach(tl -> {
                    tl.setTestLineStatus(TestLineStatus.Cancelled.getStatus());
                    tl.setActiveIndicator(false);
                });
                rspObject.setCancelTestLine(testLineByIds);
            }
        }

        List<PPTestLineRelationshipInfoPO> savePpTlRelInfo = Lists.newArrayList();
        for (PPTestLineRelationshipInfoPO ppTlRel : oldPpTlRelList){
            String ppTlRelId = UUID.randomUUID().toString();
            if (!oldPpTestLineRelIds.containsKey(ppTlRel.getID()) || StringUtils.isBlank(oldPpTestLineRelIds.get(ppTlRel.getID()))){
                oldPpTestLineRelIds.put(ppTlRel.getID(), ppTlRelId);
            }
            ppTlRel.setID(this.getPrimaryKeyId(reqObject, ppTlRel.getID(), oldPpTestLineRelIds.get(ppTlRel.getID()), TableType.PPTestLineRel));
            ppTlRel.setGeneralOrderInstanceID(reqObject.getOldOrderId());
            ppTlRel.setTestLineInstanceID(oldTestLineInstanceIds.get(ppTlRel.getTestLineInstanceID()));
            ppTlRel.setModifiedDate(DateUtils.getNow());
            ppTlRel.setModifiedBy(localUser.getRegionAccount());
            savePpTlRelInfo.add(ppTlRel);
        }
        rspObject.setPpTestLineRels(savePpTlRelInfo);
//        List<String> allPpTestLineRels = oldPpTestLineRelIds.values().stream().collect(Collectors.toList());
//        rspObject.setDelPPTLRelIds(allPpTestLineRels.stream().filter(rel -> !savePpTlRelInfo.stream().map(PPTestLineRelationshipInfoPO::getID).collect(Collectors.toList()).contains(rel)).collect(Collectors.toList()));

        rspResult.setData(rspObject);
        rspResult.setSuccess(true);
        return rspResult;
    }

    /**
     *
     * @param reqObject
     * @return
     */
    private CustomResult<TestLineCopyInfo> doCopyTestLineFile(SysCopyInfo reqObject){
        CustomResult rspResult = new CustomResult();
        List<TestLineInstancePO> oldTestLines = testLineMapper.getTestLineByOrderId(reqObject.getOldOrderId());
        if (oldTestLines == null || oldTestLines.isEmpty()){
            return rspResult;
        }
        UserInfo localUser = UserHelper.getLocalUser();
        Map<String, String> oldTestLineInstanceIds = reqObject.getOldTestLineInstanceIds();

        ConcurrentHashMap<String, Set<String>> testLineFiles = new ConcurrentHashMap();
        List<TestLineInstancePO> testLines = Lists.newArrayList();

        // 获取当前订单的Matrix
        List<TestMatrixPO> testMatrixByOrderId = matrixMapper.getTestMatrixByOrderId(reqObject.getOrderId());
        Map<String, String> oldTestMatrixIds = reqObject.getOldTestMatrixIds();
        Map<String, List<TestMatrixPO>> testLineMatrixMaps = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(testMatrixByOrderId)) {
            testLineMatrixMaps = testMatrixByOrderId.stream().collect(Collectors.groupingBy(TestMatrixPO::getTestLineInstanceID));
        }

        for (TestLineInstancePO oldTestLine : oldTestLines) {
            String oldTestLineInstanceId = oldTestLine.getID();
            if (!oldTestLineInstanceIds.containsKey(oldTestLineInstanceId)) {
                continue;
            }
            String testLineInstanceId = oldTestLineInstanceIds.get(oldTestLineInstanceId);
            oldTestLine.setID(testLineInstanceId);
            if (!testLineMatrixMaps.containsKey(testLineInstanceId)) {
                continue;
            }
            if (StringUtils.isNotBlank(oldTestLine.getFileID())) {
                if (!testLineFiles.containsKey(oldTestLine.getFileID())){
                    testLineFiles.put(oldTestLine.getFileID(), Sets.newHashSet());
                }
                testLineFiles.get(oldTestLine.getFileID()).add(testLineInstanceId);
            }
//            oldTestLine.setTestLineStatus(TestLineStatus.Completed.getStatus());
            // DIG-6929
            oldTestLine.setTestLineStatus(this.buildTestLineStatusForCopyReport(testLineMatrixMaps.get(testLineInstanceId), oldTestMatrixIds));

            oldTestLine.setModifiedBy(localUser.getRegionAccount());
            oldTestLine.setModifiedDate(DateUtils.getNow());

            testLines.add(oldTestLine);
        }
        // Copy File
        rspResult = this.doCopyTestLineFile(reqObject, testLines, testLineFiles, false);
        if (!rspResult.isSuccess()){
            return rspResult;
        }
        TestLineCopyInfo rspObject = new TestLineCopyInfo();
        rspObject.setCopyType(reqObject.getCopyType());
        rspObject.setTestLines(testLines);

        rspResult.setData(rspObject);
        rspResult.setSuccess(true);
        return rspResult;
    }

    /**
     * DIG-6929 Copy Report 时需要校验 该TestLine下是否所有的Matrix 都需要Complete
     * @param testMatrixPOS
     * @param oldTestMatrixIds
     * @return
     */
    private int buildTestLineStatusForCopyReport(List<TestMatrixPO> testMatrixPOS, Map<String, String> oldTestMatrixIds) {
        Boolean isComplete = true;
        for (TestMatrixPO testMatrix : testMatrixPOS){
            if (!oldTestMatrixIds.values().contains(testMatrix.getID())) {
                isComplete = false;
                break;
            }
        }

        return  isComplete ? TestLineStatus.Completed.getStatus() : TestLineStatus.Entered.getStatus();
    }


    /**
     *
     * @param reqObject
     * @param testLines
     * @param testLineFiles
     * @param isSubContractSync
     * @return
     */
    private CustomResult doCopyTestLineFile(SysCopyInfo reqObject, List<TestLineInstancePO> testLines, ConcurrentHashMap<String, Set<String>> testLineFiles, boolean isSubContractSync){
        CustomResult rspResult = new CustomResult();
        ConcurrentHashMap<String, FileInfo> attachments = new ConcurrentHashMap();
        final CountDownLatch latch = new CountDownLatch(testLineFiles.size());
        for (Map.Entry<String, Set<String>> testLineFile: testLineFiles.entrySet()) {
            taskExecutor.submitListenable(()-> fileClient.copyFile(attachments, reqObject.getOrderId(), testLineFile))
                    .addCallback(new ListenableFutureCallback<CustomResult>() {
                        @Override
                        public void onSuccess(CustomResult fileResult) {
                            latch.countDown();
                        }
                        @Override
                        public void onFailure(Throwable t) {
                            // TODO Auto-generated method stub
                            latch.countDown();
                        }
                    });
        }
        try {
            // 设定超时时间单位：毫秒
            latch.await(30000, TimeUnit.MILLISECONDS);
        } catch (InterruptedException ie) {
            logger.error("处理订单（{}）, TestLineService.copyFile InterruptedException Error:", reqObject.getOrderNo(), ie);
            rspResult.setSuccess(false);
            rspResult.setMsg("Copy TestLine CloudId Error.");
            return rspResult;
        }
        for (TestLineInstancePO testLine : testLines) {
            if (isSubContractSync && StringUtils.isNotEmpty(testLine.getFileID())) {
                continue;
            }
            testLine.setFileID(null);
            testLine.setDataEntryCloudID(null);
            if (!attachments.containsKey(testLine.getID())) {
                continue;
            }
            FileInfo file = attachments.get(testLine.getID());
            if (file == null){
                continue;
            }
            testLine.setFileID(file.getId());
        }
        rspResult.setSuccess(true);
        return rspResult;
    }

    /**
     *
     * @param reqObject
     * @param rspObject
     * @return
     */
    public CustomResult<TestLineCopyInfo> doCopyLanguage(SysCopyInfo reqObject, TestLineCopyInfo rspObject){
        CustomResult<TestLineCopyInfo> rspResult = new CustomResult();
        Map<String, String> oldTestLineIds = rspObject.getOldTestLineIds();
        rspResult.setSuccess(oldTestLineIds == null || oldTestLineIds.isEmpty());
        if (rspResult.isSuccess()){
            return rspResult;
        }
        // trimsLocalize 多语言的copy 应当只copy tre_order_language_rel
        List<OrderLanguageRelInfoPO> language = orderLanguageRelMapper.getOrderLanguageInfoList(reqObject.getOldOrderId(), LangTypeEnum.TestLine.getType());
        for (OrderLanguageRelInfoPO orderLanguageRelInfoPO : language) {
            orderLanguageRelInfoPO.setId(null);
            orderLanguageRelInfoPO.setOrderId(reqObject.getOrderId());
            orderLanguageRelInfoPO.setCreatedDate(DateUtils.getNow());
            orderLanguageRelInfoPO.setModifiedDate(DateUtils.getNow());
        }
        rspObject.setOrderLanguages(language);
        rspResult.setData(rspObject);
        rspResult.setSuccess(true);

        return rspResult;
    }

    /**
     *  同步回发包方 remark
     * @param oldSubTestLineRemarkMaps
     * @param oldOrder
     * @param oldTestLine
     * @param remark
     */
    public void setTestLineRemark(Map<String, String> oldSubTestLineRemarkMaps, GeneralOrderInstanceInfoPO oldOrder, TestLineInstancePO oldTestLine, String remark) {
        // 同步回发包方 remark
        if (StringUtils.isNotEmpty(remark)) {
            if (!oldSubTestLineRemarkMaps.containsKey(oldTestLine.getID())) {
                oldTestLine.setOrdertestLineRemark(String.format("%s : %s ||", oldOrder.getLabCode(), remark));
                return;
            }
            String remarkOld = oldSubTestLineRemarkMaps.get(oldTestLine.getID());
            if (!remarkOld.contains("||")) {
                oldTestLine.setOrdertestLineRemark(String.format("%s : %s ||%s", oldOrder.getLabCode(), remark, remarkOld));
                return;
            }
            if (remarkOld.split("\\|\\|").length > 1) {
                oldTestLine.setOrdertestLineRemark(String.format("%s : %s ||%s", oldOrder.getLabCode(), remark, remarkOld.split("\\|\\|")[1]));
            }
            else if (remarkOld.split("\\|\\|").length == 1) {
                oldTestLine.setOrdertestLineRemark(String.format("%s : %s ||", oldOrder.getLabCode(), remark));
            }
            else {
                oldTestLine.setOrdertestLineRemark(String.format("%s : %s ||%s", oldOrder.getLabCode(), remark, remarkOld));
            }
            return;
        }

        if (!oldSubTestLineRemarkMaps.containsKey(oldTestLine.getID())) {
            oldTestLine.setOrdertestLineRemark("");
            return;
        }
        String remarkOld = oldSubTestLineRemarkMaps.get(oldTestLine.getID());
        if (!remarkOld.contains("||")) {
            oldTestLine.setOrdertestLineRemark(String.format("%s : ||%s", oldOrder.getLabCode(), remarkOld));
            return;
        }
        if (remarkOld.split("\\|\\|").length > 1) {
            oldTestLine.setOrdertestLineRemark(String.format("%s : ||%s", oldOrder.getLabCode(), remarkOld.split("\\|\\|")[1]));
        }
        else if (remarkOld.split("\\|\\|").length == 1) {
            oldTestLine.setOrdertestLineRemark(String.format("%s : ||", oldOrder.getLabCode()));
        }
        else {
            oldTestLine.setOrdertestLineRemark(String.format("%s : ||%s", oldOrder.getLabCode(), remarkOld));
        }
        return;
    }

    /**
     *
     * @param noCopyTestLineMaps
     * @param testLineId
     * @param testLineName
     * @param testLineType
     * @param reason
     */
    private void setNoCopyTestLineInfo(Map<Integer, NoCopyTestLineInfo> noCopyTestLineMaps, Integer testLineId, String testLineName, NoCopyTestLineType testLineType, String reason){
        if (noCopyTestLineMaps.containsKey(testLineId)){
            return;
        }
        NoCopyTestLineInfo noCopyTestLine = new NoCopyTestLineInfo();
        noCopyTestLine.setTestLineId(testLineId);
        noCopyTestLine.setType(testLineType.getCode());
        noCopyTestLine.setTestLineName(testLineName);
        noCopyTestLine.setReason(reason);
        noCopyTestLineMaps.put(testLineId, noCopyTestLine);
    }

    /**
     * @param reqObject
     * @return
     */
    @Override
    public CustomResult doCopy(TestLineCopyInfo reqObject) {
        CustomResult rspResult = new CustomResult();

        if(Func.isEmpty(reqObject)){
            rspResult.setSuccess(true);
            return rspResult;
        }
        // 2020年7月29日 新增多语言插入
        List<OrderLanguageRelInfoPO> orderLanguages = reqObject.getOrderLanguages();
        if(CollectionUtils.isNotEmpty(orderLanguages)){
            orderLangRelService.batchInsertOrUpdate(reqObject.getOrderId(), orderLanguages);
        }

        List<TestLineInstancePO> testLines = reqObject.getTestLines();
        rspResult.setSuccess(testLines == null || testLines.isEmpty());
        if (rspResult.isSuccess()){
            return rspResult;
        }
        OrderCopyType copyType = reqObject.getCopyType();
        if (copyType != null){
            switch (copyType){
//                case CopyReport:
////                    rspResult.setSuccess(testLineStatusService.batchUpdateTestLineStatus(TestLineModuleType.CopyReport, testLines).isSuccess());
//                    return rspResult;
                case SubContract:
                case SubContractSync:
                case NewSubContract:
                case LightSubContract:
                    //  TODO  已在 SubContractRelCopyService 中设置
//                    // 针对新增的TL 更新原单的TL状态，因为新增的TL原单的TL 肯定是typing状态，所以可以直接按照id，status 直接更新705
//                    Map<String, String> oldTestLineIds = reqObject.getOldTestLineIds();
//                    if (oldTestLineIds == null || oldTestLineIds.isEmpty()) {
//                        break;
//                    }
//                    List<TestLineInstancePO> needUpdateTestLines = Lists.newArrayList();
//                    oldTestLineIds.keySet().forEach(testLineInstanceId->{
//                        TestLineInstancePO testLine = new TestLineInstancePO();
//                        testLine.setID(testLineInstanceId);
//                        testLine.setTestLineStatus(TestLineStatus.Entered.getStatus());
//                        needUpdateTestLines.add(testLine);
//                    });
//                    testLineStatusService.batchUpdateTestLineStatus(TestLineModuleType.CreatSubContract, needUpdateTestLines, TestLineStatus.Typing);
                    break;
            }
        }
        rspResult.setSuccess(testLineMapper.batchInsert(testLines) > 0);

        List<PPTestLineRelationshipInfoPO> ppTestLineRels = reqObject.getPpTestLineRels();
        if (ppTestLineRels != null && !ppTestLineRels.isEmpty()){
            ppTestLineRelMapper.batchInsert(ppTestLineRels);
        }
        List<TestLineInstanceMultipleLanguageInfoPO> languages = reqObject.getLanguages();
        if(Func.isNotEmpty(languages)){
            testLineMapper.batchSaveMultiLanguageTestLine(languages);
        }

        if (rspResult.isSuccess() && CollectionUtils.isNotEmpty(reqObject.getDelTestLineIds())) {
            rspResult.setSuccess(testLineMapper.batchDelete(reqObject.getDelTestLineIds()) > 0);
        }
        if (rspResult.isSuccess() && CollectionUtils.isNotEmpty(reqObject.getDelPPTLRelIds())) {
            rspResult.setSuccess(ppTestLineRelMapper.batchDelete(reqObject.getDelPPTLRelIds()) > 0);
        }
        if (rspResult.isSuccess() && CollectionUtils.isNotEmpty(reqObject.getCancelTestLine())) {
//            testLineStatusService.updateTestLineStatus()
            rspResult.setSuccess(testLineStatusService.batchUpdateTestLineStatus(TestLineModuleType.CancelTestLine, reqObject.getCancelTestLine()).isSuccess());
        }

        // 更新preorder状态 从reporting 到testing
        SysStatusReq reqStatus = reqObject.getSyncStatus();
        if(reqStatus != null){
            statusClient.insertStatusInfo(reqStatus);
        }

        return rspResult;
    }

    /**
     *
     * @param ppVersionId
     * @param sectionLevel
     * @param rootSectionMaps
     * @param ppArtifactRel
     */
    private void setRootSectionInfo(int ppVersionId, Integer sectionLevel, Map<Integer, List<PPSectionBaseInfoPO>> rootSectionMaps, NewPPArtifactRelInfo ppArtifactRel){
        if (ppVersionId <= 0 || sectionLevel == null || sectionLevel.intValue() <= 0){
            return;
        }
        List<PPSectionBaseInfoPO> rootSections;
        if (rootSectionMaps.containsKey(ppVersionId)){
            rootSections = rootSectionMaps.get(ppVersionId);
        }else{
            rootSections = ppSectionMapper.getRootSectionInfoList(ppVersionId);
            rootSectionMaps.put(ppVersionId, rootSections);
        }
        if (rootSections == null || rootSections.isEmpty()){
            return;
        }
        PPSectionBaseInfoPO rootPpSection = rootSections.stream().filter(ppSection -> NumberUtil.equals(ppSection.getSectionLevel(), sectionLevel)).findFirst().orElse(null);
        if (rootPpSection != null){
            ppArtifactRel.setSectionId(rootPpSection.getSectionId());
            ppArtifactRel.setSectionLevel(rootPpSection.getSectionLevel());
            ppArtifactRel.setSectionName(rootPpSection.getSectionText());
        }
    }

    /**
     *
     * @param orderId
     * @param newTestLine
     * @param citationRel
     * @param testLineMaps
     * @param testLineVersionIdMaps
     * @param newClaimFromTLVersionIdSet
     * @param citationVersionIdMaps
     * @param oldLanguageRelMaps
     * @param langRels
     * @param isUpgradeTestLine
     * @return
     */
    private String getTestLineInfo(
            SysCopyInfo reqParams,
            String orderId,
         NewTestLineInfo newTestLine,
         NewArtifactCitationRelInfo citationRel,
         Map<String, TestLineInstancePO> testLineMaps,
         Map<String, Integer> testLineVersionIdMaps,
         Set<Integer> newClaimFromTLVersionIdSet,
         Map<String, Integer> citationVersionIdMaps,
         Map<String, OrderLanguageRelInfoPO> oldLanguageRelMaps,
         List<OrderLanguageRelInfoPO> langRels,
         boolean isUpgradeTestLine,
            NewPpTestLineInfo ppTestLineInfo,
            OrderCopyType orderCopyType
    ){

        //查询BU是否合并TL的配置
        Boolean isMergeTl = true;
        BuParamReq buParamReq = new BuParamReq();
        buParamReq.setGroupCode(Constants.BU_PARAM.MATRIX.GROUP);
        buParamReq.setParamCode(Constants.BU_PARAM.MATRIX.MERGE_TEST_LINE.CODE);
        buParamReq.setProductLineCode(ProductLineContextHolder.getProductLineCode());
        List<BuParamValueRsp> buParamValueRsps = frameWorkClient.getBuParams(buParamReq);
        if (Func.isNotEmpty(buParamValueRsps) && Func.isNotEmpty(buParamValueRsps.get(0).getParamValue())
                && Func.equals(Constants.BU_PARAM.MATRIX.MERGE_TEST_LINE.VALUES.NO,buParamValueRsps.get(0).getParamValue())){
            isMergeTl = false;
        }

        GetCitationBaseInfoRsp citationBaseInfo = citationClient.getCitationBaseInfo(isUpgradeTestLine ? citationRel.getCitationBaseId() : newTestLine.getOldCitationBaseId());
        GetSaveTestLineKeyReq getSaveTestLineKeyReq =  new GetSaveTestLineKeyReq();
        getSaveTestLineKeyReq.setTestLineVersionId(isUpgradeTestLine?newTestLine.getTestLineVersionId():newTestLine.getOldTestLineVersionId());
        getSaveTestLineKeyReq.setCitationVersionId(isUpgradeTestLine ? citationRel.getCitationVersionId() : newTestLine.getOldCitationVersionId());
        getSaveTestLineKeyReq.setCitationSectionId(Func.isNotEmpty(citationBaseInfo)?citationBaseInfo.getCitationSectionId():0);
        getSaveTestLineKeyReq.setCitationType(Func.isNotEmpty(citationBaseInfo)?citationBaseInfo.getCitationType():0);
        getSaveTestLineKeyReq.setAid(Func.isNotEmpty(ppTestLineInfo) && Func.isNotEmpty(ppTestLineInfo.getAid())?Long.valueOf(ppTestLineInfo.getAid()):0);
        getSaveTestLineKeyReq.setTestLineType(newTestLine.getTestLineType());
        getSaveTestLineKeyReq.setMergePpTl(isMergeTl);
        String testLineCitationKey = testLineCmdService.getSaveTestLineKey(getSaveTestLineKeyReq);
        String testLineCitationKey2 = String.format("%s_%s_%s"
                ,newTestLine.getTestLineId()
                ,NumberUtil.toInt(isUpgradeTestLine ? citationRel.getCitationVersionId() : newTestLine.getOldCitationVersionId())
                ,NumberUtil.toInt(isUpgradeTestLine ? citationRel.getCitationSectionId() : newTestLine.getOldCitationSectionId()));

        TestLineInstancePO testLine = testLineMaps.get(testLineCitationKey);
        if (testLine != null){
            return testLine.getID();
        }
        UserInfo localUser = UserHelper.getLocalUser();
        testLine = new TestLineInstancePO();
        testLine.setID(UUID.randomUUID().toString());
        testLine.setGeneralOrderInstanceID(orderId);
        testLine.setOrderNo(reqParams.getOrderNo());
        testLine.setLabId(NumberUtil.toInt(reqParams.getLabBu().getLabId()));
        testLine.setTestLineID(newTestLine.getTestLineId());

        if (isUpgradeTestLine){
            testLine.setTestLineBaseId(newTestLine.getTestLineBaseId());
            testLine.setTestLineVersionID(newTestLine.getTestLineVersionId());
            testLine.setLabSectionBaseId(newTestLine.getLabSectionBaseId());

            testLine.setCitationBaseId(citationRel.getCitationBaseId());
            testLine.setCitationId(citationRel.getCitationId());
            testLine.setCitationVersionId(citationRel.getCitationVersionId());
        }else{
            testLine.setTestLineBaseId(newTestLine.getOldTestLineBaseId());
            testLine.setTestLineVersionID(newTestLine.getOldTestLineVersionId());
            testLine.setLabSectionBaseId(newTestLine.getOldLabSectionBaseId());

            testLine.setCitationBaseId(newTestLine.getOldCitationBaseId());
            testLine.setCitationId(newTestLine.getCitationId());
            testLine.setCitationVersionId(newTestLine.getOldCitationVersionId());
        }
        String documentReviewMode = this.getDocumentReviewMode();
        testLine.setTestLineStatus(TestLineStatus.Typing.getStatus());
        testLine.setDocumentReviewFlag(DocumentReviewFlag.None.getStatus());
        if(TestLineStatus.check(testLine.getTestLineStatus(),TestLineStatus.DR)){
            if(StringUtils.equalsIgnoreCase(documentReviewMode,Constants.BU_PARAM.TEST_LINE.DocumentReviewMode.VALUES.CONCLUSION)){
                testLine.setDocumentReviewFlag(DocumentReviewFlag.Conclusion.getStatus());
            }else{
                testLine.setDocumentReviewFlag(DocumentReviewFlag.Testing_Conclusion.getStatus());
            }
        }else if(Func.isNotEmpty(newTestLine.getDocumentReviewFlag())){
            testLine.setDocumentReviewFlag(newTestLine.getDocumentReviewFlag());
        }
        Integer conditionStatus = newTestLine.getConditionStatus();
        if (conditionStatus != null && conditionStatus.compareTo(Confirmed.getStatus()) == 0) {
            conditionStatus = UnConfirmed.getStatus();
        }
        testLine.setConditionStatus(conditionStatus);

//        Integer testLineType = NumberUtil.toInt(newTestLine.getTestLineType());
//        testLine.setTestLineType(testLineType);
//        if(TestLineType.check(testLineType, TestLineType.SubContractOrder)){
//            testLine.setTestLineType(testLineType.intValue() ^ TestLineType.SubContractOrder.getType());
//        }
        // 重新计算 testLineType
        Integer testLineType = TestLineType.None.getType();
        if (TrimsTestLineType.check(newTestLine.getTestLineType(), TrimsTestLineType.PRETREATMENT)) {
            testLineType |= TestLineType.Pretreatment.getType();
        }
        if (ArtifactType.check(newTestLine.getArtifactType(), ArtifactType.PP)){
            testLineType |= TestLineType.CSPP.getType();
        }
        // DIG-6001 根据TestLineVersionId 判断是否有claim值
        if (newClaimFromTLVersionIdSet.contains(testLine.getTestLineVersionID())) {
            testLineType |= TestLineType.Claim.getType();
        }
        if(TrimsTestLineType.check(newTestLine.getTestLineType(),TrimsTestLineType.OOB_TEST)){
            testLineType |= TestLineType.OOB_TEST.getType();
            testLine.setCustomerTestLineNameCN(newTestLine.getCustomerTestLineNameCN());
            testLine.setCustomerTestLineName(newTestLine.getCustomerTestLineName());
        }
        testLine.setTestLineType(testLineType);

        // EnquiryCopy出的TL需要Copy旧单的TestLineType
        if(orderCopyType.check(EnquiryCopy)){
            testLine.setTestLineType(newTestLine.getOrderTestLineType());
        }

        testLine.setProductLineAbbr(newTestLine.getProductLineAbbr());
        testLine.setOrdertestLineRemark(newTestLine.getTestLineRemark());
        testLine.setModified(null);   // 初始值 null
        testLine.setFileID(null);
        testLine.setDataEntryCloudID(null);
        testLine.setCalculateConclusionFlag(null);    // 初始值
        testLine.setActiveIndicator(true);
        testLine.setCreatedBy(localUser.getRegionAccount());
        testLine.setCreatedDate(DateUtils.getNow());
        testLine.setModifiedBy(localUser.getRegionAccount());
        testLine.setModifiedDate(DateUtils.getNow());
        testLine.setValidateDate(null);
        testLine.setValidateBy(null);
        // DIG-5138 New By Copy / Copy Detail From 不Copy 设置0
        testLine.setPendingFlag(false);
        // GPO2-1456 添加WI for sample 相关字段
        testLine.setSampleSegegrationWIID(newTestLine.getSampleSegegrationWIID());
        testLine.setSampleSegegrationWIText(newTestLine.getSampleSegegrationWIText());
        testLine.setOrderSeq(newTestLine.getOrderSeq());
        testLine.setLabTeamCode(newTestLine.getLabTeamCode());
        //DIG-6949
        testLine.setStyleVersionId(0);
        testLine.setClientStandard(newTestLine.getClientStandard());
        testLine.setCitationName(newTestLine.getCitationName());
        //获取编码
        String externalOrderNo = reqParams.getExternalOrderNo();
        if(Func.isEmpty(externalOrderNo)){
            externalOrderNo = reqParams.getOrderNo();
        }
        String number = null;
        try {
            number = codeUtil.getTestItemNo(externalOrderNo,testLine.getTestLineID());
        } catch (Exception e) {
           throw new BizException("获取TestItemNo失败:"+e.getMessage());
        }

        testLine.setTestItemNo(number);
        //
        if(reqParams.getCopyType().check(ChargeOrder)){
            testLine.setEngineer(newTestLine.getEngineer());
        }
        testLineMaps.put(testLineCitationKey, testLine);

        // region 【TestLine Lang Info】
        OrderLanguageRelInfoPO langRel = new OrderLanguageRelInfoPO();
        if (isUpgradeTestLine){
            langRel.setOrderId(orderId);
            langRel.setObjectBaseId(testLine.getTestLineBaseId());
            langRel.setLangBaseId(newTestLine.getLangBaseId());
            langRel.setLanguageId(newTestLine.getLanguageId());
            langRel.setLangType(LangTypeEnum.TestLine.getType());
        }else{
            langRel = oldLanguageRelMaps.get(String.format("%s_%s_%s", testLine.getTestLineBaseId(), newTestLine.getLanguageId(), LangTypeEnum.TestLine.getType()));
        }
        this.getOrderLanguageRelInfo(langRels, orderId, langRel);
        // endregion

        // region 【Citation Lang Info】
        langRel = new OrderLanguageRelInfoPO();
        if (isUpgradeTestLine){
            langRel.setOrderId(orderId);
            langRel.setObjectBaseId(testLine.getCitationBaseId());
            langRel.setLangBaseId(citationRel.getLangBaseId());
            langRel.setLanguageId(citationRel.getLanguageId());
            langRel.setLangType(LangTypeEnum.Citation.getType());
        }else {
            langRel = oldLanguageRelMaps.get(String.format("%s_%s_%s", testLine.getCitationBaseId(), citationRel.getLanguageId(), LangTypeEnum.Citation.getType()));
        }
        this.getOrderLanguageRelInfo(langRels, orderId, langRel);
        // endregion

        testLineVersionIdMaps.put(testLine.getID(), testLine.getTestLineVersionID());
        citationVersionIdMaps.put(testLine.getID(), testLine.getCitationVersionId());
        return testLine.getID();
    }

    /**
     *
     * @param orderId
     * @param testLineInstanceId
     * @param rootPpBaseId
     * @param ppTestLineRelMaps
     * @param langRels
     * @param ppArtifactRel
     * @return
     */
    private String getPpTestLineRelInfo(String orderId,
                                        String testLineInstanceId,
                                        Long rootPpBaseId,
                                        Map<String, PPTestLineRelationshipInfoPO> ppTestLineRelMaps,
                                        List<OrderLanguageRelInfoPO> langRels,
                                        NewPPArtifactRelInfo ppArtifactRel,
                                        Long seq,
                                        Map<String, Set<Long>> testLineInstanceIdAids){
        if (ppArtifactRel == null){
            ppArtifactRel = new NewPPArtifactRelInfo();
        }
        Long ppBaseId = NumberUtil.toLong(ppArtifactRel.getPpBaseId());
        Long artifactId = NumberUtil.toLong(ppArtifactRel.getArtifactId());
        String ppTestLineRelKey = String.format("%s_%s_%s", ppBaseId, artifactId, testLineInstanceId);
        PPTestLineRelationshipInfoPO ppTestLineRel = ppTestLineRelMaps.get(ppTestLineRelKey);
        if (ppTestLineRel != null){
            return ppTestLineRel.getID();
        }

        UserInfo localUser = UserHelper.getLocalUser();
        OrderLanguageRelInfoPO langRel;

        // TODO 检查SectionBaseId 是否一致
        // TODO PP TestLine LangInfo
        ppTestLineRel = new PPTestLineRelationshipInfoPO();
        ppTestLineRel.setID(UUID.randomUUID().toString());
        ppTestLineRel.setGeneralOrderInstanceID(orderId);
        ppTestLineRel.setPpArtifactRelId(NumberUtil.toLong(ppArtifactRel.getPpArtifactRelId()));
        ppTestLineRel.setPpBaseId(NumberUtil.toLong(ppArtifactRel.getPpBaseId()));
        // TODO 获最新的Root PpBaseId
        ppTestLineRel.setRootPpBaseId(NumberUtil.toLong(rootPpBaseId));

        ppTestLineRel.setTestLineInstanceID(testLineInstanceId);
        // 获取Root SectionId
        ppTestLineRel.setSectionID(ppArtifactRel.getSectionId());
        ppTestLineRel.setSectionName(ppArtifactRel.getSectionName());
        int sectionLevel = NumberUtil.toInt(ppArtifactRel.getSectionLevel());
        if (sectionLevel > 0){
            ppTestLineRel.setSectionLevel(String.valueOf(sectionLevel));
        }

        ppTestLineRel.setAid(NumberUtil.toLong(ppArtifactRel.getArtifactId()));
        ppTestLineRel.setConstructionId(ppArtifactRel.getConstructionId());

        ppTestLineRel.setCreatedBy(localUser.getRegionAccount());
        ppTestLineRel.setCreatedDate(DateUtils.getNow());
        ppTestLineRel.setModifiedBy(localUser.getRegionAccount());
        ppTestLineRel.setModifiedDate(DateUtils.getNow());
        ppTestLineRel.setSeq(seq);
        // 设置最新SubPpRelSeq
        protocolPackageTestLineRelationshipService.buildSubPpSeq(Lists.newArrayList(ppTestLineRel));

        ppTestLineRelMaps.put(ppTestLineRelKey, ppTestLineRel);

        // 保存  <TestLineInstanceId, Set<aid>>
        if (testLineInstanceIdAids.containsKey(testLineInstanceId)) {
            testLineInstanceIdAids.get(testLineInstanceId).add(ppTestLineRel.getAid());
        } else {
            testLineInstanceIdAids.put(testLineInstanceId, Sets.newHashSet(ppTestLineRel.getAid()));
        }

        // region 【PP Lang Info】

        langRel = new OrderLanguageRelInfoPO();
        langRel.setOrderId(orderId);
        langRel.setObjectBaseId(ppArtifactRel.getPpBaseId());
        langRel.setLangBaseId(ppArtifactRel.getPpLangBaseId());
        langRel.setLanguageId(ppArtifactRel.getPpLanguageId());
        langRel.setLangType(LangTypeEnum.PP.getType());

        this.getOrderLanguageRelInfo(langRels, orderId, langRel);
        // endregion

        // region 【PP Artifact Lang Info】
        langRel = new OrderLanguageRelInfoPO();
        langRel.setOrderId(orderId);
        langRel.setObjectBaseId(ppArtifactRel.getPpArtifactRelId());
        langRel.setLangBaseId(ppArtifactRel.getPpArtifactLangBaseId());
        langRel.setLanguageId(ppArtifactRel.getPpArtifactLanguageId());
        langRel.setLangType(LangTypeEnum.PpArtifactRel.getType());

        this.getOrderLanguageRelInfo(langRels, orderId, langRel);
        // endregion

        return ppTestLineRel.getID();
    }

    /**
     *
     * @param langRels
     * @param orderId
     * @param languageRel
     */
    private void getOrderLanguageRelInfo(List<OrderLanguageRelInfoPO> langRels, String orderId, OrderLanguageRelInfoPO languageRel){
        if (languageRel == null ||
            NumberUtil.toLong(languageRel.getObjectBaseId()) <= 0 ||
            NumberUtil.toLong(languageRel.getLangBaseId()) <= 0 ||
            NumberUtil.toInt(languageRel.getLanguageId()) <= 0){
            return;
        }
        OrderLanguageRelInfoPO langRel = new OrderLanguageRelInfoPO();
        langRel.setOrderId(orderId);
        langRel.setObjectBaseId(languageRel.getObjectBaseId());
        langRel.setLangBaseId(languageRel.getLangBaseId());
        langRel.setLanguageId(languageRel.getLanguageId());
        langRel.setLangType(languageRel.getLangType());
        langRel.setLangStatus(1);
        langRel.setCreatedDate(DateUtils.getNow());
        langRel.setModifiedDate(DateUtils.getNow());

        langRels.add(langRel);
    }

    /**
     *
     * @param rootPpBaseId
     * @param ppMaps
     * @return
     */
    private PPBaseInfoPO getNewPpInfo(Long rootPpBaseId, Map<Long, PPBaseInfoPO> ppMaps) {
        if (rootPpBaseId == null || rootPpBaseId.longValue() <= 0) {
            return null;
        }
        if (ppMaps.containsKey(rootPpBaseId)){
            return ppMaps.get(rootPpBaseId);
        }
        PPBaseInfoPO newPp = ppBaseMapper.getNewPpById(rootPpBaseId);
        if (newPp == null){
            return null;
        }
        ppMaps.put(rootPpBaseId, newPp);
        return newPp;
    }

    /**
     *
     * @param rootPpVersionId
     * @param subPpVersionId
     * @param subPpVersionIdMaps
     * @return
     */
    private boolean checkSubPpVersionId(Integer rootPpVersionId, Integer subPpVersionId, Map<Integer, Set<Integer>> subPpVersionIdMaps) {
        if (rootPpVersionId == null || rootPpVersionId.intValue() <= 0){
            return false;
        }
        if (subPpVersionIdMaps.containsKey(rootPpVersionId)){
            if (subPpVersionIdMaps.get(rootPpVersionId).contains(subPpVersionId)){
                return true;
            }
        }
        List<Integer> ppVersionIds = ppArtifactRelMapper.getPpArtifactSubPpVersionId(rootPpVersionId);
        if (ppVersionIds == null || ppVersionIds.isEmpty()){
            return false;
        }
        if (subPpVersionIdMaps.containsKey(rootPpVersionId)){
            if (subPpVersionIdMaps.get(rootPpVersionId).contains(subPpVersionId)){
                return true;
            }
        }
        Set<Integer> subPpVersionIds = Sets.newHashSet();
        for (Integer ppVersionId : ppVersionIds) {
            if (NumberUtil.equals(ppVersionId, subPpVersionId)){
                subPpVersionIds.add(ppVersionId);
                break;
            }
            findSubPpVersionIds(ppVersionId, subPpVersionId, subPpVersionIds);
        }
        if (subPpVersionIds == null || subPpVersionIds.isEmpty()){
            return false;
        }
        if (!subPpVersionIdMaps.containsKey(rootPpVersionId)){
            subPpVersionIdMaps.put(rootPpVersionId, Sets.newHashSet());
        }
        if (subPpVersionIds.contains(subPpVersionId)){
            return true;
        }
        subPpVersionIdMaps.get(rootPpVersionId).addAll(subPpVersionIds);
        return false;
    }

    /**
     *
     * @param parentVersionId
     * @param subPpVersionId
     * @param subPpVersionIds
     */
    private void findSubPpVersionIds(Integer parentVersionId, Integer subPpVersionId, Set<Integer> subPpVersionIds) {
        if (parentVersionId == null || parentVersionId.longValue() <= 0){
            return;
        }
        List<Integer> ppVersionIds = ppArtifactRelMapper.findSubPpVersionIds(parentVersionId);
        if (ppVersionIds == null || ppVersionIds.isEmpty()){
            return;
        }
        for (Integer ppVersionId : ppVersionIds) {
            if (NumberUtil.equals(ppVersionId, subPpVersionId)){
                subPpVersionIds.add(ppVersionId);
                break;
            }
            findSubPpVersionIds(ppVersionId, subPpVersionId, subPpVersionIds);
        }
    }
    private String getDocumentReviewMode(){
        String documentReviewMode = Constants.BU_PARAM.TEST_LINE.DocumentReviewMode.VALUES.CONCLUSION;
        BuParamReq buParamReqWithDRMode = new BuParamReq();
        buParamReqWithDRMode.setProductLineCode(ProductLineContextHolder.getProductLineCode());
        buParamReqWithDRMode.setLabCode(SecurityUtil.getLabCode());
        buParamReqWithDRMode.setGroupCode(Constants.BU_PARAM.TEST_LINE.GROUP);
        buParamReqWithDRMode.setParamCode(Constants.BU_PARAM.TEST_LINE.DocumentReviewMode.CODE);
        BuParamValueRsp buParam = frameWorkClient.getBuParam(buParamReqWithDRMode);
        if(Func.isNotEmpty(buParam)){
            documentReviewMode = buParam.getParamValue();
        }
        return documentReviewMode;
    }
}
