package com.sgs.otsnotes.domain.config;

import com.sgs.framework.core.base.BaseResponse;
import com.sgs.otsnotes.facade.model.rsp.NacosConfigRsp;

/**
 * <AUTHOR>
 * @title: NacosConfigService
 * @projectName otsnotes-service
 * @description: TODO
 * @date 2022/9/1416:02
 */
public interface INacosConfigService {
    BaseResponse<NacosConfigRsp> getBatchLimitConfig();
    BaseResponse<NacosConfigRsp> getStandardConfig();
}
