package com.sgs.otsnotes.domain.service.copy;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sgs.core.domain.UserInfo;
import com.sgs.framework.core.base.CustomResult;
import com.sgs.otsnotes.core.common.UserHelper;
import com.sgs.otsnotes.core.util.DateUtils;
import com.sgs.otsnotes.core.util.NumberUtil;
import com.sgs.otsnotes.dbstorages.mybatis.extmapper.LimitLanguageMapper;
import com.sgs.otsnotes.dbstorages.mybatis.extmapper.LimitMapper;
import com.sgs.otsnotes.dbstorages.mybatis.extmodel.LimitCopyInfo;
import com.sgs.otsnotes.dbstorages.mybatis.model.LimitInstancePO;
import com.sgs.otsnotes.dbstorages.mybatis.model.LimitMultipleLanguageInfoPO;
import com.sgs.otsnotes.facade.model.annotation.CopyEventType;
import com.sgs.otsnotes.facade.model.annotation.CopyServiceType;
import com.sgs.otsnotes.facade.model.enums.OrderBizType;
import com.sgs.otsnotes.facade.model.enums.OrderCopyType;
import com.sgs.otsnotes.facade.model.enums.TableType;
import com.sgs.otsnotes.facade.model.ordercopy.SysCopyInfo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

import static com.sgs.otsnotes.facade.model.enums.OrderCopyType.*;

@Service
@CopyServiceType(copyType = {
    Extract,
    Replace,
    AmendReport,
    Sample,
    Conclusion,
    TestLine,
    SubContract,
    SubContractSync,
    LightSubContract,
    NewSubContract,
    CopyReport
}, bizType = OrderBizType.Limit)
public class LimitCopyService extends BaseCopyService<String, LimitCopyInfo> {
    private static final Logger logger = LoggerFactory.getLogger(LimitCopyService.class);
    @Autowired
    private LimitMapper limitMapper;
    @Autowired
    private LimitLanguageMapper limitLanguageMapper;

    /**
     * @param reqObject1
     * @param reqObject
     * @return
     */
    @Override
    protected CustomResult<LimitCopyInfo> doInvoke(String reqObject1, SysCopyInfo reqObject) {
        CustomResult rspResult = new CustomResult();
        Map<String, String>
        oldTestMatrixIds = reqObject.getOldTestMatrixIds(),
        oldTestLineInstanceIds = reqObject.getOldTestLineInstanceIds(),
        oldSampleIds = reqObject.getOldSampleIds(),
        oldPpIds = reqObject.getOldPpIds();
        UserInfo localUser = UserHelper.getLocalUser();

        List<LimitInstancePO> oldLimits = limitMapper.getLimitListByOrderId(reqObject.getOldOrderId());

        List<LimitInstancePO> limits = Lists.newArrayList();
        Map<String, String> oldLimitIds = Maps.newHashMap();
        for (LimitInstancePO oldLimit: oldLimits) {
            if (!(oldTestLineInstanceIds.containsKey(oldLimit.getTestLineInstanceID()) && oldSampleIds.containsKey(oldLimit.getTestSampleID()) && oldTestMatrixIds.containsKey(oldLimit.getTestMatrixID()))) {
                continue;
            }
            String oldLimitId = oldLimit.getID();
            String newLimitId = this.getPrimaryKeyId(reqObject, oldLimitId, TableType.Limit);
            if (!this.isNewRelId(reqObject, newLimitId, TableType.Limit)){
                continue;
            }
            if(this.isCancelTestLine(reqObject, oldLimit.getTestLineInstanceID())){
                continue;
            }
            if (!this.isNewRelId(reqObject, oldLimit.getTestLineInstanceID(), TableType.TestLine)){
                continue;
            }
            oldLimit.setID(newLimitId);
            oldLimitIds.put(oldLimitId, newLimitId);
            oldLimit.setTestMatrixID(oldTestMatrixIds.get(oldLimit.getTestMatrixID()));
            oldLimit.setTestLineInstanceID(oldTestLineInstanceIds.get(oldLimit.getTestLineInstanceID()));
            oldLimit.setTestSampleID(oldSampleIds.get(oldLimit.getTestSampleID()));

            oldLimit.setPPId(oldPpIds.get(oldLimit.getPPId()));
            oldLimit.setPpBaseId(NumberUtil.toLong(oldLimit.getPpBaseId()));
            oldLimit.setTalBaseId(NumberUtil.toLong(oldLimit.getTalBaseId()));

            oldLimit.setActiveIndicator(true);
            oldLimit.setCreatedBy(localUser.getRegionAccount());
            oldLimit.setCreatedDate(DateUtils.getNow());
            oldLimit.setModifiedBy(localUser.getRegionAccount());
            oldLimit.setModifiedDate(DateUtils.getNow());
            limits.add(oldLimit);
        }

        LimitCopyInfo rspObject = new LimitCopyInfo();
        rspObject.setLimits(limits);
        rspObject.setOldLimitIds(oldLimitIds);
        OrderCopyType copyType = reqObject.getCopyType();
        if (!copyType.check(SubContractSync)) {
            rspObject.setDelLimitIds(this.getDelPrimaryKeyIds(reqObject, TableType.Limit));
        }

        rspResult.setData(rspObject);
        rspResult.setSuccess(true);
        return rspResult;
    }

    /**
     * @param reqObject
     * @return
     */
    @Override
    public CustomResult doCopy(LimitCopyInfo reqObject) {
        CustomResult rspResult = new CustomResult();
        List<LimitMultipleLanguageInfoPO> languages = reqObject.getLanguages();
        if (languages != null && !languages.isEmpty()){
            limitLanguageMapper.batchInsert(languages);
        }

        List<String> delLimitIds = reqObject.getDelLimitIds();
        if (delLimitIds != null && !delLimitIds.isEmpty()){
            limitMapper.batchDelete(delLimitIds);
        }

        List<LimitInstancePO> limits = reqObject.getLimits();
        rspResult.setSuccess(limits == null || limits.isEmpty());
        if (rspResult.isSuccess()){
            return rspResult;
        }
        rspResult.setSuccess(limitMapper.batchInsert(limits) > 0);
        return rspResult;
    }

    /**
     *
     * @param reqObject
     * @param rspObject
     * @return
     */
    @CopyEventType(copyType = {Extract, Replace, AmendReport, Sample, Conclusion, TestLine, SubContract, NewSubContract, LightSubContract, SubContractSync}, execSeq = 1)
    public void doCopyLanguage(SysCopyInfo reqObject, LimitCopyInfo rspObject){
        Map<String, String> oldLimitIds = rspObject.getOldLimitIds();
        if (oldLimitIds == null || oldLimitIds.isEmpty()){
            return;
        }

        List<LimitMultipleLanguageInfoPO> oldLimitLanguages = limitLanguageMapper.getLimitLanguageListByOrderId(reqObject.getOldOrderId());
        if (oldLimitLanguages == null || oldLimitLanguages.isEmpty()){
            return;
        }
        UserInfo localUser = UserHelper.getLocalUser();

        List<LimitMultipleLanguageInfoPO> limitLanguages = Lists.newArrayList();
        for (LimitMultipleLanguageInfoPO limitLanguage : oldLimitLanguages) {
            if (!oldLimitIds.containsKey(limitLanguage.getLimitInstanceID())) {
                continue;
            }
            limitLanguage.setID(this.getPrimaryKeyId(reqObject, limitLanguage.getID(), TableType.LimitLanguage));
            limitLanguage.setLimitInstanceID(oldLimitIds.get(limitLanguage.getLimitInstanceID()));

            limitLanguage.setCreatedBy(localUser.getRegionAccount());
            limitLanguage.setCreatedDate(DateUtils.getNow());
            limitLanguage.setModifiedBy(localUser.getRegionAccount());
            limitLanguage.setModifiedDate(DateUtils.getNow());
            limitLanguages.add(limitLanguage);
        }
        rspObject.setLanguages(limitLanguages);
    }
}
