package com.sgs.otsnotes.domain.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.sgs.core.domain.UserInfo;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.core.base.CustomResult;
import com.sgs.framework.facade.domain.utils.ResponseCode;
import com.sgs.framework.i18n.util.MessageUtil;
import com.sgs.framework.model.enums.LanguageType;
import com.sgs.framework.model.enums.ReportLanguage;
import com.sgs.framework.model.enums.TestLineType;
import com.sgs.framework.security.utils.SecurityUtil;
import com.sgs.framework.tool.utils.Func;
import com.sgs.framework.tool.utils.ProductLineContextHolder;
import com.sgs.framework.tool.utils.StringPool;
import com.sgs.gpo.facade.model.report.req.ReportExtForTLUpdateReq;
import com.sgs.gpo.facade.temp.ReportTempFacade;
import com.sgs.grus.bizlog.common.BizLog;
import com.sgs.grus.bizlog.common.BizLogHelper;
import com.sgs.otsnotes.core.annotation.AccessPolicyRule;
import com.sgs.otsnotes.core.common.UserHelper;
import com.sgs.otsnotes.core.constants.BizLogConstant;
import com.sgs.otsnotes.core.constants.Constants;
import com.sgs.otsnotes.core.enums.ReportFlagEnums;
import com.sgs.otsnotes.core.kafka.Producer;
import com.sgs.otsnotes.core.util.DateUtils;
import com.sgs.otsnotes.core.util.LOStringUtil;
import com.sgs.otsnotes.core.util.NumberUtil;
import com.sgs.otsnotes.core.util.Utils;
import com.sgs.otsnotes.dbstorages.mybatis.extmapper.*;
import com.sgs.otsnotes.dbstorages.mybatis.extmodel.*;
import com.sgs.otsnotes.dbstorages.mybatis.extmodel.analyte.LimitAnalytePO;
import com.sgs.otsnotes.dbstorages.mybatis.mapper.*;
import com.sgs.otsnotes.dbstorages.mybatis.model.*;
import com.sgs.otsnotes.domain.service.gpn.tracking.ITrackingService;
import com.sgs.otsnotes.domain.util.CitationUtil;
import com.sgs.otsnotes.domain.util.CodeUtil;
import com.sgs.otsnotes.facade.model.dto.CheckSubTLDTO;
import com.sgs.otsnotes.facade.model.dto.OrderLanguageDTO;
import com.sgs.otsnotes.facade.model.dto.condition.MatrixConditionCountDTO;
import com.sgs.otsnotes.facade.model.enums.*;
import com.sgs.otsnotes.facade.model.info.trims.InstructionLanguageInfo;
import com.sgs.otsnotes.facade.model.info.trims.TestAnalyteLanguage;
import com.sgs.otsnotes.facade.model.info.trims.TestLineConditionInfo;
import com.sgs.otsnotes.facade.model.info.trims.UnitForReportingLanguage;
import com.sgs.otsnotes.facade.model.kafka.GeneralMessage;
import com.sgs.otsnotes.facade.model.kafka.KafkaTopicConsts;
import com.sgs.otsnotes.facade.model.kafka.UpdatePreOrderStatusMessage;
import com.sgs.otsnotes.facade.model.req.QueryMatrixReq;
import com.sgs.otsnotes.facade.model.req.matrix.ConfirmMatrixReq;
import com.sgs.otsnotes.facade.model.req.matrix.DeleteTestReq;
import com.sgs.otsnotes.facade.model.req.matrix.QueryMatrixInfoReq;
import com.sgs.otsnotes.facade.model.req.matrix.SaveCopyTestReq;
import com.sgs.otsnotes.facade.model.req.report.ReportLangReq;
import com.sgs.otsnotes.facade.model.req.sample.AssignSampleCancelReq;
import com.sgs.otsnotes.facade.model.rsp.buparam.ConfirmMatrixRuleRsp;
import com.sgs.otsnotes.facade.model.rsp.condition.TestLineConditionRelDTO;
import com.sgs.otsnotes.facade.model.rsp.matrix.*;
import com.sgs.otsnotes.infra.repository.pp.PpRepository;
import com.sgs.otsnotes.integration.*;
import com.sgs.otsnotes.integration.trimslocal.ConditionClient;
import com.sgs.otsnotes.integration.trimslocal.TestLineClient;
import com.sgs.preorder.facade.OrderFacade;
import com.sgs.preorder.facade.StatusFacade;
import com.sgs.preorder.facade.model.dto.order.OrderAllDTO;
import com.sgs.preorder.facade.model.dto.order.ProductInstanceDTO;
import com.sgs.preorder.facade.model.req.OrderIdReq;
import com.sgs.preorder.facade.model.req.SysStatusReq;
import com.sgs.trimslocal.facade.IAnalyteFacade;
import com.sgs.trimslocal.facade.model.analyte.req.QueryTestLineAnalyteReq;
import com.sgs.trimslocal.facade.model.analyte.req.TestLineAnalyteReq;
import com.sgs.trimslocal.facade.model.analyte.rsp.AnalyteUnitLangRsp;
import com.sgs.trimslocal.facade.model.analyte.rsp.TestAnalyteLangReq;
import com.sgs.trimslocal.facade.model.analyte.rsp.TestLineAnalyteRsp;
import com.sgs.trimslocal.facade.model.analyte.rsp.TestLineAnalyteUnitRsp;
import com.sgs.trimslocal.facade.model.condition.rsp.TestConditionRsp;
import com.sgs.trimslocal.facade.model.testline.rsp.GetTestLineBaseInfoRsp;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.util.ObjectUtils;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;


@Service
public class TestMatrixService {
    private final Logger logger = LoggerFactory.getLogger(TestMatrixService.class);
    @Autowired
    private TestMatrixMapper testMatrixMapper;
    @Autowired
    private ReportMapper reportMapper;
    @Autowired
    private TestLineMapper testLineMapper;
    @Autowired
    private ReportMatrixRelMapper reportMatrixRelMapper;
    @Autowired
    private TestPositionMapper testPositionMapper;
    @Autowired
    private ConclusionMapper conclusionMapper;
    @Autowired
    private TestConditionMapper testConditionMapper;
    @Autowired
    private TransactionTemplate transactionTemplate;
    @Autowired
    private GeneralOrderInstanceMapper generalOrderInstanceMapper;
    @Autowired
    private TestSampleExtMapper testSampleExtMapper;
    @Autowired
    private TestLineDataEntryStyleInfoMapper testLineDataEntryStyleInfoMapper;
    @Autowired
    private PPMapper ppMapper;
    @Autowired
    private AnalyteMapper analyteMapper;
    @Autowired
    private AnalyteMultipleLanguageInfoMapper analyteMultipleLanguageInfoMapper;
    @Autowired
    private OrderReportClient orderReportClient;
    @Autowired
    private TestConditionGroupMapper testConditionGroupMapper;
    @Autowired
    private TestConditionLanguageMapper testConditionLanguageMapper;
    @Autowired
    private TestConditionGroupLanguageMapper testConditionGroupLanguageMapper;
    @Autowired
    private AnalyteLanguageMapper analyteLanguageMapper;
    @Autowired
    private StatusClient statusClient;
    @Autowired
    private Producer producer;
    @Autowired
    private OrderClient orderClient;
    @Autowired
    private CustomerReportRequestInfoMapper customerReportRequestInfoMapper;
    @Autowired
    private TestMatrixInfoMapper testMatrixInfoMapper;
    @Autowired
    private TestDataInfoMapper testDataInfoMapper;
    @Autowired
    private TestLineInstanceMapper testLineInstanceMapper;
    @Autowired
    private OrderMapper orderMapper;
    @Autowired
    private ReportInfoMapper reportInfoMapper;
    @Autowired
    private TestLineCustomerAppRelInfoMapper testLineCustomerAppRelInfoMapper;
    @Autowired
    private TestLineCustomerAppLangInfoMapper testLineCustomerAppLangInfoMapper;
    @Autowired
    private PPTestLineRelMapper ppTestLineRelMapper;
    @Autowired
    private PPTestLineConditionRelInfoMapper trimsPPTestLineConditionRelInfoMapper;
    @Autowired
    private TestLineConditionRelInfoMapper trimsTestLineConditionRelInfoMapper;
    @Autowired
    private LogActionInfoMapper logActionInfoMapper;
    @Autowired
    private TestLineLocalService testLineLocalService;
    @Autowired
    private UnitLanguageInfoMapper trimsUnitLanguageInfoMapper;
    @Autowired
    private OrderLanguageRelMapper orderLanguageRelMapper;
    @Autowired
    private TestLineConditionRelExtMapper testLineConditionRelExtMapper;
    @Autowired
    private TestLineClient testLineClient;
    @Autowired
    private ConditionClient conditionClient;

    private final static String CChemLab = "CChemLab";

    @Autowired
    private StatusFacade statusFacade;
    @Autowired
    private TokenClient tokenClient;

    @Autowired
    private CodeUtil codeUtil;

    @Autowired
    private OrderFacade orderFacade;
    @Autowired
    private IAnalyteFacade analyteFacade;
    @Autowired
    private TestSampleInfoMapper testSampleInfoMapper;
    @Autowired
    private ITrackingService trackingService;
    @Autowired
    private MessageUtil messageUtil;
    @Autowired
    private BUParamClient buParamClient;
    @Autowired
    private ReportTempFacade reportTempFacade;
    @Autowired
    private PpRepository ppRepository;
    @Autowired
    private CitationUtil citationUtil;
    @Autowired
    private TestLineInstanceMultipleLanguageInfoMapper testLineInstanceMultipleLanguageInfoMapper;

    /**
     * @param reqObject
     * @return
     */
    @BizLog(bizType = BizLogConstant.TEST_HISTORY, operType = "Confirm Matrix")
    public CustomResult confirmMatrix(ConfirmMatrixReq reqObject) {
        CustomResult result = new CustomResult();
        //获取Order信息
        OrderIdReq orderIdReq = new OrderIdReq();
        orderIdReq.setOrderNo(reqObject.getOrderNo());
        orderIdReq.setProductLineCode(ProductLineContextHolder.getProductLineCode());
        BaseResponse<OrderAllDTO> orderForPe = orderFacade.getOrderForPe(orderIdReq);
        if (Func.isEmpty(orderForPe) || Func.isEmpty(orderForPe.getData())) {
            result.setSuccess(false);
            result.setMsg("订单信息查询失败");
            return result;
        }
        OrderAllDTO orderAllDTO = orderForPe.getData();
        if (!Func.equals(orderAllDTO.getBUCode(), ProductLineContextHolder.getProductLineCode())) {
            result.setSuccess(false);
            result.setMsg("当前BU不允许操作当前订单！");
            return result;
        }
        // 准备校验参数
        GeneralOrderInstanceInfoPO generalOrderInstanceInfoPO = generalOrderInstanceMapper.selectGeneralOrderInstanceByOrderNo(reqObject.getOrderNo());
        String orderNo = generalOrderInstanceInfoPO.getOrderNo();
        int type = reqObject.getMatrixType();
        List<TestLineConfirmMatrixInfo> testLines = testLineMapper.getTestLineConfirmMatrixInfoByOrderNo(orderNo);
        ConfirmMatrixTypeEnum matrixType = ConfirmMatrixTypeEnum.getCode(type);
        testLines = testLines.stream().filter(testline -> {
                    boolean isChem = StringUtils.equalsIgnoreCase(testline.getProductLineAbbr(), CChemLab) || StringUtils.containsIgnoreCase(testline.getLabSectionName(), CChemLab);
                    return (matrixType == ConfirmMatrixTypeEnum.Chem && isChem) || (matrixType == ConfirmMatrixTypeEnum.Phy && !isChem) || ConfirmMatrixTypeEnum.All == matrixType;
                }).filter(testline -> !testline.getPendingFlag())//add by vincent 2021年1月13日 pending状态需要忽略
                .collect(Collectors.toList());

        // 执行业务校验
        // 获取BU配置的TestMatrix校验的Rule
        String testMatrixRule = buParamClient.getTestMatrixRule(orderAllDTO.getBUCode());
        result = this.confirmMatrixCheck(testMatrixRule, generalOrderInstanceInfoPO, type, testLines, orderAllDTO.getReportLanguage());
        if (!result.isSuccess()) {
            return result;
        }
        List<Integer> trimsTestLineIds = testLines.stream().map(TestLineConfirmMatrixInfo::getTestLineID).collect(Collectors.toList());
        List<String> testLineIds = testLines.stream().filter(t -> {
            return TestLineStatus.check(t.getTestLineStatus(), TestLineStatus.Typing, TestLineStatus.Entered, TestLineStatus.SubContracted, TestLineStatus.DR,TestLineStatus.NC);
        }).collect(Collectors.toList()).stream().map(TestLineConfirmMatrixInfo::getID).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(testLineIds)) {
            result.setSuccess(false);
            result.setMsg("All TestLine has been confirmed!");
            return result;
        }
        String testMatrixConfirmRule = buParamClient.getConfirmMatrixRule(orderAllDTO.getBUCode());
        if (Func.isNotEmpty(testMatrixConfirmRule)){
            ConfirmMatrixRuleRsp confirmMatrixRuleRsp = JSON.parseObject(testMatrixConfirmRule, ConfirmMatrixRuleRsp.class);
            if(Func.isNotEmpty(confirmMatrixRuleRsp) && Func.isNotEmpty(confirmMatrixRuleRsp.getCheckProductionLine())){
                ConfirmMatrixRuleRsp.ConfirmMatrixRule checkProductionLine = confirmMatrixRuleRsp.getCheckProductionLine();
                String productionLine = checkProductionLine.getCheckRule().getProductionLine();
                List<Integer> testLineType = checkProductionLine.getExcludeRule().getTestLineType();
                List<String> tlIds = new ArrayList<>();
                for (Integer tlType : testLineType){
                    tlIds.addAll(testLines.stream().filter(tl -> !TestLineType.check(tl.getTestLineType(),TestLineType.findType(tlType)) && !Func.equalsSafe(productionLine, tl.getBu()))
                            .map(tl -> Func.toStr(tl.getTestLineID()))
                            .collect(Collectors.toList()));
                }
                if (Func.isNotEmpty(tlIds)){
                    String tlId = String.join(StringPool.COMMA, tlIds);
                    result.setSuccess(false);
                    result.setMsg("TL id："+tlId+"，无测试能力，请更正后再confirm");
                    return result;
                }
            }
        }
        //拿到配置了样式的testLine
        TestLineDataEntryStyleInfoExample example = new TestLineDataEntryStyleInfoExample();
        example.createCriteria().andTestLineIDIn(trimsTestLineIds);
        List<TestLineDataEntryStyleInfoPO> testLineDataEntryStyles = testLineDataEntryStyleInfoMapper.selectByExample(example);
        if (CollectionUtils.isEmpty(testLineDataEntryStyles)) {
            testLineDataEntryStyles = Lists.newArrayList();
        }
        Set<Integer> styleTrimsTestLineIDSet = testLineDataEntryStyles.stream()
                .filter(style -> style.getActiveIndicator() == true)
                .map(TestLineDataEntryStyleInfoPO::getTestLineID).collect(Collectors.toSet());
        /**
         * DIG-2976  已废除
         * 2019-7-31 14:31:51
         * 1. Confirm matrix时，对于符合如下条件的TL，不存储相关analyte
         a. 该TL在Notes DB没有配置Data Entry Style
         并且
         b. 该TL的ProductLineAbbr='CChemLab' （tb_test_line_instance）
         */
        // DIG-5575 先将分包的排除掉不去拿Analyte，然后剩下的再将无样式的TL排除掉不去拿Analyte
        // DIG-5717 需求修改 去掉 CCL 和 分包的 TestLine
        // DIG-5722  CChemLab并且无样式  || 分包
        Set<String> testLineIdsNotNeedAddAnalyte = testLines.stream().filter(testLine -> {
            return (CChemLab.equalsIgnoreCase(testLine.getProductLineAbbr()) && !styleTrimsTestLineIDSet.contains(testLine.getTestLineID()))
                    || (TestLineType.check(testLine.getTestLineType(), TestLineType.SubContractOrder));
        }).collect(Collectors.toList()).stream().map(TestLineConfirmMatrixInfo::getID).collect(Collectors.toSet());

        List<PPTestLineInfo> ppTestLineInfos = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(testLineIds)){
            ppTestLineInfos = ppRepository.getPpInstanceByTestLineList(Lists.newArrayList(testLineIds));
        }
        Map<String, TestLineConfirmMatrixInfo> testLineConfirmMatrixInfoMap = testLines.stream().collect(Collectors.toMap(TestLineConfirmMatrixInfo::getID, testLineConfirmMatrixInfo -> testLineConfirmMatrixInfo));
        ppTestLineInfos.forEach(ppTestLineInfo -> {
            TestLineConfirmMatrixInfo testLine = testLineConfirmMatrixInfoMap.get(ppTestLineInfo.getTestLineInstanceID());
            ppTestLineInfo.setTestLineVersionID(testLine.getTestLineVersionID());
            ppTestLineInfo.setStandardVersionID(testLine.getStandardVersionID());
        });
        //获取需要添加analte的testLine
        List<PPTestLineInfo> ppSingleTestLineInfos=ppTestLineInfos.stream().filter(ppTestLineInfo -> {
            return  !testLineIdsNotNeedAddAnalyte.contains(ppTestLineInfo.getTestLineInstanceID())&& NumberUtil.equals(NumberUtil.toLong(ppTestLineInfo.getArtifactId()), 0L);
        }).collect(Collectors.toList());
        //获取pp testLine,且需要添加analyte的testLine,根据pp分组
        Map<Integer,List<PPTestLineInfo>> ppTestLineInfosMap=ppTestLineInfos.stream().filter(ppTestLineInfo -> {
            return  ppTestLineInfo.getArtifactId()!=null&&!testLineIdsNotNeedAddAnalyte.contains(ppTestLineInfo.getTestLineInstanceID());
        }).collect(Collectors.toList()).stream().collect(Collectors.groupingBy(PPTestLineInfo::getArtifactId));
        if (MapUtils.isEmpty(ppTestLineInfosMap)){
            logger.info("[confirmMatrix]orderNo:{},获取testLine(没有需要获取Analyte的PP-tl)的analyte", orderNo);
            ppTestLineInfosMap=Maps.newHashMap();
        }
        //将单独添加的testLine放在一组
        if (CollectionUtils.isNotEmpty(ppSingleTestLineInfos)){
            ppTestLineInfosMap.put(null,ppSingleTestLineInfos);
        }
        boolean isOnlySelectionTypeEqOne = false;
        String productLineCode = ProductLineContextHolder.getProductLineCode();
        if (!StringUtils.equalsIgnoreCase(ProductLineType.SL.getProductLineAbbr(), productLineCode)) {
            isOnlySelectionTypeEqOne = true;
        }

        Map<String, List<TestAnalyteLanguage>> analyteLanguageMaps = Maps.newHashMap();
        List<AnalyteInfoPO> trimsAnalyteList = Lists.newArrayList();
        //DIG-7492
        if (Func.equalsSafe(testMatrixRule, Constants.TEST_DATA_RULE)) {
            trimsAnalyteList = this.getTrimsLocalAnalyteInfoList(ppTestLineInfosMap, generalOrderInstanceInfoPO.getID(), analyteLanguageMaps, isOnlySelectionTypeEqOne, Sets.newHashSet());
        }
        List<TestLineConfirmMatrixInfo> testLinesFinal = testLines;

        //  oldStatus,这里需要计算，只有第一次confirm matrix时，才TO DM
        int oldStatus = orderClient.getOrderStatusByOrderNo(orderNo);
        // 第一次Confirm matrix Confirm Order的时候校验当前时间不能晚于Order DueDate
        if (oldStatus == 1) {
            // 查询orderDueDate
            Date orderDueDate = orderAllDTO.getExpectedOrderDueDate();
            if (Func.isEmpty(orderDueDate)) {
                result.setSuccess(false);
                result.setMsg("Order Expect DueDate can`t be empty！");
                return result;
            }
            SimpleDateFormat fmt = new SimpleDateFormat("yyyyMMdd");
            if (Integer.valueOf(fmt.format(new Date())) > Integer.valueOf(fmt.format(orderDueDate))) {
                result.setSuccess(false);
                result.setMsg("Order expect due date cannot be earlier than Order confirm date！");
                return result;
            }
        }
        List<AnalyteInfoPO> finalTrimsAnalyteList = trimsAnalyteList;
        boolean isSuccess = transactionTemplate.execute((trans) -> {
            if (Func.equalsSafe(testMatrixRule, Constants.TEST_DATA_RULE)) {
                saveAnalyte(generalOrderInstanceInfoPO.getID(), finalTrimsAnalyteList, analyteLanguageMaps, type);
                saveConditionGroup(generalOrderInstanceInfoPO, testLinesFinal);
            }
            saveReportSeq(generalOrderInstanceInfoPO);
            UpdateConfirmMatrixDateVO updateConfirmMatrixReq = new UpdateConfirmMatrixDateVO();
            updateConfirmMatrixReq.setOrderId(generalOrderInstanceInfoPO.getID());
            updateConfirmMatrixReq.setConfirmMatrixDate(DateUtils.getNow());
            orderMapper.updateOrderConfirmMatrixDate(updateConfirmMatrixReq);

            if (oldStatus == 1) {
                SysStatusReq reqStatus = new SysStatusReq();
                reqStatus.setObjectNo(orderNo);
                reqStatus.setIgnoreOldStatus(true);
                reqStatus.setNewStatus(3);
                reqStatus.setUserName(UserHelper.getLocalUser().getRegionAccount());
                statusClient.insertStatusInfo(reqStatus);
                SysStatusReq objSysStatusReq = new SysStatusReq();
                objSysStatusReq.setProductLineCode(ProductLineContextHolder.getProductLineCode());
                objSysStatusReq.setObjectNo(orderNo);
                objSysStatusReq.setObjectType("order");
                objSysStatusReq.setSgsToken(tokenClient.getToken());
                BaseResponse objStatusResp = statusFacade.confirmOrder(objSysStatusReq);
                if (objStatusResp.getStatus() != ResponseCode.SUCCESS.getCode()) {
                    trans.setRollbackOnly();
                }
            }
            String updateMatrixNoMessage = this.generateMatrixNo(reqObject.getOrderNo());
            if (Func.isNotEmpty(updateMatrixNoMessage)) {
                trans.setRollbackOnly();
                return false;
            }
            if (Func.isNotEmpty(testLinesFinal)) {
                Set<String> testLineInstanceIdList = testLinesFinal.stream().map(TestLineConfirmMatrixInfo::getID).filter(Func::isNotEmpty).collect(Collectors.toSet());
                // 事务提交后再执行updateReportLabSection
                if (Func.isNotEmpty(testLineInstanceIdList)) {
                    TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
                        @Override
                        public void afterCommit() {
                            try {
                                ReportExtForTLUpdateReq reportExtForTLUpdateReq = new ReportExtForTLUpdateReq();
                                reportExtForTLUpdateReq.setTestLineInstanceIdList(testLineInstanceIdList);
                                reportExtForTLUpdateReq.setToken(SecurityUtil.getSgsToken());
                                reportExtForTLUpdateReq.setProductLineCode(ProductLineContextHolder.getProductLineCode());
                                reportTempFacade.updateReportExtForTL(reportExtForTLUpdateReq);
                            } catch (Exception e) {
                                logger.error("confirmMatrix updateReportLabSection error:{}", e);
                            }
                        }
                    });
                }
            }
            return true;
        });
        LogActionInfoPO record = new LogActionInfoPO();
        record.setId(UUID.randomUUID().toString());
        record.setUsername(UserHelper.getLocalUser().getRegionAccount());
        record.setAction("Confirm Matrix");
        record.setTime(DateUtils.getNow());
        record.setOrderNo(generalOrderInstanceInfoPO.getOrderNo());
        logActionInfoMapper.insert(record);
        BizLogHelper.setValue(orderNo, "");
        BizLogHelper.setLabCode(orderAllDTO.getBUCode(), orderAllDTO.getLocationCode());
        if (oldStatus == 1) {
            trackingService.sendConfirmMatrix(generalOrderInstanceInfoPO.getOrderNo(), tokenClient.getToken(), tokenClient.getUser().getRegionAccount(), null);
//            kafkaService.sendTracking(KafkaActionType.confirmMatrix, generalOrderInstanceInfoPO.getOrderNo(), tokenClient.getToken(), tokenClient.getUser().getRegionAccount(),null);
        }
        result.setSuccess(isSuccess);
        result.setMsg("");

//        kafkaService.sendTracking(KafkaActionType.labIn, orderNo, tokenClient.getToken(), tokenClient.getUser().getRegionAccount(), jobInfoPO.getJobNo());
        return result;
    }

    /**
     * 根据BU配置执行相对应的校验
     *
     * @param generalOrder
     * @param matrixType
     * @param testLines
     * @return
     */
    private CustomResult confirmMatrixCheck(String testMatrixRule, GeneralOrderInstanceInfoPO generalOrder, int matrixType, List<TestLineConfirmMatrixInfo> testLines, String reportLanguage) {
        CustomResult customResult;
        // 根据TestMatrixRule执行对应的校验
        customResult = checkSample(generalOrder.getOrderNo(), matrixType);
        if (!customResult.isSuccess()) {
            return customResult;
        }
        customResult = checkTestLine(generalOrder.getOrderNo(), matrixType, testLines);
        if (!customResult.isSuccess()) {
            return customResult;
        }
        if (Func.equalsSafe(testMatrixRule, Constants.TEST_DATA_RULE)) {
            customResult = checkConditionConfirm(testLines, generalOrder.getID());
            if (!customResult.isSuccess()) {
                return customResult;
            }
        }
        // 校验TestMatrix 是否存在客户指定的Standard且填写了CitationFullName
        customResult = checkStandConfirm(testLines, reportLanguage);
        if (!customResult.isSuccess()) {
            return customResult;
        }
        customResult.setSuccess(true);
        return customResult;
    }

    public CustomResult checkStandConfirm(List<TestLineConfirmMatrixInfo> testLines, String reportLanguage) {
        CustomResult rspResult = new CustomResult();
        rspResult.setSuccess(true);
        if(Func.isEmpty(testLines)){
            return rspResult;
        }
        Set<String> testLineNoList = new HashSet<>();
        for(TestLineConfirmMatrixInfo testLineConfirmMatrixInfo : testLines){
            boolean provideByClient = citationUtil.isProvideByClientStandard(testLineConfirmMatrixInfo.getPpBaseId(),testLineConfirmMatrixInfo.getCitationId().longValue());
            if(!provideByClient){
                continue;
            }
            if(ReportLanguage.checkLanguage(reportLanguage,ReportLanguage.EnglishAndChineseReport,ReportLanguage.MultilingualReport)){
                //获取中文的citation
                TestLineInstanceMultipleLanguageInfoExample testLineInstanceMultipleLanguageInfoExample = new TestLineInstanceMultipleLanguageInfoExample();
                testLineInstanceMultipleLanguageInfoExample.createCriteria().andTestLineInstanceIDEqualTo(testLineConfirmMatrixInfo.getID()).andLanguageIdEqualTo(LanguageType.Chinese.getLanguageId());
                List<TestLineInstanceMultipleLanguageInfoPO> testLineInstanceMultipleLanguageInfoPOList = testLineInstanceMultipleLanguageInfoMapper.selectByExample(testLineInstanceMultipleLanguageInfoExample);
                if(Func.isEmpty(testLineInstanceMultipleLanguageInfoPOList) || Func.isEmpty(testLineInstanceMultipleLanguageInfoPOList.get(0).getCitationName()) || Func.isEmpty(testLineConfirmMatrixInfo.getCitationName())){
                    testLineNoList.add(testLineConfirmMatrixInfo.getTestLineID().toString());
                }
            } else {
                if(Func.isEmpty(testLineConfirmMatrixInfo.getCitationName())){
                    testLineNoList.add(testLineConfirmMatrixInfo.getTestLineID().toString());
                }
            }
        }
        if(Func.isNotEmpty(testLineNoList)){
            rspResult.setSuccess(false);
            rspResult.setMsg("TestLineNo: "+ String.join(",", testLineNoList) + " TestStandard = ProvidedByClient,Please fill in detail TestStandard!");
            return rspResult;
        }
        return rspResult;
    }

    private CustomResult checkConditionConfirm(List<TestLineConfirmMatrixInfo> testLines, String orderID) {
        CustomResult rspResult = new CustomResult();
        if (CollectionUtils.isEmpty(testLines)) {
            rspResult.setSuccess(true);
            return rspResult;
        }
        List<Long> testLineBaseIds = testLines.stream().map(TestLineConfirmMatrixInfo::getTestLineBaseId).distinct().collect(Collectors.toList());

        if (CollectionUtils.isEmpty(testLineBaseIds)){
            rspResult.setSuccess(true);
            return rspResult;
        }

        // 1.根据testLineBaseId 获取 添加时的 conditionFlag  所对应的testLineVersionId
        List<GetTestLineBaseInfoRsp> testLineBaseInfo = testLineClient.getTestLineBaseInfo(testLineBaseIds, null, null);
        if (testLineBaseInfo.isEmpty()) {
            rspResult.setSuccess(true);
            return rspResult;
        }
        Map<Long, Integer> hasConditionFlagTLMaps = testLineBaseInfo.stream()
                .filter(tlBase -> NumberUtil.equals(tlBase.getConditionFlag(), 1)
                        && NumberUtil.toInt(tlBase.getTestLineBaseId()) > 0
                        && NumberUtil.toInt(tlBase.getTestLineVersionId()) > 0)
                .collect(Collectors.toMap(GetTestLineBaseInfoRsp::getTestLineBaseId, GetTestLineBaseInfoRsp::getTestLineVersionId, (k1, k2) -> k1));
        if (hasConditionFlagTLMaps.isEmpty()) {
            rspResult.setSuccess(true);
            return rspResult;
        }
        List<Integer> testLineVersionIds = hasConditionFlagTLMaps.values().stream().collect(Collectors.toList());

        // 2.判断testLineVersion 是否有Condition
        List<TestConditionRsp> conditionList = conditionClient.getConditionList(testLineVersionIds, null);
        if (conditionList.isEmpty()) {
            rspResult.setSuccess(true);
            return rspResult;
        }
        List<Integer> hasCondition = conditionList.stream().map(TestConditionRsp::getTestLineVersionId).collect(Collectors.toList());
        if (hasCondition.isEmpty()) {
            rspResult.setSuccess(true);
            return rspResult;
        }

        // 3.查询当前订单中的matrix对应的condition数量
        List<MatrixConditionCountDTO> matrixConditionCount = testConditionMapper.getMatrixConditionCount(orderID);
        if (matrixConditionCount.isEmpty()) {
            // testLineVersion 包含Condition  但订单中没有  需要提示
            return rspResult.fail("TestLine not confirm condition!");
        }

        Set<Integer> conditionUpdateTestLineIds = Sets.newHashSet();
        for (MatrixConditionCountDTO matrixCondition : matrixConditionCount) {
            // 仅校验指定  testLineBaseId testLine
            if (!hasConditionFlagTLMaps.containsKey(NumberUtil.toLong(matrixCondition.getTestLineBaseId()))) {
                continue;
            }
            if (hasCondition.contains(matrixCondition.getTestLineVersionID()) && NumberUtil.equals(matrixCondition.getConditionCount(), 0)) {
                logger.info("TestLine的Condition数据有更新，请先Confirm Condition：{}", matrixCondition.getTestLineId());
                conditionUpdateTestLineIds.add(matrixCondition.getTestLineId());
            }
        }
        if (CollectionUtils.isNotEmpty(conditionUpdateTestLineIds)) {
            return rspResult.fail(String.format("TestLine的Condition数据有更新，请先Confirm Condition：【%s】)", StringUtils.join(conditionUpdateTestLineIds, ",")));
        }
        rspResult.setSuccess(true);
        return rspResult;
    }

    ;

    /**
     * @param sgsToken
     * @param orderNo
     */
    private void sendConfirmToPreorder(String sgsToken, String orderNo) {
        UpdatePreOrderStatusMessage data = new UpdatePreOrderStatusMessage();
        data.setOrderNo(orderNo);
        data.setSgsToken(sgsToken);
        GeneralMessage<UpdatePreOrderStatusMessage> general = new GeneralMessage<>();
        general.setActionType("updateDate");
        general.setUserName(UserHelper.getLocalUser().getRegionAccount());
        general.setData(data);
        producer.doSend(KafkaTopicConsts.TOPIC_OTSNOTES_SYNCSTATUS, general);
    }

    public void saveReportSeq(GeneralOrderInstanceInfoPO generalOrderInstanceInfoPO) {
        int initCount = 0;
        String orderNo = generalOrderInstanceInfoPO.getOrderNo();
        List<CustomerReportRequestInfoPO> customerReportRequestInfoPOS = Lists.newArrayList();
        String customerGroupCode = generalOrderInstanceInfoPO.getCustomerGroupCode();
        if (StringUtils.isNotEmpty(customerGroupCode)) {
            CustomerReportRequestInfoExample example = new CustomerReportRequestInfoExample();
            example.createCriteria().andCustomerGroupCodeEqualTo(customerGroupCode);
            customerReportRequestInfoPOS = customerReportRequestInfoMapper.selectByExample(example);
        }

        List<TestLineConfirmMatrixInfo> testLineInstancePOs = null;

        if (CollectionUtils.isNotEmpty(customerReportRequestInfoPOS)) {
            testLineInstancePOs = testLineMapper.getTlTestLineByOrderNo(orderNo);
        } else {
            testLineInstancePOs = testLineMapper.getPPTestLineByOrderNo(orderNo);
        }
        if (CollectionUtils.isNotEmpty(testLineInstancePOs)) {
            for (TestLineInstancePO testLineInstancePO : testLineInstancePOs) {
                testLineInstancePO.setReportSeq(++initCount);
            }
        }
        testLineMapper.updateTestlineSepecialSeq(testLineInstancePOs);

    }

    /**
     * confirm matrix
     * update standard
     * save PA
     * 公用方法
     *
     * @param orderID
     * @param analytes
     * @param analyteLanguageMaps
     * @param type
     */
    public void saveAnalyte(String orderID, List<AnalyteInfoPO> analytes, Map<String, List<TestAnalyteLanguage>> analyteLanguageMaps, int type) {
        if (analytes == null) {
            analytes = Lists.newArrayList();
        }
        if (analyteLanguageMaps == null) {
            analyteLanguageMaps = Maps.newHashMap();
        }
        SaveAnalyteResp saveAnalyte = getSaveAnalyte(orderID, analytes, analyteLanguageMaps, type);
        //存储所有需要操作的analyte
        List<AnalyteInfoPO> allAnalyte = Lists.newArrayList();
        List<AnalyteInfoPO> saveAnalytes = saveAnalyte.getInsertAnalytes();

        if (CollectionUtils.isNotEmpty(saveAnalytes)) {
            allAnalyte.addAll(saveAnalytes);
            saveAnalytes.forEach(analytePO -> {
                analytePO.setCreatedBy(UserHelper.getLocalUser().getRegionAccount());
                analytePO.setCreatedDate(DateUtils.getNow());

                //DIG-4985
                if (analytePO.getTestAnalyteSeq() == null) {
                    analytePO.setTestAnalyteSeq(0);
                }
            });
            analyteMapper.batchInsert(saveAnalytes);
        }
        List<String> delAnalyteIds = Lists.newArrayList();
        List<AnalyteInfoPO> delAnalytes = saveAnalyte.getDelAnalytes();
        if (CollectionUtils.isNotEmpty(delAnalytes)) {
            allAnalyte.addAll(delAnalytes);
            Set<String> testLineIds = Sets.newHashSet();
            for (AnalyteInfoPO analyte : delAnalytes) {
                testLineIds.add(analyte.getTestLineInstanceID());
                delAnalyteIds.add(analyte.getID());
            }
//            List<String> analyteIds = testDataMapper.getTestDataByTestLineIds(testLineIds);
//            analyteIds.forEach(analyteId->{
//                if (!delAnalyteIds.contains(analyteId)){
//                    return;
//                }
//                delAnalyteIds.remove(analyteId);
//            });
        }
        if (!delAnalyteIds.isEmpty()) {
            analyteMapper.batchDelete(delAnalyteIds);
        }
        List<AnalyteInfoPO> updateAnalytes = saveAnalyte.getUpdateAnalytes();
        if (CollectionUtils.isNotEmpty(updateAnalytes)) {
            allAnalyte.addAll(updateAnalytes);
            analyteMapper.batchInsert(updateAnalytes);
        }
        //
        //这里处理orderLanguageRelationship表
        //需要删除的analyte 剔除出去，只保留保存和更新的数据
        List<AnalyteInfoPO> shouldInsertAnalyte = allAnalyte.stream().filter(an -> !delAnalyteIds.contains(an.getID())).collect(Collectors.toList());
        List<Long> analyteBaseIdList = shouldInsertAnalyte.stream().map(an -> an.getAnalyteBaseId()).distinct().collect(Collectors.toList());
        List<Long> unitBaseIdList = shouldInsertAnalyte.stream().filter(an -> an.getUnitBaseId() != null)
                .map(an -> an.getUnitBaseId()).distinct().collect(Collectors.toList());
        //查找响应的多语言数据，进行数据保存
        List<AnalyteLanguageInfoPO> trimsAnalyteLanguageList = Lists.newArrayList();
        /*if(CollectionUtils.isNotEmpty(analyteBaseIdList)){
            AnalyteLanguageInfoExample analyteLanguageInfoExample = new AnalyteLanguageInfoExample();
            analyteLanguageInfoExample.createCriteria().andLangStatusEqualTo(1).andAnalyteBaseIdIn(analyteBaseIdList);
            trimsAnalyteLanguageList = trimsAnalyteLanguageInfoMapper.selectByExample(analyteLanguageInfoExample);
        }*/

        /* if(Func.isNotEmpty(analyteLanguageMaps)){
                List<TestAnalyteLanguage> allTestAnalyteLanguageList = new ArrayList<>();
                for (Map.Entry<String, List<TestAnalyteLanguage>> stringListEntry : analyteLanguageMaps.entrySet()) {
                    List<TestAnalyteLanguage> testAnalyteLanguageList = stringListEntry.getValue();
                    if(Func.isNotEmpty(testAnalyteLanguageList)){
                        allTestAnalyteLanguageList.addAll(testAnalyteLanguageList);
                    }
                }
                trimsAnalyteLanguageList = new ArrayList<>();
                for (TestAnalyteLanguage testAnalyteLanguage : allTestAnalyteLanguageList) {
                    AnalyteLanguageInfoPO analyteLanguageInfoPO = new AnalyteLanguageInfoPO();
                    analyteLanguageInfoPO.setLangId(Func.isEmpty(testAnalyteLanguage.getLangId())?null:Long.valueOf(testAnalyteLanguage.getLangId()));
                    analyteLanguageInfoPO.setAnalyteBaseId(testAnalyteLanguage.getAnalyteBaseId());
                    analyteLanguageInfoPO.setLanguageId(testAnalyteLanguage.getLanguageId());
                    trimsAnalyteLanguageList.add(analyteLanguageInfoPO);
                }

            }else{
                AnalyteLanguageInfoExample analyteLanguageInfoExample = new AnalyteLanguageInfoExample();
                analyteLanguageInfoExample.createCriteria().andLangStatusEqualTo(1).andAnalyteBaseIdIn(analyteBaseIdList);
                trimsAnalyteLanguageList = trimsAnalyteLanguageInfoMapper.selectByExample(analyteLanguageInfoExample);
            }*/

        List<UnitLanguageInfoPO> trimsUnitLanguageList = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(unitBaseIdList)) {
            UnitLanguageInfoExample unitLanguageInfoExample = new UnitLanguageInfoExample();
            unitLanguageInfoExample.createCriteria().andLangStatusEqualTo(1).andUnitBaseIdIn(unitBaseIdList);
            trimsUnitLanguageList = trimsUnitLanguageInfoMapper.selectByExample(unitLanguageInfoExample);
        }

        List<OrderLanguageRelInfoPO> saveOrderLanguageList = this.createOrderLanguageRelationshipPO(orderID, trimsAnalyteLanguageList, trimsUnitLanguageList);
        OrderLanguageDTO delDto = new OrderLanguageDTO();
        delDto.setOrderId(orderID);
        delDto.setLangTypes(Lists.newArrayList(OrderLanguageTypeEnums.Analyte.getType(), OrderLanguageTypeEnums.AnalyteUnit.getType()));
        orderLanguageRelMapper.batchDeleteByOrderIdAndType(delDto);
        if (!saveOrderLanguageList.isEmpty()) {
            orderLanguageRelMapper.batchInsertOrUpdate(saveOrderLanguageList);
        }
    }

    private List<OrderLanguageRelInfoPO> createOrderLanguageRelationshipPO(String orderId, List<AnalyteLanguageInfoPO> trimsAnalyteLanguageList,
                                                                           List<UnitLanguageInfoPO> trimsUnitLanguageList) {
        List<OrderLanguageRelInfoPO> list = Lists.newArrayList();
        Set<Long> analyteBaseIdSet = Sets.newHashSet();
        /*for (AnalyteLanguageInfoPO po : trimsAnalyteLanguageList) {
            Long analyteBaseId = po.getAnalyteBaseId();
            if(analyteBaseIdSet.contains(analyteBaseId)){
                continue;
            }
            analyteBaseIdSet.add(analyteBaseId);
            OrderLanguageRelInfoPO relInfoPO = new OrderLanguageRelInfoPO();
            relInfoPO.setOrderId(orderId);
            relInfoPO.setObjectBaseId(po.getAnalyteBaseId());
            relInfoPO.setLangBaseId(po.getLangId());
            relInfoPO.setLangType(OrderLanguageTypeEnums.Analyte.getType());
            relInfoPO.setLanguageId(po.getLanguageId());
            relInfoPO.setLangStatus(1);
            relInfoPO.setCreatedDate(DateUtils.getNow());
            relInfoPO.setModifiedDate(DateUtils.getNow());
            list.add(relInfoPO);
        }*/
        Set<Long> unitBaseIdSet = Sets.newHashSet();
        for (UnitLanguageInfoPO po : trimsUnitLanguageList) {
            Long unitBaseId = po.getUnitBaseId();
            if (unitBaseIdSet.contains(unitBaseId)) {
                continue;
            }
            unitBaseIdSet.add(unitBaseId);
            OrderLanguageRelInfoPO relInfoPO = new OrderLanguageRelInfoPO();
            relInfoPO.setOrderId(orderId);
            relInfoPO.setObjectBaseId(po.getUnitBaseId());
            relInfoPO.setLangBaseId(po.getLangId());
            relInfoPO.setLangType(OrderLanguageTypeEnums.AnalyteUnit.getType());
            relInfoPO.setLanguageId(po.getLanguageId());
            relInfoPO.setLangStatus(1);
            relInfoPO.setCreatedDate(DateUtils.getNow());
            relInfoPO.setModifiedDate(DateUtils.getNow());
            list.add(relInfoPO);
        }
        return list;
    }

    /**
     * @param orderID
     * @param trimsaAnalyteList
     * @param analyteLanguageMaps
     * @param type
     * @return
     */
    private SaveAnalyteResp getSaveAnalyte(String orderID, List<AnalyteInfoPO> trimsaAnalyteList, Map<String, List<TestAnalyteLanguage>> analyteLanguageMaps, int type) {
        SaveAnalyteResp saveAnalyte = new SaveAnalyteResp();
        List<AnalyteInfoPO> analyteListWithOutLimit = analyteMapper.getAnalyteWithOutLimitByOrderID(orderID);
        List<AnalyteInfoPO> analyteWithLimits = analyteMapper.getAnalyteWithLimitByOrderID(orderID);
        List<AnalyteInfoPO> insertAnalytes = Lists.newArrayList();
        // 需删除的list
        List<AnalyteInfoPO> delAnalytes = Lists.newArrayList();
        // 需更新的list
        List<AnalyteInfoPO> updateAnalytes = Lists.newArrayList();

        //存儲所有的analyte,计算需要
        List<AnalyteInfoPO> allAnalytePOList = Lists.newArrayList();

        if (CollectionUtils.isEmpty(analyteWithLimits)) {
            analyteWithLimits = Lists.newArrayList();
        } else {
            allAnalytePOList.addAll(analyteWithLimits);
        }
        if (CollectionUtils.isEmpty(analyteListWithOutLimit)) {
            analyteListWithOutLimit = Lists.newArrayList();
        } else {
            allAnalytePOList.addAll(analyteListWithOutLimit);
        }

        for (AnalyteInfoPO trimsAnalyte : trimsaAnalyteList) {
            boolean flag = false;
            List<AnalyteInfoPO> analytePOWithLimitExist = analyteWithLimits.stream().filter(analytePO -> {
                return analytePO.getAnalyteID().compareTo(trimsAnalyte.getAnalyteID()) == 0 && StringUtils.equalsIgnoreCase(analytePO.getTestLineInstanceID(), trimsAnalyte.getTestLineInstanceID());
            }).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(analytePOWithLimitExist)) {
                for (AnalyteInfoPO analytePOWithLimit : analytePOWithLimitExist) {
                    if (type != 2) {
                        analytePOWithLimit.setTestAnalyteSeq(trimsAnalyte.getTestAnalyteSeq());
                        updateAnalytes.add(analytePOWithLimit);
                        if (StringUtils.equals(trimsAnalyte.getReportUnit(), analytePOWithLimit.getReportUnit())) {
                            flag = true;
                            break;
                        }
                    } else {
                        trimsAnalyte.setTestAnalyteSeq(analytePOWithLimit.getTestAnalyteSeq());
                        if (StringUtils.equals(trimsAnalyte.getReportUnit(), analytePOWithLimit.getReportUnit())) {
                            flag = true;
                            break;
                        }
                    }

                }
                if (type == 2) {
                    if (!flag) {
                        insertAnalytes.add(trimsAnalyte);
                    }
                }
                continue;
            }
            List<AnalyteInfoPO> analytePOWithoutLimitExist = analyteListWithOutLimit.stream().filter(analytePO -> {
                return analytePO.getAnalyteID().compareTo(trimsAnalyte.getAnalyteID()) == 0 && StringUtils.equalsIgnoreCase(analytePO.getTestLineInstanceID(), trimsAnalyte.getTestLineInstanceID());
            }).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(analytePOWithoutLimitExist)) {
                for (AnalyteInfoPO analytePOWithoutLimit : analytePOWithoutLimitExist) {
                    if (type != 2) {
                        analytePOWithoutLimit.setTestAnalyteSeq(trimsAnalyte.getTestAnalyteSeq());
                        updateAnalytes.add(analytePOWithoutLimit);
                        if (StringUtils.equals(trimsAnalyte.getReportUnit(), analytePOWithoutLimit.getReportUnit())) {
                            flag = true;
                            break;
                        }
                    } else {
                        trimsAnalyte.setTestAnalyteSeq(analytePOWithoutLimit.getTestAnalyteSeq());
                        if (StringUtils.equals(trimsAnalyte.getReportUnit(), analytePOWithoutLimit.getReportUnit())) {
                            flag = true;
                        } else {
                            delAnalytes.add(analytePOWithoutLimit);
                        }
                    }

                }
            }
            if (!flag) {
                insertAnalytes.add(trimsAnalyte);
            }
        }
        saveAnalyte.setInsertAnalytes(insertAnalytes);
        saveAnalyte.setUpdateAnalytes(updateAnalytes);

        saveAnalyte.setDelAnalytes(delAnalytes);
        if (MapUtils.isEmpty(analyteLanguageMaps)) {
            return saveAnalyte;
        }
        getAnalyteLanguageList(analyteLanguageMaps, analyteWithLimits, saveAnalyte, type);
        return saveAnalyte;
    }

    /**
     * @param reqAnalyte
     * @param trimsAnalyte
     * @param limitAnalytes
     * @param analyteType
     * @param type
     * @return
     */
    private boolean convertLimitAnalyteInfo(SaveAnalyteResp reqAnalyte, AnalyteInfoPO trimsAnalyte, List<LimitAnalytePO> limitAnalytes, AnalyteType analyteType, int type) {
        if (limitAnalytes == null || limitAnalytes.isEmpty()) {
            return false;
        }
        List<AnalyteInfoPO> updateAnalytes = reqAnalyte.getUpdateAnalytes();
        List<AnalyteInfoPO> delAnalytes = reqAnalyte.getDelAnalytes();
        boolean isExistAnalyte = false;
        for (LimitAnalytePO limitAnalyte : limitAnalytes) {
            limitAnalyte.setTestAnalyteSeq(trimsAnalyte.getTestAnalyteSeq());
            if (type != 2) {
                updateAnalytes.add(limitAnalyte);
            }
            // 找到相同ReportUnit的数据，则直接退出
            if (StringUtils.equals(trimsAnalyte.getReportUnit(), limitAnalyte.getReportUnit())) {
                isExistAnalyte = true;
                continue;
            }
            if (analyteType == AnalyteType.LimitAnalyte) {
                delAnalytes.add(limitAnalyte);
            }
        }
        return isExistAnalyte;
    }

    private void getAnalyteLanguageList(Map<String, List<TestAnalyteLanguage>> analyteLanguageMaps, List<AnalyteInfoPO> analyteWithLimits, SaveAnalyteResp saveAnalyte, int type) {
        List<AnalyteInfoPO> saveAnalytes = saveAnalyte.getInsertAnalytes();
        if (analyteLanguageMaps == null) {
            analyteLanguageMaps = Maps.newHashMap();
        }
        List<AnalyteMultipleLanguageInfoPO> analyteLanguages = Lists.newArrayList();
        for (AnalyteInfoPO analyte : saveAnalytes) {
            List<TestAnalyteLanguage> languages = analyteLanguageMaps.get(String.format("%s_%s_%s", analyte.getTestLineInstanceID(), analyte.getAnalyteID(), analyte.getReportUnit()));
            if (languages == null || languages.isEmpty()) {
                continue;
            }
            // 构建对象
            this.structureAnalyteMultipleLanguageInfo(analyteLanguages, analyte.getID(), languages, null, type);
        }

        List<String> delAnalyteLanguageIds = Lists.newArrayList();
        List<AnalyteInfoPO> delAnalytes = saveAnalyte.getDelAnalytes();
        if (CollectionUtils.isNotEmpty(delAnalytes)) {
            delAnalyteLanguageIds.addAll(delAnalytes.stream().map(s -> s.getID()).collect(Collectors.toList()));
        }
        List<AnalyteInfoPO> updateAnalytes = saveAnalyte.getUpdateAnalytes();
        for (AnalyteInfoPO analyteLimit : analyteWithLimits) {
            if (delAnalyteLanguageIds.contains(analyteLimit.getID()) ||
                    analyteLanguages.stream().filter(analyteLanguage -> StringUtils.endsWithIgnoreCase(analyteLanguage.getAnalyteInstanceID(), analyteLimit.getID())).count() > 0 ||
                    updateAnalytes.stream().filter(analyte -> StringUtils.equalsIgnoreCase(analyte.getID(), analyteLimit.getID())).count() > 0) {
                continue;
            }
            updateAnalytes.add(analyteLimit);
        }

        //
        List<AnalyteMultipleLanguageInfoPO> updateAnalyteLanguages = this.updateAnalyteMultipleLanguageInfo(analyteLanguageMaps, updateAnalytes, delAnalyteLanguageIds, type);
        if (!updateAnalyteLanguages.isEmpty()) {
            analyteLanguages.addAll(updateAnalyteLanguages);
        }

        // analyteWithLimits
        if (!analyteLanguages.isEmpty()) {
            analyteLanguageMapper.batchInsert(analyteLanguages);
        }

        if (!delAnalyteLanguageIds.isEmpty()) {
            analyteLanguageMapper.batchDelete(delAnalyteLanguageIds);
        }

    }

    /**
     * @param analyteLanguageMaps
     * @param updateAnalytes
     * @param delAnalyteLanguageIds
     * @return
     */
    private List<AnalyteMultipleLanguageInfoPO> updateAnalyteMultipleLanguageInfo(Map<String, List<TestAnalyteLanguage>> analyteLanguageMaps, List<AnalyteInfoPO> updateAnalytes, List<String> delAnalyteLanguageIds, int type) {
        List<AnalyteMultipleLanguageInfoPO> updateAnalyteLanguages = Lists.newArrayList();
        if (updateAnalytes.isEmpty()) {
            return updateAnalyteLanguages;
        }
        List<String> updateAnalyteIds = updateAnalytes.stream().map(s -> s.getID()).collect(Collectors.toList());
        AnalyteMultipleLanguageInfoExample example = new AnalyteMultipleLanguageInfoExample();
        example.createCriteria().andAnalyteInstanceIDIn(updateAnalyteIds);
        List<AnalyteMultipleLanguageInfoPO> oldAnalyteLanguages = analyteMultipleLanguageInfoMapper.selectByExample(example);
        if (CollectionUtils.isEmpty(oldAnalyteLanguages)) {
            oldAnalyteLanguages = Lists.newArrayList();
        }
        for (AnalyteInfoPO analyte : updateAnalytes) {
            List<AnalyteMultipleLanguageInfoPO> oldAnalyteItems = oldAnalyteLanguages.stream().filter(oldAnalyte -> StringUtils.equalsIgnoreCase(oldAnalyte.getAnalyteInstanceID(), analyte.getID())).collect(Collectors.toList());
            if (oldAnalyteItems == null) {
                oldAnalyteItems = Lists.newArrayList();
            }
            List<TestAnalyteLanguage> languages = analyteLanguageMaps.get(String.format("%s_%s_%s", analyte.getTestLineInstanceID(), analyte.getAnalyteID(), analyte.getReportUnit()));
            if (languages == null || languages.isEmpty()) {
                if (!oldAnalyteItems.isEmpty()) {
                    delAnalyteLanguageIds.addAll(oldAnalyteItems.stream().map(s -> s.getID()).collect(Collectors.toList()));
                }
                continue;
            }
            // 构建对象
            this.structureAnalyteMultipleLanguageInfo(updateAnalyteLanguages, analyte.getID(), languages, oldAnalyteItems, type);
        }
        return updateAnalyteLanguages;
    }

    /**
     * @param analyteLanguages
     * @param analyteId
     * @param languages
     * @param oldAnalyteItems
     */
    private void structureAnalyteMultipleLanguageInfo(List<AnalyteMultipleLanguageInfoPO> analyteLanguages,
                                                      String analyteId, List<TestAnalyteLanguage> languages, List<AnalyteMultipleLanguageInfoPO> oldAnalyteItems, int type) {
        if (languages == null || languages.isEmpty()) {
            return;
        }
        if (oldAnalyteItems == null) {
            oldAnalyteItems = Lists.newArrayList();
        }
        String userName = UserHelper.getLocalUser().getRegionAccount();
        for (TestAnalyteLanguage language : languages) {
            LanguageType languageType = LanguageType.findLanguageId(language.getLanguageId());
            if (languageType == null) {
                continue;
            }
            if (!oldAnalyteItems.isEmpty()) {
                oldAnalyteItems.forEach(oldAnalyteItem -> {
                    oldAnalyteItem.setLanguageId(language.getLanguageId());
                    oldAnalyteItem.setTestAnalyteName(language.getMultiTestAnalyteDesc());
                    if (type == 2) {
                        oldAnalyteItem.setReportUnit(language.getReportUnit());
                    }
                    oldAnalyteItem.setModifiedBy(userName);
                    oldAnalyteItem.setModifiedDate(DateUtils.getNow());
                });
                analyteLanguages.addAll(oldAnalyteItems);
                continue;
            }
            AnalyteMultipleLanguageInfoPO analyteLanguage = new AnalyteMultipleLanguageInfoPO();
            analyteLanguage.setID(UUID.randomUUID().toString());
            analyteLanguage.setAnalyteInstanceID(analyteId);
            analyteLanguage.setLanguageId(language.getLanguageId());
            analyteLanguage.setTestAnalyteName(language.getMultiTestAnalyteDesc());
            analyteLanguage.setReportUnit(language.getReportUnit());
            analyteLanguage.setCreatedBy(userName);
            analyteLanguage.setCreatedDate(DateUtils.getNow());
            analyteLanguage.setModifiedBy(userName);
            analyteLanguage.setModifiedDate(DateUtils.getNow());
            analyteLanguages.add(analyteLanguage);
        }
    }

    /**
     * 获取trimslocal的analyte数据
     *
     * @param ppTestLineInfosMap
     * @param orderID
     * @param analyteLanguageMaps
     * @param isOnlySelectionTypeEqOne
     * @param analyteIds               获取哪些analyte Id 不是AnalyteBaseId
     * @return
     */
    public List<AnalyteInfoPO> getTrimsLocalAnalyteInfoList(Map<Integer, List<PPTestLineInfo>> ppTestLineInfosMap, String orderID,
                                                            Map<String, List<TestAnalyteLanguage>> analyteLanguageMaps,
                                                            boolean isOnlySelectionTypeEqOne, Set<Integer> analyteIds) {
        List<AnalyteInfoPO> analytes = Lists.newArrayList();
        if(ppTestLineInfosMap == null || ppTestLineInfosMap.isEmpty()){
            return analytes;
        }
        QueryTestLineAnalyteReq queryTestLineAnalyteReq = new QueryTestLineAnalyteReq();
        queryTestLineAnalyteReq.setAnalytes(Lists.newArrayList());
        queryTestLineAnalyteReq.setTestAnalyteIds(analyteIds);
        //组装查询对象
        for (Integer artifactId : ppTestLineInfosMap.keySet()) {
            List<PPTestLineInfo> ppTestLines = ppTestLineInfosMap.get(artifactId);
            if (ppTestLines == null || ppTestLines.isEmpty()) {
                continue;
            }
            for (PPTestLineInfo ppTestLine: ppTestLines) {
                TestLineAnalyteReq testLineAnalyteReq = new TestLineAnalyteReq();
                testLineAnalyteReq.setStandardVersionIds(Sets.newHashSet());

                testLineAnalyteReq.setArtifactId(Func.isEmpty(artifactId) ? null : Long.valueOf(artifactId));
                testLineAnalyteReq.setTestLineVersionId(ppTestLine.getTestLineVersionID());
                testLineAnalyteReq.getStandardVersionIds().add(ppTestLine.getStandardVersionID());
                queryTestLineAnalyteReq.getAnalytes().add(testLineAnalyteReq);
            }
        }
        com.sgs.trimslocal.facade.model.common.BaseResponse<List<TestLineAnalyteRsp>> testLineAnalyteRes = analyteFacade.getTestLineAnalyteList(queryTestLineAnalyteReq);
        if (testLineAnalyteRes.getStatus() != 200) {
            return analytes;
        }
        List<TestLineAnalyteRsp> data = testLineAnalyteRes.getData();

        Map<Optional<Long>, List<TestLineAnalyteRsp>> artifactIdAnalyteListMap = data.stream()
                .collect(Collectors.groupingBy(x -> Optional.ofNullable(x.getArtifactId())));
        Set<String> analyteSets = Sets.newHashSet();
        //需要转成po，返回出去
        artifactIdAnalyteListMap.forEach((aid, analyteList) -> {
            Long artifactId = aid.orElse(null);
            List<PPTestLineInfo> ppTestLineInfos = ppTestLineInfosMap.get(Func.isEmpty(artifactId) ? null : artifactId.intValue());

            for (TestLineAnalyteRsp analyte : analyteList) {
                Integer selectionType = analyte.getSelectionType();
                if (isOnlySelectionTypeEqOne && !AnalyteSectionTypeEnums.check(selectionType, AnalyteSectionTypeEnums.Mandatory)) {
                    continue;
                }
                List<TestLineAnalyteUnitRsp> units = analyte.getUnits();
                if (CollectionUtils.isEmpty(units)) {
                    continue;
                }
                List<String> testLineInstanceIds = ppTestLineInfos.stream()
                        .filter(ppTestLineInfo -> NumberUtil.equals(ppTestLineInfo.getTestLineVersionID(), analyte.getTestLineVersionId()))
                        .collect(Collectors.toList())
                        .stream().map(PPTestLineInfo::getTestLineInstanceID)
                        .collect(Collectors.toList());

                List<TestAnalyteLangReq> languages = analyte.getLanguages();

                for (TestLineAnalyteUnitRsp unit : units) {
                    for (String testLineInstanceId : testLineInstanceIds) {
                        String unitShortDepiction = unit.getUnitShortDepiction();
                        String reportUnit = LOStringUtil.delHTMLTag(unitShortDepiction);
                        String analyteKey = String.format("%s_%s_%s", testLineInstanceId, analyte.getTestAnalyteId(), reportUnit);
                        if (analyteSets.contains(analyteKey)) {
                            continue;
                        }
                        analyteSets.add(analyteKey);

                        AnalyteInfoPO newAnalyte = new AnalyteInfoPO();
                        newAnalyte.setID(UUID.randomUUID().toString());
                        newAnalyte.setAnalyteBaseId(analyte.getAnalyteBaseId());
                        newAnalyte.setAnalyteID(analyte.getTestAnalyteId());
                        newAnalyte.setGeneralOrderInstanceID(orderID);
                        newAnalyte.setReportUnit(reportUnit);
                        newAnalyte.setUnitBaseId(unit.getUnitBaseId());
                        newAnalyte.setTestAnalyteName(LOStringUtil.delHTMLTag(analyte.getTestAnalyteDesc()));
                        newAnalyte.setTestLineInstanceID(testLineInstanceId);
                        newAnalyte.setTestAnalyteSeq(analyte.getTestAnalyteSeq() != null ? analyte.getTestAnalyteSeq() : 0);
                        newAnalyte.setCasNo(analyte.getTestAnalyteCasNumber());
                        newAnalyte.setActiveIndicator(true);
                        analytes.add(newAnalyte);

                        if (CollectionUtils.isEmpty(languages)) {
                            continue;
                        }
                        //处理analyte 的多语言数据
                        List<TestAnalyteLanguage> lanList = Lists.newArrayList();
                        AnalyteUnitLangRsp unitLanguage = unit.getLanguage();
                        //这个字段不能给空字符串，因为jasper模板里面不处理空字符串，如果没有数据，一定要null
                        String reportUnitLan = unitLanguage == null ? null : StringUtils.defaultIfBlank(unitLanguage.getUnitShortDepiction(), null);
                        for (TestAnalyteLangReq language : languages) {
                            TestAnalyteLanguage testAnalyteLanguage = new TestAnalyteLanguage();
                            testAnalyteLanguage.setLanguageId(language.getLanguageId());
                            testAnalyteLanguage.setMultiTestAnalyteDesc(language.getTestAnalyteDesc());
                            testAnalyteLanguage.setReportUnit(reportUnitLan);
                            lanList.add(testAnalyteLanguage);
                        }
                        analyteLanguageMaps.put(analyteKey, lanList);
                    }
                }
            }
        });
        return analytes;
    }

    /**
     * @param analyteLanguageMaps
     * @param analyteKey
     * @param otherLanguageItems
     * @param unitForReportLanguages
     */
    public void setAnalyteReportUnitLanguage(Map<String, List<TestAnalyteLanguage>> analyteLanguageMaps, String analyteKey, List<TestAnalyteLanguage> otherLanguageItems,
                                             List<UnitForReportingLanguage> unitForReportLanguages) {
        //这里修改了引用地址
//        if (MapUtils.isEmpty(analyteLanguageMaps)){
//            analyteLanguageMaps=Maps.newHashMap();
//        }
        if (unitForReportLanguages == null || otherLanguageItems == null) {
            if (otherLanguageItems == null || otherLanguageItems.isEmpty()) {
                return;
            }
            analyteLanguageMaps.put(analyteKey, otherLanguageItems);
            return;
        }
        unitForReportLanguages.forEach(unitForReportLanguage -> {
            Integer languageId = unitForReportLanguage.getLanguageId();
            if (languageId == null || languageId.intValue() <= 0) {
                return;
            }
            for (TestAnalyteLanguage otherLanguageItem : otherLanguageItems) {
                if (otherLanguageItem.getLanguageId() != languageId) {
                    continue;
                }
                otherLanguageItem.setReportUnit(LOStringUtil.delHTMLTag(unitForReportLanguage.getMultiUnitShortDepiction()));
            }
        });
        analyteLanguageMaps.put(analyteKey, otherLanguageItems);
    }

    private CustomResult checkTestLine(String orderNo, int type, List<TestLineConfirmMatrixInfo> testLines) {
        CustomResult customResult = new CustomResult();
        ConfirmMatrixTypeEnum matrixType = ConfirmMatrixTypeEnum.getCode(type);
        List<TestLineConfirmMatrixInfo> usedTestLineList = testLineMapper.getUsedTestLineConfirmMatrixInfoByOrderNo(orderNo);
        usedTestLineList = usedTestLineList.stream().filter(testline -> {
                    boolean isChem = StringUtils.equalsIgnoreCase(testline.getProductLineAbbr(), CChemLab) || StringUtils.containsIgnoreCase(testline.getLabSectionName(), CChemLab);
                    return (matrixType == ConfirmMatrixTypeEnum.Chem && isChem) || (matrixType == ConfirmMatrixTypeEnum.Phy && !isChem) || ConfirmMatrixTypeEnum.All == matrixType;
                }).filter(testLine -> !testLine.getPendingFlag())
                .collect(Collectors.toList());

        if (testLines.size() != usedTestLineList.size()) {
            customResult.setSuccess(false);
            customResult.setMsg("TestLine is not be used");
            return customResult;
        }
        customResult.setSuccess(true);
        return customResult;
    }

    private CustomResult checkSample(String orderNo, int type) {
        CustomResult customResult = new CustomResult();
        //查出所有的样品
        List<AssignSampleInfo> allSample = testSampleExtMapper.queryAllSample(orderNo);
        //处理Ax的关系 查询DFF信息
        OrderIdReq orderIdReq = new OrderIdReq();
        orderIdReq.setOrderNo(orderNo);
        orderIdReq.setProductLineCode(ProductLineContextHolder.getProductLineCode());
        BaseResponse<List<ProductInstanceDTO>> dffSamplesResponse = orderFacade.queryProductInstaceForPe(orderIdReq);
        if(dffSamplesResponse.isSuccess() && Func.isNotEmpty(dffSamplesResponse.getData())){
            List<ProductInstanceDTO> dffSamples = dffSamplesResponse.getData();
            Map<String,ProductInstanceDTO> productSampleMap = new HashMap<>();
            for(ProductInstanceDTO productInstanceDTO : dffSamples){
                productSampleMap.put(productInstanceDTO.getId(),productInstanceDTO);
            }
            for(AssignSampleInfo sampleInfo : allSample){
                if(productSampleMap.containsKey(sampleInfo.getID())){
                    ProductInstanceDTO product = productSampleMap.get(sampleInfo.getID());
                    sampleInfo.setAxSampleFlag(product.getAxSampleFlag());
                    sampleInfo.setAxparentSampleId(product.getParentSampleID());
                }
            }
        }
        List<String> axSampleId = allSample.stream().map(e -> e.getAxparentSampleId()).filter(Func::isNotEmpty).collect(Collectors.toList());
        if(Func.isNotEmpty(axSampleId)){
            allSample = allSample.stream().filter(e -> !axSampleId.contains(e.getID())).collect(Collectors.toList());
        }
        //找出所有被使用的样品
        List<AssignSampleInfo> assignSample = testSampleExtMapper.queryAssignSampleByOrderNo(orderNo);
        /**
         * ？判断是Chem或者是Phy是不是去掉了
         */
        if (type == ConfirmMatrixTypeEnum.Chem.getCode()) {
            allSample = allSample.stream().filter(po -> SampleType.Sample.getCategoryChem().equalsIgnoreCase(po.getCategory()))
                    .collect(Collectors.toList());
        } else if (type == ConfirmMatrixTypeEnum.Phy.getCode()) {
            //1.获取category为P的数据
            //2.当两个原样做 mix样的时候，如果还没有submit sample， category这个时候为空。这种情况需要额外判断。   （最佳优化：应该是在做mix样的时候，将category设置成P。 待丁君楠回来后在处理。。。）
            //submit后category会变成P
            allSample = allSample.stream().filter(po -> SampleType.Sample.getCategoryPhy().equalsIgnoreCase(po.getCategory())
                            || (po.getSampleType().intValue() == SampleType.MixSample.getSampleType() && StringUtils.isEmpty(po.getCategory())))
                    .collect(Collectors.toList());

        }

        //通过规则计算后的，所以assign的sampleID 集合
        Set<String> allCalculateAssignSampleIDList = Sets.newHashSet();
        if (CollectionUtils.isNotEmpty(assignSample)) {
            List<AssignSampleInfo> allGroupSample = testSampleExtMapper.queryGroupSamplesByOrderNo(orderNo);
            for (AssignSampleInfo samplePO : assignSample) {
                getCalculateAssignSampleIDList(allSample, allCalculateAssignSampleIDList, samplePO, allGroupSample);
            }
        }

//		比对找出没有被assign的样品
        List<String> noAssignSampleList = Lists.newArrayList();
        for (AssignSampleInfo sample : allSample) {
            if (!allCalculateAssignSampleIDList.contains(sample.getID()) && sample.getActiveIndicator()) {
                noAssignSampleList.add(sample.getSampleNo());
            }
        }
        logger.info("=======noAssignSampleList: {}=========", JSONObject.toJSONString(noAssignSampleList));

        if (CollectionUtils.isNotEmpty(noAssignSampleList)) {
            String returnMsg = "sample " + StringUtils.join(noAssignSampleList, ",") + " not be assign";
            customResult.setSuccess(false);
            customResult.setMsg(returnMsg);
            return customResult;
        }
        customResult.setSuccess(true);
        return customResult;
    }

    /**
     * 通过计算/逻辑算法，得到所以 assign 过的sampleId
     *
     * @param allSample
     * @param allAssignSampleIDList
     * @param samplePO
     * @param allGroupSample
     * <AUTHOR>
     */
    private void getCalculateAssignSampleIDList(List<AssignSampleInfo> allSample, Set<String> allAssignSampleIDList,
                                                AssignSampleInfo samplePO, List<AssignSampleInfo> allGroupSample) {
        int sampleType = samplePO.getSampleType().intValue();
        allAssignSampleIDList.add(samplePO.getID());
        if (sampleType == SampleType.Sample.getSampleType()) {
            //1.将对应的101的父节点添加
            AssignSampleInfo samplePOTemp = allSample.stream().filter(sample -> {
                return StringUtils.equalsIgnoreCase(sample.getID(), samplePO.getSampleParentID());
            }).findFirst().orElse(null);
            if (!ObjectUtils.isEmpty(samplePOTemp)) {
                allAssignSampleIDList.add(samplePOTemp.getID());
            }
        } else if (sampleType == SampleType.SubSample.getSampleType()) {
            //1.将对应的102的父节点添加
            AssignSampleInfo samplePOTemp102 = allSample.stream().filter(sample -> {
                return StringUtils.equalsIgnoreCase(sample.getID(), samplePO.getSampleParentID());
            }).findFirst().orElse(null);
            if (!ObjectUtils.isEmpty(samplePOTemp102)) {
                allAssignSampleIDList.add(samplePOTemp102.getID());
                //1.将对应的101的父节点添加
                AssignSampleInfo samplePOTemp101 = allSample.stream().filter(sample -> {
                    return StringUtils.equalsIgnoreCase(sample.getID(), samplePOTemp102.getSampleParentID());
                }).findFirst().orElse(null);
                if (!ObjectUtils.isEmpty(samplePOTemp101)) {
                    allAssignSampleIDList.add(samplePOTemp101.getID());
                }

            }
        } else if (sampleType == SampleType.MixSample.getSampleType()) {
            //104 的组合样，有可能是103，102，101 都有可能的样品
            List<AssignSampleInfo> samplePOListTemp = allGroupSample.stream().filter(sample -> {
                return StringUtils.equalsIgnoreCase(samplePO.getID(), sample.getSampleID());
            }).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(samplePOListTemp)) {
                for (AssignSampleInfo sam : samplePOListTemp) {
                    getCalculateAssignSampleIDList(allSample, allAssignSampleIDList, sam, allGroupSample);
                }
            }

        } else if (sampleType == SampleType.ShareSample.getSampleType()) {
            //1:共样对应的原样， 共样 只能在 102上，所以查出处理
            List<AssignSampleInfo> samplePOListTemp = allGroupSample.stream().filter(sample -> {
                return StringUtils.equalsIgnoreCase(samplePO.getID(), sample.getSampleID());
            }).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(samplePOListTemp)) {
                for (AssignSampleInfo sam : samplePOListTemp) {
                    allAssignSampleIDList.add(sam.getID());
                }
            }
        }
    }

    public void saveConditionGroup(GeneralOrderInstanceInfoPO generalOrderInstanceInfoPO, List<TestLineConfirmMatrixInfo> testLines) {
        ReportLangReq reqReport = new ReportLangReq();
        reqReport.setOrderNo(generalOrderInstanceInfoPO.getOrderNo());

        //TODO
//        List<LanguageType> languageTypes = orderReportClient.getReportLanguageByOrderNo(reqReport);
        List<LanguageType> languageTypes = orderReportClient.getReportLanguageByOrderNo(null);
        if (!languageTypes.contains(LanguageType.Chinese)) {
            languageTypes.add(LanguageType.Chinese);
        }

        SaveConditionGroup saveConditionGroup = new SaveConditionGroup();
        List<TestConditionInfoPO> oldConditions = testConditionMapper.getTestConditionListByOrderId(generalOrderInstanceInfoPO.getID());
        saveConditionGroup.setTestConditionInfoPOS(oldConditions);

        List<TestConditionGroupInfoPO> oldTestConditionGroupInfoPOs = testConditionGroupMapper.getTestConditionGroupListByOrderId(generalOrderInstanceInfoPO.getID());
        saveConditionGroup.setTestConditionGroupInfoPOS(oldTestConditionGroupInfoPOs);

        List<TestConditionInstanceMultipleLanguagePO> oldTestConditionLanguages = testConditionLanguageMapper.getTestConditionLangList(generalOrderInstanceInfoPO.getID());
        saveConditionGroup.setTestConditionInstanceMultipleLanguagePOS(oldTestConditionLanguages);

        List<TestConditionGroupMultipleLanguagePO> oldTestConditionGroupMultipleLanguagePOs = testConditionGroupLanguageMapper.getTestConditionGroupLanguageListByOrderId(generalOrderInstanceInfoPO.getID());
        saveConditionGroup.setTestConditionGroupMultipleLanguagePOS(oldTestConditionGroupMultipleLanguagePOs);

        saveConditionGroup.setLanguageTypes(languageTypes);

        List<TestLineConfirmMatrixInfo> testLineConfirmMatrixInfos = testLines.stream().filter(testLine -> {
            return TestLineStatus.check(testLine.getTestLineStatus(), TestLineStatus.Typing, TestLineStatus.Entered, TestLineStatus.SubContracted);
        }).collect(Collectors.toList());
        List<Integer> testLineVersionIds = testLineConfirmMatrixInfos.stream().map(TestLineConfirmMatrixInfo::getTestLineVersionID).collect(Collectors.toList());
        List<TestLineCustomerAppRelInfoWithBLOBs> testLineCustomerAppRelInfoPOs = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(testLineVersionIds)) {
            TestLineCustomerAppRelInfoExample testLineCustomerAppRelInfoExample = new TestLineCustomerAppRelInfoExample();
            testLineCustomerAppRelInfoExample
                    .createCriteria()
                    .andRelStatusEqualTo(1)
                    .andCustomerAccountIdEqualTo(0)
                    .andTestLineVersionIdIn(testLineVersionIds);
            testLineCustomerAppRelInfoPOs = testLineCustomerAppRelInfoMapper.selectByExampleWithBLOBs(testLineCustomerAppRelInfoExample);
        }
        List<TestLineCustomerAppLangInfoWithBLOBs> testLineCustomerAppLangInfoPOs = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(testLineCustomerAppRelInfoPOs)) {
            TestLineCustomerAppLangInfoExample testLineCustomerAppLangInfoExample = new TestLineCustomerAppLangInfoExample();
            testLineCustomerAppLangInfoExample
                    .createCriteria()
                    .andLangStatusEqualTo(1)
                    .andApplicabilityRelIdIn(testLineCustomerAppRelInfoPOs.stream().map(TestLineCustomerAppRelInfoPO::getId).collect(Collectors.toList()));
            testLineCustomerAppLangInfoPOs = testLineCustomerAppLangInfoMapper.selectByExampleWithBLOBs(testLineCustomerAppLangInfoExample);
            if (CollectionUtils.isEmpty(testLineCustomerAppLangInfoPOs)) {
                testLineCustomerAppLangInfoPOs = Lists.newArrayList();
            }
        }
        for (TestLineConfirmMatrixInfo testLine : testLineConfirmMatrixInfos) {
            TestLineCustomerAppRelInfoWithBLOBs trimsTLCustomerAppRelInfoPO = testLineCustomerAppRelInfoPOs.stream().filter(t1 -> t1.getTestLineVersionId().compareTo(testLine.getTestLineVersionID()) == 0).findFirst().orElse(null);
            TestLineConditionInfo testLineCondition = new TestLineConditionInfo();
            saveConditionGroup.setTestLine(testLine);
            if (trimsTLCustomerAppRelInfoPO != null) {
                testLineCondition.setConditionInstruction(trimsTLCustomerAppRelInfoPO.getConditionInstructions());
                List<InstructionLanguageInfo> otherLanguageItems = Lists.newArrayList();
                List<TestLineCustomerAppLangInfoWithBLOBs> trimsTLCustomerAppRelLanguageInfoPOSByTestLine = testLineCustomerAppLangInfoPOs.stream().filter(con -> con.getApplicabilityRelId().compareTo(trimsTLCustomerAppRelInfoPO.getId()) == 0).collect(Collectors.toList());
                trimsTLCustomerAppRelLanguageInfoPOSByTestLine.forEach(trimsTLCustomerAppRelLanguageInfoPO -> {
                    InstructionLanguageInfo instructionLanguageInfo = new InstructionLanguageInfo();
                    instructionLanguageInfo.setLanguageId(trimsTLCustomerAppRelLanguageInfoPO.getLanguageId());
                    instructionLanguageInfo.setMultiConditionInstruction(trimsTLCustomerAppRelLanguageInfoPO.getConditionInstructions());
                    otherLanguageItems.add(instructionLanguageInfo);
                });
                testLineCondition.setOtherLanguageItems(otherLanguageItems);
            }
            saveConditionGroup.setTestLineCondition(testLineCondition);
            saveTestLineConditionInstruction(saveConditionGroup);
        }
    }

    private void constructTestConditionGroup(String instructionTemp, TestLineInstancePO testLine, TestConditionGroupInfoPO orderconditiongroup) {
        String uuid = UUID.randomUUID().toString();
        orderconditiongroup.setID(uuid);
        orderconditiongroup.setGeneralOrderInstanceID(testLine.getGeneralOrderInstanceID());
        orderconditiongroup.setCombinedConditionDescription(instructionTemp);
        orderconditiongroup.setTestLineInstanceID(testLine.getID());
        orderconditiongroup.setActiveIndicator(true);
        orderconditiongroup.setCreatedBy(UserHelper.getLocalUser().getRegionAccount());
        orderconditiongroup.setCreatedDate(DateUtils.getNow());
    }

    private void saveTestLineConditionInstruction(SaveConditionGroup saveConditionGroup) {
        TestLineInstancePO testLine = saveConditionGroup.getTestLine();
        TestLineConditionInfo testLineCondition = saveConditionGroup.getTestLineCondition();
        String conditionInstruction = testLineCondition.getConditionInstruction();

        if (StringUtils.isBlank(conditionInstruction)) {
            conditionInstruction = "";
        }
        String userName = UserHelper.getLocalUser().getRegionAccount();

        TestMatrixPO testMatrixPO = new TestMatrixPO();
        testMatrixPO.setTestLineInstanceID(testLine.getID());
        testMatrixPO.setTestConditionGroupID(null);
        testMatrixPO.setModifiedBy(userName);
        testMatrixPO.setModifiedDate(DateUtils.getNow());
        testMatrixMapper.updateTestConditionGroupIdByTestLineId(testMatrixPO);

        List<TestConditionGroupInfoPO> oldTestConditionGroups = saveConditionGroup.getTestConditionGroupInfoPOS().stream().filter(conditionGroupInfoPO -> conditionGroupInfoPO.getTestLineInstanceID().equalsIgnoreCase(testLine.getID())).collect(Collectors.toList());

        List<TestConditionInfoPO> oldTestConditions = saveConditionGroup.getTestConditionInfoPOS().stream().filter(conditionInfoPO -> conditionInfoPO.getTestLineInstanceID().equalsIgnoreCase(testLine.getID())).collect(Collectors.toList());

        List<String> testConditionIds = oldTestConditions.stream().map(testCondition -> testCondition.getID()).collect(Collectors.toList());
        List<TestConditionInstanceMultipleLanguagePO> oldTestConditionLanguages = saveConditionGroup.getTestConditionInstanceMultipleLanguagePOS().stream().filter(conditionGroupInfoPO -> testConditionIds.contains(conditionGroupInfoPO.getTestConditionInstanceID())).collect(Collectors.toList());
        Map<String, TestConditionInstanceMultipleLanguagePO> conditionLanguageMaps = Maps.newHashMap();
        oldTestConditionLanguages.forEach(language -> {
            conditionLanguageMaps.put(String.format("%s_%s", language.getTestConditionInstanceID(), language.getLanguageId()), language);
        });
        List<TestConditionGroupInfoPO> testConditionGroupInfoPOsList = Lists.newArrayList();
        List<String> testConditionGroupIDNotDelete = Lists.newArrayList();

        // 查询TestLine维护的英文的condition
        List<TestLineConditionRelDTO> testLineConditionRelDTOS = testLineConditionRelExtMapper.getDefaultTestLineConditionRels(testLine.getTestLineVersionID());
        if (CollectionUtils.isNotEmpty(oldTestConditions)) {
            Map<String, List<TestConditionInfoPO>> matrixMap = oldTestConditions.stream().collect(Collectors.groupingBy(TestConditionInfoPO::getTestMatrixID));
            Map<String, Map<String, String>> map = Maps.newHashMap();
            for (String matrixID : matrixMap.keySet()) {
                List<TestConditionInfoPO> matrixConditionList = matrixMap.get(matrixID);
                map.put(matrixID, matrixConditionList.stream().collect(Collectors.toMap(TestConditionInfoPO::getTestConditionTypeName, TestConditionInfoPO::getTestConditionDesc, (v1, v2) -> v1)));
            }
            Map<String, String> matrixIds = Maps.newHashMap();
            for (String matrixId : map.keySet()) {
                String sampleIns = conditionInstruction;
                for (Map.Entry<String, String> instruction : map.get(matrixId).entrySet()) {
                    sampleIns = Utils.replaceAll(sampleIns, "\\{" + instruction.getKey() + "\\}", instruction.getValue()).toString();
                }
                // 补充处理英文的Condition
                if(Func.isNotEmpty(testLineConditionRelDTOS)){
                    for(TestLineConditionRelDTO testLineConditionRelDTO : testLineConditionRelDTOS){
                        if(Func.isEmpty(sampleIns) || Func.isEmpty(testLineConditionRelDTO.getTestConditionTypeName())){
                            continue;
                        }
                        if(sampleIns.contains("{" + testLineConditionRelDTO.getTestConditionTypeName() + "}")){
                            sampleIns = Utils.replaceAll(sampleIns, "\\{" + testLineConditionRelDTO.getTestConditionTypeName() + "\\}", testLineConditionRelDTO.getTestConditionDesc()).toString();
                        }
                    }
                }
                matrixIds.put(matrixId, sampleIns);
            }
            // 拿到conditionInstruction合集
            Set<String> conditionInstructionSet = Sets.newHashSet();
            for (String value : matrixIds.values()) {
                conditionInstructionSet.add(value);
            }
            for (String instruction : conditionInstructionSet) {
                String instructionTemp = instruction;
                if (instruction.contains("$$$")) {//AZO
                    instructionTemp = instruction.split("\\$\\$\\$")[0].replace("{TestStandard1}", testLine.getStandardName());
                }
                String finnalInstruction = instructionTemp;
                TestConditionGroupInfoPO orderconditiongroup = oldTestConditionGroups.stream().filter(conditionGroup -> conditionGroup.getCombinedConditionDescription().equals(finnalInstruction)).findFirst().orElse(null);

                if (orderconditiongroup == null) {
                    orderconditiongroup = new TestConditionGroupInfoPO();
                    constructTestConditionGroup(instructionTemp, testLine, orderconditiongroup);
                    testConditionGroupInfoPOsList.add(orderconditiongroup);
                } else {
                    testConditionGroupIDNotDelete.add(orderconditiongroup.getID());
                }
                List<UpdateConditionGroupInfo> updateConditionGroupInfos = Lists.newArrayList();
                for (Map.Entry<String, String> entry : matrixIds.entrySet()) {
                    if (instruction.equals(entry.getValue())) {
                        UpdateConditionGroupInfo newTestConditionGroup = new UpdateConditionGroupInfo();
                        newTestConditionGroup.setMatrixId(entry.getKey());
                        newTestConditionGroup.setTestConditionGroupId(orderconditiongroup.getID());
                        newTestConditionGroup.setModifiedBy(userName);
                        newTestConditionGroup.setModifiedDate(DateUtils.getNow());
                        updateConditionGroupInfos.add(newTestConditionGroup);
                    }
                }
                if (CollectionUtils.isNotEmpty(testConditionGroupInfoPOsList)) {
                    testConditionGroupMapper.batchInsert(testConditionGroupInfoPOsList);
                }
                if (CollectionUtils.isNotEmpty(updateConditionGroupInfos)) {
                    testMatrixMapper.batchUpdateTestConditionGroupId(updateConditionGroupInfos);
                }
                this.saveConditionInstructionLanguageInfo(orderconditiongroup.getID(), conditionLanguageMaps, saveConditionGroup);
            }

        } else {
            String instructionTemp = conditionInstruction;
            if (conditionInstruction.contains("$$$")) {//AZO
                instructionTemp = conditionInstruction.split("\\$\\$\\$")[0].replace("{TestStandard1}", testLine.getStandardName());
            }
            final String instructionTempFinal = instructionTemp;
            TestConditionGroupInfoPO orderconditiongroup = oldTestConditionGroups.stream().filter(conditionGroup -> StringUtils.equalsIgnoreCase(conditionGroup.getCombinedConditionDescription(), instructionTempFinal)).findFirst().orElse(null);
            if (orderconditiongroup == null) {
                orderconditiongroup = new TestConditionGroupInfoPO();
                constructTestConditionGroup(instructionTemp, testLine, orderconditiongroup);
                testConditionGroupInfoPOsList.add(orderconditiongroup);
            } else {
                testConditionGroupIDNotDelete.add(orderconditiongroup.getID());
            }
            if (CollectionUtils.isNotEmpty(testConditionGroupInfoPOsList)) {
                testConditionGroupMapper.batchInsert(testConditionGroupInfoPOsList);
            }
            testMatrixPO = new TestMatrixPO();
            testMatrixPO.setTestLineInstanceID(testLine.getID());
            testMatrixPO.setTestConditionGroupID(orderconditiongroup.getID());
            testMatrixPO.setModifiedBy(userName);
            testMatrixPO.setModifiedDate(new Date());
            testMatrixMapper.updateTestConditionGroupIdByTestLineId(testMatrixPO);

            this.saveConditionInstructionLanguageInfo(orderconditiongroup.getID(), conditionLanguageMaps, saveConditionGroup);
        }
        List<String> groupIds = Lists.newArrayList();
        // 删除没有使用的 conditionGroup
        for (TestConditionGroupInfoPO group : oldTestConditionGroups) {
            if (testConditionGroupIDNotDelete.contains(group.getID())) {
                continue;
            }
            groupIds.add(group.getID());
        }
        if (!groupIds.isEmpty()) {
            testConditionGroupLanguageMapper.deleteByConditionGroupIds(groupIds);
            testConditionGroupMapper.deleteByIds(groupIds);
        }

    }


    private void saveConditionInstructionLanguageInfo(String testConditionGroupId, Map<String, TestConditionInstanceMultipleLanguagePO> conditionLanguageMaps, SaveConditionGroup saveConditionGroup) {
        String standardName = saveConditionGroup.getTestLine().getStandardName();
        String testLineID = saveConditionGroup.getTestLine().getID();
        List<LanguageType> languageTypes = saveConditionGroup.getLanguageTypes();
        TestLineConditionInfo testLineCondition = saveConditionGroup.getTestLineCondition();
        List<TestConditionInfoPO> oldTestConditions = saveConditionGroup.getTestConditionInfoPOS();
		/*List<TestConditionInstancePO> testConditions = oldTestConditions.stream().filter(oldTestCondition -> StringUtils.equalsIgnoreCase(oldTestCondition.getId(), testConditionGroupId)).collect(Collectors.toList());
		if (testConditions == null){
			testConditions = Lists.newArrayList();
		}*/
        List<InstructionLanguageInfo> languages = testLineCondition.getOtherLanguageItems();
        if (languages == null) {
            languages = Lists.newArrayList();
        }
        Set<String> conditionInstructions = Sets.newHashSet();

        Map<LanguageType, TestConditionGroupMultipleLanguagePO> languagesMaps = Maps.newHashMap();
        List<TestConditionGroupMultipleLanguagePO> oldConditionGroupLanguages = saveConditionGroup.getTestConditionGroupMultipleLanguagePOS().stream().filter(lan -> testConditionGroupId.equalsIgnoreCase(lan.getTestConditionGroupID())).collect(Collectors.toList());
        DeleteTestReq params = new DeleteTestReq();
        params.setTestLineInstanceId(testLineID);
        List<TestMatrixPO> testMatrixVOs = testMatrixMapper.getMatrixByTestLineInstanceId(params);
        List<String> matrixIDlListByGroup = Lists.newArrayList();
        List<String> matrixIDNotDelete = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(testMatrixVOs)) {
            testMatrixVOs = testMatrixVOs.stream().filter(m -> {
                return StringUtils.equalsIgnoreCase(m.getTestConditionGroupID(), testConditionGroupId);
            }).collect(Collectors.toList());
            matrixIDlListByGroup = testMatrixVOs.stream().map(TestMatrixPO::getID).collect(Collectors.toList());
        }
        for (InstructionLanguageInfo language : languages) {
            Integer languageId = language.getLanguageId();
            LanguageType languageType = LanguageType.findLanguageId(languageId);
            if (languageType == null) {
                continue;
            }
            String conditionInstruction = language.getMultiConditionInstruction();
            if (StringUtils.isBlank(conditionInstruction)) {
                conditionInstruction = "";
            }
            if (CollectionUtils.isNotEmpty(oldTestConditions)) {
                Map<String, Map<String, String>> map = Maps.newHashMap();
                for (TestConditionInfoPO condition : oldTestConditions) {
                    String matrixId = condition.getTestMatrixID();
                    if (!matrixIDlListByGroup.contains(matrixId)) {
                        continue;
                    }
                    TestConditionInstanceMultipleLanguagePO testConditionLanguage = conditionLanguageMaps.get(String.format("%s_%s", condition.getID(), languageId));
                    if (testConditionLanguage == null || StringUtils.isBlank(testConditionLanguage.getTestConditionDesc()) || testConditionLanguage.getLanguageId() != languageType.getLanguageId()) {
                        continue;
                    }
                    String testConditionDesc = testConditionLanguage.getTestConditionDesc();
                    if (StringUtils.isBlank(testConditionDesc)) {
                        testConditionDesc = "";
                    }
                    if (!map.containsKey(matrixId)) {
                        map.put(matrixId, Maps.newHashMap());
                    }
                    map.get(matrixId).put(testConditionLanguage.getTestConditionTypeName(), testConditionDesc);
                }

                Map<String, String> matrixIds = Maps.newHashMap();
                if (MapUtils.isNotEmpty(map)) {
                    for (String matrixId : map.keySet()) {
                        Map<String, String> conditionTypeMap = map.get(matrixId);
                        String sampleIns = conditionInstruction;
                        for (Map.Entry<String, String> instruction : conditionTypeMap.entrySet()) {
                            sampleIns = Utils.replaceAll(sampleIns, "\\{" + instruction.getKey() + "\\}", instruction.getValue()).toString();
                        }
                        matrixIds.put(matrixId, sampleIns);
                    }
                }
                // 拿到conditionInstruction合集
                for (String value : matrixIds.values()) {
                    conditionInstructions.add(value);
                }
                for (String instructionTemp : conditionInstructions) {
                    if (instructionTemp.contains("$$$")) {//AZO
                        instructionTemp = instructionTemp.split("\\$\\$\\$")[0].replace("{TestStandard1}", standardName);
                    }
                    TestConditionGroupMultipleLanguagePO conditionGroup = oldConditionGroupLanguages.stream().filter(group -> LanguageType.check(group.getLanguageId(), languageType)).findFirst().orElse(null);
                    if (conditionGroup == null) {
                        conditionGroup = new TestConditionGroupMultipleLanguagePO();
                        conditionGroup.setID(UUID.randomUUID().toString());
                    }
                    conditionGroup.setTestConditionGroupID(testConditionGroupId);
                    conditionGroup.setLanguageId(languageType.getLanguageId());
                    conditionGroup.setCombinedConditionDescription(instructionTemp);
                    matrixIDNotDelete.add(conditionGroup.getID());
                    languagesMaps.put(languageType, conditionGroup);
                }
            } else {
                String instructionTemp = conditionInstruction.contains("$$$") ? conditionInstruction.split("\\$\\$\\$")[0].replace("{TestStandard1}", standardName) : conditionInstruction;

                TestConditionGroupMultipleLanguagePO conditionGroup = oldConditionGroupLanguages.stream().filter(group -> LanguageType.check(group.getLanguageId(), languageType)).findFirst().orElse(null);
                if (conditionGroup == null) {
                    conditionGroup = new TestConditionGroupMultipleLanguagePO();
                    conditionGroup.setID(UUID.randomUUID().toString());
                }
                conditionGroup.setTestConditionGroupID(testConditionGroupId);
                conditionGroup.setLanguageId(languageType.getLanguageId());
                conditionGroup.setCombinedConditionDescription(instructionTemp);
                matrixIDNotDelete.add(conditionGroup.getID());
                languagesMaps.put(languageType, conditionGroup);
            }
        }

        List<String> delLanguageIds = Lists.newArrayList();
        for (TestConditionGroupMultipleLanguagePO conditionGroup : oldConditionGroupLanguages) {
            LanguageType languageType = LanguageType.findLanguageId(conditionGroup.getLanguageId());
            if (languageType == null || matrixIDNotDelete.contains(conditionGroup.getTestConditionGroupID())) {
                continue;
            }
            if (languageTypes.contains(languageType) && !languagesMaps.containsKey(languageType)) {
                conditionGroup.setCombinedConditionDescription(null);
                languagesMaps.put(languageType, conditionGroup);
                continue;
            }
            delLanguageIds.add(conditionGroup.getID());
        }
        TestConditionGroupMultipleLanguagePO conditionGroup;
        for (LanguageType languageType : languageTypes) {
            if (languageType == LanguageType.English || languagesMaps.containsKey(languageType)) {
                continue;
            }
            conditionGroup = new TestConditionGroupMultipleLanguagePO();
            conditionGroup.setID(UUID.randomUUID().toString());
            conditionGroup.setTestConditionGroupID(testConditionGroupId);
            conditionGroup.setLanguageId(languageType.getLanguageId());
            languagesMaps.put(languageType, conditionGroup);
        }
        if (!languagesMaps.isEmpty()) {
            testConditionGroupLanguageMapper.batchInsert(Lists.newArrayList(languagesMaps.values()));
        }
        if (delLanguageIds.isEmpty()) {
            return;
        }
        testConditionGroupLanguageMapper.deleteByConditionGroupIds(delLanguageIds);
    }

    @AccessPolicyRule(subContractType = SubContractOperationTypeEnums.CancelAssignSample)
    public CustomResult<Boolean> cancelAssignSample(AssignSampleCancelReq req) {
        String sampleId = req.getTestSampleId();
        String tlId = req.getTestLineInstanceId();
        CustomResult<Boolean> result = new CustomResult<>();
        if (StringUtils.isBlank(sampleId) || StringUtils.isBlank(tlId)) {
            result.setMsg("Params Error");
            result.setSuccess(false);
            return result;
        }
        TestMatrixInfoExample example = new TestMatrixInfoExample();
        example.createCriteria().andTestLineInstanceIDEqualTo(tlId).andTestSampleIDEqualTo(sampleId).andMatrixGroupIdEqualTo(req.getMatrixGroupId());

        List<TestMatrixInfoPO> testMatrixInfoPOS = testMatrixInfoMapper.selectByExample(example);
        if (CollectionUtils.isEmpty(testMatrixInfoPOS)) {
            result.setMsg("Get Data Error");
            result.setSuccess(false);
            return result;
        }
        TestMatrixInfoPO po = testMatrixInfoPOS.get(0);
        po.setModifiedBy(UserHelper.getLocalUser().getRegionAccount());
        Boolean activeIndicator = !po.getActiveIndicator();
        po.setActiveIndicator(activeIndicator);
        po.setModifiedDate(DateUtils.getNow());
        List<LogInfoWithBLOBs> logList = Lists.newArrayList();

        //同时将testData里面的数据 设置成NoNeedTest状态  DIG-2206
        String noNeeTest = null;
        if (!activeIndicator) {
            noNeeTest = "NoNeedTest";
        }
        final String nt = noNeeTest;
        TestDataInfoExample testDataInfoExample = new TestDataInfoExample();
        testDataInfoExample.createCriteria().andTestLineInstanceIDEqualTo(tlId).andTestSampleIDEqualTo(sampleId);

        List<TestDataInfoPO> testdataList = this.testDataInfoMapper.selectByExample(testDataInfoExample);

        //将conclusion cancel

        ConclusionInfoReq conclusion = new ConclusionInfoReq();
        conclusion.setTestLineInstanceID(tlId);
        conclusion.setTestSampleID(sampleId);
        conclusion.setModifiedBy(UserHelper.getLocalUser().getRegionAccount());
        conclusion.setModifiedDate(DateUtils.getNow());
        conclusion.setActiveIndicator(activeIndicator);

        TestLineInstancePO testLineInstance = testLineInstanceMapper.selectByPrimaryKey(tlId);
        String orderID = testLineInstance.getGeneralOrderInstanceID();
        GeneralOrderInstanceInfoPO generalOrderInstanceInfoPO = orderMapper.getOrderInfoByOrderId(orderID);
        String orderNo = generalOrderInstanceInfoPO.getOrderNo();

        ReportInfoExample reportInfoExample = new ReportInfoExample();
        reportInfoExample.createCriteria().andOrderNoEqualTo(orderNo);
        List<ReportInfoPO> reportInfoPOS = reportInfoMapper.selectByExample(reportInfoExample);
        ReportInfoPO reportInfoPO = reportInfoPOS.get(0);

        boolean isSuccess = transactionTemplate.execute((trans) -> {
            this.testMatrixMapper.updateActiveIndicatorById(po);
            if (CollectionUtils.isNotEmpty(testdataList)) {
                testdataList.forEach(td -> {
                    td.setTestValueRemark(nt);
                });
//                this.testDataMapper.batchUpdateTestValueRemark(testdataList);
            }
            int count = conclusionMapper.updateConclusionBySampleIdAndTestLineID(conclusion);

            if (count > 0) {
                if (reportInfoPO.getRecalculationFlag() == 1) {
                    reportInfoPO.setRecalculationFlag(2);
                    reportInfoPO.setModifiedBy(UserHelper.getLocalUser().getRegionAccount());
                    reportInfoPO.setModifiedDate(DateUtils.getNow());
                    reportMapper.updateRecalculationFlagByReportId(reportInfoPO);
                }
            }
            return true;
        });
        result.setSuccess(isSuccess);
        result.setData(true);
        return result;
    }

    /**
     * @param deleteTestReq
     * @return
     */
    @AccessPolicyRule(reportStatus = {ReportStatus.Approved, ReportStatus.Cancelled})
    public CustomResult deleteTest(DeleteTestReq deleteTestReq) {
        CustomResult rspResult = new CustomResult();
        // 在 testlineInstance 表中，判断 status 的状态是否为 701 Typing
        TestLineInstancePO testLineInstanceById = testLineMapper.getTestLineInstanceById(deleteTestReq.getTestLineInstanceId());
        if (!TestLineType.check(testLineInstanceById.getTestLineType(), TestLineType.SubContractOrder) && testLineInstanceById.getTestLineStatus() != TestLineStatus.Typing.getStatus()) {
            rspResult.setMsg("Only testline in Typing state can delete test");
            rspResult.setSuccess(false);
            return rspResult;
        }

        //当前TL被分包
        if (TestLineType.check(testLineInstanceById.getTestLineType(), TestLineType.SubContractOrder)) {
            //必须是typing 或者enter才能执行这个操作
            if (!TestLineStatus.check(testLineInstanceById.getTestLineStatus(), TestLineStatus.Entered, TestLineStatus.Typing)) {
                rspResult.setMsg("only testline in Typing/Entered state can delete test");
                rspResult.setSuccess(false);
                return rspResult;
            }
            //需要校验分包过去的子单当前TL的状态，如果不是typing的话，同样是不允许删除，需要子单先执行retest操作，变回typing状态
            CheckSubTLDTO checkSubTLDTO = testLineMapper.querySubTLStatusByCurrentTLId(deleteTestReq.getTestLineInstanceId());
            if (!TestLineStatus.check(checkSubTLDTO.getTestLineStatus(), TestLineStatus.Typing)) {
                rspResult.setMsg("subcontratced testline in Typing state can delete test");
                rspResult.setSuccess(false);
                return rspResult;
            }
            if (SubContractDataLockEnums.check(checkSubTLDTO.getDataLock(), SubContractDataLockEnums.lock)) {
                rspResult.setMsg(checkSubTLDTO.getOrderNo() + " data locked,Please click unlock button first");
                rspResult.setSuccess(false);
                return rspResult;
            }
        }


        //查询testLine所有的matrix
        List<TestMatrixPO> matrixList = testMatrixMapper.getMatrixByTestLineInstanceId(deleteTestReq);

        //过滤出所有要删除的matrix
        List<TestMatrixPO> deleteMatrixList = matrixList.stream()
                .filter(matrix -> matrix.getMatrixGroupId().toString().equals(deleteTestReq.getMatrixGroupId() + "")).collect(Collectors.toList());

        //删除reportMatrixRelationShip
        List<String> matrixIdList = deleteMatrixList.stream().map(m -> m.getID()).collect(Collectors.toList());

        List<String> delTestMatrixIdList = Lists.newArrayList();
        //删除TestMatrix，准备数据
        for (TestMatrixPO testMatrixPO : deleteMatrixList) {
            delTestMatrixIdList.add(testMatrixPO.getID());
            //测试无误后删除
            /*testMatrixInfoMapper.deleteByPrimaryKey(testMatrixPO.getID());
            conclusionMapper.deleteConclusionByMatrixID(testMatrixPO.getID());
            //删除所有matrix相关的condition
            testConditionMapper.deleteByMatrixId(testMatrixPO.getID());*/
        }

        List<TestMatrixPO> otherMatrixList = matrixList.stream()
                .filter(matrix -> matrix.getMatrixGroupId() != deleteTestReq.getMatrixGroupId()).collect(Collectors.toList());

        //根据matrixGroupId分组
        Map<Integer, List<TestMatrixPO>> groupMap = otherMatrixList.stream().
                collect(Collectors.groupingBy(ma -> ma.getMatrixGroupId()));

        List<TestMatrixPO> testMatrixPOList = new ArrayList<>();

        for (Integer key : groupMap.keySet()) {
            if (key > deleteTestReq.getMatrixGroupId()) {
                for (TestMatrixPO thisMatrix : groupMap.get(key)) {
                    thisMatrix.setMatrixGroupId(thisMatrix.getMatrixGroupId() - 1);
                    TestMatrixInfoPO testMatrixInfoPO = new TestMatrixInfoPO();
                    BeanUtils.copyProperties(thisMatrix, testMatrixInfoPO);
                    testMatrixPOList.add(thisMatrix);
                }
            }
        }

        rspResult.setSuccess(transactionTemplate.execute((tranStatus) -> {
            boolean reportDelIsSuccess = reportMatrixRelMapper.deleteByMatrixIDList(matrixIdList) > 0;
            testPositionMapper.deleteBytestMatrixIDs(matrixIdList);
            boolean testMatrixIsSuccess = testMatrixMapper.batchDeleteMatrix(delTestMatrixIdList) > 0;

            conclusionMapper.batchDelete(delTestMatrixIdList);
            testConditionMapper.batchDeleteCondition(delTestMatrixIdList);

            if (CollectionUtils.isNotEmpty(testMatrixPOList)) {
                testMatrixMapper.batchUpdateTestMatrix(testMatrixPOList);
            }
            if (!(reportDelIsSuccess && testMatrixIsSuccess)) {
                tranStatus.setRollbackOnly(); // 回滚事务
                return false;
            }
            return reportDelIsSuccess && testMatrixIsSuccess;
        }));
        return rspResult;
    }

    /**
     * @param saveCopyTestReq
     * @return
     */
    @AccessPolicyRule(reportStatus = {ReportStatus.Approved, ReportStatus.Cancelled},
            subContractType = SubContractOperationTypeEnums.CopyTestLine,
            testLinePendingType = TestLinePendingTypeEnums.TestLineInstanceId)
//    @TestLinePending(filedName = "testLineInstanceId",type=TestLinePendingTypeEnums.TL_ID)
    public CustomResult saveCopyTest(SaveCopyTestReq saveCopyTestReq) {
        CustomResult rspResult = new CustomResult();
        UserInfo localUser = UserHelper.getLocalUser();
        // 在 testlineInstance 表中，判断 status 的状态是否为 701 Typing
        TestLineInstancePO testLineInstanceById = testLineMapper.getTestLineInstanceById(saveCopyTestReq.getTestLineInstanceId());
        //modified by vincent 2021年1月20日 DIG-5105
        if (!TestLineStatus.check(testLineInstanceById.getTestLineStatus(), TestLineStatus.Typing, TestLineStatus.Entered)) {
            rspResult.setMsg("Only testline in Typing/Entered status can copy test");
            return rspResult;
        }
        CustomResult result = this.checkTLSubDataLock(saveCopyTestReq.getTestLineInstanceId());
        if (!result.isSuccess()) {
            return result;
        }
        // 查 report
        ReportInfoPO report = reportMapper.getReportByOrderNo(saveCopyTestReq.getOrderNo());

        // 查 matrix
        List<TestMatrixPO> matrixList = testMatrixMapper.getMatrixByTestLineIdAndSampleIds(saveCopyTestReq);
        List<TestMatrixPO> saveMatrixList = new ArrayList<>();
        List<ReportMatrixRelationShipInfoPO> saveReportMatrixRelList = new ArrayList<>();
        List<ReportInfoPO> reportListByOrderNo = reportMapper.getReportListByOrderNo(saveCopyTestReq.getOrderNo());
        //过滤出reportFlag为1 Self Generate的report
        List<ReportInfoPO> groupReportList = reportListByOrderNo.stream().filter(s -> s.getReportFlag() == null || s.getReportFlag() == ReportFlagEnums.REPORT.getCode()).collect(Collectors.toList());
        for (String sampleId : saveCopyTestReq.getSampleIds()) {
            List<TestMatrixPO> testMatrixs = matrixList.stream().filter(matrix -> StringUtils.equalsIgnoreCase(matrix.getTestSampleID(), sampleId)).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(testMatrixs)) {
                rspResult.setMsg("No testline");
                return rspResult;
            }
            TestMatrixPO newMatrix = new TestMatrixPO();
            newMatrix.setID(UUID.randomUUID().toString());
            newMatrix.setGeneralOrderInstanceID(testMatrixs.get(0).getGeneralOrderInstanceID());
            newMatrix.setActiveIndicator(true);
            newMatrix.setTestLineInstanceID(saveCopyTestReq.getTestLineInstanceId());
            newMatrix.setTestSampleID(sampleId);
            newMatrix.setMatrixGroupId(testMatrixs.size());

            newMatrix.setCreatedDate(DateUtils.getNow());
            newMatrix.setCreatedBy(localUser.getRegionAccount());
            newMatrix.setModifiedDate(DateUtils.getNow());
            newMatrix.setModifiedBy(localUser.getRegionAccount());
            newMatrix.setMatrixStatus(MatrixStatus.Typing.getStatus());
            saveMatrixList.add(newMatrix);

            ReportMatrixRelationShipInfoPO reportMatrixRelationshipPO = new ReportMatrixRelationShipInfoPO();
            reportMatrixRelationshipPO.setID(UUID.randomUUID().toString());
            ReportInfoExample example = new ReportInfoExample();
            example.createCriteria().andOrderNoEqualTo(saveCopyTestReq.getOrderNo());
            List<ReportInfoPO> reportList = reportInfoMapper.selectByExample(example);
            ReportInfoPO firstReport = null;
            if (Func.isNotEmpty(reportList)) {

                firstReport = reportList.stream().sorted(Comparator.comparing(ReportInfoPO::getReportNo)).findFirst().orElse(null);
            }
            if (Func.isEmpty(groupReportList) || groupReportList.size() == 1) {
                if (Func.isEmpty(groupReportList)) {
                    if (Func.isEmpty(firstReport)) {
                        rspResult.setMsg("not found 01 report");
                        rspResult.setSuccess(false);
                        return rspResult;
                    } else {
                        reportMatrixRelationshipPO.setReportID(firstReport.getID());
                    }
                } else {
                    reportMatrixRelationshipPO.setReportID(groupReportList.get(0).getID());
                }
                //reportMatrixRelationshipPO.setActiveIndicator(1);
                reportMatrixRelationshipPO.setTestMatrixID(newMatrix.getID());
                reportMatrixRelationshipPO.setModifiedBy(localUser.getRegionAccount());
                reportMatrixRelationshipPO.setModifiedDate(DateUtils.getNow());
                reportMatrixRelationshipPO.setCreatedBy(localUser.getRegionAccount());
                reportMatrixRelationshipPO.setCreatedDate(DateUtils.getNow());
                saveReportMatrixRelList.add(reportMatrixRelationshipPO);
            }
        }

        // 判断是否正常添加，否则回滚
        rspResult.setSuccess(transactionTemplate.execute((tranStatus) -> {
            boolean materixIsSuccess = true;
            if (Func.isNotEmpty(saveMatrixList)) {
                materixIsSuccess = saveMatrixList.size() == testMatrixMapper.batchInsert(saveMatrixList);
            }
            boolean reportIsSuccess = true;
            if (Func.isNotEmpty(saveReportMatrixRelList)) {
                reportIsSuccess = saveReportMatrixRelList.size() == reportMatrixRelMapper.batchInsert(saveReportMatrixRelList);
            }
            if (!(materixIsSuccess && reportIsSuccess)) {
                // 回滚事务
                tranStatus.setRollbackOnly();
                return false;
            }
            return true;
        }));

        return rspResult;
    }

    public CustomResult getMatrixListByReportNos(QueryMatrixReq queryMatrixReq) {
        CustomResult<List<QueryMatrixRsp>> customResult = new CustomResult<>();
        if (CollectionUtils.isEmpty(queryMatrixReq.getReportNos())) {
            customResult.setSuccess(false);
            return customResult;
        }
        List<PPMatrixInfo> ppMatrixVOList = testMatrixMapper.getPpMatrixListByReportNos(queryMatrixReq.getReportNos());
        //Qutotion 页面testLineName别名修改
        if (CollectionUtils.isNotEmpty(ppMatrixVOList)) {
            String orderNo = ppMatrixVOList.get(0).getOrderNo();
            testLineLocalService.build(orderNo, ppMatrixVOList);
            ppMatrixVOList.forEach(pmv -> {
                pmv.setTestLineAlias(pmv.getEvaluationAlias());
                pmv.setTestLineEvaluation(pmv.getEvaluationAlias());
            });
        }

        // key：TestLineInstanceID value:TestLineOut
        Map<String, QueryMatrixTestLineRsp> testLineList = new HashMap<>();
        // key：ppVersionID value:PPOut
        Map<String, QueryMatrixPpRsp> ppList = new HashMap<>();
        for (PPMatrixInfo ppMatrixVO : ppMatrixVOList) {
            // 判断是否是TestLine
            if (ppMatrixVO.getPpVersionID() == null) {
                // TestLine
                String key = ppMatrixVO.getTlId();
                if (testLineList.containsKey(key)) {
                    // 更新Qty
                    QueryMatrixTestLineRsp tlOut = testLineList.get(key);
                    tlOut.setQty(tlOut.getQty() + 1);
                } else {
                    // 添加TestLine
                    QueryMatrixTestLineRsp tlOut = this.queryMatrix2TL(ppMatrixVO);
                    testLineList.put(key, tlOut);
                }
            } else {
                // PP
                String key = ppMatrixVO.getPpVersionID() + "_" + ppMatrixVO.getRootPPVersionID();
                if (ppList.containsKey(key)) {
                    // 更新PPOut
                    QueryMatrixPpRsp ppOut = ppList.get(key);
                    this.queryMatrix2PPUpdate(ppMatrixVO, ppOut);
                } else {
                    // 添加PPOut
                    QueryMatrixPpRsp ppOut = this.queryMatrix2PPAdd(ppMatrixVO);
                    ppList.put(key, ppOut);
                }
            }
        }
        // 组装返回数据
        customResult.setData(Lists.<QueryMatrixRsp>newArrayList());
        if (CollectionUtils.isNotEmpty(testLineList.values())) {
//			vo.getData().addAll(testLineList.values());
            customResult.getData().addAll(testLineList.values());
        }
        if (CollectionUtils.isNotEmpty(ppList.values())) {
//			vo.getData().addAll(ppList.values());
            customResult.getData().addAll(ppList.values());
        }

        customResult.setSuccess(true);
        return customResult;
    }

    /**
     * 组装TestLine 数据
     *
     * @param ppMatrixVO
     * @return
     */
    private QueryMatrixTestLineRsp queryMatrix2TL(PPMatrixInfo ppMatrixVO) {
        QueryMatrixTestLineRsp tlOut = new QueryMatrixTestLineRsp();
        tlOut.setQty(1);
        tlOut.setReportSeq(ppMatrixVO.getReportSeq());
        tlOut.setLabSectionSeq(ppMatrixVO.getLabSectionSeq());
        tlOut.setReportNo(ppMatrixVO.getReportNo());
        tlOut.setStandardName(ppMatrixVO.getStandardName());
        tlOut.setTestLineEvaluation(ppMatrixVO.getTestLineEvaluation());
        tlOut.setTestLineID(ppMatrixVO.getTestLineID());
        tlOut.setTestLineVersionID(ppMatrixVO.getTestLineVersionID());
        tlOut.setTestLineAlias(ppMatrixVO.getTestLineAlias());
        tlOut.setEvaluationAlias(ppMatrixVO.getEvaluationAlias());
        return tlOut;
    }

    /**
     * 组装PP数据 更新
     *
     * @param ppMatrixVO
     * @param ppOut
     * @return
     */
    private QueryMatrixPpRsp queryMatrix2PPUpdate(PPMatrixInfo ppMatrixVO, QueryMatrixPpRsp ppOut) {
        /*维护PPTestLineOut*/
        PpTestLineOutRsp existOut = null;
        for (PpTestLineOutRsp tlOut : ppOut.getTestLineList()) {
            //合并
            if (StringUtils.equals(ppMatrixVO.getTlId(), tlOut.getTestLineInstanceID())) {
                existOut = tlOut;
            }
        }
        if (existOut == null) {
            //添加PPTestLineOut
            existOut = new PpTestLineOutRsp();
            existOut.setQty(1);
            existOut.setStandardName(ppMatrixVO.getStandardName());
            existOut.setTestLineEvaluation(ppMatrixVO.getTestLineEvaluation());
            existOut.setTestLineID(ppMatrixVO.getTestLineID());
            existOut.setTestLineInstanceID(ppMatrixVO.getTlId());
            existOut.setReportSeq(ppMatrixVO.getReportSeq());
            existOut.setLabSectionSeq(ppMatrixVO.getLabSectionSeq());
            existOut.setTestLineVersionID(ppMatrixVO.getTestLineVersionID());
            existOut.setTestLineAlias(ppMatrixVO.getTestLineAlias());
            existOut.setEvaluationAlias(ppMatrixVO.getEvaluationAlias());
            ppOut.getTestLineList().add(existOut);
        } else {
            //更新Qty
            existOut.setQty(existOut.getQty() + 1);
        }
        return ppOut;
    }

    /**
     * 组装PP数据 添加
     *
     * @param ppMatrixVO
     * @return
     */
    private QueryMatrixPpRsp queryMatrix2PPAdd(PPMatrixInfo ppMatrixVO) {
        /*创建PPOut*/
        QueryMatrixPpRsp ppOut = new QueryMatrixPpRsp();
        ppOut.setPpClientRefNo(ppMatrixVO.getPpClientRefNo());
        ppOut.setPpVersionID(ppMatrixVO.getPpVersionID());
        ppOut.setRootPPVersionID(ppMatrixVO.getRootPPVersionID());
        ppOut.setReportNo(ppMatrixVO.getReportNo());
        List<PpTestLineOutRsp> list = new ArrayList<>();
        ppOut.setTestLineList(list);
        //添加PPTestLineOut
        PpTestLineOutRsp ppTestLineOut = new PpTestLineOutRsp();
        ppTestLineOut.setQty(1);
        ppTestLineOut.setStandardName(ppMatrixVO.getStandardName());
        ppTestLineOut.setTestLineEvaluation(ppMatrixVO.getTestLineEvaluation());
        ppTestLineOut.setTestLineID(ppMatrixVO.getTestLineID());
        ppTestLineOut.setTestLineInstanceID(ppMatrixVO.getTlId());
        ppTestLineOut.setTestLineVersionID(ppMatrixVO.getTestLineVersionID());
        ppTestLineOut.setTestLineAlias(ppMatrixVO.getTestLineAlias());
        ppTestLineOut.setReportSeq(ppMatrixVO.getReportSeq());
        ppTestLineOut.setLabSectionSeq(ppMatrixVO.getLabSectionSeq());
        ppTestLineOut.setEvaluationAlias(ppMatrixVO.getEvaluationAlias());
        ppOut.getTestLineList().add(ppTestLineOut);
        return ppOut;
    }

    /**
     * 校验当前TL是否做了内部分包 并且校验是否处于lock状态
     * 适用后端操作TL相关时的校验
     *
     * @param testLineInstanceId
     * @return
     */
    public CustomResult checkTLSubDataLock(String testLineInstanceId) {
        CustomResult<Object> rspResult = new CustomResult<>();
        //需要校验分包过去的子单当前TL的状态，如果不是typing的话，同样是不允许删除，需要子单先执行retest操作，变回typing状态
        CheckSubTLDTO checkSubTLDTO = testLineMapper.querySubTLStatusByCurrentTLId(testLineInstanceId);
        if (checkSubTLDTO == null || !TestLineType.check(checkSubTLDTO.getTestLineType(), TestLineType.SubContractOrder)) {
            rspResult.setSuccess(true);
            return rspResult;
        }
        if (SubContractDataLockEnums.check(checkSubTLDTO.getDataLock(), SubContractDataLockEnums.lock)) {
            rspResult.setMsg(checkSubTLDTO.getOrderNo() + " data locked,Please click unlock button first");
            rspResult.setSuccess(false);
            return rspResult;
        }
        rspResult.setSuccess(true);
        return rspResult;
    }

    /**
     * 生成matrixNo
     *
     * @param orderNo
     * @return
     */
    public String generateMatrixNo(String orderNo) {
        //查询order下Matrix
        List<TestMatrixInfoPO> testMatrixInfoPOS = testMatrixMapper.getMatrixByOrderNoForGenerateNo(orderNo);
        //判断查询是否为空
        if (Func.isNotEmpty(testMatrixInfoPOS)) {
            //查询订单下buId，locationId
            //获取Order信息
            OrderIdReq orderIdReq = new OrderIdReq();
            orderIdReq.setOrderNo(orderNo);
            orderIdReq.setProductLineCode(ProductLineContextHolder.getProductLineCode());
            BaseResponse<OrderAllDTO> orderForPe = orderFacade.getOrderForPe(orderIdReq);
            if (Func.isEmpty(orderForPe) || Func.isEmpty(orderForPe.getData())) {
                return "查询订单信息失败";
            }
            OrderAllDTO orderAllDTO = orderForPe.getData();
            Integer locationId = orderAllDTO.getLocationID();
            Integer buId = orderAllDTO.getBUID();
            Integer quantity = testMatrixInfoPOS.size();
            String externalOrderNo = orderClient.getExternalOrderNo(orderNo);
            if (Func.isEmpty(externalOrderNo)) {
                externalOrderNo = orderNo;
            }
            List<String> matrixNos = null;
            try {
                matrixNos = codeUtil.getMatrixNoBatch(externalOrderNo, locationId, buId, quantity);
            } catch (Exception e) {
                return "FrameWork生成matrixNos失败:" + e.getMessage();
            }
            if (Func.isEmpty(matrixNos) || !Func.equals(quantity, matrixNos.size())) {
                return "FrameWork生成matrixNos失败";
            }
            int index = 0;
            //遍历生成编码
            for (TestMatrixInfoPO testMatrixInfoPO : testMatrixInfoPOS) {

//                String matrixNo = codeUtil.getMatrixNo(externalOrderNo,locationId,buId);
                String matrixNo = matrixNos.get(index++);
                testMatrixInfoPO.setMatrixConfirmDate(DateUtils.getNow());
                testMatrixInfoPO.setMatrixNo(matrixNo);
                testMatrixInfoPO.setModifiedDate(new Date());
            }
            //生成完成，更新数据
            boolean temp = transactionTemplate.execute((transactionStatus) -> {
                for (TestMatrixInfoPO testMatrixInfoPO : testMatrixInfoPOS) {
                    int count = testMatrixInfoMapper.updateByPrimaryKeySelective(testMatrixInfoPO);
                    if (count == 0) {
                        transactionStatus.setRollbackOnly();
                        return false;
                    }
                }
                return true;
            });
            if (!temp) {
                return "matrixNo更新失败";
            }

        }
        return null;
    }

    /**
     * 根据matrixNo查询Matrix信息
     *
     * @param queryMatrixInfoReq
     * @return
     */
    public BaseResponse<QueryMatrixInfoRsp> getMatrixInfoByMatrixNo(QueryMatrixInfoReq queryMatrixInfoReq) {
        BaseResponse baseResponse = new BaseResponse();
        baseResponse.setStatus(com.sgs.framework.core.base.ResponseCode.SUCCESS.getCode());
        //参数校验
        if (Func.isEmpty(queryMatrixInfoReq) || Func.isEmpty(queryMatrixInfoReq.getMatrixNo())) {
            baseResponse.setStatus(com.sgs.framework.core.base.ResponseCode.FAIL.getCode());
            baseResponse.setMessage("查询matrix信息参数校验失败");
            return baseResponse;
        }
        //查询matrix信息
        TestMatrixInfoExample testMatrixInfoExample = new TestMatrixInfoExample();
        testMatrixInfoExample.createCriteria().andMatrixNoEqualTo(queryMatrixInfoReq.getMatrixNo());
        List<TestMatrixInfoPO> testMatrixInfoPOS = testMatrixInfoMapper.selectByExample(testMatrixInfoExample);
        if (Func.isEmpty(testMatrixInfoPOS)) {
            baseResponse.setStatus(com.sgs.framework.core.base.ResponseCode.FAIL.getCode());
            baseResponse.setMessage(messageUtil.get("photo.tool.matrix.exist.not"));
            return baseResponse;
        }
        TestMatrixInfoPO testMatrixInfoPO = testMatrixInfoPOS.get(0);
        if ((Func.isNotEmpty(testMatrixInfoPO.getMatrixStatus()) && MatrixStatus.check(testMatrixInfoPO.getMatrixStatus(), MatrixStatus.Cancelled)) || (Func.isNotEmpty(testMatrixInfoPO.getActiveIndicator()) && !testMatrixInfoPO.getActiveIndicator())) {
            baseResponse.setStatus(com.sgs.framework.core.base.ResponseCode.FAIL.getCode());
            baseResponse.setMessage(messageUtil.get("photo.tool.matrix.cancel"));
            return baseResponse;
        }
        //查询Matrix 相关的TL的状态
        String testLineInstanceID = testMatrixInfoPO.getTestLineInstanceID();
        if (Func.isNotEmpty(testLineInstanceID)) {
            TestLineInstancePO testLineInstancePO = testLineInstanceMapper.selectByPrimaryKey(testLineInstanceID);
            if (Func.isNotEmpty(testLineInstancePO)) {
                if (TestLineStatus.check(testLineInstancePO.getTestLineStatus(), TestLineStatus.Cancelled)) {
                    baseResponse.setStatus(com.sgs.framework.core.base.ResponseCode.FAIL.getCode());
                    baseResponse.setMessage(messageUtil.get("photo.tool.matrix.testline.cancel"));
                    return baseResponse;
                }
            }
        }
        //查询sample信息
        TestSampleInfoPO testSampleInfoPO = testSampleInfoMapper.selectByPrimaryKey(testMatrixInfoPO.getTestSampleID());
        if (Func.isEmpty(testSampleInfoPO)) {
            baseResponse.setStatus(com.sgs.framework.core.base.ResponseCode.FAIL.getCode());
            baseResponse.setMessage(messageUtil.get("photo.tool.sample.exist.not"));
            return baseResponse;
        }
        QueryMatrixInfoRsp queryMatrixInfoRsp = new QueryMatrixInfoRsp();
        queryMatrixInfoRsp.setId(testMatrixInfoPO.getID());
        queryMatrixInfoRsp.setMatrixNo(testMatrixInfoPO.getMatrixNo());
        queryMatrixInfoRsp.setTestSampleID(testMatrixInfoPO.getTestSampleID());
        queryMatrixInfoRsp.setSampleNo(testSampleInfoPO.getSampleNo());
        queryMatrixInfoRsp.setTestLineInstanceId(testMatrixInfoPO.getTestLineInstanceID());
        baseResponse.setData(queryMatrixInfoRsp);
        return baseResponse;
    }

    public BaseResponse<List<QueryMatrixInfoRsp>> getMatrixInfoByMatrixNos(QueryMatrixInfoReq queryMatrixInfoReq) {
        if(Func.isEmpty(queryMatrixInfoReq) || Func.isEmpty(queryMatrixInfoReq.getMatrixNoList())){
            return BaseResponse.newFailInstance("param.miss",null);
        }
        TestMatrixInfoExample testMatrixInfoExample = new TestMatrixInfoExample();
        testMatrixInfoExample.createCriteria().andMatrixNoIn(queryMatrixInfoReq.getMatrixNoList());
        List<TestMatrixInfoPO> testMatrixInfoPOS = testMatrixInfoMapper.selectByExample(testMatrixInfoExample);
        List<TestSampleInfoPO> testSampleList = new ArrayList<>();
        if(Func.isNotEmpty(testMatrixInfoPOS)){
            List<String> testSampleIdList = testMatrixInfoPOS.stream().map(TestMatrixInfoPO::getTestSampleID).collect(Collectors.toList());
            if(Func.isNotEmpty(testSampleIdList)){
                testSampleList = testSampleExtMapper.getTestSampleIdList(testSampleIdList);
            }
        }
        List<QueryMatrixInfoRsp> queryMatrixInfoRsps = new ArrayList<>();
        if(Func.isNotEmpty(testMatrixInfoPOS)){
            for (TestMatrixInfoPO testMatrixInfoPO : testMatrixInfoPOS) {
                QueryMatrixInfoRsp queryMatrixInfoRsp = new QueryMatrixInfoRsp();
                queryMatrixInfoRsp.setId(testMatrixInfoPO.getID());
                queryMatrixInfoRsp.setMatrixNo(testMatrixInfoPO.getMatrixNo());
                if(Func.isNotEmpty(testSampleList)){
                    TestSampleInfoPO testSampleInfoPO = testSampleList.stream().filter(item -> Func.equalsSafe(item.getID(), testMatrixInfoPO.getTestSampleID())).findAny().orElse(null);
                    if(Func.isNotEmpty(testSampleInfoPO)){
                        queryMatrixInfoRsp.setSampleNo(testSampleInfoPO.getSampleNo());
                        queryMatrixInfoRsp.setOrderNo(testSampleInfoPO.getOrderNo());
                    }
                }
                queryMatrixInfoRsp.setTestSampleID(testMatrixInfoPO.getTestSampleID());
                queryMatrixInfoRsp.setTestLineInstanceId(testMatrixInfoPO.getTestLineInstanceID());
                queryMatrixInfoRsps.add(queryMatrixInfoRsp);
            }
        }
        return BaseResponse.newSuccessInstance(queryMatrixInfoRsps);
    }
}
