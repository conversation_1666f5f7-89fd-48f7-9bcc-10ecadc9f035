package com.sgs.otsnotes.domain.service;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.sgs.core.domain.UserInfo;
import com.sgs.framework.core.base.CustomResult;
import com.sgs.framework.model.enums.LanguageType;
import com.sgs.framework.model.enums.TestLineType;
import com.sgs.otsnotes.core.common.TrimsShuntLabHelper;
import com.sgs.otsnotes.core.common.UserHelper;
import com.sgs.otsnotes.core.config.LOStringUtil;
import com.sgs.otsnotes.core.thread.ThreadPoolContextTaskExecutor;
import com.sgs.otsnotes.core.util.DateUtils;
import com.sgs.otsnotes.core.util.ListHelper;
import com.sgs.otsnotes.core.util.NumberUtil;
import com.sgs.otsnotes.core.util.StringUtil;
import com.sgs.otsnotes.dbstorages.mybatis.enums.CustomerType;
import com.sgs.otsnotes.dbstorages.mybatis.extmapper.*;
import com.sgs.otsnotes.dbstorages.mybatis.extmodel.requirment.LimitInstanceInfo;
import com.sgs.otsnotes.dbstorages.mybatis.extmodel.requirment.RequirmentLimitInfo;
import com.sgs.otsnotes.dbstorages.mybatis.extmodel.worksheet.TestLineLimitInfo;
import com.sgs.otsnotes.dbstorages.mybatis.mapper.TestLineDataEntryStyleInfoMapper;
import com.sgs.otsnotes.dbstorages.mybatis.model.*;
import com.sgs.otsnotes.domain.service.limitpa.TrimsLocalLimitGroupPAService;
import com.sgs.otsnotes.facade.model.comparator.RequirmentSampleComparator;
import com.sgs.otsnotes.facade.model.comparator.RequirmentTestLineComparator;
import com.sgs.otsnotes.facade.model.comparator.TestAnalyteLimitComparator;
import com.sgs.otsnotes.facade.model.comparator.TestLineLimitComparator;
import com.sgs.otsnotes.facade.model.dto.*;
import com.sgs.otsnotes.facade.model.enums.*;
import com.sgs.otsnotes.facade.model.info.PPSectionSampleTestLineInfo;
import com.sgs.otsnotes.facade.model.info.limitgroup.*;
import com.sgs.otsnotes.facade.model.info.trims.PPSampleLimitGroupInfo;
import com.sgs.otsnotes.facade.model.info.trims.TestAnalyteLanguage;
import com.sgs.otsnotes.facade.model.info.trims.UnitForReportingLanguage;
import com.sgs.otsnotes.facade.model.req.requirment.*;
import com.sgs.otsnotes.facade.model.req.testLine.TestLineInstanceIdReq;
import com.sgs.otsnotes.facade.model.req.trims.ProductAttrArgumentReq;
import com.sgs.otsnotes.facade.model.req.trims.TrimsTestLineProductAttrReq;
import com.sgs.otsnotes.facade.model.rsp.PPSampleRelInfo;
import com.sgs.otsnotes.facade.model.rsp.dataentry.ProductAttribute;
import com.sgs.otsnotes.facade.model.rsp.requirment.GetRequirmentListRsp;
import com.sgs.otsnotes.facade.model.rsp.requirment.PPMatrixRelInfo;
import com.sgs.otsnotes.facade.model.rsp.trims.ClientLimitGroupRsp;
import com.sgs.otsnotes.facade.model.rsp.trims.TrimsLimitGroupRsp;
import com.sgs.otsnotes.integration.CustomerClient;
import com.sgs.otsnotes.integration.TrimsClient;
import com.sgs.preorder.facade.model.enums.CustomerWeightType;
import com.sgs.preorder.facade.model.info.customer.CustomerBuyerGroupInfo;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.Predicate;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.util.concurrent.ListenableFutureCallback;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Service
public class RequirmentService {
    private static final Logger logger = LoggerFactory.getLogger(RequirmentService.class);
    @Autowired
    private LimitMapper limitMapper;
    @Autowired
    private AnalyteMapper analyteMapper;
    @Autowired
    private PPMapper ppMapper;
    @Autowired
    private TestConditionMapper testConditionMapper;
    @Autowired
    private TestMatrixService testMatrixService;
    @Autowired
    private LimitLanguageMapper limitLanguageMapper;
    @Autowired
    private ReportMapper reportMapper;
    @Autowired
    private OrderMapper orderMapper;
    @Autowired
    private ConclusionMapper conclusionMapper;
    @Autowired
    private PPTestLineRelMapper ppTestLineRelMapper;
    @Autowired
    private LimitGroupMapper limitGroupMapper;
    @Autowired
    private ProductAttrMapper productAttrMapper;
    @Autowired
    private TestLineMapper testLineMapper;
    @Autowired
    private TestSampleExtMapper testSampleExtMapper;
    @Autowired
    private CustomerClient customerClient;
    @Autowired
    private TrimsClient trimsClient;
    @Autowired
    private TransactionTemplate transactionTemplate;
    @Autowired
    private TestAnalyteLimitBaseInfoExtMapper testAnalyteLimitBaseInfoExtMapper;
    @Autowired
    private TestLineLocalService testLineLocalService;
    @Autowired
    private TrimsLocalLimitGroupPAService trimsLocalLimitGroupPAService;
    @Autowired
    private ThreadPoolContextTaskExecutor trimsTaskExecutor;
    @Autowired
    private TestLineDataEntryStyleInfoMapper testLineDataEntryStyleInfoMapper;
    @Resource
    private GeneralOrderInstanceMapper generalOrderInstanceMapper;

    private final static String CChemLab="CChemLab";

    /**
     *
     * @param reqObject
     * @return
     */
    public CustomResult getLimitGroupList(LimitGroupReq reqObject){
        CustomResult rspResult = new CustomResult();

        GeneralOrderInstanceInfoPO order = orderMapper.getOrderInfo(reqObject.getOrderNo());
        if (order == null) {
            rspResult.setMsg("Order do not exist!");
            return rspResult;
        }
        ConclusionMode conclusionMode = ConclusionMode.findCode(order.getConclusionMode());
        ConclusionMode oldConclusionMode = conclusionMode;
        if (reqObject.getConclusionMode() > 0){
            conclusionMode = ConclusionMode.findCode(reqObject.getConclusionMode());
        }
        List<RequirmentTestLineInfo> testLineMatrixs = testLineMapper.getRequirmentTestLineInfoList(order.getID());
        // TODO v-tl别名修改
        testLineLocalService.build(reqObject.getOrderNo(),testLineMatrixs);

        RequirmentInfo requirment = new RequirmentInfo();
        requirment.setOrderNo(order.getOrderNo());
        requirment.setOrderStatus(order.getOrderStatus());
        if (conclusionMode != ConclusionMode.PP_MATRIX){
            conclusionMode = ConclusionMode.MATRIX;
        }
        requirment.setConclusionMode(conclusionMode.getId());
        requirment.setActiveIndicator(order.getActiveIndicator() == true ? 1 : 0);

        String customerGroupCode = order.getCustomerGroupCode();
        CustomResult<CustomerBuyerGroupInfo> rspWeight = customerClient.getCustomerWeightInfo(reqObject.getOrderNo(), CustomerWeightType.CAL);
        if (rspWeight.getData() != null){
            customerGroupCode = rspWeight.getData().getBuyerGroup();
            requirment.setCustomerGroupCode(customerGroupCode);
        }
        requirment.setCustomerGroupCode(customerGroupCode);
        requirment.setCustomerGroupName(order.getCustomerGroupName());

        List<PPSampleRelInfo> ppSampleRels = ppMapper.getPPSampleRelList(order.getID());
        Map<String, PPSampleRelInfo> ppMaps = Maps.newHashMap();
        ppSampleRels.forEach(ppSampleRel->{
            if (StringUtils.isBlank(ppSampleRel.getPpId()) || ppMaps.containsKey(ppSampleRel.getPpId())){
                return;
            }
            ppMaps.put(ppSampleRel.getPpId(), ppSampleRel);
        });
        //TODO z-PA 这里可以从本地获取limitGroup PP customer
        ClientLimitGroupRsp rspLimit = null;
        if(TrimsShuntLabHelper.isShuntLab()){
            rspLimit = trimsLocalLimitGroupPAService.getLocalLimitGroupList(ppSampleRels, customerGroupCode, ConclusionMode.MATRIX);
        }else{
            rspLimit = trimsClient.getLimitGroupList(ppSampleRels, customerGroupCode, ConclusionMode.MATRIX);
        }

        List<TrimsLimitGroupRsp> limitGroups = Lists.newArrayList();
        if(rspLimit != null && rspLimit.getLimitGroups() != null){
            limitGroups = rspLimit.getLimitGroups();
        }
        // region Limit Group 分组

        Map<Integer, LimitGroupTypeInfo> limitGroupTypeMaps = Maps.newHashMap();
        LimitGroupTypeInfo limitGroupType;
        LimitGroupItemInfo limitGroupItem;
        for (TrimsLimitGroupRsp limitGroup: limitGroups) {
            int testLimitGroupTypeId = limitGroup.getTestLimitGroupTypeId();
            if (!limitGroupTypeMaps.containsKey(testLimitGroupTypeId)){
                limitGroupType = new LimitGroupTypeInfo();
                limitGroupType.setLimitGroupTypeId(testLimitGroupTypeId);
                limitGroupType.setLimitGroupTypeName(limitGroup.getTestLimitGroupTypeName());
                limitGroupType.setLimitGroups(Lists.newArrayList());
                limitGroupTypeMaps.put(testLimitGroupTypeId, limitGroupType);
            }
            limitGroupType = limitGroupTypeMaps.get(testLimitGroupTypeId);
            limitGroupItem = new LimitGroupItemInfo();
            limitGroupItem.setId(limitGroup.getTestLimitGroupId());
            limitGroupItem.setText(limitGroup.getTestLimitGroupValue());
            limitGroupItem.setTypeId(testLimitGroupTypeId);
            limitGroupType.getLimitGroups().add(limitGroupItem);
        }
        requirment.setLimitGroups(Lists.newArrayList(limitGroupTypeMaps.values()));
        // endregion
        boolean isLimitGroupEmpty = limitGroupTypeMaps.isEmpty();

        // 以TestSampleId 分组 PP Id
        Map<String, Map<String, PPSampleLimitGroupInfo>> ppLimitGroupMaps = Maps.newHashMap();
        // TODO z-PA 这里是获取PP matrix 的limitGroup 就是右边的limitGroup数据
        if(TrimsShuntLabHelper.isShuntLab()){
            ppLimitGroupMaps = trimsLocalLimitGroupPAService.getLocalLimitGroupPPMatrixList(ppSampleRels, conclusionMode);
        }else{
            ppLimitGroupMaps = trimsClient.getLimitGroupList(ppSampleRels, conclusionMode);
        }
        /* 获取Sample数据 */
        Map<String, RequirmentSampleInfo> sampleMaps = this.getRequirmentSampleList(order.getOrderNo(), oldConclusionMode, conclusionMode, ppLimitGroupMaps, ppMaps, isLimitGroupEmpty);
        List<RequirmentSampleInfo> samples = Lists.newArrayList(sampleMaps.values());
        samples.sort(new RequirmentSampleComparator(true));
        requirment.setSamples(samples);

        Map<String, Set<String>> ppMatrixMaps = Maps.newHashMap();
        List<PPMatrixRelInfo> ppMatrixRels = ppMapper.getPPMatrixRelInfoList(order.getID());
        ppMatrixRels.forEach(ppMatrixRel->{
            String ppMatrixKey = String.format("%s_%s", ppMatrixRel.getTestSampleId(), ppMatrixRel.getTestLineInstanceId());
            if (!ppMatrixMaps.containsKey(ppMatrixKey)){
                ppMatrixMaps.put(ppMatrixKey, Sets.newHashSet());
            }
            ppMatrixMaps.get(ppMatrixKey).add(ppMatrixRel.getPpId());
        });

        List<LimitGroupProductAttrInfo> oldProductAttrs = productAttrMapper.getLimitGroupProductAttrList(reqObject.getOrderNo());
        // testMatrixID+testLineVersionID+lgId
        Map<String, LimitGroupProductAttrInfo> oldProductAttrMaps = Maps.newHashMap();
        Map<String, Set<String>> testMatrixPpMaps = Maps.newHashMap();
        // 加PP到时会有多个TestMatrixId
        for (LimitGroupProductAttrInfo oldProductAttr : oldProductAttrs) {
            Long ppBaseId = NumberUtil.toLong(oldProductAttr.getPpBaseId());
            // PpBaseId+TestMatrixId+LimitGroupId
            String productAttrKey = String.format("%s_%s_%s", ppBaseId, oldProductAttr.getTestMatrixId(), NumberUtil.toInt(oldProductAttr.getLimitGroupId()));
            if (!testMatrixPpMaps.containsKey(oldProductAttr.getTestMatrixId())){
                testMatrixPpMaps.put(oldProductAttr.getTestMatrixId(), Sets.newHashSet());
            }
            testMatrixPpMaps.get(oldProductAttr.getTestMatrixId()).add(ppBaseId.toString());
            if (oldProductAttrMaps.containsKey(productAttrKey)){
                // TODO 是否会出现重复
                continue;
            }
            oldProductAttrMaps.put(productAttrKey, oldProductAttr);
        }
        boolean isPpMatrix = conclusionMode == ConclusionMode.PP_MATRIX;
        // TODO testMatrixId+testSampleID+testLineVersionID+limitGroupId
        Map<String, Set<MatrixLimitGroupInfo>> matrixLimitGroupMaps = Maps.newHashMap();
        // SampleId+TestLineId，testMatrixId
        Map<String, String> testMatrixMaps = Maps.newHashMap();
        // testLineVersionId_{limitGroupIds}
        Set<String> testLineVersionIds = Sets.newHashSet();

        Map<Object, ProductAttrArgumentReq> limitGroupMaps = Maps.newHashMap();
        List<Integer> limitGroupIds;
        ProductAttrArgumentReq reqArgument;
        MatrixLimitGroupInfo matrix;
        String limitGroupKey;
        for (RequirmentTestLineInfo testLine : testLineMatrixs) {
            List<TestLineMatrixInfo> matrixs = testLine.getMatrixs();
            if (matrixs == null || matrixs.isEmpty()){
                continue;
            }
            Integer testLineVersionId = testLine.getTestLineVersionId();
            for (TestLineMatrixInfo testMatrix: matrixs) {
                String testMatrixKey = String.format("%s_%s", testMatrix.getSampleId(), testLine.getTestLineInstanceId());
                if (!testMatrixMaps.containsKey(testMatrixKey)){
                    testMatrixMaps.put(testMatrixKey, testMatrix.getMatrixId());
                }
                RequirmentSampleInfo requirmentSample = sampleMaps.get(testMatrix.getSampleId());
                if (requirmentSample == null){
                    // DIG-5020 可能存在Cancel的
                    continue;
                }
                List<PPSampleLimitGroupInfo> ppSampleLimitGroups = requirmentSample.getPpSampleRels();
                if (ppSampleLimitGroups == null || ppSampleLimitGroups.isEmpty()){
                    continue;
                }

                List<TestLineMatrixProductAttrInfo> productAttrs = testMatrix.getProductAttrs();
                // 如果Customer应该就一条数据，List<Integer>
                for (PPSampleLimitGroupInfo limitGroup: ppSampleLimitGroups){
                    ConclusionMode _conclusionMode = ConclusionMode.findCode(limitGroup.getConclusionMode());
                    if (conclusionMode == ConclusionMode.PP_MATRIX){
                        Set<String> ppIds = ppMatrixMaps.get(testMatrixKey);
                        if (ppIds == null || ppIds.isEmpty() || !ppIds.contains(limitGroup.getPpId())){
                            continue;
                        }
                    }
                    limitGroupIds = Lists.newArrayList();
                    switch (_conclusionMode){
                        case PP_MATRIX:
                            Integer limitGroupId = limitGroup.getLimitGroupId();
                            if (limitGroupId == null || limitGroupId.intValue() <= 0){
                                continue;
                            }
                            limitGroupIds.add(limitGroupId);
                            break;
                        default:
                            limitGroupIds = limitGroup.getLimitGroupIds();
                            if (!isPpMatrix && (limitGroupIds == null || limitGroupIds.isEmpty())){
                                limitGroupIds = this.getLimitGroupList(sampleMaps, sampleMaps.get(testMatrix.getSampleId()));
                            }
                            break;
                    }
                    if (limitGroupIds == null || limitGroupIds.isEmpty()){
                        continue;
                    }
                    limitGroupKey = String.format("%s_%s", testLineVersionId, StringUtils.join(limitGroupIds,","));
                    matrix = new MatrixLimitGroupInfo();
                    matrix.setTestMatrixId(testMatrix.getMatrixId());
                    matrix.setLimitGroupKey(limitGroupKey);
                    matrix.setConclusionMode(conclusionMode);
                    matrix.setRowId(limitGroup.getRowId());
                    matrix.setTestLineVersionId(testLineVersionId);
                    matrix.setLimitGroupIds(limitGroupIds);
                    matrix.setPpId(limitGroup.getPpId());

                    // 当前Matrix下有多少Limit Group(考虑PP Limit PA Id)
                    if (!matrixLimitGroupMaps.containsKey(testMatrix.getMatrixId())){
                        matrixLimitGroupMaps.put(testMatrix.getMatrixId(), Sets.newHashSet());
                    }
                    matrixLimitGroupMaps.get(testMatrix.getMatrixId()).add(matrix);

                    if (!testLineVersionIds.contains(limitGroupKey)){
                        testLineVersionIds.add(limitGroupKey);

                        if (!limitGroupMaps.containsKey(limitGroupKey)){
                            limitGroupMaps.put(limitGroupKey, new ProductAttrArgumentReq());
                        }
                        reqArgument = limitGroupMaps.get(limitGroupKey);
                        reqArgument.setTlVersionIdentifier(testLineVersionId);
                        Set<Integer> testLimitGroupIds = reqArgument.getTestLimitGroupIds();
                        limitGroupIds.forEach(limitGroupId->{
                            if (!testLimitGroupIds.contains(limitGroupId)){
                                testLimitGroupIds.add(limitGroupId);
                            }
                        });
                        reqArgument.setConclusionMode(_conclusionMode.getId());
                        if (_conclusionMode != ConclusionMode.PP_MATRIX){
                            reqArgument.setCustomerCode(customerGroupCode);
                            reqArgument.setCustomerType(CustomerType.Group.getCode());
                        }
                    }

                    TestLineMatrixProductAttrInfo attr;
                    for (Integer limitGroupId: limitGroupIds) {
                        // PpBaseId+TestMatrixId+LimitGroupId
                        String productAttrKey = String.format("%s_%s_%s", StringUtil.isBlank(limitGroup.getPpId(), "0"), testMatrix.getMatrixId(), limitGroupId);
                        LimitGroupProductAttrInfo productAttr = oldProductAttrMaps.get(productAttrKey);
                        if (productAttr == null){
                            continue;
                        }
                        attr = new TestLineMatrixProductAttrInfo();
                        attr.setProductAttrId(productAttr.getProductAttrId());
                        attr.setOldProductAttrId(productAttr.getProductAttrId());
                        attr.setRowId(limitGroup.getRowId());
                        attr.setPpId(limitGroup.getPpId());
                        attr.setConclusionMode(limitGroup.getConclusionMode());
                        attr.setLimitGroupId(limitGroupId);
                        /**
                         * 1、Save 完再去Assign Sample时，需要考虑有PA的数据
                         * 2、需要获取PA数据列表
                         */
                        productAttrs.add(attr);
                    }
                }
            }
        }
        Map<Object, List<ProductAttribute>> productAttrMaps = Maps.newHashMap();
        if (!limitGroupMaps.isEmpty()){
            TrimsTestLineProductAttrReq reqParams = new TrimsTestLineProductAttrReq();
            reqParams.setType(ProductAttrType.AnalyteLimit.getAttrType());
            // TODO z-PA 这里是加载LG时，加载默认PA
            if(TrimsShuntLabHelper.isShuntLab()){
                //根据orderNo 获取所有testLine - testItem
                OrderPpSampleInfo ppSample = new OrderPpSampleInfo();
                ppSample.setOrderNo(reqObject.getOrderNo());
                productAttrMaps = trimsLocalLimitGroupPAService.getLocalLimitGroupProductAttr(reqParams, limitGroupMaps);
            }else{
                productAttrMaps = trimsClient.getLimitGroupProductAttr(reqParams, limitGroupMaps);
            }
        }

        for (RequirmentTestLineInfo testLine: testLineMatrixs) {
            List<TestLineMatrixInfo> matrixs = testLine.getMatrixs();
            if (matrixs == null || matrixs.isEmpty()){
                continue;
            }
            for (TestLineMatrixInfo testMatrix: matrixs) {
                // DIG-5887
                if (StringUtils.isNotBlank(testMatrix.getDescription())){
                    testMatrix.setSampleNo(String.format("%s (%s)", testMatrix.getSampleNo(), testMatrix.getDescription()));
                }
                Set<MatrixLimitGroupInfo> matrixLimitGroups = matrixLimitGroupMaps.get(testMatrix.getMatrixId());
                if (matrixLimitGroups == null || matrixLimitGroups.isEmpty()){
                    continue;
                }
                List<TestLineMatrixProductAttrInfo> productAttrs = testMatrix.getProductAttrs();
                for (MatrixLimitGroupInfo limitGroup: matrixLimitGroups){
                    if (!productAttrMaps.containsKey(limitGroup.getLimitGroupKey())){
                        continue;
                    }
                    if (isPpMatrix && testMatrixPpMaps.containsKey(testMatrix.getMatrixId())){
                        Set<String> ppIds = testMatrixPpMaps.get(testMatrix.getMatrixId());
                        if (!ppIds.contains(limitGroup.getPpId())){
                            continue;
                        }
                    }
                    TestLineMatrixProductAttrInfo attr;
                    for (Integer limitGroupId: limitGroup.getLimitGroupIds()) {
                        attr = productAttrs.stream().filter(productAttr->
                            StringUtil.equalsIgnoreCase(productAttr.getRowId(), limitGroup.getRowId()) &&
                            StringUtil.equalsIgnoreCase(productAttr.getPpId(), limitGroup.getPpId()) &&
                            StringUtil.equals(productAttr.getLimitGroupId(), limitGroupId)
                        ).findFirst().orElse(null);
                        if (attr == null){
                            continue;
                        }
                        ProductAttrLimitInfo productAttrLimit = this.getProductAttrList(productAttrMaps.get(limitGroup.getLimitGroupKey()), attr.getLimitGroupId(), attr.getProductAttrId());
                        attr.setLimitGroupId(productAttrLimit.getLimitGroupId());
                        attr.setProductAttrs(productAttrLimit.getProductAttrs());
                    }
                    /*attr = new TestLineMatrixProductAttrInfo();
                    attr.setRowId(limitGroup.getRowId());
                    attr.setPpId(limitGroup.getPpId());
                    attr.setConclusionMode(limitGroup.getConclusionMode().getId());
                    if (productAttrMaps.containsKey(limitGroup.getLimitGroupKey())) {
                        ProductAttrLimitInfo productAttrLimit = this.getProductAttrList(productAttrMaps.get(limitGroup.getLimitGroupKey()), attr.getLimitGroupId(), attr.getProductAttrId());
                        attr.setLimitGroupId(productAttrLimit.getLimitGroupId());
                        attr.setProductAttrs(productAttrLimit.getProductAttrs());
                    }
                    productAttrs.add(attr);*/
                }
                productAttrs.removeIf(productAttr->{
                    return NumberUtil.toInt(productAttr.getProductAttrId()) > 0 && CollectionUtils.isEmpty(productAttr.getProductAttrs());
                });
            }
        }
        testLineMatrixs.sort(new RequirmentTestLineComparator(true));
        requirment.setTestLineMatrixs(testLineMatrixs);

        rspResult.setData(requirment);
        rspResult.setSuccess(true);
        return rspResult;
    }

    public CustomResult getLimitGroupList_Old(LimitGroupReq reqObject){
        CustomResult rspResult = new CustomResult();

        GeneralOrderInstanceInfoPO order = orderMapper.getOrderInfo(reqObject.getOrderNo());
        if (order == null) {
            rspResult.setMsg("Order do not exist!");
            return rspResult;
        }
        ConclusionMode conclusionMode = ConclusionMode.findCode(order.getConclusionMode());
        ConclusionMode oldConclusionMode = conclusionMode;
        if (reqObject.getConclusionMode() > 0){
            conclusionMode = ConclusionMode.findCode(reqObject.getConclusionMode());
        }
        List<RequirmentTestLineInfo> testLineMatrixs = testLineMapper.getRequirmentTestLineInfoList(order.getID());
        // TODO v-tl别名修改
        testLineLocalService.build(reqObject.getOrderNo(),testLineMatrixs);

        RequirmentInfo requirment = new RequirmentInfo();
        requirment.setOrderNo(order.getOrderNo());
        requirment.setOrderStatus(order.getOrderStatus());
        if (conclusionMode != ConclusionMode.PP_MATRIX){
            conclusionMode = ConclusionMode.MATRIX;
        }
        requirment.setConclusionMode(conclusionMode.getId());
        requirment.setActiveIndicator(order.getActiveIndicator() == true ? 1 : 0);

        String customerGroupCode = order.getCustomerGroupCode();
        CustomResult<CustomerBuyerGroupInfo> rspWeight = customerClient.getCustomerWeightInfo(reqObject.getOrderNo(), CustomerWeightType.CAL);
        if (rspWeight.getData() != null){
            customerGroupCode = rspWeight.getData().getBuyerGroup();
            requirment.setCustomerGroupCode(customerGroupCode);
        }
        requirment.setCustomerGroupCode(customerGroupCode);
        requirment.setCustomerGroupName(order.getCustomerGroupName());

        List<PPSampleRelInfo> ppSampleRels = ppMapper.getPPSampleRelList(order.getID());
        Map<String, PPSampleRelInfo> ppMaps = Maps.newHashMap();
        ppSampleRels.forEach(ppSampleRel->{
            if (StringUtils.isBlank(ppSampleRel.getPpId()) || ppMaps.containsKey(ppSampleRel.getPpId())){
                return;
            }
            ppMaps.put(ppSampleRel.getPpId(), ppSampleRel);
        });
        //TODO z-PA 这里可以从本地获取limitGroup PP customer
        ClientLimitGroupRsp rspLimit = null;
        if(TrimsShuntLabHelper.isShuntLab()){
            rspLimit = trimsLocalLimitGroupPAService.getLocalLimitGroupList(ppSampleRels, customerGroupCode, ConclusionMode.MATRIX);
        }else{
            rspLimit = trimsClient.getLimitGroupList(ppSampleRels, customerGroupCode, ConclusionMode.MATRIX);
        }

        List<TrimsLimitGroupRsp> limitGroups = Lists.newArrayList();
        if(rspLimit != null && rspLimit.getLimitGroups() != null){
            limitGroups = rspLimit.getLimitGroups();
        }
        // region Limit Group 分组

        Map<Integer, LimitGroupTypeInfo> limitGroupTypeMaps = Maps.newHashMap();
        LimitGroupTypeInfo limitGroupType;
        LimitGroupItemInfo limitGroupItem;
        for (TrimsLimitGroupRsp limitGroup: limitGroups) {
            int testLimitGroupTypeId = limitGroup.getTestLimitGroupTypeId();
            if (!limitGroupTypeMaps.containsKey(testLimitGroupTypeId)){
                limitGroupType = new LimitGroupTypeInfo();
                limitGroupType.setLimitGroupTypeId(testLimitGroupTypeId);
                limitGroupType.setLimitGroupTypeName(limitGroup.getTestLimitGroupTypeName());
                limitGroupType.setLimitGroups(Lists.newArrayList());
                limitGroupTypeMaps.put(testLimitGroupTypeId, limitGroupType);
            }
            limitGroupType = limitGroupTypeMaps.get(testLimitGroupTypeId);
            limitGroupItem = new LimitGroupItemInfo();
            limitGroupItem.setId(limitGroup.getTestLimitGroupId());
            limitGroupItem.setText(limitGroup.getTestLimitGroupValue());
            limitGroupItem.setTypeId(testLimitGroupTypeId);
            limitGroupType.getLimitGroups().add(limitGroupItem);
        }
        requirment.setLimitGroups(Lists.newArrayList(limitGroupTypeMaps.values()));
        // endregion
        boolean isLimitGroupEmpty = limitGroupTypeMaps.isEmpty();

        // 以TestSampleId 分组 PP Id
        Map<String, Map<String, PPSampleLimitGroupInfo>> ppLimitGroupMaps = Maps.newHashMap();
        //TODO z-PA 这里是获取PP matrix 的limitGroup 就是右边的limitGroup数据
        if(TrimsShuntLabHelper.isShuntLab()){
            ppLimitGroupMaps = trimsLocalLimitGroupPAService.getLocalLimitGroupPPMatrixList(ppSampleRels, conclusionMode);
        }else{
            ppLimitGroupMaps = trimsClient.getLimitGroupList(ppSampleRels, conclusionMode);
        }
        /* 获取Sample数据 */
        Map<String, RequirmentSampleInfo> sampleMaps = this.getRequirmentSampleList(order.getOrderNo(), oldConclusionMode, conclusionMode, ppLimitGroupMaps, ppMaps, isLimitGroupEmpty);
        List<RequirmentSampleInfo> samples = Lists.newArrayList(sampleMaps.values());
        samples.sort(new RequirmentSampleComparator(true));
        requirment.setSamples(samples);

        Map<String, Set<String>> ppMatrixMaps = Maps.newHashMap();
        List<PPMatrixRelInfo> ppMatrixRels = ppMapper.getPPMatrixRelInfoList(order.getID());
        ppMatrixRels.forEach(ppMatrixRel->{
            String ppMatrixKey = String.format("%s_%s", ppMatrixRel.getTestSampleId(), ppMatrixRel.getTestLineInstanceId());
            if (!ppMatrixMaps.containsKey(ppMatrixKey)){
                ppMatrixMaps.put(ppMatrixKey, Sets.newHashSet());
            }
            ppMatrixMaps.get(ppMatrixKey).add(ppMatrixRel.getPpId());
        });

        List<LimitGroupProductAttrInfo> oldProductAttrs = productAttrMapper.getLimitGroupProductAttrList(reqObject.getOrderNo());
        // testMatrixID+testLineVersionID+lgId
        Map<String, LimitGroupProductAttrInfo> oldProductAttrMaps = Maps.newHashMap();
        Map<String, Set<String>> testMatrixPpMaps = Maps.newHashMap();
        // 加PP到时会有多个TestMatrixId
        for (LimitGroupProductAttrInfo oldProductAttr : oldProductAttrs) {
            Long ppBaseId = NumberUtil.toLong(oldProductAttr.getPpBaseId());
            // PpBaseId+TestMatrixId+LimitGroupId
            String productAttrKey = String.format("%s_%s_%s", ppBaseId, oldProductAttr.getTestMatrixId(), oldProductAttr.getLimitGroupId());
            if (!testMatrixPpMaps.containsKey(oldProductAttr.getTestMatrixId())){
                testMatrixPpMaps.put(oldProductAttr.getTestMatrixId(), Sets.newHashSet());
            }
            testMatrixPpMaps.get(oldProductAttr.getTestMatrixId()).add(ppBaseId.toString());
            if (oldProductAttrMaps.containsKey(productAttrKey)){
                // TODO 是否会出现重复
                continue;
            }
            oldProductAttrMaps.put(productAttrKey, oldProductAttr);
        }
        boolean isPpMatrix = conclusionMode == ConclusionMode.PP_MATRIX;
        // TODO testMatrixId+testSampleID+testLineVersionID+limitGroupId
        Map<String, Set<MatrixLimitGroupInfo>> matrixLimitGroupMaps = Maps.newHashMap();
        // SampleId+TestLineId，testMatrixId
        Map<String, String> testMatrixMaps = Maps.newHashMap();
        // testLineVersionId_{limitGroupIds}
        Set<String> testLineVersionIds = Sets.newHashSet();

        Map<Object, ProductAttrArgumentReq> limitGroupMaps = Maps.newHashMap();
        List<Integer> limitGroupIds;
        ProductAttrArgumentReq reqArgument;
        MatrixLimitGroupInfo matrix;
        String limitGroupKey;
        for (RequirmentTestLineInfo testLine : testLineMatrixs) {
            List<TestLineMatrixInfo> matrixs = testLine.getMatrixs();
            if (matrixs == null || matrixs.isEmpty()){
                continue;
            }
            Integer testLineVersionId = testLine.getTestLineVersionId();

            for (TestLineMatrixInfo testMatrix: matrixs) {
                String testMatrixKey = String.format("%s_%s", testMatrix.getSampleId(), testLine.getTestLineInstanceId());
                if (!testMatrixMaps.containsKey(testMatrixKey)){
                    testMatrixMaps.put(testMatrixKey, testMatrix.getMatrixId());
                }
                RequirmentSampleInfo requirmentSample = sampleMaps.get(testMatrix.getSampleId());
                if (requirmentSample == null){
                    // DIG-5020 可能存在Cancel的
                    continue;
                }
                List<PPSampleLimitGroupInfo> ppSampleLimitGroups = requirmentSample.getPpSampleRels();
                if (ppSampleLimitGroups == null || ppSampleLimitGroups.isEmpty()){
                    continue;
                }

                // 如果Customer应该就一条数据，List<Integer>
                for (PPSampleLimitGroupInfo limitGroup: ppSampleLimitGroups){
                    ConclusionMode _conclusionMode = ConclusionMode.findCode(limitGroup.getConclusionMode());
                    if (conclusionMode == ConclusionMode.PP_MATRIX){
                        Set<String> ppIds = ppMatrixMaps.get(testMatrixKey);
                        if (ppIds == null || ppIds.isEmpty() || !ppIds.contains(limitGroup.getPpId())){
                            continue;
                        }
                    }
                    limitGroupIds = Lists.newArrayList();
                    switch (_conclusionMode){
                        case PP_MATRIX:
                            Integer limitGroupId = limitGroup.getLimitGroupId();
                            if (limitGroupId == null || limitGroupId.intValue() <= 0){
                                continue;
                            }
                            limitGroupIds.add(limitGroupId);
                            break;
                        default:
                            limitGroupIds = limitGroup.getLimitGroupIds();
                            if (!isPpMatrix && (limitGroupIds == null || limitGroupIds.isEmpty())){
                                limitGroupIds = this.getLimitGroupList(sampleMaps, sampleMaps.get(testMatrix.getSampleId()));
                            }
                            break;
                    }
                    if (limitGroupIds == null || limitGroupIds.isEmpty()){
                        continue;
                    }
                    limitGroupKey = String.format("%s_%s", testLineVersionId, StringUtils.join(limitGroupIds,","));
                    matrix = new MatrixLimitGroupInfo();
                    matrix.setTestMatrixId(testMatrix.getMatrixId());
                    matrix.setLimitGroupKey(limitGroupKey);
                    matrix.setConclusionMode(conclusionMode);
                    matrix.setRowId(limitGroup.getRowId());
                    matrix.setTestLineVersionId(testLineVersionId);
                    matrix.setLimitGroupIds(limitGroupIds);
                    matrix.setPpId(limitGroup.getPpId());
                    String matrixLimitGroupKey = String.format("%s_%s", testMatrix.getMatrixId(), testLineVersionId);
                    if (StringUtils.isNotBlank(limitGroup.getLimitGroupInstanceId())){
                        matrixLimitGroupKey += String.format("_%s", limitGroup.getLimitGroupInstanceId());
                    }
                    matrix.setMatrixLimitGroupKey(matrixLimitGroupKey);

                    // MatrixId + PPId

                    // 当前Matrix下有多少Limit Group(考虑PP Limit PA Id)
                    if (!matrixLimitGroupMaps.containsKey(testMatrix.getMatrixId())){
                        matrixLimitGroupMaps.put(testMatrix.getMatrixId(), Sets.newHashSet());
                    }
                    matrixLimitGroupMaps.get(testMatrix.getMatrixId()).add(matrix);

                    if (testLineVersionIds.contains(limitGroupKey)){
                        continue;
                    }
                    testLineVersionIds.add(limitGroupKey);

                    if (!limitGroupMaps.containsKey(limitGroupKey)){
                        limitGroupMaps.put(limitGroupKey, new ProductAttrArgumentReq());
                    }
                    reqArgument = limitGroupMaps.get(limitGroupKey);
                    reqArgument.setTlVersionIdentifier(testLineVersionId);
                    Set<Integer> testLimitGroupIds = reqArgument.getTestLimitGroupIds();
                    limitGroupIds.forEach(limitGroupId->{
                        if (!testLimitGroupIds.contains(limitGroupId)){
                            testLimitGroupIds.add(limitGroupId);
                        }
                    });
                    if (_conclusionMode != ConclusionMode.PP_MATRIX){
                        reqArgument.setCustomerCode(customerGroupCode);
                        reqArgument.setCustomerType(CustomerType.Group.getCode());
                    }
                }
            }
        }
        Map<Object, List<ProductAttribute>> productAttrMaps = Maps.newHashMap();
        if (!limitGroupMaps.isEmpty()){
            TrimsTestLineProductAttrReq reqParams = new TrimsTestLineProductAttrReq();
            reqParams.setType(ProductAttrType.AnalyteLimit.getAttrType());
            //TODO z-PA 这里是加载LG时，加载默认PA
            if(TrimsShuntLabHelper.isShuntLab()){
                //根据orderNo 获取所有testLine - testItem
                OrderPpSampleInfo ppSample = new OrderPpSampleInfo();
                ppSample.setOrderNo(reqObject.getOrderNo());
                productAttrMaps = trimsLocalLimitGroupPAService.getLocalLimitGroupProductAttr(reqParams, limitGroupMaps);
            }else{
                productAttrMaps = trimsClient.getLimitGroupProductAttr(reqParams, limitGroupMaps);
            }
        }

        LimitGroupProductAttrInfo productAttr;
        TestLineMatrixProductAttrInfo attr;
        for (RequirmentTestLineInfo testLine: testLineMatrixs) {
            List<TestLineMatrixInfo> matrixs = testLine.getMatrixs();
            if (matrixs == null || matrixs.isEmpty()){
                continue;
            }
            for (TestLineMatrixInfo testMatrix: matrixs) {
                Set<MatrixLimitGroupInfo> matrixLimitGroups = matrixLimitGroupMaps.get(testMatrix.getMatrixId());
                if (matrixLimitGroups == null || matrixLimitGroups.isEmpty()){
                    continue;
                }
                List<TestLineMatrixProductAttrInfo> productAttrs = testMatrix.getProductAttrs();
                for (MatrixLimitGroupInfo limitGroup: matrixLimitGroups){
                    if (!productAttrMaps.containsKey(limitGroup.getLimitGroupKey())){
                        continue;
                    }
                    if (isPpMatrix && testMatrixPpMaps.containsKey(testMatrix.getMatrixId())){
                        Set<String> ppIds = testMatrixPpMaps.get(testMatrix.getMatrixId());
                        if (!ppIds.contains(limitGroup.getPpId())){
                            continue;
                        }
                    }
                    //productAttrKey = String.format("%s_%s_%s", testMatrix.getId(), testLineVersionId, limitGroupId);
                    //productAttrKey = limitGroup.getProductAttrKey();
                    String matrixLimitGroupKey = limitGroup.getMatrixLimitGroupKey();
                    attr = new TestLineMatrixProductAttrInfo();
                    productAttr = new LimitGroupProductAttrInfo();

                    // PpBaseId+TestMatrixId+LimitGroupId

                    boolean isProductAttrId = oldProductAttrMaps.containsKey(matrixLimitGroupKey);
                    if (isProductAttrId){
                        productAttr = oldProductAttrMaps.get(matrixLimitGroupKey);
                    }else {
                        if (!(isProductAttrId || isPpMatrix)){
                            // TODO 当Save PA后，则添加TL时，是否要考虑
                            //productAttr = this.findParentSampleProductAttributeList(sampleMaps, testMatrixMaps, oldProductAttrMaps, sampleMaps.get(testMatrix.getSampleId()), limitGroup, testLine.getTestLineInstanceId());
                        }
                    }
                    if (productAttr == null){
                        productAttr = new LimitGroupProductAttrInfo();
                    }
                    if (isProductAttrId){
                        attr.setProductAttrId(productAttr.getProductAttrId());
                        attr.setOldProductAttrId(productAttr.getProductAttrId());
                    }
                    attr.setRowId(limitGroup.getRowId());
                    attr.setPpId(limitGroup.getPpId());
                    attr.setConclusionMode(limitGroup.getConclusionMode().getId());
                    attr.setLimitGroupId(productAttr.getLimitGroupId());
                    if (productAttrMaps.containsKey(limitGroup.getLimitGroupKey())) {
                        ProductAttrLimitInfo productAttrLimit = this.getProductAttrList(productAttrMaps.get(limitGroup.getLimitGroupKey()), attr.getLimitGroupId(), attr.getProductAttrId());
                        attr.setLimitGroupId(productAttrLimit.getLimitGroupId());
                        attr.setProductAttrs(productAttrLimit.getProductAttrs());
                    }
                    /*limitGroupIds = limitGroup.getLimitGroupIds();
                    if ((productAttr.getLimitGroupId() == null || productAttr.getLimitGroupId().intValue() <= 0 ) && limitGroupIds != null && !limitGroupIds.isEmpty()){
                        attr.setLimitGroupId(limitGroupIds.get(0));
                    }*/
                    productAttrs.add(attr);
                }
            }
        }
        testLineMatrixs.sort(new RequirmentTestLineComparator(true));
        requirment.setTestLineMatrixs(testLineMatrixs);

        rspResult.setData(requirment);
        rspResult.setSuccess(true);
        return rspResult;
    }

    /**
     * PpBaseId+TestSampleId+TestLineInstanceId(TestLineVersionId)+LimitGroupInstanceId(LimitGroupId)
     * @param orderNo
     * @param oldConclusionMode
     * @param conclusionMode
     * @param ppLimitGroups
     * @param ppMaps
     * @param isLimitGroupEmpty
     * @return
     */
    private Map<String, RequirmentSampleInfo> getRequirmentSampleList(String orderNo, ConclusionMode oldConclusionMode, ConclusionMode conclusionMode, Map<String, Map<String, PPSampleLimitGroupInfo>> ppLimitGroups, Map<String, PPSampleRelInfo> ppMaps, boolean isLimitGroupEmpty){
        List<LimitGroupInstancePO> oldLimitGroups = Lists.newArrayList();
        if (oldConclusionMode.getId() > 0 && oldConclusionMode == conclusionMode){
            oldLimitGroups = this.limitGroupMapper.getLimitGroupListByOrderNo(orderNo);
        }
        Set<String> limitGroups = Sets.newHashSet();
        PPSampleLimitGroupInfo ppLimitGroup;
        List<String> delLimitGroupIds = Lists.newArrayList();

        for (LimitGroupInstancePO oldLimitGroup : oldLimitGroups) {
            String limitGroupKey = String.format("%s_%s", oldLimitGroup.getTestSampleID(), oldLimitGroup.getTestLimitGroupTypeID());
            long ppBaseId = NumberUtil.toLong(oldLimitGroup.getPpBaseId());
            String ppId = ppBaseId > 0 ? String.valueOf(ppBaseId) : "";
            if (StringUtils.isBlank(ppId) && limitGroups.contains(limitGroupKey)) {
                delLimitGroupIds.add(oldLimitGroup.getID());
                continue;
            }
            limitGroups.add(limitGroupKey);

            if (StringUtils.isBlank(ppId) || conclusionMode != ConclusionMode.PP_MATRIX){
                ppId = "";
            }
            if (!ppLimitGroups.containsKey(oldLimitGroup.getTestSampleID())){
                ppLimitGroups.put(oldLimitGroup.getTestSampleID(), Maps.newHashMap());
            }
            // PPId
            Map<String, PPSampleLimitGroupInfo> ppLimitGroupMaps = ppLimitGroups.get(oldLimitGroup.getTestSampleID());
            if (!ppLimitGroupMaps.containsKey(ppId)){
                ppLimitGroup = new PPSampleLimitGroupInfo();
                ppLimitGroup.setRowId(DigestUtils.md5Hex(oldLimitGroup.getTestSampleID()));
                if (StringUtils.isNotBlank(ppId)){
                    // 说明该PP已删除
                    PPSampleRelInfo ppSampleRel = ppMaps.get(ppId);
                    if (ppSampleRel == null){
                        delLimitGroupIds.add(oldLimitGroup.getID());
                        continue;
                    }
                    ppLimitGroup.setPpId(ppSampleRel.getPpId());
                    ppLimitGroup.setPpName(StringUtil.isBlankDefault(ppSampleRel.getPpName(), ppSampleRel.getCitationName(), ";"));
                    ppLimitGroup.setCitationName(ppSampleRel.getCitationName());
                    ppLimitGroup.setRowId(DigestUtils.md5Hex(String.format("%s_%s", ppSampleRel.getPpId(), ppSampleRel.getTestSampleId())));
                }
                ppLimitGroupMaps.put(ppId, ppLimitGroup);
            }
            ppLimitGroup = ppLimitGroupMaps.get(ppId);
            ppLimitGroup.setConclusionMode(oldLimitGroup.getConclusionMode());

            switch (ConclusionMode.findCode(oldLimitGroup.getConclusionMode())){
                case PP_MATRIX:
                    // TODO 如果没有PP，则不显示 Matrix、PP Matrix
                    if (StringUtils.isNotBlank(ppId) && ppLimitGroup.getPpLimitGroup() != null){
                        ppLimitGroup.setLimitGroupId(oldLimitGroup.getLimitGroupID());
                    }
                    break;
                default:
                    if (StringUtils.isBlank(ppLimitGroup.getRowId())){
                        ppLimitGroup.setRowId(DigestUtils.md5Hex(oldLimitGroup.getTestSampleID()));
                    }
                    if (isLimitGroupEmpty){
                        ppLimitGroup.setLimitGroupIds(Lists.newArrayList());
                        continue;
                    }
                    List<Integer> limitGroupIds = ppLimitGroup.getLimitGroupIds();
                    limitGroupIds.add(oldLimitGroup.getLimitGroupID());
                    break;
            }
        }
        Map<String, RequirmentSampleInfo> sampleMaps = Maps.newHashMap();
        List<PPSampleLimitGroupInfo> ppSampleRels;
        RequirmentSampleInfo newSample;
        List<TestSampleInfoPO> oldSamples = testSampleExtMapper.getSampleByOrderNo(orderNo);
        for (TestSampleInfoPO oldSample: oldSamples) {
            SampleType sampleType = SampleType.findType(oldSample.getSampleType());
            if (sampleType == null){
                continue;
            }
            //NC的sample 需要过滤 add by vincent 2020-12-10
            if(oldSample.getApplicable()){
                continue;
            }
            //cancel 的sample 需要过滤掉 add by vincent
            if(!oldSample.getActiveIndicator()){
                continue;
            }
            if(oldSample.getApplicable()){
                continue;
            }
            newSample = new RequirmentSampleInfo();
            newSample.setSampleId(oldSample.getID());
            newSample.setSampleNo(oldSample.getSampleNo());
            newSample.setSampleType(oldSample.getSampleType());
            newSample.setSampleParentId(oldSample.getSampleParentID());
            newSample.setDescription(oldSample.getDescription());
            newSample.setComposition(oldSample.getComposition());
            newSample.setCategory(oldSample.getCategory());
            newSample.setColor(oldSample.getColor());
            newSample.setSampleTypeShort(sampleType.getShortMessage());
            newSample.setSampleSeq(oldSample.getSampleSeq());

            ppSampleRels = Lists.newArrayList();
            if (ppLimitGroups.containsKey(newSample.getSampleId())){
                ppSampleRels.addAll(Lists.newArrayList(ppLimitGroups.get(newSample.getSampleId()).values()));
            }
            if (ppSampleRels.isEmpty()){
                ppLimitGroup = new PPSampleLimitGroupInfo();
                ppLimitGroup.setRowId(DigestUtils.md5Hex(newSample.getSampleId()));
                ppSampleRels.add(ppLimitGroup);
            }
            newSample.setPpSampleRels(ppSampleRels);

            sampleMaps.put(newSample.getSampleId(), newSample);
        }
        if (!delLimitGroupIds.isEmpty()){
            limitGroupMapper.batchDelete(delLimitGroupIds);
        }
        //this.convertSampleTree(sampleTrees, samples, null);
        return sampleMaps;
    }

    /**
     *
     * @param sampleMaps
     * @param sample
     * @return
     */
    private List<Integer> getLimitGroupList(Map<String, RequirmentSampleInfo> sampleMaps, RequirmentSampleInfo sample) {
        List<PPSampleLimitGroupInfo> ppSampleRels = sample.getPpSampleRels();
        if (ppSampleRels == null || ppSampleRels.isEmpty()) {
            return null;
        }
        List<Integer> limitGroupIds = Lists.newArrayList();
        for (PPSampleLimitGroupInfo ppSampleRel: ppSampleRels) {
            limitGroupIds = ppSampleRel.getLimitGroupIds();
            if (limitGroupIds == null || limitGroupIds.isEmpty()){
                continue;
            }
            return limitGroupIds;
        }
        if(limitGroupIds.isEmpty() && StringUtils.isNotBlank(sample.getSampleParentId())){
            RequirmentSampleInfo parentSample = sampleMaps.get(sample.getSampleParentId());
            if (parentSample != null) {
                limitGroupIds = this.getLimitGroupList(sampleMaps, parentSample);
            }
        }
        return limitGroupIds;
    }

    /**
     *
     * @param reqObject
     * @return
     */
    public CustomResult<Map<String, ProductAttrLimitInfo>> getProductAttributeList(ProductAttrLimitGroupReq reqObject){
        CustomResult rspResult = new CustomResult();
        if (StringUtils.isBlank(reqObject.getOrderNo())){
            rspResult.setMsg("请求的OrderNo不能为空.");
            return rspResult;
        }
        Set<String> sets = Sets.newHashSet();//做去重用
        Map<Object, ProductAttrArgumentReq> limitGroupMaps = Maps.newHashMap();
        String customerGroupCode = reqObject.getCustomerGroupCode();
        CustomResult<CustomerBuyerGroupInfo> rspWeight = customerClient.getCustomerWeightInfo(reqObject.getOrderNo(), CustomerWeightType.CAL);
        if (rspWeight.getData() != null){
            customerGroupCode = rspWeight.getData().getBuyerGroup();
        }
        Map<String, ProductAttrLimitInfo> productAttrMaps = Maps.newHashMap();

        Set<Integer> limitGroupIds = reqObject.getLimitGroupIds();
        if (limitGroupIds == null || limitGroupIds.isEmpty()){
            rspResult.setMsg("请求的Limit Group Ids不能为空.");
            return rspResult;
        }
        boolean isPpMatrix = ConclusionMode.check(reqObject.getConclusionMode(), ConclusionMode.PP_MATRIX);
        Set<Integer> testLineVersionIds = reqObject.getTestLineVersionIds();

        boolean isTrimsLocal = TrimsShuntLabHelper.isShuntLab();
        if (isPpMatrix){
            if (StringUtils.isBlank(reqObject.getPpId())){
                rspResult.setMsg("请求的PpId不能为空.");
                return rspResult;
            }
            if (StringUtils.isBlank(reqObject.getSampleId())){
                rspResult.setMsg("请求的SampleId不能为空.");
                return rspResult;
            }
            com.sgs.otsnotes.facade.model.info.limitgroup.OrderPpSampleInfo ppSample = new com.sgs.otsnotes.facade.model.info.limitgroup.OrderPpSampleInfo();
            ppSample.setOrderNo(reqObject.getOrderNo());
            //PPID 是用来确定当前操作的是哪一个PP 然后找到PP对应的TL ，进行PA的回显，否则会出现操作PP1 ,回显pp2,3,4 的TL的PA
            ppSample.setPpId(reqObject.getPpId());
            ppSample.setPpBaseId(NumberUtil.toLong(reqObject.getPpId()));
            ppSample.setSampleId(reqObject.getSampleId());
            if (isTrimsLocal){
                Set<Integer> ppTestLineVersionIds = testLineMapper.getPpSampleTestLineVersionIds(ppSample);
                if(CollectionUtils.isNotEmpty(ppTestLineVersionIds)){
                    testLineVersionIds = ppTestLineVersionIds;
                }
            }else{
                testLineVersionIds = testLineMapper.getPpSampleTestLineVersionIdsNew(ppSample);
            }
        }
        if (testLineVersionIds == null || testLineVersionIds.isEmpty()){
            rspResult.setMsg("请求的TestLine Version Ids不能为空.");
            return rspResult;
        }
        ProductAttrArgumentReq reqArgument;

        // 循环添加查询条件
        for (Integer testLineVersionId : testLineVersionIds) {
            for (Integer limitGroupId : limitGroupIds) {
                String limitKey = String.format("%s_%s_%s", testLineVersionId, customerGroupCode, limitGroupId);
                if(sets.contains(limitKey)){
                    continue;
                }
                sets.add(limitKey);

                if (!limitGroupMaps.containsKey(testLineVersionId)){
                    limitGroupMaps.put(testLineVersionId, new ProductAttrArgumentReq());
                }
                reqArgument = limitGroupMaps.get(testLineVersionId);
                reqArgument.setTlVersionIdentifier(testLineVersionId);
                Set<Integer> testLimitGroupIds = reqArgument.getTestLimitGroupIds();
                if (!testLimitGroupIds.contains(limitGroupId)){
                    testLimitGroupIds.add(limitGroupId);
                }
                if (!isPpMatrix){
                    reqArgument.setCustomerCode(customerGroupCode);
                    reqArgument.setCustomerType(CustomerType.Group.getCode());
                }
            }
        }
        TrimsTestLineProductAttrReq reqParams = new TrimsTestLineProductAttrReq();
        reqParams.setType(ProductAttrType.AnalyteLimit.getAttrType());
        reqParams.setQueryArgument(Lists.newArrayList(limitGroupMaps.values()));

        // 查询结果转化
        Map<Object, List<ProductAttribute>> productAttributeMaps = Maps.newHashMap();
        if(isTrimsLocal){
            productAttributeMaps = trimsLocalLimitGroupPAService.getLocalLimitGroupProductAttr(reqParams, limitGroupMaps);
        }else{
            productAttributeMaps = trimsClient.getLimitGroupProductAttr(reqParams, limitGroupMaps);
        }
        ProductAttrLimitInfo productAttr;
        for (Object testLineVersionId : productAttributeMaps.keySet()) {
            productAttr = new ProductAttrLimitInfo();
            ProductAttrArgumentReq argument = limitGroupMaps.get(testLineVersionId);
            if (argument != null){
                productAttr.setRowId(reqObject.getRowId());
                productAttr.setConclusionMode(reqObject.getConclusionMode());
            }
            ProductAttrLimitInfo productAttrLimit = this.getProductAttrList(productAttributeMaps.get(testLineVersionId), null, null);
            productAttr.setLimitGroupId(productAttrLimit.getLimitGroupId());
            productAttr.setProductAttrs(productAttrLimit.getProductAttrs());

            productAttrMaps.put(String.valueOf(testLineVersionId), productAttr);
        }
        rspResult.setSuccess(true);
        rspResult.setData(productAttrMaps);
        return rspResult;
    }

    /**
     *
     * @param productAttributes
     * @param testLimitGroupId
     * @param productAttrId
     * @return
     */
    private ProductAttrLimitInfo getProductAttrList(List<ProductAttribute> productAttributes, Integer testLimitGroupId, Integer productAttrId){
        ProductAttrLimitInfo productAttr = new ProductAttrLimitInfo();
        if (productAttributes == null || productAttributes.isEmpty()){
            return productAttr;
        }
        if (testLimitGroupId == null){
            testLimitGroupId = 0;
        }
        List<ProductAttrInfo> productAttrs = Lists.newArrayList();
        ProductAttrInfo productAttrItem;
        Integer limitGroupId = null;
        for (ProductAttribute productAttribute : productAttributes) {
            if (limitGroupId == null || limitGroupId.intValue() <= 0 || limitGroupId.intValue() == testLimitGroupId){
                limitGroupId = productAttribute.getLimitGroupId();
            }
            productAttrItem = new ProductAttrInfo();
            productAttrItem.setId(productAttribute.getProductAttributeId());
            productAttrItem.setText(productAttribute.getProductAttributeValue());
            productAttrs.add(productAttrItem);
            if (productAttrId == null || productAttrId.intValue() <= 0){
                continue;
            }
            limitGroupId = productAttribute.getLimitGroupId();
        }
        productAttr.setProductAttrs(productAttrs);
        productAttr.setLimitGroupId(limitGroupId);

        return productAttr;
    }

    /**
     *
     * @param reqObject
     * @return
     */
    public CustomResult saveLimitGroup(SaveLimitGroupReq reqObject){
        CustomResult rspResult = new CustomResult();
        ReportInfoPO report = reportMapper.getReportByOrderNo(reqObject.getOrderNo());
        if (ReportStatus.check(report.getReportStatus(), ReportStatus.Approved)){
            rspResult.setMsg("Data cannot be modified after report approve.");
            return rspResult;
        }
        UserInfo localUser = UserHelper.getLocalUser();
        GeneralOrderInstanceInfoPO order = this.orderMapper.getOrderInfo(reqObject.getOrderNo());
        if (order == null) {
            rspResult.setMsg("Order do not exist!");
            return rspResult;
        }
        ConclusionMode conclusionMode = ConclusionMode.findCode(reqObject.getConclusionMode());
        ConclusionMode oldConclusionMode = ConclusionMode.findCode(order.getConclusionMode());
        boolean isUpdateConclusionMode = conclusionMode != oldConclusionMode;
        if (isUpdateConclusionMode && (oldConclusionMode == ConclusionMode.PP_MATRIX || conclusionMode == ConclusionMode.PP_MATRIX)){
            // 获取是否存在601数据
            ConclusionInfoPO conclusion = new ConclusionInfoPO();
            conclusion.setReportID(report.getID());
            conclusion.setConclusionLevelID(ConclusionType.Matrix.getCode());
            int conclusionCount = conclusionMapper.getConclusionLevelCount(conclusion);
            if (conclusionCount > 0){
                rspResult.setMsg("已录入Conclusion数据，不能切换PP Matrix模式.");
                return rspResult;
            }
        }
        Map<String, Long> ppInstanceIds = Maps.newHashMap();
        Map<String, String> ppBaseIds = Maps.newHashMap();
        boolean isPpMatrix = conclusionMode == ConclusionMode.PP_MATRIX;
        if (isPpMatrix){
            List<RequirmentPPInfo> pps = ppTestLineRelMapper.getPPBaseIdList(order.getID());
            pps.forEach(pp->{
                if (StringUtils.isNotBlank(pp.getPpInstanceId())){
                    ppInstanceIds.put(pp.getPpInstanceId(), pp.getPpBaseId());
                }
                ppBaseIds.put(NumberUtil.toLong(pp.getPpBaseId()).toString(), pp.getPpInstanceId());
            });
        }
        Map<String, LimitGroupSampleReq> sampleMaps = Maps.newHashMap();
        Map<String, Set<LimitGroupSampleInfo>> ppSampleLimitGroups = Maps.newHashMap();
        List<LimitGroupSampleReq> samples = reqObject.getSamples();

        Map<String, Set<String>> ppMatrixMaps = Maps.newHashMap();
        List<PPSectionSampleTestLineInfo> ppMatrixs = ppTestLineRelMapper.getPPSectionSampleTLByReportId(report.getID());
        ppMatrixs.forEach(ppMatrix->{
            if (!ppMatrixMaps.containsKey(ppMatrix.getMatrixId())){
                ppMatrixMaps.put(ppMatrix.getMatrixId(), Sets.newHashSet());
            }
            ppMatrixMaps.get(ppMatrix.getMatrixId()).add(ppMatrix.getPpId());
        });

        List<LimitGroupInstancePO> limitGroups = Lists.newArrayList();
        List<LimitGroupItemReq> limitGroupItems;
        LimitGroupSampleInfo limitGroupSample;
        // 这个循环就是找到当前Sample选择的limitGroup...写的真麻烦。。。。。。
        for (LimitGroupSampleReq sample : samples) {
            sampleMaps.put(sample.getSampleId(), sample);

            limitGroupItems = sample.getLimitGroups();
            if (limitGroupItems == null || limitGroupItems.isEmpty()){
                continue;
            }
            ConclusionMode conMode = ConclusionMode.findCode(sample.getConclusionMode());

            if (!ppSampleLimitGroups.containsKey(sample.getSampleId())){
                ppSampleLimitGroups.put(sample.getSampleId(), Sets.newHashSet());
            }

            for (LimitGroupItemReq limitGroupItem: limitGroupItems) {
                LimitGroupInstancePO limitGroup = this.getLimitGroupInfo(limitGroupItem);
                if (limitGroup == null){
                    continue;
                }
                // PP Id-》LimitGroupIds
                limitGroupSample = new LimitGroupSampleInfo();
                limitGroupSample.setRowId(sample.getRowId());
                limitGroupSample.setPpId(sample.getPpId());
                limitGroupSample.setLimitGroupId(limitGroupItem.getLimitGroupId());
                // DIG-5711 只有PP Matrix 模式才会使用该值
                /*if (isPpMatrix){
                    limitGroupSample.setLimitGroupInstanceId(limitGroup.getID());
                }*/
                limitGroupSample.setLimitGroupInstanceId(limitGroup.getID());
                ppSampleLimitGroups.get(sample.getSampleId()).add(limitGroupSample);

                limitGroup.setTestSampleID(sample.getSampleId());
                limitGroup.setConclusionMode(conMode.getId());

                String ppId = sample.getPpId();
                if (!isPpMatrix || StringUtils.isBlank(ppId)){
                    limitGroups.add(limitGroup);
                    continue;
                }
                if (ppBaseIds.containsKey(ppId)){
                    limitGroup.setPpId(ppBaseIds.get(ppId));
                    limitGroup.setPpBaseId(NumberUtil.toLong(ppId));
                }
                if (ppInstanceIds.containsKey(ppId)){
                    limitGroup.setPpBaseId(ppInstanceIds.get(ppId));
                }

                limitGroups.add(limitGroup);
            }
        }
        List<LimitGroupTestLineReq> testLineMatrixs = reqObject.getTestLineMatrixs();
        List<ProductAttributeInstanceInfoPO> attributes = Lists.newArrayList();
        // SampleId+TestLineInstanceId
        Map<String, LimitGroupMatrixInfo> limitGroupMatrixMaps = Maps.newHashMap();

        LimitGroupMatrixInfo limitGroupMatrix;
        ProductAttributeInstanceInfoPO attribute;
        for (LimitGroupTestLineReq testLine : testLineMatrixs) {
            String testLineMatrixKey = String.format("%s_%s", testLine.getSampleId(), testLine.getTestLineInstanceId());
            if (!limitGroupMatrixMaps.containsKey(testLineMatrixKey)){
                Set<LimitGroupSampleInfo> limitGroupSamples = ppSampleLimitGroups.get(testLine.getSampleId());
                // Select Limit Group By Sample  继承使用
                if (!isPpMatrix && (limitGroupSamples == null || limitGroupSamples.isEmpty())){
                    limitGroupSamples = this.findParentSampleLimitGroup(sampleMaps, ppSampleLimitGroups, sampleMaps.get(testLine.getSampleId()));
                }
                if (limitGroupSamples == null || limitGroupSamples.isEmpty()){
                    continue;
                }
                Set<Integer> limitGroupIds = Sets.newHashSet();
                /**
                 * 1、如果是以Matrix模式：limitGroupSamples.size() > 0
                 * 2、如果是以PP Matrix模式：limitGroupSamples.size() = 1
                 */
                limitGroupMatrix = new LimitGroupMatrixInfo();
                for (LimitGroupSampleInfo oldLimitGroup: limitGroupSamples) {
                    Integer limitGroupId = oldLimitGroup.getLimitGroupId();
                    if (limitGroupId == null || limitGroupId.intValue() <= 0){
                        continue;
                    }
                    limitGroupIds.add(limitGroupId);
                    LimitGroupSampleInfo newLimitGroup = new LimitGroupSampleInfo();
                    BeanUtils.copyProperties(oldLimitGroup, newLimitGroup);
                    if (isPpMatrix && ppMatrixMaps.containsKey(testLine.getTestMatrixId())){
                        Set<String> ppIds = ppMatrixMaps.get(testLine.getTestMatrixId());
                        if (ppIds == null || ppIds.isEmpty() || !ppIds.contains(newLimitGroup.getPpId())){
                            continue;
                        }
                    }
                    newLimitGroup.setProductAttrIds(Sets.newHashSet());
                    limitGroupMatrix.getLimitGroups().add(newLimitGroup);
                }
                if (limitGroupIds == null || limitGroupIds.isEmpty()){
                    continue;
                }
                limitGroupMatrix.setTestMatrixId(testLine.getTestMatrixId());
                limitGroupMatrix.setTestSampleId(testLine.getSampleId());
                limitGroupMatrix.setTestLineInstanceId(testLine.getTestLineInstanceId());
                limitGroupMatrix.setTestLineVersionId(testLine.getTestLineVersionId());
                Integer standardVersionId = testLine.getStandardVersionId();
                if (standardVersionId == null){
                    standardVersionId = 0;
                }
                limitGroupMatrix.setStandardVersionId(standardVersionId);
                limitGroupMatrixMaps.put(testLineMatrixKey, limitGroupMatrix);
            }
            limitGroupMatrix = limitGroupMatrixMaps.get(testLineMatrixKey);

            List<LimitGroupProductAttrReq> productAttrs = testLine.getProductAttrs();
            if (productAttrs == null || productAttrs.isEmpty()){
                continue;
            }
            Set<LimitGroupSampleInfo> limitGroupSamples = limitGroupMatrix.getLimitGroups();
            LimitGroupSampleInfo limitGroupItem;
            for (LimitGroupProductAttrReq productAttr: productAttrs){
                // LimitGroupId、ProductAttrIds
                limitGroupItem = limitGroupSamples.stream().filter(lg -> {
                    if (isPpMatrix){
                        return StringUtil.equalsIgnoreCase(lg.getRowId(), productAttr.getRowId()) &&
                        StringUtil.equalsIgnoreCase(lg.getPpId(), productAttr.getPpId()) &&
                        StringUtil.equals(lg.getLimitGroupId(), productAttr.getLimitGroupId());
                    }
                    return StringUtil.equals(lg.getLimitGroupId(), productAttr.getLimitGroupId());
                }).findFirst().orElse(null);
                if (limitGroupItem == null){
                    // TODO
                    continue;
                }
                limitGroupItem.getProductAttrIds().add(productAttr.getProductAttrId());

                attribute = new ProductAttributeInstanceInfoPO();
                attribute.setID(UUID.randomUUID().toString());
                attribute.setOrderNo(order.getOrderNo());
                attribute.setProductAttributeID(productAttr.getProductAttrId());
                attribute.setProductAttributeValue(productAttr.getProductAttrValue());
                attribute.setTestSampleID(testLine.getSampleId());
                attribute.setTestLineVersionID(testLine.getTestLineVersionId());
                attribute.setTestMatrixID(testLine.getTestMatrixId());
                // 只有PP Matrix 模式才有该值
                attribute.setLimitGroupId(limitGroupItem.getLimitGroupInstanceId());
                attribute.setActiveIndicator(true);
                attribute.setCreatedBy(localUser.getRegionAccount());
                attribute.setCreatedDate(DateUtils.getNow());
                attribute.setModifiedBy(localUser.getRegionAccount());
                attribute.setModifiedDate(DateUtils.getNow());

                attributes.add(attribute);
            }
        }
        RequirmentLimitInfo rspObject = this.getRequirmentLimitInfo(order.getID(), ppInstanceIds, ppBaseIds, testLineMatrixs, limitGroupMatrixMaps);

        rspResult.setSuccess(transactionTemplate.execute((trans)->{
            //
            this.limitGroupMapper.delLimitGroupByOrderNo(order.getOrderNo());
            if (!limitGroups.isEmpty()) {
                limitGroupMapper.batchInsert(limitGroups);
            }
            /* Matrix Save */
            this.productAttrMapper.delProductAttr(order.getOrderNo());

            if (!attributes.isEmpty()) {
                this.productAttrMapper.batchInsert(attributes);
            }

            if (rspObject.isDelLimit()){
                this.limitMapper.delLimitInfoByOrderId(order.getID());
            }
            // Analyte
            testMatrixService.saveAnalyte(order.getID(), rspObject.getAnalytes(), rspObject.getAnalyteLanguageMaps(), 2);

            List<LimitInstancePO> limits = rspObject.getLimits();
            if (limits != null && !limits.isEmpty()) {
                this.limitMapper.batchInsert(limits);
            }

            //
            if (rspObject.isDelLimitLang()){
                this.limitLanguageMapper.delLimitLanguageInfo(order.getID());
            }
            List<String> delLimitLangIds = rspObject.getDelLimitLangIds();
            if (delLimitLangIds != null && !delLimitLangIds.isEmpty()) {
                this.limitLanguageMapper.delLimitLanguageIds(delLimitLangIds);
            }

            List<LimitMultipleLanguageInfoPO> limitLangs = rspObject.getLimitLangs();
            if (limitLangs != null && !limitLangs.isEmpty()) {
                this.limitLanguageMapper.batchInsert(limitLangs);
            }

            List<String> delLimitIds = rspObject.getDelLimitIds();
            if (delLimitIds != null && !delLimitIds.isEmpty()) {
                this.limitMapper.batchDelete(delLimitIds);
            }
            if (isUpdateConclusionMode){
                order.setConclusionMode(conclusionMode.getId());
                orderMapper.updateOrderConclusionMode(order);
            }
            return true;
        }));
        return rspResult;
    }

    /**
     *
     * @param limitGroupItem
     * @return
     */
    private LimitGroupInstancePO getLimitGroupInfo(LimitGroupItemReq limitGroupItem){
        if (limitGroupItem == null){
            return null;
        }
        UserInfo localUser = UserHelper.getLocalUser();
        LimitGroupInstancePO limitGroup = new LimitGroupInstancePO();
        limitGroup.setID(UUID.randomUUID().toString());
        //limitGroup.setTestSampleID(sampleId);
        limitGroup.setLimitGroupID(limitGroupItem.getLimitGroupId());
        limitGroup.setLimitGroupName(limitGroupItem.getLimitGroupName());
        limitGroup.setTestLimitGroupTypeID(limitGroupItem.getLimitGroupTypeId());
        limitGroup.setTestLimitGroupTypeName(limitGroupItem.getLimitGroupTypeName());
        limitGroup.setActiveIndicator(true);
        limitGroup.setCreatedBy(localUser.getRegionAccount());
        limitGroup.setCreatedDate(DateUtils.getNow());
        limitGroup.setModifiedBy(localUser.getRegionAccount());
        limitGroup.setModifiedDate(DateUtils.getNow());
        return limitGroup;
    }

    /**
     *
     * @param sampleMaps
     * @param ppSampleLimitGroups
     * @param samplePO
     * @return
     */
    private Set<LimitGroupSampleInfo> findParentSampleLimitGroup(Map<String, LimitGroupSampleReq> sampleMaps, Map<String, Set<LimitGroupSampleInfo>> ppSampleLimitGroups, LimitGroupSampleReq samplePO){
        if (samplePO == null){
            return Sets.newHashSet();
        }
        SampleType sampleType = SampleType.findType(samplePO.getSampleType());
        if (sampleType == null){
            return Sets.newHashSet();
        }
        String sampleId = sampleType == SampleType.OriginalSample ? samplePO.getSampleId() : samplePO.getSampleParentId();
        if (sampleType == SampleType.OriginalSample || ppSampleLimitGroups.containsKey(sampleId)){
            return ppSampleLimitGroups.get(sampleId);
        }
        return findParentSampleLimitGroup(sampleMaps, ppSampleLimitGroups, sampleMaps.get(samplePO.getSampleParentId()));
    }

    /**
     *
     * @param orderId
     * @param ppInstanceIds
     * @param testLineMatrixs
     * @param limitGroupMatrixMaps
     * @return
     */
    private RequirmentLimitInfo getRequirmentLimitInfo(
            String orderId,
            Map<String, Long> ppInstanceIds,
            Map<String, String> ppBaseIds,
            List<LimitGroupTestLineReq> testLineMatrixs,
            Map<String, LimitGroupMatrixInfo> limitGroupMatrixMaps){
        List<LimitInstanceInfo> limits = Lists.newArrayList();

        RequirmentLimitInfo rspObject = new RequirmentLimitInfo();
        if (limitGroupMatrixMaps.isEmpty()){
            return rspObject;
        }

        Map<String, Set<Integer>> testConditionIds = Maps.newHashMap();
        // 获取Condition信息
        List<TestConditionInfoPO> testConditions = this.testConditionMapper.getTestConditionListByOrderId(orderId);
        for (TestConditionInfoPO testCondition : testConditions) {
            Boolean isConditionTypeBlock = testCondition.getIsConditionTypeBlock();
            if (isConditionTypeBlock == null || !isConditionTypeBlock.booleanValue()){
                continue;
            }
            String testLineMatrixKey = String.format("%s_%s", testCondition.getTestSampleID(), testCondition.getTestLineInstanceID());
            if (!testConditionIds.containsKey(testLineMatrixKey)) {
                testConditionIds.put(testLineMatrixKey, Sets.newHashSet());
            }
            testConditionIds.get(testLineMatrixKey).add(testCondition.getTestConditionID());
        }

        Set<Integer> testLimitGroupIds = Sets.newHashSet();

        /**
         * PA
         *  RowId+PPId+LimitGroupId
         */
        Set<TestAnalyteLimitReq> testAnalyteLimits = Sets.newHashSet();
        TestAnalyteLimitReq paramLimit;
        for (Map.Entry<String, LimitGroupMatrixInfo> entry: limitGroupMatrixMaps.entrySet()){
            LimitGroupMatrixInfo reqLimit = entry.getValue();
            Set<LimitGroupSampleInfo> limitGroups = reqLimit.getLimitGroups();
            if (limitGroups == null || limitGroups.isEmpty()){
                continue;
            }
            Set<Integer> conditionIds = testConditionIds.get(entry.getKey());
            for (LimitGroupSampleInfo limitGroup: limitGroups) {
                int limitGroupId = limitGroup.getLimitGroupId();
                Set<Integer> productAttrIds = limitGroup.getProductAttrIds();
                if (productAttrIds == null || productAttrIds.isEmpty()){
                    productAttrIds = Sets.newHashSet(0);
                }
                testLimitGroupIds.add(limitGroupId);
                for (Integer productAttrId: productAttrIds){
                    paramLimit = new TestAnalyteLimitReq();
                    paramLimit.setTestLimitGroupId(limitGroupId);
                    paramLimit.setTlVersionIdentifier(reqLimit.getTestLineVersionId());
                    //reqLimit.setTsVersionIdentifier(reqParams.getStandardVersionId());
                    paramLimit.setTestConditionIds(conditionIds);

                    if (productAttrId != null && productAttrId.intValue() > 0){
                        paramLimit.setProductAttributeId(productAttrId);
                    }
                    paramLimit.setTestConditionIds(conditionIds);
                    testAnalyteLimits.add(paramLimit);
                }
            }
        }
        if (testAnalyteLimits.isEmpty()){
            return rspObject;
        }

        CustomResult<List<TestAnalyteLimitRsp>> rspResult = this.getTestAnalyteLimitInfoList(testAnalyteLimits, TrimsShuntLabHelper.isShuntLab());
        if (!rspResult.isSuccess()){
            return rspObject;
        }
        List<TestAnalyteLimitRsp> rspLimits = rspResult.getData();
        if (rspLimits == null || rspLimits.isEmpty()){
            return rspObject;
        }
        List<AnalyteInfoPO> analytes = Lists.newArrayList();
        Map<String, List<TestAnalyteLanguage>> analyteLanguageMaps = Maps.newHashMap();
        TestMatrixPO testMatrix;
        for(LimitGroupTestLineReq testLineMatrix : testLineMatrixs) {
            String testLineMatrixKey = String.format("%s_%s", testLineMatrix.getSampleId(), testLineMatrix.getTestLineInstanceId());
            LimitGroupMatrixInfo limitGroupMatrix = limitGroupMatrixMaps.get(testLineMatrixKey);
            if (limitGroupMatrix == null){
                continue;
            }

            Set<LimitGroupSampleInfo> limitGroups = limitGroupMatrix.getLimitGroups();
            if (limitGroups == null || limitGroups.isEmpty()){
                continue;
            }
            Set<Integer> conditionIds = testConditionIds.get(testLineMatrixKey);
            if (conditionIds == null){
                conditionIds = Sets.newHashSet();
            }
            for (LimitGroupSampleInfo limitGroup: limitGroups) {

                int limitGroupId = limitGroup.getLimitGroupId();
                List<TestAnalyteLimitRsp> analyteLimits = ListHelper.filter(rspLimits,
                        limit -> NumberUtil.equals(limit.getTestLimitGroupId(), limitGroupId) &&
                                NumberUtil.equals(limit.getTlVersionIdentifier(), limitGroupMatrix.getTestLineVersionId())
                );
                if (analyteLimits == null || analyteLimits.isEmpty()){
                    continue;
                }
                String ppId = limitGroup.getPpId();
                if (StringUtils.isBlank(ppId)){
                    ppId = "";
                }
                Set<Integer> productAttrIds = limitGroup.getProductAttrIds();
                if (productAttrIds == null){
                    productAttrIds = Sets.newHashSet();
                }
                testMatrix = new TestMatrixPO();
                testMatrix.setGeneralOrderInstanceID(orderId);
                testMatrix.setID(testLineMatrix.getTestMatrixId());
                testMatrix.setTestSampleID(testLineMatrix.getSampleId());
                testMatrix.setTestLineInstanceID(testLineMatrix.getTestLineInstanceId());
                limits.addAll(this.convertAnalyteLimitInfo(ppId, analytes, analyteLanguageMaps, testMatrix, limitGroupId, analyteLimits, productAttrIds, conditionIds));
            }
        }
        rspObject.setAnalytes(analytes);
        rspObject.setAnalyteLanguageMaps(analyteLanguageMaps);

        if (limits.isEmpty()){
            return rspObject;
        }
        List<String> delLimitIds = Lists.newArrayList();
        List<LimitInstancePO> oldLimits = this.limitMapper.getLimitListByOrderId(orderId);
        for (LimitInstancePO oldLimit: oldLimits){
            LimitInstancePO limitInstanceDO = limits.stream().filter(limit -> limit.hashCode() == oldLimit.hashCode()).findFirst().orElse(null);
            if (limitInstanceDO == null){
                delLimitIds.add(oldLimit.getID());
                continue;
            }
            limitInstanceDO.setID(oldLimit.getID());
        }

        // 构建多语言对象
        this.structureLimitMultipleLanguageInfo(orderId, rspObject, limits);

        rspObject.setDelLimit(false);
        List<LimitInstancePO> newlimits = Lists.newArrayList(limits);
        rspObject.setLimits(newlimits);
        rspObject.setDelLimitIds(delLimitIds);

        newlimits.forEach(limit->{
            String ppId = limit.getPPId();
            if (StringUtils.isBlank(ppId)){
                ppId = "0";
            }
            if (ppBaseIds.containsKey(ppId)){
                limit.setPPId(ppBaseIds.get(ppId));
                limit.setPpBaseId(NumberUtil.toLong(ppId));
                return;
            }
            if (ppInstanceIds.containsKey(ppId)){
                limit.setPpBaseId(ppInstanceIds.get(ppId));
            }
        });
        return rspObject;
    }

    /**
     *
     * @param testAnalyteLimits
     * @param isTrimsLocal
     * @return
     */
    public CustomResult getTestAnalyteLimitInfoList(Set<TestAnalyteLimitReq> testAnalyteLimits, boolean isTrimsLocal){
        if (isTrimsLocal){
            return this.getTestAnalyteLimitInfoList(testAnalyteLimits);
        }
        return trimsClient.getTestAnalyteLimit(testAnalyteLimits);
    }

    /**
     * 1：ProductAttributeId
     *   当传了ProductAttributeId时，则精确匹配，如果为空或零，则取零的数据
     * 2：ConditionId
     *   当传了ConditionIds时，如果找不到则取空的，即没有设置的ConditionId
     * 3：TestItemVersionId
     *   精确匹配
     * 4：StandardVersionId、
     * 5：RegulationVersionId
     * 6：Units
     *   当有StandardVersionId时，如果找不到则获取所有
     *
     * @param tals
     * @return
     */
    private CustomResult getTestAnalyteLimitInfoList(Set<TestAnalyteLimitReq> tals){
        CustomResult rspResult = new CustomResult(false);
        if (tals == null || tals.isEmpty()){
            return rspResult;
        }
        /*String jsonStr = JSON.toJSONString(talItems);*/
        ConcurrentLinkedQueue<TestAnalyteLimitRsp> testAnalyteLimits = new ConcurrentLinkedQueue();
        final CountDownLatch latch = new CountDownLatch(tals.size());

        for (TestAnalyteLimitReq tal: tals) {
            // TODO Test
            /*this.getTestAnalyteLimitInfoList(testAnalyteLimits, tal);*/
            trimsTaskExecutor.submitListenable(()-> this.getTestAnalyteLimitInfoList(testAnalyteLimits, tal))
            .addCallback(new ListenableFutureCallback<Boolean>() {
                @Override
                public void onSuccess(Boolean isSuccess) {
                    latch.countDown();
                }
                @Override
                public void onFailure(Throwable t) {
                    // TODO Auto-generated method stub
                    latch.countDown();
                }
            });
        }
        try {
            // 设定超时时间单位：毫秒
            latch.await(30000, TimeUnit.MILLISECONDS);
        } catch (InterruptedException ie) {
            logger.error("RequirmentService.getTestAnalyteLimitInfoList({}) Error：", ie);
            rspResult.setSuccess(false);
            rspResult.setMsg("Get Trims Test Analyte Limit Info Time out.");
            return rspResult;
        }
        List<TestAnalyteLimitRsp> analyteLimits = Lists.newArrayList(testAnalyteLimits);
        analyteLimits.sort(new TestAnalyteLimitComparator(true));

        rspResult.setData(analyteLimits);
        rspResult.setSuccess(true);

        return rspResult;
    }

    /**
     *
     * @param testAnalyteLimits
     * @param talItem
     */
    private boolean getTestAnalyteLimitInfoList(ConcurrentLinkedQueue<TestAnalyteLimitRsp> testAnalyteLimits, TestAnalyteLimitReq talItem){
        // TODO 多线程处理
        List<TalInfo> tals = testAnalyteLimitBaseInfoExtMapper.getTestAnalyteLimitInfoList(talItem);
        if (tals == null || tals.isEmpty()){
            return false;
        }
        int productAttrId = NumberUtil.toInt(talItem.getProductAttributeId());
        Set<Integer> testConditionIds = talItem.getTestConditionIds();
        TestAnalyteLimitRsp testAnalyteLimit;

        for (TalInfo tal: tals) {
            /*if (NumberUtil.equals(tal.getTalVersionId(), 160404)){
                int talVersionId = tal.getTalVersionId();
            }*/
            Map<Integer, TestConditionRsp> testConditionMaps = Maps.newHashMap();
            List<Integer> talApplicabilityIds = tal.getTalApplicabilityIds();
            if (talApplicabilityIds != null && !talApplicabilityIds.isEmpty()){
                // region TalApplicabilitys
                List<TalApplicabilityInfo> talApplicabilitys = tal.getTalApplicabilitys();
                if (talApplicabilitys == null || talApplicabilitys.isEmpty()) {
                    continue;
                }
                int talApplicabilityId = 0;
                boolean isEmptyConditionIds = false;

                for (TalApplicabilityInfo talApplicability: talApplicabilitys) {
                    if (!talApplicabilityIds.contains(talApplicability.getTalApplicabilityId())){
                        continue;
                    }
                    /*testConditions = Lists.newArrayList();*/

                    Set<Integer> productAttrIds = talApplicability.getProductAttrIds();
                    if (productAttrIds != null && productAttrIds.size() > 1) {
                        productAttrIds.remove(0);
                    }
                    // 当传了ProductAttributeId时，则精确匹配，如果为空或零，则取零的数据
                    if (productAttrIds == null || !(productAttrId <= 0 && productAttrIds.contains(0)) && !(productAttrId > 0 && (productAttrIds.contains(productAttrId) || productAttrIds.contains(0)))) {
                        continue;
                    }
                    Set<Integer> talTestConditionIds = talApplicability.getTestConditionIds();
                    if (talTestConditionIds == null) {
                        talTestConditionIds = Sets.newHashSet();
                    }
                    if (talTestConditionIds.size() > 1) {
                        talTestConditionIds.remove(0);
                    }
                    // 如果有传TestConditionIds时，则要找到对应的TestConditionIds，否则要找到TalApplicabilityId 组下为空的TestConditionIds
                    if (testConditionIds == null || testConditionIds.isEmpty()) {
                        // 如果没有传TestConditionIds，可有返回TestConditionIds，则要判断有没有返回空的TestConditionIds，即为零（0）
                        if (!talTestConditionIds.contains(0)) {
                            continue;
                        }
                    } else {
                        TestConditionRsp testCondition;
                        // 如果有传TestConditionIds，可数据里没有TalTestConditionIds，则要取空(即：0)的TestConditionIds
                        for (Integer testConditionId : talTestConditionIds) {
                            if (!testConditionIds.contains(testConditionId) || testConditionMaps.containsKey(testConditionId)) {
                                continue;
                            }
                            testCondition = new TestConditionRsp();
                            testCondition.setTestConditionId(testConditionId);
                            testConditionMaps.put(testConditionId, testCondition);
                        }
                        if (testConditionMaps.isEmpty()) {
                            if (talTestConditionIds.contains(0)) {
                                isEmptyConditionIds = true;
                            }
                            continue;
                        }
                    }
                    talApplicabilityId = talApplicability.getTalApplicabilityId();
                    /*break;*/
                }
                if (!isEmptyConditionIds && talApplicabilityId <= 0) {
                    continue;
                }
                // endregion
            }

            TestLimitOpreatorRsp testLimitOpreator = tal.getTestLimitOpreator();
            if (testLimitOpreator == null) {
                logger.info("Tal_TestLimitOpreator_{}, 为空.", tal.getTalVersionId());
                continue;
            }
            TestAnalyteRsp testAnalyte = tal.getTestAnalyte();
            if (testAnalyte == null) {
                logger.info("Tal_TestAnalyte_{}, 为空.", tal.getTalVersionId());
                continue;
            }
            int testAnalyteLanguageId = NumberUtil.toInt(testAnalyte.getTestAnalyteLanguageId());
            if (testAnalyteLanguageId > 0) {
                List<TestAnalyteLanguage> testAnalyteLangs = Lists.newArrayList();
                TestAnalyteLanguage testAnalyteLang = new TestAnalyteLanguage();
                testAnalyteLang.setLanguageId(testAnalyteLanguageId);
                testAnalyteLang.setMultiTestAnalyteDesc(testAnalyte.getMultiTestAnalyteDesc());
                testAnalyteLangs.add(testAnalyteLang);
                testAnalyte.setOtherLanguageItems(testAnalyteLangs);
            }

            ReportUnitInfo reportUnit = tal.getUnit();
            if (reportUnit == null) {
                logger.info("Tal_Unit_{}, 为空.", tal.getTalVersionId());
                continue;
            }
            //add by vincent 2021年3月4日
            testAnalyte.setReportUnitId(reportUnit.getUnitBaseId());

            testAnalyteLimit = new TestAnalyteLimitRsp();
            testAnalyteLimit.setTalBaseId(tal.getTalBaseId());
            testAnalyteLimit.setTlVersionIdentifier(tal.getTestLineVersionId());
            testAnalyteLimit.setVersionIdentifier(tal.getTalVersionId());
            testAnalyteLimit.setTestLimitGroupId(tal.getTestLimitGroupId());
            // TODO TsVersionIdentifier
            /*testAnalyteLimit.setTsVersionIdentifier(1);*/
            testAnalyteLimit.setProductAttributeId(productAttrId);
            testAnalyteLimit.setTalValue1(tal.getTalValue1());
            testAnalyteLimit.setTalValue2(tal.getTalValue2());
            testAnalyteLimit.setTalReportDescription(tal.getTalReportDescription());
            testAnalyteLimit.setSequence(tal.getSequence());

            testAnalyteLimit.setTestLimitOpreator(testLimitOpreator);
            testAnalyteLimit.setTestAnalyte(testAnalyte);
            testAnalyteLimit.setTestConditionItems(Lists.newArrayList(testConditionMaps.values()));

            // Report Unit
            List<MutipleUnitRsp> mutipleUnits = Lists.newArrayList();
            MutipleUnitRsp mutipleUnit = new MutipleUnitRsp();
            mutipleUnit.setSequence(NumberUtil.toInt(reportUnit.getSeq()));

            UnitForReportingRsp unit = new UnitForReportingRsp();
            unit.setUnitBaseId(reportUnit.getUnitBaseId());
            unit.setUnitId(reportUnit.getUnitId());
            unit.setUnitDepiction(reportUnit.getUnitDepiction());
            unit.setUnitShortDepiction(reportUnit.getUnitShortDepiction());
            unit.setOtherLanguageItems(reportUnit.getOtherLanguageItems());

            mutipleUnit.setUnitForReporting(unit);
            mutipleUnits.add(mutipleUnit);
            // TODO
            testAnalyteLimit.setMutipleUnits(mutipleUnits);

            testAnalyteLimit.setOtherLanguageItems(tal.getOtherLanguageItems());

            testAnalyteLimits.add(testAnalyteLimit);
        }
        return true;
    }

    /**
     *
     * @param ppId
     * @param analytes
     * @param analyteLanguageMaps
     * @param matrix
     * @param limitGroupId
     * @param analyteLimits
     * @param productAttrIds
     * @param conditionIds
     * @return
     */
    private List<LimitInstanceInfo> convertAnalyteLimitInfo(String ppId, List<AnalyteInfoPO> analytes, Map<String, List<TestAnalyteLanguage>> analyteLanguageMaps, TestMatrixPO matrix, Integer limitGroupId, List<TestAnalyteLimitRsp> analyteLimits, Set<Integer> productAttrIds, Set<Integer> conditionIds) {
        List<LimitInstanceInfo> limits = Lists.newArrayList();
        Set<String> analyteLimitKeys = Sets.newHashSet();
        for (TestAnalyteLimitRsp analyteLimit: analyteLimits){
            TestLimitOpreatorRsp testLimitOpreator = analyteLimit.getTestLimitOpreator();
            // TODO TestLimitOperatorName 空是否过滤
            if (testLimitOpreator == null || StringUtils.isBlank(testLimitOpreator.getTestLimitOperatorName()) || testLimitOpreator.getTestLimitOperatorName().equals("No Limit")){
                continue;
            }
            TestAnalyteRsp testAnalyte = analyteLimit.getTestAnalyte();
            // TODO
            //if (testAnalyte == null || StringUtils.isBlank(analyteLimit.getTestLimitGroupName()) || !limitGroupNames.contains(analyteLimit.getTestLimitGroupName())){
            if (testAnalyte == null){
                continue;
            }
            // 如果PA为空，productAttrIds.isEmpty() &&
            int productAttrId = analyteLimit.getProductAttributeId();
            if (productAttrId <= 0){
                if (!productAttrIds.isEmpty()){
                    continue;
                }
            }
            if (productAttrId > 0){
                if (!productAttrIds.contains(productAttrId)){
                    continue;
                }
            }
            String analyteLimitKey = String.format("%s_%s_%s", limitGroupId , testAnalyte.getTestAnalyteId(), analyteLimit.getVersionIdentifier());

            testAnalyte.setReportUnit(LOStringUtil.delHTMLTag(analyteLimit.getReportUnit()));

            // TODO 传了ConditionIds，有可能返回部分、所有、都没有三种情况
            // 是否在考虑会返回多条Condition
            List<TestConditionRsp> testConditionItems = analyteLimit.getTestConditionItems();
            if (testConditionItems == null || testConditionItems.isEmpty()){
                testConditionItems.add(new TestConditionRsp());
            }
            int oldLimitSize = limits.size();
            for (TestConditionRsp testCondition: testConditionItems){
                int testConditionId = testCondition.getTestConditionId();
                if (conditionIds.isEmpty()){
                    if (testConditionId <= 0){
                        this.convertLimitInfo(limits, analyteLimitKeys, matrix, analyteLimit, analyteLimitKey, ppId, null);
                    }
                    continue;
                }
                if (conditionIds.contains(testConditionId)){
                    this.convertLimitInfo(limits, analyteLimitKeys, matrix, analyteLimit, analyteLimitKey, ppId, testConditionId);
                    continue;
                }
                // TODO testConditionId 未找到时，需要找到空的TestConditionItems
                if (testConditionId <= 0){
                    this.convertLimitInfo(limits, analyteLimitKeys, matrix, analyteLimit, analyteLimitKey, ppId, null);
                }
            }
            if (oldLimitSize != limits.size()){
                this.covertAnalyteInfo(analytes, analyteLanguageMaps, matrix, analyteLimit, testAnalyte);
            }
        }
        return limits;
    }

    /**
     *
     * @param limits
     * @param analyteLimitKeys
     * @param matrix
     * @param analyteLimit
     * @param analyteLimitKey
     * @param ppId
     * @param testConditionId
     * @return
     */
    private void convertLimitInfo(List<LimitInstanceInfo> limits, Set<String> analyteLimitKeys, TestMatrixPO matrix, TestAnalyteLimitRsp analyteLimit, String analyteLimitKey, String ppId, Integer testConditionId){
        analyteLimitKey = String.format("%s_%s", analyteLimitKey, NumberUtil.toInt(testConditionId));
        if (analyteLimitKeys.contains(analyteLimitKey)){
            return;
        }
        analyteLimitKeys.add(analyteLimitKey);

        UserInfo localUser = UserHelper.getLocalUser();
        LimitInstanceInfo limit = new LimitInstanceInfo();
        limit.setID(UUID.randomUUID().toString());
        limit.setOrderId(matrix.getGeneralOrderInstanceID());
        limit.setTestSampleID(matrix.getTestSampleID());
        limit.setTestLineInstanceID(matrix.getTestLineInstanceID());
        limit.setTestMatrixID(matrix.getID());

        TestAnalyteRsp testAnalyte = analyteLimit.getTestAnalyte();
        if (testAnalyte != null){
            limit.setAnalyteID(testAnalyte.getTestAnalyteId());
            limit.setReportUnit(testAnalyte.getReportUnit());
        }
        limit.setTestConditionID(testConditionId);
        limit.setTalBaseId(NumberUtil.toLong(analyteLimit.getTalBaseId()));

        /*limit.setAnalyteLimitVersionID(analyteLimit.getVersionIdentifier());
        TestLimitOpreatorRsp testLimitOpreator = analyteLimit.getTestLimitOpreator();
        if (testLimitOpreator != null){
            limit.setOperatorID(testLimitOpreator.getTestLimitOperatorId());
            limit.setOperatorName(testLimitOpreator.getTestLimitOperatorName());
        }*/
        // TODO PPId
        // TODO CreateBy
        limit.setSequence(analyteLimit.getSequence());

        limit.setValue1(analyteLimit.getTalValue1());
        limit.setValue2(analyteLimit.getTalValue2());
        limit.setReportDescription(LOStringUtil.delHTMLTag(analyteLimit.getTalReportDescription()));
        limit.setActiveIndicator(true);
        limit.setCreatedBy(localUser.getRegionAccount());
        limit.setCreatedDate(DateUtils.getNow());
        limit.setModifiedBy(localUser.getRegionAccount());
        limit.setModifiedDate(DateUtils.getNow());
        if (StringUtils.isNoneBlank(ppId)){
            limit.setPPId(ppId);
        }
        limit.setLimitLanguages(analyteLimit.getOtherLanguageItems());
        limit.setUnitForReportLanguages(analyteLimit.getUnitForReportLanguage());
        limits.add(limit);
    }

    /**
     *
     * @param analytes
     * @param analyteLanguageMaps
     * @param matrix
     * @param testAnalyte
     */
    private void covertAnalyteInfo(List<AnalyteInfoPO> analytes, Map<String, List<TestAnalyteLanguage>> analyteLanguageMaps, TestMatrixPO matrix, TestAnalyteLimitRsp analyteLimit, TestAnalyteRsp testAnalyte) {
        String analyteKey = String.format("%s_%s_%s", matrix.getTestLineInstanceID(), testAnalyte.getTestAnalyteId(), testAnalyte.getReportUnit());
        if (analyteLanguageMaps.containsKey(analyteKey)){
            return;
        }
        testMatrixService.setAnalyteReportUnitLanguage(analyteLanguageMaps, analyteKey, testAnalyte.getOtherLanguageItems(), analyteLimit.getUnitForReportLanguage());

        if (analytes.stream().filter(analyte->{
            return StringUtils.equalsIgnoreCase(analyte.getTestLineInstanceID(), matrix.getTestLineInstanceID()) &&
            StringUtil.equals(analyte.getAnalyteID(), testAnalyte.getTestAnalyteId()) &&
            StringUtils.equalsIgnoreCase(analyte.getReportUnit(), testAnalyte.getReportUnit());
        }).count() > 0){
            return;
        }
        UserInfo localUser = UserHelper.getLocalUser();

        List<MutipleUnitRsp> mutipleUnits = analyteLimit.getMutipleUnits();
        // DIG-5649 Save PA时获取Analyte的逻辑
        // DIG-5717 需求修改 去掉 CCL 和 分包的 TestLine
        TestLineInstancePO testLineInfoById = testLineMapper.getTestLineById(matrix.getTestLineInstanceID());
        Integer testLineID = testLineInfoById.getTestLineID();
        //拿到配置了样式的testLine
        TestLineDataEntryStyleInfoExample example=new TestLineDataEntryStyleInfoExample();
        example.createCriteria().andTestLineIDEqualTo(testLineID);
        List<TestLineDataEntryStyleInfoPO> testLineDataEntryStyles = testLineDataEntryStyleInfoMapper.selectByExample(example);
        if (CollectionUtils.isEmpty(testLineDataEntryStyles)){
            testLineDataEntryStyles=Lists.newArrayList();
        }
        Set<Integer>styleTrimsTestLineIDSet=testLineDataEntryStyles.stream()
                .filter(style -> style.getActiveIndicator() == true)
                .map(TestLineDataEntryStyleInfoPO::getTestLineID).collect(Collectors.toSet());
        // DIG-5722  CChemLab并且无样式  || 分包
        if ((CChemLab.equalsIgnoreCase(testLineInfoById.getProductLineAbbr()) && !styleTrimsTestLineIDSet.contains(testLineInfoById.getTestLineID()))
                || (TestLineType.check(testLineInfoById.getTestLineType(), TestLineType.SubContractOrder))) {
            return;
        }
        if(CollectionUtils.isNotEmpty(mutipleUnits)){
            for (MutipleUnitRsp mutipleUnit : mutipleUnits) {
                UnitForReportingRsp unitForReporting = mutipleUnit.getUnitForReporting();
                Long unitBaseId = unitForReporting.getUnitBaseId();
                AnalyteInfoPO analyte = new AnalyteInfoPO();
                analyte.setID(UUID.randomUUID().toString());
                analyte.setUnitBaseId(unitBaseId);
                analyte.setAnalyteBaseId(testAnalyte.getAnalyteBaseId());
                analyte.setGeneralOrderInstanceID(matrix.getGeneralOrderInstanceID());
                analyte.setTestLineInstanceID(matrix.getTestLineInstanceID());
                analyte.setAnalyteID(testAnalyte.getTestAnalyteId());
                analyte.setCasNo(testAnalyte.getTestAnalyteCasNumber());
                analyte.setReportUnit(testAnalyte.getReportUnit());
                analyte.setTestAnalyteName(LOStringUtil.delHTMLTag(testAnalyte.getTestAnalyteDesc()));
                analyte.setActiveIndicator(true);
                analyte.setCreatedBy(localUser.getRegionAccount());
                analyte.setCreatedDate(DateUtils.getNow());
                analyte.setModifiedBy(localUser.getRegionAccount());
                analyte.setModifiedDate(DateUtils.getNow());

                analytes.add(analyte);
            }

        }else{
            AnalyteInfoPO analyte = new AnalyteInfoPO();
            analyte.setID(UUID.randomUUID().toString());
            analyte.setAnalyteBaseId(testAnalyte.getAnalyteBaseId());
            analyte.setGeneralOrderInstanceID(matrix.getGeneralOrderInstanceID());
            analyte.setTestLineInstanceID(matrix.getTestLineInstanceID());
            analyte.setAnalyteID(testAnalyte.getTestAnalyteId());
            analyte.setCasNo(testAnalyte.getTestAnalyteCasNumber());
            analyte.setReportUnit(testAnalyte.getReportUnit());
            analyte.setTestAnalyteName(LOStringUtil.delHTMLTag(testAnalyte.getTestAnalyteDesc()));
            analyte.setActiveIndicator(true);
            analyte.setCreatedBy(localUser.getRegionAccount());
            analyte.setCreatedDate(DateUtils.getNow());
            analyte.setModifiedBy(localUser.getRegionAccount());
            analyte.setModifiedDate(DateUtils.getNow());

            analytes.add(analyte);
        }
    }

    /**
     *
     * @param samples
     * @return
     */
    private List<RequirmentSampleInfo> getRequirmentSampleList(List<RequirmentSampleInfo> samples){
        List<RequirmentSampleInfo> requirmentSamples = Lists.newArrayList();
        /*for (RequirmentSampleInfo sampleVO : samples) {
            requirmentSamples.add(sampleVO);
            List<RequirmentSampleInfo> childSamples = sampleVO.getChildList();
            if (childSamples == null || childSamples.isEmpty()){
                continue;
            }
            childSamples = getRequirmentSampleList(childSamples);
            if (childSamples != null && childSamples.size() > 0) {
                requirmentSamples.addAll(childSamples);
            }
        }*/
        return requirmentSamples;
    }

    /**
     *
     * @param orderId
     * @param rspObject
     * @param limits
     */
    private void structureLimitMultipleLanguageInfo(String orderId, RequirmentLimitInfo rspObject, List<LimitInstanceInfo> limits){
        if (limits == null || limits.isEmpty()){
            /*limitLanguageMapper.delLimitLanguageInfo(orderId);*/
            return;
        }
        Map<String, LimitMultipleLanguageInfoPO> limitLanguageMaps = Maps.newHashMap();
        LimitMultipleLanguageInfoPO limitLanguage;
        for (LimitInstanceInfo limit: limits){
            List<LimitLanguageRsp> limitLanguages = limit.getLimitLanguages();
            if (limitLanguages != null){
                for (LimitLanguageRsp language: limitLanguages){
                    Integer languageId = language.getLanguageId();
                    LanguageType languageType = LanguageType.findLanguageId(languageId);
                    if(languageType == null){
                        continue;
                    }
                    limitLanguage = this.getLimitLanguageInfo(limitLanguageMaps, limit.getID(), languageId);
                    limitLanguage.setReportDescription(LOStringUtil.delHTMLTag(language.getReportDescription()));
                }
            }
            List<UnitForReportingLanguage> unitForReportLanguages = limit.getUnitForReportLanguages();
            if (unitForReportLanguages == null || unitForReportLanguages.isEmpty()){
                continue;
            }
            for (UnitForReportingLanguage unitForReportLanguage: unitForReportLanguages){
                Integer languageId = unitForReportLanguage.getLanguageId();
                LanguageType languageType = LanguageType.findLanguageId(languageId);
                if(languageType == null){
                    continue;
                }
                limitLanguage = this.getLimitLanguageInfo(limitLanguageMaps, limit.getID(), languageId);
                limitLanguage.setReportUnit(LOStringUtil.delHTMLTag(unitForReportLanguage.getMultiUnitShortDepiction()));
            }
        }
        if (limitLanguageMaps.isEmpty()){
            /*limitLanguageMapper.delLimitLanguageInfo(orderId);*/
            return;
        }
        List<String> delLimitLanguageIds = Lists.newArrayList();
        List<LimitMultipleLanguageInfoPO> oldLimitLanguages = limitLanguageMapper.getLimitLanguageListByOrderId(orderId);
        for (LimitMultipleLanguageInfoPO oldLimitLanguage: oldLimitLanguages){
            String limitLanguageKey = String.format("%s_%s", oldLimitLanguage.getLimitInstanceID(), oldLimitLanguage.getLanguageId());
            LimitMultipleLanguageInfoPO language = limitLanguageMaps.get(limitLanguageKey);
            if (language == null){
                delLimitLanguageIds.add(oldLimitLanguage.getID());
                continue;
            }
            language.setID(oldLimitLanguage.getID());
        }
        rspObject.setDelLimitLang(false);
        rspObject.setLimitLangs(Lists.newArrayList(limitLanguageMaps.values()));
        rspObject.setDelLimitLangIds(delLimitLanguageIds);

        /*if (!delLimitLanguageIds.isEmpty()){
            limitLanguageMapper.delLimitLanguageIds(delLimitLanguageIds);
        }
        limitLanguageMapper.batchInsert(Lists.newArrayList(limitLanguageMaps.values()));*/
    }

    /**
     *
     * @param limitLanguageMaps
     * @param limitId
     * @param languageId
     * @return
     */
    private LimitMultipleLanguageInfoPO getLimitLanguageInfo(Map<String, LimitMultipleLanguageInfoPO> limitLanguageMaps, String limitId, Integer languageId){
        UserInfo localUser = UserHelper.getLocalUser();
        String limitLanguageKey = String.format("%s_%s", limitId, languageId);
        if (!limitLanguageMaps.containsKey(limitLanguageKey)){
            LimitMultipleLanguageInfoPO limitLanguage = new LimitMultipleLanguageInfoPO();
            limitLanguage.setID(UUID.randomUUID().toString());
            limitLanguage.setLimitInstanceID(limitId);
            limitLanguage.setLanguageId(languageId);
            limitLanguage.setCreatedBy(localUser.getRegionAccount());
            limitLanguage.setCreatedDate(DateUtils.getNow());
            limitLanguage.setModifiedBy(localUser.getRegionAccount());
            limitLanguage.setModifiedDate(DateUtils.getNow());

            limitLanguageMaps.put(limitLanguageKey, limitLanguage);
        }
        return limitLanguageMaps.get(limitLanguageKey);
    }

    /**
     *
     * @param testAnalyte
     * @param productAttr
     */
    private void setProductAttrId(TestAnalyteLimitInfo testAnalyte, LimitGroupProductAttrReq productAttr){
        Map<Integer, Map<String, Set<Integer>>> ppLimitGroupIds = testAnalyte.getPpLimitGroupIds();
        if (ppLimitGroupIds == null || ppLimitGroupIds.isEmpty()){
            return;
        }
        Integer productAttrId = productAttr.getProductAttrId();
        if (productAttrId == null || productAttrId.intValue() <= 0){
            return;
        }
        Map<String, Set<Integer>> ppMaps;
        if (testAnalyte.getConclusionMode() != ConclusionMode.PP_MATRIX){
            String ppId = StringUtils.EMPTY;
            for (Map.Entry<Integer, Map<String, Set<Integer>>> limitGroup: ppLimitGroupIds.entrySet()) {
                ppMaps = limitGroup.getValue();
                if (!ppMaps.containsKey(ppId)){
                    ppMaps.put(ppId, Sets.newHashSet());
                }
                ppMaps.get(ppId).add(productAttrId);
            }
            return;
        }
        Integer limitGroupId = productAttr.getLimitGroupId();
        if (limitGroupId == null || limitGroupId.intValue() <= 0){
            return;
        }
        ppMaps = ppLimitGroupIds.get(limitGroupId);
        if (ppMaps == null || ppMaps.isEmpty()){
            return;
        }
        if (StringUtils.isBlank(productAttr.getPpId())){
            return;
        }
        Set<Integer> productAttrIds = ppMaps.get(productAttr.getPpId());
        if (productAttrIds == null){
            productAttrIds = Sets.newHashSet();
        }
        productAttrIds.add(productAttrId);
    }

    /**
     *
     * @param testAnalyte
     * @param limitGroupPPMaps
     */
    private void setLimitGroup(TestAnalyteLimitInfo testAnalyte, Map<Integer, Set<String>> limitGroupPPMaps, Set<String> ppIds){
        if (limitGroupPPMaps == null || limitGroupPPMaps.isEmpty()){
            return;
        }
        if (ppIds == null){
            ppIds = Sets.newHashSet();
        }
        ConclusionMode conclusionMode = testAnalyte.getConclusionMode();
        // limitGroupId，Map<ppId, productAttrId>
        Map<Integer, Map<String, Set<Integer>>> ppLimitGroupIds = Maps.newHashMap();
        //Map<Integer, Set<String>> ppLimitGroupIds = Maps.newHashMap();
        Set<Integer> limitGroupIds = testAnalyte.getLimitGroupIds();
        Set<String> limitPpIds;
        // limitGroupId, ppIds
        for (Map.Entry<Integer, Set<String>> limitGroup: limitGroupPPMaps.entrySet()){
            if (limitGroup.getValue() == null){
                continue;
            }
            Integer limitGroupId = limitGroup.getKey();
            if (!ppLimitGroupIds.containsKey(limitGroupId)){
                ppLimitGroupIds.put(limitGroupId, Maps.newHashMap());
            }
            limitGroupIds.add(limitGroupId);
            if (conclusionMode != ConclusionMode.PP_MATRIX){
                continue;
            }
            Map<String, Set<Integer>> ppIdMaps = ppLimitGroupIds.get(limitGroupId);
            for (String ppId: ppIds){
                if (!limitGroup.getValue().contains(ppId) || ppIdMaps.containsKey(ppId)){
                    continue;
                }
                ppIdMaps.put(ppId, Sets.newHashSet());
            }
            if (ppIdMaps.isEmpty()){
                ppLimitGroupIds.remove(limitGroupId);
            }
        }
        testAnalyte.setPpLimitGroupIds(ppLimitGroupIds);
        testAnalyte.setLimitGroupIds(limitGroupIds);
    }

    /**
     *
     * @param sampleMaps
     * @param sampleLimitGroupIds
     * @param samplePO
     * @return
     */
    private Map<Integer, Set<String>> findParentSampleLimitGroupIds(Map<String, LimitGroupSampleReq> sampleMaps, Map<String, Map<Integer, Set<String>>> sampleLimitGroupIds, LimitGroupSampleReq samplePO){
        if (samplePO == null){
            return null;
        }
        SampleType sampleType = SampleType.findType(samplePO.getSampleType());
        if (sampleType == null){
            return null;
        }
        String sampleId = sampleType == SampleType.OriginalSample ? samplePO.getSampleId() : samplePO.getSampleParentId();
        if (sampleType == SampleType.OriginalSample || sampleLimitGroupIds.containsKey(sampleId)){
            return sampleLimitGroupIds.get(sampleId);
        }
        return findParentSampleLimitGroupIds(sampleMaps, sampleLimitGroupIds, sampleMaps.get(samplePO.getSampleParentId()));
    }

    /**
     *
     * @param copyLimitReq
     * @return
     */
    public CustomResult copyLimit(CopyLimitReq copyLimitReq){
        CustomResult result = new CustomResult();
        //copyLimitReq.setOldOrderNo(copyLimitReq.getOldOrderNo().replace("\"", ""));
        // 新Order中如果已经获取了TRIMS Limit，则终止limit的copy并弹出提示
        List<LimitInstancePO> trimsLimitList = limitMapper.queryLimitFromTrimsByOrderNo(copyLimitReq.getOrderNo());
        if (trimsLimitList != null && trimsLimitList.size() > 0) {
            result.setSuccess(false);
            result.setMsg("OrderNo " + copyLimitReq.getOrderNo() + " already has limits from trims.");
            return result;
        }

        // 原订单是否有来自TRIMS的Limit，如果有，则终止limit的copy并弹出提示
        List<LimitInstancePO> oldOrderNoTrimsLimitList = limitMapper.queryLimitFromTrimsByOrderNo(copyLimitReq.getOldOrderNo());
        if (oldOrderNoTrimsLimitList != null && oldOrderNoTrimsLimitList.size() > 0) {
            result.setSuccess(false);
            result.setMsg("Parent OrderNo " + copyLimitReq.getOldOrderNo() + " already has limits from trims.");
            return result;
        }

        // 系统需要校验limit对应的Analyte ID是否与tb_analyte_instance表中的analyte
        // ID一致，如果不一致，终止limit的copy并弹出提示
        List<LimitInstancePO> oldLimitList = limitMapper.queryLimitWithOutTrimsByOrderNo(copyLimitReq.getOldOrderNo());
        if (oldLimitList == null || oldLimitList.size() == 0) {
            result.setSuccess(false);
            result.setMsg(" Parent OrderNo " + copyLimitReq.getOldOrderNo() + " has no limit.");
            return result;
        }

        Map<String, LimitInstancePO> oldLimitMap = new HashMap<>();
        // 老订单的数据
        List<TestLineLimitInfo> oldtestLineLimitList = limitMapper.queryOrderTestLineLimitInfo(copyLimitReq.getOldOrderNo());
        // 新订单数据
        List<TestLineLimitInfo> testLineLimitList = limitMapper.queryOrderTestLineLimitInfo(copyLimitReq.getOrderNo());
        // 取新订单的analyte,系统需要校验limit对应的Analyte ID是否与tb_analyte_instance表中的analyte
        // ID一致，如果不一致，终止limit的copy并弹出提示
        List<AnalyteInfoPO> analyteList = analyteMapper.queryAnalyteListByOrderNo(copyLimitReq.getOrderNo());


        Map<String, List<Integer>> analyteMap = analyteList.stream().collect(Collectors.groupingBy(
                an -> an.getTestLineInstanceID(), Collectors.mapping(as -> as.getAnalyteID(), Collectors.toList())));

        // 将map中的key替换为testLineId
        Map<Integer, List<Integer>> testLineAnalyteMap = ConvertAnalyteData(analyteMap, testLineLimitList);

        for (TestLineLimitInfo testLine : oldtestLineLimitList) {

            if (testLineAnalyteMap.containsKey(testLine.getTestLineID())) {
                for (LimitInstancePO limitInstance : testLine.getLimitList()) {

                    if (limitInstance.getAnalyteID() != null && !testLineAnalyteMap.get(testLine.getTestLineID())
                            .contains(limitInstance.getAnalyteID())) {
                        result.setSuccess(false);
                        result.setMsg("TestLine " + testLine.getTestLineID() + " has no analyte "
                                + limitInstance.getAnalyteID());
                        return result;
                    }
                    if (limitInstance.getID() != null) {
                        oldLimitMap.put(testLine.getPPVersionID() + "_" + testLine.getTestLineID() + "_"
                                + testLine.getStandardVersionID() + "_" + testLine.getSampleNo() + "_"
                                + limitInstance.getAnalyteID(), limitInstance);
                    }
                }
            }
        }

        Map<String, LimitInstancePO> newLimitMap = new HashMap<>();

        Map<String, String> newSampleIdMap = new HashMap<>();

        Map<Integer, String> testLineIdMap = new HashMap<>();

        Map<String, String> matrixIdMap = new HashMap<>();

        // 保存新单testline中assign的sampleNo
        Map<Integer, List<String>> testLineSampleMap = new HashMap<>();

        for (TestLineLimitInfo limit : testLineLimitList) {
            if (!testLineSampleMap.containsKey(limit.getTestLineID())) {
                testLineSampleMap.put(limit.getTestLineID(), new ArrayList<>());
            }
            testLineSampleMap.get(limit.getTestLineID()).add(limit.getSampleNo());

            newSampleIdMap.put(limit.getSampleNo(), limit.getNewSampleId());
            testLineIdMap.put(limit.getTestLineID(), limit.getTestLineInstanceId());
            matrixIdMap.put(limit.getSampleNo() + "_" + limit.getTestLineID(), limit.getNewMatrixId());
            for (LimitInstancePO limitInstance : limit.getLimitList()) {
                if (limitInstance.getID() != null) {
                    String key = limit.getPPVersionID() + "_" + limit.getTestLineID() + "_"
                            + limit.getStandardVersionID() + "_" + limit.getSampleNo() + "_"
                            + limitInstance.getAnalyteID();
                    newLimitMap.put(key, limitInstance);
                }
            }
        }
        List<LimitInstancePO> saveOrUpdateList = new ArrayList<>();
        UserInfo localUser = UserHelper.getLocalUser();

        // 遍历老单的limit， 对比新单 ，如果新单的limit存在 ，就覆盖， 不存在， 则添加
        for (String oldKey : oldLimitMap.keySet()) {
            // 如果新单存在对应的limit 就覆盖原有的limit
            if (newLimitMap.containsKey(oldKey)) {

                LimitInstancePO limit = newLimitMap.get(oldKey);
                if (!oldLimitMap.get(oldKey).getReportDescription().equals(limit.getReportDescription())) {
                    limit.setReportDescription(oldLimitMap.get(oldKey).getReportDescription());
                    saveOrUpdateList.add(limit);
                }

            } else {// 不存在 且旧单的testLine 中assign了这个样品 则添加
                String sampleNo = oldKey.split("_")[3];
                String testlineId = oldKey.split("_")[1];
                if (testLineSampleMap.get(Integer.valueOf(testlineId)).contains(sampleNo)) {
                    LimitInstancePO newlimit = new LimitInstancePO();
                    LimitInstancePO oldLimit = oldLimitMap.get(oldKey);
                    BeanUtils.copyProperties(oldLimit, newlimit);
                    newlimit.setID(UUID.randomUUID().toString());
                    newlimit.setTestSampleID(newSampleIdMap.get(oldKey.split("_")[3]));
                    // newlimit.setTestMatrixID(matrixIdMap.get(oldKey.split("_")[3]+"_"+oldKey.split("_")[1]));
                    newlimit.setTestLineInstanceID(testLineIdMap.get(Integer.valueOf(oldKey.split("_")[1])));
                    newlimit.setTalBaseId(NumberUtil.toLong(oldLimit.getTalBaseId()));
                    newlimit.setActiveIndicator(true);
                    newlimit.setCreatedDate(DateUtils.getNow());
                    newlimit.setCreatedBy(localUser.getRegionAccount());
                    newlimit.setModifiedDate(DateUtils.getNow());
                    newlimit.setModifiedBy(localUser.getRegionAccount());
                    saveOrUpdateList.add(newlimit);
                }
            }
        }

        CollectionUtils.filter(newLimitMap.keySet(), new Predicate() {
            @Override
            public boolean evaluate(Object arg0) {
                // TODO Auto-generated method stub
                String s = arg0.toString();
                return !oldLimitMap.containsKey(s);
            }
        });
        List<String> limitIds = new ArrayList<>();
        for (Map.Entry<String, LimitInstancePO> param : newLimitMap.entrySet()) {
            limitIds.add(param.getValue().getID());
        }


        result.setSuccess(transactionTemplate.execute((tranStatus)->{
            boolean isSuccess = limitIds.isEmpty() || limitMapper.batchDeleteLimit(limitIds) > 0;
            if (!isSuccess){
                tranStatus.setRollbackOnly(); // 回滚事务
                return isSuccess;
            }
            if (saveOrUpdateList.isEmpty()){
                result.setMsg("No limit need copy");
                result.setSuccess(false);
                return false;
            }
            saveOrUpdateList.forEach(item ->{
                item.setActiveIndicator(true);
            });

            isSuccess = limitMapper.batchInsert(saveOrUpdateList) > 0;
            if (!isSuccess){
                tranStatus.setRollbackOnly(); // 回滚事务
                return isSuccess;
            }
            return isSuccess;
        }));
        return result;
    }

    private Map<Integer, List<Integer>> ConvertAnalyteData(Map<String, List<Integer>> analyteMap,
                                                           List<TestLineLimitInfo> testLineLimitList) {
        Map<Integer, List<Integer>> result = new HashMap<>();
        for (TestLineLimitInfo testLine : testLineLimitList) {
            for (String key : analyteMap.keySet()) {
                if (testLine.getTestLineInstanceId().equals(key)) {
                    result.put(testLine.getTestLineID(), analyteMap.get(key));
                }
            }
        }
        return result;
    }

    public CustomResult<GetRequirmentListRsp> getRequirmentList(GetRequirmentListReq getRequirmentListReq) {
        CustomResult customResult = new CustomResult();

        return customResult;
    }

    /**
     *
     * @param requirmentListReq
     * @return
     */
    public CustomResult getTestLineList(GetRequirmentListReq requirmentListReq){
        CustomResult result = new CustomResult();

        //orderNo 判断
        if (!StringUtils.isNoneBlank(requirmentListReq.getOrderNo())){
            result.setMsg("orderNo is null");
            result.setSuccess(false);
            return result;
        }

        //查询Order信息
        GeneralOrderInstanceDTO order = this.generalOrderInstanceMapper.getByNo(requirmentListReq.getOrderNo());
        if (order==null){
            result.setMsg("can not find order");
            result.setSuccess(false);
            return result;
        }

        //根据Order查询testLine信息
        List<TestLineRequirmentListDTO> lstRequirment= testLineMapper.getTestLineForRequirment(order.getId());

        //testline 别名处理
        testLineLocalService.build(order.getOrderNo(),lstRequirment);

        TestLineDTO testLineDTO=new TestLineDTO();
        testLineDTO.setOrderNo(order.getOrderNo());
        testLineDTO.setActiveIndicator(order.getActiveIndicator());
        testLineDTO.setTestLines(lstRequirment);
        result.setData(testLineDTO);
        result.setSuccess(true);
        return result;
    }

    /**
     *
     * @param testLineInstanceIdReq
     * @return
     */
    public CustomResult getTestLineRequirmentList(TestLineInstanceIdReq testLineInstanceIdReq){
        CustomResult result = new CustomResult();

        //TestLineInstanceId 判断
        if (!StringUtils.isNoneBlank(testLineInstanceIdReq.getTestLineInstanceId())){
            result.setMsg("testLineInstanceId is null");
            result.setSuccess(false);
            return result;
        }

        //获取TestLine状态
        TestLineInstancePO testLineInstance=testLineMapper.getTestLineById(testLineInstanceIdReq.getTestLineInstanceId());
        if (testLineInstance == null) {
            result.setMsg("TestLineInstanceID Error！");
            result.setSuccess(false);
            return result;
        }

        //查询testLimits信息
        List<TestLineLimitsDTO> lstTestLimits = testLineMapper.getTestLimits(testLineInstanceIdReq.getTestLineInstanceId());

        if(lstTestLimits == null || lstTestLimits.isEmpty()){
            result.setMsg("testLineInstanceId get no data");
            result.setSuccess(false);
            return result;
        }

        // PPNo, ppBaseId
        Map<Integer, Long> ppNoOfPpIdsMap = Maps.newHashMap();
        // testLineId, Set<PPNo>
        Set<Integer> ppNos= Sets.newHashSet();

        // 获取所有的ppNo
        for(TestLineLimitsDTO testItem : lstTestLimits){
            if (testItem == null){
                continue;
            }
            Integer ppNo = NumberUtil.toInt(testItem.getPpNo());
            Long ppBaseId = NumberUtil.toLong(testItem.getPpId());
            if (ppNo > 0 && !ppNoOfPpIdsMap.containsKey(ppNo)){
                ppNoOfPpIdsMap.put(ppNo, ppBaseId);
            }
            ppNos.add(ppNo);
        }

        // 查询Sample信息
        List<TestLineSampleDTO> lstTestLineSample=testLineMapper.getTestLineSample(testLineInstanceIdReq.getTestLineInstanceId());

        // 查询Limit信息
        List<LimitInstancePO> lstLimitInfo= limitMapper.getLimitListByTestLineInstanceID(testLineInstanceIdReq.getTestLineInstanceId());

        // condition Map
        Map<Integer, String> conditionMap = Maps.newHashMap();
        Map<Integer, TestLineLimitsDTO> conditionMapResult = Maps.newHashMap();
        for (LimitInstancePO limit : lstLimitInfo) {
            if (!conditionMap.containsKey(limit.getTestConditionID())) {
                conditionMap.put(limit.getTestConditionID(), "");
            }
        }

        //需要展示的condition（condition合并）
        for (TestLineLimitsDTO orderTestCondition : lstTestLimits) {
            if(orderTestCondition==null){
                continue;
            }
            if (conditionMap.containsKey(orderTestCondition.getTestConditionId())) {
                conditionMapResult.put(orderTestCondition.getTestConditionId(),orderTestCondition);
                conditionMap.remove(orderTestCondition.getTestConditionId());
            }
        }
        if (conditionMap.containsKey(null)) {
            conditionMapResult.put(null,null);
        }

        // DIG-5762 ConclusionMode 应在tb_general_order_instance表中获取，与LimitGroup 没关系
        GeneralOrderInstanceInfoPO orderInfoByOrderId = orderMapper.getOrderInfoByOrderId(testLineInstance.getGeneralOrderInstanceID());
        ConclusionMode conclusionMode = ConclusionMode.findCode(orderInfoByOrderId.getConclusionMode());
        // 默认显示MATRIX 维度
        if (ConclusionMode.check(orderInfoByOrderId.getConclusionMode(), ConclusionMode.NoLimit)) {
            conclusionMode = ConclusionMode.MATRIX;
        }

        List<TestLineLimitsDTO> lstTestLimitsResult = Lists.newArrayList();

        // 查询Analyte信息
        List<AnalyteInfoPO> lstAnalyteInfo=analyteMapper.getAnalyteTestLineList(testLineInstanceIdReq.getTestLineInstanceId());
        Set<Integer> requirmentMaps=Sets.newHashSet();
        Set<String> limitMaps=Sets.newHashSet();
        //组织结果数据
        for (Map.Entry<Integer, TestLineLimitsDTO> entry : conditionMapResult.entrySet()) {
            for (TestLineSampleDTO sampleInfo : lstTestLineSample) {
                Set<Long> ppBaseIds = sampleInfo.getPpBaseIds();
                if (ppBaseIds == null){
                    ppBaseIds = Sets.newHashSet(0L);
                }
                Set<Long> finalPpBaseIds = ppBaseIds;
                List<LimitInstancePO> limits = lstLimitInfo.stream().filter(p -> StringUtils.equalsIgnoreCase(p.getTestSampleID(), sampleInfo.getSampleId())).collect(Collectors.toList());
                if (conclusionMode == ConclusionMode.PP_MATRIX){
                    limits = limits.stream().filter(p -> finalPpBaseIds.contains(NumberUtil.toLong(p.getPpBaseId()))).collect(Collectors.toList());
                }

                for (AnalyteInfoPO analyteItem : lstAnalyteInfo) {
                    List<LimitInstancePO> lstAnalyteLimitInfo = Lists.newArrayList();
                    Integer testConditionId = entry.getKey();
                    if (testConditionId == null) {
                        lstAnalyteLimitInfo = limits.stream().filter(p ->
                            p.getTestConditionID() == null &&
                            NumberUtil.equals(p.getAnalyteID(), analyteItem.getAnalyteID())
                        ).collect(Collectors.toList());
                    } else {
                        lstAnalyteLimitInfo = limits.stream().filter(p ->
                            NumberUtil.equals(p.getTestConditionID(), testConditionId) &&
                            NumberUtil.equals(p.getAnalyteID(), analyteItem.getAnalyteID())
                        ).collect(Collectors.toList());
                    }
                    if (lstAnalyteLimitInfo == null || lstAnalyteLimitInfo.isEmpty()) {
                        List<LimitInstancePO> lstAnalyteLimitTmpInfo = limits.stream().filter(p ->
                            NumberUtil.equals(p.getTestConditionID(), testConditionId) &&
                            p.getAnalyteID() == null
                        ).collect(Collectors.toList());
                        if(lstAnalyteLimitTmpInfo.isEmpty() && testConditionId == null){
                            lstAnalyteLimitTmpInfo = limits.stream().filter(p -> NumberUtil.equals(p.getAnalyteID(), analyteItem.getAnalyteID())).collect(Collectors.toList());
                            if(lstAnalyteLimitTmpInfo.isEmpty()) {
                                LimitInstancePO limitInfo = new LimitInstancePO();
                                limitInfo.setTestSampleID(sampleInfo.getSampleId());
                                limitInfo.setTestMatrixID(sampleInfo.getTestMatrixID());
                                limitInfo.setTestConditionID(entry.getKey());
                                limitInfo.setAnalyteID(analyteItem.getAnalyteID());
                                lstAnalyteLimitInfo.add(limitInfo);
                            }
                        }else {
                            for (LimitInstancePO analyteLimitItem : lstAnalyteLimitTmpInfo) {
                                LimitInstancePO limitInfo = new LimitInstancePO();
                                BeanUtils.copyProperties(analyteLimitItem, limitInfo);
                                limitInfo.setTestConditionID(entry.getKey());
                                limitInfo.setAnalyteID(analyteItem.getAnalyteID());
                                lstAnalyteLimitInfo.add(limitInfo);
                            }
                        }
                    }
                    if (conclusionMode == ConclusionMode.MATRIX) {
                        String limitKey = String.format("%s_%s_%s_%s_%s",
                                sampleInfo.getSampleId(),
                                analyteItem.getAnalyteID(),
                                analyteItem.getReportUnit(),
                                entry.getKey(),
                                0
                        );
                        if (limitMaps.contains(limitKey)) {
                            continue;
                        }
                        limitMaps.add(limitKey);
                        TestLineLimitsDTO testLineLimitInfo = new TestLineLimitsDTO();
                        testLineLimitInfo.setRowId(String.format("%s_%s_%s", null, entry.getKey(), analyteItem.getID()));
                        testLineLimitInfo.setPpNo(0);
                        testLineLimitInfo.setPpNoHide(0);
                        testLineLimitInfo.setPpId(StringUtils.EMPTY);
                        getLimitResult(lstTestLimitsResult, entry, analyteItem, lstAnalyteLimitInfo, testLineLimitInfo);
                        continue;
                    }
                    // DIG-5824
                    if (ppNos.isEmpty()){
                        ppNos.add(0);
                    }
                    for (Integer ppNo : ppNos) {
                        long ppBaseId = NumberUtil.toLong(ppNoOfPpIdsMap.get(ppNo));
                        if (!ppBaseIds.contains(ppBaseId)){
                            continue;
                        }
                        TestLineLimitsDTO testLineLimitInfo = new TestLineLimitsDTO();
                        testLineLimitInfo.setRowId(String.format("%s_%s_%s", ppNoOfPpIdsMap.get(ppNo), entry.getKey(), analyteItem.getID()));
                        if (!requirmentMaps.contains(ppNo)) {
                            testLineLimitInfo.setPpNo(ppNo);
                            requirmentMaps.add(ppNo);
                        }
                        testLineLimitInfo.setPpId(String.valueOf(ppBaseId));
                        testLineLimitInfo.setPpNoHide(ppNo);
                        String limitKey = String.format("%s_%s_%s_%s_%s",
                                sampleInfo.getSampleId(),
                                analyteItem.getAnalyteID(),
                                analyteItem.getReportUnit(),
                                entry.getKey(),
                                ppNo != null && ppNo.intValue() > 0 ? ppNo : 0
                        );
                        if (limitMaps.contains(limitKey)) {
                            continue;
                        }
                        limitMaps.add(limitKey);
                        // 筛选PP对应的Limit
                        List<LimitInstancePO> lstAnalyteLimitResult = lstAnalyteLimitInfo.stream().filter(p -> NumberUtil.equals(p.getPpBaseId(), ppNoOfPpIdsMap.get(ppNo))).collect(Collectors.toList());
                        if(lstAnalyteLimitResult.isEmpty()){
                            for(LimitInstancePO limitItem :lstAnalyteLimitInfo){
                                LimitInstancePO limitInfo=new LimitInstancePO();
                                limitInfo.setTestMatrixID(limitItem.getTestMatrixID());
                                limitInfo.setTestSampleID(limitItem.getTestSampleID());
                                limitInfo.setReportDescription(StringUtils.EMPTY);
                                if (lstAnalyteLimitResult.contains(limitInfo)){
                                    continue;
                                }
                                lstAnalyteLimitResult.add(limitInfo);
                            }
                            lstAnalyteLimitResult = lstAnalyteLimitResult.stream().distinct().collect(Collectors.toList());
                        }
                        getLimitResult(lstTestLimitsResult, entry, analyteItem, lstAnalyteLimitResult, testLineLimitInfo);
                    }
                }
            }
        }
        // 结果排序
        // lstTestLimitsResult=lstTestLimitsResult.stream().sorted(Comparator.comparing(TestLineLimitsDTO::getPpNoHide).thenComparing(TestLineLimitsDTO::getTestConditionSeq).thenComparing(TestLineLimitsDTO::getTestAnalyteSeq)).collect(Collectors.toList());
        lstTestLimitsResult.sort(new TestLineLimitComparator(true));
        TestLineRequirmentDTO requirmentResult = new TestLineRequirmentDTO();
        requirmentResult.setTestLineStatus(testLineInstance.getTestLineStatus());
        requirmentResult.setTestLimits(lstTestLimitsResult);
        requirmentResult.setSamples(lstTestLineSample);
        result.setData(requirmentResult);
        result.setSuccess(true);
        return result;
    }

    /**
     * 获取testLimits数据
     * @param lstTestLimitsResult
     * @param entry
     * @param analyteItem
     * @param oldLimits
     * @param testLineLimitInfo
     */
    private void getLimitResult(List<TestLineLimitsDTO> lstTestLimitsResult, Map.Entry<Integer, TestLineLimitsDTO> entry, AnalyteInfoPO analyteItem, List<LimitInstancePO> oldLimits, TestLineLimitsDTO testLineLimitInfo) {
        testLineLimitInfo.setTestConditionId(entry.getKey());
        TestLineLimitsDTO testCondition = entry.getValue();
        if (testCondition != null){
            testLineLimitInfo.setTestConditionName(testCondition.getTestConditionName());
            testLineLimitInfo.setTestConditionSeq(NumberUtil.toInt(testCondition.getTestConditionSeq()));
        }
        testLineLimitInfo.setAnalyteId(analyteItem.getID());
        testLineLimitInfo.setTestAnalyteId(analyteItem.getAnalyteID());
        testLineLimitInfo.setTestAnalyteName(analyteItem.getTestAnalyteName());
        testLineLimitInfo.setTestAnalyteSeq(NumberUtil.toInt(analyteItem.getTestAnalyteSeq()));
        testLineLimitInfo.setReportUnit(analyteItem.getReportUnit());
        List<TestLineLimitsMatrixsDTO> lstMatrixs = Lists.newArrayList();
        if (oldLimits == null || oldLimits.isEmpty()) {
            lstTestLimitsResult.add(testLineLimitInfo);
            return;
        }
        for (LimitInstancePO oldLimit: oldLimits) {
            TestLineLimitsMatrixsDTO matrixsInfo = new TestLineLimitsMatrixsDTO();
            matrixsInfo.setLimitInstanceId(oldLimit.getID());
            matrixsInfo.setTestSampleId(oldLimit.getTestSampleID());
            matrixsInfo.setTestMatrixId(oldLimit.getTestMatrixID());
            matrixsInfo.setManualRequirement(oldLimit.getManualRequirement());
            matrixsInfo.setReportDesc(LOStringUtil.decode(oldLimit.getReportDescription()));
            lstMatrixs.add(matrixsInfo);
        }
        testLineLimitInfo.setTestMatrixs(lstMatrixs);
        lstTestLimitsResult.add(testLineLimitInfo);
    }

    /**
     * 更新Requirment
     * @param reqObject
     * @return
     */
    public CustomResult updateRequirment(RequirmentSaveReq reqObject) {
        CustomResult rspResult = new CustomResult(false);
        String testLineInstanceId = reqObject.getTestLineInstanceId();
        if (StringUtils.isBlank(testLineInstanceId)){
            rspResult.setMsg("请求的TestLineInstanceId不能为空.");
            return rspResult;
        }
        ReportInfoPO report = reportMapper.getReportByTestLineInstanceID(testLineInstanceId);
        // TestLineInstanceID 数据检查
        TestLineInstancePO testLineInstance = testLineMapper.getTestLineById(testLineInstanceId);
        if (testLineInstance == null) {
            rspResult.setMsg("TestLineInstanceID Error！");
            return rspResult;
        }
        // 报告状态检查
        if (report == null || ReportStatus.check(report.getReportStatus(), ReportStatus.Approved)){
            rspResult.setMsg("报告approve后，此页面数据将被冻结.");
            return rspResult;
        }
        UserInfo localUser = UserHelper.getLocalUser();
        List<TestLineLimitsDTO> requirmentList = reqObject.getTestLimits();
        /* del requirment */
        /*limitMapper.deleteBytestLineInstanceID(testLineInstanceId);*/

        Map<String, LimitInstancePO> oldLimitMaps = Maps.newHashMap();
        limitMapper.getTestLineLimitInfoList(testLineInstanceId).forEach(oldLimit->{
            oldLimitMaps.put(oldLimit.getID(), oldLimit);
        });
        /* save requirment */
        List<LimitInstancePO> limits = Lists.newArrayList();
        for (TestLineLimitsDTO testLimit : requirmentList) {
            for (TestLineLimitsMatrixsDTO limitsMatrix: testLimit.getTestMatrixs()){
                String reportDesc = LOStringUtil.delHTMLTag(limitsMatrix.getReportDesc());
                LimitInstancePO oldLimit = oldLimitMaps.get(limitsMatrix.getLimitInstanceId());
                String limitInstanceId = UUID.randomUUID().toString();
                if (oldLimit != null){
                    if (StringUtils.equalsIgnoreCase(oldLimit.getReportDescription(), reportDesc)){
                        continue;
                    }
                    limitInstanceId = oldLimit.getID();
                }
                if (StringUtils.isBlank(reportDesc)){
                    continue;
                }
                LimitInstancePO limit = new LimitInstancePO();
                limit.setID(limitInstanceId);
                limit.setTestLineInstanceID(testLineInstanceId);
                limit.setReportDescription(LOStringUtil.delHTMLTag(limitsMatrix.getReportDesc()));
                limit.setReportUnit(testLimit.getReportUnit());
                limit.setTestSampleID(limitsMatrix.getTestSampleId());
                limit.setAnalyteID(testLimit.getTestAnalyteId());
                limit.setTestMatrixID(limitsMatrix.getTestMatrixId());
                if(!StringUtils.isBlank(testLimit.getPpId())){
                    limit.setPpBaseId(Long.valueOf(testLimit.getPpId()));
                }

                if (testLimit.getTestConditionId() != null) {
                    limit.setTestConditionID(testLimit.getTestConditionId());
                }
                limit.setTalBaseId(0L);
                limit.setManualRequirement(true);
                limit.setActiveIndicator(true);
                limit.setCreatedDate(DateUtils.getNow());
                limit.setModifiedDate(DateUtils.getNow());
                limit.setCreatedBy(localUser.getRegionAccount());
                limit.setModifiedBy(localUser.getRegionAccount());
                limits.add(limit);
            }
        }
        if (!limits.isEmpty()) {
            limitMapper.batchInsert(limits);
        }
        rspResult.setSuccess(true);
        return rspResult;
    }
}
