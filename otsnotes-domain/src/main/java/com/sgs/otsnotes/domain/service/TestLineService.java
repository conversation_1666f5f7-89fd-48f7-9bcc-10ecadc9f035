package com.sgs.otsnotes.domain.service;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.sgs.core.domain.UserInfo;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.core.base.CustomResult;
import com.sgs.framework.core.context.SystemContextHolder;
import com.sgs.framework.core.exception.BizException;
import com.sgs.framework.facade.domain.rsp.BuParamValueRsp;
import com.sgs.framework.i18n.util.MessageUtil;
import com.sgs.framework.log.SystemLogHelper;
import com.sgs.framework.log.model.SystemLog;
import com.sgs.framework.model.enums.LanguageType;
import com.sgs.framework.model.enums.ReportEntryModeEnum;
import com.sgs.framework.model.enums.ReportLanguage;
import com.sgs.framework.model.enums.TestLineType;
import com.sgs.framework.security.context.SecurityContextHolder;
import com.sgs.framework.security.utils.SecurityUtil;
import com.sgs.framework.tool.utils.Func;
import com.sgs.framework.tool.utils.ProductLineContextHolder;
import com.sgs.gpo.facade.model.otsnotes.testdata.req.TLDataHeaderSaveReq;
import com.sgs.gpo.facade.model.otsnotes.testdata.rsp.TLDataHeaderSaveRsp;
import com.sgs.gpo.facade.model.otsnotes.testline.req.LabSectionUpdateItemReq;
import com.sgs.gpo.facade.model.otsnotes.testline.req.LabSectionUpdateReq;
import com.sgs.gpo.facade.model.report.req.ReportExtForTLUpdateReq;
import com.sgs.gpo.facade.temp.ReportTempFacade;
import com.sgs.grus.async.AsyncCall;
import com.sgs.grus.async.AsyncResult;
import com.sgs.grus.async.AsyncUtils;
import com.sgs.grus.bizlog.BizLogClient;
import com.sgs.grus.bizlog.common.BizLog;
import com.sgs.grus.bizlog.common.BizLogHelper;
import com.sgs.grus.bizlog.info.BizLogInfo;
import com.sgs.otsnotes.core.annotation.AccessPolicyRule;
import com.sgs.otsnotes.core.common.UserHelper;
import com.sgs.otsnotes.core.config.SubcontractConfig;
import com.sgs.otsnotes.core.constants.BizLogConstant;
import com.sgs.otsnotes.core.constants.Constants;
import com.sgs.otsnotes.core.constants.LabCodeConsts;
import com.sgs.otsnotes.core.kafka.Producer;
import com.sgs.otsnotes.core.thread.ThreadPoolContextTaskExecutor;
import com.sgs.otsnotes.core.util.CommUtil;
import com.sgs.otsnotes.core.util.DateUtils;
import com.sgs.otsnotes.core.util.NumberUtil;
import com.sgs.otsnotes.core.util.StringUtil;
import com.sgs.otsnotes.dbstorages.mybatis.enums.TestLineRequestType;
import com.sgs.otsnotes.dbstorages.mybatis.extmapper.*;
import com.sgs.otsnotes.dbstorages.mybatis.extmapper.testmatrix.TestMatrixExtMapper;
import com.sgs.otsnotes.dbstorages.mybatis.extmodel.CitationBaseStandarInfo;
import com.sgs.otsnotes.dbstorages.mybatis.extmodel.PPTestLineInfo;
import com.sgs.otsnotes.dbstorages.mybatis.extmodel.TestLineStandarBase;
import com.sgs.otsnotes.dbstorages.mybatis.extmodel.UpdateTestlineInstanceFileIdReq;
import com.sgs.otsnotes.dbstorages.mybatis.extmodel.analyte.AnalyteExtPO;
import com.sgs.otsnotes.dbstorages.mybatis.mapper.*;
import com.sgs.otsnotes.dbstorages.mybatis.model.*;
import com.sgs.otsnotes.domain.service.common.TestLineEvaluationAliasUtil;
import com.sgs.otsnotes.domain.service.gpn.analyte.IAnalyteService;
import com.sgs.otsnotes.domain.service.gpn.citation.ICitationService;
import com.sgs.otsnotes.domain.service.gpn.order.IOrderService;
import com.sgs.otsnotes.domain.service.gpn.subcontract.ISubContractExternalRelService;
import com.sgs.otsnotes.domain.service.localize.LocalizableTranslator;
import com.sgs.otsnotes.domain.service.productlineservice.ProductLineServiceHolder;
import com.sgs.otsnotes.domain.service.testline.ITestLineStatusService;
import com.sgs.otsnotes.domain.service.testline.TestLineStatusService;
import com.sgs.otsnotes.domain.service.trimslocal.TrimsLocalTestLineService;
import com.sgs.otsnotes.domain.util.CitationUtil;
import com.sgs.otsnotes.domain.util.CodeUtil;
import com.sgs.otsnotes.facade.model.comparator.StandardComparator;
import com.sgs.otsnotes.facade.model.dto.*;
import com.sgs.otsnotes.facade.model.dto.analyte.TestLineAnalyteDTO;
import com.sgs.otsnotes.facade.model.dto.subcontract.SubContractOrderInfoDTO;
import com.sgs.otsnotes.facade.model.enums.*;
import com.sgs.otsnotes.facade.model.info.*;
import com.sgs.otsnotes.facade.model.info.subcontract.SubContractInfo;
import com.sgs.otsnotes.facade.model.info.testline.*;
import com.sgs.otsnotes.facade.model.info.trims.LabSectionBaseInfo;
import com.sgs.otsnotes.facade.model.info.trims.TestAnalyteLanguage;
import com.sgs.otsnotes.facade.model.info.trims.TestLineInfo;
import com.sgs.otsnotes.facade.model.kafka.GeneralMessage;
import com.sgs.otsnotes.facade.model.kafka.KafkaTopicConsts;
import com.sgs.otsnotes.facade.model.po.SubcontractRelInfoPO;
import com.sgs.otsnotes.facade.model.po.TestLineReportPo;
import com.sgs.otsnotes.facade.model.req.*;
import com.sgs.otsnotes.facade.model.req.analyte.AnalyteDetailListReq;
import com.sgs.otsnotes.facade.model.req.citation.CitationListReq;
import com.sgs.otsnotes.facade.model.req.condition.*;
import com.sgs.otsnotes.facade.model.req.dataentry.ReturnTestLineReq;
import com.sgs.otsnotes.facade.model.req.matrix.DeleteTestReq;
import com.sgs.otsnotes.facade.model.req.order.OrderPpTestLineReq;
import com.sgs.otsnotes.facade.model.req.report.GetTestLineConclusionReq;
import com.sgs.otsnotes.facade.model.req.sample.SaveAssignSampleReq;
import com.sgs.otsnotes.facade.model.req.subcontract.GetOrderInfoRsp;
import com.sgs.otsnotes.facade.model.req.testLine.*;
import com.sgs.otsnotes.facade.model.req.trims.TrimsTestLineReq;
import com.sgs.otsnotes.facade.model.rsp.*;
import com.sgs.otsnotes.facade.model.rsp.master.PreOrder;
import com.sgs.otsnotes.facade.model.rsp.matrix.MatrixTableListRsp;
import com.sgs.otsnotes.facade.model.rsp.matrix.TestMatrixWithTestLineRsp;
import com.sgs.otsnotes.facade.model.rsp.matrix.ToStarlimsTableListRsp;
import com.sgs.otsnotes.facade.model.rsp.report.GetTestLineConclusionRsp;
import com.sgs.otsnotes.facade.model.rsp.report.ReportTestLineConclusionDTO;
import com.sgs.otsnotes.facade.model.rsp.subcontract.SampleRsp;
import com.sgs.otsnotes.facade.model.rsp.subcontract.SubContractPrintRsp;
import com.sgs.otsnotes.facade.model.rsp.subcontract.TestLineInstanceSubContractRsp;
import com.sgs.otsnotes.facade.model.rsp.testLine.QueryTestLineRsp;
import com.sgs.otsnotes.facade.model.rsp.testLine.*;
import com.sgs.otsnotes.facade.model.rsp.trims.ProductLineRsp;
import com.sgs.otsnotes.facade.model.testmatrix.info.OrderTestMatrixInfo;
import com.sgs.otsnotes.facade.model.testmatrix.req.OrderTestMatrixReq;
import com.sgs.otsnotes.integration.*;
import com.sgs.otsnotes.integration.gpo.GPOTestLineTempClient;
import com.sgs.otsnotes.integration.trimslocal.CitationClient;
import com.sgs.otsnotes.integration.trimslocal.LabSectionClient;
import com.sgs.otsnotes.integration.trimslocal.PpClient;
import com.sgs.otsnotes.integration.trimslocal.WorkingInstructionClient;
import com.sgs.preorder.facade.OrderFacade;
import com.sgs.preorder.facade.model.dto.order.OrderAllDTO;
import com.sgs.preorder.facade.model.dto.order.OrderInfoDto;
import com.sgs.preorder.facade.model.enums.CustomerWeightType;
import com.sgs.preorder.facade.model.enums.OperationType;
import com.sgs.preorder.facade.model.info.customer.CustomerBuyerGroupInfo;
import com.sgs.preorder.facade.model.info.user.UserLabBuInfo;
import com.sgs.preorder.facade.model.req.BuParamReq;
import com.sgs.preorder.facade.model.req.JobInfoReq;
import com.sgs.preorder.facade.model.req.OrderIdReq;
import com.sgs.preorder.facade.model.req.SysStatusReq;
import com.sgs.preorder.facade.model.req.job.JobReq;
import com.sgs.preorder.facade.model.rsp.SlOrderInfoRep;
import com.sgs.priceengine.facade.QuotationFacade;
import com.sgs.priceengine.facade.model.DTO.fcmmodel.FcmSimpleTestLine;
import com.sgs.priceengine.facade.model.request.OrderIdRequest;
import com.sgs.trimslocal.facade.ICitationFacade;
import com.sgs.trimslocal.facade.IPpFacade;
import com.sgs.trimslocal.facade.ITestLineFacade;
import com.sgs.trimslocal.facade.model.citation.req.CitationInfoReq;
import com.sgs.trimslocal.facade.model.citation.rsp.CitationInfoRsp;
import com.sgs.trimslocal.facade.model.labsection.rsp.GetLabSectionBaseInfoRsp;
import com.sgs.trimslocal.facade.model.pp.req.SearchPpInfoReq;
import com.sgs.trimslocal.facade.model.pp.rsp.GetRootPPSectionInfoRsp;
import com.sgs.trimslocal.facade.model.pp.rsp.PpLanguageRsp;
import com.sgs.trimslocal.facade.model.pp.rsp.SearchPpInfoRsp;
import com.sgs.trimslocal.facade.model.testline.info.TestLineLabSectionNameInfo;
import com.sgs.trimslocal.facade.model.testline.req.QueryPpTestLineReq;
import com.sgs.trimslocal.facade.model.testline.rsp.*;
import com.sgs.trimslocal.facade.model.workinginstruction.rsp.WorkingInstructionRsp;
import com.sgs.user.facade.domain.dimention.ProductLine;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.StopWatch;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.util.concurrent.ListenableFutureCallback;
import org.springframework.web.bind.annotation.RequestBody;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.sgs.otsnotes.facade.model.enums.TestLineStatus.Entered;
import static com.sgs.otsnotes.facade.model.enums.TestLineStatus.Typing;
import static java.util.stream.Collectors.collectingAndThen;
import static java.util.stream.Collectors.toCollection;

/**
 *
 */
@Service
public class TestLineService {
    private static final Logger logger = LoggerFactory.getLogger(TestLineService.class);

    @Autowired
    private OrderMapper orderMapper;
    @Autowired
    private TestLineMapper testLineMapper;
    @Autowired
    private ReportTempFacade reportTempFacade;
    @Autowired
    private PPBaseMapper ppBaseMapper;
    @Autowired
    private TestLineBaseMapper testLineBaseMapper;
    @Autowired
    private TestSampleExtMapper testSampleMapper;
    @Autowired
    private OrderClient orderClient;
    @Autowired
    private FrameWorkClient frameWorkClient;
    @Autowired
    private FileClient fileClient;
    @Autowired
    private ThreadPoolContextTaskExecutor taskExecutor;
    @Autowired
    private SubContractTestLineMappingMapper subContractTestLineMappingMapper;
    @Autowired
    private JobTestLineRelationshipInfoMapper jobTestLineRelationshipInfoMapper;
    @Autowired
    private JobInfoMapper jobInfoMapper;
    @Autowired
    private TransactionTemplate transactionTemplate;
    @Autowired
    private StatusClient statusClient;
    @Autowired
    private JobClient jobClient;
    @Autowired
    private TestMatrixInfoMapper testMatrixInfoMapper;
    @Autowired
    private SubContractMapper subContractMapper;
    @Autowired
    private SubContractExtMapper subContractExtMapper;
    @Autowired
    private SubcontractConfig subcontractConfig;
    @Autowired
    private PreOrderClient preOrderClient;
    @Autowired
    private CustomerClient customerClient;
    @Autowired
    private PPTestLineRelMapper ppTestLineRelMapper;
    @Autowired
    private TestLineTatConfigMapper testLineTatConfigMapper;
    @Autowired
    private OrderLanguageRelInfoMapper orderLanguageRelInfoMapper;
    @Autowired
    private OrderLanguageRelMapper orderLanguageRelMapper;
    @Autowired
    private TestLineCitationLangMapper testLineCitationLangMapper;
    @Autowired
    private TestDataMapper testDataMapper;
    @Autowired
    private TestLineInstanceMapper testLineInstanceMapper;
    @Autowired
    private GeneralOrderInstanceInfoMapper generalOrderInstanceInfoMapper;
    @Autowired
    private  TestMatrixService testMatrixService;
    @Autowired
    private PPMapper ppMapper;
    @Autowired
    private TestMatrixMapper testMatrixMapper;
    @Autowired
    private ConclusionMapper conclusionMapper;
    @Autowired
    private ReportInfoMapper reportInfoMapper;
    @Autowired
    private ReportMapper reportMapper;
    @Autowired
    private JobExtMapper jobExtMapper;
    @Autowired
    private ArtifactCitationRelInfoMapper testLineCitationRelInfoMapper;
    @Autowired
    private ArtifactCitationLangInfoMapper testLineCitationLanguageInfoMapper;
    @Autowired
    private AnalyteMapper analyteMapper;
    @Autowired
    private OrderLangRelService orderLangRelService;
    @Autowired
    private TestSampleInfoMapper testSampleInfoMapper;
    @Autowired
    private TrimsClient trimsClient;
    @Autowired
    private ITestLineFacade testLineFacade;
    @Autowired
    private JobService jobService;
    @Autowired
    private OrderCitationRelService orderCitationRelService;
    @Resource
    private Producer producer;
    @Autowired
    private ReferDataRelationshipMapper referDataRelationshipMapper;
    @Autowired
    private PPArtifactRelService ppArtifactRelService;
    @Autowired
    private PPSectionMapper ppSectionMapper;
    @Autowired
    private TokenClient tokenClient;
    @Autowired
    private TestLineInstanceExtMapper testLineInstanceExtMapper;
    @Autowired
    private TestLineLocalService testLineLocalService;
    @Autowired
    private ProtocolPackageTestLineRelationshipService protocolPackageTestLineRelationshipService;
    @Autowired
    private PPSampleRelMapper ppSampleRelMapper;
    @Autowired
    private SampleClaimRelExtMapper sampleClaimRelExtMapper;
    @Autowired
    private ReportMatrixRelationShipInfoMapper reportMatrixRelationShipInfoMapper;
    @Autowired
    private ReportService reportService;
    @Autowired
    private TrimsLocalTestLineService trimsLocalTestLineService;
    @Autowired
    private CodeUtil codeUtil;
    @Autowired
    private UserManagementClient userManagementClient;
    @Autowired
    private ReportMatrixRelMapper reportMatrixRelMapper;
    @Autowired
    private PPTestLineRelationshipInfoMapper ppTestLineRelationshipInfoMapper;
    @Autowired
    private CitationClient citationClient;
    @Autowired
    private PpClient ppClient;
    @Autowired
    private LabSectionClient labSectionClient;
    @Autowired
    private WorkingInstructionClient workingInstructionClient;
    @Autowired
    private OrderTestLineService orderTestLineService;
    @Autowired
    private IPpFacade ppFacade;
    @Autowired
    private ICitationFacade citationFacade;
    @Autowired
    private CitationUtil citationUtil;
    @Autowired
    private ICitationService citationService;
    @Autowired
    private IAnalyteService analyteService;
    @Autowired
    private BizLogClient bizLogClient;
    @Autowired
    private ISubContractExternalRelService subContractExternalRelService;
    @Autowired
    private SubContractOperateService subContractOperateService;
    @Autowired
    private OrderSubcontractRelMapper subcontractRelMapper;
    @Autowired
    private QuotationFacade quotationFacade;
    @Autowired
    private TestLineCmdService testLineCmdService;
    @Autowired
    private TestConditionService testConditionService;
    @Autowired
    private TestLineQueryService testLineQueryService;
    @Autowired
    private MessageUtil messageUtil;
    @Autowired
    private TestLineStatusService testLineStatusService;
    @Autowired
    private ITestLineStatusService testLineStatusServiceNew;
    @Autowired
    private TestLineInstanceMultipleLanguageInfoMapper testLineInstanceMultipleLanguageInfoMapper;
    @Autowired
    private OrderFacade orderFacade;
    @Autowired
    private GeneralOrderInstanceMapper generalOrderInstanceMapper;
    @Autowired
    private GPOTestLineTempClient gpoTestLineTempClient;
    @Autowired
    private UserClient userClient;
    @Autowired
    private TestMatrixExtMapper testMatrixExtMapper;
    @Autowired
    private OrderTestLineVMExtMapper orderTestLineVMExtMapper;
    @Autowired
    private SampleService sampleService;
    @Autowired
    private SystemLogHelper systemLogHelper;
    /*@Autowired
    private IGpnReportService gpnReportService;*/

    /**
     *
     * @param reqObject
     * @return
     */
    public CustomResult getPPList(QueryPPReq reqObject,Integer page,Integer rows){
        CustomResult rspResult = new CustomResult();
        GeneralOrderInstanceInfoPO order = orderMapper.getOrderInfoByOrderId(reqObject.getOrderId());
        if (order == null){
            rspResult.setMsg("Order is  null.");
            return rspResult;
        }
        if(!(StringUtils.isNotEmpty(reqObject.getClient())||StringUtils.isNotEmpty(reqObject.getClientPPRef())||StringUtils.isNotEmpty(reqObject.getProductTaxonomy())
        ||(reqObject.getPpNo()!=null&&reqObject.getPpNo().intValue()>0) || Func.isNotEmpty(reqObject.getCitationVersionId()))){
            rspResult.setMsg("At least one query parameter");
            return rspResult;
        }

        String customerGroupCode = StringUtils.EMPTY;
        if (StringUtils.isNotBlank(reqObject.getClient())) {
            customerGroupCode = order.getCustomerGroupCode();
            CustomResult<CustomerBuyerGroupInfo> customResult=customerClient.getCustomerWeightInfo(order.getOrderNo(), CustomerWeightType.TestPackage);
            if(customResult.isSuccess()&&customResult.getData()!=null){
                customerGroupCode = customResult.getData().getBuyerGroup();
            }
        }

        List<QueryPPRsp> pps = Lists.newArrayList();
        SearchPpInfoReq searchPpInfoReq = new SearchPpInfoReq();
        searchPpInfoReq.setCallerBU(ProductLineContextHolder.getProductLineCode());
        searchPpInfoReq.setPpNo(reqObject.getPpNo());
        searchPpInfoReq.setProductTaxonomy(reqObject.getProductTaxonomy());
        searchPpInfoReq.setPpName(reqObject.getClientPPRef());
        searchPpInfoReq.setCustomerGroupCode(customerGroupCode);
        searchPpInfoReq.setLanguageIds(Arrays.asList(1,2));
        searchPpInfoReq.setPpType(Lists.newArrayList(0,1));
        if(Func.isNotEmpty(reqObject.getBuCode())){
            searchPpInfoReq.setProductLineId(Integer.parseInt(reqObject.getBuCode()));
        }
        if(Func.isNotEmpty(reqObject.getCitationVersionId())){
            searchPpInfoReq.setCitationVersionId(reqObject.getCitationVersionId());
        }
        if(Func.isNotEmpty(reqObject.getCitationType())){
            searchPpInfoReq.setCitationType(reqObject.getCitationType());
        }
        searchPpInfoReq.setPageIndex(Func.toInt(page,1));
        searchPpInfoReq.setPageSize(Func.toInt(rows,100));
        if(Func.isNotEmpty(reqObject.getPhaseOutFlag()) && reqObject.getPhaseOutFlag()){
            searchPpInfoReq.setStatues(Arrays.asList(1,2));
        }
        if (StringUtils.isNotBlank(customerGroupCode)) {
            Integer customerAccountId = customerClient.getCustomerAccountId(customerGroupCode);
            if (customerAccountId == null || customerAccountId == 0) {
                rspResult.setSuccess(true);
                rspResult.setData(pps);
                return rspResult;
            } else {
                searchPpInfoReq.setCustomerAccountId(customerAccountId);
            }
        }
        logger.info("getPpList from local trims:{}",JSON.toJSONString(searchPpInfoReq));

        com.sgs.trimslocal.facade.model.common.BaseResponse ppListResponse = ppFacade.getPpList(searchPpInfoReq);
        long total = 0;
        if(Func.isNotEmpty(ppListResponse) && Func.isNotEmpty(ppListResponse.getData())){
            PageInfo<SearchPpInfoRsp> searchPpInfoRsp = (PageInfo) ppListResponse.getData();
            List<SearchPpInfoRsp> ppInfoList = searchPpInfoRsp.getList();
            total = searchPpInfoRsp.getTotal();
            //获取客户主语言
//            String defaultLanguageCode = tokenClient.getUser().getDefaultLanguageCode();
            String defaultLanguageCode = frameWorkClient.getPrimaryLanguageCode(ProductLineContextHolder.getProductLineCode());
            Integer languageId = UserInfoDefaultLanguageCodeEnums.getIdByCode(defaultLanguageCode);
            List<Integer> csppTls=ppInfoList.stream().filter(e->PpType.check(e.getPpType(),PpType.CCPP)).map(e1->e1.getPpVersionId()).collect(Collectors.toList());
            List<com.sgs.trimslocal.facade.model.testline.rsp.QueryTestLineRsp> queryTestLineRspList=Lists.newArrayList();
            if(Func.isNotEmpty(csppTls)){
                queryTestLineRspList=this.getTlList(csppTls);
            }
            for (SearchPpInfoRsp ppInfoRsp : ppInfoList) {
                QueryPPRsp queryPPRsp = new QueryPPRsp();
                queryPPRsp.setPpId(ppInfoRsp.getPpBaseId());
                queryPPRsp.setPpNo(ppInfoRsp.getPpNo());
                queryPPRsp.setVersionId(ppInfoRsp.getPpVersionId());
                queryPPRsp.setProductLineCode(reqObject.getProductLineCode());
                queryPPRsp.setPpStatus(ppInfoRsp.getPpStatus());
                queryPPRsp.setPpType(ppInfoRsp.getPpType());
                if(PpType.check(ppInfoRsp.getPpType(),PpType.CCPP)){
                    com.sgs.trimslocal.facade.model.testline.rsp.QueryTestLineRsp queryTestLineRsp=queryTestLineRspList.stream().filter(e->Func.equalsSafe(e.getTestLineVersionId(),ppInfoRsp.getPpVersionId())).findFirst().orElse(null);
                    if(queryTestLineRsp==null){
                        throw new BizException("CSPP is not found");
                    }
                    queryPPRsp.setPpId(queryTestLineRsp.getArtifactBaseId());
                }
                List<PPArtifactCitationRelInfoRsp> citations = ppInfoRsp.getCitations();
                if(Func.isNotEmpty(citations)){
                    if(PpType.check(ppInfoRsp.getPpType(),PpType.CCPP)){
                        setStandardListForCspp(reqObject, languageId, queryPPRsp, citations);
                    }
                    List<PPCitationLangRsp> citationLanguagesRsps = citations.get(0).getLanguages();
                    if(Func.isNotEmpty(citationLanguagesRsps)){
                        Map<Integer, PPCitationLangRsp> languageRspMap = citationLanguagesRsps.stream().collect(Collectors.toMap(PPCitationLangRsp :: getLanguageId, o -> o,(key1 , key2)-> key2 ));
                        if(languageRspMap.containsKey(languageId) && languageRspMap.get(languageId)!=null && Func.isNotEmpty(languageRspMap.get(languageId).getCitationName())){
                            PPCitationLangRsp lineCitationLanguagesRsp = languageRspMap.get(languageId);
                            String standardName = lineCitationLanguagesRsp.getCitationName();
                            queryPPRsp.setStandardName(standardName);
                        }else{
                            String standardName = citations.get(0).getCitationName();
                            queryPPRsp.setStandardName(standardName);
                        }
                    }else{
                        String standardName = citations.get(0).getCitationName();
                        queryPPRsp.setStandardName(standardName);
                    }
                }
                //   2022/5/19  双语
                boolean needDoubleLanguage = frameWorkClient.needDoubleLanguage(ProductLineContextHolder.getProductLineCode());

                List<PpLanguageRsp> languages = ppInfoRsp.getLanguages();
                if(Func.isNotEmpty(languages)){
                    Map<Integer, PpLanguageRsp> languageRspMap = languages.stream().collect(Collectors.toMap(PpLanguageRsp :: getLanguageId, o -> o,(key1 , key2)-> key2 ));
                    //根据客户主语言获取PPName，主语言为中文，则获取中文数据，中文数据为空，则使用英文数据
                    if(needDoubleLanguage){
                        if(Func.isNotEmpty(languageRspMap) && languageRspMap.containsKey(2)){
                            if(Func.isNotEmpty(languageRspMap.get(2)) && Func.isNotEmpty(languageRspMap.get(2).getPpName()) && Func.isNotEmpty(ppInfoRsp.getPpName())){
                                queryPPRsp.setPpNameCn(languageRspMap.get(2).getPpName());
                                queryPPRsp.setPpName(ppInfoRsp.getPpName());
                            }else if(Func.isNotEmpty(languageRspMap.get(2)) && Func.isNotEmpty(languageRspMap.get(2).getPpName()) && Func.isEmpty(ppInfoRsp.getPpName())){
                                queryPPRsp.setPpNameCn(languageRspMap.get(2).getPpName());
                                queryPPRsp.setPpName(languageRspMap.get(2).getPpName());
                            }else if((Func.isEmpty(languageRspMap.get(2)) || Func.isEmpty(languageRspMap.get(2).getPpName()))&& Func.isNotEmpty(ppInfoRsp.getPpName())){
                                queryPPRsp.setPpNameCn(ppInfoRsp.getPpName());
                                queryPPRsp.setPpName(ppInfoRsp.getPpName());
                            }else{
                                queryPPRsp.setPpNameCn(languageRspMap.get(2).getPpName());
                                queryPPRsp.setPpName(ppInfoRsp.getPpName());
                            }
                        }else{
                            queryPPRsp.setPpNameCn(ppInfoRsp.getPpName());
                            queryPPRsp.setPpName(ppInfoRsp.getPpName());
                        }
                    }else{
                        if(languageRspMap.containsKey(languageId) && languageRspMap.get(languageId)!=null && Func.isNotEmpty(languageRspMap.get(languageId).getPpName())){
                            PpLanguageRsp ppLanguageRsp = languageRspMap.get(languageId);
                            queryPPRsp.setPpName(ppLanguageRsp.getPpName());
                        }else{
                            queryPPRsp.setPpName(ppInfoRsp.getPpName());
                        }
                    }
                }else{
                    queryPPRsp.setPpName(ppInfoRsp.getPpName());
                    queryPPRsp.setPpNameCn(ppInfoRsp.getPpName());
                }
                pps.add(queryPPRsp);
            }
        }
        rspResult.setSuccess(true);
        PageInfo pageInfo = PageInfo.of(pps);
        pageInfo.setTotal(total);
        pageInfo.setPageNum(page);
        pageInfo.setPageSize(rows);
        rspResult.setData(pageInfo);
        return rspResult;
    }

    private void setStandardListForCspp(QueryPPReq reqObject, Integer languageId, QueryPPRsp queryPPRsp, List<PPArtifactCitationRelInfoRsp> citations) {
        List<StandardRsp> standards = new ArrayList<>();
        List<StandardLanguageRsp> standardLanguageRspList = new ArrayList<>();
        for (PPArtifactCitationRelInfoRsp citation : citations) {
//                    queryTestLineListRsp.setEvaluationAlias(citation.getEvaluationAlias());
            List<PPCitationLangRsp> languages = citation.getLanguages();
            StandardRsp standardRsp = new StandardRsp();
            standardRsp.setOriginCitationName(citation.getCitationName());
            if(Func.isNotEmpty(languages)){
                PPCitationLangRsp testLineCitationLanguagesRsp = languages.stream().filter(i -> Func.equals(i.getLanguageId(), languageId)).findFirst().orElse(null);
                //根据客户主语言获取，主语言为中文，则获取中文数据，中文数据为空，则使用英文数据
                if(Func.isNotEmpty(testLineCitationLanguagesRsp)){
                    standardRsp.setCitationName(testLineCitationLanguagesRsp.getCitationName());
                    standardRsp.setStandardName(testLineCitationLanguagesRsp.getCitationName());
                }
                for (PPCitationLangRsp languageRsp : languages) {
                    StandardLanguageRsp standardLanguageRsp = new StandardLanguageRsp();
                    standardLanguageRsp.setLanguageId(languageRsp.getLanguageId());
                    standardLanguageRsp.setCitationName(languageRsp.getCitationName());
                    standardLanguageRspList.add(standardLanguageRsp);
                }
            }
            if(Func.isEmpty(standardRsp.getCitationName())){
                standardRsp.setCitationName(citation.getCitationName());

            }
            if(Func.isEmpty(standardRsp.getStandardName())){
                standardRsp.setStandardName(citation.getCitationName());
            }
            standardRsp.setCitationSeq(citation.getCitationSeq());
            standardRsp.setCitationBaseId(citation.getCitationBaseId());
            standardRsp.setCitationVersionId(citation.getCitationVersionId());
            standardRsp.setStandardId(citation.getCitationId());
            standardRsp.setiSDefault(false);
            standardRsp.setCitationType(citation.getCitationType());
            standardRsp.setStandardLanguageRspList(standardLanguageRspList);
            standards.add(standardRsp);
        }
        if (Func.isNotEmpty(standards)){
            standards.sort(new StandardComparator(true));
            queryPPRsp.setStandardVersionIdSelected(standards.stream().findFirst().get().getCitationVersionId());
            if(Func.isNotEmpty(reqObject.getCitationVersionId())){
                List<StandardRsp> standardRspList=standards.stream().filter(e->Func.isNotEmpty(e.getCitationVersionId())&&e.getCitationVersionId().equals(reqObject.getCitationVersionId())).collect(Collectors.toList());
                if(Func.isNotEmpty(standardRspList)){
                    standardRspList.sort(new StandardComparator(true));
                    queryPPRsp.setStandardVersionIdSelected(standardRspList.get(0).getCitationVersionId());
                }
            }
            queryPPRsp.setCitationList(standards);
        }
    }

    /**
     *
     * @param reqObject
     * @return
     */
    public CustomResult getPPTestLineList(@RequestBody QueryPPTestLineReq reqObject){
        CustomResult rspResult = new CustomResult();
        Long ppId = reqObject.getPpId();
        if (ppId == null || ppId.longValue() <= 0){
            rspResult.setMsg("PpId is not null.");
            return rspResult;
        }
        GeneralOrderInstanceInfoPO order = orderMapper.getOrderInfoByOrderId(reqObject.getOrderId());
        if (order == null){
            rspResult.setMsg("Order is not null.");
            return rspResult;
        }

        //
        PPTestLineRelationshipInfoExample ppTestLineRelationshipInfoExample = new PPTestLineRelationshipInfoExample();
        ppTestLineRelationshipInfoExample.createCriteria().andGeneralOrderInstanceIDEqualTo(reqObject.getOrderId());
        List<PPTestLineRelationshipInfoPO> ppTestLineRelationshipInfoPOS = ppTestLineRelationshipInfoMapper.selectByExample(ppTestLineRelationshipInfoExample);

        List<QueryTestLineRsp> ppTestlines= Lists.newArrayList();

        //  修改为调用local Trims接口查询
        QueryPpTestLineReq queryPpTestLineReq = new QueryPpTestLineReq();
        queryPpTestLineReq.setCallerBU(ProductLineContextHolder.getProductLineCode());
        queryPpTestLineReq.setLabId(order.getOrderLaboratoryID());
        queryPpTestLineReq.setPpBaseId(reqObject.getPpId());
        queryPpTestLineReq.setPpVersionId(reqObject.getVersionId());
        queryPpTestLineReq.setLanguageIds(Arrays.asList(1,2));
        if(Func.isNotEmpty(reqObject.getPhaseOutFlag()) && reqObject.getPhaseOutFlag()){
            queryPpTestLineReq.setStatues(Arrays.asList(1,2));
        }
        logger.info("queryPpTestLineReq from local trims:{}",JSON.toJSONString(queryPpTestLineReq));
        com.sgs.trimslocal.facade.model.common.BaseResponse<List<QueryPpTestLineRsp>> ppTestLineListRes = testLineFacade.getPpTestLineList(queryPpTestLineReq);
        if(Func.isNotEmpty(ppTestLineListRes) && Func.isNotEmpty(ppTestLineListRes.getData())){
            List<QueryPpTestLineRsp> ppTestLineList = Lists.newArrayList();
            PageInfo pageInfo = (PageInfo) ppTestLineListRes.getData();
            if(Func.isNotEmpty(pageInfo)){
                 ppTestLineList = pageInfo.getList();
            }
            if(Func.isEmpty(ppTestlines)){
                ppTestlines = new ArrayList<>();
            }
            //获取客户主语言
//            String defaultLanguageCode = tokenClient.getUser().getDefaultLanguageCode();
              String defaultLanguageCode = frameWorkClient.getPrimaryLanguageCode(ProductLineContextHolder.getProductLineCode());
            boolean needDoubleLanguage = frameWorkClient.needDoubleLanguage(ProductLineContextHolder.getProductLineCode());
            List<TestLineInstancePO> testLineInstancePOList = new ArrayList<>();
            if(Func.isNotEmpty(ppTestLineRelationshipInfoPOS)){
                List<String> testLineInstanceIdList = ppTestLineRelationshipInfoPOS.stream().map(PPTestLineRelationshipInfoPO::getTestLineInstanceID).distinct().filter(Func::isNotEmpty).collect(Collectors.toList());
                if(Func.isNotEmpty(testLineInstanceIdList)){
                    TestLineInstanceExample testLineInstanceExample = new TestLineInstanceExample();
                    testLineInstanceExample.createCriteria().andIDIn(testLineInstanceIdList);
                    testLineInstancePOList = testLineInstanceMapper.selectByExample(testLineInstanceExample);
                }
            }


            for (QueryPpTestLineRsp queryPpTestLineRsp : ppTestLineList) {
                QueryTestLineRsp queryTestLineRsp = new QueryTestLineRsp();
                queryTestLineRsp.setCitationVersionId(queryPpTestLineRsp.getCitationVersionId());
                queryTestLineRsp.setSectionBaseId(queryPpTestLineRsp.getSectionBaseId());
                queryTestLineRsp.setPpId(queryPpTestLineRsp.getPpBaseId());
                queryTestLineRsp.setSectionLevel(queryPpTestLineRsp.getSectionLevel());
                queryTestLineRsp.setArtifactBaseId(queryPpTestLineRsp.getArtifactBaseId());
                queryTestLineRsp.setArtifactType(queryPpTestLineRsp.getArtifactType());
                queryTestLineRsp.setPpVersionId(queryPpTestLineRsp.getPpVersionId());
                queryTestLineRsp.setRootPpBaseId(queryPpTestLineRsp.getRootPpBaseId());
                queryTestLineRsp.setAid(queryPpTestLineRsp.getAid());
                queryTestLineRsp.setConstructionId(queryPpTestLineRsp.getConstructionId());
                queryTestLineRsp.setTestLineId(queryPpTestLineRsp.getTestLineId());
                queryTestLineRsp.setTestLineVersionId(queryPpTestLineRsp.getTestLineVersionId());
                queryTestLineRsp.setStandardId(queryPpTestLineRsp.getCitationId());
                queryTestLineRsp.setLabSectionName(queryPpTestLineRsp.getLabSectionName());
                List<TestLineLabSectionNameInfo> trimsLabSections = queryPpTestLineRsp.getLabSections();
                if(Func.isNotEmpty(trimsLabSections)){
                    List<LabSectionBaseInfo> labSections = Lists.newArrayList();
                    trimsLabSections.stream().forEach(l->{
                        LabSectionBaseInfo labSectionBaseInfo = new LabSectionBaseInfo();
                        labSectionBaseInfo.setLabSectionId(Func.toLong(l.getLabSectionId()));
                        labSectionBaseInfo.setLabSectionName(l.getLabSectionName());
                        labSectionBaseInfo.setLabSectionBaseId(l.getLabSectionBaseId());
                        labSections.add(labSectionBaseInfo);
                    });
                    queryTestLineRsp.setLabSections(labSections);
                    if(Func.isNotEmpty(labSections)&&labSections.size()==1){
                        queryTestLineRsp.setLabSectionId(Func.toLong(labSections.get(0).getLabSectionId()));
                        queryTestLineRsp.setLabSectionName(labSections.get(0).getLabSectionName());
                    }
                }


                queryTestLineRsp.setPpNo(queryPpTestLineRsp.getPpNo());
                queryTestLineRsp.setTlExecutionClassificationCode(queryPpTestLineRsp.getTlExecutionClassificationCode());
                queryTestLineRsp.setTestLineBaseId(queryPpTestLineRsp.getTestLineBaseId());
                queryTestLineRsp.setTestLineSeq(queryPpTestLineRsp.getTestLineSeq());
                queryTestLineRsp.setCitationBaseId(queryPpTestLineRsp.getCitationBaseId());
                queryTestLineRsp.setStandardName(queryPpTestLineRsp.getCitationName());
                queryTestLineRsp.setProductLineCode(reqObject.getProductLineCode());
                queryTestLineRsp.setTestLineType(queryPpTestLineRsp.getTestLineType());
                queryTestLineRsp.setTestLineStatus(queryPpTestLineRsp.getTestLineStatus());
                queryTestLineRsp.setCitationSectionId(queryPpTestLineRsp.getCitationSectionId());
                queryTestLineRsp.setCitationType(queryPpTestLineRsp.getCitationType());
                logger.info(Func.toStr(queryPpTestLineRsp.getAid()));
                if(Func.isNotEmpty(ppTestLineRelationshipInfoPOS) && Func.isNotEmpty(testLineInstancePOList)){
                    PPTestLineRelationshipInfoPO ppTestLineRelationshipInfoPO = ppTestLineRelationshipInfoPOS.stream().filter(p -> Func.isNotEmpty(queryPpTestLineRsp.getAid()) && Func.isNotEmpty(p.getAid()) && Func.equals(Func.toStr(queryPpTestLineRsp.getAid()),Func.toStr(p.getAid()))).findFirst().orElse(null);
                    if(Func.isNotEmpty(ppTestLineRelationshipInfoPO)){
                        TestLineInstancePO testLineInstancePO = testLineInstancePOList.stream().filter(tl -> Func.equals(ppTestLineRelationshipInfoPO.getTestLineInstanceID(), tl.getID())).findFirst().orElse(null);
                        if(Func.isNotEmpty(testLineInstancePO) && !TestLineStatus.check(testLineInstancePO.getTestLineStatus(),TestLineStatus.Cancelled)){
                            queryTestLineRsp.setExistFlag(true);
                        }
                    }
                }

                List<PpTestLineLangRsp> languages = queryPpTestLineRsp.getLanguages();
                Map<Integer, PpTestLineLangRsp> languageRspMap = languages.stream().collect(Collectors.toMap(PpTestLineLangRsp :: getLanguageId, o -> o,(key1 , key2)-> key2 ));
                Integer testLineType = queryPpTestLineRsp.getTestLineType();
                if(Func.isNotEmpty(testLineType)&& Func.equalsSafe(testLineType,Constants.OOB_TEST_LINE_TYPE)){
                    queryTestLineRsp.setCustomerTestLineName(queryPpTestLineRsp.getEvaluationAlias());
                    PpTestLineLangRsp ppTestLineLangRspCN = languageRspMap.get(LanguageType.Chinese.getLanguageId());
                    if(Func.isNotEmpty(ppTestLineLangRspCN)){
                        queryTestLineRsp.setCustomerTestLineNameCN(ppTestLineLangRspCN.getEvaluationAlias());
                    }
                }
                Integer languageId = UserInfoDefaultLanguageCodeEnums.getIdByCode(defaultLanguageCode);

                if(needDoubleLanguage){
                    if(Func.isNotEmpty(languageRspMap) && languageRspMap.containsKey(2) && Func.isNotEmpty(languageRspMap.get(2).getEvaluationAlias())){
                        PpTestLineLangRsp chiPpTestLine = languageRspMap.get(2);
                        if(Func.isNotEmpty(queryPpTestLineRsp.getEvaluationAlias())){
                           queryTestLineRsp.setEvaluationAliasCn(chiPpTestLine.getEvaluationAlias());
                           queryTestLineRsp.setEvaluationAlias(queryPpTestLineRsp.getEvaluationAlias());
                       }else{
                            queryTestLineRsp.setEvaluationAliasCn(chiPpTestLine.getEvaluationAlias());
                            queryTestLineRsp.setEvaluationAlias(chiPpTestLine.getEvaluationAlias());
                        }
                    }else{
                        queryTestLineRsp.setEvaluationAliasCn(queryPpTestLineRsp.getEvaluationAlias());
                        queryTestLineRsp.setEvaluationAlias(queryPpTestLineRsp.getEvaluationAlias());
                    }
                }


                if(Func.isNotEmpty(languageRspMap)){
                    //根据客户主语言获取，主语言为中文，则获取中文数据，中文数据为空，则使用英文数据
                    if(!needDoubleLanguage && languageRspMap.containsKey(languageId) && languageRspMap.get(languageId)!=null){
                        PpTestLineLangRsp ppTestLineLangRsp = languageRspMap.get(languageId);
                        if(Func.isNotEmpty(languageRspMap.get(languageId).getEvaluationAlias())){
                            queryTestLineRsp.setEvaluationAlias(ppTestLineLangRsp.getEvaluationAlias());
                        }else{
                            queryTestLineRsp.setEvaluationAlias(queryPpTestLineRsp.getEvaluationAlias());
                        }

                        //中英文处理
                        if(Func.isNotEmpty(languageRspMap.get(languageId).getCitationFullName())){
                            queryTestLineRsp.setStandardName(languageRspMap.get(languageId).getCitationFullName());
                        }else {
                            queryTestLineRsp.setStandardName(queryPpTestLineRsp.getCitationFullName());
                        }
                        if(Func.isNotEmpty(languageRspMap.get(languageId).getPpNotes())){
                            queryTestLineRsp.setPpNotes(ppTestLineLangRsp.getPpNotes());
                        }else{
                            queryTestLineRsp.setPpNotes(queryPpTestLineRsp.getPpNotes());
                        }
                        if(Func.isNotEmpty(languageRspMap.get(languageId).getSectionName())){
                            queryTestLineRsp.setSectionName(ppTestLineLangRsp.getSectionName());
                        }else{
                            queryTestLineRsp.setSectionName(queryPpTestLineRsp.getSectionName());
                        }
                    }else{
                        queryTestLineRsp.setPpName(queryPpTestLineRsp.getPpName());
                        queryTestLineRsp.setEvaluationAlias(queryPpTestLineRsp.getEvaluationAlias());
                        queryTestLineRsp.setStandardName(queryPpTestLineRsp.getCitationFullName());
                        queryTestLineRsp.setPpNotes(queryPpTestLineRsp.getPpNotes());
                        queryTestLineRsp.setSectionName(queryPpTestLineRsp.getSectionName());
                    }
                }else{
                    queryTestLineRsp.setPpName(queryPpTestLineRsp.getPpName());
                    queryTestLineRsp.setEvaluationAlias(queryPpTestLineRsp.getEvaluationAlias());
                    queryTestLineRsp.setStandardName(queryPpTestLineRsp.getCitationFullName());
                    queryTestLineRsp.setPpNotes(queryPpTestLineRsp.getPpNotes());
                    queryTestLineRsp.setSectionName(queryPpTestLineRsp.getSectionName());
                }
                ppTestlines.add(queryTestLineRsp);
            }
//            ppTestlines = ppTestlines.stream().sorted(Comparator.comparing(c-> (Func.isEmpty(c.getSectionLevel())?0:Integer.parseInt(c.getSectionLevel())),Comparator.nullsFirst(Comparator.naturalOrder()))).collect(Collectors.toList());
            ppTestlines = ppTestlines.stream().sorted(Comparator.comparing(item -> (Func.isEmpty(item.getSectionLevel())?0:Integer.parseInt(item.getSectionLevel())), Comparator.nullsLast(Integer::compareTo))).collect(Collectors.toList());
        }
        rspResult.setSuccess(true);
        rspResult.setData(ppTestlines);
        return rspResult;
    }
    public CustomResult getCitationInfoList(@RequestBody QueryTestLineReq reqObject){
        CustomResult rspResult = new CustomResult();
        CitationInfoReq citationInfoReq = new CitationInfoReq();
        citationInfoReq.setCitationName(reqObject.getStandardName());
        com.sgs.trimslocal.facade.model.common.BaseResponse<List<CitationInfoRsp>> citationInfoList = citationFacade.getCitationInfoList(citationInfoReq);
        List<CitationInfoRsp> citationInfoListData = new ArrayList<>();
        if( Func.isNotEmpty(citationInfoList) && citationInfoList.getStatus()==200){
            citationInfoListData = citationInfoList.getData();
            rspResult.setSuccess(true);
        }else{
            rspResult.setSuccess(false);
            rspResult.setMsg(citationInfoList.getMessage());
        }
        rspResult.setData(citationInfoListData);
        return rspResult;
    }
    /**
     * 获取所有子PP所对应的RootPP的RootSection信息
     * @param ppVersionId
     * @return key=PpVersionId , value=RootSection
     */
    private Map<Integer, QueryPPSection> getSubPpSectionInfo(Integer ppVersionId,Map<Integer, PPSectionBaseInfoPO> rootSectionMap) {
        Map<Integer, QueryPPSection> subPPToRootSectionMap = Maps.newHashMap();

        // SubPPSection
        List<QueryPPSection> subPP = ppSectionMapper.getSubPPSectionInfoList(ppVersionId);
        // key=subPP <--> value=RootSection
        if (CollectionUtils.isNotEmpty(subPP)) {
            subPP.forEach(sub -> {
                PPSectionBaseInfoPO ppSectionPO = rootSectionMap.get(sub.getSubPpSectionLevel());
                sub.setRootPpSectionId(ppSectionPO.getSectionId());
                sub.setRootPpSectionLevel(ppSectionPO.getSectionLevel());
                sub.setRootPpSectionName(ppSectionPO.getSectionText());
                // subPP 的所有子PP
                Set<Integer> subPpVersionIds = ppArtifactRelService.getSubPpVersionIds(sub.getSubPpVersionId(), Sets.newHashSet());
                subPpVersionIds.forEach(subPPId -> subPPToRootSectionMap.put(subPPId, sub));
            });
        }
        return subPPToRootSectionMap;
    }

    private boolean isChinese() {
//        String defaultLanguageCode = tokenClient.getUser().getDefaultLanguageCode();
        String defaultLanguageCode = frameWorkClient.getPrimaryLanguageCode(ProductLineContextHolder.getProductLineCode());
        return !UserInfoDefaultLanguageCodeEnums.check(defaultLanguageCode,UserInfoDefaultLanguageCodeEnums.en_us);
    }

    /**
     *
     * @param reqObject
     * @return
     */
    public CustomResult getTestLineList(OrderTestLineReq reqObject){
        return getTestLineListByOrderId(reqObject);
    }

    /**
     * 查询订单下TL最大DueDate
     * @param reqObject
     * @return
     */
    public CustomResult getTestLineMaxByOrderNo(OrderTestLineReq reqObject){
        CustomResult rspResult = new CustomResult();
        //校验参数
        logger.info("---->>>",reqObject.getOrderNo());
        if (reqObject == null || StringUtils.isBlank(reqObject.getOrderNo())){
            rspResult.setMsg("OrderNo is not null.");
            return rspResult;
        }
        Date maxDueDate = testLineInstanceExtMapper.getTestLineMaxByOrderNo(reqObject.getOrderNo());
        rspResult.setSuccess(true);
        rspResult.setData(maxDueDate);
        return rspResult;
    }




    private CustomResult<List<OrderTestLineRsp>> getTestLineListByOrderId(OrderTestLineReq reqObject){
        CustomResult rspResult = new CustomResult();
        if (StringUtils.isBlank(reqObject.getOrderId())&&StringUtils.isBlank(reqObject.getOrderNo())){
            rspResult.setMsg("OrderID is not null.");
            return rspResult;
        }
        GeneralOrderInstanceInfoPO order = null;
        if(Func.isNotEmpty(reqObject.getOrderId())) {
            order=orderMapper.getOrderInfoByOrderId(reqObject.getOrderId());
        }else{
            order=orderMapper.getOrderInfoByOrderNo(reqObject.getOrderNo());
        }
        if (order == null){
            rspResult.setMsg("Order is not null..");
            return rspResult;
        }

        List<OrderTestLineRsp> testLines = testLineMapper.getTestLineListByOrderId(reqObject.getOrderId());
        if (CollectionUtils.isEmpty(testLines)){
            rspResult.setData(Lists.newArrayList());
            rspResult.setSuccess(true);
            return rspResult;
        }

        //search sample&matrix info
        List<PPMatrixSampleRelInfo> ppMatrixSampleRelInfos=testSampleMapper.getMatrixSampleInfoByOrderId(reqObject.getOrderId());
        //search matrix info
        TestMatrixInfoExample testMatrixInfoExample=new TestMatrixInfoExample();
        testMatrixInfoExample.createCriteria().andGeneralOrderInstanceIDEqualTo(reqObject.getOrderId());
        List<TestMatrixInfoPO> testMatrixInfoPOs=testMatrixInfoMapper.selectByExample(testMatrixInfoExample);

        //refer data，这里是到matrix级别的判断：取得source order对应的maxtrix,然后跟current order的matrix进行比对
        List<TestMatrixWithTestLineRsp> referSourceMatrixs = Lists.newArrayList();

        ReferDataRelationshipExample referDataRelationshipExample = new ReferDataRelationshipExample();
        referDataRelationshipExample.createCriteria().andCurrentOrderNoEqualTo(order.getOrderNo());
        List<ReferDataRelationshipPO> referDataRelationshipPOS = referDataRelationshipMapper.selectByExample(referDataRelationshipExample);
        if (CollectionUtils.isNotEmpty(referDataRelationshipPOS)){
            List<String> sourceOrderNos = referDataRelationshipPOS.stream().map(x -> x.getSourceOrderNo()).collect(Collectors.toList());
            List<String> sourceSampleIds = referDataRelationshipPOS.stream().map(x -> x.getSourceSampleId()).distinct().collect(Collectors.toList());
            referSourceMatrixs = testMatrixMapper.getReferMatrixByOrderAndSamples(sourceOrderNos, sourceSampleIds);
        }

//        List<ReferDataRelationshipPO> finalReferDataRelationshipPOS = referDataRelationshipPOS;
        List<TestMatrixWithTestLineRsp> finalReferSourceMatrixs = referSourceMatrixs;
        testLines.forEach(testLine->{
            //find
            List<PPMatrixSampleRelInfo> matchedPPMatrixSampleRelInfos=ppMatrixSampleRelInfos.stream().filter(ppMatrixSampleRelInfo -> StringUtil.isBlank(testLine.getId(),"").equals(ppMatrixSampleRelInfo.getPpTLRelId())).collect(Collectors.toList());
            // build sampleNos
            /**
             * TODO Kevin
             * 1、List<String> sampleNos = Lists.newArrayList();
             * 2、if (StringUtils.isNoneBlank(sampleNos)) {
             *   sampleNos = sampleNos.substring(1);
             *  } 改为：StringUtils.join(sampleNos, ",")
             */
            String sampleNos="";
            List<TestMatrixInfoPO> matchedTestMatrixInfoPOs=testMatrixInfoPOs.stream().filter(testMatrixInfoPO -> testLine.getTestLineInstanceId().equals(testMatrixInfoPO.getTestLineInstanceID())).collect(Collectors.toList());
            for (PPMatrixSampleRelInfo ppSampleRelationshipVO : matchedPPMatrixSampleRelInfos) {
                AtomicReference<String> referMarker = new AtomicReference<>(StringUtils.EMPTY);
                finalReferSourceMatrixs.stream().filter(p -> StringUtils.equalsIgnoreCase(p.getCurrentSampleId(), ppSampleRelationshipVO.getTestSampleID()) && Objects.equals(p.getTestLineId(), ppSampleRelationshipVO.getTestLineID()))
                 .findFirst().ifPresent(c -> {
                    referMarker.set(Constants.REFER_STYLE_HIGHLIGHT);
                });
//                finalReferDataRelationshipPOS.stream().filter(p -> StringUtils.equalsIgnoreCase(p.getCurrentSampleID(), ppSampleRelationshipVO.getTestSampleID())).findFirst()
//                        .ifPresent(c -> {
//                            referMarker.set(Constants.REFER_STYLE_HIGHLIGHT);
//                        });

                if (ppSampleRelationshipVO.getIsCancelled() != null
                        && ppSampleRelationshipVO.getIsCancelled() == 0) {
                    sampleNos += ",<font style='text-decoration:line-through;color:red;"+referMarker.get()+"'>"
                            + ppSampleRelationshipVO.getSampleNo() + "</font>";
                } else {
                    // matrix cancel
                    if (CollectionUtils.isNotEmpty(matchedTestMatrixInfoPOs)) {
                        String matrixId = ppSampleRelationshipVO.getMatrixId();
                        List<TestMatrixInfoPO> list = matchedTestMatrixInfoPOs.stream().filter(
                                vo2 -> vo2.getID().equals(matrixId) && !vo2.getActiveIndicator())
                                .collect(Collectors.toList());
                        if (list != null && list.size() > 0) {
                            //业务上，此段代码不会执行
                            sampleNos += ",<label style='background-color:red;'>"
                                    + ppSampleRelationshipVO.getSampleNo() + "</label>";
                        } else {
                            sampleNos += ",<font style='"+referMarker.get()+"'>" + ppSampleRelationshipVO.getSampleNo()+"</font>";
                        }
                    } else {
                        sampleNos += ",<font style='"+referMarker.get()+"'>" + ppSampleRelationshipVO.getSampleNo()+"</font>";
                    }
                }
            }
            if (StringUtils.isNoneBlank(sampleNos)) {
                sampleNos = sampleNos.substring(1);
            }
            testLine.setSampleNos(sampleNos);

        });

        // 获取rootPpName
        Map<Long, String> rootPpNameMap = Collections.EMPTY_MAP;
        List<Long> rootBaseIds = testLines.stream()
                .filter(pp -> NumberUtil.toLong(pp.getRootPpBaseId()) > 0 && !NumberUtil.equals(pp.getPpId(), pp.getRootPpBaseId()))
                .map(OrderTestLineRsp::getRootPpBaseId).distinct().collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(rootBaseIds)){
            List<PPBaseInfoWithBLOBs> rootPpBaseInfos = ppBaseMapper.getPpBaseInfoByIds(rootBaseIds);
            rootPpNameMap = rootPpBaseInfos.stream().collect(Collectors.toMap(PPBaseInfoWithBLOBs::getId, PPBaseInfoWithBLOBs::getPpName));
        }
        //
        List<OrderLanguageRelInfoPO> orderLanguageRelInfoPODBs=this.getOrderLanguageRelInfoPOS(reqObject.getOrderId());
        List<TestLineLanguageInfoPO> testLineLanguageBaseInfoReqPOS = getTestLineLanguageInfoPOS(testLines.stream().map(e->e.getTestLineBaseId()).collect(Collectors.toList()));
        List<ArtifactCitationLangInfoWithBLOBs> testLineCitationLanguageInfoPOS = testLineCitationLangMapper.getTestLineCitationLangList(reqObject.getOrderId());
        boolean isChinese = isChinese();
        for(OrderTestLineRsp testLineInstanceMatrixDTO : testLines){
            if (isChinese) {
                if (CollectionUtils.isNotEmpty(testLineLanguageBaseInfoReqPOS)) {
                    orderLanguageRelInfoPODBs.stream().filter(e->e.getLangType()!=null&&e.getLangType().intValue()==LangTypeEnum.TestLine.getType()&&e.getObjectBaseId().longValue()==testLineInstanceMatrixDTO.getTestLineBaseId().intValue()&&LanguageType.Chinese.getLanguageId()==e.getLanguageId().intValue()).findFirst().ifPresent(orderLanguageRelInfoPO-> {
                        testLineLanguageBaseInfoReqPOS.stream().filter(e -> e.getLangId().longValue() == orderLanguageRelInfoPO.getLangBaseId()).
                                findFirst().ifPresent(objTestLineLanguageInfoPO -> {
                                    if(StringUtils.isNotBlank(objTestLineLanguageInfoPO.getMethodDesc())){
                                        testLineInstanceMatrixDTO.setPpNotes(objTestLineLanguageInfoPO.getMethodDesc());
                                    }
                        });
                    });
                }
                testLineCitationLanguageInfoPOS.stream().filter(e-> NumberUtil.equals(e.getCitationBaseId(), testLineInstanceMatrixDTO.getCitationBaseId())).findFirst().ifPresent(objTestLineCitationLanguageInfoPO->{
                    testLineInstanceMatrixDTO.setEvaluationAlias(objTestLineCitationLanguageInfoPO.getEvaluationAlias());
                });
            }
            List<OrderTestLineRsp> existsDtoByTestLineID= testLines.stream().filter(t->{
                return StringUtils.equalsIgnoreCase(t.getTestLineInstanceId(), testLineInstanceMatrixDTO.getTestLineInstanceId())&&StringUtils.isNotEmpty(t.getSampleNos());
            }).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(existsDtoByTestLineID)) {
                testLineInstanceMatrixDTO.setShowNcRemark(true);
            }

            //set rootpp name
            String rootPpName = rootPpNameMap.get(testLineInstanceMatrixDTO.getRootPpBaseId());
            if(StringUtils.isNotEmpty(rootPpName)){
                testLineInstanceMatrixDTO.setPpName(rootPpName);
            }
        }
        //DIG-4691
        if (isChinese) {
            TestLineEvaluationAliasUtil.buildEvaluationAliasMultiLanguage(testLines, LanguageType.Chinese);
        }

//        testLineLocalService.buildReportLang(order.getOrderNo(),testLines);

        //sort
        testLines.sort((vo1, vo2) -> vo1.getTestLineId()
                .compareTo(vo2.getTestLineId()));
        rspResult.setSuccess(true);
        rspResult.setData(testLines);
        return rspResult;
    }

    private List<PPBaseSectionPO> getRootPpList(List<OrderTestLineRsp> testLines) {
        PPBaseDTO objPPBaseDTO=new PPBaseDTO();
        List<Integer> ppVersionIds =testLines.stream()
                .filter(e->e.getPpVersionId()!=null&&e.getPpVersionId()>0).map(e1->e1.getPpVersionId())
                .distinct()
                .collect(Collectors.toList());
        if(CollectionUtils.isEmpty(ppVersionIds)){
            return Lists.newArrayList();
        }
        objPPBaseDTO.setArtifactVersionIds(ppVersionIds);
        List<PPBaseSectionPO> ppBaseSectionPOS=ppBaseMapper.getRootPPListByPpIds(objPPBaseDTO);
        if(ppBaseSectionPOS == null){
            ppBaseSectionPOS = Lists.newArrayList();
        }
        return ppBaseSectionPOS;
    }

    private List<ArtifactCitationLangInfoPO> getTestLineCitationLanguageInfoPOS(List<Long> citationBaseIds) {
        TestlineCitationLanguageDTO objTestlineCitationLanguageDTO=new TestlineCitationLanguageDTO();
        objTestlineCitationLanguageDTO.setCitationBaseIds(citationBaseIds);
        return testLineBaseMapper.getTlCitationLanguage(objTestlineCitationLanguageDTO);
    }

    private List<TestLineLanguageInfoPO> getTestLineLanguageInfoPOS(List<Long> testLineBaseIds) {
        TestLineLanguageDTO objTestLineLanguageDTO=new TestLineLanguageDTO();
        objTestLineLanguageDTO.setTestLineBaseIds(testLineBaseIds);
        return testLineBaseMapper.getTestlineLanguage(objTestLineLanguageDTO);
    }

    private List<TrimsPPTestLineLanguageInfoPO> getPpTestLineLanguageInfoPOS(List<Long> ppArtifactRelIds) {
        TrimsPPTestLineLanguageDTO objTrimsPPTestLineLanguageDTO=new TrimsPPTestLineLanguageDTO();
        objTrimsPPTestLineLanguageDTO.setPpArtifactRelIds(ppArtifactRelIds);
        return testLineBaseMapper.getPpTlRelLanguage(objTrimsPPTestLineLanguageDTO);
    }

    /**
     *
     * @param reqObject
     * @return
     */
    public CustomResult queryTestLineList(QueryTestLineReq reqObject,Integer page,Integer rows){
        CustomResult rspResult = new CustomResult();
        GeneralOrderInstanceInfoPO order = orderMapper.getOrderInfoByOrderId(reqObject.getOrderId());
        if (order == null){
            rspResult.setMsg("Order not found.");
            return rspResult;
        }
        if( Func.isEmpty(reqObject.getCitationVersionId())
                && StringUtils.isBlank(reqObject.getTestLineName())
                &&(reqObject.getTestLineId()==null||reqObject.getTestLineId().intValue()==0)
        ){
            rspResult.setMsg("At least one query parameter");
            return rspResult;
        }
        reqObject.setLabId(order.getOrderLaboratoryID());
//        String defaultLanguageCode = tokenClient.getUser().getDefaultLanguageCode();
        String defaultLanguageCode = frameWorkClient.getPrimaryLanguageCode(ProductLineContextHolder.getProductLineCode());
//        boolean isChinese = isChinese();
        boolean needDoubleLanguage = frameWorkClient.needDoubleLanguage(ProductLineContextHolder.getProductLineCode());

        //  修改为调用local Trims服务
        List<QueryTestLineListRsp> testLines = new ArrayList<>();
        com.sgs.trimslocal.facade.model.testline.req.QueryTestLineReq queryTestLineReq = new com.sgs.trimslocal.facade.model.testline.req.QueryTestLineReq();
        queryTestLineReq.setCallerBU(ProductLineContextHolder.getProductLineCode());
        if(Func.isNotEmpty(reqObject.getBuCode())){
            queryTestLineReq.setProductLineId(Integer.parseInt(reqObject.getBuCode()));
        }
        queryTestLineReq.setTestLineId(reqObject.getTestLineId());
        queryTestLineReq.setEvaluationName(reqObject.getTestLineName());
//        queryTestLineReq.setCitationName(reqObject.getStandardName());
        queryTestLineReq.setCitationVersionId(reqObject.getCitationVersionId());
        queryTestLineReq.setCitationType(reqObject.getCitationType());
        queryTestLineReq.setPageIndex(Func.toInt(page,1));
        queryTestLineReq.setPageSize(Func.toInt(rows,100));
        queryTestLineReq.setLabId(order.getOrderLaboratoryID());
        queryTestLineReq.setLanguageIds(Arrays.asList(1,2));
        if(Func.isNotEmpty(reqObject.getPhaseOutFlag()) && reqObject.getPhaseOutFlag()){
            queryTestLineReq.setStatues(Arrays.asList(1,2));
        }
        Integer languageId = UserInfoDefaultLanguageCodeEnums.getIdByCode(defaultLanguageCode);
        logger.info("queryTestLineReq from local trims:{}",JSON.toJSONString(queryTestLineReq));
        com.sgs.trimslocal.facade.model.common.BaseResponse baseResponse = testLineFacade.getTestLineList(queryTestLineReq);
        long total = 0;
        if(Func.isNotEmpty(baseResponse) && Func.isNotEmpty(baseResponse.getData())){
            PageInfo<com.sgs.trimslocal.facade.model.testline.rsp.QueryTestLineRsp> testLinepPageInfo = (PageInfo<com.sgs.trimslocal.facade.model.testline.rsp.QueryTestLineRsp>) baseResponse.getData();
            total = testLinepPageInfo.getTotal();
            List<com.sgs.trimslocal.facade.model.testline.rsp.QueryTestLineRsp> testLineList = testLinepPageInfo.getList();
            if(Func.isEmpty(testLineList)){
                testLineList = new ArrayList<>();
            }
            for (com.sgs.trimslocal.facade.model.testline.rsp.QueryTestLineRsp queryTestLineRsp : testLineList) {
                QueryTestLineListRsp queryTestLineListRsp = new QueryTestLineListRsp();
                List<ArtifactCitationRelInfoRsp> citations = queryTestLineRsp.getCitations();
                List<StandardRsp> standards = new ArrayList<>();
                String evaluationAlias = "";
                List<StandardLanguageRsp> standardLanguageRspList = new ArrayList<>();
                for (ArtifactCitationRelInfoRsp citation : citations) {
                    String citationEvaluationAlias = citation.getEvaluationAlias();
//                    queryTestLineListRsp.setEvaluationAlias(citation.getEvaluationAlias());
                    List<TestLineCitationLanguagesRsp> languages = citation.getLanguages();
                    StandardRsp standardRsp = new StandardRsp();
                    standardRsp.setOriginCitationName(citation.getCitationName());
                    if(Func.isNotEmpty(languages)){
                        TestLineCitationLanguagesRsp testLineCitationLanguagesRsp = languages.stream().filter(i -> Func.equals(i.getLanguageId(), languageId)).findFirst().orElse(null);
                        //根据客户主语言获取，主语言为中文，则获取中文数据，中文数据为空，则使用英文数据
                        if(Func.isNotEmpty(testLineCitationLanguagesRsp)){
                            standardRsp.setCitationName(testLineCitationLanguagesRsp.getCitationName());
                            standardRsp.setStandardName(testLineCitationLanguagesRsp.getCitationFullName());
                        }
                        for (TestLineCitationLanguagesRsp languageRsp : languages) {
                            StandardLanguageRsp standardLanguageRsp = new StandardLanguageRsp();
                            standardLanguageRsp.setLanguageId(languageRsp.getLanguageId());
                            standardLanguageRsp.setCitationName(languageRsp.getCitationName());
                            standardLanguageRsp.setEvaluationAlias(languageRsp.getEvaluationAlias());
                            boolean useCnEvaluationAlias = UserInfoDefaultLanguageCodeEnums.check(defaultLanguageCode,UserInfoDefaultLanguageCodeEnums.zh_cn) && LanguageType.check(languageRsp.getLanguageId(),LanguageType.Chinese);
                            if(useCnEvaluationAlias){
                                citationEvaluationAlias = languageRsp.getEvaluationAlias();
                            }
                            standardLanguageRspList.add(standardLanguageRsp);
                        }
                    }
                    if(Func.isEmpty(standardRsp.getCitationName())){
                        standardRsp.setCitationName(citation.getCitationName());

                    }
                    if(Func.isEmpty(standardRsp.getStandardName())){
                        standardRsp.setStandardName(citation.getCitationFullName());
                    }
                    standardRsp.setCitationSeq(citation.getCitationSeq());
                    standardRsp.setCitationBaseId(citation.getCitationBaseId());
                    standardRsp.setStandardSectionId(citation.getCitationSectionId());
                    standardRsp.setCitationVersionId(citation.getCitationVersionId());
                    standardRsp.setStandardId(citation.getCitationId());
                    standardRsp.setiSDefault(false);
                    standardRsp.setCitationType(citation.getCitationType());
                    standardRsp.setEvaluationAlias(citationEvaluationAlias);
                    standardRsp.setStandardLanguageRspList(standardLanguageRspList);
                    standards.add(standardRsp);
                }
                if (Func.isNotEmpty(standards)){
                    standards.sort(new StandardComparator(true));
                    queryTestLineListRsp.setStandardVersionIdSelected(standards.stream().findFirst().get().getCitationVersionId());
                    if(Func.isNotEmpty(reqObject.getCitationVersionId())){
                        List<StandardRsp> standardRspList=standards.stream().filter(e->Func.isNotEmpty(e.getCitationVersionId())&&e.getCitationVersionId().equals(reqObject.getCitationVersionId())).collect(Collectors.toList());
                        if(Func.isNotEmpty(standardRspList)){
                            standardRspList.sort(new StandardComparator(true));
                            queryTestLineListRsp.setStandardVersionIdSelected(standardRspList.get(0).getCitationVersionId());
                        }
                    }
                }
                List<TestLineLabSectionNameInfo> trimsLabSections = queryTestLineRsp.getLabSections();
                if(Func.isNotEmpty(trimsLabSections)){
                    List<LabSectionBaseInfo> labSections = Lists.newArrayList();
                    trimsLabSections.stream().forEach(l->{
                        LabSectionBaseInfo labSectionBaseInfo = new LabSectionBaseInfo();
                        labSectionBaseInfo.setLabSectionId(Func.toLong(l.getLabSectionId()));
                        labSectionBaseInfo.setLabSectionName(l.getLabSectionName());
                        labSectionBaseInfo.setLabSectionBaseId(l.getLabSectionBaseId());
                        labSections.add(labSectionBaseInfo);
                    });
                    queryTestLineListRsp.setLabSections(labSections);
                    if(Func.isNotEmpty(labSections)&&labSections.size()==1){
                        queryTestLineListRsp.setLabSectionId(Func.toLong(labSections.get(0).getLabSectionId()));
                        queryTestLineListRsp.setLabSectionName(labSections.get(0).getLabSectionName());
                    }
                }


                List<QueryTestLineLanguagesRsp> languages = queryTestLineRsp.getLanguages();
                if(needDoubleLanguage && Func.isNotEmpty(languages)){
                    QueryTestLineLanguagesRsp lineLanguagesRsp = languages.stream().filter(e->Func.equals(e.getLanguageId(),2)).findFirst().orElse(null);
                    if(Func.isNotEmpty(lineLanguagesRsp) && Func.isNotEmpty(lineLanguagesRsp.getEvaluationName())){
                        if(Func.isNotEmpty(lineLanguagesRsp.getEvaluationName())){
                            queryTestLineListRsp.setEvaluationAliasCn(lineLanguagesRsp.getEvaluationName());
                            queryTestLineListRsp.setEvaluationAlias(queryTestLineRsp.getEvaluationName());
                        }else{
                            queryTestLineListRsp.setEvaluationAliasCn(lineLanguagesRsp.getEvaluationName());
                            queryTestLineListRsp.setEvaluationAlias(lineLanguagesRsp.getEvaluationName());
                        }
                    }else{
                        queryTestLineListRsp.setEvaluationAliasCn(queryTestLineRsp.getEvaluationName());
                        queryTestLineListRsp.setEvaluationAlias(queryTestLineRsp.getEvaluationName());
                    }
                }

                QueryTestLineLanguagesRsp lineLanguagesRsp = languages.stream().filter(e->Func.equals(e.getLanguageId(),languageId)).findFirst().orElse(null);
                if(Func.isNotEmpty(lineLanguagesRsp) && !needDoubleLanguage){
                    evaluationAlias = lineLanguagesRsp.getEvaluationName();
                }else{
                    evaluationAlias = queryTestLineRsp.getEvaluationName();
                }
                queryTestLineListRsp.setEvaluationAlias(evaluationAlias);
                queryTestLineListRsp.setStandards(standards);
                queryTestLineListRsp.setArtifactBaseId(Func.isEmpty(queryTestLineRsp.getArtifactBaseId())?"":queryTestLineRsp.getArtifactBaseId().toString());
                queryTestLineListRsp.setArtifactType(queryTestLineRsp.getArtifactType());
                queryTestLineListRsp.setTestLineId(queryTestLineRsp.getTestLineId());
                queryTestLineListRsp.setPpNotes(queryTestLineRsp.getMethodDesc());
                queryTestLineListRsp.setTestLineStatus(queryTestLineRsp.getTestLineStatus());
                queryTestLineListRsp.setTestLineVersionId(queryTestLineRsp.getTestLineVersionId());
//                queryTestLineListRsp.setLabSectionName(queryTestLineRsp.getLabSectionName());
                Integer testLineType = queryTestLineRsp.getTestLineType();
                queryTestLineListRsp.setTestLineType(testLineType);
                if(Func.equalsSafe(testLineType,Constants.OOB_TEST_LINE_TYPE)){
                    QueryTestLineLanguagesRsp lineLanguagesCN = languages.stream().filter(e->Func.equals(e.getLanguageId(),LanguageType.Chinese.getLanguageId())).findFirst().orElse(null);
                    if(Func.isNotEmpty(lineLanguagesCN)){
                        queryTestLineListRsp.setCustomerTestLineNameCN(lineLanguagesCN.getEvaluationName());
                    }
                    queryTestLineListRsp.setCustomerTestLineName(queryTestLineRsp.getEvaluationName());
                    queryTestLineListRsp.setOrderTestLineType(TestLineType.OOB_TEST.getType());
                }
                testLines.add(queryTestLineListRsp);
            }
        }

        rspResult.setSuccess(true);
        PageInfo pageInfo = PageInfo.of(testLines);
        pageInfo.setTotal(total);
        pageInfo.setPageNum(page);
        pageInfo.setPageSize(rows);
        rspResult.setData(pageInfo);
        return rspResult;
    }

    private List<com.sgs.trimslocal.facade.model.testline.rsp.QueryTestLineRsp> getTlList(List<Integer> tlVersionIds){
        com.sgs.trimslocal.facade.model.testline.req.QueryTestLineReq objQueryTestLineReq = new com.sgs.trimslocal.facade.model.testline.req.QueryTestLineReq();
        objQueryTestLineReq.setPageIndex(1);
        objQueryTestLineReq.setPageSize(500);
        objQueryTestLineReq.setStatues(Lists.newArrayList(1,2));
        objQueryTestLineReq.setLanguageIds(Lists.newArrayList(1,2));
        objQueryTestLineReq.setTestLineVersionIds(tlVersionIds);
        logger.info("queryTestLineReq from local trims:{}",JSON.toJSONString(objQueryTestLineReq));
        com.sgs.trimslocal.facade.model.common.BaseResponse baseResponse = testLineFacade.getTestLineList(objQueryTestLineReq);
        if(baseResponse==null||baseResponse.getStatus()!=200){
            logger.error("========查询 LocalTRIMS 接口：{} resp：{}","getTestLineList",JSON.toJSONString(baseResponse));
            throw new BizException("LocalTrims getTestLineList接口调用失败");
        }
        if(baseResponse!=null&&baseResponse.getStatus()==200&&baseResponse.getData()!=null){
            PageInfo<com.sgs.trimslocal.facade.model.testline.rsp.QueryTestLineRsp> objQueryTestLineRsp=(PageInfo<com.sgs.trimslocal.facade.model.testline.rsp.QueryTestLineRsp>)baseResponse.getData();
            return objQueryTestLineRsp.getList();
        }
        return Lists.newArrayList();
    }


    /**
     *
     * @param reqObject
     * @return
     */
    @AccessPolicyRule(orderStatus = {
            com.sgs.preorder.facade.model.enums.OrderStatus.Completed,
            com.sgs.preorder.facade.model.enums.OrderStatus.Closed,
            com.sgs.preorder.facade.model.enums.OrderStatus.Cancelled,
            com.sgs.preorder.facade.model.enums.OrderStatus.Pending }
            , subContractType = SubContractOperationTypeEnums.DelTestLine
    )
    @BizLog(bizType= BizLogConstant.TEST_HISTORY,operType="Delete TL")
    public CustomResult delTestLine(DelTestLineReq reqObject){
        CustomResult rspResult = new CustomResult();
        rspResult.setSuccess(false);

        GeneralOrderInstanceInfoPO order = orderMapper.getOrderInfoByOrderId(reqObject.getOrderId());
        if (order == null){
            rspResult.setMsg("Order is  null.");
            return rspResult;
        }
        List<String> ppTestLineRelIds = reqObject.getPpTestLineRelIds();

        if (CollectionUtils.isEmpty(ppTestLineRelIds)) {
            rspResult.setMsg("No data to delete!");
            return rspResult;
        }
        BaseResponse checkRes = checkDeleteTestLine(reqObject);
        if(!checkRes.isSuccess()){
            rspResult.setMsg(checkRes.getMessage());
            return rspResult;
        }
        // 查询是否有状态不为Typing或者Pending的TL
        if (checkTestLineStatus(ppTestLineRelIds)) {
            rspResult.setMsg("Delete Testline failed, since you selected wrong Testline. Only testline with no data can be deleted.");
            return rspResult;
        }
        /*if (checkDeleteSubContract(ppTestLineRelIds)) {
            rspResult.setMsg("Delete Testline failed, please delete subcontract first.");
            return rspResult;
        }*/
        //all ppTlRel of order
        List<PPTestLineRelationshipInfoPO> pptlRelsOfOrder=ppTestLineRelMapper.getPPTestLineRelListByOrderId(reqObject.getOrderId());
        if (CollectionUtils.isEmpty(pptlRelsOfOrder)) {
            rspResult.setMsg("PPTestLineRelationship Data is not found!");
            return rspResult;
        }

        //all tls of order
        List<TestLineInstancePO> allTlsOfOrder= testLineMapper.getTestLineByOrderId(reqObject.getOrderId());
        if (CollectionUtils.isEmpty(allTlsOfOrder)) {
            rspResult.setMsg("TestlineInstance Data is not found!");
            return rspResult;
        }

        //待删除的TL ID
        List<String> tlIdsWaitForDelete= Lists.newArrayList();
        List<TestlineBatchDelDTO> testlineBatchDelDTOS=Lists.newArrayList();
        //把用户请求删除的TL按TL ID分组并统计数量
        Map<String,Long> possibleDeleteTestlineInstanceIdMaps=pptlRelsOfOrder.stream().filter(pptlRel->ppTestLineRelIds.contains(pptlRel.getID())).collect(Collectors.groupingBy(PPTestLineRelationshipInfoPO::getTestLineInstanceID,Collectors.counting()));
        //把当前订单的所有tl按TL ID分组并统计数量
        Map<String,Long> orderTestlineInstanceIdMaps=pptlRelsOfOrder.stream().collect(Collectors.groupingBy(PPTestLineRelationshipInfoPO::getTestLineInstanceID,Collectors.counting()));
        orderTestlineInstanceIdMaps.forEach((k,v)->{
            if(NumberUtil.equals(v, possibleDeleteTestlineInstanceIdMaps.get(k))) {
                tlIdsWaitForDelete.add(k);
                TestlineBatchDelDTO objTestlineBatchDelDTO=new TestlineBatchDelDTO();
                objTestlineBatchDelDTO.setId(k);
                objTestlineBatchDelDTO.setOrderID(reqObject.getOrderId());
                testlineBatchDelDTOS.add(objTestlineBatchDelDTO);
            }
        });

        if (CollectionUtils.isNotEmpty(tlIdsWaitForDelete)&&!this.ableDeleteTestline(tlIdsWaitForDelete)) {
            rspResult.setMsg("Exist test line has test data");
            return rspResult;
        }


        List<TestLineSampleAssociationInfo> matrixListWaitForDelete = Lists.newArrayList();
        // DIG-4484 matrix从deleteTestLine的SQL中抽出来，单独删除，主要思路如下：
        // 1、根据ppTlRel从pp_sample_rel表中找到对应的sample，以及matrix；
        // 2、根据tl+sample=matrix判断除了自己外是否还有其他记录，如果有则不删除matrix，如果没有则删除matrix；
        List<PpInstanceSampleRelationshipWithTestLineInfo> ppInstanceSampleRelationshipWithTestLineInfos = ppSampleRelMapper.listPpSampleRelationWithTestLineListByOrderId(reqObject.getOrderId());

        //待删除的，即用户勾选的
        List<PpInstanceSampleRelationshipWithTestLineInfo> ppInstanceSampleRelationshipWithTestLineInfosWaitForDelete = ppInstanceSampleRelationshipWithTestLineInfos.stream()
                .filter(p -> ppTestLineRelIds.contains(p.getPpTLRelID())).collect(Collectors.toList());

        //排除待删除的
        List<PpInstanceSampleRelationshipWithTestLineInfo> ppInstanceSampleRelationshipWithTestLineInfosExcludeDelete = ppInstanceSampleRelationshipWithTestLineInfos.stream()
                .filter(p -> ! ppTestLineRelIds.contains(p.getPpTLRelID())).collect(Collectors.toList());

        for (String ppTestLineRelId : ppTestLineRelIds) {
            List<PpInstanceSampleRelationshipWithTestLineInfo> currentPpInstanceSampleRelationshipWithTestLineInfos = ppInstanceSampleRelationshipWithTestLineInfosWaitForDelete.stream().filter(p -> StringUtils.equalsIgnoreCase(ppTestLineRelId, p.getPpTLRelID())).collect(Collectors.toList());
            if (org.springframework.util.CollectionUtils.isEmpty(currentPpInstanceSampleRelationshipWithTestLineInfos)){
                continue;
            }

            //再判断matrix
            for (PpInstanceSampleRelationshipWithTestLineInfo currentPpInstanceSampleRelationshipWithTestLineInfo : currentPpInstanceSampleRelationshipWithTestLineInfos) {
                Optional<PpInstanceSampleRelationshipWithTestLineInfo> ppInstanceSampleRelationshipWithTestLineInfoOptional = ppInstanceSampleRelationshipWithTestLineInfosExcludeDelete.stream()
                        .filter(p -> StringUtils.equalsIgnoreCase(currentPpInstanceSampleRelationshipWithTestLineInfo.getMatrixID(), p.getMatrixID()))
                        .findFirst();

                if (! ppInstanceSampleRelationshipWithTestLineInfoOptional.isPresent()) {
                    TestLineSampleAssociationInfo testLineSampleAssociationInfo = new TestLineSampleAssociationInfo();
                    testLineSampleAssociationInfo.setMatrixID(currentPpInstanceSampleRelationshipWithTestLineInfo.getMatrixID());
                    matrixListWaitForDelete.add(testLineSampleAssociationInfo);
                }
            }
        }

        OrderIdReq orderIdReq = new OrderIdReq();
        orderIdReq.setOrderNo(order.getOrderNo());
        OrderAllDTO orderAllDTO = orderFacade.getOrderForPe(orderIdReq).getData();


        DelPPTestLineRelInfo delPPTestLineRel = new DelPPTestLineRelInfo();
        delPPTestLineRel.setOrderId(reqObject.getOrderId());
        delPPTestLineRel.setTestLineInstanceIds(tlIdsWaitForDelete);
        delPPTestLineRel.setPpTestLineRelIds(ppTestLineRelIds);

        boolean isSuccess = transactionTemplate.execute((trans) -> {
            List<String> matrixIdList = matrixListWaitForDelete.stream().map(TestLineSampleAssociationInfo::getMatrixID).collect(Collectors.toList());
            List<ReportMatrixRelationShipInfoPO> reportMatrixRelationShipInfoPOS = new ArrayList<>();
            if(Func.isNotEmpty(matrixIdList)){
                ReportMatrixRelationShipInfoExample reportMatrixRelationShipInfoExample = new ReportMatrixRelationShipInfoExample();
                reportMatrixRelationShipInfoExample.createCriteria().andTestMatrixIDIn(matrixIdList);
                reportMatrixRelationShipInfoPOS = reportMatrixRelationShipInfoMapper.selectByExample(reportMatrixRelationShipInfoExample);
            }
            if (CollectionUtils.isNotEmpty(matrixListWaitForDelete)) {
                testMatrixMapper.deleteMatrixWithForeign(matrixListWaitForDelete);
            }

            // delete ppTestLineRel list
            ppTestLineRelMapper.deletePPTestLineRelationship(delPPTestLineRel);
            // delete tl with slave table
            testLineMapper.batchDeleteExt(delPPTestLineRel);
            //delete language list
            deleteOrderLanguageList(reqObject, allTlsOfOrder, tlIdsWaitForDelete, pptlRelsOfOrder,order.getOrderLaboratoryID());
            //checkPreOrderStatus(order);
            //更新Report LabSection
            //删除后再重新查询一遍
            if(Func.isNotEmpty(reportMatrixRelationShipInfoPOS)){

                Set<String> reportIdList = reportMatrixRelationShipInfoPOS.stream().map(ReportMatrixRelationShipInfoPO::getReportID).collect(Collectors.toSet());
                if(Func.isNotEmpty(reportIdList)){
                    List<TLDataHeaderSaveReq.ReportMatrixItemReq> reportMatrixItemReqList = new ArrayList<>();
                    List<String> reportIds = reportIdList.stream().collect(Collectors.toList());
                    List<ReportMatrixDTO> newReportMatrixList = reportMatrixRelMapper.getReportMatrixListByReportIdList(reportIds);
                    if(Func.isEmpty(newReportMatrixList)){
                        newReportMatrixList = new ArrayList<>();
                    }
                    Map<String, List<ReportMatrixDTO>> groups = newReportMatrixList.stream().collect(Collectors
                            .groupingBy(ReportMatrixDTO::getReportId));
                    for (String reportId: reportIdList) {
                        List<ReportMatrixDTO> reportMatrixDTOList = groups.getOrDefault(reportId, null);
                        if(Func.isNotEmpty(reportMatrixDTOList)){
                            for (ReportMatrixDTO reportMatrixDTO : reportMatrixDTOList) {
                                TLDataHeaderSaveReq.ReportMatrixItemReq reportMatrixItemReq = new TLDataHeaderSaveReq.ReportMatrixItemReq();
                                reportMatrixItemReq.setReportId(reportId);
                                reportMatrixItemReq.setTestLineInstanceId(reportMatrixDTO.getTestLineInstanceId());
                                reportMatrixItemReqList.add(reportMatrixItemReq);
                            }
                        }else{
                            TLDataHeaderSaveReq.ReportMatrixItemReq reportMatrixItemReq = new TLDataHeaderSaveReq.ReportMatrixItemReq();
                            reportMatrixItemReq.setReportId(reportId);
                            reportMatrixItemReqList.add(reportMatrixItemReq);
                        }
                    }
                    // 事务提交后再执行updateReportLabSection
                    TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
                        @Override
                        public void afterCommit() {
                            try {
                                ReportExtForTLUpdateReq reportExtForTLUpdateReq = new ReportExtForTLUpdateReq();
                                reportExtForTLUpdateReq.setReportIdList(reportIdList);
                                reportExtForTLUpdateReq.setToken(SecurityUtil.getSgsToken());
                                reportExtForTLUpdateReq.setProductLineCode(ProductLineContextHolder.getProductLineCode());
                                reportTempFacade.updateReportExtForTL(reportExtForTLUpdateReq);
                            } catch (Exception e) {
                                logger.error("delTL updateReportLabSection error:{}",e);
                            }
                        }
                    });
                    try {
                        //create Tl Report
                        TLDataHeaderSaveReq tlDataHeaderSaveReq = new TLDataHeaderSaveReq();
                        tlDataHeaderSaveReq.setToken(SystemContextHolder.getSgsToken());
                        tlDataHeaderSaveReq.setProductLineCode(ProductLineContextHolder.getProductLineCode());
                        tlDataHeaderSaveReq.setReportMatrixItemReqList(reportMatrixItemReqList);
                        tlDataHeaderSaveReq.setCheckFlag(true);
                        tlDataHeaderSaveReq.setOrderNo(orderAllDTO.getOrderNo());
                        BaseResponse<TLDataHeaderSaveRsp> checkSaveTLReportResponse = reportTempFacade.saveTestDataHeader(tlDataHeaderSaveReq);
                        if(checkSaveTLReportResponse.isFail()){
                            trans.setRollbackOnly();
                            rspResult.setSuccess(false);
                            rspResult.setMsg(checkSaveTLReportResponse.getMessage());
                            if(Func.isNotEmpty(checkSaveTLReportResponse.getData())){
                                rspResult.setData(checkSaveTLReportResponse.getData().getCheckResultList());
                            }
                            return false;
                        }
                    } catch (Exception e) {
                        logger.error("assign Report Matrix create Tl Report error:{}",e);
                        trans.setRollbackOnly();
                        rspResult.setSuccess(false);
                        rspResult.setMsg(e.getMessage());
                        return false;
                    }
                }
            }
            //DIG-4507 All TL Complete后需要更改Order状态为Reporting
            orderStatusValidate(reqObject.getOrderId());
            TestLineStatusUpdateReq req  = new TestLineStatusUpdateReq();
            req.setOrderNo(order.getOrderNo());
            CustomResult customResult = testLineStatusServiceNew.onChange(req);
            if(!customResult.isSuccess()){
                trans.setRollbackOnly();
                rspResult.setSuccess(false);
                rspResult.setMsg(customResult.getMsg());
                rspResult.setData(customResult.getData());
                return false;
            }
            return true;
        });

        if(!isSuccess){
            return rspResult;
        }

        List<String> testLineInstanceIds = delPPTestLineRel.getTestLineInstanceIds();
        Set<Integer> testLineIDList = allTlsOfOrder.stream().filter(testLine->{
            return  testLineInstanceIds.contains(testLine.getID());
        }).collect(Collectors.toSet()).stream().map(TestLineInstancePO::getTestLineID).collect(Collectors.toSet());
        BizLogHelper.setValue(order.getOrderNo(), StringUtils.join(testLineIDList, ","));
        if (Func.isNotEmpty(orderAllDTO)){
            BizLogHelper.setLabCode(orderAllDTO.getBUCode(), orderAllDTO.getLocationCode());
        }


        //删除TL要通知CROSSDB
        Set<String> realDeletedTestLineInstanceIds = allTlsOfOrder.stream().filter(testLine->{
            return  testLineInstanceIds.contains(testLine.getID());
        }).collect(Collectors.toSet()).stream().map(TestLineInstancePO::getID).collect(Collectors.toSet());

        doSendMsgOnDeleteTestLine(realDeletedTestLineInstanceIds);

        rspResult.setSuccess(isSuccess);
        return rspResult;
    }

    private BaseResponse checkDeleteTestLine(DelTestLineReq reqObject){
        List<String> ppTestLineRelIds = reqObject.getPpTestLineRelIds();
        boolean subOrderFlag = reqObject.isSubOrderFlag();
        //无关联的有效的Job
        if (checkHasJob(ppTestLineRelIds,subOrderFlag)) {
            return BaseResponse.newFailInstance("Delete TestLine failed, one or more Job(s) have been created.");
        }
        if(!subOrderFlag){
            //无关联的有效的Subcontract
            if (checkHasValidSubcontract(ppTestLineRelIds)) {
                return BaseResponse.newFailInstance("Delete TestLine failed, one or more SubContract(s) have been created.");
            }
            //无关联的有效的Report或Report的状态为New
            if (checkHasValidReport(ppTestLineRelIds)) {
                return BaseResponse.newFailInstance("Delete TestLine failed, one or more Report(s) have been created.");
            }
        }
        return BaseResponse.newSuccessInstance(true);
    }
    /**
     * 物理删除tl后，需要通知crossdb
     * @param testLineInstanceIds
     */
    private void doSendMsgOnDeleteTestLine(Set<String> testLineInstanceIds) {
        logger.info("方法doSendMsgOnDeleteTestLine，物理删除tl后，需要通知crossdb");
        if(testLineInstanceIds == null || testLineInstanceIds.size() ==0) {
            logger.warn("方法doSendMsgOnDeleteTestLine，物理删除tl后，但没有testlineinstanceId");
            return;
        }
        PhysicDeleteReq physicDeleteReq = new PhysicDeleteReq();
        physicDeleteReq.setTableName("tb_test_line_instance");
        physicDeleteReq.setKey(StringUtils.join(testLineInstanceIds, ","));
        logger.info("方法doSendMsgOnDeleteTestLine，物理删除tl后，KEY:"+physicDeleteReq.getKey());

        GeneralMessage<PhysicDeleteReq> msg = new GeneralMessage<>();
        msg.setActionType("delete");
        msg.setData(physicDeleteReq);

        producer.doSend(KafkaTopicConsts.TOPIC_PHYCAL_DELETE, msg);
    }

    public void checkPreOrderStatus(GeneralOrderInstanceInfoPO order) {
        // 验证是否所有的testLine都被validate
        TestLineInstanceExample objTestLineInstanceExample=new TestLineInstanceExample();
        objTestLineInstanceExample.createCriteria().andGeneralOrderInstanceIDEqualTo(order.getID()).andTestLineStatusNotEqualTo(708);
        List<TestLineInstancePO> testLineLists=testLineInstanceMapper.selectByExample(objTestLineInstanceExample);

        int count = 0;
        int validCount = 0;
        for (TestLineInstancePO testLine : testLineLists) {
            if (TestLineStatus.check(testLine.getTestLineStatus(), TestLineStatus.Completed, TestLineStatus.Cancelled, TestLineStatus.NC) ||
                    TestLineType.check(testLine.getTestLineType(), TestLineType.Pretreatment)) {
                count++;
            }
            if(!TestLineStatus.check(testLine.getTestLineStatus(), TestLineStatus.Cancelled, TestLineStatus.NC)){
                validCount ++;
            }
        }
        // 所有的testline都为Completed或Subcontracted或cancelled状态   且 至少有一条有效的TL
        if (validCount > 0 && count == testLineLists.size()) {
            GeneralOrderInstanceInfoPO updateOrderInfo=new GeneralOrderInstanceInfoPO();
            updateOrderInfo.setID(order.getID());
            updateOrderInfo.setOrderStatus(OrderStatus.Completed.getStatus());
            generalOrderInstanceInfoMapper.updateByPrimaryKeySelective(updateOrderInfo);
            SysStatusReq reqStatus = new SysStatusReq();
            reqStatus.setObjectNo(order.getOrderNo());
            reqStatus.setIgnoreOldStatus(true);
            reqStatus.setNewStatus(PreOrderStatus.Reporting.getStatus());
            String regionAccount = "System";
            UserInfo localUser = UserHelper.getLocalUser();
            if (localUser != null){
                regionAccount = localUser.getRegionAccount();
            }
            reqStatus.setUserName(regionAccount);
            statusClient.insertStatusInfo(reqStatus);
        }
    }

    private void deleteOrderLanguageList(DelTestLineReq reqObject, List<TestLineInstancePO> testLineInstanceDbList, List<String> testlineInstanceIdDeleteds,  List<PPTestLineRelationshipInfoPO> pptlRelDbList,int labId) {
        Set<Long> testTestlineDeletedLangs = Sets.newHashSet();
        Set<Long> testTestlineCitationDeletedLangs = Sets.newHashSet();
        List<TestLineInstancePO> testlineInstanceDeletedList=testLineInstanceDbList.stream().filter(testLineInstancePO -> testlineInstanceIdDeleteds.contains(testLineInstancePO.getID())).collect(Collectors.toList());

        Map<Long, Integer> ppBaseIds = Maps.newHashMap();
        ppMapper.getPpTestLineGroupList(reqObject.getOrderId()).forEach(pp->{
            ppBaseIds.put(pp.getPpBaseId(), pp.getPpCount());
        });
        Map<Long,Long> possibleDeletedTestlineBaseIdMaps=testlineInstanceDeletedList.stream().filter(e->e.getTestLineBaseId()!=null).collect(Collectors.groupingBy(TestLineInstancePO::getTestLineBaseId,Collectors.counting()));
        Map<Long,Long> possibleDeletedTestlineCitBaseIdMaps=testlineInstanceDeletedList.stream().filter(e->e.getCitationBaseId()!=null).collect(Collectors.groupingBy(TestLineInstancePO::getCitationBaseId,Collectors.counting()));

        testLineInstanceDbList.stream().filter(e->e.getTestLineBaseId()!=null).collect(Collectors.groupingBy(TestLineInstancePO::getTestLineBaseId,Collectors.counting())).forEach((k,v)->{if(v==possibleDeletedTestlineBaseIdMaps.get(k)){
            testTestlineDeletedLangs.add(k);
        }});
        testLineInstanceDbList.stream().filter(e->e.getCitationBaseId()!=null).collect(Collectors.groupingBy(TestLineInstancePO::getCitationBaseId,Collectors.counting())).forEach((k,v)->{if(v==possibleDeletedTestlineCitBaseIdMaps.get(k)){
            testTestlineCitationDeletedLangs.add(k);
        }});

        List<Long> trimsPPtlRelIdDeleteds = Lists.newArrayList();
        List<Long> possibleDeletedTrimsPPtlRelIds=pptlRelDbList.stream().filter(pptlRel->pptlRel.getPpArtifactRelId()!=null&&reqObject.getPpTestLineRelIds().
                contains(pptlRel.getID())).map(e->e.getPpArtifactRelId()).collect(Collectors.toList());
        pptlRelDbList.stream().filter(e->e.getPpArtifactRelId()!=null).collect(Collectors.groupingBy(PPTestLineRelationshipInfoPO::getPpArtifactRelId,Collectors.counting())).forEach((k,v)->{if(v==1&&possibleDeletedTrimsPPtlRelIds.contains(k)){
            trimsPPtlRelIdDeleteds.add(k);
        }});

        Set<Long> delLangPpBaseIds = Sets.newHashSet(),
        delLangPpArtifactRelIds = Sets.newHashSet(),
        delLangPpSectionBaseIds = Sets.newHashSet();

        if(CollectionUtils.isNotEmpty(trimsPPtlRelIdDeleteds)){
            PPTestLineDTO objPPTestLineDTO=new PPTestLineDTO();
            objPPTestLineDTO.setLabId(labId);
            objPPTestLineDTO.setPpArtifactRelId(trimsPPtlRelIdDeleteds);
            List<QueryTestLineRsp> ppTestLines = testLineBaseMapper.getPpTestLineList(objPPTestLineDTO);
            ppTestLines.forEach(ppTestLine->{
                long ppBaseId = NumberUtil.toLong(ppTestLine.getPpId());
                if (ppBaseId > 0 && !ppBaseIds.containsKey(ppBaseId)){
                    delLangPpBaseIds.add(ppBaseId);
                }
                long ppArtifactRelId = NumberUtil.toLong(ppTestLine.getArtifactBaseId());
                if (ppArtifactRelId > 0){
                    delLangPpArtifactRelIds.add(ppArtifactRelId);
                }
                long sectionBaseId = NumberUtil.toLong(ppTestLine.getSectionBaseId());
                if (sectionBaseId > 0){
                    delLangPpSectionBaseIds.add(sectionBaseId);
                }
            });
        }
        for (Map.Entry<Long, Integer> entry: ppBaseIds.entrySet()) {
            int ppCount = entry.getValue();
            if (ppCount > 0){
                continue;
            }
            delLangPpBaseIds.add(entry.getKey());
        }

        List<OrderLanguageDTO> orderLangs = Lists.newArrayList();
        OrderLanguageDTO orderLang;
        if(CollectionUtils.isNotEmpty(testTestlineDeletedLangs)){
            orderLang = new OrderLanguageDTO();
            orderLang.setOrderId(reqObject.getOrderId());
            orderLang.setLangType(LangTypeEnum.TestLine.getType());
            orderLang.setObjectBaseIds(testTestlineDeletedLangs);
            orderLangs.add(orderLang);
        }
        if(CollectionUtils.isNotEmpty(testTestlineCitationDeletedLangs)){
            orderLang = new OrderLanguageDTO();
            orderLang.setOrderId(reqObject.getOrderId());
            orderLang.setLangType(LangTypeEnum.Citation.getType());
            orderLang.setObjectBaseIds(testTestlineCitationDeletedLangs);
            orderLangs.add(orderLang);
        }
        if(CollectionUtils.isNotEmpty(delLangPpBaseIds)){
            orderLang = new OrderLanguageDTO();
            orderLang.setOrderId(reqObject.getOrderId());
            orderLang.setLangType(LangTypeEnum.PP.getType());
            orderLang.setObjectBaseIds(delLangPpBaseIds);
            orderLangs.add(orderLang);
        }
       if(CollectionUtils.isNotEmpty(delLangPpArtifactRelIds)){
           orderLang = new OrderLanguageDTO();
           orderLang.setOrderId(reqObject.getOrderId());
           orderLang.setLangType(LangTypeEnum.PpArtifactRel.getType());
           orderLang.setObjectBaseIds(delLangPpArtifactRelIds);
           orderLangs.add(orderLang);
        }
        if(CollectionUtils.isNotEmpty(delLangPpSectionBaseIds)){
            orderLang = new OrderLanguageDTO();
            orderLang.setOrderId(reqObject.getOrderId());
            orderLang.setLangType(LangTypeEnum.Section.getType());
            orderLang.setObjectBaseIds(delLangPpSectionBaseIds);
            orderLangs.add(orderLang);
        }
        if (orderLangs.isEmpty()){
            return;
        }
        orderLanguageRelMapper.batchDelLangRels(orderLangs);
    }

    private boolean ableDeleteTestline(List<String> testLineIDList) {
        /**
         * TODO Kevin
         * 1、return count <= 0;
         */
        Integer count = this.testDataMapper.getExistedTestDataCount(testLineIDList);
        if (count > 0) {
            return false;
        } else {
            return true;
        }
    }

    private boolean checkDeleteSubContract(List<String> relationIDs) {
        /**
         * TODO Kevin
         * 1、return list != null && list.size() > 0;
         */
        List<SubContractTestLineMappingPO> list = subContractExtMapper.queryMappingByPPTestLineRelationShipIDs(relationIDs);
        if (list != null && list.size() > 0) {
            return true;
        }
        return false;
    }

    private boolean checkTestLineStatus(List<String> relationIDs) {
        List<TestLineInstancePO> List = testLineMapper.queryTestLineByRelId(relationIDs);
        for (TestLineInstancePO testline : List) {
            if(TestLineStatus.checkCategory(testline.getTestLineStatus(),Constants.TEST_LINE.STATUS_CATEGORY.LOCK) || testline.getPendingFlag()){
                return  true;
            }
        }
        return false;
    }

    private boolean checkHasJob(List<String> relationIDs,boolean subOrderFlag) {
        return testLineMapper.queryJobCountByPpTlRelId(relationIDs,subOrderFlag) > 0;
    }
    private boolean checkHasValidSubcontract(List<String> relationIDs) {
        return testLineMapper.querySubcontractCountByPpTlRelId(relationIDs) > 0;
    }
    private boolean checkHasValidReport(List<String> relationIDs) {
        return testLineMapper.queryReportCountByPpTlRelId(relationIDs) > 0;
    }
    private boolean checkHasValidReportByTestLineInstanceId(List<String> relationIDs) {
        return testLineMapper.queryReportCountByTestLineInstanceIdList(relationIDs) > 0;
    }

    /**
     *
     * @param reqObj
     * @return
     */
    @AccessPolicyRule(reportStatus = { ReportStatus.Approved, ReportStatus.Cancelled },
            subContractType = SubContractOperationTypeEnums.CancelTestLine,
            testLinePendingType = TestLinePendingTypeEnums.TestLineInstanceId)
//    @BizLog(bizType=BizLogConstant.TEST_HISTORY,operType="Cancel TL")
//    @TestLinePending(filedName = "testLineInstanceId",type=TestLinePendingTypeEnums.TL_ID)
    public CustomResult cancelTestLine(TestLineCancelReq reqObj){
        CustomResult rspResult;
        boolean isSuccess;

        if(Func.isNotEmpty(reqObj.getProductLineCode())){
            ProductLineContextHolder.setProductLineCode(reqObj.getProductLineCode());
        }
        rspResult = new CustomResult();
        TestLineInstancePO testLineInstance = testLineMapper.getBaseTestLineById(reqObj.getTestLineInstanceId());
        if(Func.isEmpty(testLineInstance)){
            rspResult.setSuccess(false);
            rspResult.setMsg("TLInstanceId " + reqObj.getTestLineInstanceId() +  " This testLine not found!");
            return  rspResult;
        }
        OrderIdReq orderIdReq = new OrderIdReq();
        orderIdReq.setOrderNo(testLineInstance.getOrderNo());
        orderIdReq.setProductLineCode(ProductLineContextHolder.getProductLineCode());
        BaseResponse<OrderAllDTO> orderForPe = orderFacade.getOrderForPe(orderIdReq);
        OrderAllDTO orderAllDTO = null;
        if(orderForPe.isSuccess()){
            orderAllDTO = orderForPe.getData();
        }
        try {
            SubContractTestLineMappingExample example = new SubContractTestLineMappingExample();
            example.createCriteria().andTestLineInstanceIDEqualTo(reqObj.getTestLineInstanceId());
            List<SubContractTestLineMappingPO> subContractTestLineMappingPOS = subContractTestLineMappingMapper.selectByExample(example);
            if(CollectionUtils.isNotEmpty(subContractTestLineMappingPOS)){
                String subContractID = subContractTestLineMappingPOS.get(0).getSubContractID();
                SubContractPO subContractPO = subContractMapper.selectByPrimaryKey(subContractID);
                if(subContractPO!=null){
                    Integer subContractOrder = subContractPO.getSubContractOrder();
                    Integer dataLock = subContractPO.getDataLock();
                    if(subContractOrder!=null && subContractOrder.compareTo(1)==0
                            && SubContractDataLockEnums.check(dataLock,SubContractDataLockEnums.lock)){
                        rspResult.setSuccess(false);
                        rspResult.setMsg("TLID: " + testLineInstance.getTestLineID()  +  " This testLine has been subcontracted locked,can't cancel");
                        return  rspResult;
                    }
                }
            }
//        TestLineInstancePO testLineInstance=testLineInstanceMapper.selectByPrimaryKey(reqObj.getTestLineInstanceId());
            //TestLineInstancePO testLineInstance = testLineMapper.getBaseTestLineById(reqObj.getTestLineInstanceId());
            Integer originalTestLineStatus = testLineInstance.getTestLineStatus();
            //校验是否pending
            if (testLineInstance.getPendingFlag()){
                rspResult.setSuccess(false);
                rspResult.setMsg("TLID: " + testLineInstance.getTestLineID()  +  " This testLine  has been Pending,can't cancel");
                return  rspResult;
            }
            if (TestLineStatus.checkCategory(testLineInstance.getTestLineStatus(),Constants.TEST_LINE.STATUS_CATEGORY.LOCK)){
                rspResult.setSuccess(false);
                rspResult.setMsg("TLID: " + testLineInstance.getTestLineID()  +  " This testLine Status has been Lock,can't cancel");
                return  rspResult;
            }
            //校验是否关联有效的Report
            if(checkHasValidReportByTestLineInstanceId(Lists.newArrayList(reqObj.getTestLineInstanceId()))){
                rspResult.setSuccess(false);
                rspResult.setMsg("TLID: " + testLineInstance.getTestLineID()  +  " Cancel TestLine failed, one or more Report(s) have been created.");
                return  rspResult;
            }
            UserInfo user = reqObj.getUser();
            if(Func.isEmpty(user)){
                user = SecurityContextHolder.getUserInfoFillSystem();
            }
            String username=user.getRegionAccount();
            Date modifiedDate=DateUtils.getNow();
            testLineInstance.setTestLineStatus(TestLineStatus.Cancelled.getStatus());
            testLineInstance.setModifiedBy(username);
            testLineInstance.setModifiedDate(modifiedDate);
            testLineInstance.setActiveIndicator(false);
            List<LogInfoWithBLOBs> logList = Lists.newArrayList();

            TestMatrixPO testMatrixPO=new TestMatrixPO();
            testMatrixPO.setTestLineInstanceID(reqObj.getTestLineInstanceId());
            testMatrixPO.setModifiedBy(username);
            testMatrixPO.setModifiedDate(modifiedDate);
            testMatrixPO.setActiveIndicator(false);
            testMatrixPO.setMatrixStatus(MatrixStatus.Cancelled.getStatus());

            ConclusionInfoPO conclusion=new ConclusionInfoPO();
            conclusion.setTestLineInstanceID(reqObj.getTestLineInstanceId());
            conclusion.setModifiedBy(username);
            conclusion.setModifiedDate(modifiedDate);
            conclusion.setID(reqObj.getTestLineInstanceId());

            String orderID=testLineInstance.getGeneralOrderInstanceID();
            GeneralOrderInstanceInfoPO generalOrderInstanceInfoPO= orderMapper.getOrderInfoByOrderId(orderID);
            String  orderNo=generalOrderInstanceInfoPO.getOrderNo();

            ReportInfoExample reportInfoExample=new ReportInfoExample();
            reportInfoExample.createCriteria().andOrderNoEqualTo(orderNo);
            List<ReportInfoPO> reportInfoPOS = reportInfoMapper.selectByExample(reportInfoExample);
            ReportInfoPO reportInfoPO=reportInfoPOS.get(0);

            // DIG-7128 判断当前TestLine 是否是所处的分包单的最后一个 若是，则不能Cancel
            SubContractPO subContractPO = subContractExtMapper.querySubContractByTestLineInstanceId(testLineInstance.getID());
            if (subContractPO != null && SubContractType.check(subContractPO.getSubContractOrder(), SubContractType.SubContract)) {
                List<TestLineInstancePO> subcontractTestLineInfo = testLineMapper.getSubcontractTestLineInfo(subContractPO.getID());
                List<TestLineInstancePO> subTestLineInfos = subcontractTestLineInfo.stream().filter(tl -> tl.getActiveIndicator()
                        && !TestLineStatus.check(tl.getTestLineStatus(), TestLineStatus.Cancelled)
                        && !StringUtils.equals(tl.getID(), testLineInstance.getID())).collect(Collectors.toList());
                if (org.apache.commons.collections4.CollectionUtils.isEmpty(subTestLineInfos)) {
                    rspResult.setSuccess(false);
                    rspResult.setMsg("TLID: " + testLineInstance.getTestLineID()  +  " Fail to cancel! Because it was the only one TL of ongoing Internal Subcontract!");
                    return  rspResult;
                }
            }
            DeleteTestReq deleteTestReq = new DeleteTestReq();
            deleteTestReq.setTestLineInstanceId(reqObj.getTestLineInstanceId());
            List<TestMatrixPO> matrixList = testMatrixMapper.getMatrixByTestLineInstanceId(deleteTestReq);
            List<String> delExePpTestLineRelIds = new ArrayList<>();
            String exeOrderId = "";
            if(Func.isNotEmpty(subContractPO)){
                SubContractExternalRelationshipPO searchPO = new SubContractExternalRelationshipPO();
                searchPO.setSubContractNo(subContractPO.getSubContractNo());
                searchPO.setSubContractType(SubContractType.SubContract.getType());
                BaseResponse<List<SubContractExternalRelationshipPO>> response = subContractExternalRelService.getSubContractExternalRels(searchPO);
                List<SubContractExternalRelationshipPO> subContractRelList = response.getData();
                if(Func.isNotEmpty(subContractRelList)){
                    String exeOrderNo = subContractRelList.get(0).getExternalNo();
                    GeneralOrderInstanceDTO exeOrder = generalOrderInstanceMapper.getByNo(exeOrderNo);
                    if(Func.isNotEmpty(exeOrder)){
                        exeOrderId = exeOrder.getId();
                    }
                }
                if(Func.isNotEmpty(exeOrderId)){
                    // 接包方对应的orderId
                    DelTestLineReq delTestLineReq = new DelTestLineReq();
                    List<PPTestLineJobDto> ppTestLineJob = testLineMapper.getPpTestLineJob(orderID, Lists.newArrayList(reqObj.getTestLineInstanceId()),exeOrderId);
                    if(Func.isNotEmpty(ppTestLineJob)){
                        if (CollectionUtils.isNotEmpty(ppTestLineJob)) {
                            if (ppTestLineJob.stream().filter(pptl -> TestLineStatus.checkCategory(pptl.getTestLineStatus(), Constants.TEST_LINE.STATUS_CATEGORY.LOCK) || NumberUtil.equals(pptl.getPendingFlag(),1)).collect(Collectors.toSet()).size() > 0) {
                                String editTlStatus = Arrays.stream(TestLineStatus.values()).filter(tl -> tl.getCategory().equalsIgnoreCase(Constants.TEST_LINE.STATUS_CATEGORY.EDIT)).map(TestLineStatus::getMessage).collect(Collectors.joining(","));
                                rspResult.setSuccess(false);
                                rspResult.setMsg(String.format("TLID: " + testLineInstance.getTestLineID()  + " Removed Test Line was not in status '" + editTlStatus + "'"));
                                return rspResult;
                            }
                        }
                        delExePpTestLineRelIds = ppTestLineJob.stream().map(PPTestLineJobDto::getPpTestLineRelID).distinct().collect(Collectors.toList());
                    }
                }
            }

            List<String> finalDelExePpTestLineRelIds = delExePpTestLineRelIds;
            String finalExeOrderId = exeOrderId;
            isSuccess = transactionTemplate.execute((trans) -> {
                testLineMapper.cancelTestLine(testLineInstance);
                testMatrixMapper.cancelMatrixByTestLineInstanceId(testMatrixPO);
                subContractExtMapper.deleteByTestLineInstanceId(testLineInstance.getID());
                int count=conclusionMapper.cancelConclusionByTestLineInstanceId(conclusion);
                if (count>0){
                    if(reportInfoPO.getRecalculationFlag()==1){
                        reportInfoPO.setRecalculationFlag(2);
                        reportInfoPO.setModifiedBy(username);
                        reportInfoPO.setModifiedDate(modifiedDate);
                        reportMapper.updateRecalculationFlagByReportId(reportInfoPO);
                    }
                }
                JobTestLineRelationshipInfoExample exampleDelete=new JobTestLineRelationshipInfoExample();
                exampleDelete.createCriteria().andTestlineinstanceidEqualTo(reqObj.getTestLineInstanceId());
                jobTestLineRelationshipInfoMapper.deleteByExample(exampleDelete);
                //删除Report Matrix relationShip
                if(Func.isNotEmpty(matrixList)){
                    List<String> matrixIdList = matrixList.stream().map(TestMatrixPO::getID).collect(Collectors.toList());
                    if(Func.isNotEmpty(matrixIdList)){
                        ReportMatrixRelationShipInfoExample reportMatrixRelationShipInfoExample = new ReportMatrixRelationShipInfoExample();
                        reportMatrixRelationShipInfoExample.createCriteria().andTestMatrixIDIn(matrixIdList);
                        List<ReportMatrixRelationShipInfoPO> reportMatrixRelationShipInfoPOS = reportMatrixRelationShipInfoMapper.selectByExample(reportMatrixRelationShipInfoExample);
                        Set<String> reportIdList = reportMatrixRelationShipInfoPOS.stream().map(ReportMatrixRelationShipInfoPO::getReportID).collect(Collectors.toSet());
                        if(Func.isNotEmpty(matrixIdList)){
                            reportMatrixRelMapper.deleteByMatrixIDList(matrixIdList);
                        }
                        if(Func.isNotEmpty(reportIdList)){
                            //更新Report LabSection
                            // 事务提交后再执行updateReportLabSection
                            try {
                                TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
                                    @Override
                                    public void afterCommit() {
                                        ReportExtForTLUpdateReq reportExtForTLUpdateReq = new ReportExtForTLUpdateReq();
                                        reportExtForTLUpdateReq.setReportIdList(reportIdList);
                                        reportExtForTLUpdateReq.setToken(SecurityUtil.getSgsToken());
                                        reportExtForTLUpdateReq.setProductLineCode(ProductLineContextHolder.getProductLineCode());
                                        reportTempFacade.updateReportExtForTL(reportExtForTLUpdateReq);
                                    }
                                });
                            } catch (IllegalStateException e) {
                                logger.error("TLID: " + testLineInstance.getTestLineID() + " cancelTL updateReportLabSection error:{}",e);
                            }
                            //create Tl Report
                            List<TLDataHeaderSaveReq.ReportMatrixItemReq> reportMatrixItemReqList = new ArrayList<>();


                            if(Func.isNotEmpty(reportIdList)){
                                List<ReportMatrixDTO> newReportMatrixList = reportMatrixRelMapper.getReportMatrixListByReportIdList(new ArrayList<>(reportIdList));
                                if(Func.isEmpty(newReportMatrixList)){
                                    newReportMatrixList = new ArrayList<>();
                                }
                                Map<String, List<ReportMatrixDTO>> groups = newReportMatrixList.stream().collect(Collectors
                                        .groupingBy(ReportMatrixDTO::getReportId));
                                for (String reportId: reportIdList) {
                                    List<ReportMatrixDTO> reportMatrixDTOList = groups.getOrDefault(reportId, null);
                                    if(Func.isNotEmpty(reportMatrixDTOList)){
                                        for (ReportMatrixDTO reportMatrixDTO : reportMatrixDTOList) {
                                            TLDataHeaderSaveReq.ReportMatrixItemReq reportMatrixItemReq = new TLDataHeaderSaveReq.ReportMatrixItemReq();
                                            reportMatrixItemReq.setReportId(reportId);
                                            reportMatrixItemReq.setTestLineInstanceId(reportMatrixDTO.getTestLineInstanceId());
                                            reportMatrixItemReqList.add(reportMatrixItemReq);
                                        }
                                    }else{
                                        TLDataHeaderSaveReq.ReportMatrixItemReq reportMatrixItemReq = new TLDataHeaderSaveReq.ReportMatrixItemReq();
                                        reportMatrixItemReq.setReportId(reportId);
                                        reportMatrixItemReqList.add(reportMatrixItemReq);
                                    }
                                }
                            }
                            try {
                                TLDataHeaderSaveReq tlDataHeaderSaveReq = new TLDataHeaderSaveReq();
                                tlDataHeaderSaveReq.setToken(SystemContextHolder.getSgsToken());
                                tlDataHeaderSaveReq.setProductLineCode(ProductLineContextHolder.getProductLineCode());
                                tlDataHeaderSaveReq.setReportMatrixItemReqList(reportMatrixItemReqList);
                                tlDataHeaderSaveReq.setCheckFlag(true);
                                tlDataHeaderSaveReq.setOrderNo(orderNo);
                                BaseResponse<TLDataHeaderSaveRsp> saveRspBaseResponse = reportTempFacade.saveTestDataHeader(tlDataHeaderSaveReq);
                                if(saveRspBaseResponse.isFail()){
                                    trans.setRollbackOnly();
                                    rspResult.setSuccess(false);
                                    rspResult.setMsg(saveRspBaseResponse.getMessage());
                                    if(Func.isNotEmpty(saveRspBaseResponse.getData())){
                                        rspResult.setData(saveRspBaseResponse.getData().getCheckResultList());
                                    }
                                    return false;
                                }
                            } catch (Exception e) {
                                logger.error("TLID: " + testLineInstance.getTestLineID() + " assign Report Matrix create Tl Report error:{}",e);
                                trans.setRollbackOnly();
                                rspResult.setSuccess(false);
                                rspResult.setMsg(e.getMessage());
                                return false;
                            }
                        }
                    }

                }



                if(Func.isNotEmpty(finalDelExePpTestLineRelIds)){
                    DelTestLineReq delTestLineReq = new DelTestLineReq();
                    delTestLineReq.setPpTestLineRelIds(finalDelExePpTestLineRelIds);
                    delTestLineReq.setOrderId(finalExeOrderId);
                    delTestLineReq.setToken(tokenClient.getToken());
                    delTestLineReq.setSubOrderFlag(true);
                    CustomResult res = this.delTestLine(delTestLineReq);
                    logger.info("cancelTl.delTestLine.res:{}", JSON.toJSONString(res));
                }


                orderStatusValidate(orderID);
                TestLineStatusUpdateReq req  = new TestLineStatusUpdateReq();
                req.setOrderNo(orderNo);
                CustomResult customResult = testLineStatusServiceNew.onChange(req);
                if(!customResult.isSuccess()){
                    trans.setRollbackOnly();
                    return false;
                }
                return  true;
            });
            if(isSuccess){
                try {
                    if(Func.isNotEmpty(orderAllDTO)){
                        BizLogInfo bizLog = new BizLogInfo();
                        bizLog.setBizOpType(BizLogConstant.TEST_HISTORY);
                        bizLog.setLab(orderAllDTO.getLocationCode());
                        bizLog.setBu(orderAllDTO.getBUCode());
                        bizLog.setOpUser(user.getRegionAccount());
                        bizLog.setBizId(generalOrderInstanceInfoPO.getOrderNo());
                        bizLog.setOpType(Func.toStr(reqObj.getOperateBy())+" Cancel TL:["+Func.toStr(testLineInstance.getTestLineID())+"]");
                        bizLog.setNewVal(TestLineStatus.Cancelled.getMessage());
                        bizLog.setOriginalVal(Func.toStr(TestLineStatus.getMessage(originalTestLineStatus)));
                        bizLogClient.doSend(bizLog);
                    }
                } catch (Exception e) {

                }
            }
        } finally {
            String regionAccount = SystemContextHolder.getUserInfoFillSystem().getRegionAccount();
            SystemLog systemLog = new SystemLog();
            systemLog.setObjectType("TestLine");
            systemLog.setObjectNo(testLineInstance.getTestItemNo());
            systemLog.setProductLineCode(SystemContextHolder.getBuCode());
            systemLog.setType(SystemLogType.API.getType());
            systemLog.setRemark("Cancel TestLine");
            systemLog.setOperationType("Cancel TestLine");
            systemLog.setCreateBy(regionAccount);
            systemLog.setRequestId(reqObj.getRequestId());
            systemLog.setRequest(testLineInstance.getID());
            systemLogHelper.save(systemLog);
        }

        rspResult.setSuccess(isSuccess);
        return rspResult;
    }


    /**
     *
     * @param reqObj
     * @return
     */
    public CustomResult updateModified(UpdateModifiedTestLineListReq reqObj){
        CustomResult rspResult = new CustomResult();
        if(StringUtils.isBlank(reqObj.getTestLineInstanceId()) || reqObj.getModified()==null){
            rspResult.setMsg("TestLineInstanceId is not null");
            return rspResult;
        }
        TestLineInstancePO testLineInstancePO = new TestLineInstancePO();

        testLineInstancePO.setID(reqObj.getTestLineInstanceId());
        testLineInstancePO.setModified("1".equals(reqObj.getModified()));
        testLineInstancePO.setModifiedBy(UserHelper.getLocalUser().getRegionAccount());
        testLineInstancePO.setModifiedDate(DateUtils.getNow());

        // 判断是否正常添加，否则回滚
        rspResult.setSuccess(testLineMapper.updateModifiedById(testLineInstancePO) > 0);
        return rspResult;
    }
    /**
     *
     * @param reqObject
     * @return
     */
    public CustomResult getTestLineRemark(TestLineInstanceIdReq reqObject){
        CustomResult rspResult = new CustomResult();
        rspResult.setSuccess(true);
        rspResult.setData(testLineMapper.getTestLineRemark(reqObject.getTestLineInstanceId()));
        return rspResult;
    }


    /**
     *
     * @param reqObject
     * @return
     */
//    @TestLinePending(filedName = "testLineInstanceId",type=TestLinePendingTypeEnums.TL_ID)
    @AccessPolicyRule(testLinePendingType = TestLinePendingTypeEnums.TestLineInstanceId)
    public CustomResult updateRemark(TestLineRemarkReq reqObject){
        CustomResult rspResult = new CustomResult();
        TestLineInstancePO testLineInstancePO = testLineMapper.getTestLineInstanceById(reqObject.getTestLineInstanceId());
        if (TestLineStatus.check(testLineInstancePO.getTestLineStatus(),TestLineStatus.NC)){
            rspResult=checkAssignedSampleByTestLine(testLineInstancePO.getID());
            if (!rspResult.isSuccess()){
                return rspResult;
            }
        }
        TestLineInstancePO testLineInstancePOUpDate = new TestLineInstancePO();
        testLineInstancePOUpDate.setID(reqObject.getTestLineInstanceId());
        testLineInstancePOUpDate.setModifiedBy(UserHelper.getLocalUser().getRegionAccount());

        testLineInstancePOUpDate.setModifiedDate(DateUtils.getNow());
        testLineInstancePOUpDate.setOrdertestLineRemark(reqObject.getTestLineRemark());
        testLineInstancePOUpDate.setTestLineStatus(testLineInstancePO.getTestLineStatus());
        testLineMapper.updateTestlineInstanceRemark(testLineInstancePOUpDate);
        rspResult.setSuccess(true);
        return rspResult;
    }

    /**
     *
     * @param reqObject
     * @return
     */
    public CustomResult getTestStandard(TestStandardReq reqObject){
        CustomResult rspResult = new CustomResult();
        if (reqObject==null|| reqObject.getTestLineInstanceId()==null){
            rspResult.setSuccess(false);
            rspResult.setMsg("Para is null!");
            return  rspResult;
        }
        List<String >testLineIDList=Lists.newArrayList();
        testLineIDList.add(reqObject.getTestLineInstanceId());
        OrderTestLineListSearchReq testLineListSearchReq = new OrderTestLineListSearchReq();
        testLineListSearchReq.setTestLineInstanceIdList(testLineIDList);
        List<OrderTestLineListDTO> orderTestLineInfoList = orderTestLineService.getOrderTestLineInfoList(testLineListSearchReq);

        if (CollectionUtils.isEmpty(orderTestLineInfoList)){
            rspResult.setSuccess(false);
            rspResult.setMsg(" Get testLine error!");
            return  rspResult;
        }

        OrderTestLineListDTO testLineListDTO = orderTestLineInfoList.get(0);
        GeneralOrderInstanceInfoPO orderInfo = orderMapper.getOrderInfoByOrderId(testLineListDTO.getOrderId());
        //获取用户语言,暂时支持中文，后边多语言拓展
        Boolean isChinese = false;
        UserInfo user = tokenClient.getUser();
//        String defaultLanguageCode = user.getDefaultLanguageCode();
        String defaultLanguageCode = frameWorkClient.getPrimaryLanguageCode(ProductLineContextHolder.getProductLineCode());
        if (!UserInfoDefaultLanguageCodeEnums.check(defaultLanguageCode,UserInfoDefaultLanguageCodeEnums.en_us)){
            isChinese = true;
        }
        QueryTestStandardRsp queryTestStandardRsp = buildQueryTestStandardRsp(testLineListDTO,isChinese);

        //TODO v-tl别名修改
        testLineLocalService.build(orderInfo.getOrderNo(),Lists.newArrayList(queryTestStandardRsp));

        rspResult.setData(queryTestStandardRsp);
        rspResult.setSuccess(true);
        return rspResult;
    }

    @NotNull
    public QueryTestStandardRsp buildQueryTestStandardRsp(OrderTestLineListDTO testLineInstancePO,Boolean isChinese) {
        ArtifactCitationRelInfoExample testLineCitationRelInfoExample=new ArtifactCitationRelInfoExample();
        testLineCitationRelInfoExample.createCriteria()
                .andArtifactVersionIdEqualTo(testLineInstancePO.getTestLineVersionId())
                .andArtifactTypeEqualTo(0)
                .andCitationStatusEqualTo(1)
                .andCitationTypeBetween(CitationType.Regulation.getType(), CitationType.Standard.getType());
        List<ArtifactCitationRelInfoPO> testLineCitationRelInfoPOS=testLineCitationRelInfoMapper.selectByExample(testLineCitationRelInfoExample);

        List<ArtifactCitationLangInfoPO> testLineCitationLanguageInfoPOS= Lists.newArrayList();
        List<Long>citationBaseIDList=testLineCitationRelInfoPOS.stream().map(ArtifactCitationRelInfoPO::getId).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(citationBaseIDList)){
            ArtifactCitationLangInfoExample testLineCitationLanguageInfoExample=new ArtifactCitationLangInfoExample();
            testLineCitationLanguageInfoExample.createCriteria().andCitationBaseIdIn(citationBaseIDList).andLangStatusEqualTo(1);
            testLineCitationLanguageInfoPOS=testLineCitationLanguageInfoMapper.selectByExample(testLineCitationLanguageInfoExample);
        }

        Integer versionID = NumberUtil.toInt(testLineInstancePO.getCitationVersionId());
        Integer sectionID = NumberUtil.toInt(testLineInstancePO.getCitationSectionId());

        QueryTestStandardRsp queryTestStandardRsp=new QueryTestStandardRsp();
        queryTestStandardRsp.setTestLineInstanceId(testLineInstancePO.getTestLineInstanceId());
        queryTestStandardRsp.setEvaluationAlias(testLineInstancePO.getTestItem());
        queryTestStandardRsp.setTestLineId(testLineInstancePO.getTestLineId());
        queryTestStandardRsp.setTestLineStatus(testLineInstancePO.getTestLineStatus());
        queryTestStandardRsp.setStandardName(testLineInstancePO.getTestStandard());
        List<TestStandardExtRsp> standardExtRspList=Lists.newArrayList();
        // 替换 Citation 列表查询接口为Local Trims
        CitationListReq citationListReq = new CitationListReq();
        citationListReq.setChinese(isChinese);
        citationListReq.setTestLineVersionId(testLineInstancePO.getTestLineVersionId());
        //不是testLine的,是CSPP 的 需要加上artifactType:1
        /*if(TestLineType.check(testLineInstancePO.getTestLineType(),TestLineType.CSPP) || TestLineType.check(testLineInstancePO.getTestLineType(),TestLineType.CSPP_OPEN)){
            citationListReq.setArtifactType(ArtifactType.PP.getType());
        }*/
        BaseResponse<List<TestStandardExtRsp>> citationRes = citationService.getCitationListByTl(citationListReq);
        if(Func.isNotEmpty(citationRes)&&Func.isNotEmpty(citationRes.getData())){
            standardExtRspList = citationRes.getData();
            standardExtRspList.stream().forEach(standard->{
                Integer standardVersionId = standard.getStandardVersionId();
                Integer standardSectionId = standard.getStandardSectionId();
                standard.setSelected(Func.equals(versionID,standardVersionId)&&Func.equals(sectionID,standardSectionId));
                if(standard.isSelected()){
                    queryTestStandardRsp.setStandardName(standard.getStandardName());
                }
            });
        }
        queryTestStandardRsp.setClientStandard(testLineInstancePO.getClientStandard());
        queryTestStandardRsp.setEditCitationName(testLineInstancePO.getEditCitationName());
        queryTestStandardRsp.setEditCitationNameCN(testLineInstancePO.getEditCitationNameCN());
//        for(ArtifactCitationRelInfoPO testLineCitationRelInfoPO:testLineCitationRelInfoPOS){
//
//            int citationId = NumberUtil.toInt(testLineCitationRelInfoPO.getCitationVersionId(),0);
//            int citationSectionId =  NumberUtil.toInt(testLineCitationRelInfoPO.getCitationSectionId(),0);
//            TestStandardExtRsp objStandardExtRsp=new TestStandardExtRsp();
//            objStandardExtRsp.setSelected(versionID == citationId && sectionID == citationSectionId);
//            objStandardExtRsp.setCitationBaseId(testLineCitationRelInfoPO.getId());
//            objStandardExtRsp.setStandardName(testLineCitationRelInfoPO.getCitationName());
//            if(StringUtils.isNotEmpty(testLineCitationRelInfoPO.getCitationSectionName())) {
//                objStandardExtRsp.setStandardName(testLineCitationRelInfoPO.getCitationName()+","+testLineCitationRelInfoPO.getCitationSectionName());
//            }
//            objStandardExtRsp.setStandardSectionId(testLineCitationRelInfoPO.getCitationSectionId());
//            objStandardExtRsp.setStandardVersionId(testLineCitationRelInfoPO.getCitationVersionId());
//            //阅读下来没有用到这段代码，注释掉了
////            List<ArtifactCitationLangInfoPO>testLineCitationLanguageInfoPOList= testLineCitationLanguageInfoPOS.stream().filter(testLineCitationLanguageInfoPO->testLineCitationRelInfoPO.getId().compareTo(testLineCitationLanguageInfoPO.getCitationBaseId())==0).collect(Collectors.toList());
////            List<TestStandardOtherLanguageItemRsp> testStandardOtherLanguageItemRspList=Lists.newArrayList();
////            testLineCitationLanguageInfoPOList.forEach(testLineCitationLanguageInfoPO->{
////                TestStandardOtherLanguageItemRsp objTTestStandardOtherLanguageItemRsp=new TestStandardOtherLanguageItemRsp();
////                objTTestStandardOtherLanguageItemRsp.setLanguageId(testLineCitationLanguageInfoPO.getLanguageId());
////                objTTestStandardOtherLanguageItemRsp.setMultiEvaluationAlias(testLineCitationLanguageInfoPO.getEvaluationAlias());
////                objTTestStandardOtherLanguageItemRsp.setMultiStandardName(testLineCitationLanguageInfoPO.getCitationName());
////                testStandardOtherLanguageItemRspList.add(objTTestStandardOtherLanguageItemRsp);
////            });
//            //判断中英文
//            if (isChinese && Func.isNotEmpty(testLineCitationLanguageInfoPOS)){
//                ArtifactCitationLangInfoPO artifactCitationLangInfoPO =
//                        testLineCitationLanguageInfoPOS.stream().
//                                filter(testLineCitationLanguage -> Func.equals(testLineCitationLanguage.getCitationBaseId(),testLineCitationRelInfoPO.getId())).findFirst().orElse(null);
//                if (Func.isNotEmpty(artifactCitationLangInfoPO)){
//                    objStandardExtRsp.setStandardName(artifactCitationLangInfoPO.getCitationName());
//                    if(StringUtils.isNotEmpty(artifactCitationLangInfoPO.getCitationSectionName())) {
//                        objStandardExtRsp.setStandardName(artifactCitationLangInfoPO.getCitationName()+","+artifactCitationLangInfoPO.getCitationSectionName());
//                    }
//                }
//            }
//            standardExtRspList.add(objStandardExtRsp);
//        }

        //这里增加一个语言的判断
//        CitationLocalizeHelper.build(standardExtRspList);
        queryTestStandardRsp.setStandardList(standardExtRspList);
        return queryTestStandardRsp;
    }

    /**
     *
     * @param reqObjects
     * @return
     */
    @AccessPolicyRule(reportStatus = { ReportStatus.Approved, ReportStatus.Cancelled },
            subContractType = SubContractOperationTypeEnums.UpdateStandard,
            testLinePendingType = TestLinePendingTypeEnums.SaveStandTestLineId)
//    @TestLinePending(filedName = "standards.testLineInstanceId" ,type=TestLinePendingTypeEnums.TL_ID_List)
    public CustomResult updateStandard(SaveTestStandardReq reqObjects){
        CustomResult rspResult = new CustomResult();
        if (reqObjects==null||CollectionUtils.isEmpty(reqObjects.getStandards())){
            rspResult.setMsg("Standards is null!");
            rspResult.setSuccess(false);
            return rspResult;
        }


        List<StandardReq> standards=reqObjects.getStandards();

        List<String> testLineInstanceIds = Lists.newArrayList();
        Boolean noStandardFlag=false;
        for (StandardReq saveTestStandardReq:standards){
            testLineInstanceIds.add(saveTestStandardReq.getTestLineInstanceId());
            if (saveTestStandardReq.getStandardVersionId()==null||saveTestStandardReq.getStandardVersionId() <= 0){
                noStandardFlag=true;
                break;
            }
        }
        if (noStandardFlag){
            rspResult.setSuccess(false);
            rspResult.setMsg("Invalidate standard!");
            return rspResult;
        }

        Set<Long> citationBaseIds = standards.stream().map(st -> st.getCitationBaseId()).distinct().collect(Collectors.toSet());

//        ArtifactCitationRelInfoExample example = new ArtifactCitationRelInfoExample();
//        example.createCriteria().andIdIn(citationBaseIds);
//        List<ArtifactCitationRelInfoPO> artifactCitationRelInfoPOS = testLineCitationRelInfoMapper.selectByExample(example);
        //接trimslocal接口，待稳定后上面三行代码可移除
        List<GetCitationBaseInfoRsp> artifactCitationRelInfoPOS = citationClient.getCitationBaseInfo(citationBaseIds,null, LanguageType.English);

        Map<Long, GetCitationBaseInfoRsp> citationBaseIdPOMap = artifactCitationRelInfoPOS.stream().collect(Collectors.toMap(GetCitationBaseInfoRsp::getCitationBaseId, Function.identity(), (k1, k2) -> k1));

        Map<String, StandardReq> testLineStandardReqMap = standards.stream().collect(Collectors.toMap(StandardReq::getTestLineInstanceId,Function.identity(),(k1, k2) -> k2));

        Map<String, List<TestAnalyteLanguage>> analyteLanguageMaps=Maps.newHashMap();
        // 查询订单报告语言，如果是单语言报告，需要同时更新对应CN/EN报告
        String reportLanguage ="";
        OrderIdReq orderIdReq = new OrderIdReq();
        orderIdReq.setOrderNo(reqObjects.getOrderNo());
        BaseResponse<OrderAllDTO> orderRes =  orderFacade.getOrderForPe(orderIdReq);
        if(Func.isNotEmpty(orderRes)&&Func.isNotEmpty(orderRes.getData())){
            reportLanguage = orderRes.getData().getReportLanguage();
        }
        OrderTestLineListSearchReq orderTestLineListSearchReq = new OrderTestLineListSearchReq();
        orderTestLineListSearchReq.setTestLineInstanceIdList(testLineInstanceIds);
        List<OrderTestLineListDTO> orderTestLineListDTOList = orderTestLineVMExtMapper.getOrderTestLineListNew(orderTestLineListSearchReq);
        Map<Integer,String> messageMap = new HashMap<>();
        if(Func.isNotEmpty(orderTestLineListDTOList)){
            for (OrderTestLineListDTO orderTestLineListDTO : orderTestLineListDTOList) {
                StandardReq standard = testLineStandardReqMap.getOrDefault(orderTestLineListDTO.getTestLineInstanceId(),null);
                if(Func.isEmpty(standard)){
                    continue;
                }
                if(citationUtil.isProvideByClientStandard(null, Func.toLong(standard.getCitationId()))){
                    if(Func.isEmpty(StringUtils.trim(standard.getEditCitationName())) && ReportLanguage.checkLanguage(reportLanguage,ReportLanguage.EnglishReportOnly)){
                        messageMap.put(orderTestLineListDTO.getTestLineId(),"Please Input StandardName");
                    }else if(Func.isEmpty(StringUtils.trim(standard.getEditCitationNameCN())) && ReportLanguage.checkLanguage(reportLanguage,ReportLanguage.ChineseReportOnly) ){
                        messageMap.put(orderTestLineListDTO.getTestLineId(),"Please Input StandardName(CN)");
                    }else if( ReportLanguage.checkLanguage(reportLanguage,ReportLanguage.EnglishAndChineseReport)){
                        if(Func.isEmpty(StringUtils.trim(standard.getEditCitationName()))){
                            messageMap.put(orderTestLineListDTO.getTestLineId(),"Please Input StandardName");
                        }
                        if(Func.isEmpty(StringUtils.trim(standard.getEditCitationNameCN()))){
                            messageMap.put(orderTestLineListDTO.getTestLineId(),"Please Input StandardName(CN)");
                        }
                    }
                }
            }
        }
        if(Func.isNotEmpty(messageMap)){
            String message = "";
            for (Map.Entry<Integer, String> entry: messageMap.entrySet()){
                if(Func.isNotEmpty(message)){
                    message+="<br>";
                }
                Integer key = entry.getKey();
                String value = entry.getValue();
                message += key;
                message += ":";
                message += value;
            }
            if(Func.isNotEmpty(message)){
                rspResult.setMsg(message);
                rspResult.setSuccess(false);
                return rspResult;
            }
        }

        List<TestLineInstancePO> testLineInstancePOS=testLineMapper.getBaseTestLineByIds(testLineInstanceIds);
        String orderID = testLineInstancePOS.get(0).getGeneralOrderInstanceID();
        GeneralOrderInstanceInfoPO orderInfo = orderMapper.getOrderInfoByOrderId(orderID);

        List<TestLineInstancePO> testLineInstancePOsUpdate = Lists.newArrayList();
        for (TestLineInstancePO testLineInstancePO : testLineInstancePOS) {
            StandardReq standard = testLineStandardReqMap.getOrDefault(testLineInstancePO.getID(),null);
            if(Func.isEmpty(standard)){
                continue;
            }
            Long citationBaseId = standard.getCitationBaseId();
            GetCitationBaseInfoRsp artifactCitationRelInfoPO = citationBaseIdPOMap.get(citationBaseId);
            Integer citationId = artifactCitationRelInfoPO.getCitationId();
            Integer citationVersionId = artifactCitationRelInfoPO.getCitationVersionId();
            String editCitationName = standard.getEditCitationName();
            String editCitationNameCN = standard.getEditCitationNameCN();
            if(Func.equalsSafe(reportLanguage, ReportLanguage.EnglishReportOnly.getCode())){
                editCitationNameCN = editCitationName;
            }
            if(Func.equalsSafe(reportLanguage,ReportLanguage.ChineseReportOnly.getCode())){
                editCitationName = editCitationNameCN;
            }
            standard.setEditCitationName(editCitationName);
            standard.setEditCitationNameCN(editCitationNameCN);
            if (NumberUtil.equals(testLineInstancePO.getStandardVersionID(), standard.getStandardVersionId())
                    && NumberUtil.equals(testLineInstancePO.getStandardSectionID(), standard.getStandardSectionId()) && Func.equals(testLineInstancePO.getClientStandard(),standard.getClientStandard())
            && Func.equals(testLineInstancePO.getCitationName(),standard.getEditCitationName()) && Func.equals(testLineInstancePO.getCitationNameCN(),standard.getEditCitationNameCN())) {
                testLineStandardReqMap.remove(testLineInstancePO.getID());
                testLineInstanceIds.remove(testLineInstancePO.getID());
                continue;
            }
            testLineInstancePO.setCitationBaseId(standard.getCitationBaseId());
            testLineInstancePO.setCitationId(citationId);
            testLineInstancePO.setCitationVersionId(citationVersionId);
            testLineInstancePO.setStandardVersionID(standard.getStandardVersionId());
            testLineInstancePO.setClientStandard(standard.getClientStandard());
            testLineInstancePO.setCitationName(standard.getEditCitationName());
            testLineInstancePOsUpdate.add(testLineInstancePO);
        }

        if (CollectionUtils.isEmpty(testLineInstanceIds)){
            rspResult.setSuccess(true);
            return  rspResult;
        }

        //除了自己外，仍存在相同的TL，则不允许更新。判断条件：testLineStandard.getTestLineId(), citation.getCitationId(), citation.getCitationVersionId(), citation.getCitationSectionId())
        //{"standards":[{"testLineInstanceId":"65a14bf4-375d-4a87-9d1b-6b54bea5ba4a","standardVersionId":3852,"citationBaseId":6808192,"standardSectionId":0,"testLineCitationId":""}]}
        List<MergeTestLineRsp> mergeTestLineRsps = testLineInstanceExtMapper.selectTestLineMergeList(orderID);
        AtomicReference<Boolean> exists = new AtomicReference<>(false);
        AtomicReference<String> fmt = new AtomicReference<>("");
        for (TestLineInstancePO testLineInstancePO : testLineInstancePOsUpdate) {
            mergeTestLineRsps.stream().filter(p -> {
                return
                        (!StringUtils.equalsIgnoreCase(p.getTestLineInstanceId(), testLineInstancePO.getID()))
                                && NumberUtil.equals(testLineInstancePO.getTestLineID(), p.getTestLineId())
                                && NumberUtil.equals(testLineInstancePO.getCitationBaseId(), p.getCitationBaseId());
            }).findFirst().ifPresent(c -> {
                exists.set(true);
                GetCitationBaseInfoRsp artifactCitationRelInfoPO = citationBaseIdPOMap.get(c.getCitationBaseId());
                fmt.set(String.format("TL ID: %s Standard: %s is exists and can not be UPDATE.", c.getTestLineId(), (artifactCitationRelInfoPO == null ? "" : artifactCitationRelInfoPO.getCitationName())));
            });
            if (exists.get()) {
                break;
            }
        }
        if(exists.get()){
            rspResult.setSuccess(false);
            rspResult.setMsg(fmt.get());
            return rspResult;
        }

        List<PPTestLineInfo> ppTestLineInfos =ppMapper.getPpInstanceByTestLineList(testLineInstanceIds);
        //获取需要添加analyte的testLine
        List<PPTestLineInfo> ppSingleTestLineInfos=ppTestLineInfos.stream().filter(ppTestLineInfo -> {
            return ppTestLineInfo.getArtifactId()==null;
        }).collect(Collectors.toList());
        //获取pp testLine,且需要添加analyte的testLine,根据pp分组
        Map<Integer,List<PPTestLineInfo>> ppTestLineInfosMap=ppTestLineInfos.stream().filter(ppTestLineInfo -> {
            return ppTestLineInfo.getArtifactId()!=null;
        }).collect(Collectors.toList()).stream().collect(Collectors.groupingBy(PPTestLineInfo::getArtifactId));
        if (MapUtils.isEmpty(ppTestLineInfosMap)){
            ppTestLineInfosMap=Maps.newHashMap();
        }
        //将单独添加的testLine放在一组
        if (CollectionUtils.isNotEmpty(ppSingleTestLineInfos)){
            ppTestLineInfosMap.put(null,ppSingleTestLineInfos);
        }
        Map<String,TestLineInstancePO>testLineInfoMap=testLineInstancePOsUpdate.stream().collect(Collectors.toMap(TestLineInstancePO::getID,lineInstancePO->lineInstancePO));
        ppTestLineInfos.forEach(ppTestLineInfo -> {
            TestLineInstancePO testLine = testLineInfoMap.get(ppTestLineInfo.getTestLineInstanceID());
            ppTestLineInfo.setTestLineVersionID(testLine.getTestLineVersionID());
            ppTestLineInfo.setStandardVersionID(testLine.getStandardVersionID());
        });
//        List<AnalyteInfoPO> analytePOList = testMatrixService.getTestAnalyteInfoList(ppTestLineInfosMap, orderID,analyteLanguageMaps,false);
        //获取所有analyte
        List<AnalyteInfoPO> dbAnalytes = analyteMapper.getAnalyteByTestLineInstanceIds(testLineInstanceIds);
        Set<Integer> analyteIds = dbAnalytes.stream().map(dba -> dba.getAnalyteID()).collect(Collectors.toSet());
        List<AnalyteInfoPO> analytePOList = testMatrixService.getTrimsLocalAnalyteInfoList(ppTestLineInfosMap, orderID,analyteLanguageMaps,false,analyteIds);
        List<OrderLanguageRelInfoPO> orderLanguageRelInfoDbList= getOrderLanguageRelInfoPOS(orderID);
        //append testline citation language
        List<OrderLanguageRelInfoPO> orderLanguageRelInfoPOSSaved = Lists.newArrayList();
        appendTestlineCitationLanguageRelInfoPOS(orderID, orderLanguageRelInfoDbList, orderLanguageRelInfoPOSSaved, Lists.newArrayList(citationBaseIds));
        Set<String> testLineIdSets = new HashSet<>(testLineInstanceIds);
        orderLangRelService.batchUpdateCitationLangInfo(orderID, testLineIdSets, orderLanguageRelInfoPOSSaved);
        TestLineInstanceMultipleLanguageInfoExample testLineInstanceMultipleLanguageInfoExample = new TestLineInstanceMultipleLanguageInfoExample();
        testLineInstanceMultipleLanguageInfoExample.createCriteria().andTestLineInstanceIDIn(testLineInstanceIds);
        List<TestLineInstanceMultipleLanguageInfoPO> testLineInstanceMultipleLanguageInfoPOS = testLineInstanceMultipleLanguageInfoMapper.selectByExample(testLineInstanceMultipleLanguageInfoExample);
        List<TestLineInstanceMultipleLanguageInfoPO> newMultiLanguageTestLineList = new ArrayList<>();
        List<TestLineInstanceMultipleLanguageInfoPO> updateMultiLanguageTestLineList = new ArrayList<>();
        UserInfo localUser = UserHelper.getLocalUser();

        Date now = DateUtils.getNow();
        for (String testLineInstanceId : testLineInstanceIds) {
            if(Func.isNotEmpty(testLineStandardReqMap.getOrDefault(testLineInstanceId, null))){
                StandardReq standardReq = testLineStandardReqMap.getOrDefault(testLineInstanceId, null);
                TestLineInstancePO testLineInstancePO = testLineInstancePOS.stream().filter(i -> Func.equals(i.getID(), testLineInstanceId)).findAny().orElse(null);
                if(Func.isNotEmpty(testLineInstancePO)){
                    TestLineInstanceMultipleLanguageInfoPO testLineInstanceMultipleLanguageInfoPO = testLineInstanceMultipleLanguageInfoPOS.stream().filter(i -> Func.equals(i.getTestLineInstanceID(), testLineInstanceId)).findAny().orElse(null);
                    if(Func.isNotEmpty(testLineInstanceMultipleLanguageInfoPO)){
                        String editCitationName = standardReq.getEditCitationName();
                        String editCitationNameCN = standardReq.getEditCitationNameCN();
                        if(Func.equalsSafe(reportLanguage, ReportLanguage.EnglishReportOnly.getCode())){
                            editCitationNameCN = editCitationName;
                        }
                        testLineInstanceMultipleLanguageInfoPO.setCitationName(editCitationNameCN);
                        testLineInstanceMultipleLanguageInfoPO.setModifiedBy(Func.isNotEmpty(localUser)?localUser.getRegionAccount():"system");
                        testLineInstanceMultipleLanguageInfoPO.setModifiedDate(now);
                        updateMultiLanguageTestLineList.add(testLineInstanceMultipleLanguageInfoPO);
                    }else{
                        testLineInstanceMultipleLanguageInfoPO = new TestLineInstanceMultipleLanguageInfoPO();
                        testLineInstanceMultipleLanguageInfoPO.setID(UUID.randomUUID().toString());
                        testLineInstanceMultipleLanguageInfoPO.setTestLineInstanceID(testLineInstanceId);
                        testLineInstanceMultipleLanguageInfoPO.setLanguageId(LanguageType.Chinese.getLanguageId());
                        testLineInstanceMultipleLanguageInfoPO.setCreatedDate(now);
                        testLineInstanceMultipleLanguageInfoPO.setCreatedBy(Func.isNotEmpty(localUser)?localUser.getRegionAccount():"system");
                        testLineInstanceMultipleLanguageInfoPO.setModifiedDate(now);
                        testLineInstanceMultipleLanguageInfoPO.setModifiedBy(Func.isNotEmpty(localUser)?localUser.getRegionAccount():"system");
                        String editCitationName = standardReq.getEditCitationName();
                        String editCitationNameCN = standardReq.getEditCitationNameCN();
                        if(Func.equalsSafe(reportLanguage, ReportLanguage.EnglishReportOnly.getCode())){
                            editCitationNameCN = editCitationName;
                        }
                        testLineInstanceMultipleLanguageInfoPO.setCitationName(editCitationNameCN);
                        newMultiLanguageTestLineList.add(testLineInstanceMultipleLanguageInfoPO);
                    }
                }
            }
        }

        rspResult.setSuccess(transactionTemplate.execute((tranStatus)->{
            analyteMapper.deleteAnalyteWithOutLimit(testLineInstanceIds);
            testMatrixService.saveAnalyte(orderID, analytePOList, analyteLanguageMaps, 1);
            testLineMapper.updateBatchStandard(testLineInstancePOsUpdate);
            //update multiLanguageStandard
            if(Func.isNotEmpty(newMultiLanguageTestLineList)){
                testLineMapper.batchSaveMultiLanguageTestLine(newMultiLanguageTestLineList);
            }
            if(Func.isNotEmpty(updateMultiLanguageTestLineList)){
                testLineMapper.updateBatchStandardMultilanguage(updateMultiLanguageTestLineList);
            }
            return true;
        }));
        return rspResult;
    }

    public BaseResponse updateLabSection(SaveLabSectionReq reqObjects){
        return BaseResponse.newFailInstance("已迁移到新接口，检查调用是否准确");
//        BaseResponse response = new BaseResponse();
//        if(Func.isEmpty(reqObjects)||Func.isEmpty(reqObjects.getLabSectionItems())){
//            return response;
//        }
//        List<LabSectionInfoPO> labSections = Lists.newArrayList();
//        List<LabSectionItemReq> labSectionItemReqs = reqObjects.getLabSectionItems();
//        List<String> errorMessage = Lists.newArrayList();
//        labSectionItemReqs.stream().forEach(labSection ->{
//            String testLineInstanceId = labSection.getTestLineInstanceId();
//            TestLineEditDetailRsp oldTestLine = testLineMapper.getEditTestLineInfoById(testLineInstanceId);
//            if(Func.isNotEmpty(oldTestLine) && !Func.equalsSafe(oldTestLine.getLabSectionBaseIdList(),labSection.getLabSectionBaseId())){
//                CustomResult<String> checkLabSectionRes = testLineCmdService.checkLabSection(testLineInstanceId);
//                if(Func.isNotEmpty(checkLabSectionRes)&&Func.isNotEmpty(checkLabSectionRes.getData())){
//                    errorMessage.add(checkLabSectionRes.getData());
//                }
//            }
//            LabSectionInfoPO labSectionInfoPO= new LabSectionInfoPO();
//            labSectionInfoPO.setLabSectionID(labSection.getLabSectionBaseId());
//            labSectionInfoPO.setTestLineInstanceID(testLineInstanceId);
//            labSections.add(labSectionInfoPO);
//        });
//        if(Func.isNotEmpty(errorMessage)){
//            response.setMessage(errorMessage.stream().collect(Collectors.joining(";")));
//            response.setStatus(500);
//            return response;
//        }
//        if(Func.isNotEmpty(labSections)){
//            testLineMapper.batchUpdateTlLabSectionBaseId(labSections);
//        }
//        return response;
    }
    /**
     * description   :  getSubContractInfo
     *
     * @param reqObject
     * @return       : com.sgs.otsnotes.facade.model.common.CustomResult
     * @exception    :
     * @date         : 2020/6/18 10:20 PM
     * <AUTHOR> Killian.Sun  Sun Hengyuan
     */
    public CustomResult getSubContractInfo(SubContractInfoReq reqObject){
        CustomResult rspResult = new CustomResult();
        // 获取各个系统的参数拼装
        SubContractPrintRsp vo = new SubContractPrintRsp();
        SubContractPO subContractPO = subContractMapper.selectByPrimaryKey(reqObject.getSubContractId());
//        //设置分包单价格
//        SubcontractRequirementInfo subcontractRequirementInfo = subcontractRequirementMapper.selectSubcontractPrice(reqObject.getSubContractId());
//        if (subcontractRequirementInfo != null){
//            vo.setSubContractFeeCurrency(subcontractRequirementInfo.getSubcontractFee());
//        }
        GeneralOrderInstanceInfoPO orderByOrderNo = orderMapper.getOrderInfo(subContractPO.getOrderNo());
        String labCode = StringUtils.trim(orderByOrderNo.getLabCode());
        if (LabCodeConsts.LAB_CODE_NJ.equalsIgnoreCase(labCode)) {
            vo.setAddress(subcontractConfig.getLabNjAddress());
            vo.setEmail(subcontractConfig.getNjEmail());
        } else if (LabCodeConsts.LAB_CODE_GZ.equalsIgnoreCase(labCode)) {
            vo.setAddress(subcontractConfig.getLabGzAddress());
            vo.setEmail(subcontractConfig.getGzEmail());
        } else if (StringUtils.equalsIgnoreCase(labCode,LabCodeConsts.LAB_CODE_QD)){
            vo.setAddress(subcontractConfig.getLabQdAddress());
            vo.setEmail(subcontractConfig.getQdEmail());
        } else if (StringUtils.equalsIgnoreCase(labCode,LabCodeConsts.LAB_CODE_XM)){
            vo.setAddress(subcontractConfig.getLabXmAddress());
            vo.setEmail(subcontractConfig.getXmEmail());
        } else if(StringUtils.equalsIgnoreCase(labCode,LabCodeConsts.LAB_CODE_SH)){
            vo.setAddress(subcontractConfig.getLabShAddress());
        } else if(StringUtils.equalsIgnoreCase(labCode,LabCodeConsts.LAB_CODE_HZ)){
            vo.setAddress(subcontractConfig.getLabHzAddress());
            vo.setEmail(subcontractConfig.getHzEmail());
        }else if(StringUtils.equalsIgnoreCase(labCode,LabCodeConsts.LAB_CODE_CZ)){
            vo.setAddress(subcontractConfig.getLabCzAddress());
            vo.setEmail(subcontractConfig.getCzEmail());
        }else if(StringUtils.equalsIgnoreCase(labCode,LabCodeConsts.LAB_CODE_NB)){
            vo.setAddress(subcontractConfig.getLabNbAddress());
            vo.setEmail(subcontractConfig.getNbEmail());
        }else if(StringUtils.equalsIgnoreCase(labCode,LabCodeConsts.LAB_CODE_TJ)){
            vo.setEmail(subcontractConfig.getTjEmail());
        }
        vo.setTo(subContractPO.getSubContractLabName()+" / "+subContractPO.getSubContractContract()+" / "+subContractPO.getSubContractContractTel()+" / "+subContractPO.getSubContractContractEmail());
        vo.setOrderNo(subContractPO.getOrderNo());
        vo.setAdditionalInfo(subContractPO.getAdditionalInfo());
        vo.setServiceType(subContractPO.getSubContractServiceType());

        //zhangtao DIG-3242: 设置分包单备注
        vo.setRemark(subContractPO.getSubContractRemark());

        //String expectedDueDate = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(subContractPO.getSubContractExpectDueDate());
        vo.setExpectedDueDate(DateUtils.format(subContractPO.getSubContractExpectDueDate()));
        vo.setSubContractNo(subContractPO.getSubContractNo());
        GetOrderInfoRsp params = new GetOrderInfoRsp();
        params.setOrderNo(subContractPO.getOrderNo());
        PreOrder preOrder = preOrderClient.getOrderInfo(params);

        vo.setOrderCreateDate(preOrder.getOrderCreateDate());

        String cSContactTel = StringUtil.isBlank(preOrder.getcSContactTel(), StringUtils.EMPTY);
        String responsibleCS = StringUtil.isBlank(preOrder.getResponsibleCS(), StringUtils.EMPTY);
        String cSEmailAddress = StringUtil.isBlank(preOrder.getcSEmailAddress(), StringUtils.EMPTY);

        vo.setFm(String.format("%s / %s %s,%s",orderByOrderNo.getLabCode(), responsibleCS, cSContactTel, cSEmailAddress));
        vo.setBuyer(tranNull(preOrder.getBuyerClientName()));
        vo.setResponsibleTeam(preOrder.getResponsibleTeam());

        ReportLanguage reportLanguage = ReportLanguage.findName(preOrder.getReportLanguage());
        if (reportLanguage!=null){
            vo.setReportLanguage(reportLanguage.getName());
        }
        vo.setCommentRequired(preOrder.getCommentRequired());
        String returnResidueSample = "";
        if(Constants.YES_OR_NO.YES.equalsIgnoreCase(preOrder.getReturnResidueSampleFlag())){
            /**
             * TODO Hengyuan
             * 1、改为：returnResidueSample=String.format("Residue Sample: %s", StringUtil.isBlank(preOrder.getReturnResidueSampleRemark(), ""));
             */
            if(StringUtils.isNoneBlank(preOrder.getReturnResidueSampleRemark())){
                returnResidueSample=String.format("Residue Sample: %s", StringUtil.isBlank(preOrder.getReturnResidueSampleRemark(), ""));
            }else {
                returnResidueSample="Residue Sample: ";
            }
        }

        vo.setReturnResidueSample(returnResidueSample);
        String returnTestedSample = "";
        if(Constants.YES_OR_NO.YES.equalsIgnoreCase(preOrder.getReturnTestedSampleFlag())){
            /**
             * TODO Hengyuan
             * 1、改为：returnTestedSample=String.format("Tested Sample: %s", StringUtil.isBlank(preOrder.getReturnTestedSampleRemark(), ""));
             */
            if(StringUtils.isNoneBlank(preOrder.getReturnTestedSampleRemark())){
                 //returnTestedSample="Tested Sample: "+preOrder.getReturnTestedSampleRemark();
                 returnTestedSample=String.format("Tested Sample:  %s", StringUtil.isBlank(preOrder.getReturnTestedSampleRemark(), ""));
             }else {
                 returnTestedSample="Tested Sample:  ";
             }
        }
        vo.setReturnTestedSample(returnTestedSample);

        List<TestLineInstanceSubContractDTO> testLineInstanceSubContractDTOS =
                queryTestlineList(reqObject.getSubContractId(),subContractPO.getOrderNo());

        //设置TestLine的Remark信息
        if(testLineInstanceSubContractDTOS.size()>0){
            for (int i = 0; i < testLineInstanceSubContractDTOS.size(); i++) {
                TestLineInstanceDTO testLineInstanceDTO = testLineInstanceSubContractDTOS.get(i);
                if(StringUtils.isNotEmpty(testLineInstanceDTO.getOrdertestLineRemark())){
                    vo.setOrdertestLineRemark(testLineInstanceDTO.getOrdertestLineRemark());
                }
            }
        }


        List<SampleDTO> sampleDTOS = subContractExtMapper.querySampleList(reqObject.getSubContractId());
        List<SampleRsp> sampleRsps = new ArrayList<>();
        for (SampleDTO sampleDTO : sampleDTOS){
            SampleRsp newRes = new SampleRsp();
            BeanUtils.copyProperties(sampleDTO,newRes);
            sampleRsps.add(newRes);
        }
        List<TestLineInstanceSubContractRsp> testLineInstanceSubContractRsps = new ArrayList<>();
        for (TestLineInstanceSubContractDTO subContractDTO : testLineInstanceSubContractDTOS){
            TestLineInstanceSubContractRsp newRes = new TestLineInstanceSubContractRsp();
            BeanUtils.copyProperties(subContractDTO,newRes);
            testLineInstanceSubContractRsps.add(newRes);
        }
        testLineLocalService.build(subContractPO.getOrderNo(), testLineInstanceSubContractRsps);

        vo.setTestItemList(testLineInstanceSubContractRsps);
        //vo.setSampleList(sampleDTOS);
        vo.setSampleList(sampleRsps);

        if(StringUtils.isNoneBlank(preOrder.getPhotoRemark())){
            vo.setPhotoRequired(preOrder.getPhotoRequired()+" | "+preOrder.getPhotoRemark());
        }else{
            vo.setPhotoRequired(preOrder.getPhotoRequired());
        }

        rspResult.setData(vo);
        rspResult.setSuccess(true);
        return rspResult;
    }

    public List<TestLineInstanceSubContractDTO> queryTestlineList(String subContractId,String orderNo) {
        List<TestLineSamplesDTO> testLineSampleNos = subContractExtMapper.getSampleNoBySubContractIds(subContractId);
        Map<String, TestLineSamplesDTO> dlesCodeMileMap = testLineSampleNos.stream().collect(Collectors.toMap(TestLineSamplesDTO :: getTestLineInstanceId, o -> o,(key1 , key2)-> key2 ));
        OrderTestLineListSearchReq orderTestLineListSearchReq = new OrderTestLineListSearchReq();
        orderTestLineListSearchReq.setSubContractId(subContractId);
        List<OrderTestLineListDTO> orderTestLineListNew = orderTestLineService.getOrderTestLineInfoList(orderTestLineListSearchReq);
        List<TestLineInstanceSubContractDTO> testLineInstanceSubContractDTOs = new ArrayList<>();
        AnalyteDetailListReq analyteDetailListReq = new AnalyteDetailListReq();
        analyteDetailListReq.setSubContractId(subContractId);
        analyteDetailListReq.setOrderNo(orderNo);
        BaseResponse<List<AnalyteExtPO>> testLineAnalytes = analyteService.getTestLineAnalytes(analyteDetailListReq);
        List<AnalyteExtPO> analyteExtPOList = testLineAnalytes.getData();
        Map<String,List<AnalyteExtPO>> testLineAnalyteDtoMap = new HashMap<>();
        if(Func.isNotEmpty(analyteExtPOList)){
            testLineAnalyteDtoMap = analyteExtPOList.stream().collect(Collectors.groupingBy(b -> b.getTestLineInstanceId()));
        }

        if(Func.isNotEmpty(orderTestLineListNew)){
            for (OrderTestLineListDTO orderTestLinePageListDTO : orderTestLineListNew) {
                TestLineInstanceSubContractDTO testLineInstanceSubContractDTO = new TestLineInstanceSubContractDTO();
                testLineInstanceSubContractDTO.setId(orderTestLinePageListDTO.getTestLineInstanceId());
                TestLineSamplesDTO testLineSamplesDTO = dlesCodeMileMap.getOrDefault(orderTestLinePageListDTO.getTestLineInstanceId(), new TestLineSamplesDTO());

                testLineInstanceSubContractDTO.setSampleNo(testLineSamplesDTO.getSampleNos());
                testLineInstanceSubContractDTO.setTestLineId(Func.isNotEmpty(orderTestLinePageListDTO.getTestLineId())?orderTestLinePageListDTO.getTestLineId().toString():"");
                testLineInstanceSubContractDTO.setTestLineVersionID(Func.isNotEmpty(orderTestLinePageListDTO.getTestLineVersionId())?orderTestLinePageListDTO.getTestLineVersionId().toString():"");
                testLineInstanceSubContractDTO.setEvaluationAlias(orderTestLinePageListDTO.getTestItem());
                testLineInstanceSubContractDTO.setStandardName(orderTestLinePageListDTO.getTestStandard());
                testLineInstanceSubContractDTO.setClientStandard(orderTestLinePageListDTO.getClientStandard());
                testLineInstanceSubContractDTO.setCitationSectionName(orderTestLinePageListDTO.getCitationSectionName());

                AnalyteExtPO analyteExtPO = null;
                List<AnalyteExtPO> analyteExtPOS = testLineAnalyteDtoMap.getOrDefault(orderTestLinePageListDTO.getTestLineInstanceId(), null);
                if(Func.isNotEmpty(analyteExtPOS)){
                    analyteExtPO = analyteExtPOS.get(0);
                }

                List<String> analyteNames = new ArrayList<>();
                if(Func.isNotEmpty(analyteExtPO)){
                    if(Func.isEmpty(analyteExtPO.getAnalyteFlag())){
                        analyteNames = analyteExtPO.getAnalyteList().stream().map(TestLineAnalyteDTO::getTestAnalyteName).collect(Collectors.toList());
                    }else{
                        analyteNames = Lists.newArrayList(AnalyteSectionTypeEnums.findType(analyteExtPO.getAnalyteFlag()).getName());
                    }
                }
                testLineInstanceSubContractDTO.setAnalyteCN(analyteNames);
                testLineInstanceSubContractDTO.setCuttingRequest(orderTestLinePageListDTO.getCuttingRequest());
                testLineInstanceSubContractDTO.setOrdertestLineRemark(orderTestLinePageListDTO.getRemark());

                testLineInstanceSubContractDTOs.add(testLineInstanceSubContractDTO);
            }
        }



        List<String> testLineInstanceIDs = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(testLineInstanceSubContractDTOs)) {
            testLineInstanceSubContractDTOs.forEach(testLineInstanceSubContractDTO -> {
                testLineInstanceIDs.add(testLineInstanceSubContractDTO.getId());
            });
            if (CollectionUtils.isNotEmpty(testLineInstanceIDs)) {
                Map<String, Object> map = new HashMap<String, Object>();
                map.put("testLineInstanceIDs", testLineInstanceIDs);
                List<TestLineInstanceSubContractDTO> testLineInstanceSubContractDTOs2 = subContractExtMapper
                        .getTestConditionByTestLineIdList(testLineInstanceIDs);
                Map<String, String> testConditionMap = new HashMap<>();
                for (TestLineInstanceSubContractDTO testLineInstanceSubContractDTO : testLineInstanceSubContractDTOs2) {
                    if (testLineInstanceSubContractDTO != null) {
                        testConditionMap.put(
                                testLineInstanceSubContractDTO.getId() + "-"
                                        + testLineInstanceSubContractDTO.getMatrixGroupId(),
                                testLineInstanceSubContractDTO.getTestConditionName());
                    }
                }
                for (TestLineInstanceSubContractDTO testLineInstanceSubContractDTO : testLineInstanceSubContractDTOs) {
                    testLineInstanceSubContractDTO
                            .setTestConditionName(testConditionMap.get(testLineInstanceSubContractDTO.getId() + "-"
                                    + testLineInstanceSubContractDTO.getMatrixGroupId()));
                }

            }
        }
        return testLineInstanceSubContractDTOs;
    }
    private  CustomResult checkAssignedSampleByTestLine(String testLineID){
        CustomResult rspResult = new CustomResult();
        TestMatrixInfoExample testMatrixInfoExample=new TestMatrixInfoExample();
        testMatrixInfoExample.createCriteria().andTestLineInstanceIDEqualTo(testLineID);
        List<TestMatrixInfoPO> testMatrixInfoPOS= testMatrixInfoMapper.selectByExample(testMatrixInfoExample);
        if (CollectionUtils.isNotEmpty(testMatrixInfoPOS)){
            rspResult.setSuccess(false);
            rspResult.setMsg("TestLine has assigned sample ,please check!");
            return rspResult;
        }
        rspResult.setSuccess(true);
        return  rspResult;
    }

    /**
     *
     * @param reqObj
     * @return
     */
    @AccessPolicyRule(reportStatus = { ReportStatus.Approved, ReportStatus.Cancelled },
            subContractType = SubContractOperationTypeEnums.NCTestLine,
            testLinePendingType = TestLinePendingTypeEnums.TestLineInstanceId)
//    @TestLinePending(filedName = "testLineInstanceId",type=TestLinePendingTypeEnums.TL_ID)
    public CustomResult ncTestLine(TestLineRemarkReq reqObj){
        final String testLineInstanceId = reqObj.getTestLineInstanceId();
        CustomResult rspResult = new CustomResult();
        SubContractTestLineMappingExample example = new SubContractTestLineMappingExample();
        example.createCriteria().andTestLineInstanceIDEqualTo(testLineInstanceId);
        List<SubContractTestLineMappingPO> mapping = subContractTestLineMappingMapper.selectByExample(example);
        if (CollectionUtils.isNotEmpty(mapping)) {
            rspResult.setSuccess(false);
            rspResult.setMsg("TestLine has subContract ,please check!");
            return rspResult;
        }
        TestLineInstancePO testLineInstancePO = testLineMapper.getTestLineInstanceById(testLineInstanceId);
        CustomResult customResult = TestLineStatusManager.checkTestLineStatus(ActionTestLineStatusMatrixEnum.NC_TL, Lists.newArrayList(testLineInstancePO));
        if(! customResult.isSuccess()){
            rspResult.setSuccess(false);
            rspResult.setMsg("TestLine状态为"+customResult.getMsg()+"才能NC TestLine!");
            return rspResult;
        }

        JobTestLineRelationshipInfoExample jobTestLineRelationshipInfoExample = new JobTestLineRelationshipInfoExample();
        jobTestLineRelationshipInfoExample.createCriteria().andTestlineinstanceidEqualTo(testLineInstanceId);
        List<JobTestLineRelationshipInfoPO> jobTestLineRelationshipInfoPOS = jobTestLineRelationshipInfoMapper.selectByExample(jobTestLineRelationshipInfoExample);
        JobInfoPO jobInfoPOUpdate = null;
        List<JobInfoReq> jobInfoReqs = Lists.newArrayList();
        String  jobID=null;
        if (CollectionUtils.isNotEmpty(jobTestLineRelationshipInfoPOS)) {
            JobTestLineRelationshipInfoPO jobTestLineRelationshipInfoPO = jobTestLineRelationshipInfoPOS.get(0);
            jobID=jobTestLineRelationshipInfoPO.getJobid();
            JobTestLineRelationshipInfoExample jobTestLineRelationshipExample = new JobTestLineRelationshipInfoExample();
            jobTestLineRelationshipExample.createCriteria().andJobidEqualTo(jobID).andTestlineinstanceidNotEqualTo(testLineInstanceId);
            List<JobTestLineRelationshipInfoPO> jobTestLineRelationshipInfoPOSByJobID = jobTestLineRelationshipInfoMapper.selectByExample(jobTestLineRelationshipExample);
            if (CollectionUtils.isEmpty(jobTestLineRelationshipInfoPOSByJobID)){
                JobInfoPO jobInfoPO = jobInfoMapper.selectByPrimaryKey(jobTestLineRelationshipInfoPO.getJobid());
                jobInfoPOUpdate=new JobInfoPO();
                jobInfoPOUpdate.setID(jobID);
                jobInfoPOUpdate.setModifiedBy(UserHelper.getLocalUser().getRegionAccount());
                jobInfoPOUpdate.setModifiedDate(new Date());
                jobInfoPOUpdate.setJobStatus(JobStatus.Cancelled.getStatus());
                jobInfoPOUpdate.setJobNo(jobInfoPO.getJobNo());
                JobInfoReq jobInfoReq = new JobInfoReq();
                jobInfoReq.setJobNo(jobInfoPO.getJobNo());
                jobInfoReq.setJobStatus(JobStatus.Cancelled.getStatus());
                jobInfoReq.setModifiedBy(UserHelper.getLocalUser().getRegionAccount());
                jobInfoReqs.add(jobInfoReq);
            }
        }
        String orderId = testLineInstancePO.getGeneralOrderInstanceID();
        TestLineInstancePO testLineInstancePOUpDate = new TestLineInstancePO();
        testLineInstancePOUpDate.setID(testLineInstancePO.getID());
        testLineInstancePOUpDate.setModifiedBy(UserHelper.getLocalUser().getRegionAccount());

        testLineInstancePOUpDate.setModifiedDate(new Date());
        testLineInstancePOUpDate.setOrdertestLineRemark(reqObj.getTestLineRemark());
        testLineInstancePOUpDate.setTestLineStatus(TestLineStatus.NC.getStatus());
        JobInfoPO jobInfoPOUpdateFinal=  jobInfoPOUpdate;
        String deleteJobID=jobID;
        boolean isSuccess = transactionTemplate.execute((trans) -> {
            testLineMapper.updateTestlineInstanceRemark(testLineInstancePOUpDate);
            CustomResult res=new CustomResult();
            res.setSuccess(true);
            try {
                UserInfo userInfo = tokenClient.getUser();
                BizLogInfo bizLog = new BizLogInfo();
                bizLog.setLab(userInfo.getCurrentLabCode().split(" ")[0]);
                bizLog.setBu(userInfo.getCurrentLabCode().split(" ")[1]);
                bizLog.setOpUser(userInfo.getRegionAccount());
                bizLog.setBizId(testLineInstancePOUpDate.getID());
                bizLog.setOpType("NC TL");
                bizLog.setNewVal(TestLineStatus.NC.getStatus());
                bizLog.setOriginalVal(testLineInstancePO.getTestLineStatus());
                bizLogClient.doSend(bizLog);
            } catch (Exception e) {
                logger.error("subContract status log err:{}",e);
            }
            if (StringUtils.isNotEmpty(deleteJobID)){
                JobTestLineRelationshipInfoExample jobTestLineRelationshipInfoExampleDelete=new JobTestLineRelationshipInfoExample();
                jobTestLineRelationshipInfoExampleDelete.createCriteria().andJobidEqualTo(deleteJobID).andTestlineinstanceidEqualTo(testLineInstanceId);
                jobTestLineRelationshipInfoMapper.deleteByExample(jobTestLineRelationshipInfoExampleDelete);
            }
            if (jobInfoPOUpdateFinal!=null){
                jobInfoMapper.updateByPrimaryKeySelective(jobInfoPOUpdateFinal);
                JobReq reqJob = new JobReq();
                reqJob.setJobs(jobInfoReqs);
                res = jobClient.updateJobBatch(reqJob);
            }

            if (res.isSuccess()) {
                orderStatusValidate(orderId);
            } else {
                trans.setRollbackOnly();
            }
            return res.isSuccess();
        });
        rspResult.setSuccess(isSuccess);
        return rspResult;
    }

    private void orderStatusValidate(String orderId) {
        GeneralOrderInstanceInfoPO order = orderMapper.getOrderInfoByOrderId(orderId);
        List<TestLineInstancePO> testLineInstancePOS = testLineMapper.getTestLineByOrderId(orderId);

        //过滤出非Pretreatment TL
        testLineInstancePOS = testLineInstancePOS.stream().filter(tl->!TestLineType.check(tl.getTestLineType(), TestLineType.Pretreatment)).collect(Collectors.toList());
        //存在测试TL 非 NC,DR,Cancelled
        long count = testLineInstancePOS.stream()
                .filter(testline -> !TestLineStatus.check(testline.getTestLineStatus(),TestLineStatus.NC,TestLineStatus.NA,TestLineStatus.DR,TestLineStatus.Cancelled))
                .count();
        //没有TL了 不做处理
        if(count==0){
            return;
        }
        //找出所有completed的TL
        long validCount = testLineInstancePOS.stream()
                .filter(testline ->TestLineStatus.check(testline.getTestLineStatus(),TestLineStatus.Completed))
                .count();
        //没有completed TL 不做处理
        if(validCount==0){
            return;
        }
        //所有tl 都是complete 可以修改状态了
        if (count == validCount) {
            order.setOrderStatus(OrderStatus.Completed.getStatus());
            order.setModifiedBy(UserHelper.getLocalUser().getRegionAccount());
            order.setModifiedDate(new Date());
            orderMapper.updateOrderStatus(order);
            SysStatusReq reqStatus = new SysStatusReq();
            reqStatus.setObjectNo(order.getOrderNo());
            reqStatus.setIgnoreOldStatus(true);
            reqStatus.setNewStatus(com.sgs.preorder.facade.model.enums.OrderStatus.Reporting.getStatus());
            reqStatus.setUserName(UserHelper.getLocalUser().getRegionAccount());
            statusClient.insertStatusInfo(reqStatus);
        }

    }

    /**
     *
     * @param reqObject
     * @return
     */
    @AccessPolicyRule(orderStatus = {
            com.sgs.preorder.facade.model.enums.OrderStatus.Completed,
            com.sgs.preorder.facade.model.enums.OrderStatus.Closed,
            com.sgs.preorder.facade.model.enums.OrderStatus.Cancelled,
            com.sgs.preorder.facade.model.enums.OrderStatus.Pending }, isDistLock = true)
    public CustomResult saveTestLine(SaveTestLineListReq reqObject){
        CustomResult rspResult = new CustomResult();
        //校验BU
        GeneralOrderInstanceInfoPO orderInfo = orderMapper.getOrderInfoByOrderId(reqObject.getOrderId());
        if (Func.isEmpty(orderInfo)){
            rspResult.setSuccess(false);
            rspResult.setMsg("订单信息查询失败");
            return rspResult;
        }
        OrderInfoDto orderInfoDto = orderClient.getOrderInfoByOrderNo(orderInfo.getOrderNo());
        if (Func.isEmpty(orderInfoDto)){
            rspResult.setSuccess(false);
            rspResult.setMsg("订单信息查询失败");
            return rspResult;
        }
        if (!Func.equals(orderInfoDto.getBUCode(),ProductLineContextHolder.getProductLineCode())){
            rspResult.setSuccess(false);
            rspResult.setMsg("当前BU不允许操作当前订单！");
            return rspResult;
        }
        //rspResult = oldSyncService.syncTrimsDataInfo(reqObject.getOrderId());
        if (!this.saveTestLineList(reqObject, rspResult)){
            rspResult.setSuccess(false);
            return rspResult;
        };
        rspResult.setSuccess(true);
        return rspResult;
    }


    public CustomResult saveAddPpTestLine(SaveTestLineListReq reqObject){
        CustomResult rspResult = new CustomResult(false);
        if(Func.isEmpty(reqObject) || Func.isEmpty(reqObject.getTestLines())){
            rspResult.setSuccess(false);
            rspResult.setMsg("TestLine不能为空");
            return rspResult;
        }
        //校验BU
        GeneralOrderInstanceInfoPO orderInfo = orderMapper.getOrderInfoByOrderId(reqObject.getOrderId());
        if (Func.isEmpty(orderInfo)){
            rspResult.setSuccess(false);
            rspResult.setMsg("订单信息查询失败");
            return rspResult;
        }
        OrderInfoDto orderInfoDto = orderClient.getOrderInfoByOrderNo(orderInfo.getOrderNo());
        if (Func.isEmpty(orderInfoDto)){
            rspResult.setSuccess(false);
            rspResult.setMsg("订单信息查询失败");
            return rspResult;
        }
        if (!Func.equals(orderInfoDto.getBUCode(),ProductLineContextHolder.getProductLineCode())){
            rspResult.setSuccess(false);
            rspResult.setMsg("当前BU不允许操作当前订单！");
            return rspResult;
        }


        List<SaveTestLineReq> saveTestLineReqList = reqObject.getTestLines();

        SaveTestLineListReq saveTestLineListReq = new SaveTestLineListReq();
        List<SaveTestLineReq> saveTestLineReq = saveTestLineReqList.stream().filter(testLine -> NumberUtil.toLong(testLine.getAid()) == 0).collect(Collectors.toList());
        List<SaveTestLineReq> savePpTestLineReq = saveTestLineReqList.stream().filter(testLine -> NumberUtil.toLong(testLine.getAid()) > 0).collect(Collectors.toList());

        // PP and TestLine 需要分开处理
        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(saveTestLineReq)) {
            saveTestLineListReq.setOrderId(reqObject.getOrderId());
            saveTestLineListReq.setTestLines(saveTestLineReq);
            if (!this.saveTestLineList(saveTestLineListReq, rspResult)){
                rspResult.setSuccess(false);
                return rspResult;
            }
        }

        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(savePpTestLineReq)) {
            saveTestLineListReq.setOrderId(reqObject.getOrderId());
            saveTestLineListReq.setTestLines(savePpTestLineReq);
            if (!this.saveTestLineList(saveTestLineListReq, rspResult)){
                rspResult.setSuccess(false);
                return rspResult;
            }
        }
        rspResult.setSuccess(true);
        return rspResult;
    }




    /**
     *
     * @param reqObject
     * @return
     */
    public CustomResult getTestLineClaimInfo(GetTestLineClaimInfoReq reqObject) {
        CustomResult rspResult = new CustomResult();
        rspResult.setSuccess(false);
        if (reqObject.getTestLineInstanceId() == null) {
            rspResult.setMsg("PLEASE CHECK PARAMETER");
            return rspResult;
        }
        String testLineInstanceId = reqObject.getTestLineInstanceId();
        List<TestLineClaimInfo> testLineClaimInfo = testLineMapper.getTestLineClaimInfo(Lists.newArrayList(testLineInstanceId));
        if (CollectionUtils.isEmpty(testLineClaimInfo)) {
            rspResult.setMsg("NO Claim Data");
            return rspResult;
        }
        TestLineInstancePO testLineById = testLineMapper.getTestLineById(testLineInstanceId);
        if (testLineById == null || testLineById.getTestLineType() == null) {
            rspResult.setMsg("NO Claim Data");
            return rspResult;
        }
        Integer testLineType = testLineById.getTestLineType();
        if (!TestLineType.check(testLineType, TestLineType.Claim)) {
            rspResult.setMsg("NO Claim TestLine");
            return rspResult;
        }
        Map<String, List<TestLineClaimInfo>> testLineClaimInfoMap = testLineClaimInfo.stream()
                .collect(Collectors.groupingBy(TestLineClaimInfo::getTestAnalyteDesc));
        List<TestLineAnalyteClaimInfo> collect = testLineClaimInfoMap.entrySet()
                .stream().map(e -> new TestLineAnalyteClaimInfo(e.getKey(), e.getValue()))
                .collect(Collectors.toList());

        GetTestLineClaimInfoRsp getTestLineClaimInfoRsp = new GetTestLineClaimInfoRsp();
        getTestLineClaimInfoRsp.setClaimInfos(collect);

        if (this.checkSubTestLineLock(testLineInstanceId)) {
            getTestLineClaimInfoRsp.setSubOrderLock(true);
        } else {
            getTestLineClaimInfoRsp.setSubOrderLock(false);
        }

        rspResult.setData(getTestLineClaimInfoRsp);
        rspResult.setSuccess(true);
        return rspResult;
    }

    /**
     *
     * @param reqObject
     * @return
     */
    public CustomResult saveTestLineClaimInfo(SaveTestLineClaimInfoReq reqObject) {
        CustomResult rspResult = new CustomResult();
        rspResult.setSuccess(false);
        if(StringUtils.isEmpty(reqObject.getTestLineInstanceId())) {
            rspResult.setMsg("PLEASE CHECK PARAMETER");
            return rspResult;
        }
        String testLineInstanceId = reqObject.getTestLineInstanceId();
        // 判断 是否包含 conclusion
        List<ConclusionInfoPO> conclusionFromTestLine = conclusionMapper.getConclusionFromTestLine(testLineInstanceId);
        if (CollectionUtils.isNotEmpty(conclusionFromTestLine)) {
            rspResult.setMsg("该TestLine 已设置 Conclusion，不能更改Claim！");
            return rspResult;
        }
        List<TestLineAnalyteClaimInfo> claimInfos = reqObject.getClaimInfos();
        if (CollectionUtils.isEmpty(claimInfos)) {
            rspResult.setMsg("PLEASE CHECK PARAMETER");
            return rspResult;
        }

        if (this.checkSubTestLineLock(testLineInstanceId)) {
            rspResult.setSuccess(false);
            rspResult.setMsg(" data locked,Please click unlock button first");
            return rspResult;
        }

        GeneralOrderInstanceInfoPO orderInfo = orderMapper.getOrderByTestLineInstanceId(testLineInstanceId);
        OrderInfoDto order = orderClient.getOrderInfoByOrderNo(orderInfo.getOrderNo());
        if(OperationType.check(order.getOperationType(), OperationType.SubContract)) {
            rspResult.setSuccess(false);
            rspResult.setMsg(" SubContract 单不能更改Claim！");
            return rspResult;
        }

        UserInfo localUser = UserHelper.getLocalUser();
        List<SampleClaimRelInfoPO> sampleClaimRelInfoPOS = Lists.newArrayList();
        for (TestLineAnalyteClaimInfo testLineAnalyteClaimInfo :  claimInfos) {
            if (testLineAnalyteClaimInfo == null ||
                    CollectionUtils.isEmpty(testLineAnalyteClaimInfo.getSampleClaims())) {
                continue;
            }
            List<TestLineClaimInfo> sampleClaims = testLineAnalyteClaimInfo.getSampleClaims();
            for (TestLineClaimInfo claimInfo: sampleClaims) {
                if (StringUtils.isEmpty(claimInfo.getClaimValue()) || StringUtils.isEmpty(claimInfo.getClaimUnit())) {
                    rspResult.setMsg("请填写所有的Claim值！");
                    return rspResult;
                }
                SampleClaimRelInfoPO sampleClaimRelInfoPO = new SampleClaimRelInfoPO();
                sampleClaimRelInfoPO.setId(NumberUtil.toLong(claimInfo.getSampleClaimId()));
                sampleClaimRelInfoPO.setSampleId(claimInfo.getTestSampleID());
                sampleClaimRelInfoPO.setClaimType(claimInfo.getTestAnalyteDesc());
                sampleClaimRelInfoPO.setClaimValue(claimInfo.getClaimValue());
                sampleClaimRelInfoPO.setClaimUnit(claimInfo.getClaimUnit());
                sampleClaimRelInfoPO.setCreatedBy(localUser.getRegionAccount());
                sampleClaimRelInfoPO.setCreatedDate(DateUtils.getNow());
                sampleClaimRelInfoPO.setModifiedBy(localUser.getRegionAccount());
                sampleClaimRelInfoPO.setModifiedDate(DateUtils.getNow());
                sampleClaimRelInfoPOS.add(sampleClaimRelInfoPO);
            }
        }

        rspResult.setSuccess(sampleClaimRelExtMapper.batchInsertAndUpdate(sampleClaimRelInfoPOS) > 0);

        return rspResult;
    }

    /**
     * 判断TestLine 是否是分包TestLine 并且订单是否锁定
     * @param testLineInstanceId
     * @return
     */
    private boolean checkSubTestLineLock(String testLineInstanceId) {
        boolean testLineOrderIsLock = false;
        //校验当前订单是否是内部分包被锁定订单
        List<SubcontractDTO> subcontractDTOS = subContractExtMapper.querySubContractListByTLIds(Lists.newArrayList(testLineInstanceId));
        if(CollectionUtils.isNotEmpty(subcontractDTOS)){
            //有做分包
            SubcontractDTO subcontractDTO = subcontractDTOS.get(0);
            String subContractNo = subcontractDTO.getSubContractNo();
            SubContractExample example=new SubContractExample();
            example.createCriteria().andSubContractNoEqualTo(subContractNo);

            List<SubContractPO> subContractPOs = subContractMapper.selectByExample(example);
            if(CollectionUtils.isNotEmpty(subContractPOs)){
                SubContractPO subContractPO = subContractPOs.get(0);
                Integer subContractOrder = subContractPO.getSubContractOrder();
                //说明是内部分包，需要进行校验
                if(subContractOrder!=null && subContractOrder.compareTo(1)==0){
                    Integer dataLock = subContractPO.getDataLock();
                    if(dataLock!=null && dataLock.compareTo(1)==0){
                        testLineOrderIsLock = true;
                    }
                }
            }
        }
        return testLineOrderIsLock;
    }


    public boolean saveTestLineList(SaveTestLineListReq reqObject, CustomResult rspResult) {
        logger.info("-------------saveTestLineList------reqObject:  "+JSON.toJSONString(reqObject));
        GeneralOrderInstanceInfoPO order = orderMapper.getOrderInfoByOrderId(reqObject.getOrderId());
        if (order == null){
            rspResult.setMsg("Order is  null.");
            return false;
        }
        OrderIdReq orderIdReq = new OrderIdReq();
        orderIdReq.setOrderNo(order.getOrderNo());
        BaseResponse<OrderAllDTO> orderRes =  orderFacade.getOrderForPe(orderIdReq);
        if(Func.isEmpty(orderRes) || orderRes.isFail() || Func.isEmpty(orderRes.getData())){
            rspResult.setMsg("Not Find Order");
            return false;
        }
        List<SaveTestLineReq> testLines = reqObject.getTestLines();
        boolean assignAllLabSection = reqObject.isAssignAllLabSection();
        if(testLines == null || testLines.isEmpty()){
            rspResult.setMsg("SaveTestLineReqs is  Empty.");
            return false;
        }
        OrderAllDTO orderAllDTO = orderRes.getData();
        // ppTestLineType check
//        if(CollectionUtils.isEmpty(testLines.stream().filter(testLine->ArtifactType.check(testLine.getArtifactType())).collect(Collectors.toList()))){
//            rspResult.setMsg("ppTestLineType Illegal parameter");
//            return false;
//        }
        SaveTestLineReq testLineReq = testLines.get(0);
        Boolean isAddPPTL = (NumberUtil.toLong(testLineReq.getPpVersionId()) > 0 || Func.isNotEmpty(testLineReq.getAid()))? true :false;

        //查询BU是否合并TL的配置
        Boolean isMergeTl = this.getMergeTestLineBuParam();
        // 验证添加的TestLine 是否已经在其他层级的Order中添加过
        SubContractOrderInfoDTO contractOrderInfo = new SubContractOrderInfoDTO();
        CustomResult checkResult = subContractOperateService.checkAddTestLineForContract(reqObject.getOrderId(), reqObject.getTestLines(), contractOrderInfo,isMergeTl);
        if (!checkResult.isSuccess()) {
            rspResult.setMsg(checkResult.getMsg());
            return false;
        }
        List<Long> invalidLangIdList =testLineBaseMapper.getInvalidLangList(order.getID());
        /**
         * Add PP时，ArtifactBaseId是tre_trims_pp_artifact_relationship.Id
         * Add TL时，ArtifactBaseId是tb_trims_testline_baseinfo.Id
         */

        String getLabSectionLabCode  = orderClient.getOrderTopsToLabCode(order.getOrderNo());
        Integer labId = order.getOrderLaboratoryID();
        if(Func.isNotEmpty(getLabSectionLabCode)){
            LabInfo labInfoByLabCode = frameWorkClient.getLabInfoByLabCode(getLabSectionLabCode);
            if(Func.isNotEmpty(labInfoByLabCode)){
                labId = labInfoByLabCode.getLaboratoryID();
            }
        }
        List<TestLineStandarBase> testLineStandards = getTestLineStandardBases(testLines, isAddPPTL, labId,assignAllLabSection);

        // DIG-6677 getTestLineByOrderId接口开发
        PPTestLineRelationshipInfoExample ppTestLineRelationshipInfoExample = new PPTestLineRelationshipInfoExample();
        ppTestLineRelationshipInfoExample.createCriteria().andGeneralOrderInstanceIDEqualTo(reqObject.getOrderId());
        List<PPTestLineRelationshipInfoPO> ppTestLineRelationshipInfoPOS = ppTestLineRelationshipInfoMapper.selectByExample(ppTestLineRelationshipInfoExample);
        List<TestLineInstancePO> oldTestLines = trimsLocalTestLineService.getTestLineByOrderId(reqObject.getOrderId());
        Map<String, TestLineInstancePO> testLineMaps= this.getSavedTestLineInstanceList(oldTestLines,testLineStandards,ppTestLineRelationshipInfoPOS,isMergeTl);
        List<OrderLanguageRelInfoPO> orderLanguageRelInfoDbList= getOrderLanguageRelInfoPOS(reqObject.getOrderId());

        List<TestLineInstancePO> testLineInstancePOListSaved= Lists.newArrayList();
        List<PPTestLineRelationshipInfoPO> ppTestLineRelationshipInfoPOListSaved= Lists.newArrayList();

        if(CollectionUtils.isEmpty(testLineStandards)){
            Integer aid = testLineReq.getAid();
            rspResult.setMsg("TestLineID:"+testLineReq.getTestLineId()+(Func.isNotEmpty(aid)?",Aid:"+aid:"")+",citation cannot be empty!");
            return false;
        }
        Map<Integer, PPSectionBaseInfoPO> rootSectionMap = Collections.emptyMap();
        Map<Integer, QueryPPSection> subPPRootSectionMap = Collections.emptyMap();
        List<Long> rootPpBaseIdList = testLines.stream().map(SaveTestLineReq::getRootPpBaseId).map(NumberUtil::toLong).filter(item->item>0).collect(Collectors.toList());
        if(Func.isNotEmpty(rootPpBaseIdList)){
            List<PPBaseInfoWithBLOBs> rootPPList = ppBaseMapper.getPpBaseInfoByIds(rootPpBaseIdList);
            if(Func.isNotEmpty(rootPPList)){
                for (PPBaseInfoWithBLOBs rootPP : rootPPList) {
                    Integer rootPPVersionId = rootPP.getPpVersionId();
                    // 2.所有Tl和subPP的TL都指向rootPP和RootSection
                    // 2.1查询rootSectionList
//            List<PPSectionBaseInfoPO> sectionBaseInfoList = ppSectionMapper.getRootSectionInfoList(rootPPVersionId);
                    // DIG-6873 对接trimsLocal
                    List<GetRootPPSectionInfoRsp> rootSectionInfoListByPpVersionId = ppClient.getRootSectionInfoListByPpVersionId(rootPPVersionId);
                    List<PPSectionBaseInfoPO> sectionBaseInfoList = Lists.newArrayList();
                    rootSectionInfoListByPpVersionId.forEach(rootPPSection -> {
                        PPSectionBaseInfoPO ppSectionBaseInfoPO = new PPSectionBaseInfoPO();
                        BeanUtils.copyProperties(rootPPSection, ppSectionBaseInfoPO);
                        sectionBaseInfoList.add(ppSectionBaseInfoPO);
                    });

                    if (CollectionUtils.isNotEmpty(rootSectionInfoListByPpVersionId)){
                        // 2.2查询rootPP的rootPPSection
                        rootSectionMap = sectionBaseInfoList.stream()
                                .collect(Collectors.toMap(PPSectionBaseInfoPO::getSectionLevel, v -> v));
                        // 2.3查询subPP的rootSectionList
                        subPPRootSectionMap = getSubPpSectionInfo(rootPPVersionId,rootSectionMap);
                    }
                }

            }

        }
        /*Long rootPpBaseId = NumberUtil.toLong(testLineReq.getRootPpBaseId());

        if (rootPpBaseId > 0) {

        }*/

        Set<Long> ppBaseIds = Sets.newHashSet();
        List<Long> citationBaseIds = Lists.newArrayList();
        UserInfo localUser = UserHelper.getLocalUser();
        // 获取 preOrder 中的订单信息
//        int orderStatus= orderClient.getOrderStatusByOrderNo(order.getOrderNo());
        Set<Long> testLineBaseIds = Sets.newHashSet();
        //DIG-6717 获取处理不能合并的tlID
        List<Integer> cannotMergeTestLineIdList = this.cannotMergeTestLine(reqObject);
        testLines.stream().sorted(Comparator.comparing(SaveTestLineReq::getOrderSeq, Comparator.nullsLast(Integer::compareTo))).collect(Collectors.toList());
        List<String> citationUnSelectMsgList=Lists.newArrayList();
        for(SaveTestLineReq saveTestLineReq:testLines){
            // search testlinebase
            TestLineStandarBase testLineStandard = getTestLineStandarBaseByArtifactBaseId(testLineStandards, saveTestLineReq, isAddPPTL);
            logger.info("testLineStandarBase:req:{},stand:{}",JSON.toJSONString(saveTestLineReq),JSON.toJSONString(testLineStandard));
            if(testLineStandard == null){
                //rspResult.setMsg(messageUtil.get("test.line.citation.unselect",new Object[]{saveTestLineReq.getTestLineId()}));
                if(saveTestLineReq.getPpNo()!=null){
                    citationUnSelectMsgList.add(messageUtil.get("test.line.citation.PP.unselect",new Object[]{saveTestLineReq.getPpNo().toString(),saveTestLineReq.getTestLineId().toString(),Func.toStr(saveTestLineReq.getChargeName())}));
                }else{
                    citationUnSelectMsgList.add(messageUtil.get("test.line.citation.TL.unselect",new Object[]{saveTestLineReq.getTestLineId().toString(),Func.toStr(saveTestLineReq.getChargeName())}));
                }
                //return false;
                continue;
            }
            //search testline citation base
            CitationBaseStandarInfo citation = null;
            if(CollectionUtils.isNotEmpty(testLineStandard.getCitationBaseStandarInfos())){
                citation= testLineStandard.getCitationBaseStandarInfos()
                        .stream()
                        .filter(c -> NumberUtil.equals(c.getCitationId(), saveTestLineReq.getStandardId()))
                        .filter(c->NumberUtil.equals(c.getCitationVersionId(),saveTestLineReq.getCitationVersionId()))
                        .findFirst()
                        .orElse(null);
            }
            if(citation==null||citation.getCitationId()==null){
                citation= testLineStandard.getCitationBaseStandarInfos()
                        .stream()
                        .filter(c -> NumberUtil.equals(c.getId(), Func.toLong(saveTestLineReq.getCitationBaseId())))
                        .findFirst()
                        .orElse(null);
                if(citation != null && saveTestLineReq.getStandardId() == null) {
                    saveTestLineReq.setStandardId(citation.getCitationId());
                    saveTestLineReq.setCitationVersionId(citation.getCitationVersionId());
                }
            }
            if(citation==null||citation.getCitationId()==null){
                rspResult.setMsg(messageUtil.get("test.line.citation.is.phaseout",new Object[]{saveTestLineReq.getTestLineId()}));
                logger.info("citationBase is not find,TestLineBaseId="+testLineStandard.getTestLineBaseId()+" TestLineId="+testLineStandard.getTestLineId()+",CitationId:"+saveTestLineReq.getStandardId()+",CitationVersionId:"+saveTestLineReq.getCitationVersionId());
                return false;
            }
            long testLineBaseId = NumberUtil.toLong(testLineStandard.getTestLineBaseId());
            if (testLineBaseId > 0){
                testLineBaseIds.add(testLineBaseId);
            }
            citationBaseIds.add(citation.getId());

            GetSaveTestLineKeyReq getSaveTestLineKeyReq = new GetSaveTestLineKeyReq();
            getSaveTestLineKeyReq.setTestLineVersionId(saveTestLineReq.getTestLineVersionId());
            getSaveTestLineKeyReq.setCitationVersionId(citation.getCitationVersionId());
            getSaveTestLineKeyReq.setCitationSectionId(citation.getCitationSectionId());
            getSaveTestLineKeyReq.setCitationType(citation.getCitationType());
            getSaveTestLineKeyReq.setAid(Func.isEmpty(saveTestLineReq.getAid())?0:Long.valueOf(saveTestLineReq.getAid()));
            getSaveTestLineKeyReq.setTestLineType(saveTestLineReq.getTestLineType());
            getSaveTestLineKeyReq.setMergePpTl(isMergeTl);
            getSaveTestLineKeyReq.setCheckMergeTl(true);
            String key = testLineCmdService.getSaveTestLineKey(getSaveTestLineKeyReq);
//            String key = String.format("%s_%s_%s", testLineStandard.getTestLineId(), citation.getId(), testLineStandard.getPpArtifactRelId());
            TestLineInstancePO orderTestLine = testLineMaps.get(key);

            // DIG-5995 设置Claim值
//            //获取wiForSample
//            TestLineWorkInstructionInfo testLineWorkInstructionInfo = new TestLineWorkInstructionInfo();
//            testLineWorkInstructionInfo.setTestLineVersionId(testLineStandard.getTestLineVersionId());
//            testLineWorkInstructionInfo.setCategoryId(CategoryEnums.WIForCS.getCode());
//            TestLineWorkInstructionRsp claimInfo = workInstructionRelMapper.getClaimByTestLineVersionId(testLineWorkInstructionInfo);
            // DIG-6873 对接 trimsLocal
            List<WorkingInstructionRsp> workingInstructionList = workingInstructionClient.getWorkingInstructionList(Sets.newHashSet(testLineStandard.getTestLineVersionId()), CategoryEnums.WIForCS.getCode());

            //判断是否可以mergeTestline
            // DIG-4947  PreOrderStatus = New（1）和Confirm（3）时合并
            //if(orderTestLine == null || !PreOrderStatus.checkStatus(orderStatus, PreOrderStatus.New, PreOrderStatus.Confirmed))
            //DIG-6717 不需要preorder status的判断了 orderTestLine==null 是需要新增的
            if (cannotMergeTestLineIdList.contains(saveTestLineReq.getTestLineId()) || orderTestLine==null || (isAddPPTL && !isMergeTl)) {
                //build new TestlineInstance 这里是 【不】 进行merge的处理
                orderTestLine = buildTestLineInstancePO(reqObject, testLineStandard, citation, CollectionUtils.isNotEmpty(workingInstructionList),saveTestLineReq);
                testLineMaps.put(key, orderTestLine);
            }
            //quotation ToTest 需要将testLine的orderSeq同步设置为quotation serviceItem的sequenceNo
            if(Func.isNotEmpty(saveTestLineReq.getQuotationToTestFlag()) && saveTestLineReq.getQuotationToTestFlag()){
                orderTestLine.setOrderSeq(saveTestLineReq.getOrderSeq());
            }
            orderTestLine.setModifiedDate(DateUtils.getNow());
            orderTestLine.setPendingFlag(false);
            orderTestLine.setModifiedBy(SecurityContextHolder.getUserInfoFillSystem().getRegionAccount());
            orderTestLine.setCalculateConclusionFlag(ConclusionFlag.Calculate.getFlag());
            orderTestLine.setStyleVersionId(0);
            orderTestLine.setOrderNo(orderAllDTO.getOrderNo());
            orderTestLine.setLabId(orderAllDTO.getLabDTO().getLabId());
            try{
//                OrderInfoDto orderInfoByOrderNo = orderClient.getOrderInfoByOrderNo(order.getOrderNo());
                //获取编码
                String externalOrderNo = orderClient.getExternalOrderNo(order.getOrderNo());
                if(Func.isEmpty(externalOrderNo)){
                    externalOrderNo = order.getOrderNo();
                }
                String number = codeUtil.getTestItemNo(externalOrderNo,orderTestLine.getTestLineID());
                orderTestLine.setTestItemNo(number);
            }catch (Exception e){
                logger.error("获取 testItemNo 失败");
                rspResult.setMsg("获取 testItemNo 失败:"+e.getMessage());
                return false;
            }
            testLineInstancePOListSaved.add(orderTestLine);
            //append ppTestlineRelationship

            Long ppBaseId = appendPpTestLineRelationshipInfoPOS(reqObject.getOrderId(),
                    ppTestLineRelationshipInfoPOListSaved,
                    orderTestLine,
                    testLineStandard,
                    NumberUtil.toLong( saveTestLineReq.getRootPpBaseId()),
                    rootSectionMap,
                    subPPRootSectionMap,saveTestLineReq);
            if (ppBaseId == null || ppBaseId.longValue() <= 0){
                continue;
            }
            ppBaseIds.add(ppBaseId);
        }
        if(Func.isNotEmpty(citationUnSelectMsgList)){
            citationUnSelectMsgList.add(0,messageUtil.get("test.line.citation.updated"));
            rspResult.setMsg(StringUtils.join(citationUnSelectMsgList,"<br/>"));
            return false;
        }
        // 按照规则自动设置PP、TestLine的Seq
        generateTestLineSeq(reqObject.getOrderId(),ppTestLineRelationshipInfoPOListSaved);

        //======append language
        List<OrderLanguageRelInfoPO> orderLangRels = Lists.newArrayList();
        //append pp testline specialized languageSL521052200093FW
        if(isAddPPTL){
            List<Long> ppArtifactRelIds =testLineStandards.stream().map(testLineStandarBase->testLineStandarBase.getPpArtifactRelId()).collect(Collectors.toList());
            //append pptestline language
            appendPpTestlineLanguageRelInfoPOS(reqObject.getOrderId(), orderLanguageRelInfoDbList,orderLangRels, ppArtifactRelIds);
            List<Long> ppBaseSetIds=testLineStandards.stream().map(testLineStandarBase->testLineStandarBase.getPpBaseId()).collect(Collectors.toList());
            //append pp language
            appendPpLanguageRelInfoPOS(reqObject.getOrderId(), orderLanguageRelInfoDbList, orderLangRels, ppBaseSetIds);
            //append pp section language
            List<Long> ppSectionBaseIds=testLineStandards.stream().filter(testLineStandarBase -> testLineStandarBase.getSectionBaseId()!=null&&testLineStandarBase.getSectionBaseId().longValue()>0).map(testLineStandarBase->testLineStandarBase.getSectionBaseId()).collect(Collectors.toList());
            appendPpSectionLanguageRelInfoPOS(reqObject.getOrderId(), orderLanguageRelInfoDbList, orderLangRels, ppSectionBaseIds);

            protocolPackageTestLineRelationshipService.buildSubPpSeq(ppTestLineRelationshipInfoPOListSaved);

        }
        // append testline Language
        appendTestlineLanguageRelInfoPOS(reqObject.getOrderId(), orderLanguageRelInfoDbList,orderLangRels, testLineBaseIds);
        // append testline citation language
        appendTestlineCitationLanguageRelInfoPOS(reqObject.getOrderId(), orderLanguageRelInfoDbList, orderLangRels, citationBaseIds);

        // set dr
        setDrFlag(testLineStandards,testLineInstancePOListSaved, oldTestLines);
        logger.info("-------testLineInstancePOListSaved：  "+JSON.toJSONString(testLineInstancePOListSaved));
        List<SubcontractRelInfoPO> subcontractRels = Lists.newArrayList();
        // 如果当前单是新版内部分包子单，则需要将记录写到 Relationship 中
        if (contractOrderInfo != null && StringUtils.isNotBlank(contractOrderInfo.getOriginalOrderId())) {
            this.appendSubTestLinRelInfoPOS(contractOrderInfo.getOriginalOrderId(), order.getID(), subcontractRels, testLineInstancePOListSaved, ppTestLineRelationshipInfoPOListSaved);
        }

        boolean isSuccess = transactionTemplate.execute((trans) -> {
            // insert testlineinstance
            if(CollectionUtils.isNotEmpty(testLineInstancePOListSaved)){
                testLineMapper.batchInsertForAddTl(testLineInstancePOListSaved);
            }

            // insert ppTestLineRel
            if(CollectionUtils.isNotEmpty(ppTestLineRelationshipInfoPOListSaved)){
                ppTestLineRelMapper.batchInsert(ppTestLineRelationshipInfoPOListSaved);
            }

            // delete LangStatus=0
            if(CollectionUtils.isNotEmpty(invalidLangIdList)){
                testLineBaseMapper.deleteInvalidLangList(invalidLangIdList);
            }
            //
            orderCitationRelService.batchInsert(order.getID(), ppBaseIds, orderLangRels);

            // insert language
            if(CollectionUtils.isNotEmpty(orderLangRels)){
                orderLangRelService.batchInsertOrUpdate(order.getID(), orderLangRels);
            }

            // insert tre_order_subcontract_relationship_mapping
            if (CollectionUtils.isNotEmpty(subcontractRels)) {
                subcontractRelMapper.batchInsert(subcontractRels);
            }
            boolean autoAssignSample = true;
            if(Func.isNotEmpty(reqObject) &&  Func.isNotEmpty(reqObject.getAutoAssignSample()) && !reqObject.getAutoAssignSample()){
                autoAssignSample = false;
            }
            //for quotation
            String source = reqObject.getSource();
            List<TestMatrixPO> testMatrixPOList=this.doMatrixForQuotation(order,reqObject.getTestLines(),ppTestLineRelationshipInfoPOListSaved,testLineInstancePOListSaved,autoAssignSample,source);
            //for quotation condition
            doSaveTestlineCondition(reqObject, testLineInstancePOListSaved, ppTestLineRelationshipInfoPOListSaved,testMatrixPOList);

            SysStatusReq reqStatus = new SysStatusReq();
            reqStatus.setObjectNo(order.getOrderNo());
            reqStatus.setOldStatus(PreOrderStatus.Reporting.getStatus());
            reqStatus.setNewStatus(PreOrderStatus.Testing.getStatus());
            reqStatus.setUserName(SecurityContextHolder.getUserInfoFillSystem().getRegionAccount());
            statusClient.insertStatusInfo(reqStatus);
            TestLineStatusUpdateReq testLineStatusUpdateReq = new TestLineStatusUpdateReq();
            testLineStatusUpdateReq.setOrderNo(order.getOrderNo());
            CustomResult customResult = testLineStatusServiceNew.onChange(testLineStatusUpdateReq);
            if(!customResult.isSuccess()){
                trans.setRollbackOnly();
                return false;
            }
            return true;
        });
        if(isSuccess && reqObject.isManuallyAdd() && orderRes.isSuccess()){
            //记录BizLog
            String regionAccount = SecurityContextHolder.getUserInfoFillSystem().getRegionAccount();
            for (SaveTestLineReq saveTestLineReq : testLines) {
                BizLogInfo bizLog = new BizLogInfo();
                bizLog.setBizOpType(BizLogConstant.TEST_HISTORY);
                bizLog.setLab(orderAllDTO.getLocationCode());
                bizLog.setBu(orderAllDTO.getBUCode());
                bizLog.setOpUser(regionAccount);
                bizLog.setBizId(orderAllDTO.getOrderNo());
                bizLog.setOpType(isAddPPTL?"Add PP":"Add TL");
                bizLog.setNewVal(saveTestLineReq.getTestLineId());
                bizLogClient.doSend(bizLog);
            }
        }
        //更新LabSection；
        List<LabSectionUpdateItemReq> testLineLabSectionList = Lists.newArrayList();
        testLineInstancePOListSaved.forEach(testLineInstancePO -> {
            if(!TestLineStatus.check(testLineInstancePO.getTestLineStatus(),TestLineStatus.DR)){
                //如果SaveLabSectionList为空，则清空LabSection数据
                LabSectionUpdateItemReq labSectionUpdateItemReq = new LabSectionUpdateItemReq();
                labSectionUpdateItemReq.setTestLineInstanceId(testLineInstancePO.getID());
                List<com.sgs.gpo.facade.model.otsnotes.testline.req.LabSectionReq> labSectionList = new ArrayList<>();
                if(Func.isNotEmpty(testLineInstancePO.getSaveLabSectionList())){
                    for (LabSectionBaseInfo labSection : testLineInstancePO.getSaveLabSectionList()) {
                        com.sgs.gpo.facade.model.otsnotes.testline.req.LabSectionReq labSectionReq = new com.sgs.gpo.facade.model.otsnotes.testline.req.LabSectionReq();
                        labSectionReq.setLabSectionBaseId(labSection.getLabSectionBaseId());
                        labSectionReq.setLabSectionId(labSection.getLabSectionId());
                        labSectionList.add(labSectionReq);
                    }
                    labSectionUpdateItemReq.setLabSectionList(labSectionList);
                    testLineLabSectionList.add(labSectionUpdateItemReq);
                }
            }
        });
        if(Func.isNotEmpty(testLineLabSectionList) && isSuccess){
            LabSectionUpdateReq labSectionUpdateReq = new LabSectionUpdateReq();
            labSectionUpdateReq.setOrderId(testLineInstancePOListSaved.get(0).getGeneralOrderInstanceID());
            labSectionUpdateReq.setTestLineLabSectionList(testLineLabSectionList);
            labSectionUpdateReq.setToken(reqObject.getToken());
            labSectionUpdateReq.setProductLineCode(reqObject.getProductLineCode());
            logger.info("gpoTestLineTempClient.updateLabSection:{},{}",labSectionUpdateReq.getProductLineCode(),labSectionUpdateReq.getToken());
            BaseResponse updateLabSectionRsp = gpoTestLineTempClient.updateLabSection(labSectionUpdateReq);
            if(updateLabSectionRsp.isFail()){
                throw new BizException(updateLabSectionRsp.getMessage());
            }
        }
        return isSuccess;
    }

    private boolean getMergeTestLineBuParam(){
        //查询BU是否合并TL的配置
        Boolean isMergeTl = true;
        BuParamReq buParamReq = new BuParamReq();
        buParamReq.setGroupCode(Constants.BU_PARAM.MATRIX.GROUP);
        buParamReq.setParamCode(Constants.BU_PARAM.MATRIX.MERGE_TEST_LINE.CODE);
        buParamReq.setProductLineCode(ProductLineContextHolder.getProductLineCode());
        List<BuParamValueRsp> buParamValueRsps = frameWorkClient.getBuParams(buParamReq);
        if (Func.isNotEmpty(buParamValueRsps) && Func.isNotEmpty(buParamValueRsps.get(0).getParamValue())
                && Func.equals(Constants.BU_PARAM.MATRIX.MERGE_TEST_LINE.VALUES.NO,buParamValueRsps.get(0).getParamValue())){
            isMergeTl = false;
        }
        return isMergeTl;
    }

    private void doSaveTestlineCondition(SaveTestLineListReq reqObject, List<TestLineInstancePO> testLineInstancePOListSaved, List<PPTestLineRelationshipInfoPO> ppTestLineRelationshipInfoPOListSaved,List<TestMatrixPO> testMatrixInfoPOS) {
        List<SaveTestLineReq> saveTestLineReqList= reqObject.getTestLines();
        if(CollectionUtils.isNotEmpty(testLineInstancePOListSaved)&&saveTestLineReqList.stream().anyMatch(e->Func.isNotEmpty(e.getSaveConditionReqList()))){
            if(Func.isEmpty(testMatrixInfoPOS)){
                throw new BizException("Save Condition is failed,TestMatrix is null");
            }
            for(TestLineInstancePO objTestLineInstancePO: testLineInstancePOListSaved){
                PPTestLineRelationshipInfoPO objPPTestLineRelationshipInfoPO= ppTestLineRelationshipInfoPOListSaved.stream().filter(e->e.getTestLineInstanceID().equals(objTestLineInstancePO.getID())).findFirst().orElse(null);
                SaveTestLineReq objSaveTestLineReq=saveTestLineReqList.stream().filter(e->e.getQuotationTestlineInstanceID().equals(objPPTestLineRelationshipInfoPO.getQuotationTestlineInstanceID())).findFirst().orElse(null);
                if(objSaveTestLineReq!=null&&Func.isNotEmpty(objSaveTestLineReq.getSaveConditionReqList())){
                    SaveConditionReq saveConditionReq=new SaveConditionReq();
                    List<TestMatrixPO> testMatrixPOList1= testMatrixInfoPOS.stream().filter(e->e.getTestLineInstanceID().equals(objTestLineInstancePO.getID())).collect(Collectors.toList());
                    List<SampleMatrixReq> sampleMatrixReqs=Lists.newArrayList();
                    if(Func.isNotEmpty(testMatrixPOList1)){
                        for(TestMatrixPO testMatrixPO:testMatrixPOList1){
                            SampleMatrixReq objSampleMatrixReq=new SampleMatrixReq();
                            objSampleMatrixReq.setSampleId(testMatrixPO.getTestSampleID());
                            objSampleMatrixReq.setTestMatrixId(testMatrixPO.getID());
                            sampleMatrixReqs.add(objSampleMatrixReq);
                        }
                    }
                    List<ConditionListReq> conditionList=Lists.newArrayList();
                    for(SaveTestLineConditionReq saveTestLineConditionReq:objSaveTestLineReq.getSaveConditionReqList()){
                        ConditionListReq objConditionListReq=new ConditionListReq();
                        objConditionListReq.setTestConditionTypeBlockLevel(saveTestLineConditionReq.getTestConditionTypeBlockLevel());//todo
                        objConditionListReq.setTestConditionTypeId(saveTestLineConditionReq.getConditionTypeId());
                        objConditionListReq.setTestConditionTypeName(saveTestLineConditionReq.getConditionTypeName());
                        objConditionListReq.setIsConditionTypeBlock(saveTestLineConditionReq.getIsConditionTypeBlock());
                        objConditionListReq.setIsProcedureCondition(saveTestLineConditionReq.getIsProcedureCondition());
                        ConditionItemReq objConditionItemReq=new ConditionItemReq();
                        objConditionItemReq.setTestConditionId(saveTestLineConditionReq.getConditionValueId());
                        objConditionItemReq.setTestConditionName(saveTestLineConditionReq.getConditionValueName());
                        objConditionItemReq.setTestConditionDesc(saveTestLineConditionReq.getTestConditionDesc());
                        objConditionItemReq.setConditionBaseId(saveTestLineConditionReq.getConditionBaseId());
                        objConditionItemReq.setTestConditionSeq(saveTestLineConditionReq.getTestConditionSeq());
                        objConditionListReq.setConditionVoList(Lists.newArrayList(objConditionItemReq));
                        conditionList.add(objConditionListReq);
                    }
                    saveConditionReq.setMatrixSamples(sampleMatrixReqs);
                    saveConditionReq.setConditionList(conditionList);
                    saveConditionReq.setTestLineInstanceId(objTestLineInstancePO.getID());
                    CustomResult customResult=testConditionService.saveCondition(saveConditionReq);
                    if(!customResult.isSuccess()){
                        throw new BizException("Save Condition is failed");
                    }
                }
            }
        }
    }

    /** 判断当前tl是否需要进行合并
     * TL所在Job/Subcontract 为New，添加不同Pp的相同TL，TL应该合并
     TL所在Job/Subcontract 为Testing，添加不同Pp的相同TL，TL应该不合并
     TL所在Job/Subcontract 为Completed，添加不同Pp的相同TL，TL应该不合并
     TL所在Job/Subcontract 为Cancel，添加不同Pp的相同TL，TL应该不合并
     TL所在Job/Subcontract 为New，TL不为Typing，添加不同Pp的相同TL，TL应该不合并
     @Return list 不能进行merge的tl
     */
    private List<Integer> cannotMergeTestLine(SaveTestLineListReq reqObject){
        List<Integer> result = Lists.newArrayList();
        List<SaveTestLineReq> testLines = reqObject.getTestLines();
        //再进一步判断job/subcontract 的状态，判断是否可以合并
        List<Integer> reqTLIds = testLines.stream().map(SaveTestLineReq::getTestLineId).distinct().collect(Collectors.toList());
        TestLineJobSubcontractReqDTO reqDTO = new TestLineJobSubcontractReqDTO();
        reqDTO.setOrderId(reqObject.getOrderId());
        reqDTO.setTestLineIds(reqTLIds);
        List<TestLineJobSubcontractDTO> dtoList = testLineMapper.getTestLineJobSubcontractStatusByOrderID(reqDTO);

        if(org.apache.commons.collections4.CollectionUtils.isEmpty(dtoList)){
            return result;
        }
        //只有testLine 为typeing 且 job 、subcontractstatus 都为new 才能合并，其它清空不能合并
        //过滤出来看是否是非new状态 ，是不予许继续合并的
        List<TestLineJobSubcontractDTO> notNewStatus = dtoList.stream().filter(dto ->{
            boolean isNotTyping = !TestLineStatus.check(dto.getTestLineStatus(), TestLineStatus.Typing);
            if(isNotTyping){
                return true;
            }
            boolean jobOrSubIsNotNew = false;
            if(NumberUtil.toInt(dto.getJobStatus())!=0){
                boolean jobIsNotNew = !JobStatus.check(dto.getJobStatus(), JobStatus.New);
                jobOrSubIsNotNew =  jobOrSubIsNotNew || jobIsNotNew;
            }
            if(dto.getSubStatus()!=null){
                boolean subIsNotNew = !SubContractStatusEnum.check(dto.getSubStatus(), SubContractStatusEnum.Created);
                jobOrSubIsNotNew =  jobOrSubIsNotNew || subIsNotNew;
            }
            return isNotTyping || jobOrSubIsNotNew;
        }).collect(Collectors.toList());

        if(org.apache.commons.collections4.CollectionUtils.isNotEmpty(notNewStatus)){
            return notNewStatus.stream().map(TestLineJobSubcontractDTO::getTestLineId).distinct().collect(Collectors.toList());
        }

        return result;
    }

    private TestMatrixPO createTestMatrixPO(String sampleId,String tlId,String orderId, String userName, Long ppBaseId, Integer citationId){
        TestMatrixPO po = new TestMatrixPO();
        po.setID(UUID.randomUUID().toString());
        po.setModifiedDate(DateUtils.getNow());
        po.setModifiedBy(userName);
        po.setCreatedDate(DateUtils.getNow());
        po.setCreatedBy(userName);
        po.setActiveIndicator(true);
        po.setMatrixGroupId(0);
        po.setTestSampleID(sampleId);
        po.setTestLineInstanceID(tlId);
        po.setGeneralOrderInstanceID(orderId);
        po.setMatrixStatus(MatrixStatus.Typing.getStatus());
        //判断当前TL是否为客供 非客供自动Confirm Matrix
        if(Func.equals(ProductLineContextHolder.getProductLineCode(), com.sgs.framework.model.enums.ProductLineType.HL.getProductLineAbbr()) && !citationUtil.isProvideByClientStandard(ppBaseId,Func.toLong(citationId))){
            po.setMatrixConfirmDate(DateUtils.getNow());
        }
        return po;
    }

    private PPSampleRelationshipInfoPO createPPSampleRelPO(String sampleId,String pptlId,String userName){
        PPSampleRelationshipInfoPO po = new PPSampleRelationshipInfoPO();
        po.setID(UUID.randomUUID().toString());
        po.setPPTLRelID(pptlId);
        po.setTestSampleID(sampleId);
        po.setCreatedBy(userName);
        po.setModifiedBy(userName);
        po.setCreatedDate(DateUtils.getNow());
        po.setModifiedDate(DateUtils.getNow());
        return po;
    }

    public List<TestMatrixPO> doMatrixForQuotation(List<SaveTestLineReq> testLineReqsResult,String orderNo){
        GeneralOrderInstanceInfoPO order = orderMapper.getOrderInfoByOrderNo(orderNo);

        PPTestLineRelationshipInfoExample ppTestLineRelationshipInfoExample = new PPTestLineRelationshipInfoExample();
        ppTestLineRelationshipInfoExample.createCriteria().andGeneralOrderInstanceIDEqualTo(order.getID());
        List<PPTestLineRelationshipInfoPO> ppTestLineRelationshipInfoPOS = ppTestLineRelationshipInfoMapper.selectByExample(ppTestLineRelationshipInfoExample);

        TestLineInstanceExample objTestLineInstanceExample=new TestLineInstanceExample();
        objTestLineInstanceExample.createCriteria().andGeneralOrderInstanceIDEqualTo(order.getID());
        List<TestLineInstancePO> testLineInstancePOList=testLineInstanceMapper.selectByExample(objTestLineInstanceExample);

        return this.doMatrixForQuotation(order,testLineReqsResult,ppTestLineRelationshipInfoPOS,testLineInstancePOList,true,null);

    }


    public List<TestMatrixPO> doMatrixForQuotation(GeneralOrderInstanceInfoPO order, List<SaveTestLineReq> testLineReqsResult,List<PPTestLineRelationshipInfoPO> ppTestLineRelationshipInfoPOSDB,List<TestLineInstancePO> testLineInstancePOList,boolean autoAssignSample,String source) {
        logger.info("doMatrixForQuotation.testLineReqsResult:{}",JSON.toJSONString(testLineReqsResult));
        if(!autoAssignSample){
            return Lists.newArrayList();//如果不是quotation过来的直接返回不需要处理
        }
        //search sample
        String orderNo=order.getOrderNo();
        String orderId=order.getID();
        TestSampleInfoExample objTestSampleInfoExample=new TestSampleInfoExample();
        objTestSampleInfoExample.createCriteria().andOrderNoEqualTo(orderNo);
        List<TestSampleInfoPO> testSampleInfoPOS=testSampleInfoMapper.selectByExample(objTestSampleInfoExample);
        //ReportInfoPO reportInfoPO = this.reportMapper.getReportByOrderNo(orderNo);
        List<ReportInfoPO> reportInfoPOList = this.getAddReportMatrixReport(orderNo,testLineReqsResult,source);
        logger.info("source={},reportList={}",source,JSON.toJSONString(reportInfoPOList));
        Date now = DateUtils.getNow();

        //处理matrix
        List<TestMatrixPO> waitAddMatrixList = new ArrayList<>();
        List<TestMatrixExtPO> waitAddMatrixExtList=Lists.newArrayList();
        List<ReportMatrixRelationShipInfoPO> waitAddReportMatrixRelList = Lists.newArrayList();
        List<PPSampleRelationshipInfoPO> waitAddSampleRelList = new ArrayList<>();

        for(SaveTestLineReq testLineReq:testLineReqsResult){
            Map<String,Set<Integer>> sampleAfMatrixMaps=testLineReq.getSampleAfMatrixMaps();
            if(StringUtils.isNotEmpty(testLineReq.getSampleNos())){
                String[] sampleNoArr=testLineReq.getSampleNos().split(",");
                for(String sampleNo:sampleNoArr){
                    List<TestSampleInfoPO> testSampleInfoPOSd= testSampleInfoPOS.stream().filter(testSampleInfoPO->testSampleInfoPO.getSampleNo().equals(sampleNo)).collect(Collectors.toList());
                    if(CollectionUtils.isNotEmpty(testSampleInfoPOSd)){
                        PPTestLineRelationshipInfoPO ppTestLineRelationshipInfoPO=null;
                        TestLineInstancePO objTestLineInstancePO=null;
                         if(testLineReq.getAid()!=null&&testLineReq.getAid().intValue()>0){//如果有aid
                             //ppTestLineRelationshipInfoPO=ppTestLineRelationshipInfoPOSDB.stream().filter(ppTlRel->ppTlRel.getAid()!=null&&ppTlRel.getAid().toString().equals(testLineReq.getAid().toString())).findFirst().orElse(null);
                             ppTestLineRelationshipInfoPO=ppTestLineRelationshipInfoPOSDB.stream().filter(ppTlRel->ppTlRel.getAid()!=null&&ppTlRel.getAid().toString().equals(testLineReq.getAid().toString()) && Func.equalsSafe(ppTlRel.getPpBaseId(),testLineReq.getPpBaseId())).findFirst().orElse(null);
                             if(ppTestLineRelationshipInfoPO==null){
                                 continue;
                             }
                             String tlInstanceId=ppTestLineRelationshipInfoPO.getTestLineInstanceID();
                             objTestLineInstancePO=testLineInstancePOList.stream().filter(e->e.getID().equals(tlInstanceId)).findFirst().orElse(null);
                             if(objTestLineInstancePO==null){
                                 continue;
                             }
                         }else{
                             objTestLineInstancePO=testLineInstancePOList.stream().filter(e->e.getTestLineVersionID().intValue()==testLineReq.getTestLineVersionId()&&e.getCitationVersionId().intValue()==testLineReq.getCitationVersionId().intValue()).findFirst().orElse(null);
                             if(objTestLineInstancePO==null){
                                 continue;
                             }
                             String tlInstanId=objTestLineInstancePO.getID();
                             ppTestLineRelationshipInfoPO=ppTestLineRelationshipInfoPOSDB.stream().filter(ppTlRel->ppTlRel.getTestLineInstanceID().equals(tlInstanId)).findFirst().orElse(null);
                             if(ppTestLineRelationshipInfoPO==null){
                                 continue;
                             }
                         }

                        TestSampleInfoPO objTestSampleInfoPO=testSampleInfoPOSd.get(0);
                        String testSampleId=objTestSampleInfoPO.getID();
                        String testLineInstanceId=ppTestLineRelationshipInfoPO.getTestLineInstanceID();
                        String userName=UserHelper.getLocalUser().getRegionAccount();
                        String ppTlRelId=ppTestLineRelationshipInfoPO.getID();
                        //testMatrix
                        TestMatrixPO testMatrixPO=this.createTestMatrixPO(testSampleId,testLineInstanceId,orderId,userName,ppTestLineRelationshipInfoPO.getPpBaseId(),objTestLineInstancePO.getCitationId());
                        waitAddMatrixList.add(testMatrixPO);
                        if(sampleAfMatrixMaps!=null&&sampleAfMatrixMaps.get(sampleNo)!=null){
                            TestMatrixExtPO objTestMatrixExtPO=new TestMatrixExtPO();
                            objTestMatrixExtPO.setTestMatrixId(testMatrixPO.getID());
                            objTestMatrixExtPO.setApplicationFactorId(JSON.toJSONString(sampleAfMatrixMaps.get(sampleNo)));
                            waitAddMatrixExtList.add(objTestMatrixExtPO);
                        }
                        //reportMatrixRelation
                        if(Func.equalsSafe(source,Constants.ASSIGN_SAMPLE_SOURCE.DATA_ENTRY)){
                            if(Func.isNotEmpty(reportInfoPOList)){
                                for(ReportInfoPO reportInfoPO:reportInfoPOList){
                                    ReportMatrixRelationShipInfoPO po = new ReportMatrixRelationShipInfoPO();
                                    po.setID(UUID.randomUUID().toString());
                                    po.setReportID(reportInfoPO.getID());
                                    po.setTestMatrixID(testMatrixPO.getID());
                                    po.setCreatedBy(userName);
                                    po.setCreatedDate(now);
                                    waitAddReportMatrixRelList.add(po);
                                }
                            }
                        } else {
                            if(Func.isNotEmpty(reportInfoPOList) && reportInfoPOList.size() == 1){
                                ReportMatrixRelationShipInfoPO po = new ReportMatrixRelationShipInfoPO();
                                po.setID(UUID.randomUUID().toString());
                                po.setReportID(reportInfoPOList.get(0).getID());
                                po.setTestMatrixID(testMatrixPO.getID());
                                po.setCreatedBy(userName);
                                po.setCreatedDate(now);
                                waitAddReportMatrixRelList.add(po);
                            }
                        }
                        //PPSampleRelationshipInfoPO
                        PPSampleRelationshipInfoPO objPPSampleRelationshipInfoPO=this.createPPSampleRelPO(testSampleId,ppTlRelId,userName);
                        waitAddSampleRelList.add(objPPSampleRelationshipInfoPO);

                    }

                }
            }
        }


        int iFlag=transactionTemplate.execute((tranStatus)->{
            //添加ppSampleRel
            if(CollectionUtils.isNotEmpty(waitAddSampleRelList)){
                this.ppSampleRelMapper.batchInsert(waitAddSampleRelList);
            }
            //添加matrix
            if(CollectionUtils.isNotEmpty(waitAddMatrixList)){
                this.testMatrixMapper.batchInsert(waitAddMatrixList);
            }
            //添加matrix
            if(CollectionUtils.isNotEmpty(waitAddMatrixExtList)){
                this.testMatrixExtMapper.insertBatch(waitAddMatrixExtList);
            }
            //添加reportMatrix
            if(CollectionUtils.isNotEmpty(waitAddReportMatrixRelList)){
                this.reportMatrixRelMapper.batchInsert(waitAddReportMatrixRelList);
            }
            TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
                @Override
                public void afterCommit() {
                    try {
                        if(Func.isNotEmpty(waitAddReportMatrixRelList)){
                            TLDataHeaderSaveReq tlDataHeaderSaveReq = new TLDataHeaderSaveReq();
                            tlDataHeaderSaveReq.setToken(SystemContextHolder.getSgsToken());
                            tlDataHeaderSaveReq.setProductLineCode(ProductLineContextHolder.getProductLineCode());
                            tlDataHeaderSaveReq.setCheckFlag(false);
                            tlDataHeaderSaveReq.setOrderNo(orderNo);
                            reportTempFacade.saveTestDataHeader(tlDataHeaderSaveReq);
                        }
                    } catch (Exception e) {
                        logger.error("add TestLine saveTestDataHeader error:{}",e);
                    }
                }
            });
            return 1;
        });
        return waitAddMatrixList;
    }

    private List<ReportInfoPO> getAddReportMatrixReport(String orderNo,List<SaveTestLineReq> testLineReqsResult, String source){
        List<ReportInfoPO> reportInfoPOList = new ArrayList<>();
        //根据source判断 source = DataEntry; other:quotation to test
        if(Func.equalsSafe(source,Constants.ASSIGN_SAMPLE_SOURCE.DATA_ENTRY)){
            //DataEntry 查询报告逻辑：
            //1.聚合 ppBaseId
            List<Long> ppBaseIds = testLineReqsResult.stream().map(SaveTestLineReq::getPpBaseId).filter(Func::isNotEmpty).collect(Collectors.toList());
            if(Func.isEmpty(ppBaseIds)){
                return reportInfoPOList;
            }
            //2. 查询哪些Report与传入的ppBaseId有交集
            logger.info("addReportMAtrixReport.orderNo:{},ppBaseId:{}",orderNo,JSON.toJSONString(ppBaseIds));
            reportInfoPOList = reportMapper.selectReportByPpBaseIds(orderNo,ppBaseIds);
            logger.info("addReportMAtrixReport.reportList:{}",JSON.toJSONString(reportInfoPOList));
            if(Func.isEmpty(reportInfoPOList)){
                //如果為空且order下有且僅有一份有效報告 返回這個
                ReportInfoExample example = new ReportInfoExample();
                example.createCriteria().andOrderNoEqualTo(orderNo).andReportStatusNotIn(Arrays.asList(ReportStatus.Cancelled.getCode(), ReportStatus.Reworked.getCode(),ReportStatus.Replaced.getCode()));
                List<ReportInfoPO> orderReportInfoPOList = reportInfoMapper.selectByExample(example);
                if(Func.isNotEmpty(orderReportInfoPOList) && orderReportInfoPOList.size() == 1){
                    return orderReportInfoPOList;
                }
            }
        } else {
            //Quotation to test 查询报告逻辑，当前订单下的有效报告
            ReportInfoExample example = new ReportInfoExample();
            example.createCriteria().andOrderNoEqualTo(orderNo).andReportStatusNotIn(Arrays.asList(ReportStatus.Cancelled.getCode(), ReportStatus.Reworked.getCode(),ReportStatus.Replaced.getCode()));
            reportInfoPOList = reportInfoMapper.selectByExample(example);
        }
        return reportInfoPOList;
    }
    private TestLineStandarBase getTestLineStandarBaseByArtifactBaseId(List<TestLineStandarBase> testLineStandards, SaveTestLineReq reqObject, Boolean isAddPPTL) {
        ArtifactType artifactType = isAddPPTL ? ArtifactType.PP : ArtifactType.TestLine;

        ArtifactType finalArtifactType = artifactType;
        return testLineStandards.stream().filter(testLineStandard->
            (
                finalArtifactType == ArtifactType.TestLine &&
                NumberUtil.equals(testLineStandard.getTestLineBaseId(), reqObject.getArtifactBaseId())
            ) ||
            (
                finalArtifactType == ArtifactType.PP &&
                NumberUtil.equals(testLineStandard.getPpArtifactRelId(), reqObject.getArtifactBaseId()) && NumberUtil.equals(testLineStandard.getTestLineType(), reqObject.getTestLineType(), true)
                //NumberUtil.equals(testLineStandard.getBasePpVersionId(), reqObject.getPpVersionId()) && NumberUtil.equals(testLineStandard.getTestLineVersionId(), reqObject.getTestLineVersionId())
            )
        ).findFirst().orElse(null);
    }

    private Long appendPpTestLineRelationshipInfoPOS(String orderId,List<PPTestLineRelationshipInfoPO> ppTestLineRelationshipInfoPOListSaved,
              TestLineInstancePO objTestLineInstancePO,TestLineStandarBase objTestLineStandarBase, Long rootPpBaseId,
                                                     Map<Integer, PPSectionBaseInfoPO> rootSectionMap,Map<Integer, QueryPPSection> subPPRootSectionMap,SaveTestLineReq saveTestLineReq) {
        PPTestLineRelationshipInfoPO ppTestLineRelationship = buildPpTestLineRelationshipInfoPO(orderId,objTestLineInstancePO,objTestLineStandarBase, rootPpBaseId,rootSectionMap,subPPRootSectionMap);
        ppTestLineRelationship.setQuotationTestlineInstanceID(saveTestLineReq.getQuotationTestlineInstanceID());
        ppTestLineRelationship.setConstructionId(saveTestLineReq.getConstructionId());
        if(StringUtils.isNotEmpty(saveTestLineReq.getSectionName())){
            ppTestLineRelationship.setSectionName(saveTestLineReq.getSectionName());
        }
        if(Func.isNotEmpty(saveTestLineReq.getSectionId())){
            ppTestLineRelationship.setSectionID(saveTestLineReq.getSectionId());
        }
        Map<String,Object> paramMaps=Maps.newHashMap();
        if(saveTestLineReq.getParentAid()!=null&&saveTestLineReq.getParentAid().intValue()>0){
            paramMaps.put("ppAid",saveTestLineReq.getParentAid());
        }
        if(Func.isNotEmpty(saveTestLineReq.getParentConstractionId())){
            paramMaps.put("ppConstructionId",saveTestLineReq.getParentConstractionId());
        }
        ppTestLineRelationship.setConstructionId(saveTestLineReq.getConstructionId());
        paramMaps.put("parentTestLineInstanceId",saveTestLineReq.getParentTestLineInstanceId());
        ppTestLineRelationship.setExtFields(JSON.toJSONString(paramMaps));

        ppTestLineRelationshipInfoPOListSaved.add(ppTestLineRelationship);
        return ppTestLineRelationship.getPpBaseId();
    }


    private void appendTestlineCitationLanguageRelInfoPOS(String orderId, List<OrderLanguageRelInfoPO> orderLanguageRelInfoPOsExisted, List<OrderLanguageRelInfoPO> orderLanguageRelInfoPOSSaved, List<Long> citationBaseIds) {
        if (citationBaseIds == null || citationBaseIds.isEmpty()){
            return;
        }
        TestlineCitationLanguageDTO objTestlineCitationLanguageDTO=new TestlineCitationLanguageDTO();
        objTestlineCitationLanguageDTO.setCitationBaseIds(citationBaseIds);
        List<ArtifactCitationLangInfoPO> citationLangs = testLineBaseMapper.getTlCitationLanguage(objTestlineCitationLanguageDTO);
        if (citationLangs == null || citationLangs.isEmpty()){
            return;
        }
        for(ArtifactCitationLangInfoPO citationLang: citationLangs){
            if(!orderLanguageRelInfoPOsExisted.stream().anyMatch(orderLanguageRelInfoPO -> orderLanguageRelInfoPO.getLangBaseId().longValue()==citationLang.getLangId().longValue()&&orderLanguageRelInfoPO.getLangType().intValue()== LangTypeEnum.Citation.getType())){
                //build new OrderLanguageRelInfoPO
                orderLanguageRelInfoPOSSaved.add(buildOrderLanguageRelIInfo(citationLang.getCitationBaseId(),citationLang.getLanguageId(),citationLang.getLangId(),orderId,LangTypeEnum.Citation.getType()));
            }
        }
    }

    private void appendPpTestlineLanguageRelInfoPOS(String orderId, List<OrderLanguageRelInfoPO> orderLanguageRelInfoDbList,List<OrderLanguageRelInfoPO> orderLanguageRelInfoPOSSaved, List<Long> ppArtifactRelIds) {
        List<TrimsPPTestLineLanguageInfoPO> trimsPPTestLineLanguageInfoPOS=getPpTestLineLanguageInfoPOS(ppArtifactRelIds);
        //build testline language list
        if(CollectionUtils.isNotEmpty(trimsPPTestLineLanguageInfoPOS)){
            for(TrimsPPTestLineLanguageInfoPO trimsPPTestLineLanguageInfoPO:trimsPPTestLineLanguageInfoPOS){
                if(!orderLanguageRelInfoDbList.stream().anyMatch(orderLanguageRelInfoPO -> orderLanguageRelInfoPO.getLangBaseId().intValue()==trimsPPTestLineLanguageInfoPO.getLangId().intValue()&&orderLanguageRelInfoPO.getLangType().intValue()== LangTypeEnum.PpArtifactRel.getType())){
                    //build new OrderLanguageRelInfoPO
                    orderLanguageRelInfoPOSSaved.add(buildOrderLanguageRelIInfo(trimsPPTestLineLanguageInfoPO.getPpArtifactRelId(),trimsPPTestLineLanguageInfoPO.getLanguageId(),trimsPPTestLineLanguageInfoPO.getLangId(),orderId,LangTypeEnum.PpArtifactRel.getType()));
                }
            }
        }
    }

    private void appendTestlineLanguageRelInfoPOS(String orderId, List<OrderLanguageRelInfoPO> orderLanguageRelInfoDbList,List<OrderLanguageRelInfoPO> orderLanguageRelInfoPOSSaved, Set<Long> testLineBaseIds) {
        TestLineLanguageDTO objTestLineLanguageDTO = new TestLineLanguageDTO();
        objTestLineLanguageDTO.setTestLineBaseIds(Lists.newArrayList(testLineBaseIds));
        List<TestLineLanguageInfoPO> testLineLangs = testLineBaseMapper.getTestlineLanguage(objTestLineLanguageDTO);
        if (testLineLangs == null || testLineLangs.isEmpty()){
            return;
        }
        for(TestLineLanguageInfoPO testLineLanguageInfoPO: testLineLangs){
            if(!orderLanguageRelInfoDbList.stream().anyMatch(orderLanguageRelInfoPO -> orderLanguageRelInfoPO.getLangBaseId().intValue()==testLineLanguageInfoPO.getLangId().intValue()&&orderLanguageRelInfoPO.getLangType().intValue()== LangTypeEnum.TestLine.getType())){
                //build new OrderLanguageRelInfoPO
                orderLanguageRelInfoPOSSaved.add(buildOrderLanguageRelIInfo(testLineLanguageInfoPO.getTestLineBaseId(),testLineLanguageInfoPO.getLanguageId(),testLineLanguageInfoPO.getLangId(),orderId,LangTypeEnum.TestLine.getType()));
            }
        }
    }

    private void appendPpLanguageRelInfoPOS(String orderId, List<OrderLanguageRelInfoPO> orderLanguageRelInfoPOS_existed,List<OrderLanguageRelInfoPO> orderLanguageRelInfoPOSSaved, List<Long> ppBaseIds) {
        PpLanguageDTO ppLanguageDTO=new PpLanguageDTO();
        ppLanguageDTO.setPpBaseIds(ppBaseIds);
        List<PPLanguageInfoPO> ppLangs = testLineBaseMapper.getPpLanguage(ppLanguageDTO);
        if (ppLangs == null || ppLangs.isEmpty()){
            return;
        }
        // build testline language list
        for(PPLanguageInfoPO ppLang: ppLangs){
            if(!orderLanguageRelInfoPOS_existed.stream().anyMatch(langRel -> langRel.getLangBaseId().intValue()==ppLang.getLangId().intValue()&&langRel.getLangType().intValue()== LangTypeEnum.PP.getType())){
                //build new OrderLanguageRelInfoPO
                orderLanguageRelInfoPOSSaved.add(buildOrderLanguageRelIInfo(ppLang.getPpBaseId(),ppLang.getLanguageId(),ppLang.getLangId(),orderId,LangTypeEnum.PP.getType()));
            }
        }
    }

    private void appendPpSectionLanguageRelInfoPOS(String orderId, List<OrderLanguageRelInfoPO> orderLanguageRelInfoPOS_existed,List<OrderLanguageRelInfoPO> orderLanguageRelInfoPOSSaved, List<Long> ppSectionBaseIds) {
        if (CollectionUtils.isEmpty(ppSectionBaseIds)){
            return;
        }
        PpSectionLanguageDTO ppSectionLanguageDTO=new PpSectionLanguageDTO();
        ppSectionLanguageDTO.setSectionBaseIds(ppSectionBaseIds);
        List<PPSectionLanguageInfoPO> ppSectionLanguageInfoPOList=testLineBaseMapper.getPpSectionLanguage(ppSectionLanguageDTO);
        //build testline language list
        if(CollectionUtils.isNotEmpty(ppSectionLanguageInfoPOList)){
            for(PPSectionLanguageInfoPO ppSectionLanguageInfoPO:ppSectionLanguageInfoPOList){
                if(!orderLanguageRelInfoPOS_existed.stream().anyMatch(orderLanguageRelInfoPO -> orderLanguageRelInfoPO.getLangBaseId().intValue()==ppSectionLanguageInfoPO.getLangId().intValue()&&orderLanguageRelInfoPO.getLangType().intValue()== LangTypeEnum.Section.getType())){
                    //build new OrderLanguageRelInfoPO
                    orderLanguageRelInfoPOSSaved.add(buildOrderLanguageRelIInfo(ppSectionLanguageInfoPO.getSectionBaseId(),ppSectionLanguageInfoPO.getLanguageId(),ppSectionLanguageInfoPO.getLangId(),orderId,LangTypeEnum.Section.getType()));
                }
            }
        }
    }


    /**
     *
     * @param originalOrderId
     * @param subOrderId
     * @param subcontractRels
     * @param testLineInstancePOList
     */
    private void appendSubTestLinRelInfoPOS(String originalOrderId, String subOrderId,
                                            List<SubcontractRelInfoPO> subcontractRels,
                                            List<TestLineInstancePO> testLineInstancePOList,
                                            List<PPTestLineRelationshipInfoPO> ppTestLineRelList) {
        if (CollectionUtils.isNotEmpty(testLineInstancePOList)) {
            for (TestLineInstancePO testLinePO : testLineInstancePOList){
                SubcontractRelInfoPO subcontractRelInfoPO = new SubcontractRelInfoPO();
                subcontractRelInfoPO.setOriginalOrderId(originalOrderId);
                subcontractRelInfoPO.setSubOrderId(subOrderId);
                // add Tl时 主单rel字段设置null
                subcontractRelInfoPO.setOriginalRelId(null);
                subcontractRelInfoPO.setSubRelId(testLinePO.getID());
                subcontractRelInfoPO.setRelType(TableType.TestLine.getTableId());
                subcontractRelInfoPO.setEventType(EventType.None.getType());
                subcontractRelInfoPO.setStatus(1);
                subcontractRelInfoPO.setCreatedBy(UserHelper.getLocalUser().getRegionAccount());
                subcontractRelInfoPO.setModifiedBy(UserHelper.getLocalUser().getRegionAccount());
                subcontractRelInfoPO.setCreatedDate(DateUtils.getNow());
                subcontractRelInfoPO.setModifiedDate(DateUtils.getNow());
                subcontractRels.add(subcontractRelInfoPO);
            }
        }
        if (CollectionUtils.isNotEmpty(ppTestLineRelList)) {
            for (PPTestLineRelationshipInfoPO ppTestLineRelationshipInfos : ppTestLineRelList){
                SubcontractRelInfoPO subcontractRelInfoPO = new SubcontractRelInfoPO();
                subcontractRelInfoPO.setOriginalOrderId(originalOrderId);
                subcontractRelInfoPO.setSubOrderId(subOrderId);
                // add Tl时 主单rel字段设置null
                subcontractRelInfoPO.setOriginalRelId(null);
                subcontractRelInfoPO.setSubRelId(ppTestLineRelationshipInfos.getID());
                subcontractRelInfoPO.setRelType(TableType.PPTestLineRel.getTableId());
                subcontractRelInfoPO.setEventType(EventType.None.getType());
                subcontractRelInfoPO.setStatus(1);
                subcontractRelInfoPO.setCreatedBy(UserHelper.getLocalUser().getRegionAccount());
                subcontractRelInfoPO.setModifiedBy(UserHelper.getLocalUser().getRegionAccount());
                subcontractRelInfoPO.setCreatedDate(DateUtils.getNow());
                subcontractRelInfoPO.setModifiedDate(DateUtils.getNow());
                subcontractRels.add(subcontractRelInfoPO);
            }
        }

    }

    /**
     *
     * @param isAddPPTL
     * @param labId
     * @return
     */
    private List<TestLineStandarBase> getTestLineStandardBases(List<SaveTestLineReq> saveTestLineReqList, Boolean isAddPPTL,int labId,boolean assignAllLabSection) {
        List<Integer> aids = saveTestLineReqList.stream().map(SaveTestLineReq::getAid).collect(Collectors.toList());
        List<Long> artifactBaseIds = saveTestLineReqList.stream().map(testlineReq->testlineReq.getArtifactBaseId()).collect(Collectors.toList());
        List<Integer> citationBaseIds = saveTestLineReqList.stream().map(testlineReq->testlineReq.getCitationBaseId()).filter(baseid->{return Func.isNotEmpty(baseid);}).distinct().collect(Collectors.toList());
        List<SaveTestLineCitationReq> saveTestLineCitationReqList=saveTestLineReqList.stream().filter(e->e.getSaveTestLineCitationReq()!=null).map(e->e.getSaveTestLineCitationReq()).collect(Collectors.toList());
        QueryTestLineReq queryTestLineReq=new QueryTestLineReq();
        queryTestLineReq.setArtifactBaseIds(artifactBaseIds);
        queryTestLineReq.setLabId(labId);
        queryTestLineReq.setCitationBaseIds(citationBaseIds);
        queryTestLineReq.setSaveTestLineCitationReqs(saveTestLineCitationReqList);
        List<TestLineStandarBase> testLineStandarBases =
                isAddPPTL ? testLineBaseMapper.getStandardPpTestLineList(queryTestLineReq):
                testLineBaseMapper.getStandardTestLineListWithoutLabSection(queryTestLineReq);
        logger.info("getTestLineStandardBases,req:{}",JSON.toJSONString(saveTestLineReqList));
        logger.info("getTestLineStandardBases,result:{}",JSON.toJSONString(testLineStandarBases));
        if(Func.isNotEmpty(testLineStandarBases) && isAddPPTL){
            for(SaveTestLineReq saveTestLineReq : saveTestLineReqList){
                //遍历req 找出aid&testLineVersionId 匹配req与bases结果 对比ppversion 如果不一致 那req ppVersionId 请求 localTrims接口获取ppNo 重新set testLineStandarBases里面
                TestLineStandarBase testLineStandarBase = testLineStandarBases.stream()
                        .filter(e -> Func.isNotEmpty(saveTestLineReq.getAid()) && Func.equalsSafe(e.getArtifactId(), saveTestLineReq.getAid().longValue()))
                        .filter(e -> Func.equalsSafe(e.getTestLineVersionId(), saveTestLineReq.getTestLineVersionId()))
                        .filter(e -> Func.equalsSafe(e.getBasePpVersionId(),saveTestLineReq.getPpVersionId())).findAny().orElse(null);
                if (Func.isEmpty(testLineStandarBase)) {
                    TestLineStandarBase newTestLineStandarBase = testLineStandarBases.stream()
                            .filter(e -> Func.isNotEmpty(saveTestLineReq.getAid()) && Func.equalsSafe(e.getArtifactId(), saveTestLineReq.getAid().longValue()))
                            .filter(e -> Func.equalsSafe(e.getTestLineVersionId(), saveTestLineReq.getTestLineVersionId())).findAny().orElse(null);
                    SearchPpInfoReq searchPpInfoReq = new SearchPpInfoReq();
                    searchPpInfoReq.setPpVersionIds(Lists.newArrayList(saveTestLineReq.getPpVersionId()));
                    searchPpInfoReq.setStatues(Lists.newArrayList(TrimsPpStatus.Active.getStatus(),TrimsPpStatus.PhaseOut.getStatus()));
                    List<SearchPpInfoRsp> searchPpInfoRspList = ppClient.getPpList(searchPpInfoReq);
                    logger.info("getTestLineStandardBases,searchPpInfoRspList:{}",JSON.toJSONString(searchPpInfoRspList));
                    if(Func.isNotEmpty(searchPpInfoRspList) && Func.isNotEmpty(newTestLineStandarBase)){
                        TestLineStandarBase addTestLineStandarBase = new TestLineStandarBase();
                        Func.copy(newTestLineStandarBase, addTestLineStandarBase);
                        SearchPpInfoRsp searchPpInfoRsp = searchPpInfoRspList.get(0);
                        addTestLineStandarBase.setPpBaseId(searchPpInfoRsp.getPpBaseId());
                        saveTestLineReq.setPpBaseId(searchPpInfoRsp.getPpBaseId());
                        addTestLineStandarBase.setPpVersionId(searchPpInfoRsp.getPpVersionId());
                        addTestLineStandarBase.setTestLineType(saveTestLineReq.getTestLineType());
                        testLineStandarBases.add(addTestLineStandarBase);
                    }
                } else {
                    saveTestLineReq.setPpBaseId(testLineStandarBase.getPpBaseId());
                    testLineStandarBase.setTestLineType(saveTestLineReq.getTestLineType());
                }
            }
        }
        logger.info("getTestLineStandardBases,AfterAddresult:{}",JSON.toJSONString(testLineStandarBases));
        // labsection单独处理
        if (CollectionUtils.isNotEmpty(testLineStandarBases)){
            List<Integer> testLineVersionIds = testLineStandarBases.stream().map(tl -> tl.getTestLineVersionId()).distinct().collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(artifactBaseIds)) {
//                List<TestLineLabSectionRelWithLabSectionInfo> testLineLabSectionRelInfoPOS = testLineLabSectionRelInfoExtMapper.listTestLineLabSectionRelInfos(testLineVersionIds, labId);
                // 切换为本地化获取
                List<GetLabSectionBaseInfoRsp> labSectionListByTestLineVersionIds = labSectionClient.getLabSectionListByTestLineVersionIds(aids,testLineVersionIds, labId);
                if(assignAllLabSection){
                    for (SaveTestLineReq saveTestLineReq : saveTestLineReqList) {

                        List<GetLabSectionBaseInfoRsp> testLineLabSections = null;
                        if(Func.isNotEmpty(labSectionListByTestLineVersionIds)){
                            //非PP
                            Integer aid = saveTestLineReq.getAid();
                            if(Func.isEmpty(aid) || NumberUtil.equals(aid,0)){
                                testLineLabSections = labSectionListByTestLineVersionIds.stream().filter(item -> NumberUtil.equals(item.getTestLineVersionId(), saveTestLineReq.getTestLineVersionId())).collect(Collectors.toList());
                            }else{
                                //PP:优先匹配PP，没有PP，匹配TL
                                testLineLabSections = labSectionListByTestLineVersionIds.stream().filter(item -> NumberUtil.equals(aid, item.getAid())).collect(Collectors.toList());
                                if(Func.isEmpty(testLineLabSections)){
                                    testLineLabSections = labSectionListByTestLineVersionIds.stream().filter(item -> NumberUtil.equals(item.getTestLineVersionId(), saveTestLineReq.getTestLineVersionId())).collect(Collectors.toList());
                                }
                            }
                        }
                        if(Func.isNotEmpty(testLineLabSections)){
                            List<LabSectionBaseInfo> saveLabSectionList = new ArrayList<>();
                            for (GetLabSectionBaseInfoRsp testLineLabSection : testLineLabSections) {
                                LabSectionBaseInfo labSectionBaseInfo = new LabSectionBaseInfo();
                                labSectionBaseInfo.setLabSectionBaseId(testLineLabSection.getLabSectionBaseId());
                                labSectionBaseInfo.setLabSectionId(Func.toLong(testLineLabSection.getLabSectionId()));
                                saveLabSectionList.add(labSectionBaseInfo);
                            }
                            saveTestLineReq.setSaveLabSectionList(saveLabSectionList);

                        }
                    }
                }else{
                    for (TestLineStandarBase testLineStandarBase : testLineStandarBases) {
                        List<GetLabSectionBaseInfoRsp> testLineLabSections = null;
                        if(Func.isNotEmpty(labSectionListByTestLineVersionIds)){
                            //非PP
                            Long aid =  testLineStandarBase.getArtifactId();
                            if(Func.isEmpty(aid) || NumberUtil.equals(aid,0L)){
                                testLineLabSections = labSectionListByTestLineVersionIds.stream().filter(item -> NumberUtil.equals(item.getTestLineVersionId(), testLineStandarBase.getTestLineVersionId())).collect(Collectors.toList());
                            }else{
                                //PP:优先匹配PP，没有PP，匹配TL
                                testLineLabSections = labSectionListByTestLineVersionIds.stream().filter(item -> NumberUtil.equals(aid, Func.toLong(item.getAid()))).collect(Collectors.toList());
                                if(Func.isEmpty(testLineLabSections)){
                                    testLineLabSections = labSectionListByTestLineVersionIds.stream().filter(item -> NumberUtil.equals(item.getTestLineVersionId(), testLineStandarBase.getTestLineVersionId())).collect(Collectors.toList());
                                }
                            }
                        }
                        if(Func.isNotEmpty(testLineLabSections)){
                            testLineLabSections.stream().findFirst()
                                .ifPresent(c -> {
                                    testLineStandarBase.setLabSectionBaseId(c.getLabSectionBaseId());
                                    testLineStandarBase.setLabSectionId(Func.toLong(c.getLabSectionId()));
                                });
                        }
                    }
                }
            }
        }

        return testLineStandarBases;
    }

    private List<OrderLanguageRelInfoPO> getOrderLanguageRelInfoPOS(String orderId) {
        //search orderLanguageRelInfoMapper
        OrderLanguageRelInfoExample objOrderLanguageRelInfoExample=new OrderLanguageRelInfoExample();
        objOrderLanguageRelInfoExample.createCriteria().andOrderIdEqualTo(orderId);
        List<OrderLanguageRelInfoPO> orderLanguageRelInfoPOS_existed=orderLanguageRelInfoMapper.selectByExample(objOrderLanguageRelInfoExample);
        if(CollectionUtils.isEmpty(orderLanguageRelInfoPOS_existed)) orderLanguageRelInfoPOS_existed=new ArrayList<OrderLanguageRelInfoPO>();
        return orderLanguageRelInfoPOS_existed;
    }

    private OrderLanguageRelInfoPO buildOrderLanguageRelIInfo(Long objectBaseId, int languageId,Long langBaseId,String orderId,int langType) {
        OrderLanguageRelInfoPO objOrderLanguageRelInfoPO=new OrderLanguageRelInfoPO();
        objOrderLanguageRelInfoPO.setObjectBaseId(objectBaseId);
        objOrderLanguageRelInfoPO.setLangBaseId(langBaseId);
        objOrderLanguageRelInfoPO.setLangType(langType);
        objOrderLanguageRelInfoPO.setLanguageId(languageId);
        objOrderLanguageRelInfoPO.setOrderId(orderId);
        objOrderLanguageRelInfoPO.setLangStatus(1);
        objOrderLanguageRelInfoPO.setCreatedDate(DateUtils.getNow());
        objOrderLanguageRelInfoPO.setModifiedDate(DateUtils.getNow());
        return objOrderLanguageRelInfoPO;
    }

    /**
     * 获取已保存TestLineInstance 并返回Map结构
     * @param oldTestLines
     * @param testLineStandards
     * @return
     */
    private Map<String, TestLineInstancePO> getSavedTestLineInstanceList(List<TestLineInstancePO> oldTestLines, List<TestLineStandarBase> testLineStandards,List<PPTestLineRelationshipInfoPO> ppTestLineRelationshipInfoPOS,boolean isMergeTl){
		/* 查询已存在OrderTestLine */
        Map<String, TestLineInstancePO> testLineMaps = Maps.newHashMap();
        for (TestLineInstancePO oldTestLine : oldTestLines) {
            if (TestLineStatus.check(oldTestLine.getTestLineStatus(), TestLineStatus.Cancelled)) {
                continue;
            }
            TestLineStandarBase testLine = testLineStandards.stream().filter(e->NumberUtil.equals(oldTestLine.getTestLineBaseId(), e.getTestLineBaseId())).findFirst().orElse(null);
            if(testLine == null){
                continue;
            }
            List<CitationBaseStandarInfo> citations = testLine.getCitationBaseStandarInfos();
            if(citations == null || citations.isEmpty()){
                continue;
            }
            CitationBaseStandarInfo citation = citations.stream().filter(c ->NumberUtil.equals(oldTestLine.getCitationBaseId(), c.getId())).findFirst().orElse(null);
            if(citation == null){
                continue;
            }
            PPTestLineRelationshipInfoPO ppTestLineRelationshipInfoPO = null;
            if (Func.isNotEmpty(ppTestLineRelationshipInfoPOS)){
                ppTestLineRelationshipInfoPO = ppTestLineRelationshipInfoPOS.stream().filter(e -> Func.equals(oldTestLine.getID(),e.getTestLineInstanceID())).findFirst().orElse(null);
            }
            GetSaveTestLineKeyReq getSaveTestLineKeyReq =  new GetSaveTestLineKeyReq();
            getSaveTestLineKeyReq.setTestLineVersionId(oldTestLine.getTestLineVersionID());
            getSaveTestLineKeyReq.setCitationVersionId(oldTestLine.getCitationVersionId());
            getSaveTestLineKeyReq.setCitationSectionId(oldTestLine.getCitationSectionId());
            getSaveTestLineKeyReq.setCitationSectionId(oldTestLine.getCitationSectionId());
            getSaveTestLineKeyReq.setCitationType(oldTestLine.getCitationTypeID());
            getSaveTestLineKeyReq.setAid(Func.isNotEmpty(ppTestLineRelationshipInfoPO)?ppTestLineRelationshipInfoPO.getPpArtifactRelId():0);
            getSaveTestLineKeyReq.setTestLineType(oldTestLine.getTestLineType());
            getSaveTestLineKeyReq.setMergePpTl(isMergeTl);
            getSaveTestLineKeyReq.setCheckMergeTl(true);
            String key = testLineCmdService.getSaveTestLineKey(getSaveTestLineKeyReq);
//            if(testLineMaps.containsKey(key)){
//                TestLineInstancePO testLineInstancePO = testLineMaps.get(key);
//                String remark = testLineInstancePO.getOrdertestLineRemark() + "|" +  oldTestLine.getOrdertestLineRemark();
//                oldTestLine.setOrdertestLineRemark(remark.substring(0,3999));
//            }
            testLineMaps.put(key,oldTestLine);
        }
        return testLineMaps;
    }

    private PPTestLineRelationshipInfoPO buildPpTestLineRelationshipInfoPO(String orderId,TestLineInstancePO testLineInstancePO,TestLineStandarBase objTestLineStandarBase, Long rootPpBaseId,
                                                                           Map<Integer, PPSectionBaseInfoPO> rootSectionMap,Map<Integer, QueryPPSection> subPPRootSectionMap) {
        PPTestLineRelationshipInfoPO ppTestLineRelationship = new PPTestLineRelationshipInfoPO();
        ppTestLineRelationship.setGeneralOrderInstanceID(orderId);
        ppTestLineRelationship.setID(UUID.randomUUID().toString());

        if (NumberUtil.equals(rootPpBaseId , objTestLineStandarBase.getPpBaseId())) {
            PPSectionBaseInfoPO rootPpSection = rootSectionMap.get(objTestLineStandarBase.getSectionLevel());
            if (rootPpSection != null) {
                ppTestLineRelationship.setSectionID(rootPpSection.getSectionId());
                ppTestLineRelationship.setSectionLevel(String.valueOf(rootPpSection.getSectionLevel()));
                ppTestLineRelationship.setSectionName(rootPpSection.getSectionText());
            }
        }else{
            QueryPPSection rootPpSection = subPPRootSectionMap.get(objTestLineStandarBase.getPpVersionId());
            if (rootPpSection != null) {
                ppTestLineRelationship.setSectionID(rootPpSection.getRootPpSectionId());
                ppTestLineRelationship.setSectionLevel(String.valueOf(rootPpSection.getRootPpSectionLevel()));
                ppTestLineRelationship.setSectionName(rootPpSection.getRootPpSectionName());
            }
        }

        ppTestLineRelationship.setTestLineInstanceID(testLineInstancePO.getID());
        ppTestLineRelationship.setPpArtifactRelId(NumberUtil.toLong(objTestLineStandarBase.getPpArtifactRelId()));
        ppTestLineRelationship.setPpBaseId(NumberUtil.toLong(objTestLineStandarBase.getPpBaseId()));
        ppTestLineRelationship.setCreatedBy(SecurityContextHolder.getUserInfoFillSystem().getRegionAccount());
        ppTestLineRelationship.setCreatedDate(DateUtils.getNow());

        // ppTestLineRelationship.setPPNotes(citationBaseStandarInfo.getPpNotes());
        ppTestLineRelationship.setAid(NumberUtil.toLong(objTestLineStandarBase.getArtifactId()));

        //objTestLineStandarBase.getPpBaseId()的最顶层PP，即用户输入进行搜索的PP
        ppTestLineRelationship.setRootPpBaseId(NumberUtil.toLong(rootPpBaseId));
        //这里赋默认0，当by pp添加时，会重新赋值
        ppTestLineRelationship.setSubPpRelSeq(0);
        return ppTestLineRelationship;
    }

    private void generateTestLineSeq(String orderId, List<PPTestLineRelationshipInfoPO> ppTestLineRelationshipInfoPOListSaved) {
        PPTestLineRelationshipInfoExample example = new PPTestLineRelationshipInfoExample();
        example.createCriteria().andGeneralOrderInstanceIDEqualTo(orderId);
        List<PPTestLineRelationshipInfoPO> allPpTestLineRelationshipInfoPOS = ppTestLineRelationshipInfoMapper.selectByExample(example);
        for (int i = 0; i < ppTestLineRelationshipInfoPOListSaved.size(); i++) {
            PPTestLineRelationshipInfoPO ppTestLineRelationshipInfoPO = ppTestLineRelationshipInfoPOListSaved.get(i);
            Long ppBaseId = ppTestLineRelationshipInfoPO.getPpBaseId();
            //根据PP分组
            Map<Long, List<PPTestLineRelationshipInfoPO>> ppBaseTestLineMap = allPpTestLineRelationshipInfoPOS.stream().collect(Collectors.groupingBy(PPTestLineRelationshipInfoPO::getPpBaseId));
            List<PPTestLineRelationshipInfoPO> ppTestLineRelationshipInfoPOS = ppBaseTestLineMap.get(ppBaseId);
            if (Func.isEmpty(ppTestLineRelationshipInfoPOS)) {
                ppTestLineRelationshipInfoPOS = new ArrayList<>();
            }
            String ppSeq;
            String prefixSeq;
            if (NumberUtil.equals(0L, ppBaseId, true)) {
                prefixSeq = "90";
                ppSeq = String.format("%03d", 0);
            } else {
                prefixSeq = "10";
                //是否已有该PPBaseId对应的数据
                if (ppTestLineRelationshipInfoPOS.size() > 0) {
                    String oldSeq = ppTestLineRelationshipInfoPOS.get(0).getSeq().toString();
                    ppSeq = oldSeq.substring(2, 5);
                } else {
                    if (ppBaseTestLineMap.containsKey(0L)) {
                        ppSeq = String.format("%03d", ppBaseTestLineMap.size());
                    } else {
                        ppSeq = String.format("%03d", ppBaseTestLineMap.size() + 1);
                    }
                }
            }
            String testLineSeq = String.format("%04d", ppTestLineRelationshipInfoPOS.size() + 1 + i);
            String wholeSeq = prefixSeq + ppSeq + testLineSeq;
            ppTestLineRelationshipInfoPO.setSeq(Long.parseLong(wholeSeq));
        }
    }


    /**
     * 根据StandardBase与CitationBase构造TestLine
     * @param reqObject
     * @param testLineBase
     * @param citation
     * @param 
     * @return
     */
    private TestLineInstancePO buildTestLineInstancePO(SaveTestLineListReq reqObject, TestLineStandarBase testLineBase,
                                                       CitationBaseStandarInfo citation, Boolean isWorkingInstructionTl ,SaveTestLineReq saveTestLineReq) {
        TestLineInstancePO testLine = new TestLineInstancePO();
        testLine.setID(UUID.randomUUID().toString());
        testLine.setGeneralOrderInstanceID(reqObject.getOrderId());
        testLine.setTestLineID(testLineBase.getTestLineId());
        testLine.setTestLineVersionID(testLineBase.getTestLineVersionId());
        testLine.setTestLineStatus(TestLineStatus.Typing.getStatus());
        testLine.setConditionStatus(ConditionStatus.UnConfirmed.getStatus());
        testLine.setProductLineAbbr(testLineBase.getProductLineAbbr());
        List<LabSectionBaseInfo> labSectionBaseInfoList = new ArrayList<>();
        if(Func.isNotEmpty(saveTestLineReq.getSaveLabSectionList())){
            labSectionBaseInfoList = saveTestLineReq.getSaveLabSectionList();
        }else if(Func.isNotEmpty(testLineBase.getLabSectionBaseId())){
            LabSectionBaseInfo labSectionBaseInfo = new LabSectionBaseInfo();
            labSectionBaseInfo.setLabSectionId(testLineBase.getLabSectionId());
            labSectionBaseInfo.setLabSectionBaseId(testLineBase.getLabSectionBaseId());
            labSectionBaseInfoList.add(labSectionBaseInfo);
        }
        testLine.setSaveLabSectionList(labSectionBaseInfoList);
        testLine.setTestLineBaseId(testLineBase.getTestLineBaseId());
        testLine.setCitationBaseId(citation.getId());
        testLine.setCitationId(citation.getCitationId());
        testLine.setClientStandard(saveTestLineReq.getClientStandard());
        //add by vincent 2020年9月20日 添加新字段
        testLine.setCitationVersionId(citation.getCitationVersionId());
        int testLineType = TestLineType.None.getType();
        if(Func.isNotEmpty(saveTestLineReq.getOrderTestLineType())){
            testLineType = saveTestLineReq.getOrderTestLineType();
        }
        if (TestLineType.isPretreatment(citation.getEvaluationAlias())) {
            testLineType = TestLineType.Pretreatment.getType();
        }
        if (ArtifactType.check(testLineBase.getArtifactType(), ArtifactType.PP)){
            testLineType |= TestLineType.CSPP.getType();
        }
        // 设置Claim值
        if (isWorkingInstructionTl) {
            testLineType |= TestLineType.Claim.getType();
        }
        // 设置FCM
/*        if (saveTestLineReq.isReadOnly()) {
            testLineType |= TestLineType.FCM.getType();
        }*/

        // OOB业务维护 testLineType
        if(TestLineType.check(testLineType,TestLineType.OOB_TEST)){
            testLine.setCustomerTestLineName(saveTestLineReq.getCustomerTestLineName());
            testLine.setCustomerTestLineNameCN(saveTestLineReq.getCustomerTestLineNameCN());
        }
        testLine.setTestLineType(testLineType);

        testLine.setActiveIndicator(true);
        testLine.setCreatedBy(SecurityContextHolder.getUserInfoFillSystem().getRegionAccount());
        testLine.setCreatedDate(DateUtils.getNow());
        testLine.setExternalTestlineInstanceId(saveTestLineReq.getExternalTestlineInstanceId());
        testLine.setOrdertestLineRemark(saveTestLineReq.getTestRemark());
        return testLine;
    }

    //设置status 为708的逻辑
    private void setDrFlag(List<TestLineStandarBase> testLineRedisList, List<TestLineInstancePO> testLineInstancePOListSave, List<TestLineInstancePO> testLineInstancePOList_existed) {
        BuParamReq buParamReq = new BuParamReq();
        buParamReq.setProductLineCode(ProductLineContextHolder.getProductLineCode());
        buParamReq.setLabCode(SecurityUtil.getLabCode());
        buParamReq.setGroupCode(Constants.BU_PARAM.TEST_LINE.GROUP);
        buParamReq.setParamCode(Constants.BU_PARAM.TEST_LINE.DocumentReviewMode.CODE);
        BuParamValueRsp buParam = frameWorkClient.getBuParam(buParamReq);
        String documentReviewMode = Constants.BU_PARAM.TEST_LINE.DocumentReviewMode.VALUES.CONCLUSION;
        if(Func.isNotEmpty(buParam)){
            documentReviewMode = buParam.getParamValue();
        }

        // 存放本次添加testLine的 drFlag
        Map<String, Integer> drFlagMap = new HashMap<String, Integer>();
        for (TestLineStandarBase testLine : testLineRedisList) {
            TestLineInstancePO objTestLineInstanceSavePO=testLineInstancePOListSave.stream().filter(testLineInstancePO -> testLineInstancePO.getTestLineBaseId().intValue()== testLine.getTestLineBaseId().intValue()).findFirst().orElse(null);
            if (objTestLineInstanceSavePO == null){
                continue;
            }
            String key = objTestLineInstanceSavePO.getTestLineID() + "_" + objTestLineInstanceSavePO.getStandardVersionID() + "_"
                    + objTestLineInstanceSavePO.getStandardSectionID();
            if (drFlagMap.containsKey(key)) {
                if (StringUtils.equalsIgnoreCase(
                        TlExecutionClassification.getMessage(testLine.getTlExecutionClassificationCode()==null?
                                TlExecutionClassification.NeedToTest.getStatus():testLine.getTlExecutionClassificationCode()),
                        TlExecutionClassification.NeedToTest.getMessage())) {
                    drFlagMap.put(key, testLine.getTlExecutionClassificationCode()==null
                            ?TlExecutionClassification.NeedToTest.getStatus():testLine.getTlExecutionClassificationCode());
                }
            } else {
                drFlagMap.put(key, testLine.getTlExecutionClassificationCode()==null?
                        TlExecutionClassification.NeedToTest.getStatus():testLine.getTlExecutionClassificationCode());
            }
        }
        if (CollectionUtils.isNotEmpty(testLineInstancePOListSave)) {
            for (TestLineInstancePO testLineInstancePO : testLineInstancePOListSave) {
                testLineInstancePO.setDocumentReviewFlag(DocumentReviewFlag.None.getStatus());
                String key = testLineInstancePO.getTestLineID() + "_" + testLineInstancePO.getStandardID()+ "_"
                        + testLineInstancePO.getStandardSectionID();
                if(drFlagMap.containsKey(key)){
                    if (!testLineInstancePOList_existed.stream().anyMatch(testLineInstancePO1 -> testLineInstancePO1.getID().equals(testLineInstancePO.getID()))) {// insert
                        if (StringUtils.equalsIgnoreCase(drFlagMap.get(key).toString(), TlExecutionClassification.InformationReviewOnly.getStatus().toString())) {
                            if(StringUtils.equalsIgnoreCase(documentReviewMode,Constants.BU_PARAM.TEST_LINE.DocumentReviewMode.VALUES.CONCLUSION)){
                                testLineInstancePO.setDocumentReviewFlag(DocumentReviewFlag.Conclusion.getStatus());
                                testLineInstancePO.setTestLineStatus(TestLineStatus.Completed.getStatus());
                            }else{
                                testLineInstancePO.setDocumentReviewFlag(DocumentReviewFlag.Testing_Conclusion.getStatus());
                                testLineInstancePO.setTestLineStatus(TestLineStatus.Typing.getStatus());
                            }
                            testLineInstancePO.setSaveLabSectionList(null);
                        }
                    }else{
                        //708是dr类型
                        if (StringUtils.equalsIgnoreCase(testLineInstancePO.getTestLineStatus().toString(), "708")) {
                            if (StringUtils.equalsIgnoreCase(drFlagMap.get(key).toString(),
                                    TlExecutionClassification.NeedToTest.getStatus().toString())) {
                                testLineInstancePO.setTestLineStatus(TestLineStatus.Typing.getStatus());
                            }
                        }
                    }

                }
            }

        }
    }

    /**
     *
     * @param testLineInstanceId
     * @return
     */
    public TestLineInstancePO getTestLineInstanceById(String testLineInstanceId){
        return testLineMapper.getTestLineInstanceById(testLineInstanceId);
    }

    /**
     *
     * @param jobNo
     * @return
     */
    public List<JobTestLineDto> getTestLineInstanceByJobNo(String jobNo){
        return testLineMapper.getTestLineInstanceByJobNo(jobNo);
    }

    /**
     *
     * @param req
     * @return
     */
    public CustomResult uploadTestLineReport(TestLineReportListReq req){
        logger.info("TestLineInstanceService.uploadTestLineReport Start(OrderNo_{}), Req: {}", req.getOrderNo(), req);
        CustomResult result = new CustomResult();
        if (StringUtils.isEmpty(req.getOrderNo())){
            result.setMsg("请求的OrderNo不能为空.");
            return result;
        }
        if (StringUtils.isEmpty(req.getJobNo())){
            result.setMsg("请求的JobNo不能为空.");
            return result;
        }
        List<TestLineReportReq> testLineReports = req.getTestLineReports();
        if (testLineReports == null || testLineReports.isEmpty()){
            result.setMsg("请求的testLineReports 列表不能为空.");
            return result;
        }
        GeneralOrderInstanceInfoPO oldOrder = orderMapper.getOrderInfo(req.getOrderNo());
        if (oldOrder == null){
            result.setMsg("对应找到对应的OrderNo信息.");
            return result;
        }
        HashMap<Integer, String> testLineReportMaps = Maps.newHashMap();
        HashSet<Integer> testLineIds = Sets.newHashSet();
        testLineReports.forEach(testLine->{
            List<Integer> tlIds = testLine.getTestLineIds();
            if (tlIds == null || tlIds.isEmpty()){
                return;
            }
            testLineIds.addAll(tlIds);
            for (Integer testLineId: tlIds) {
                testLineReportMaps.put(testLineId, testLine.getReportUrl());
            }
        });
        TestLineReportParamReq reqParams = new TestLineReportParamReq();
        reqParams.setOrderNo(req.getOrderNo());
        reqParams.setTestLineIds(Lists.newArrayList(testLineIds));

        // 检查当前OrderNo对应的TestLine
        List<TestLineReportPo> tlReports = testLineMapper.getTestLineByTestLineIds(reqParams);
        if (tlReports == null || tlReports.isEmpty()){
            result.setMsg("未找到对应有效的 TestLine信息.");
            return result;
        }

        // 根据OrderNo获取BuId、LocationID
        OrderInfoDto order = orderClient.getOrderInfoByOrderNo(req.getOrderNo());
        if (order == null){
            result.setMsg(String.format("未找到对应的OrderNo(%s)信息.", req.getOrderNo()));
            return result;
        }

        List<UpdateTestlineInstanceFileIdReq> testLineFiles = Lists.newArrayList();
        HashMap<String, UploadFileInfo> groupReportMaps = Maps.newHashMap();
        UpdateTestlineInstanceFileIdReq testLineFile = null;
        HashSet<Integer> success = Sets.newHashSet();
        HashSet<Integer> failures = Sets.newHashSet();

        for (TestLineReportPo tlReport: tlReports){
            if (tlReport.getActive() != null && tlReport.getActive() == 1){
                failures.add(tlReport.getTestLineId());
                continue;
            }
            if (!TestLineStatus.check(tlReport.getTestLineStatus(), TestLineStatus.Typing)){
                failures.add(tlReport.getTestLineId());
                logger.info("当前OrderNo({})的TestLine({}) Status 为：{}.", req.getOrderNo(), tlReport.getId(), tlReport.getTestLineStatus());
                continue;
            }
            if (testLineReportMaps.containsKey(tlReport.getTestLineId())){
                String reportUrl = testLineReportMaps.get(tlReport.getTestLineId());
                if (!groupReportMaps.containsKey(reportUrl)){
                    UploadFileInfo file = new UploadFileInfo();
                    file.setBuId(order.getBUID());
                    file.setLocationID(order.getLocationID());
                    file.setOrderId(tlReport.getOrderId());
                    file.setId(tlReport.getId());
                    file.setTestLineId(tlReport.getTestLineId());
                    file.setReportUrl(reportUrl);
                    groupReportMaps.put(reportUrl, file);
                }
            }
            success.add(tlReport.getTestLineId());
            testLineFile = new UpdateTestlineInstanceFileIdReq();
            testLineFile.setTestLineInstanceId(tlReport.getId());
            testLineFile.setTestLineId(tlReport.getTestLineId());
            testLineFile.setTestLineStatus(TestLineStatus.Completed.getStatus());
            if (TestLineStatus.check(tlReport.getTestLineStatus(), TestLineStatus.SubContracted)){
                testLineFile.setTestLineStatus(TestLineStatus.SubContracted.getStatus());
            }
            testLineFiles.add(testLineFile);
        }
        TestLineReportRsp testLineRsp = new TestLineReportRsp();
        if (groupReportMaps.isEmpty()){
            testLineRsp.setFailures(failures);
            result.setData(testLineRsp);
            result.setMsg("未找到OrderNo下有效testLine(只允许无样式回传报告).");
            return result;
        }
        ConcurrentHashMap<String, String> reportFiles = this.parallelUploadFile(groupReportMaps);
        for (UpdateTestlineInstanceFileIdReq file: testLineFiles) {
            if (!testLineReportMaps.containsKey(file.getTestLineId())){
                failures.add(file.getTestLineId());
                success.remove(file.getTestLineId());
                continue;
            }
            String reportUrl = testLineReportMaps.get(file.getTestLineId());
            if (!reportFiles.containsKey(reportUrl)){
                failures.add(file.getTestLineId());
                success.remove(file.getTestLineId());
                continue;
            }
            file.setFileID(reportFiles.get(reportUrl));
        }
        testLineRsp.setSuccess(success);
        testLineRsp.setFailures(failures);
        result.setData(testLineRsp);
        if (reportFiles.isEmpty()){
            result.setMsg("附件上传失败.");
            return result;
        }

        result.setSuccess(transactionTemplate.execute((tranStatus)->{
            boolean isSuccess = testLineMapper.batchUpdateTestlineInstanceFileId(testLineFiles) > 0;
            if (!isSuccess){
                // 回滚事务
                tranStatus.setRollbackOnly();
                return false;
            }
            this.checkPreOrderStatus(oldOrder);

            return true;
        }));
        //result.setSuccess(testLineMapper.batchUpdateTestlineInstanceFileId(testLineFiles) > 0);

        logger.info("TestLineInstanceService.uploadTestLineReport End(OrderNo_{}), Req: {}, Rsp: {}", req.getOrderNo(), req, result);
        return result;
    }

    /**
     *
     * @param groupReportMaps
     * @return
     */
    private ConcurrentHashMap<String, String> parallelUploadFile(HashMap<String, UploadFileInfo> groupReportMaps){
        ConcurrentHashMap<String, String> reportFiles = new ConcurrentHashMap();
        final CountDownLatch latch = new CountDownLatch(groupReportMaps.size());
        Iterator<Map.Entry<String, UploadFileInfo>> entrys = groupReportMaps.entrySet().iterator();
        while (entrys.hasNext()){
            Map.Entry<String, UploadFileInfo> entry = entrys.next();
            taskExecutor.submitListenable(()-> fileClient.uploadFileWithoutGeneric(reportFiles, entry.getValue()))
                    .addCallback(new ListenableFutureCallback<CustomResult>() {
                        @Override
                        public void onSuccess(CustomResult limits) {
                            latch.countDown();
                        }
                        @Override
                        public void onFailure(Throwable t) {
                            // TODO Auto-generated method stub
                            latch.countDown();
                        }
                    });
        }
        try {
            // 设定超时时间单位：毫秒
            latch.await(10000, TimeUnit.MILLISECONDS);
        } catch (InterruptedException ie) {

        }
        return reportFiles;
    }

    public List<JobTestLineForEmDto> getTestLineInstanceByJobNoForEm(List<String> jobNos){
        return testLineMapper.getTestLineInstanceByJobNoForEm(jobNos);
    }

    /**
     *
     * @param testLineBreakDownReq
     * @return
     */
    public BaseResponse<TestLineBreakDownRsp> getTestLineBreakDownInfoList(TestLineBreakDownReq testLineBreakDownReq){
        String orderNo = testLineBreakDownReq.getOrderNo();
        boolean openCspp = testLineBreakDownReq.isOpenCspp();
        BaseResponse response = new BaseResponse();
        TestLineBreakDownRsp rspResult = new TestLineBreakDownRsp();
        List<OriginalSampleBreakDownInfo> sampleBreakDownInfoList = testSampleMapper.getOriginalSampleBreakDownInfoList(orderNo);
        List<OriginalSampleBreakDownInfo> samples = Lists.newArrayList();
        if(Func.isNotEmpty(sampleBreakDownInfoList)){
            //查询DFF表 找出所有语言的 Sample Description
            Map<String,List<OriginalSampleBreakDownInfo>> sampleMap = sampleBreakDownInfoList.stream().collect(Collectors.groupingBy(OriginalSampleBreakDownInfo::getSampleNo));
            for(Map.Entry<String,List<OriginalSampleBreakDownInfo>> entry : sampleMap.entrySet()){
                OriginalSampleBreakDownInfo newSample = new OriginalSampleBreakDownInfo();
                List<OriginalSampleBreakDownInfo> sampleBreakDownInfos = entry.getValue();
                Func.copy(sampleBreakDownInfos.get(0), newSample);
                List<SampleBreakDownLanguage> languageList = Lists.newArrayList();
                for(OriginalSampleBreakDownInfo originalSampleBreakDownInfo : sampleBreakDownInfos){
                    SampleBreakDownLanguage language = new SampleBreakDownLanguage();
                    language.setLanguageId(originalSampleBreakDownInfo.getLanguageId());
                    language.setSampleDescription(originalSampleBreakDownInfo.getSampleDescription());
                    language.setProductDescription(originalSampleBreakDownInfo.getProductDescription());
                    languageList.add(language);
                }
                newSample.setLanguageList(languageList);
                samples.add(newSample);
            }
        }
        rspResult.setSamples(samples);
        List<TestLineBreakDownInfo> testLines = testLineMapper.getTestLineBreakDownInfoList(orderNo);
        if (testLines == null || testLines.isEmpty()){
            return BaseResponse.newSuccessInstance(rspResult);
        }

        Map<String, TestLineBreakDownInfo> ppTlCitationMap = Maps.newHashMap();
//        List<PpTestLineRelCitationInfo> ppTLRelCitationInfoByOrderId = ppTestLineRelMapper.getPPTestLineRelCitationInfoByOrderId(allOrderId);
        List<GetCitationBaseInfoRsp> trimsCitationBaseInfoList = new ArrayList<>();
        if(Func.isNotEmpty(testLines)){
            Set<Long> citationBaseIdSet = testLines.stream().map(TestLineBreakDownInfo::getCitationBaseId).distinct().filter(Func::isNotEmpty).collect(Collectors.toSet());
            if(Func.isNotEmpty(citationBaseIdSet)){
                trimsCitationBaseInfoList = citationClient.getCitationBaseInfo(citationBaseIdSet, null, LanguageType.English);
            }
        }
        Boolean isMergeTl = this.getMergeTestLineBuParam();
        List<GetCitationBaseInfoRsp> finalTrimsCitationBaseInfoList = trimsCitationBaseInfoList;
        testLines.forEach(ppTl -> {
            if(Func.isNotEmpty(finalTrimsCitationBaseInfoList)){
                GetCitationBaseInfoRsp citationBaseInfoRsp = finalTrimsCitationBaseInfoList.stream().filter(i -> Func.equals(Func.toStr(ppTl.getCitationBaseId()), Func.toStr(i.getCitationBaseId()))).findFirst().orElse(null);
                if(Func.isNotEmpty(citationBaseInfoRsp)){
                    GetSaveTestLineKeyReq getSaveTestLineKeyReq =  new GetSaveTestLineKeyReq();
                    getSaveTestLineKeyReq.setTestLineVersionId(ppTl.getTestLineVersionId());
                    getSaveTestLineKeyReq.setCitationVersionId(ppTl.getTestStandardVersionId());
                    getSaveTestLineKeyReq.setCitationSectionId(citationBaseInfoRsp.getCitationSectionId());
                    getSaveTestLineKeyReq.setCitationType(citationBaseInfoRsp.getCitationType());
                    getSaveTestLineKeyReq.setAid(ppTl.getPpArtifactRelId());
                    getSaveTestLineKeyReq.setTestLineType(ppTl.getTestLineType());
                    getSaveTestLineKeyReq.setMergePpTl(isMergeTl);
                    String key = testLineCmdService.getSaveTestLineKey(getSaveTestLineKeyReq);
                    if (ppTlCitationMap.containsKey(key)) {
                        return;
                    }
                    ppTlCitationMap.put(key, ppTl);
                }
            }
        });

        //过滤未展开过的CSPP
        if(openCspp){
            List<SaveTestLineReq> addOpenCsppTestLineList = new ArrayList<>();
            List<TestLineBreakDownInfo> csPPTestLineList = testLines.stream().filter(
                    e -> !TestLineStatus.check(e.getTestLineStatus(),TestLineStatus.Cancelled) && Func.isNotEmpty(e.getTestLineType())
                            && TestLineType.check(e.getTestLineType(),TestLineType.CSPP) && !TestLineType.check(e.getTestLineType(),TestLineType.CSPP_OPEN)).collect(Collectors.toList());
            if(Func.isNotEmpty(csPPTestLineList)){
                GeneralOrderInstanceInfoPO order = orderMapper.getOrderInfo(orderNo);
                for (TestLineBreakDownInfo csPpTestLine : csPPTestLineList) {
                    //获取csPP关联的testLine列表
                    QueryPpTestLineReq queryPpTestLineReq = new QueryPpTestLineReq();
                    queryPpTestLineReq.setCallerBU(ProductLineContextHolder.getProductLineCode());
                    queryPpTestLineReq.setLabId(order.getOrderLaboratoryID());
                    queryPpTestLineReq.setPpVersionId(csPpTestLine.getTestLineVersionId());
                    queryPpTestLineReq.setExcludeSubPpInfo(true);
                    com.sgs.trimslocal.facade.model.common.BaseResponse<PageInfo<QueryPpTestLineRsp>> ppTestLineListPageRes = testLineFacade.getPpTestLineList(queryPpTestLineReq);
                    if(Func.isNotEmpty(ppTestLineListPageRes) && Func.isNotEmpty(ppTestLineListPageRes.getData())){
                        List<QueryPpTestLineRsp> ppTestLineRspList = ppTestLineListPageRes.getData().getList();
                        if(Func.isNotEmpty(ppTestLineRspList)){
                            // 排除已经添加过的
                            for (QueryPpTestLineRsp queryPpTestLineRsp : ppTestLineRspList) {
                                Boolean isAddPPTL = (NumberUtil.toLong(queryPpTestLineRsp.getPpVersionId()) > 0 || Func.isNotEmpty(queryPpTestLineRsp.getAid()))? true :false;
                                // testLineStandards 是指定 CitationBaseId搜索的，理论上citationBaseStandarInfos 中只有一条CitationBaseid
                                if(Func.isNotEmpty(queryPpTestLineRsp.getCitationBaseId())) {
                                    Integer citationSectionId = queryPpTestLineRsp.getCitationSectionId();
                                    Integer citationType = queryPpTestLineRsp.getCitationType();
                                    GetSaveTestLineKeyReq getSaveTestLineKeyReq = new GetSaveTestLineKeyReq();
                                    getSaveTestLineKeyReq.setTestLineVersionId(queryPpTestLineRsp.getTestLineVersionId());
                                    getSaveTestLineKeyReq.setCitationVersionId(queryPpTestLineRsp.getCitationVersionId());
                                    getSaveTestLineKeyReq.setCitationSectionId(citationSectionId);
                                    getSaveTestLineKeyReq.setCitationType(citationType);
                                    getSaveTestLineKeyReq.setAid(!isAddPPTL ? 0 : queryPpTestLineRsp.getArtifactBaseId());
                                    getSaveTestLineKeyReq.setTestLineType(queryPpTestLineRsp.getTestLineType());
                                    getSaveTestLineKeyReq.setMergePpTl(isMergeTl);
                                    String addKey = testLineCmdService.getSaveTestLineKey(getSaveTestLineKeyReq);
                                    if (ppTlCitationMap.containsKey(addKey)) {
                                        continue;
                                    }
                                }
                                SaveTestLineReq  saveTestLineReq = new SaveTestLineReq();
                                saveTestLineReq.setArtifactBaseId(queryPpTestLineRsp.getArtifactBaseId());
                                saveTestLineReq.setArtifactType(queryPpTestLineRsp.getArtifactType());
                                saveTestLineReq.setPpVersionId(queryPpTestLineRsp.getPpVersionId());
                                saveTestLineReq.setRootPpBaseId(queryPpTestLineRsp.getRootPpBaseId());
                                saveTestLineReq.setAid(queryPpTestLineRsp.getAid());
                                saveTestLineReq.setTestLineId(queryPpTestLineRsp.getTestLineId());
                                saveTestLineReq.setStandardId(queryPpTestLineRsp.getCitationId());
                                saveTestLineReq.setStandardName(queryPpTestLineRsp.getCitationName());
                                saveTestLineReq.setCitationVersionId(queryPpTestLineRsp.getCitationVersionId());
                                saveTestLineReq.setTestLineVersionId(queryPpTestLineRsp.getTestLineVersionId());
                                int testLineType = Func.toInt(queryPpTestLineRsp.getTestLineType(), 0);
                                saveTestLineReq.setTestLineType(testLineType | TestLineType.CSPP_OPEN_TL.getType());
                                saveTestLineReq.setOrderTestLineType(saveTestLineReq.getTestLineType());
                                saveTestLineReq.setOrderSeq(0);
                                saveTestLineReq.setCitationBaseId(Func.toInteger(queryPpTestLineRsp.getCitationBaseId()));
                                saveTestLineReq.setQuotationToTestFlag(false);
                                saveTestLineReq.setSaveLabSectionList(Lists.newArrayList());
                                saveTestLineReq.setPpNo(queryPpTestLineRsp.getPpNo());
                                saveTestLineReq.setParentTestLineInstanceId(csPpTestLine.getTestLineInstanceId());
                                SaveTestLineCitationReq objSaveTestLineCitationReq=new SaveTestLineCitationReq();
                                objSaveTestLineCitationReq.setCitationId(queryPpTestLineRsp.getCitationId());
                                objSaveTestLineCitationReq.setCitationVersionId(queryPpTestLineRsp.getCitationVersionId());
                                objSaveTestLineCitationReq.setCitationSectionId(queryPpTestLineRsp.getCitationSectionId());
                                objSaveTestLineCitationReq.setCitationType(queryPpTestLineRsp.getCitationType());
                                objSaveTestLineCitationReq.setCitationBaseId(Func.toInt(queryPpTestLineRsp.getCitationBaseId()));
                                saveTestLineReq.setSaveTestLineCitationReq(objSaveTestLineCitationReq);
                                addOpenCsppTestLineList.add(saveTestLineReq);
                            }
                        }
                    }
                }
                //保存展开的CsPp，更新展开标记
                if(Func.isNotEmpty(addOpenCsppTestLineList)){
                    boolean isSuccess = transactionTemplate.execute((tranStatus)->{
                        try {
                            SaveTestLineListReq saveTestLineListReq = new SaveTestLineListReq();
                            saveTestLineListReq.setOrderId(order.getID());
                            saveTestLineListReq.setAutoAssignSample(false);
                            saveTestLineListReq.setAssignAllLabSection(false);
                            saveTestLineListReq.setTestLines(addOpenCsppTestLineList);
                            CustomResult customResult = this.saveTestLine(saveTestLineListReq);
                            if(Func.isNotEmpty(customResult)){
                                if(customResult.isSuccess()){
                                    //更新CSPP展开标记
                                    if(Func.isNotEmpty(csPPTestLineList)){
                                        for (TestLineBreakDownInfo testLineBreakDownInfo : csPPTestLineList) {
                                            TestLineTypeUpdateReq testLineTypeUpdateReq = getTestLineTypeUpdateReq(testLineBreakDownInfo);
                                            testLineMapper.updateTestLineType(testLineTypeUpdateReq);
                                        }
                                    }
                                }else{
                                    tranStatus.setRollbackOnly();
                                    response.setStatus(com.sgs.framework.core.base.ResponseCode.INTERNAL_SERVER_ERROR.getCode());
                                    response.setMessage("展开CSPP失败:"+customResult.getMsg());
                                    return false;
                                }
                            }
                        } catch (Exception e) {
                            tranStatus.setRollbackOnly();
                            response.setStatus(com.sgs.framework.core.base.ResponseCode.INTERNAL_SERVER_ERROR.getCode());
                            response.setMessage("展开CSPP失败:"+e.getMessage());
                            return false;
                        }
                        return true;
                    });
                    if(!isSuccess){
                        return response;
                    }
                }
            }
            // 鉴于CSPP已展开CSPP重新查询
            if(Func.isNotEmpty(addOpenCsppTestLineList)){
                testLines = testLineMapper.getTestLineBreakDownInfoList(orderNo);
            }
             if(Func.isNotEmpty(csPPTestLineList)){
                //查询TL Rel 获取到所有的PP - TL 父子关系
                GeneralOrderInstanceDTO generalOrderInstanceDTO = ProductLineServiceHolder.getProductLineService(IOrderService.class).getOrderByNo(orderNo);
                if(Func.isNotEmpty(generalOrderInstanceDTO)){
                    //获取tre_pp_test_line_relationship 去除订单下所有的PP TL 关系
                    PPTestLineRelationshipInfoExample example = new PPTestLineRelationshipInfoExample();
                    example.createCriteria().andGeneralOrderInstanceIDEqualTo(generalOrderInstanceDTO.getId());
                    List<PPTestLineRelationshipInfoPO> ppTestLineRelationshipInfoPOList = ppTestLineRelationshipInfoMapper.selectByExampleWithBLOBs(example);
                    Map<String,List<String>> csppRel = this.findCSPPOpen(ppTestLineRelationshipInfoPOList);
                    handleCsPPOpenTlAssignSample(csppRel,ppTestLineRelationshipInfoPOList,generalOrderInstanceDTO);
                    //基于展开的TL 重新处理TL与分包单的关系
                    List<SubContractTestLineMappingPO> saveMapperList = new ArrayList<>();
                    for (TestLineBreakDownInfo csPpTestLine : csPPTestLineList) {
                        //查询CSPP是否包含在分包单中
                        SubContractTestLineMappingExample subContractTestLineMappingExample = new SubContractTestLineMappingExample();
                        subContractTestLineMappingExample.createCriteria().andTestLineInstanceIDEqualTo(csPpTestLine.getTestLineInstanceId());
                        List<SubContractTestLineMappingPO> subContractTestLineMappingPOList = subContractTestLineMappingMapper.selectByExample(subContractTestLineMappingExample);
                        if(Func.isNotEmpty(subContractTestLineMappingPOList)){
                            subContractTestLineMappingPOList.stream().forEach(mapper->{
                                List<String> tlIds = csppRel.get(csPpTestLine.getTestLineInstanceId());
                                if(Func.isNotEmpty(tlIds)){
                                    for(String tlId : tlIds){
                                        SubContractTestLineMappingPO subContractTestLineMappingPO = new SubContractTestLineMappingPO();
                                        subContractTestLineMappingPO.setID(UUID.randomUUID().toString());
                                        subContractTestLineMappingPO.setTestLineInstanceID(tlId);
                                        subContractTestLineMappingPO.setSubContractID(mapper.getSubContractID());
                                        subContractTestLineMappingPO.setCreatedBy("ToBom");
                                        subContractTestLineMappingPO.setCreatedDate(DateUtils.now());
                                        subContractTestLineMappingPO.setModifiedBy("ToBom");
                                        subContractTestLineMappingPO.setModifiedDate(DateUtils.now());
                                        saveMapperList.add(subContractTestLineMappingPO);
                                    }
                                }
                            });
                        }
                    }
                    if(Func.isNotEmpty(saveMapperList)){
                        subContractTestLineMappingMapper.insertBatch(saveMapperList);
                    }
                }
            }
        }
        //TestSeparately取值
        List<Integer> ppVersionIds = testLines.stream().map(TestLineBreakDownInfo::getpPVersionId).distinct().collect(Collectors.toList());
        QueryPpTestLineReq queryPpTestLineReq = new QueryPpTestLineReq();
        List<QueryPpTestLineRsp> ppTestLineRspList = new ArrayList<>();
        if(Func.isNotEmpty(ppVersionIds)){
            for(Integer ppVersionId : ppVersionIds){
                queryPpTestLineReq.setPpVersionId(ppVersionId);
                List<QueryPpTestLineRsp> ppTestLineRsps = ppClient.getPpTestLineList(queryPpTestLineReq);
                logger.info("ppTestLine Rsp:{}",ppTestLineRsps);
                if(Func.isNotEmpty(ppTestLineRsps)){
                    ppTestLineRspList.addAll(ppTestLineRsps);
                }
            }
        }
        if(Func.isNotEmpty(ppTestLineRspList)){
            for(TestLineBreakDownInfo testLineBreakDownInfo : testLines){
                if(Func.isNotEmpty(testLineBreakDownInfo.getpPVersionId())){
                    //List<QueryPpTestLineRsp> testLineRspList = ppTestLineRspList.stream().filter(e -> Func.isNotEmpty(e.getPpVersionId()) && Func.isNotEmpty(e.getTestLineId()) && e.getTestLineId().equals(testLineBreakDownInfo.getTestLineId()) && e.getPpVersionId().equals(testLineBreakDownInfo.getpPVersionId())).collect(Collectors.toList());
                    QueryPpTestLineRsp testLineRsp = ppTestLineRspList.stream().filter(e -> Func.isNotEmpty(e.getAid()) && e.getAid().equals(testLineBreakDownInfo.getAid())).findAny().orElse(null);
                    if(Func.isNotEmpty(testLineRsp)){
                        testLineBreakDownInfo.setTestSeparately(testLineRsp.getTestSeparately());
                        testLineBreakDownInfo.setConstructionId(testLineRsp.getConstructionId());
                        testLineBreakDownInfo.setSectionName(testLineRsp.getSectionName());
                    }
                }
            }
        }
        rspResult.setOrderTestLines(testLines);
        List<TestLineBreakDownInfo> chemTestLines = Lists.newArrayList();
        testLines.forEach(testLine->{
            String labSectionName = testLine.getLabSectionName();
            //TL chem 分包必须要分到chem实验室，正常单productLineAbbr cchemLab，或者 labSection contins chem
            if ( (StringUtils.isNoneBlank(testLine.getSubContractTestLineId()) && labSectionName.toLowerCase().contains("chem"))
                    || StringUtils.equalsIgnoreCase(testLine.getProductLineAbbr(), "CChemLab")
                    || (StringUtils.isNotBlank(labSectionName) && labSectionName.toLowerCase().contains("chem"))){
                chemTestLines.add(testLine);
            }
        });
        rspResult.setTestLines(chemTestLines);
        return BaseResponse.newSuccessInstance(rspResult);
    }


    private void handleCsPPOpenTlAssignSample(Map<String, List<String>> csppRel,
                                              List<PPTestLineRelationshipInfoPO> ppTestLineRelationshipInfoPOList,
                                              GeneralOrderInstanceDTO generalOrderInstanceDTO) {
        // 检查输入参数是否为空，避免空指针异常
        if (csppRel == null || ppTestLineRelationshipInfoPOList == null || generalOrderInstanceDTO == null) {
            logger.warn("Input parameters are null, skipping processing.");
            return;
        }

        Set<String> csppTestLineInstanceIdList = csppRel.keySet();
        if (csppTestLineInstanceIdList.isEmpty()) {
            logger.warn("No test line instance IDs found in csppRel, skipping processing.");
            return;
        }

        OrderTestMatrixReq testMatrixReq = new OrderTestMatrixReq();
        testMatrixReq.setTestLineInstanceIdList(csppTestLineInstanceIdList);

        List<OrderTestMatrixInfo> orderTestMatrixInfoList = testMatrixExtMapper.selectOrderTestMatrixes(testMatrixReq);
        if (Func.isEmpty(orderTestMatrixInfoList)) {
            logger.warn("No order test matrix info found, skipping processing.");
            return;
        }

        // 按TestLineInstanceID 分组
        Map<String, List<OrderTestMatrixInfo>> testLineMatrixMap = orderTestMatrixInfoList.stream()
                .collect(Collectors.groupingBy(OrderTestMatrixInfo::getTestLineInstanceId));

        for (String csppTestLineInstanceId : csppTestLineInstanceIdList) {
            // 获取当前测试线实例对应的测试矩阵信息
            List<OrderTestMatrixInfo> orderTestMatrixInfoListByTL = testLineMatrixMap.getOrDefault(csppTestLineInstanceId, Collections.emptyList());
            if (Func.isEmpty(orderTestMatrixInfoListByTL)) {
                logger.warn("No test matrix info found for test line instance ID: {}", csppTestLineInstanceId);
                continue;
            }
            // 提取样本 ID 列表
            List<String> testSampleIdList = orderTestMatrixInfoListByTL.stream()
                    .map(OrderTestMatrixInfo::getSampleId)
                    .distinct()
                    .collect(Collectors.toList());
            // 获取 CSPPOpenTL 列表
            List<String> csppOpenTlList = csppRel.getOrDefault(csppTestLineInstanceId, Collections.emptyList());
            if (Func.isEmpty(csppOpenTlList) || Func.isEmpty(testSampleIdList)) {
                logger.warn("No CSPP open TL or test sample IDs found for test line instance ID: {}", csppTestLineInstanceId);
                continue;
            }
            // 筛选 PP 测试关系 ID
            List<String> ppTestRelIdList = ppTestLineRelationshipInfoPOList.stream()
                    .filter(item -> csppOpenTlList.contains(item.getTestLineInstanceID()))
                    .map(PPTestLineRelationshipInfoPO::getID)
                    .distinct()
                    .collect(Collectors.toList());
            if (Func.isEmpty(ppTestRelIdList)) {
                logger.warn("No PP test relationship IDs found for test line instance ID: {}", csppTestLineInstanceId);
                continue;
            }

            // 构建AssignMatrix请求
            SaveAssignSampleReq objAssignSampleReq = buildSaveAssignSampleReq(generalOrderInstanceDTO, ppTestRelIdList, testSampleIdList);

            // 调用服务保存Matrix
            CustomResult customResult = sampleService.executeAssignSample(objAssignSampleReq);
            if (!customResult.isSuccess()) {
                logger.error("Failed to save CSPPOpenTL matrix for test line instance ID: {}. Error: {}",
                        csppTestLineInstanceId, customResult.getMsg());
            }
        }
    }

    // 辅助方法：构建分配样本请求
    private SaveAssignSampleReq buildSaveAssignSampleReq(GeneralOrderInstanceDTO generalOrderInstanceDTO,
                                                         List<String> ppTestRelIdList,
                                                         List<String> testSampleIdList) {
        SaveAssignSampleReq req = new SaveAssignSampleReq();
        req.setOrderId(generalOrderInstanceDTO.getId());
        req.setOrderNo(generalOrderInstanceDTO.getOrderNo());
        req.setAssignType(0);
        req.setPpTestLineRelIds(ppTestRelIdList);
        req.setSampleIds(testSampleIdList);
        req.setAutoAssignReportMatrix(false);
        req.setToken(SystemContextHolder.getSgsToken());
        return req;
    }





    private Map<String,List<String>> findCSPPOpen(List<PPTestLineRelationshipInfoPO> ppTestLineRelationshipInfoPOList){
        Map<String, List<String>> testLineMap = new HashMap<>();
        if(Func.isNotEmpty(ppTestLineRelationshipInfoPOList)){
            ppTestLineRelationshipInfoPOList.stream().forEach(po ->{
                if(Func.isNotEmpty(po.getExtFields())){
                    String parentTestLineInstanceId = Func.toStr(JSONObject.parseObject(po.getExtFields()).getOrDefault("parentTestLineInstanceId", ""));
                    if(Func.isNotEmpty(parentTestLineInstanceId)){
                        if(testLineMap.containsKey(parentTestLineInstanceId)){
                            testLineMap.get(parentTestLineInstanceId).add(po.getTestLineInstanceID());
                        } else {
                            testLineMap.put(parentTestLineInstanceId,Lists.newArrayList(po.getTestLineInstanceID()));
                        }
                    }
                }
            });
        }
        return testLineMap;
    }

    private static @NotNull TestLineTypeUpdateReq getTestLineTypeUpdateReq(TestLineBreakDownInfo testLineBreakDownInfo) {
        TestLineTypeUpdateReq testLineTypeUpdateReq = new TestLineTypeUpdateReq();
        testLineTypeUpdateReq.setTestLineInstanceId(testLineBreakDownInfo.getTestLineInstanceId());
        testLineTypeUpdateReq.setTestLineType(testLineBreakDownInfo.getTestLineType() | TestLineType.CSPP_OPEN.getType());
        testLineTypeUpdateReq.setModifiedDate(new Date());
        testLineTypeUpdateReq.setModifiedBy(SecurityContextHolder.getUserInfoFillSystem().getRegionAccount());
        return testLineTypeUpdateReq;
    }

    /**
     *
     * @param reqObject
     * @return
     */
    public CustomResult returnTestLine(ReturnTestLineReq reqObject){
        CustomResult rspResult = new CustomResult();

        UserInfo localUser = UserHelper.getLocalUser();
        TestLineInstancePO line = testLineInstanceMapper.selectByPrimaryKey(reqObject.getTestLineInstanceId());
        TestLineInstancePO lineNew = new TestLineInstancePO();
        lineNew.setID(reqObject.getTestLineInstanceId());
        lineNew.setTestLineStatus(TestLineStatus.Entered.getStatus());
        lineNew.setModifiedDate(DateUtils.getNow());
        lineNew.setModifiedBy(localUser.getRegionAccount());

        rspResult.setSuccess(transactionTemplate.execute((tranStatus)->{
            boolean isSuccess = testLineInstanceMapper.updateByPrimaryKeySelective(lineNew) == 1;
            if (!isSuccess ){
                // 回滚事务
                tranStatus.setRollbackOnly();
                return false;
            }
            return true;
        }));
        return rspResult;
    }

    /**
     *
     * @param reqObject
     * @return
     */
    @BizLog(bizType = BizLogConstant.DATA_ENTRY_OPERATION_HISTORY, operType="Retest")
    public CustomResult reTest(ReTestReq reqObject){
        CustomResult rspResult = new CustomResult();
        String testLineInstanceID = reqObject.getTestLineInstanceId();
        // delete all testdata

        List<String> testMatrixs = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(reqObject.getTestMatrixId())) {
            testMatrixs = reqObject.getTestMatrixId();
        } else {
            testMatrixs = testMatrixMapper.getTestMatrixByTestLineId(testLineInstanceID);
        }

//        List<String> testLineInstanceIDs = new ArrayList<>();
//        testLineInstanceIDs.add(testLineInstanceID);

        GeneralOrderInstanceInfoPO order = orderMapper.getOrderByTestLineInstanceId(testLineInstanceID);

        String username="system";
        UserInfo userInfo = UserHelper.getLocalUser();
        if(Func.isNotEmpty(userInfo)){
            username = userInfo.getRegionAccount();
        }

        // 修改testline的状态为typing  DIG-6577 retest后 testLine 状态变为Entry
        TestLineInstancePO line = testLineMapper.getTestLineById(testLineInstanceID);
        line.setTestLineStatus(TestLineStatus.Entered.getStatus());
        line.setModifiedDate(DateUtils.getNow());
        line.setModifiedBy(username);
        //DIG-6913 retest 不清空
//        //DIG-5555 retest 清空无样式的文件
//        line.setFileID(null);
        line.setValidateBy(null);
        line.setValidateDate(null);

        // 业务动作日志记录
        ActionLogDTO dto = new ActionLogDTO();
        dto.setTestLineInstanceId(testLineInstanceID);
        dto.setOrderNo(order.getOrderNo());
        String testValue = String.valueOf(line.getTestLineID());
        // 获取Matrix的Sample
        if (CollectionUtils.isNotEmpty(testMatrixs)) {
            List<String> sampleList = testMatrixMapper.getSampleListByMatrixs(testMatrixs);
            if (CollectionUtils.isNotEmpty(sampleList)) {
                testValue = String.format("%s(%s)", testValue, StringUtils.join(sampleList, ","));
            }
        }
        BizLogHelper.setValue(order.getOrderNo(), testValue);
        OrderInfoDto orderInfoDto = orderClient.getOrderInfoByOrderNo(order.getOrderNo());
        if (Func.isNotEmpty(orderInfoDto)){
            BizLogHelper.setLabCode(orderInfoDto.getBUCode(),orderInfoDto.getLocationCode());
        }


        List<String> finalTestMatrixs = testMatrixs;
        String finalUsername = username;
        rspResult.setSuccess(transactionTemplate.execute((tranStatus)->{
            // DIG-6913 retest 不清空 TestData
//            boolean failedDelIsSuccess = testDataMapper.deleteFailedByTLIdAndMatrixIDs(testLineInstanceID, finalTestMatrixs) > 0;
//            boolean testDataDelIsSuccess = testDataMapper.deleteByTLIdAndMatrixId(testLineInstanceID, finalTestMatrixs) > 0;

            ReportInfoPO reportInfo = new ReportInfoPO();
            ReportInfoPO reportPO = new ReportInfoPO();
            // DIG-6913 清空所有Conclusion
            boolean conclusionDelIsSuccess = conclusionMapper.deleteConclusionByTestLineInstanceId(testLineInstanceID) > 0;
            boolean recalculationUpdIsSuccess = true;
            boolean md5DelIsSuccess = true;
            if(conclusionDelIsSuccess){
                reportPO = reportMapper.getReportByOrderNo(order.getOrderNo());

                if(CommUtil.null2Int(reportPO.getRecalculationFlag())==1){
                    reportPO.setRecalculationFlag(2);

                    reportInfo.setRecalculationFlag(2);
                    reportInfo.setModifiedBy(finalUsername);
                    reportInfo.setModifiedDate(DateUtils.getNow());
                    reportInfo.setID(reportPO.getID());
                    recalculationUpdIsSuccess = reportMapper.updateRecalculationFlagByReportId(reportInfo) > 0;
                    //在retest的时候把reportConclusionMD5的值清除掉

                    md5DelIsSuccess = reportMapper.deleteMd5ByReportId(reportPO.getID()) > 0;
                }
            }

            // DIG-6577 不清空footNotes
//            boolean ppConditionGroupUpdIsSuccess = testPpConditionGroupExtMapper.deleteByConditionGroupIDsByTLId(testLineInstanceID) > 0;

            boolean testLineUpdIsSuccess = testLineStatusService.updateMatrixStatus(TestLineModuleType.ReTest, testLineInstanceID, Sets.newHashSet(finalTestMatrixs)).isSuccess();



            if (!testLineUpdIsSuccess){
                tranStatus.setRollbackOnly(); // 回滚事务
                return false;
            }else {
                TestLineStatusUpdateReq testLineStatusUpdateReq = new TestLineStatusUpdateReq();
                testLineStatusUpdateReq.setOrderNo(order.getOrderNo());
                testLineStatusUpdateReq.setProductLineCode(reqObject.getProductLineCode());
                CustomResult customResult = testLineStatusServiceNew.onChange(testLineStatusUpdateReq);
                if(!customResult.isSuccess()){
                    tranStatus.setRollbackOnly();
                    return false;
                }
                SysStatusReq reqStatus = new SysStatusReq();
                reqStatus.setObjectNo(order.getOrderNo());
                reqStatus.setNewStatus(PreOrderStatus.Testing.getStatus());
                reqStatus.setOldStatus(PreOrderStatus.Reporting.getStatus());
                reqStatus.setUserName(finalUsername);
                statusClient.insertStatusInfo(reqStatus);
                return true;
            }
        }));

        if (rspResult.isSuccess()){
//            CustomResult customResult = jobService.handleRetest(testLineInstanceID);
//            logger.info("jobService.handleRetest返回结果:{}", JSON.toJSONString(customResult));
        }
        return rspResult;
    }

    /**
     * ProtocolRetest
     * @param reqObject
     * @return
     */
    public CustomResult reTestNew(ReTestReq reqObject){
        CustomResult customResult = new CustomResult();
        if(Func.isEmpty(reqObject.getTestLineInstanceId())){
            customResult.fail("缺少TL参数");
            return customResult;
        }
        // TestLine状态为Completed
        TestLineInstancePO testLineInfo = testLineInstanceMapper.selectByPrimaryKey(reqObject.getTestLineInstanceId());
        if(Func.isEmpty(testLineInfo) || !TestLineStatus.check(testLineInfo.getTestLineStatus(),TestLineStatus.Completed)){
            customResult.fail("TestLine必须为Completed状态才可以执行Retest。");
            return customResult;
        }
        // 关联的Report的状态为New
        List<ReportDTO> relReportList = testLineMapper.queryReportByTestLine(testLineInfo.getID());
        if(Func.isNotEmpty(relReportList)){
            ReportDTO activeReport = relReportList.stream().filter(report-> !ReportStatus.check(report.getReportStatus(),ReportStatus.New) &&
                    !ReportStatus.checkCategory(report.getReportStatus(), Constants.REPORT.STATUS_CATEGORY.INACTIVE)).findAny().orElse(null);
            if(Func.isNotEmpty(activeReport)){
                customResult.fail("TestLine存在关联的Report状态非New，不允许执行Retest。");
                return customResult;
            }
        }
        GeneralOrderInstanceInfoPO gpnOrder = orderMapper.getOrderInfoByOrderId(testLineInfo.getGeneralOrderInstanceID());
        if(Func.isEmpty(gpnOrder)){
            customResult.fail("Order 查询异常。");
            return customResult;
        }
        // Retest后需要更新Tl状态为Entered
        TestLineStatusUpdateReq testLineStatusUpdateReq = new TestLineStatusUpdateReq();
        testLineStatusUpdateReq.setOrderNo(gpnOrder.getOrderNo());
        testLineStatusUpdateReq.setSynMatrixStatus(true);
        List<String> testLineInstanceIds = Lists.newArrayList();
        testLineInstanceIds.add(testLineInfo.getID());
        testLineStatusUpdateReq.setTestLineInstanceIds(testLineInstanceIds);
        return testLineStatusServiceNew.entered(testLineStatusUpdateReq);
    }

    public CustomResult getSubContractTestLine(GetSubContractTestLineReq getSubContractTestLineReq){
        CustomResult rspResult = new CustomResult();
        //设置参数
        TestLineListSearchReq testLineListSearchReq = new TestLineListSearchReq();
        testLineListSearchReq.setOrderNo(getSubContractTestLineReq.getOrderNo());
        testLineListSearchReq.setSubcontractId(getSubContractTestLineReq.getSubContractId());
        testLineListSearchReq.setAllflag(getSubContractTestLineReq.getAllflag());
        //调用testLine统一查询
        PageInfo<TestLinePageListDTO> testLinePageListDTOPageInfo = this.testLineListSearch(testLineListSearchReq);
        List<TestLinePageListDTO> list = testLinePageListDTOPageInfo.getList();

//        List<SubcontractDTO> list = null;
//        List<SubcontractDTO> defaultlist = subContractExtMapper.queryDefaultSubcontractTestLine(getSubContractTestLineReq.getOrderNo());
//        List<SubcontractDTO> subContractlist = subContractExtMapper.querySubcontractBySubcontractId(getSubContractTestLineReq.getSubContractId());

//        if (getSubContractTestLineReq.getAllflag() == 1) {// 展示所有TL
//            list = subContractExtMapper.querySubContractTestLine(getSubContractTestLineReq.getOrderNo());
//
//            if (!defaultlist.isEmpty()) {
//                for (SubcontractDTO sub : list) {
//                    for (SubcontractDTO dd : defaultlist) {
//                        if (!dd.getTestLineInstanceID().equals(sub.getTestLineInstanceID())) {
//                            sub.setFlag(true);
//                            break;
//                        }
//                    }
//                }
//            } else {
//                for (SubcontractDTO sub : list) {
//                    sub.setFlag(true);
//                }
//            }
//            for (SubcontractDTO dd : subContractlist) {
//                for (SubcontractDTO sub : list) {
//                    if (dd.getTestLineInstanceID().equals(sub.getTestLineInstanceID())) {
//                        sub.setSelectFlag(true);
//                        sub.setFlag(false);
//                        break;
//                    }
//                }
//            }
//
//        } else {
//            list = new ArrayList<>();
//            list.addAll(defaultlist);
//            if (!subContractlist.isEmpty()) {
//                for (SubcontractDTO sub : subContractlist) {
//                    sub.setSelectFlag(true);
//                    list.add(sub);
//                }
//            }
//        }

        for (TestLinePageListDTO dto : list) {
            //判断selectFlag
            if (Func.equals(getSubContractTestLineReq.getSubContractId(),dto.getSubcontractId())){
                dto.setSelectFlag(true);
            }else {
                dto.setSelectFlag(false);
            }
            if (StringUtils.isBlank(dto.getLabSection())) {
                if (StringUtils.isBlank(dto.getSubContractName())) {
                    dto.setLabSection("/");
                } else {
                    dto.setLabSection(dto.getSubContractName());
                }
            }
        }
        TestLineTatConfigExample testLineTatConfigPONull = new TestLineTatConfigExample();
        List<TestLineTatConfigPO> testLineTATConfigPOS = testLineTatConfigMapper.selectByExample(testLineTatConfigPONull);
        list.forEach(x->{
            List<TestLineTatConfigPO> collect = testLineTATConfigPOS.stream().filter(y -> y.getTestLineID().equals(Integer.valueOf(x.getTestLineId()))).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(collect)) {
                /*String token = getSubContractTestLineReq.getToken();
                Map<String, Object> params = new HashMap<>();
                params.put("orderNo", getSubContractTestLineReq.getOrderNo());
                params.put("token", token);*/
                GetOrderInfoRsp getOrderParams = new GetOrderInfoRsp();
                getOrderParams.setOrderNo(getSubContractTestLineReq.getOrderNo());

                try {
                    PreOrder generalOrder = preOrderClient.getGeneralOrder(getOrderParams);
                    if(collect.get(0).getTat()>Integer.valueOf(generalOrder.getTat())) {
                        x.setLongTatMsg("TestLine TAT:" + collect.get(0).getTat() + " Days");
                    }
                } catch (Exception e) {
                    logger.error("调用OrderApi出错：", e);
                }
            }
        });

        List<GetSubContractTestLineRsp> getSubContractTestLineRsps=new ArrayList<GetSubContractTestLineRsp>(list.size());
        list.forEach(subcontractDTO -> {
            GetSubContractTestLineRsp objGetSubContractTestLineRsp=new GetSubContractTestLineRsp();
            BeanUtils.copyProperties(subcontractDTO,objGetSubContractTestLineRsp);
            objGetSubContractTestLineRsp.setTestLineInstanceId(subcontractDTO.getTestLineInstanceID());
            objGetSubContractTestLineRsp.setTestLineVersionId(subcontractDTO.getTestLineVersionID());
            getSubContractTestLineRsps.add(objGetSubContractTestLineRsp);
        });
        //TODO v-tl别名修改
        testLineLocalService.build(getSubContractTestLineReq.getOrderNo(),getSubContractTestLineRsps);
        rspResult.setData(getSubContractTestLineRsps);
        rspResult.setSuccess(true);
        return rspResult;
    }

    @BizLog(bizType=BizLogConstant.TEST_HISTORY,operType="Change Matrix")
    @AccessPolicyRule(reportStatus = { ReportStatus.Approved, ReportStatus.Cancelled }, subContractType = SubContractOperationTypeEnums.UpdateTLStatus)
    public CustomResult updateTLStatus(UpdateTLStatusReq reqObject){
        CustomResult result = new CustomResult<>();
        if (StringUtils.isBlank(reqObject.getTestLineInstanceId())) {
            result.setSuccess(false);
            result.setMsg( "params is Empty");
            return result;
        }
        TestLineInstancePO po = testLineMapper.getTestLineInfo(reqObject.getTestLineInstanceId());
        if (po == null) {
            result.setSuccess(false);
            result.setMsg( "Get data error");
            return result;
        }
        GeneralOrderInstanceInfoPO order = generalOrderInstanceInfoMapper.selectByPrimaryKey(po.getGeneralOrderInstanceID());
        //校验当前订单是否是内部分包被锁定订单
        List<SubcontractDTO> subcontractDTOS = subContractExtMapper.querySubContractListByTLIds(Lists.newArrayList(reqObject.getTestLineInstanceId()));
        if(CollectionUtils.isNotEmpty(subcontractDTOS)){
            //有做分包
            SubcontractDTO subcontractDTO = subcontractDTOS.get(0);
            String subContractNo = subcontractDTO.getSubContractNo();
            SubContractExample example=new SubContractExample();
            example.createCriteria().andSubContractNoEqualTo(subContractNo);

            List<SubContractPO> subContractPOs = subContractMapper.selectByExample(example);
            if(CollectionUtils.isNotEmpty(subContractPOs)){
                SubContractPO subContractPO = subContractPOs.get(0);
                Integer subContractOrder = subContractPO.getSubContractOrder();
                //说明是内部分包，需要进行校验
                if(subContractOrder!=null && subContractOrder.compareTo(1)==0){
                    Integer dataLock = subContractPO.getDataLock();
                    if(dataLock!=null && dataLock.compareTo(1)==0){
                        result.setSuccess(false);
                        result.setMsg(order.getOrderNo() + " data locked,Please click unlock button first");
                        return result;
                    }
                }
            }
        }

//        result.setSuccess(transactionTemplate.execute((tranStatus)->{
//            Integer status = po.getTestLineStatus();
//            if (TestLineStatus.check(status,TestLineStatus.Submit,TestLineStatus.Completed)){
//                // DIG-6914 change 时 不更改 TestLine 状态
////                List<TestLineInstancePO> testLines=Lists.newArrayList();
////                TestLineInstancePO testLineInstancePO=new TestLineInstancePO();
////                testLineInstancePO.setTestLineStatus(TestLineStatus.Entered.getStatus());
////                testLineInstancePO.setModifiedBy(UserHelper.getLocalUser().getRegionAccount());
////                testLineInstancePO.setModifiedDate(DateUtils.getNow());
////                testLineInstancePO.setID(po.getID());
////                testLines.add(testLineInstancePO);
////                testLineStatusService.batchUpdateTestLineStatus(TestLineModuleType.Change, testLines);
//                if (StringUtils.isNotBlank(reqObject.getSubcontractId())){
//                    SubContractPO subContract = new SubContractPO();
//                    subContract.setID(reqObject.getSubcontractId());
//                    subContract.setStatus(1);
//                    subContract.setModifiedBy(UserHelper.getLocalUser().getRegionAccount());
//                    subContract.setModifiedDate(DateUtils.getNow());
//                    subContractMapper.updateByPrimaryKeySelective(subContract);
//                }
//                // DIG-2234
//                // add by vincent POSL 1137 2018年7月26日
//                // modify by vincent 2018年12月7日 最新需求，在点击Change按钮修改TL状态时，不再重置job状态
//                // orderStatusChangeUtil.updateJobStatusByChangeTLstatus(token, id);
//                SysStatusReq reqStatus = new SysStatusReq();
//                reqStatus.setObjectNo(order.getOrderNo());
//                reqStatus.setOldStatus(com.sgs.preorder.facade.model.enums.OrderStatus.Reporting.getStatus());
//                reqStatus.setNewStatus(com.sgs.preorder.facade.model.enums.OrderStatus.Testing.getStatus());
//                reqStatus.setUserName(UserHelper.getLocalUser().getRegionAccount());
//                statusClient.insertStatusInfo(reqStatus);
//            }
//            return true;
//        }));
//        BizLogHelper.setValue(order.getOrderNo(), po.getTestLineID());
        result.setSuccess(true);
        return result;
    }


    public CustomResult rollBackTLStatusAndType(List<String> ids,TestLineStatus status){
        CustomResult customResult = new CustomResult();
        if(Func.isEmpty(ids)){
            return customResult;
        }
        List<TestLineInstancePO> testLineList = this.getTestLineListByIds(ids);
        if(Func.isEmpty(testLineList)){
            return customResult;
        }
        testLineList.stream().forEach(testLine->{
            if(TestLineStatus.check(testLine.getTestLineStatus(), Entered)){
                testLine.setTestLineStatus(Typing.getStatus());
            }
            if(Func.isNotEmpty(status)){
                testLine.setTestLineStatus(status.getStatus());
            }
            if(TestLineType.check(testLine.getTestLineType(),TestLineType.SubContractOrder)){
                testLine.setTestLineType(testLine.getTestLineType() ^ TestLineType.SubContractOrder.getType());
            }
            testLine.setModifiedDate(DateUtils.getNow());
        });
        logger.info("rollBackTLStatusAndType:{}",testLineList);
        testLineMapper.batchUpdateTestLineStatusAndTestLineType(testLineList);
        return customResult;
    }

    @AccessPolicyRule(reportStatus = { ReportStatus.Approved, ReportStatus.Cancelled })
    public CustomResult getTlTemplateData(TlTemplateReq tlTemplateReq){
        CustomResult rspResult = new CustomResult();
        TestLineExportRsp testLineExportRsp=new TestLineExportRsp();
        GeneralOrderInstanceInfoPO generalOrderInstanceInfoPO= generalOrderInstanceInfoMapper.selectByPrimaryKey(tlTemplateReq.getGeneralOrderInstanceID());
        TestSampleInfoExample testSampleInfoExample=new TestSampleInfoExample();
        testSampleInfoExample.createCriteria().andOrderNoEqualTo(generalOrderInstanceInfoPO.getOrderNo()).andActiveIndicatorEqualTo(true);
        List<TestSampleInfoPO> testSampleInfoPOS= testSampleInfoMapper.selectByExample(testSampleInfoExample);
        List<TestSampleRsp> testSampleRsps=Lists.newArrayList();
        for(TestSampleInfoPO samplePO:testSampleInfoPOS){
            TestSampleRsp testSampleRsp=new TestSampleRsp();
            BeanUtils.copyProperties(samplePO,testSampleRsp);
            testSampleRsp.setId(samplePO.getID());
            testSampleRsps.add(testSampleRsp);
        }
        testSampleInfoPOS.forEach(testSampleInfoPO -> {
            testSampleInfoPO.setCategory(StringUtils.isBlank(testSampleInfoPO.getCategory())?SampleType.OriginalSample.getCategoryPhy():testSampleInfoPO.getCategory());
        });
        testLineExportRsp.setSampleList(testSampleRsps);
        OrderTestLineReq reqObject=new OrderTestLineReq();
        reqObject.setOrderId(tlTemplateReq.getGeneralOrderInstanceID());
        CustomResult<List<OrderTestLineRsp>>  customResult=this.getTestLineListByOrderId(reqObject);
        testLineExportRsp.setQueryTestLineRsps(customResult.getData());
        testLineExportRsp.setOrderNo(generalOrderInstanceInfoPO.getOrderNo());
        testLineExportRsp.setCustomerGroupName(generalOrderInstanceInfoPO.getCustomerGroupName());
        OrderIdReq reqOrder = new OrderIdReq();
        reqOrder.setOrderNo(generalOrderInstanceInfoPO.getOrderNo());

        SlOrderInfoRep slOrderInfoRep= orderClient.getSlOrderInfoByOrderId(reqOrder);
        testLineExportRsp.setCr(StringUtils.isNoneBlank(slOrderInfoRep.getCr()) ? slOrderInfoRep.getCr() : "");
        testLineExportRsp.setOr(StringUtils.isNoneBlank(slOrderInfoRep.getOr()) ? slOrderInfoRep.getOr() : "");
        testLineExportRsp.setOrderExpectDueDate(slOrderInfoRep.getOrderExpectDueDate());
        rspResult.setData(testLineExportRsp);
        rspResult.setSuccess(true);
        return rspResult;
    }

    public String tranNull(String str){
        if (str == null){
            return " ";
        }else {
            return str;
        }
    }

    public CustomResult getConfrimMatrixGemoData(GemoReq gemoReq){
        List<GemoRsp> gemoRsps= testLineMapper.getConfirmMatrixGemoTestLine(gemoReq);
        if (CollectionUtils.isNotEmpty(gemoRsps)) {
            setGemoDTO(gemoRsps,gemoReq.getCustomerCode());
        }
        CustomResult customResult=new CustomResult();
        customResult.setSuccess(true);
        customResult.setData(gemoRsps);
        return  customResult;
    }

    public CustomResult getSoftcopyGemoData(GemoReq gemoReq){
        List<String> reportNos = gemoReq.getReportNos();
        CustomResult customResult=new CustomResult();
        customResult.setSuccess(true);
        if(CollectionUtils.isNotEmpty(reportNos)){
            ReportInfoExample example=new ReportInfoExample();
            example.createCriteria().andReportNoIn(reportNos);
            List<ReportInfoPO> reportInfoPOS= reportInfoMapper.selectByExample(example);
            List<GemoRsp> gemoDTOs = Lists.newArrayList();
            for (ReportInfoPO report : reportInfoPOS) {
                GetTestLineConclusionReq getTestLineConclusionReq = new GetTestLineConclusionReq();
                getTestLineConclusionReq.setReportNo(report.getReportNo());
                CustomResult<List<GetTestLineConclusionRsp>> testLineConclusionResult = reportService.getTestLineConclusion(getTestLineConclusionReq);
                List<GetTestLineConclusionRsp> testLineConclusionRsps = testLineConclusionResult.getData();
                if (CollectionUtils.isEmpty(testLineConclusionRsps)){
                    continue;
                }
                GetTestLineConclusionRsp testLineConclusionRsp = testLineConclusionRsps.get(0);
                List<ReportTestLineConclusionDTO> distinctTestLineList = testLineConclusionRsp.getDistinctTestLineList();
                for (ReportTestLineConclusionDTO testLineConclusionDTO : distinctTestLineList) {
                    GemoRsp gemoRsp = new GemoRsp();
                    gemoRsp.setTestLineID(testLineConclusionDTO.getTestLineID());
                    gemoRsp.setEvaluationAlias(testLineConclusionDTO.getTestItem());
                    gemoRsp.setDescription(testLineConclusionDTO.getTestLineConclusion());
                    gemoRsp.setOrderNo(report.getOrderNo());
                    gemoRsp.setTestLineInstanceId(testLineConclusionDTO.getTestLineInstanceID());
                    gemoDTOs.add(gemoRsp);
                }
            }


            if (CollectionUtils.isNotEmpty(gemoDTOs)) {
                setGemoDTO(gemoDTOs,gemoReq.getCustomerCode());
            }
            customResult.setData(gemoDTOs);
        }
        return customResult;
    }

    private void setGemoDTO(List<GemoRsp> gemoDTOs,String customerCode) {
        Set<Integer> testLineVersionSet = Sets.newHashSet();
        gemoDTOs.forEach(gemoDTO -> {
            testLineVersionSet.add(gemoDTO.getTestLineVersionID());
        });

        TrimsTestLineReq reqObject=new TrimsTestLineReq();
        reqObject.setVersionIdentifiers(testLineVersionSet);
        reqObject.setCustomerType("Customer");
        reqObject.setCustomerCode("2431676");
        List<TestLineInfo> testLineList = trimsClient.getTestLineList(reqObject);
        Map<Integer, TestLineInfo> testLineMap =Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(testLineList)) {
            testLineList.forEach(testLine -> {
                testLineMap.put(testLine.getTestLineId(), testLine);
            });
        }
        gemoDTOs.forEach(gemoDTO -> {
            TestLineInfo testLine = testLineMap.get(gemoDTO.getTestLineID());
            gemoDTO.setCustomerTestCategoryId(testLine == null ? "" : testLine.getCustomerTestCategoryId());
            gemoDTO.setCustomerTestCategoryName(testLine == null ? "" : testLine.getCustomerTestCategoryName());
        });
    }

    /**
     *
     * @param reqObject
     * @return
     */
    public CustomResult<List<MatrixTestLineRelInfo>> getPPTestLineRelList(OrderPpTestLineReq reqObject){
        CustomResult rspResult = new CustomResult();
        List<PPTestLineRelReq> ppTestLineRels = reqObject.getPpTestLineRels();
        if (ppTestLineRels == null || ppTestLineRels.isEmpty()){
            return rspResult;
        }
        List<MatrixTestLineRelInfo> oldPpTestLineRels = testLineMapper.getPPTestLineRelList(reqObject.getOrderNo());
        if (oldPpTestLineRels == null || oldPpTestLineRels.isEmpty()){
            return rspResult;
        }
        Map<String, MatrixTestLineRelInfo> ppTestLineRelMaps = Maps.newHashMap();
        oldPpTestLineRels.forEach(rel->{
            String matrixGroupKey = String.format("%s_%s", Func.toStr(rel.getPpTestLineRelId()), rel.getMatrixGroupId());
            if (ppTestLineRelMaps.containsKey(matrixGroupKey)){
                return;
            }
            if(rel.getMatrixGroupId()>0){
                ppTestLineRelMaps.put(matrixGroupKey, rel);
                return;
            }
            if (ppTestLineRels.stream().filter(testLineRel -> StringUtils.equalsIgnoreCase(testLineRel.getPpTestLineRelId(), rel.getPpTestLineRelId()) && NumberUtil.equals(testLineRel.getMatrixGroupId(), rel.getMatrixGroupId())).count() <= 0){
                return;
            }
            ppTestLineRelMaps.put(matrixGroupKey, rel);
        });
        rspResult.setSuccess(!ppTestLineRelMaps.isEmpty());
        rspResult.setData(ppTestLineRelMaps.values());
        return rspResult;
    }

//    @TestLinePending(filedName = "testLineInstanceId",type=TestLinePendingTypeEnums.TL_ID)
    @AccessPolicyRule(testLinePendingType = TestLinePendingTypeEnums.TestLineInstanceId)
    public CustomResult<Object> checkTestLineSubContractStatus(SingleTestLienReq req) {
        CustomResult result = new CustomResult();

        String testLineInstanceId = req.getTestLineInstanceId();

        Map<String,Object> map = new HashMap<>();
        List<SubContractTestLineMappingPO> mapping=subContractExtMapper.queryMappingByTestLineIds(Arrays.asList(testLineInstanceId));

        if(CollectionUtils.isNotEmpty(mapping)){
            SubContractPO subContractPO=subContractMapper.selectByPrimaryKey(mapping.get(0).getSubContractID());
            Integer dataLock = subContractPO.getDataLock();
            map.put("dataLock",dataLock);
            if(subContractPO.getStatus() == 2){
                String subContractID = mapping.get(0).getSubContractID();
                map.put("subContractID",subContractID);
            }
            String subContractNo = subContractPO.getSubContractNo();
            Map<String,Object> param = new HashMap<>();
            param.put("subContractNo",subContractNo);
            SubContractExternalRelationshipPO objSubContractSlimJobPO=new SubContractExternalRelationshipPO();
            objSubContractSlimJobPO.setSubContractNo(subContractNo);
            SubContractExternalRelationshipPO slimJobPO = null;
            BaseResponse<List<SubContractExternalRelationshipPO>> response = subContractExternalRelService.getSubContractExternalRels(objSubContractSlimJobPO);
            if(response.isSuccess()&&Func.isNotEmpty(response.getData())&&response.getData().size()>0){
                slimJobPO = response.getData().get(0);
            }
            if(slimJobPO!=null && slimJobPO.getSubContractType()!=null && slimJobPO.getSubContractType().compareTo(1)==0){
                map.put("tlIsInternalSub",true);
            }
            result.setData(map);
        }
        result.setSuccess(true);
        return result;
    }

    public List<TestLineInstancePO> getTestLineByOrderIdAndStatus(TestLineStatusReq testLine) {
        return testLineMapper.getTestLineByOrderIdAndStatus(testLine);
    }

    /**
     * 获取 TestLine 或 PP 的中文名称
     *
     * @param getTestLinePPNameZHReq
     * @return
     */
    public CustomResult<List<GetTestLinePPNameZHRsp>> getTestLinePPNameZH(GetTestLinePPNameZHReq getTestLinePPNameZHReq) {
        CustomResult result = new CustomResult();
        result.setSuccess(true);
        if (StringUtils.isBlank(getTestLinePPNameZHReq.getOrderNo())) {
            result.setSuccess(false);
            result.setMsg("OrderNo params is Empty");
            return result;
        }
        Map<String, List<GetTestLinePPNameZHRsp>> nameZhRspMap = new HashMap<>();
        // 获取所有的TestLineId PP的对应中文名
        List<GetTestLinePPNameZHRsp> testLineNameZHRsps = this.testLineMapper.getTestLineNameZH(getTestLinePPNameZHReq.getOrderNo());
        List<GetTestLinePPNameZHRsp> testLineNameZHRspList = new ArrayList<>();
        Map<String, GetTestLinePPNameZHRsp> map = new HashMap<>();
        testLineNameZHRsps.forEach(testLineNameZHRsp -> {
            if (testLineNameZHRsp.getTestLineId() == null || map.containsKey(testLineNameZHRsp.getTestLineId())) {
                return;
            }
            GetTestLinePPNameZHRsp testLinePPNameZHRsp = new GetTestLinePPNameZHRsp();
            testLinePPNameZHRsp.setTestLineId(testLineNameZHRsp.getTestLineId());
            testLinePPNameZHRsp.setEvaluationName(testLineNameZHRsp.getEvaluationName());
            testLineNameZHRspList.add(testLinePPNameZHRsp);
            map.put(testLineNameZHRsp.getTestLineId(), testLinePPNameZHRsp);
        });

        if (CollectionUtils.isNotEmpty(testLineNameZHRspList)) {
            nameZhRspMap.put("TLZHList", testLineNameZHRspList);
        }
        result.setData(nameZhRspMap);
        result.setSuccess(true);
        return result;
    }

    public CustomResult<List<ProductLineRsp>> getBuCodeList() {
        CustomResult<List<ProductLineRsp>> result = new CustomResult<>();
        List<ProductLine> productLines = frameWorkClient.getProductLines(null);
        List<ProductLineRsp> productLineRsps = new ArrayList<>();
        if(Func.isNotEmpty(productLines)){
            for(ProductLine productLine:productLines) {
                ProductLineRsp productLineRsp = Func.copy(productLine, ProductLineRsp.class);
                productLineRsp.setProductLineId(Func.toStr(productLine.getProductLineID()));
                productLineRsps.add(productLineRsp);
            }
        }
        try{
            UserLabBuInfo userLabBuInfo  = userManagementClient.getUserLabBuInfo(SecurityUtil.getSgsToken());
            for (ProductLineRsp rsp : productLineRsps) {
                String productLineAbbr = rsp.getProductLineAbbr();
                rsp.setDefaultAbbr(StringUtils.equalsIgnoreCase(productLineAbbr,userLabBuInfo.getProductLineCode()));
            }
        }catch (Exception e){
            logger.error("Matrix table 添加TL时，获取用户当前labCode异常");
        }
        result.setSuccess(true);
        result.setData(productLineRsps);
        return result;
    }


    private boolean checkDeleteReportMatrix(List<String> testLineInstanceIds) {
        TestMatrixInfoExample objTestMatrixInfoExample=new TestMatrixInfoExample();
        objTestMatrixInfoExample.createCriteria().andTestLineInstanceIDIn(testLineInstanceIds);
        List<TestMatrixInfoPO> testMatrixInfoPOS=testMatrixInfoMapper.selectByExample(objTestMatrixInfoExample);
        if(Func.isNotEmpty(testMatrixInfoPOS)){

            ReportMatrixRelationShipInfoExample objReportMatrixRelationShipInfoExample=new ReportMatrixRelationShipInfoExample();
            objReportMatrixRelationShipInfoExample.createCriteria().andTestMatrixIDIn(testMatrixInfoPOS.stream().map(e->e.getID()).collect(Collectors.toList()));
            List<ReportMatrixRelationShipInfoPO> reportMatrixRelationShipInfoPOS=reportMatrixRelationShipInfoMapper.selectByExample(objReportMatrixRelationShipInfoExample);
            if(Func.isNotEmpty(reportMatrixRelationShipInfoPOS)){
                return false;
            }
        }
        return false;
    }

    /**
     * matrix table组件查询接口
     * 新的查询以ppTlRel为主表，开始关联查询
     * @param saveTestLineInstanceReqs
     * @return
     */
    public int batchUpdateTestlineInstance(List<SaveTestLineInstanceReq> saveTestLineInstanceReqs) {
        return this.testLineMapper.batchUpdateTestlineInstance(saveTestLineInstanceReqs);
    }


    /**
     * 查询testLine列表
     * @param testLineListSearchReq
     * @return
     */
    public PageInfo<TestLinePageListDTO> testLineListSearch(TestLineListSearchReq testLineListSearchReq){
        //设置最大分页
        if (null == testLineListSearchReq.getPage() || null == testLineListSearchReq.getRows()){
            testLineListSearchReq.setPage(1);
            testLineListSearchReq.setRows(1000);
        }
        //分页工具
        PageHelper.startPage(testLineListSearchReq.getPage(), testLineListSearchReq.getRows());
        //查询数据
        List<TestLinePageListDTO> testLinePageListDTOS = null;
        if (Func.equals(TestLineRequestType.pageList.getStatus(),testLineListSearchReq.getRequestType())){
            testLinePageListDTOS = testLineMapper.getTestLineByPage(testLineListSearchReq);
        }else {
            testLinePageListDTOS = testLineMapper.getPageList(testLineListSearchReq);
        }
        if (CollectionUtils.isEmpty(testLinePageListDTOS)){
            return PageInfo.of(testLinePageListDTOS);
        }
        //中英文翻译
        //获取用户语言
//        String defaultLanguageCode = tokenClient.getUser().getDefaultLanguageCode();
        String defaultLanguageCode = frameWorkClient.getPrimaryLanguageCode(ProductLineContextHolder.getProductLineCode());
        if (!UserInfoDefaultLanguageCodeEnums.check(defaultLanguageCode,UserInfoDefaultLanguageCodeEnums.en_us)){
            //不是英文语言时需要翻译
            LocalizableTranslator.translate(testLinePageListDTOS,UserInfoDefaultLanguageCodeEnums.getIdByCode(defaultLanguageCode));
            //处理testStandard
            testLinePageListDTOS.stream().forEach(testLinePageListDTO -> {
                if (Func.isNotEmpty(testLinePageListDTO.getCitationName())){
                    testLinePageListDTO.setTestStandard(testLinePageListDTO.getCitationName());
                }
            });
        }
        // OOB业务场景下优先显示CustomerTestLineName
        if(Func.isNotEmpty(testLinePageListDTOS)){
            testLinePageListDTOS.stream().forEach(ts ->{
                Integer testLineType = ts.getTestLineType();
                if(Func.isNotEmpty(testLineType) && (testLineType & TestLineType.OOB_TEST.getType()) == TestLineType.OOB_TEST.getType()){
                    if(UserInfoDefaultLanguageCodeEnums.check(defaultLanguageCode,UserInfoDefaultLanguageCodeEnums.en_us)){
                        if(Func.isNotEmpty(ts.getCustomerTestLineName())){
                            ts.setTestItem(ts.getCustomerTestLineName());
                        }
                    }
                    if(UserInfoDefaultLanguageCodeEnums.check(defaultLanguageCode,UserInfoDefaultLanguageCodeEnums.zh_cn)){
                        if(Func.isNotEmpty(ts.getCustomerTestLineNameCN())){
                            ts.setTestItem(ts.getCustomerTestLineNameCN());
                        }
                    }

                }
            });
        }
        return PageInfo.of(testLinePageListDTOS);
    }

    public CustomResult getTestLineToLabAndLabSectionCode(OrderTestLineReq req){
        CustomResult result = new CustomResult();
        /*传参：BUCode 、LabSection 、ToLab(LabSection和tolab二选一)
		2．LabSection/ToLab 要求其中一个必须有值
		3．BU+LabSection/ToLab 匹配不到数据时，用BU+ LabSection/ToLab(All)匹配，都匹配不到返回空  优先使用toLab获取
		4. 如果ServiceItem对应多个TestLine+Standard， 再通过Testline+Standard对应Labsection, 如果LabSection取到多个，则不取默认值*/
        String orderNo = req.getOrderNo();
        List<GpnToLabAndLabSectionDTO> toLabAndLabSectionCodeList = testLineInstanceExtMapper.getToLabAndLabSectionCode(orderNo);
        if(Func.isNotEmpty(toLabAndLabSectionCodeList)){
            List<String> testLineInstanceIdList = toLabAndLabSectionCodeList.stream().map(GpnToLabAndLabSectionDTO::getTestLineInstanceId).collect(Collectors.toList());
            List<TestLineLabSectionRelDTO> testLineLabSectionRelDTOS = testLineMapper.queryTestLineLabSectionRel(testLineInstanceIdList);
            if(Func.isNotEmpty(testLineLabSectionRelDTOS)){
                List<Long> tlabSectionBaseIdList = testLineLabSectionRelDTOS.stream().map(TestLineLabSectionRelDTO::getLabSectionBaseId).filter(Func::isNotEmpty).distinct().collect(Collectors.toList());
                List<GetLabSectionBaseInfoRsp> labSectionBaseInfoList = labSectionClient.getLabSectionBaseInfo(tlabSectionBaseIdList);
                if(Func.isNotEmpty(labSectionBaseInfoList)){
                    for (GpnToLabAndLabSectionDTO gpnToLabAndLabSectionDTO : toLabAndLabSectionCodeList) {
                        List<TestLineLabSectionRelDTO> testLineLabSectionRelDTOList = testLineLabSectionRelDTOS.stream().filter(item -> Func.equalsSafe(item.getTestLineInstanceId(), gpnToLabAndLabSectionDTO.getTestLineInstanceId())).collect(Collectors.toList());
                        if(Func.isNotEmpty(testLineLabSectionRelDTOList)){
                            List<String> labSectionBaseIdList = testLineLabSectionRelDTOList.stream().map(TestLineLabSectionRelDTO::getLabSectionBaseId).map(Func::toStr).collect(Collectors.toList());
                            List<GetLabSectionBaseInfoRsp> labSectionBaseInfoRsps = labSectionBaseInfoList.stream().filter(item -> Func.isNotEmpty(labSectionBaseIdList) && labSectionBaseIdList.contains(Func.toStr(item.getLabSectionBaseId()))).collect(Collectors.toList());
                                if(Func.isNotEmpty(labSectionBaseInfoRsps)){
                                    List<String> labSectionCodeList = labSectionBaseInfoRsps.stream().map(GetLabSectionBaseInfoRsp::getLabSectionCode).filter(Func::isNotEmpty).sorted().collect(Collectors.toList());
                                    gpnToLabAndLabSectionDTO.setLabSectionCodeList(labSectionCodeList);
                                }
                        }
                    }
                }

            }
        }
        if(Func.isNotEmpty(toLabAndLabSectionCodeList)){
            toLabAndLabSectionCodeList = toLabAndLabSectionCodeList.stream().collect(collectingAndThen(toCollection(() -> new TreeSet<>(Comparator.comparing(item->String.format("%s-%s-%s",item.getTestLineVersionID(),item.getCitationId(),Func.isEmpty(item.getLabSectionCodeList())?"":CollectionUtils.join(item.getLabSectionCodeList(),","))))), ArrayList::new));
        }
        result.setData(toLabAndLabSectionCodeList);
        return result;
    }

    public CustomResult getTestLineListForQuotation(OrderReq reqObject) {
        CustomResult result = new CustomResult();
        logger.info("===getTestLineListForQuotation:"+JSON.toJSONString(reqObject));
        if(StringUtils.isEmpty(reqObject.getOrderNo())){
            logger.info("OrderNo is null");
            return result;
        }
        GeneralOrderInstanceInfoPO objGeneralOrderInstanceInfoPO = orderMapper.getOrderInfoByOrderNo(reqObject.getOrderNo());
        if (objGeneralOrderInstanceInfoPO == null){
            logger.info("OrderNo is not foud");
            return result;
        }
        PPTestLineRelationshipInfoExample objPPTestLineRelationshipInfoExample=new PPTestLineRelationshipInfoExample();
        objPPTestLineRelationshipInfoExample.createCriteria().andGeneralOrderInstanceIDEqualTo(objGeneralOrderInstanceInfoPO.getID());
        List<PPTestLineRelationshipInfoPO> ppTestLineRelationshipInfoPOS=ppTestLineRelationshipInfoMapper.selectByExample(objPPTestLineRelationshipInfoExample);
        if(Func.isEmpty(ppTestLineRelationshipInfoPOS)){
            result.setSuccess(true);
            return result;
        }
        List<TestLineStatusDTO> testLineStatusDTOS=testLineMapper.getTestLineStatusByOrderNo(reqObject.getOrderNo());
        List<TestLineForQuotationDTO> testLineForQuotationDTOS=Lists.newArrayList();
        for(PPTestLineRelationshipInfoPO objPPTestLineRelationshipInfoPO:ppTestLineRelationshipInfoPOS){
            TestLineStatusDTO testLineStatusDTO=testLineStatusDTOS.stream().filter(e->Func.equals(e.getTestlineInstanceID(),objPPTestLineRelationshipInfoPO.getTestLineInstanceID())).findFirst().orElse(null);
            if(testLineStatusDTO!=null){
                TestLineForQuotationDTO objTestLineForQuotationDTO=new TestLineForQuotationDTO();
                objTestLineForQuotationDTO.setTestLineID(testLineStatusDTO.getTestLineID());
                objTestLineForQuotationDTO.setTestLineStatus(testLineStatusDTO.getTestLineStatus());
                objTestLineForQuotationDTO.setQuotationTestlineInstanceId(objPPTestLineRelationshipInfoPO.getQuotationTestlineInstanceID());
                testLineForQuotationDTOS.add(objTestLineForQuotationDTO);
            }
        }
        result.setSuccess(true);
        result.setData(testLineForQuotationDTOS);
        return result;
    }

    public CustomResult getStarlimsTestLine(GetSubContractTestLineReq getSubContractTestLineReq){
        CustomResult rspResult = new CustomResult();

        //非HL\AFL的不做toStarlims
        if(!"HL".equalsIgnoreCase(ProductLineContextHolder.getProductLineCode())&&!"AFL".equalsIgnoreCase(ProductLineContextHolder.getProductLineCode())){
            rspResult.setSuccess(false);
            rspResult.setMsg("当前分包单暂不支持ToStarlims操作，请联系支持团队");
            rspResult.setData(Lists.newArrayList());
            return rspResult;
        }

        SubContractTestLineMappingExample example = new SubContractTestLineMappingExample();
        example.createCriteria().andSubContractIDEqualTo(getSubContractTestLineReq.getSubContractId());
        List<SubContractTestLineMappingPO> subContractTestLineMappingPOS = subContractTestLineMappingMapper.selectByExample(example);
        if(Func.isEmpty(subContractTestLineMappingPOS)){
            rspResult.setSuccess(false);
            rspResult.setMsg("Not found testlineInstance mapping");
            rspResult.setData(Lists.newArrayList());
            return rspResult;
        }
        com.sgs.otsnotes.facade.model.req.matrix.MatrixTableListReq objMatrixTableListReq=new com.sgs.otsnotes.facade.model.req.matrix.MatrixTableListReq();
        objMatrixTableListReq.setPage(1);
        objMatrixTableListReq.setRows(100);
        objMatrixTableListReq.setOrderNo(getSubContractTestLineReq.getOrderNo());
        objMatrixTableListReq.setTestLineInstanceIds(subContractTestLineMappingPOS.stream().map(e->e.getTestLineInstanceID()).collect(Collectors.toList()));
        PageInfo<MatrixTableListRsp> objMatrixTableListResp=testLineQueryService.queryPPTestLineList(objMatrixTableListReq);
        if(Func.isEmpty(objMatrixTableListResp)||Func.isEmpty(objMatrixTableListResp.getList())){
            rspResult.setSuccess(false);
            rspResult.setMsg("Not found testlineInstance");
            rspResult.setData(Lists.newArrayList());
            return rspResult;
        }
        OrderIdRequest objOrderIdRequest=new OrderIdRequest();
        objOrderIdRequest.setProductLineCode(ProductLineContextHolder.getProductLineCode());
        objOrderIdRequest.setSystemId(15);
        objOrderIdRequest.setOrderNo(getSubContractTestLineReq.getOrderNo());
        BaseResponse<List<FcmSimpleTestLine>> quotationBaseResp=quotationFacade.getFcmTestlineMatrix(objOrderIdRequest);
        if(quotationBaseResp==null||quotationBaseResp.getStatus()!=200){
            rspResult.setSuccess(false);
            rspResult.setMsg("Call quotationFacade.getFcmTestlineMatrix is failed");
            rspResult.setData(Lists.newArrayList());
            return rspResult;
        }


        List<FcmSimpleTestLine> fcmSimpleTestLineList=quotationBaseResp.getData();
        List<MatrixTableListRsp> matrixTableListRspList=objMatrixTableListResp.getList();


        List<ToStarlimsTableListRsp> toStarlimsTableListRspList=Lists.newArrayList();
        PPTestLineRelationshipInfoExample ppTestLineRelationshipInfoExample = new PPTestLineRelationshipInfoExample();
        ppTestLineRelationshipInfoExample.createCriteria().andTestLineInstanceIDIn(matrixTableListRspList.stream().map(e->e.getTestLineInstanceId()).collect(Collectors.toList()));
        List<PPTestLineRelationshipInfoPO> ppTestLineRelationshipInfoPOS = ppTestLineRelationshipInfoMapper.selectByExample(ppTestLineRelationshipInfoExample);
        for(MatrixTableListRsp objMatrixTableListRsp:matrixTableListRspList){
            ToStarlimsTableListRsp objToStarlimsTableListRsp=new ToStarlimsTableListRsp();
            BeanUtils.copyProperties(objMatrixTableListRsp,objToStarlimsTableListRsp);
            //处理af
            if(Func.isNotEmpty(fcmSimpleTestLineList)){
                PPTestLineRelationshipInfoPO objPPTestLineRelationshipInfoPO=ppTestLineRelationshipInfoPOS.stream().filter(e->e.getTestLineInstanceID().equals(objMatrixTableListRsp.getTestLineInstanceId())).findFirst().orElse(null);
                FcmSimpleTestLine objFcmSimpleTestLine=fcmSimpleTestLineList.stream().filter(e->getTestLineKey(e).equals(getTestLineKey(objMatrixTableListRsp,objPPTestLineRelationshipInfoPO.getAid()))).findFirst().orElse(null);
                if(objFcmSimpleTestLine!=null){
                    if(Func.isNotEmpty(objFcmSimpleTestLine.getAfFullName())){
                        objToStarlimsTableListRsp.setAfFullNameList(objFcmSimpleTestLine.getAfFullName());
                    }
                    if(Func.isNotEmpty(objFcmSimpleTestLine.getAnalyteNameList())){
                        objToStarlimsTableListRsp.setAnalyteText(objFcmSimpleTestLine.getAnalyteNameList());
                    }
                }
            }
            toStarlimsTableListRspList.add(objToStarlimsTableListRsp);
        }

        rspResult.setSuccess(true);
        rspResult.setData(toStarlimsTableListRspList);
        return rspResult;
    }

    private  String objectToStr(Object o){
        if(o==null){
            return "";
        }
        return o.toString();
    }

    private  String getTestLineKey(FcmSimpleTestLine objFcmSimpleTestLine){
        String keyss=objectToStr(objFcmSimpleTestLine.getTestLineVersionId())+
                objectToStr(objFcmSimpleTestLine.getStandardVersionId())+
                objectToStr(objFcmSimpleTestLine.getAid());
        return keyss;
    }

    private  String getTestLineKey(MatrixTableListRsp objFcmSimpleTestLine,Long aid){
        String keyss=objectToStr(objFcmSimpleTestLine.getTestLineVersionId())+
                objectToStr(objFcmSimpleTestLine.getCitationVersionId())+
                objectToStr(aid);
        return keyss;
    }

    //如果分包出去的只有一个TL,删除该TL时，对应取消接包方的Job,subContract
    //根据分包单ID查询接包方的Job及SubContract信息
    public int updateSubcontractJobStatus(String subcontractId,List<String> delPpTestLineRelIds,String userName){
        int result = 0;
        List<ExecTestLineStatusRsp> execTestLineStatusRspList = subContractExtMapper.checkSubcontractTestLine(subcontractId);
        if(Func.isNotEmpty(execTestLineStatusRspList) && execTestLineStatusRspList.size()==1 && delPpTestLineRelIds.contains(execTestLineStatusRspList.get(0).getId())){
            ExecTestLineStatusRsp execTestLineStatusRsp = execTestLineStatusRspList.get(0);
            //取消接包方的subContract
            if(Func.isNotEmpty(execTestLineStatusRsp.getExcSubContractNo()) && SubContractStatus.check(execTestLineStatusRsp.getExcSubContractStatus(),SubContractStatus.NEW,SubContractStatus.TESTING,SubContractStatus.PENDING)){
                SubContractPO subContractPO = new SubContractPO();
                subContractPO.setStatus(SubContractStatusEnum.Cancelled.getStatus());
                subContractPO.setModifiedBy(userName);
                subContractPO.setModifiedDate(new Date());
                SubContractExample subContractExample = new SubContractExample();
                subContractExample.createCriteria().andSubContractNoEqualTo(execTestLineStatusRsp.getExcSubContractNo());
                result = subContractMapper.updateByExampleSelective(subContractPO,subContractExample);
            }
            //取消接包方的job
            if(Func.isNotEmpty(execTestLineStatusRsp.getExcJobNo()) && JobStatus.check(execTestLineStatusRsp.getExcJobStatus(),JobStatus.New,JobStatus.Testing)){
                JobInfoPO jobInfoPO = new JobInfoPO();
                jobInfoPO.setJobStatus(JobStatus.Cancelled.getStatus());
                jobInfoPO.setModifiedBy(userName);
                jobInfoPO.setModifiedDate(new Date());
                JobInfoExample jobInfoExample = new JobInfoExample();
                jobInfoExample.createCriteria().andJobNoEqualTo(execTestLineStatusRsp.getExcJobNo());
                result = jobInfoMapper.updateByExampleSelective(jobInfoPO,jobInfoExample);
            }
        }
        return result;
    }

    public List<TestLineInstancePO> getTestLineListByIds(List<String> testLineIds){
        List<TestLineInstancePO> testLineInstanceList = Lists.newArrayList();
        if(Func.isEmpty(testLineIds)){
            return testLineInstanceList;
        }
        TestLineInstanceExample example = new TestLineInstanceExample();
        example.createCriteria().andIDIn(testLineIds);
        return testLineInstanceMapper.selectByExample(example);
    }

    /**
     * 批量更新TL Type
     * @return
     */
    public int updateTestLineTypeBatch(List<String> testLineIds,TestLineType testLineType){
        if(Func.isEmpty(testLineIds)){
            return 0;
        }
        return  testLineInstanceExtMapper.updateTestLineTypeBatch(testLineIds, testLineType.getType());
    }

    public BaseResponse dealEmptyPpTestLineSeq(GeneralPPTestLineSeqReq generalPPTestLineSeqReq){
        if(Func.isEmpty(generalPPTestLineSeqReq) || Func.isEmpty(generalPPTestLineSeqReq.getOrderNo())){
            return BaseResponse.newSuccessInstance("OrderNo is required");
        }
        List<PPTestLineRelationshipInfoPO> ppTestLineRelList = ppTestLineRelMapper.getPPTestLineRelList(generalPPTestLineSeqReq.getOrderNo());
        this.dealTLRelSeq(ppTestLineRelList);
        return BaseResponse.newSuccessInstance(true);
    }

    private void dealTLRelSeq(List<PPTestLineRelationshipInfoPO> ppTestLineRelList){
        if(Func.isNotEmpty(ppTestLineRelList)){
            List<PPTestLineRelationshipInfoPO> seqEmptyRelList = ppTestLineRelList.stream().filter(i -> Func.isEmpty(i.getSeq())).collect(Collectors.toList());
            if(Func.isNotEmpty(seqEmptyRelList)){
                seqEmptyRelList = seqEmptyRelList.stream().sorted(Comparator.comparing(PPTestLineRelationshipInfoPO::getCreatedDate,Comparator.nullsFirst(Comparator.naturalOrder()))).collect(Collectors.toList());
                // 根据PPBaseId分组，再取出每个分组下Seq的最大值
                Map<Long,PPTestLineRelationshipInfoPO> ppBaseTestLineMaxSeqMap =
                        ppTestLineRelList.parallelStream().filter(i->Func.isNotEmpty(i.getSeq())).collect(
                                Collectors.groupingBy(i->Func.isEmpty(i.getPpBaseId())?0:i.getPpBaseId(),
                                        collectingAndThen(
                                                Collectors.reducing((a,b)->a.getSeq()> b.getSeq()?a:b),Optional::get)
                                )
                        );
                Map<Long,Long> maxSeqMap = new HashMap<>();
                for (Map.Entry<Long, PPTestLineRelationshipInfoPO> entry: ppBaseTestLineMaxSeqMap.entrySet()) {
                    maxSeqMap.put(entry.getKey(), entry.getValue().getSeq());
                }
                // 为Seq为空的数据设置值
                for (int i = 0; i < seqEmptyRelList.size(); i++) {
                    PPTestLineRelationshipInfoPO ppTestLineRelationshipInfoPO = seqEmptyRelList.get(i);
                    String ppSeq = null;
                    String prefixSeq = null;
                    Long maxSeq = 0L;
                    Long ppBaseId = ppTestLineRelationshipInfoPO.getPpBaseId();
                    String wholeSeq = "";
                    Map<Long, List<PPTestLineRelationshipInfoPO>> ppBaseTestLineMap = ppTestLineRelList.stream().collect(Collectors.groupingBy(PPTestLineRelationshipInfoPO::getPpBaseId));
                    List<PPTestLineRelationshipInfoPO> ppTestLineRelationshipInfoPOS = ppBaseTestLineMap.get(ppBaseId);
                    if(maxSeqMap.containsKey(ppBaseId) && Func.isNotEmpty(maxSeqMap.get(ppBaseId))){
                        wholeSeq = Func.toStr(maxSeqMap.get(ppBaseId)+1);
                        maxSeq = Func.toLong(wholeSeq.substring(5, wholeSeq.length()));
                    }else{
                        if (NumberUtil.equals(0L, ppBaseId, true)) {
                            // TL
                            prefixSeq = "90";
                            ppSeq = String.format("%03d", 0);
                        } else {
                            // PP
                            prefixSeq = "10";
                            if (maxSeqMap.containsKey(0L)) {
                                ppSeq = String.format("%03d", maxSeqMap.size());
                            } else {
                                ppSeq = String.format("%03d", maxSeqMap.size() + 1);
                            }
                        }
                        String testLineSeq = String.format("%04d", 1);
                        maxSeq = Func.toLong(testLineSeq);
                        wholeSeq = prefixSeq + ppSeq + testLineSeq;
                    }
                    maxSeqMap.put(ppBaseId,Func.toLong(wholeSeq));
                    PPTestLineRelationshipInfoPO updateInfo = new PPTestLineRelationshipInfoPO();
                    updateInfo.setID(ppTestLineRelationshipInfoPO.getID());
                    updateInfo.setSeq(Func.toLong(wholeSeq));
                    ppTestLineRelationshipInfoMapper.updateByPrimaryKeySelective(updateInfo);
                }
//                logger.info(JSON.toJSONString(seqEmptyRelList));
            }
        }
    }

    /**
     * 刷数程序
     */
    public void dealAllEmptyTlRelSeq(){
        PPTestLineRelationshipInfoExample relationshipInfoExample = new PPTestLineRelationshipInfoExample();
        relationshipInfoExample.createCriteria().andSeqIsNull();
        List<PPTestLineRelationshipInfoPO> ppTestLineRelationshipInfoPOS = ppTestLineRelationshipInfoMapper.selectByExample(relationshipInfoExample);
        //再查询存在Seq为空的相关的所有的ppTlRel
        if(Func.isNotEmpty(ppTestLineRelationshipInfoPOS)){
            List<String> orderIds = ppTestLineRelationshipInfoPOS.stream().map(PPTestLineRelationshipInfoPO::getGeneralOrderInstanceID).filter(Func::isNotEmpty).distinct().collect(Collectors.toList());
            PPTestLineRelationshipInfoExample relationshipInfoExample2 = new PPTestLineRelationshipInfoExample();
            relationshipInfoExample2.createCriteria().andGeneralOrderInstanceIDIn(orderIds);
            List<PPTestLineRelationshipInfoPO> allPpTestLineRelationshipInfoPOS = ppTestLineRelationshipInfoMapper.selectByExample(relationshipInfoExample2);
            Map<String,List<PPTestLineRelationshipInfoPO>> map = allPpTestLineRelationshipInfoPOS.stream().collect(Collectors.groupingBy(b -> b.getGeneralOrderInstanceID()));
            for (Map.Entry<String, List<PPTestLineRelationshipInfoPO>> entry: map.entrySet()) {
                this.dealTLRelSeq(entry.getValue());
            }
        }
    }

    /**
     * DataEntry添加进来的SubPp和虚拟TL判定为同一种
     * @param testLineType
     * @return
     */
    public Integer buildTestLineTypeKey(Integer testLineType){
        if(TestLineType.check(testLineType,TestLineType.SUB_PP) || TestLineType.check(testLineType,TestLineType.VIRTAUL_TL)){
            return -1;
        }else{
            return Func.toInt(testLineType,TestLineType.None.getType());
        }
    }

    public CustomResult batchCancelTestLine(TestLineBatchCancelReq reqObj){
        CustomResult rspResult =new CustomResult();
        if(Func.isEmpty(reqObj) || Func.isEmpty(reqObj.getTestLineInstanceIdList())){
            rspResult.setSuccess(false);
            rspResult.setMsg("param.miss");
            return rspResult;
        }
        List<Map> maps = Lists.newArrayList();
        StopWatch totalWatch = new StopWatch();
        totalWatch.start();
        String productLineCode = ProductLineContextHolder.getProductLineCode();
        String sgsToken = SecurityContextHolder.getSgsToken();
        UserInfo user = SecurityContextHolder.getUserInfoFillSystem();
        AsyncCall asyncCall = new AsyncCall();

        for (String testLineInstanceId : reqObj.getTestLineInstanceIdList()) {
            TestLineCancelReq testLineCancelReq = new TestLineCancelReq();
            testLineCancelReq.setProductLineCode(productLineCode);
            testLineCancelReq.setToken(sgsToken);
            testLineCancelReq.setTestLineInstanceId(testLineInstanceId);
            testLineCancelReq.setOperateBy(reqObj.getOperateBy());
            testLineCancelReq.setUser(user);
            asyncCall.put(testLineInstanceId, () -> this.cancelTestLine(testLineCancelReq));
        }

        List<AsyncResult> asyncResults = AsyncUtils.awaitResult(asyncCall);
        for (AsyncResult asyncResult : asyncResults) {
            String asyncType = asyncResult.getTaskKey();
            Map objMap = new HashMap();
            objMap.put("testLineInstanceId", asyncType);
            CustomResult objBaseResponse = asyncResult.getData();
            if(Func.isEmpty(objBaseResponse)){
                objMap.put("status", 500);
                objMap.put("message", "系统异常");
            }
            else if (!objBaseResponse.isSuccess()) {
                objMap.put("status", 500);
                objMap.put("message", objBaseResponse.getMsg());
            } else {
                objMap.put("message", "Success");
                objMap.put("status", 200);
            }
            maps.add(objMap);
        }
        totalWatch.stop();
        logger.info("batch cancel TL 耗时:{}", totalWatch.getTime());
        rspResult.setSuccess(true);
        rspResult.setData(maps);
        return rspResult;
    }
}