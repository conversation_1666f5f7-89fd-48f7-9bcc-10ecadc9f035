package com.sgs.otsnotes.domain.service;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.sgs.core.domain.UserInfo;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.core.base.CustomResult;
import com.sgs.framework.core.base.ResponseCode;
import com.sgs.framework.core.context.SystemContextHolder;
import com.sgs.framework.core.exception.BizException;
import com.sgs.framework.core.util.IdUtil;
import com.sgs.framework.i18n.util.MessageUtil;
import com.sgs.framework.log.SystemLogHelper;
import com.sgs.framework.log.model.SystemLog;
import com.sgs.framework.model.enums.*;
import com.sgs.framework.model.enums.ProductLineType;
import com.sgs.framework.model.enums.SampleType;
import com.sgs.framework.model.enums.TestLineStatus;
import com.sgs.framework.model.enums.TestLineType;
import com.sgs.framework.model.trims.labsection.LabSectionBO;
import com.sgs.framework.mybatis.config.DatabaseContextHolder;
import com.sgs.framework.mybatis.enums.DatabaseTypeEnum;
import com.sgs.framework.security.context.SecurityContextHolder;
import com.sgs.framework.security.utils.SecurityUtil;
import com.sgs.framework.tool.utils.Func;
import com.sgs.framework.tool.utils.ProductLineContextHolder;
import com.sgs.framework.tool.utils.StringPool;
import com.sgs.gpo.facade.model.otsnotes.testdata.req.TLDataHeaderSaveReq;
import com.sgs.gpo.facade.model.otsnotes.testdata.rsp.TLDataHeaderSaveRsp;
import com.sgs.gpo.facade.model.report.req.ReportExtForTLUpdateReq;
import com.sgs.gpo.facade.temp.ReportTempFacade;
import com.sgs.grus.bizlog.BizLogClient;
import com.sgs.grus.bizlog.info.BizLogInfo;
import com.sgs.otsnotes.core.annotation.AccessPolicyRule;
import com.sgs.otsnotes.core.annotation.NotVerifyToken;
import com.sgs.otsnotes.core.common.UserHelper;
import com.sgs.otsnotes.core.constants.BizLogConstant;
import com.sgs.otsnotes.core.constants.Constants;
import com.sgs.otsnotes.core.enums.ReportFlagEnums;
import com.sgs.otsnotes.core.enums.ShareType;
import com.sgs.otsnotes.core.util.DateUtils;
import com.sgs.otsnotes.core.util.ExceptionUtil;
import com.sgs.otsnotes.core.util.NumberUtil;
import com.sgs.otsnotes.core.util.StringUtil;
import com.sgs.otsnotes.dbstorages.mybatis.extmapper.AnalyteLanguageMapper;
import com.sgs.otsnotes.dbstorages.mybatis.extmapper.AnalyteMapper;
import com.sgs.otsnotes.dbstorages.mybatis.extmapper.ConclusionMapper;
import com.sgs.otsnotes.dbstorages.mybatis.extmapper.LimitGroupMapper;
import com.sgs.otsnotes.dbstorages.mybatis.extmapper.LimitMapper;
import com.sgs.otsnotes.dbstorages.mybatis.extmapper.OrderLanguageRelMapper;
import com.sgs.otsnotes.dbstorages.mybatis.extmapper.OrderMapper;
import com.sgs.otsnotes.dbstorages.mybatis.extmapper.PPBaseMapper;
import com.sgs.otsnotes.dbstorages.mybatis.extmapper.PPSampleRelMapper;
import com.sgs.otsnotes.dbstorages.mybatis.extmapper.PPTestLineRelMapper;
import com.sgs.otsnotes.dbstorages.mybatis.extmapper.ProductAttrMapper;
import com.sgs.otsnotes.dbstorages.mybatis.extmapper.ReportMapper;
import com.sgs.otsnotes.dbstorages.mybatis.extmapper.ReportMatrixRelMapper;
import com.sgs.otsnotes.dbstorages.mybatis.extmapper.SlimSubContractExtMapper;
import com.sgs.otsnotes.dbstorages.mybatis.extmapper.SubContractExtMapper;
import com.sgs.otsnotes.dbstorages.mybatis.extmapper.TestConditionGroupLanguageMapper;
import com.sgs.otsnotes.dbstorages.mybatis.extmapper.TestConditionGroupMapper;
import com.sgs.otsnotes.dbstorages.mybatis.extmapper.TestConditionMapper;
import com.sgs.otsnotes.dbstorages.mybatis.extmapper.TestDataMapper;
import com.sgs.otsnotes.dbstorages.mybatis.extmapper.TestLineMapper;
import com.sgs.otsnotes.dbstorages.mybatis.extmapper.TestMatrixMapper;
import com.sgs.otsnotes.dbstorages.mybatis.extmapper.TestPositionMapper;
import com.sgs.otsnotes.dbstorages.mybatis.extmapper.TestSampleExtMapper;
import com.sgs.otsnotes.dbstorages.mybatis.extmapper.TestSampleGroupExtMapper;
import com.sgs.otsnotes.dbstorages.mybatis.extmapper.TestSampleLangMapper;
import com.sgs.otsnotes.dbstorages.mybatis.extmapper.TestSampleMapper;
import com.sgs.otsnotes.dbstorages.mybatis.extmapper.TrimsPPTestLineRelMapper;
import com.sgs.otsnotes.dbstorages.mybatis.extmapper.testmatrix.TestMatrixExtMapper;
import com.sgs.otsnotes.dbstorages.mybatis.extmodel.AssignSampleInfo;
import com.sgs.otsnotes.dbstorages.mybatis.extmodel.matrix.SyncAzureTestMatrixInfo;
import com.sgs.otsnotes.dbstorages.mybatis.mapper.AnalyteInfoMapper;
import com.sgs.otsnotes.dbstorages.mybatis.mapper.LimitGroupInstanceMapper;
import com.sgs.otsnotes.dbstorages.mybatis.mapper.LimitInstanceMapper;
import com.sgs.otsnotes.dbstorages.mybatis.mapper.ObjectReferInfoMapper;
import com.sgs.otsnotes.dbstorages.mybatis.mapper.PPSampleRelationshipInfoMapper;
import com.sgs.otsnotes.dbstorages.mybatis.mapper.PPTestLineRelationshipInfoMapper;
import com.sgs.otsnotes.dbstorages.mybatis.mapper.ProductAttributeInstanceInfoMapper;
import com.sgs.otsnotes.dbstorages.mybatis.mapper.ReportInfoMapper;
import com.sgs.otsnotes.dbstorages.mybatis.mapper.StatusInfoMapper;
import com.sgs.otsnotes.dbstorages.mybatis.mapper.SubContractTestLineMappingMapper;
import com.sgs.otsnotes.dbstorages.mybatis.mapper.TestMatrixInfoMapper;
import com.sgs.otsnotes.dbstorages.mybatis.mapper.TestSampleGroupInfoMapper;
import com.sgs.otsnotes.dbstorages.mybatis.mapper.TestSampleInfoMapper;
import com.sgs.otsnotes.dbstorages.mybatis.model.AnalyteInfoExample;
import com.sgs.otsnotes.dbstorages.mybatis.model.AnalyteInfoPO;
import com.sgs.otsnotes.dbstorages.mybatis.model.AnalyteMultipleLanguageInfoPO;
import com.sgs.otsnotes.dbstorages.mybatis.model.GeneralOrderInstanceInfoPO;
import com.sgs.otsnotes.dbstorages.mybatis.model.LimitGroupInstanceExample;
import com.sgs.otsnotes.dbstorages.mybatis.model.LimitGroupInstancePO;
import com.sgs.otsnotes.dbstorages.mybatis.model.LimitInstanceExample;
import com.sgs.otsnotes.dbstorages.mybatis.model.LimitInstancePO;
import com.sgs.otsnotes.dbstorages.mybatis.model.ObjectReferInfoExample;
import com.sgs.otsnotes.dbstorages.mybatis.model.ObjectReferInfoPO;
import com.sgs.otsnotes.dbstorages.mybatis.model.OrderLanguageRelInfoPO;
import com.sgs.otsnotes.dbstorages.mybatis.model.PPArtifactRelInfoWithBLOBs;
import com.sgs.otsnotes.dbstorages.mybatis.model.PPSampleRelationshipInfoExample;
import com.sgs.otsnotes.dbstorages.mybatis.model.PPSampleRelationshipInfoPO;
import com.sgs.otsnotes.dbstorages.mybatis.model.PPTestLineRelationshipInfoExample;
import com.sgs.otsnotes.dbstorages.mybatis.model.PPTestLineRelationshipInfoPO;
import com.sgs.otsnotes.dbstorages.mybatis.model.ProductAttributeInstanceInfoExample;
import com.sgs.otsnotes.dbstorages.mybatis.model.ReportInfoExample;
import com.sgs.otsnotes.dbstorages.mybatis.model.ReportInfoPO;
import com.sgs.otsnotes.dbstorages.mybatis.model.ReportMatrixRelationShipInfoPO;
import com.sgs.otsnotes.dbstorages.mybatis.model.SlimSubcontractPO;
import com.sgs.otsnotes.dbstorages.mybatis.model.StatusInfoExample;
import com.sgs.otsnotes.dbstorages.mybatis.model.StatusInfoPO;
import com.sgs.otsnotes.dbstorages.mybatis.model.SubContractTestLineMappingExample;
import com.sgs.otsnotes.dbstorages.mybatis.model.SubContractTestLineMappingPO;
import com.sgs.otsnotes.dbstorages.mybatis.model.TestConditionInfoPO;
import com.sgs.otsnotes.dbstorages.mybatis.model.TestDataInfoPO;
import com.sgs.otsnotes.dbstorages.mybatis.model.TestLineInstancePO;
import com.sgs.otsnotes.dbstorages.mybatis.model.TestMatrixExtPO;
import com.sgs.otsnotes.dbstorages.mybatis.model.TestMatrixInfoExample;
import com.sgs.otsnotes.dbstorages.mybatis.model.TestMatrixInfoPO;
import com.sgs.otsnotes.dbstorages.mybatis.model.TestMatrixPO;
import com.sgs.otsnotes.dbstorages.mybatis.model.TestSampleGroupInfoExample;
import com.sgs.otsnotes.dbstorages.mybatis.model.TestSampleGroupInfoPO;
import com.sgs.otsnotes.dbstorages.mybatis.model.TestSampleInfoExample;
import com.sgs.otsnotes.dbstorages.mybatis.model.TestSampleInfoPO;
import com.sgs.otsnotes.dbstorages.mybatis.model.TestSampleLangInfoPO;
import com.sgs.otsnotes.dbstorages.mybatis.model.TestSampleOrderPO;
import com.sgs.otsnotes.domain.service.testline.ITestLineStatusService;
import com.sgs.otsnotes.domain.util.CitationUtil;
import com.sgs.otsnotes.facade.model.comparator.TestSampleComparator;
import com.sgs.otsnotes.facade.model.dto.OrderLanguageDTO;
import com.sgs.otsnotes.facade.model.dto.OrderTestLineListDTO;
import com.sgs.otsnotes.facade.model.dto.ReportMatrixDTO;
import com.sgs.otsnotes.facade.model.dto.sample.TestSampleMatrixDTO;
import com.sgs.otsnotes.facade.model.dto.tracking.TrackOrderStartDateDTO;
import com.sgs.otsnotes.facade.model.dto.tracking.TrackingOrderDTO;
import com.sgs.otsnotes.facade.model.dto.tracking.TrackingStockListDTO;
import com.sgs.otsnotes.facade.model.enums.*;
import com.sgs.otsnotes.facade.model.enums.ReportStatus;
import com.sgs.otsnotes.facade.model.gpn.testline.req.TLInstanceDetailReq;
import com.sgs.otsnotes.facade.model.info.PPSampleRelInfo;
import com.sgs.otsnotes.facade.model.info.PPTestLineRelInfo;
import com.sgs.otsnotes.facade.model.info.TestSampleInfo;
import com.sgs.otsnotes.facade.model.info.matrix.MatrixStatusInfo;
import com.sgs.otsnotes.facade.model.info.sample.TestSampleDTO;
import com.sgs.otsnotes.facade.model.req.OrderSubContractReq;
import com.sgs.otsnotes.facade.model.req.OrderTestLineListSearchReq;
import com.sgs.otsnotes.facade.model.req.QuerySubContractReq;
import com.sgs.otsnotes.facade.model.req.SampleBreakDownReq;
import com.sgs.otsnotes.facade.model.req.SampleGroupReq;
import com.sgs.otsnotes.facade.model.req.SampleReq;
import com.sgs.otsnotes.facade.model.req.SampleStartRequest;
import com.sgs.otsnotes.facade.model.req.ShareTestLineReq;
import com.sgs.otsnotes.facade.model.req.SlimMappingReq;
import com.sgs.otsnotes.facade.model.req.TestMatrixReq;
import com.sgs.otsnotes.facade.model.req.TestSampleReq;
import com.sgs.otsnotes.facade.model.req.report.ReportMatrixQueryReq;
import com.sgs.otsnotes.facade.model.req.sample.AssignSampleCancelReq;
import com.sgs.otsnotes.facade.model.req.sample.AssignSampleReq;
import com.sgs.otsnotes.facade.model.req.sample.CheckAssignSampleReq;
import com.sgs.otsnotes.facade.model.req.sample.CopyTestLineGetSampleReq;
import com.sgs.otsnotes.facade.model.req.sample.SaveAssignSampleReq;
import com.sgs.otsnotes.facade.model.req.testLine.SaveAnalyteReq;
import com.sgs.otsnotes.facade.model.req.testLine.TestLineCancelReq;
import com.sgs.otsnotes.facade.model.req.testLine.TestLineStatusUpdateReq;
import com.sgs.otsnotes.facade.model.req.tracking.TrackingStockListReq;
import com.sgs.otsnotes.facade.model.rsp.OrderInfoForSlimRsp;
import com.sgs.otsnotes.facade.model.rsp.PPRelationShipRsp;
import com.sgs.otsnotes.facade.model.rsp.QuerySubContractRsp;
import com.sgs.otsnotes.facade.model.rsp.TestSampleRsp;
import com.sgs.otsnotes.facade.model.rsp.assignsample.AssignSampleRsp;
import com.sgs.otsnotes.facade.model.rsp.assignsample.SamplePPSRsp;
import com.sgs.otsnotes.facade.model.rsp.assignsample.SampleRsp;
import com.sgs.otsnotes.facade.model.rsp.sample.CopyTestSampleRsp;
import com.sgs.otsnotes.facade.model.rsp.testLine.AnalyteSelectRsp;
import com.sgs.otsnotes.facade.model.rsp.testLine.TestLineEditDetailRsp;
import com.sgs.otsnotes.facade.model.rsp.testLine.TestLineListReq;
import com.sgs.otsnotes.facade.model.rsp.tracking.TrackingStockListRsp;
import com.sgs.otsnotes.integration.FrameWorkClient;
import com.sgs.otsnotes.integration.HLPreOrderClient;
import com.sgs.otsnotes.integration.OrderClient;
import com.sgs.otsnotes.integration.TokenClient;
import com.sgs.otsnotes.integration.TrackingClient;
import com.sgs.preorder.facade.OrderFacade;
import com.sgs.preorder.facade.model.dto.order.OrderAllDTO;
import com.sgs.preorder.facade.model.dto.order.OrderInfoDto;
import com.sgs.preorder.facade.model.dto.order.ProductInstanceDTO;
import com.sgs.preorder.facade.model.enums.ActiveType;
import com.sgs.preorder.facade.model.enums.ContactsType;
import com.sgs.preorder.facade.model.enums.OperationType;
import com.sgs.preorder.facade.model.info.TestRequestContactsInfo;
import com.sgs.preorder.facade.model.info.TestRequestInfo;
import com.sgs.preorder.facade.model.info.externalOrder.GpoExternalOrderInfo;
import com.sgs.preorder.facade.model.req.OrderIdReq;
import com.sgs.preorder.facade.model.req.OrderNosReq;
import com.sgs.preorder.facade.model.req.externalOrder.GpoExternalOrderNoReq;
import com.sgs.preorder.facade.model.rsp.TestRequestRsp;
import com.sgs.trimslocal.facade.IAnalyteFacade;
import com.sgs.trimslocal.facade.model.analyte.req.QueryTestLineAnalyteReq;
import com.sgs.trimslocal.facade.model.analyte.req.TestLineAnalyteReq;
import com.sgs.trimslocal.facade.model.analyte.rsp.TestAnalyteLangReq;
import com.sgs.trimslocal.facade.model.analyte.rsp.TestLineAnalyteRsp;
import io.protostuff.ListAdapter;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.SortedMap;
import java.util.TreeSet;
import java.util.UUID;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class SampleService{
    private static final Logger logger = LoggerFactory.getLogger(SampleService.class);
    @Autowired
    private OrderMapper orderMapper;
    @Autowired
    private TestSampleExtMapper testSampleExtMapper;
    @Autowired
    private TestSampleGroupExtMapper testSampleGroupExtMapper;
    @Autowired
    private TestMatrixMapper testMatrixMapper;
    @Autowired
    private TestLineMapper testLineMapper;
    @Autowired
    private ReportMapper reportMapper;
    @Autowired
    private ReportInfoMapper reportInfoMapper;
    @Autowired
    private TestConditionMapper testConditionMapper;
    @Autowired
    private SubContractExtMapper subContractMapper;
    @Autowired
    private TransactionTemplate transactionTemplate;
    @Autowired
    private SlimSubContractExtMapper slimSubContractExtMapper;
    @Autowired
    private OrderClient orderClient;
    @Autowired
    private SubContractTestLineMappingMapper subContractTestLineMappingMapper;
    @Autowired
    private AnalyteInfoMapper analyteInfoMapper;
    @Autowired
    private PPSampleRelationshipInfoMapper ppSampleRelationshipInfoMapper;
    @Autowired
    private TrimsPPTestLineRelMapper trimsPPTestLineRelMapper;
    //这是本地的扩展mapper
    @Autowired
    private PPTestLineRelMapper ppTestLineRelMapper;
    @Autowired
    private PPSampleRelMapper ppSampleRelMapper;
    @Autowired
    private TestDataMapper testDataMapper;
    @Autowired
    private TestMatrixInfoMapper testMatrixInfoMapper;
    @Autowired
    private ConclusionMapper conclusionMapper;
    @Autowired
    private LimitGroupMapper limitGroupMapper;
    @Autowired
    private LimitMapper limitMapper;
    @Autowired
    private TestConditionGroupMapper testConditionGroupMapper;
    @Autowired
    private TestConditionGroupLanguageMapper testConditionGroupLanguageMapper;
    @Autowired
    private TestPositionMapper testPositionMapper;
    @Autowired
    private ProductAttrMapper productAttrMapper;
    @Autowired
    private ReportMatrixRelMapper reportMatrixRelMapper;
    @Autowired
    private TestLinePendingService testLinePendingService;
    @Autowired
    private TestMatrixService testMatrixService;
    @Autowired
    private TestSampleInfoMapper testSampleInfoMapper;
    @Autowired
    private TestSampleGroupInfoMapper testSampleGroupInfoMapper;
    @Autowired
    private TestSampleMapper testSampleMapper;
    @Autowired
    private TokenClient tokenClient;
    @Autowired
    private LimitGroupInstanceMapper limitGroupInstanceMapper;
    @Autowired
    private LimitInstanceMapper limitInstanceMapper;
    @Autowired
    private ProductAttributeInstanceInfoMapper productAttributeInstanceInfoMapper;
    @Autowired
    private OrderFacade gpoOrderFacade;
    @Autowired
    private  PPBaseMapper ppBaseMapper;
    @Autowired
    private TrackingClient trackingClient;
    @Autowired
    private RedissonClient redissonClient;
    @Autowired
    private OrderTestLineService orderTestLineService;
    @Autowired
    private HLPreOrderClient hlPreOrderClient;
    @Autowired
    private TestMatrixExtMapper testMatrixExtMapper;
    @Autowired
    private TestSampleLangMapper testSampleLangMapper;
    @Autowired
    private TestSampleLangService testSampleLangService;
    @Autowired
    private StatusInfoMapper statusInfoMapper;
    @Autowired
    private ObjectReferInfoMapper objectReferInfoMapper;
    @Autowired
    private BizLogClient bizLogClient;
    @Autowired
    private TestLineService testLineService;
    @Autowired
    private ITestLineStatusService testLineStatusServiceNew;
    @Autowired
    private ReportTempFacade reportTempFacade;
    @Autowired
    private PPTestLineRelationshipInfoMapper ppTestLineRelationshipInfoMapper;
    @Autowired
    private FrameWorkClient frameWorkClient;
    @Autowired
    private IAnalyteFacade analyteFacade;
    @Autowired
    private TestLineCmdService testLineCmdService;
    @Autowired
    private TestLineQueryService testLineQueryService;
    @Autowired
    private AnalyteMapper analyteMapper;
    @Autowired
    private AnalyteLanguageMapper analyteLanguageMapper;
    @Autowired
    private OrderLanguageRelMapper orderLanguageRelMapper;
    @Autowired
    private SystemLogHelper systemLogHelper;
    @Autowired
    private CitationUtil citationUtil;
    @Autowired
    private MessageUtil messageUtil;

    private final static String operationUserName ="BOM";

    /**
     * @Desc Bom 回传
     * @param reqObject
     * @return
     */
    public CustomResult updateBreakDown(SampleBreakDownReq reqObject) {
        // 记录日志
        SystemLog systemLog = new SystemLog();
        systemLog.setObjectType("Bom回传");
        systemLog.setObjectNo(reqObject.getOrderNo());
        systemLog.setProductLineCode(reqObject.getProductLineCode());
        systemLog.setType(SystemLogType.API.getType());
        systemLog.setRemark("Bom回传Request");
        systemLog.setOperationType("Bom回传");
        systemLog.setCreateBy(reqObject.getUserName());
        systemLog.setRequest(JSON.toJSONString(reqObject));
        systemLogHelper.save(systemLog);
        //logger.info("Update BreakDown Json :{}",JSON.toJSONString(reqObject));
        Date now = DateUtils.getNow();
        CustomResult rspReuslt = new CustomResult();
        String key = String.format("%s_%s_%s", this.getClass().getName(),"updateBreakDown",reqObject.getOrderNo());
        RLock lock = redissonClient.getLock(key);
        try{
            //拿不到锁，提示不能重复请求
            if(!lock.tryLock(1L,10L, TimeUnit.SECONDS)){
                return rspReuslt.fail(String.format("请稍后，当前%s的请求还未处理结束", reqObject.getOrderNo()));
            }
        }catch (Exception e){
            //如果产生异常，就不用拿着锁了，直接释放掉
            try{
                if(lock.isLocked()){
                    lock.unlock();
                }
            }catch (Exception ex){

            }
            return rspReuslt.fail(String.format("请稍后，当前%s的请求还未处理结束", reqObject.getOrderNo()));
        }
        String updateUser =  Func.isNotEmpty(reqObject.getUserName())?reqObject.getUserName():operationUserName;
        try{
            ProductLineContextHolder.setProductLineCode(reqObject.getProductLineCode());
            List<TestMatrixReq> matrixs = reqObject.getMatrixs();
            if (matrixs == null){
                matrixs = Lists.newArrayList();
            }
            Set<String> testLineIds = Sets.newHashSet();
            matrixs.forEach(matrix->{
                testLineIds.add(matrix.getTestLineInstanceId());
            });
            List<SampleGroupReq> groups = reqObject.getGroups();
            if (groups == null){
                groups = Lists.newArrayList();
            }
            List<TestSampleInfoPO> oldSamples = testSampleExtMapper.getSampleByOrderNo(reqObject.getOrderNo());
            List<TestSampleReq> samples = reqObject.getSamples();
            //BOM回传的SampleId会变化，这里按照SampleNo重新设置一下
            Map<String,String> sampleIdMapping = new HashMap<>();
            if(Func.isNotEmpty(oldSamples)){
                oldSamples = oldSamples.stream().filter(e -> e.getActiveIndicator()).collect(Collectors.toList());
                if(Func.isNotEmpty(oldSamples)){
                    for (TestSampleReq sample : samples) {
                        TestSampleInfoPO oldSampleInfo = oldSamples.stream().filter(item -> Func.equalsSafe(item.getSampleNo(), sample.getSampleNo())).findAny().orElse(null);
                        if(Func.isNotEmpty(oldSampleInfo) && Func.equalsSafe(oldSampleInfo.getID(),sample.getSampleId())){
                            sample.setSampleId(oldSampleInfo.getID());
                            sampleIdMapping.put(sample.getSampleId(),oldSampleInfo.getID());
                        }
                    }
                }
            }
            if(Func.isNotEmpty(matrixs)){
                for (TestMatrixReq matrix : matrixs) {
                    if(sampleIdMapping.containsKey(matrix.getSampleId())){
                        matrix.setSampleId(sampleIdMapping.get(matrix.getSampleId()));
                    }
                }
            }

            for (SampleGroupReq groupReq : groups) {
                if(sampleIdMapping.containsKey(groupReq.getSampleId())){
                    groupReq.setSampleId(sampleIdMapping.get(groupReq.getSampleId()));
                }
            }

            /*
             * 1、只有沒有Matrix的Sample才能標記Not Test
             * 2、如果TL是NC或者Sample是Not Test，則不能建立Matrix
             * 3、SampleParentID+SampleType检查类型，是否跨界如：O
             * 4、NC的样品，Bom不再回传过来，需要本地做处理 add vincent 2020年12月1日
             * */
            rspReuslt = this.checkSampleParameters(reqObject);
            if (!rspReuslt.isSuccess()){
                return rspReuslt;
            }
            rspReuslt = new CustomResult();

            GeneralOrderInstanceInfoPO order = orderMapper.getOrderInfo(reqObject.getOrderNo());
            if (order == null) {
                rspReuslt.setMsg(String.format("当前OrderNo [%s] 不存在.", reqObject.getOrderNo()));
                return rspReuslt;
            }
            OrderInfoDto orderInfo = orderClient.getOrderInfoByOrderNo(reqObject.getOrderNo());
            if (OperationType.check(orderInfo.getOperationType(), OperationType.NewSubContract)) {
                return rspReuslt.fail(String.format("当前OrderNo [%s] 不能此操作.", reqObject.getOrderNo()));
            }
            // POSL-2363
            /*ReportInfoPO oldReport = reportMapper.getReportByOrderNo(reqObject.getOrderNo());
            if (oldReport == null){
                rspReuslt.setMsg(String.format("未找到当前OrderNo [%s] 下的Report 信息.", reqObject.getOrderNo()));
                return rspReuslt;
            }
            if (!ReportStatus.check(oldReport.getReportStatus(), ReportStatus.New, ReportStatus.Combined, ReportStatus.Draft)){
                rspReuslt.setMsg(String.format("当前Report[%s] 的状态为New、Combin、Draft才允许回传.", oldReport.getReportStatus()));
                return rspReuslt;
            }*/

            verifyDuplicateSamples(reqObject, order.getID());
            // 重新计算SampleSeq
            this.recalculateSort(reqObject.getSamples(), null);
            //
            rspReuslt = this.resetMixSampleNo(reqObject);
            if (!rspReuslt.isSuccess()){
                return rspReuslt;
            }
            rspReuslt = new CustomResult();


            // 过滤 Not Test // Applicable
            Map<String, TestSampleInfoPO> oldSampleMaps = Maps.newHashMap();
            Map<String, SampleType> oldSampleIds = Maps.newHashMap();
            Map<String, PPTestLineRelInfo> oldPPSampleRelMaps = Maps.newHashMap();
            Map<String, String> oldSampleGroupIds = Maps.newHashMap();

            Set<String> phySampleIds = Sets.newHashSet();

            //原样NC标记，因为最新逻辑2020年12月1日 ，Bom 不会回传NC的样品回来了，需要本地做处理

            Set<String> sampleIdSet = Sets.newHashSet();
            Map<String,Boolean> originalSampleNCMap = Maps.newHashMap();
            for (TestSampleInfoPO sample: oldSamples){
                if(SampleType.check(sample.getSampleType(),SampleType.OriginalSample)){
                    originalSampleNCMap.put(sample.getID(),sample.getApplicable());
                }

                SampleType sampleType = SampleType.findType(sample.getSampleType());
                if (sampleType != SampleType.OriginalSample && !StringUtils.equalsIgnoreCase(sampleType.getCategoryChem(), sample.getCategory())){
                    phySampleIds.add(sample.getID());
                    continue;
                }
                oldSampleIds.put(sample.getID(), sampleType);
                oldSampleMaps.put(sample.getID(), sample);
                sampleIdSet.add(sample.getID());
            }

            List<PPSampleRelInfo> oldPPSampleRels = testSampleExtMapper.getPPSampleRelByOrderId(order.getID());
            PPTestLineRelInfo ppTestLineRel;
            for (PPSampleRelInfo ppSampleRel: oldPPSampleRels){
                if (phySampleIds.contains(ppSampleRel.getTestSampleId()) || !testLineIds.contains(ppSampleRel.getTestLineId())){
                    continue;
                }
                String tlRelKey = String.format("%s_%s", ppSampleRel.getPpTLRelId(), ppSampleRel.getTestLineId());
                if (!oldPPSampleRelMaps.containsKey(tlRelKey)){
                    ppTestLineRel = new PPTestLineRelInfo();
                    ppTestLineRel.setPpTLRelId(ppSampleRel.getPpTLRelId());
                    ppTestLineRel.setTestSampleId(ppSampleRel.getTestSampleId());
                    ppTestLineRel.setTestLineId(ppSampleRel.getTestLineId());
                    oldPPSampleRelMaps.put(tlRelKey, ppTestLineRel);
                }
                if (StringUtils.isBlank(ppSampleRel.getTestSampleId()) || StringUtils.isBlank(ppSampleRel.getPpSampleRelId())){
                    continue;
                }
                ppTestLineRel = oldPPSampleRelMaps.get(tlRelKey);
                ppTestLineRel.getPpSampleRelMaps().put(ppSampleRel.getTestSampleId(), ppSampleRel.getPpSampleRelId());
            }

            List<TestSampleGroupInfoPO> oldSampleGroups = testSampleGroupExtMapper.getSampleGroupByOrderNo(reqObject.getOrderNo());
            for (TestSampleGroupInfoPO sampleGroup: oldSampleGroups){
                if (!oldSampleIds.containsKey(sampleGroup.getSampleID())){
                    continue;
                }
                oldSampleGroupIds.put(String.format("%s_%s", sampleGroup.getSampleID(), sampleGroup.getSampleGroupID()), sampleGroup.getID());
            }

            // 过滤 TL是NC、activeIndicator 为 0
            Map<String, TestLineInstancePO> testLineMaps = Maps.newHashMap();
            DatabaseContextHolder.setDatabaseType(DatabaseTypeEnum.Write);
            List<TestLineInstancePO> testLines = testLineMapper.getTestLineByOrderId(order.getID());
            for (TestLineInstancePO testLine: testLines){
                if (!testLineIds.contains(testLine.getID())){
                    continue;
                }
                if (!TestLineStatus.check(testLine.getTestLineStatus(), TestLineStatus.Typing, TestLineStatus.Entered)){
                    rspReuslt.setMsg(String.format("当前Test Line(%s)状态为%s，只允许Typing的状态回传.", testLine.getTestLineID(), TestLineStatus.findStatus(testLine.getTestLineStatus())));
                    return rspReuslt;
                }
                testLineMaps.put(testLine.getID(), testLine);
            }
            Map<String, TestMatrixPO> oldTestMatrixMaps = Maps.newHashMap();
            Map<String, Set<String>> oldMatrixSampleIds = Maps.newHashMap();
            for (TestMatrixPO testMatrix: testMatrixMapper.getTestMatrixByOrderId(order.getID())){
                if (!oldMatrixSampleIds.containsKey(testMatrix.getTestSampleID())){
                    oldMatrixSampleIds.put(testMatrix.getTestSampleID(), Sets.newHashSet());
                }
                oldMatrixSampleIds.get(testMatrix.getTestSampleID()).add(testMatrix.getTestLineInstanceID());
                if (!testLineIds.contains(testMatrix.getTestLineInstanceID())){
                    continue;
                }
                oldTestMatrixMaps.put(String.format("%s_%s", testMatrix.getTestSampleID(), testMatrix.getTestLineInstanceID()), testMatrix);
            }

            /*
             * 1、检查是否OriginalSample 是否一致
             * 2、检查是否跨Order的Sample存在
             * 3、tre_report_matrix_relationship
             * 4、tre_pp_sample_relationship
             * */

            // 检查Nc
            rspReuslt = this.checkSampleNc(reqObject, oldSamples, oldMatrixSampleIds, oldSampleGroups);
            if (!rspReuslt.isSuccess()){
                return rspReuslt;
            }
            rspReuslt = new CustomResult();

            List<TestSampleGroupInfoPO> testSampleGroups = Lists.newArrayList();
            List<TestSampleInfoPO> testSamples = Lists.newArrayList();
            List<TestSampleLangInfoPO> testSampleLangs = Lists.newArrayList();
            Set<String> newSampleIds = Sets.newHashSet();
            Set<String> sampleSetIds = Sets.newHashSet();

            Set<String> testSampleIds = samples.stream().map(TestSampleReq::getSampleId).collect(Collectors.toSet());
            Map<String,List<TestSampleLangInfoPO>> langMap = Maps.newHashMap();
            if(!CollectionUtils.isEmpty(testSampleIds)){
                List<TestSampleLangInfoPO> langs = testSampleLangMapper.getTestSampleListIds(testSampleIds);
                langMap = langs.stream().collect(Collectors.groupingBy(TestSampleLangInfoPO::getSampleId));
            }
            List<String> delSampleLangIds = Lists.newArrayList();
            TestSampleInfoPO testSample;
            SampleType parentSampleType;
            for (TestSampleReq sample : samples) {
                SampleType sampleType = SampleType.findType(sample.getSampleType());
                if (sampleType == null) {
                    rspReuslt.setMsg(String.format("未找到对应的sample(%s)类型.", sample.getSampleNo()));
                    return rspReuslt;
                }
                if (sample.getNoTest() == null){
                    sample.setNoTest(false);
                }
                if (sampleType == SampleType.OriginalSample) {
                    String originalSampleId = sample.getSampleId();
                    if (StringUtils.isBlank(originalSampleId)){
                        rspReuslt.setMsg(String.format("对应的sample(%s)，ExecutionSystemSampleId 属性不能为空.", sample.getSampleNo()));
                        return rspReuslt;
                    }
                    TestSampleInfoPO sampleItem = oldSampleMaps.get(originalSampleId);
                    if (sampleItem == null){
                        rspReuslt.setMsg(String.format("未找该Original Sample(%s).", sample.getSampleNo()));
                        return rspReuslt;
                    }
                    if (!SampleType.equals(sampleItem.getSampleType(), SampleType.OriginalSample)){
                        rspReuslt.setMsg(String.format("请求的Original Sample(%s)类型无效.", sample.getSampleNo()));
                        return rspReuslt;
                    }
                    //这个判断的场景是，同步之后，notes做了NC,但是Bom 没有再同步
                    Boolean applicable = sampleItem.getApplicable();
                    if (applicable != null && applicable.booleanValue()){
                        rspReuslt.setMsg(String.format("请求的原样Sample(%s)已被Not Test，无法操作.", sample.getSampleNo()));
                        return rspReuslt;
                    }
                    if (oldSampleIds.containsKey(originalSampleId)){
                        oldSampleIds.remove(originalSampleId);
                    }
                    sampleSetIds.add(originalSampleId);
                    continue;
                }
                if (sample.getNoTest() && oldMatrixSampleIds.containsKey(sample.getSampleId())){
                    rspReuslt.setMsg(String.format("当前Sample(%s)已Not Test，无法Matrix.", sample.getSampleNo()));
                    return rspReuslt;
                }
                if (StringUtils.isBlank(sample.getSampleId()) || StringUtils.isBlank(sample.getSampleNo())){
                    rspReuslt.setMsg(String.format("当前SampleId(%s)或SampleNo(%s)不能为空.", sample.getSampleId(), sample.getSampleNo()));
                    return rspReuslt;
                }
                parentSampleType = this.findParentSampleType(samples, sample);
                TestSampleReq parentSample = this.findParentSample(samples,oldSamples, sample);
                Boolean parentSampleIsNC = originalSampleNCMap.get(parentSample.getSampleId());
                switch (sampleType) {
                    case Sample: // 102
                        if (parentSampleType == null || parentSampleType != SampleType.OriginalSample || parentSampleIsNC) {
                            rspReuslt.setMsg(String.format("未找到对应的sample(%s) 的父样,或者父样已经Not Test", sample.getSampleNo()));
                            return rspReuslt;
                        }
                        // 是否设置为ShareSample，即105
                        rspReuslt = this.shareSample(reqObject, oldSampleGroupIds, testSampleGroups, oldSampleMaps, sample);
                        if (!rspReuslt.isSuccess()) {
                            return rspReuslt;
                        }
                        sampleType = SampleType.findType(sample.getSampleType());
                        break;
                    case SubSample: // 103
                        if (parentSampleType == null || parentSampleType != SampleType.OriginalSample || parentSampleIsNC) {
                            rspReuslt.setMsg(String.format("未找到对应的sample(%s) 的父样,或者父样已经Not Test", sample.getSampleNo()));
                            return rspReuslt;
                        }
                        break;
                    case MixSample:  // 104 可以是任何样品
                        rspReuslt = this.mixSample(reqObject, oldSampleGroupIds, testSampleGroups, sample);
                        if (!rspReuslt.isSuccess()) {
                            return rspReuslt;
                        }
                        break;
                    case ShareSample:  // 105 该值只能是原样，且不能删除
                        if (parentSampleType == null || parentSampleType != SampleType.OriginalSample || parentSampleIsNC) {
                            rspReuslt.setMsg(String.format("未找到对应的sample(%s) 的父样,或者父样已经Not Test", sample.getSampleNo()));
                            return rspReuslt;
                        }
                        rspReuslt = this.shareSample(reqObject, oldSampleGroupIds, testSampleGroups, oldSampleMaps, sample);
                        if (!rspReuslt.isSuccess()) {
                            return rspReuslt;
                        }
                        sampleType = SampleType.findType(sample.getSampleType());
                        break;
                }
                //校验通过，原DB数据就不需要处理了
                if (oldSampleIds.containsKey(sample.getSampleId())){
                    oldSampleIds.remove(sample.getSampleId());
                }
                if(oldSampleIds.containsKey(parentSample.getSampleId())){
                    oldSampleIds.remove(parentSample.getSampleId());
                }
                testSample = new TestSampleInfoPO();
                // 检查SampleId是否当前OrderNo下
                if (!oldSampleMaps.containsKey(sample.getSampleId())){
                    newSampleIds.add(sample.getSampleId());
                }
                testSample.setID(sample.getSampleId());
                testSample.setOrderNo(reqObject.getOrderNo());
                testSample.setCategory(sampleType.getCategoryChem());
                if (!sample.isChem() && (sampleType == SampleType.MixSample)){
                    testSample.setCategory(sampleType.getCategoryPhy());
                }
                testSample.setSampleParentID(sample.getSampleParentId());
                testSample.setSampleNo(sample.getSampleNo());
                testSample.setDescription(sample.getSampleDesc());
                testSample.setChineseDescription(sample.getChineseDescription());
                testSample.setSampleType(sampleType.getSampleType());
                testSample.setSampleSeq(sample.getSampleSeq());
                //testSample.setMaterial(sample.getMaterial());
                testSample.setColor(sample.getColor());
                testSample.setEndUse(sample.getEndUse());
                testSample.setApplicable(sample.getNoTest());
                //testSample.setSampleRemark(sample.getRemark());
                testSample.setOtherSampleInfo(sample.getRemark());
                testSample.setGroupType(sample.getGroupType());

                testSample.setActiveIndicator(true);
                testSample.setCreatedBy(updateUser);
                testSample.setCreatedDate(DateUtils.getNow());
                testSample.setModifiedBy(updateUser);
                testSample.setModifiedDate(DateUtils.getNow());
                testSamples.add(testSample);

                // TODO Sample Lang
//                if(!SampleType.check(sample.getSampleType(),SampleType.MixSample)){
                List<TestSampleLangInfoPO> langs = langMap.get(sample.getSampleId());
                testSampleLangs.addAll(testSampleLangService.getSampleLangInfoList(testSample, sample,langs));
                if(!CollectionUtils.isEmpty(langs)){
                    delSampleLangIds.addAll(langs.stream().map(TestSampleLangInfoPO::getId).collect(Collectors.toList()));
                }
//                }
                sampleSetIds.add(testSample.getID());
                rspReuslt = new CustomResult();
            }
            List<String> delSampleIds = Lists.newArrayList();
            Iterator<Map.Entry<String, SampleType>> sampleIds = oldSampleIds.entrySet().iterator();
            while (sampleIds.hasNext()) {
                Map.Entry<String, SampleType> entry = sampleIds.next();
                TestSampleInfoPO oldSample = oldSampleMaps.get(entry.getKey());
                String sampleNo = "";
                if (oldSample != null){
                    sampleNo = oldSample.getSampleNo();
                }
                //当前原样如果是NC ，就不用判断删除逻辑了，因为Bom不会回传NC样品
                Boolean isNC = originalSampleNCMap.get(entry.getKey());
                isNC = isNC!=null ?isNC:false;
                if ((entry.getValue() == null || entry.getValue() == SampleType.OriginalSample) && !isNC){
                    rspReuslt.setMsg(String.format("Original Sample(%s)不能删除.", sampleNo));
                    return rspReuslt;
                }
                if (oldMatrixSampleIds.containsKey(entry.getKey())){
                    for (String testLineId: oldMatrixSampleIds.get(entry.getKey())){
                        if (testLineIds.contains(testLineId)){
                            continue;
                        }
                        rspReuslt.setMsg(String.format("当前Sample(%s)已Assign 不能删除.", sampleNo));
                        return rspReuslt;
                    }
                }
                delSampleIds.add(entry.getKey());
            }

            List<String> delSampleGroupIds = Lists.newArrayList();
            Iterator<Map.Entry<String, String>> sampleGroupIds = oldSampleGroupIds.entrySet().iterator();
            while (sampleGroupIds.hasNext()) {
                Map.Entry<String, String> entry = sampleGroupIds.next();
                if (StringUtils.isBlank(entry.getValue())){
                    continue;
                }
                delSampleGroupIds.add(entry.getValue());
            }

            if (!newSampleIds.isEmpty()){
                List<String> orderNos = testSampleExtMapper.getSampleIdList(Lists.newArrayList(newSampleIds));
                if (orderNos != null && orderNos.size() > 0 &&  !orderNos.contains(reqObject.getOrderNo())){
                    rspReuslt.setMsg(String.format("当前SampleIds(%s)未在Order下.", StringUtils.join(newSampleIds, ",")));
                    return rspReuslt;
                }
            }
            ReportInfoPO report = reportMapper.getReportByOrderNo(reqObject.getOrderNo());
            if (report == null){
                rspReuslt.setMsg(String.format("未找到Order(%s)下的Report信息.", reqObject.getOrderNo()));
                return rspReuslt;
            }

            List<TestMatrixExtPO> testMatrixExtInsertList = new ArrayList<>();
            List<TestMatrixExtPO> testMatrixExtUpdateList = new ArrayList<>();
            CustomResult<List<TestMatrixPO>> rspMatrix = this.handleTestMatrix(order.getID(), reqObject, oldPPSampleRelMaps, sampleSetIds, oldTestMatrixMaps, testLineMaps,testMatrixExtInsertList,testMatrixExtUpdateList);
            if (!rspMatrix.isSuccess()){
                return rspMatrix;
            }
            List<String> conclusionTestMatrixIds = conclusionMapper.getConclusionTestMatrixIds(order.getID());

            List<String> delPPSampleRelIds = Lists.newArrayList();
            List<String> delMatrixIds = Lists.newArrayList();
            Iterator<Map.Entry<String, TestMatrixPO>> testMatrixMaps = oldTestMatrixMaps.entrySet().iterator();
            while (testMatrixMaps.hasNext()) {
                Map.Entry<String, TestMatrixPO> entry = testMatrixMaps.next();
                TestMatrixPO testMatrix = entry.getValue();
                TestLineInstancePO testLine = testLineMaps.get(testMatrix.getTestLineInstanceID());
                if (testLine == null){
                    continue;
                }
                TestLineStatus testLineStatus = TestLineStatus.findStatus(testLine.getTestLineStatus());
                if (testLineStatus == null || !(testLineStatus == TestLineStatus.Typing || testLineStatus == TestLineStatus.DR)) {
                    rspReuslt.setMsg(String.format("当前TestLine(%s)状态为(%s)不能删除Matrix.", testMatrix.getTestLineInstanceID(), testLineStatus));
                    return rspReuslt;
                }
                if (phySampleIds.contains(testMatrix.getTestSampleID())){
                    continue;
                }
                if (!conclusionTestMatrixIds.isEmpty() && conclusionTestMatrixIds.contains(testMatrix.getID())){
                    rspReuslt.setMsg(String.format("当前TestLine(%s)已入录conclusion不能删除Matrix(%s).", testMatrix.getTestLineInstanceID(), testMatrix.getID()));
                    return rspReuslt;
                }
                delMatrixIds.add(testMatrix.getID());
            }
            List<TestMatrixPO> newTestMatrixs = rspMatrix.getData();
            //
            List<PPSampleRelationshipInfoPO> ppSampleRels = this.handlePPSampleRel(oldPPSampleRelMaps, delPPSampleRelIds, reqObject.getUserName());
            //
//            List<ReportMatrixRelationShipInfoPO> reportMatrixRels = this.handleReportMatrixRel(newTestMatrixs, report.getID(), reqObject.getUserName());

            rspReuslt = validateToSlimSubcontracts(reqObject);
            if (!rspReuslt.isSuccess()){
                return rspReuslt;
            }
            //构造slim subcontract
            List<SlimSubcontractPO> slimSubcontractList = convertToSlimSubcontracts(order, reqObject);
            //根据新的Sample匹配出相关的Sample，再匹配出关联的Report
            ReportMatrixQueryReq reportMatrixQueryReq = new ReportMatrixQueryReq();
            reportMatrixQueryReq.setOrderNoList(Sets.newHashSet(reqObject.getOrderNo()));
            List<ReportMatrixDTO> dbReportMatrixDtoList = reportMatrixRelMapper.selectReportMatrixList(reportMatrixQueryReq);
            List<ReportMatrixDTO> newReportMatrixDtoList = new ArrayList<>();
            //将新的SampleGroups和旧的SampleGroups放一起
            List<TestSampleGroupInfoPO> allTestSampleGroups = new ArrayList<>();
            allTestSampleGroups.addAll(oldSampleGroups);
            allTestSampleGroups.addAll(testSampleGroups);
            // 记录日志
            SystemLog systemLog1 = new SystemLog();
            systemLog1.setObjectType("Bom回传");
            systemLog1.setObjectNo(reqObject.getOrderNo());
            systemLog1.setProductLineCode(reqObject.getProductLineCode());
            systemLog1.setType(SystemLogType.API.getType());
            systemLog1.setRemark("Bom回传DBMatrix关系");
            systemLog1.setOperationType("Bom回传");
            systemLog1.setCreateBy(reqObject.getUserName());
            systemLog1.setRequest(JSON.toJSONString(dbReportMatrixDtoList));
            systemLogHelper.save(systemLog1);

            if(Func.isNotEmpty(testSamples) && Func.isNotEmpty(dbReportMatrixDtoList)){
                //排除Cancelled Reworked数据
                dbReportMatrixDtoList = dbReportMatrixDtoList.stream()
                        .filter(item->!ReportStatus.check(item.getReportStatus(),ReportStatus.Cancelled,ReportStatus.Reworked))
                        .collect(Collectors.collectingAndThen(Collectors.toCollection(
                        () -> new TreeSet<>(Comparator.comparing(o -> o.getId()))), ArrayList::new));
                List<TestSampleInfoPO> allTestSampleList = new ArrayList<>();
                allTestSampleList.addAll(oldSamples);
                allTestSampleList.addAll(testSamples);
                allTestSampleList = allTestSampleList.stream().filter(item->!delSampleIds.contains(item.getID())).collect(Collectors.collectingAndThen(Collectors.toCollection(
                        () -> new TreeSet<>(Comparator.comparing(o -> o.getID()))), ArrayList::new));

                Map<String,List<TestSampleInfoPO>> groupSampleListMap = new HashMap<>();
                if(Func.isNotEmpty(allTestSampleGroups)){
                    List<String> groupSampleIdList = allTestSampleGroups.stream().map(TestSampleGroupInfoPO::getSampleID).distinct().collect(Collectors.toList());
                    for (String groupSampleId : groupSampleIdList) {
                        List<TestSampleInfoPO> groupSampleList = findGroupSample(groupSampleId, allTestSampleList, allTestSampleGroups);
                        groupSampleListMap.put(groupSampleId,groupSampleList);
                    }
                }

                newReportMatrixDtoList = this.findReportMatrix(newTestMatrixs, allTestSampleList, groupSampleListMap, dbReportMatrixDtoList,testLines,oldPPSampleRels);
            }
            logger.info("new ReportMatrix:{}",Func.toJson(newReportMatrixDtoList));
            List<ReportMatrixDTO> pendingReportList = newReportMatrixDtoList.stream().filter(item -> ReportStatus.check(item.getReportStatus(), ReportStatus.Pending)).collect(Collectors.toList());
            List<StatusInfoPO> statusInfoPOS = new ArrayList<>();
            if(Func.isNotEmpty(pendingReportList)){
                List<String> pendingReportNoList = pendingReportList.stream().map(ReportMatrixDTO::getReportNo).collect(Collectors.toList());
                StatusInfoExample statusInfoExample = new StatusInfoExample();
                statusInfoExample.createCriteria().andObjectTypeEqualTo(4).andOperationTypeEqualTo(1).andObjectNoIn(pendingReportNoList);
                statusInfoPOS = statusInfoMapper.selectByExample(statusInfoExample);
            }
            Set<String> unNewReport = new HashSet<>();
            List<ReportMatrixRelationShipInfoPO> reportMatrixRels = new ArrayList<>();
            if(Func.isNotEmpty(newReportMatrixDtoList)){
                for (TestMatrixPO newTestMatrix : newTestMatrixs) {
                    List<StatusInfoPO> finalStatusInfoPOS = statusInfoPOS;
                    List<ReportMatrixDTO> currentReportMatrix = newReportMatrixDtoList.stream().filter(item -> Func.equalsSafe(item.getTestMatrixId(), newTestMatrix.getID())).collect(Collectors.toList());
                    if(Func.isEmpty(currentReportMatrix)){
                        continue;
                    }
                    //判断是否有多个Report
                    long reportCount = currentReportMatrix.stream()
                            .map(ReportMatrixDTO::getReportNo)
                            .filter(Func::isNotEmpty)
                            .distinct()
                            .count();
                    if (reportCount > 1L) {
                        logger.info("多个ReportMatrix,不继承");
                        continue;
                    }

                    currentReportMatrix.stream().findAny().ifPresent(item -> {
                        //关联的报告状态是否都是New或Pending前是否是New
                        //过滤非New或pending前非New的report
                        Integer reportStatus = item.getReportStatus();
                        String reportNo = item.getReportNo();
                        Boolean isNew = false;
                        if (ReportStatus.check(reportStatus, ReportStatus.New)) {
                            isNew = true;
                        } else if (ReportStatus.check(reportStatus, ReportStatus.Pending) && Func.isNotEmpty(finalStatusInfoPOS)) {
                            StatusInfoPO latestStatusInfoPO = finalStatusInfoPOS.stream().filter(statusItem -> Func.equals(statusItem.getObjectNo(),reportNo)).max(Comparator.comparing(StatusInfoPO::getCreatedDate)).orElse(null);
                            if (Func.isNotEmpty(latestStatusInfoPO) && ReportStatus.check(latestStatusInfoPO.getOldStatus(), ReportStatus.New)) {
                                isNew = true;
                            }
                        }
                        if(!isNew){
                            unNewReport.add(reportNo);
                            return;
                        }
                        ReportMatrixRelationShipInfoPO reportMatrixRelationShipInfoPO = new ReportMatrixRelationShipInfoPO();
                        reportMatrixRelationShipInfoPO.setID(IdUtil.uuId());
                        reportMatrixRelationShipInfoPO.setReportID(item.getReportId());
                        reportMatrixRelationShipInfoPO.setTestMatrixID(newTestMatrix.getID());
                        reportMatrixRelationShipInfoPO.setCreatedBy(updateUser);
                        reportMatrixRelationShipInfoPO.setCreatedDate(now);
                        reportMatrixRelationShipInfoPO.setModifiedBy(updateUser);
                        reportMatrixRelationShipInfoPO.setModifiedDate(now);
                        reportMatrixRelationShipInfoPO.setSeq(item.getSeq());
                        reportMatrixRels.add(reportMatrixRelationShipInfoPO);
                    });
                }
                if(Func.isNotEmpty(unNewReport)){
                    String notNewReportNoList = String.join(",", unNewReport);
                    rspReuslt.setSuccess(false);
                    rspReuslt.setMsg("["+notNewReportNoList+"] Status is Not New,Not allowed to receive matrix information");
                    return rspReuslt;
                }
            }
            logger.info("new Report Matrix rel:{}",JSON.toJSON(reportMatrixRels));

            List<TestLineListReq> reqTestLineList = reqObject.getTestLines();
            List<TestLineInstancePO> updateTestLineStatusList = new ArrayList<>();
            if(Func.isNotEmpty(reqTestLineList)){
                List<String> updateTestLineInstanceIdList = reqTestLineList.stream().map(TestLineListReq::getTestLineInstanceId).collect(Collectors.toList());
                updateTestLineStatusList = testLineMapper.getTestLineByIds(updateTestLineInstanceIdList);
            }
            //   GPO2-13380 查询Sample关联的Report，且Report的状态都是New(或Pending前是New)才允许接收(排除Cancelled、Reworked)
            List<TestMatrixPO> finalNewTestMatrixs = newTestMatrixs;
            CustomResult finalRspReuslt = rspReuslt;
            List<TestLineInstancePO> finalUpdateTestLineStatusList = updateTestLineStatusList;
            int trans = transactionTemplate.execute((tranStatus)->{
                int execNum = 1;
                if (!delMatrixIds.isEmpty()){
                    execNum += testMatrixMapper.batchDelete(delMatrixIds);
                }
                if (!delPPSampleRelIds.isEmpty()){
                    execNum += testSampleExtMapper.delPPSampleRelInfo(delPPSampleRelIds);
                }
                if (!delSampleGroupIds.isEmpty()){
                    execNum += testSampleGroupExtMapper.delTestSampleGroupInfo(delSampleGroupIds);
                }
                // TODO 检查原样是未传送过来
                if (!delSampleIds.isEmpty()){
                    // 还有考虑一种情况，Sample没有删除，只是没有Assign对应的TestLine
                    execNum += testMatrixMapper.delTestMatrixBySampleIds(delSampleIds);
                    //
                    execNum += testSampleExtMapper.batchDelete(delSampleIds);
                    //删除test_sample_language表的数据
                    execNum += testSampleLangMapper.deleteBySampleIds(delSampleIds);
                }
                // 删除子样下对应的Sample
                if (!testSamples.isEmpty() && testSampleExtMapper.batchInsert(testSamples) <= 0){
                    tranStatus.setRollbackOnly(); // 回滚事务
                    return execNum;
                }
                if (!testSampleLangs.isEmpty() && testSampleLangMapper.batchInsert(testSampleLangs) <= 0){
                    tranStatus.setRollbackOnly(); // 回滚事务
                    return execNum;
                }
                if (!testSampleGroups.isEmpty() && testSampleGroupExtMapper.batchInsert(testSampleGroups) <= 0){
                    tranStatus.setRollbackOnly(); // 回滚事务
                    return execNum;
                }
                if (!ppSampleRels.isEmpty() && testSampleExtMapper.batchSavePPSampleRelInfo(ppSampleRels) <= 0){
                    tranStatus.setRollbackOnly(); // 回滚事务
                    return execNum;
                }
                if (!finalNewTestMatrixs.isEmpty() && testMatrixMapper.batchInsert(finalNewTestMatrixs) <= 0){
                    tranStatus.setRollbackOnly(); // 回滚事务
                    return execNum;
                }
                if (!reportMatrixRels.isEmpty() && reportMapper.batchSaveReportMatrixRelInfo(reportMatrixRels) <= 0){
                    tranStatus.setRollbackOnly(); // 回滚事务
                    return execNum;
                }

                if(!testMatrixExtInsertList.isEmpty() && testMatrixExtMapper.insertBatch(testMatrixExtInsertList) <= 0){
                    tranStatus.setRollbackOnly(); // 回滚事务
                    return execNum;
                }
                if(!testMatrixExtUpdateList.isEmpty() && testMatrixExtMapper.updateAFByTestMatrixId(testMatrixExtUpdateList) <=0){
                    tranStatus.setRollbackOnly(); // 回滚事务
                    return execNum;
                }
                if(Func.isNotEmpty(reqTestLineList)){
                    try {
                        // 是否触发计算订单状态
                        boolean triggerOrderChange = false;
                        List<String> cancelTestLineInstanceIdList = reqTestLineList.stream().filter(item->TestLineStatus.check(item.getTestLineStatus(),TestLineStatus.Cancelled)).map(TestLineListReq::getTestLineInstanceId).distinct().collect(Collectors.toList());
                        //已经cancel的不需要再次Cancel
                        List<String> alreadyCancelledTl = finalUpdateTestLineStatusList.stream().filter(item->TestLineStatus.check(item.getTestLineStatus(),TestLineStatus.Cancelled)).map(TestLineInstancePO::getID).collect(Collectors.toList());
                        cancelTestLineInstanceIdList = cancelTestLineInstanceIdList.stream().filter(item->!alreadyCancelledTl.contains(item)).collect(Collectors.toList());
                        List<String> naTestLineInstanceIdList = reqTestLineList.stream().filter(item->TestLineStatus.check(item.getTestLineStatus(),TestLineStatus.NA)).map(TestLineListReq::getTestLineInstanceId).distinct().collect(Collectors.toList());
                        //已经NA的不需要再次Cancel
                        List<String> alreadyNATl = finalUpdateTestLineStatusList.stream().filter(item->TestLineStatus.check(item.getTestLineStatus(),TestLineStatus.NA)).map(TestLineInstancePO::getID).collect(Collectors.toList());
                        naTestLineInstanceIdList = naTestLineInstanceIdList.stream().filter(item->!alreadyNATl.contains(item)).collect(Collectors.toList());

                        //Cancel TL
                        if(Func.isNotEmpty(cancelTestLineInstanceIdList)){
                            for (String cancelId : cancelTestLineInstanceIdList) {
                                TestLineCancelReq testLineCancelReq = new TestLineCancelReq();
                                testLineCancelReq.setTestLineInstanceId(cancelId);
                                testLineCancelReq.setOperateBy(updateUser);
                                CustomResult customResult = testLineService.cancelTestLine(testLineCancelReq);
                                if(!customResult.isSuccess()){
                                    tranStatus.setRollbackOnly(); // 回滚事务
                                    finalRspReuslt.setSuccess(false);
                                    finalRspReuslt.setMsg(customResult.getMsg());
                                }
                                triggerOrderChange = true;
                            }
                        }
                        List<TestLineInstancePO> naTestLineStatusList = new ArrayList<>();
                        List<MatrixStatusInfo> naTestMatrixList = Lists.newArrayList();
                        if(Func.isNotEmpty(finalUpdateTestLineStatusList) && Func.isNotEmpty(naTestLineInstanceIdList)){
                            List<String> finalNaTestLineInstanceIdList = naTestLineInstanceIdList;
                            boolean existsCompletedTL = finalUpdateTestLineStatusList.stream().anyMatch(item -> finalNaTestLineInstanceIdList.contains(item.getID()) && TestLineStatus.check(item.getTestLineStatus(), TestLineStatus.Completed));
                            if(existsCompletedTL){
                                tranStatus.setRollbackOnly(); // 回滚事务
                                finalRspReuslt.setSuccess(false);
                                finalRspReuslt.setMsg("TL with Completed status cannot operate NA");
                            }
                            if(Func.isNotEmpty(naTestLineInstanceIdList)){
                                for (String naTlInstanceId : naTestLineInstanceIdList) {
                                    TestLineInstancePO testLineInstancePO  = new TestLineInstancePO();
                                    testLineInstancePO.setID(naTlInstanceId);
                                    testLineInstancePO.setTestLineStatus(TestLineStatus.NA.getStatus());
                                    testLineInstancePO.setModifiedDate(new Date());
                                    naTestLineStatusList.add(testLineInstancePO);
                                    MatrixStatusInfo matrixStatusInfo = new MatrixStatusInfo();
                                    matrixStatusInfo.setTestLineInstanceId(naTlInstanceId);
                                    matrixStatusInfo.setMatrixStatus(MatrixStatus.NA.getStatus());
                                    matrixStatusInfo.setUpdateTime(DateUtils.now());
                                    matrixStatusInfo.setRegionAccount(updateUser);
                                    naTestMatrixList.add(matrixStatusInfo);
                                }
                                testLineMapper.batchUpdateTestLineStatus(naTestLineStatusList);
                                // 同步更新matrix状态
                                testMatrixMapper.updateMatrixStatusByTestLineId(naTestMatrixList);
                                //同步删除分包单-TL关系
                                SubContractTestLineMappingExample example = new SubContractTestLineMappingExample();
                                example.createCriteria().andTestLineInstanceIDIn(naTestLineInstanceIdList);
                                subContractTestLineMappingMapper.deleteByExample(example);
                                triggerOrderChange = true;
                                try {
                                    //记录TestLine状态变化BizLog
                                    for (TestLineInstancePO testLineInstancePO : naTestLineStatusList) {
                                        BizLogInfo bizLog = new BizLogInfo();
                                        bizLog.setLab(orderInfo.getLocationCode());
                                        bizLog.setBu(orderInfo.getBUCode());
                                        bizLog.setOpUser(reqObject.getUserName());
                                        bizLog.setBizId(orderInfo.getOrderNo());
                                        bizLog.setOpType("Bom Tool Operation");
                                        bizLog.setNewVal(TestLineStatus.findStatus(testLineInstancePO.getTestLineStatus()));
                                        bizLog.setOriginalVal(testLineInstancePO.getTestLineStatus());
                                        bizLogClient.doSend(bizLog);
                                    }
                                } catch (Exception e) {

                                }
                            }
                        }
                        if(triggerOrderChange){
                            // 触发TestLineChange
                            TestLineStatusUpdateReq testLineStatusUpdateReq = new TestLineStatusUpdateReq();
                            testLineStatusUpdateReq.setOrderNo(reqObject.getOrderNo());
                            CustomResult onChangeResult = testLineStatusServiceNew.onChange(testLineStatusUpdateReq);
                            logger.info("onChange res:{}",onChangeResult);
                        }
                    } catch (Exception e) {
                        tranStatus.setRollbackOnly(); // 回滚事务
                        finalRspReuslt.setSuccess(false);
                        finalRspReuslt.setMsg(e.getMessage());
                    }

                }
                //处理procedure & scheme 的临表数据
                if(! CollectionUtils.isEmpty(reqObject.getSlimMapping())){
                    slimSubContractExtMapper.deleteSlimSubcontractWithoutToSlim(reqObject.getOrderNo());
                    if(! CollectionUtils.isEmpty(slimSubcontractList)) {
                        slimSubContractExtMapper.batchInsert(slimSubcontractList);
                    }
                }
                return execNum;
            });
            //
            this.matrixUpdateCondition(order.getID(), testLines);
            this.dealShareSolution(reqObject,order);
            this.dealCSPPSubcontract(order.getID(), order.getOrderNo());
            rspReuslt.setSuccess(trans > 0);
            //记录BizLog
            BizLogInfo bizLog = new BizLogInfo();
            bizLog.setBizId(reqObject.getOrderNo());
            bizLog.setBu(orderInfo.getBUCode());
            bizLog.setLab(orderInfo.getLocationCode());
            bizLog.setBizOpType(BizLogConstant.ORDER_OPERATION_HISTORY);
            bizLog.setOpUser(updateUser);
            bizLog.setOpType("BOM To GPO");
            bizLogClient.doSend(bizLog);
        }catch (Exception ex){
            logger.error("SampleService.updateBreakDown, OrderNo_{}（Req：{}） Error：",reqObject.getOrderNo(), reqObject, ex);
            rspReuslt.setSuccess(false);
            rspReuslt.setMsg(ex.getMessage());
            rspReuslt.setStackTrace(ExceptionUtil.getStackTrace(ex));
        }
        return rspReuslt;
    }
    private List<ReportMatrixDTO> findReportMatrix(List<TestMatrixPO> newTestMatrixs, List<TestSampleInfoPO> allTestSampleList, Map<String, List<TestSampleInfoPO>> groupSampleListMap,
                                                   List<ReportMatrixDTO> dbReportMatrixDtoList, List<TestLineInstancePO> testLines,
                                                   List<PPSampleRelInfo> oldPPSampleRels) {
        List<ReportMatrixDTO> newReportMatrixDtoList = new ArrayList<>();
        Map<String, List<ReportMatrixDTO>> tl_sample_ReportMatrixList = new HashMap<>();
        Map<String, List<ReportMatrixDTO>> testLineReportMatrixMaps = dbReportMatrixDtoList.stream()
                .collect(Collectors.groupingBy(ReportMatrixDTO::getTestLineInstanceId));

        // 提前缓存 CSPP 展开的 TestLineInstanceId 列表
        List<String> csppOpenTestLineInstanceIdList = testLines.stream()
                .filter(item -> TestLineType.check(item.getTestLineType(), TestLineType.CSPP_OPEN_TL))
                .map(TestLineInstancePO::getID)
                .collect(Collectors.toList());

        // 提前将 allTestSampleList 转换为 Map，减少重复遍历
        Map<String, TestSampleInfoPO> testSampleIdMap = allTestSampleList.stream()
                .collect(Collectors.toMap(TestSampleInfoPO::getID, item -> item));

        for (TestMatrixPO testMatrixPO : newTestMatrixs) {
            String newMatrixId = testMatrixPO.getID();
            String testLineInstanceId = testMatrixPO.getTestLineInstanceID();
            String newSampleId = testMatrixPO.getTestSampleID();
            String tl_sample_key = String.format("%s_%s", testLineInstanceId, newSampleId);
            // 缓存当前 TestLine 的 ReportMatrix 列表
            List<ReportMatrixDTO> currentTestLineRPMatrix = testLineReportMatrixMaps.getOrDefault(testLineInstanceId, Collections.emptyList());

            // 查找对应的 TestSampleInfoPO
            // 查找对应的 TestSampleInfoPO
            TestSampleInfoPO testSampleInfoPO = testSampleIdMap.getOrDefault(newSampleId,null);
            if (Func.isEmpty(testSampleInfoPO)) {
                continue;
            }

            // 处理 CSPP 展开的 TL，直接继承 CSPP 的 ReportMatrix
            if (csppOpenTestLineInstanceIdList.contains(testLineInstanceId) && Func.isNotEmpty(oldPPSampleRels)) {
                try {
                    List<ReportMatrixDTO> reportMatrixDTOList = handleCsppOpenTlReportMatrix(testMatrixPO, oldPPSampleRels, testLineReportMatrixMaps, testSampleInfoPO);
                    if(Func.isNotEmpty(reportMatrixDTOList)){
                        setMatrixIdFields(reportMatrixDTOList, newMatrixId, testLineInstanceId, testSampleInfoPO);
                        newReportMatrixDtoList.addAll(reportMatrixDTOList);
                    }
                } catch (Exception e) {
                    logger.error("Error handle CSPPOpenTestLine Matrix:{} " , e.getMessage());
                }
                continue;
            }

            // 处理 MIX 或 WITH 类型的Sample
            if (SampleGroupTypeEnums.check(testSampleInfoPO.getGroupType(), SampleGroupTypeEnums.MIX, SampleGroupTypeEnums.WITH) && Func.isNotEmpty(groupSampleListMap)) {
                List<TestSampleInfoPO> testSampleInfoPOS = groupSampleListMap.get(testSampleInfoPO.getID());
                if (Func.isEmpty(testSampleInfoPOS)) {
                    continue;
                }

                List<ReportMatrixDTO> tlGroupReportMatrixList = new ArrayList<>();
                for (TestSampleInfoPO testSampleInfoItem : testSampleInfoPOS) {
                    List<ReportMatrixDTO> groupReportMatrixList = handleGroupReportMatrix(testSampleInfoItem, currentTestLineRPMatrix, allTestSampleList, tl_sample_ReportMatrixList,tl_sample_key);
                    if (Func.isNotEmpty(groupReportMatrixList)) {
                        tlGroupReportMatrixList.addAll(groupReportMatrixList);
                    }
                }
                if(Func.isNotEmpty(tlGroupReportMatrixList)){
                    setMatrixIdFields(tlGroupReportMatrixList, newMatrixId, testLineInstanceId, testSampleInfoPO);
                    newReportMatrixDtoList.addAll(tlGroupReportMatrixList);
                }
            } else {
                // 处理单个Sample
                List<ReportMatrixDTO> reportMatrixDTOList = handleSingleSampleReportMatrix(testSampleInfoPO, currentTestLineRPMatrix, allTestSampleList, tl_sample_ReportMatrixList,tl_sample_key);
                if (Func.isNotEmpty(reportMatrixDTOList)) {
                    setMatrixIdFields(reportMatrixDTOList, newMatrixId, testLineInstanceId, testSampleInfoPO);
                    newReportMatrixDtoList.addAll(reportMatrixDTOList);
                }
            }
        }
        return newReportMatrixDtoList;
    }
    // 提取公共字段设置逻辑
    private void setMatrixIdFields(List<ReportMatrixDTO> reportMatrixDTOList, String newMatrixId, String testLineInstanceId, TestSampleInfoPO testSampleInfoPO) {
        reportMatrixDTOList.forEach(item -> {
            item.setTestMatrixId(newMatrixId);
            item.setTestLineInstanceId(testLineInstanceId);
            item.setTestSampleId(testSampleInfoPO.getID());
            item.setSampleNo(testSampleInfoPO.getSampleNo());
        });
    }
    private List<ReportMatrixDTO> handleCsppOpenTlReportMatrix(TestMatrixPO testMatrixPO,
                                                               List<PPSampleRelInfo> oldPPSampleRels,
                                                               Map<String, List<ReportMatrixDTO>> testLineReportMatrixMaps,
                                                               TestSampleInfoPO testSampleInfoPO){
        List<ReportMatrixDTO> resultList = new ArrayList<>();
        String testLineInstanceId = testMatrixPO.getTestLineInstanceID();
        PPSampleRelInfo ppSampleRelInfo = oldPPSampleRels.stream()
                .filter(item -> Func.equalsSafe(item.getTestLineId(), testLineInstanceId))
                .findAny().orElse(null);

        if (Func.isEmpty(ppSampleRelInfo) || Func.isEmpty(ppSampleRelInfo.getExtFields())) {
            return resultList;
        }

        try {
            JSONObject jsonObject = JSON.parseObject(ppSampleRelInfo.getExtFields());
            if (!jsonObject.containsKey("parentTestLineInstanceId") || Func.isEmpty(Func.toStr(jsonObject.get("parentTestLineInstanceId")))) {
                return resultList;
            }

            String csppTestLineInstanceId = Func.toStr(jsonObject.get("parentTestLineInstanceId"));
            List<ReportMatrixDTO> csppRPMatrixList = testLineReportMatrixMaps.getOrDefault(csppTestLineInstanceId, Collections.emptyList());

            if (Func.isEmpty(csppRPMatrixList)) {
                return resultList;
            }
            resultList = Func.copy(csppRPMatrixList, ReportMatrixDTO.class, ReportMatrixDTO.class);
        } catch (Exception e) {
            // 记录异常信息
            logger.error("Error parsing extFields:{} " , e.getMessage());
        }
        return resultList;
    }

    // 提取方法：MIX或WITH样的 ReportMatrix
    private List<ReportMatrixDTO> handleGroupReportMatrix(TestSampleInfoPO testSampleInfoItem,
                                                          List<ReportMatrixDTO> currentTestLineRPMatrix,
                                                          List<TestSampleInfoPO> allTestSampleList,
                                                          Map<String, List<ReportMatrixDTO>> tl_sample_ReportMatrixList,String tl_sample_key) {
        if (tl_sample_ReportMatrixList.containsKey(tl_sample_key)) {
            return Func.copy(tl_sample_ReportMatrixList.get(tl_sample_key), ReportMatrixDTO.class, ReportMatrixDTO.class);
        } else {
            List<ReportMatrixDTO> groupReportMatrixList = buildReportMatrix(testSampleInfoItem, currentTestLineRPMatrix, allTestSampleList);
            if (Func.isEmpty(groupReportMatrixList)) {
                return Collections.emptyList();
            }
            List<ReportMatrixDTO> copiedGroupReportMatrixList = Func.copy(groupReportMatrixList, ReportMatrixDTO.class, ReportMatrixDTO.class);
            tl_sample_ReportMatrixList.put(tl_sample_key, copiedGroupReportMatrixList);
            return copiedGroupReportMatrixList;
        }
    }

    // 提取方法：处理子样、子子样的 ReportMatrix
    private List<ReportMatrixDTO> handleSingleSampleReportMatrix(TestSampleInfoPO testSampleInfoPO,
                                                                 List<ReportMatrixDTO> currentTestLineRPMatrix,
                                                                 List<TestSampleInfoPO> allTestSampleList,
                                                                 Map<String, List<ReportMatrixDTO>> tl_sample_ReportMatrixList,
                                                                 String tl_sample_key) {
        if (tl_sample_ReportMatrixList.containsKey(tl_sample_key)) {
            return Func.copy(tl_sample_ReportMatrixList.get(tl_sample_key), ReportMatrixDTO.class, ReportMatrixDTO.class);
        } else {
            List<ReportMatrixDTO> reportMatrixDTOList = buildReportMatrix(testSampleInfoPO, currentTestLineRPMatrix, allTestSampleList);
            if (Func.isEmpty(reportMatrixDTOList)) {
                return Collections.emptyList();
            }

            long reportCount = reportMatrixDTOList.stream()
                    .map(ReportMatrixDTO::getReportNo)
                    .filter(Func::isNotEmpty)
                    .distinct()
                    .count();

            if (reportCount > 1L) {
                return Collections.emptyList();
            }

            List<ReportMatrixDTO> copiedReportMatrixDTOList = Func.copy(reportMatrixDTOList, ReportMatrixDTO.class, ReportMatrixDTO.class);
            tl_sample_ReportMatrixList.put(tl_sample_key, copiedReportMatrixDTOList);
            return copiedReportMatrixDTOList;
        }
    }



    private List<ReportMatrixDTO> buildReportMatrix(TestSampleInfoPO testSampleInfoPO,List<ReportMatrixDTO> dbReportMatrixDtoList,List<TestSampleInfoPO> allTestSampleList){
        if (testSampleInfoPO == null) {
            return null;
        }
        Map<String, List<ReportMatrixDTO>> sampleIdToReportMatrixDtoList = dbReportMatrixDtoList.stream()
                .collect(Collectors.groupingBy(ReportMatrixDTO::getTestSampleId));

        String sampleParentID = getSampleParentID(testSampleInfoPO);
        if (sampleParentID == null) {
            return null;
        }
        List<ReportMatrixDTO> reportMatrix = sampleIdToReportMatrixDtoList.getOrDefault(sampleParentID, Collections.emptyList());
        if (!reportMatrix.isEmpty()) {
            return reportMatrix;
        }
        if (SampleType.check(testSampleInfoPO.getSampleType(), SampleType.SubSample)) {
            TestSampleInfoPO parentSample = allTestSampleList.stream()
                    .filter(item -> Func.equals(item.getID(), sampleParentID))
                    .findAny().orElse(null);
            if (parentSample != null) {
                return sampleIdToReportMatrixDtoList.getOrDefault(parentSample.getSampleParentID(), Collections.emptyList());
            }
        }
        return null;
    }
    private String getSampleParentID(TestSampleInfoPO testSampleInfoPO) {
        if (SampleType.check(testSampleInfoPO.getSampleType(), SampleType.OriginalSample)) {
            return testSampleInfoPO.getID();
        } else if (SampleType.check(testSampleInfoPO.getSampleType(), SampleType.Sample, SampleType.SubSample)) {
            return testSampleInfoPO.getSampleParentID();
        }
        return null;
    }

    private List<TestSampleInfoPO>  findGroupSample(String groupSampleId,List<TestSampleInfoPO> testSampleInfoPOList,List<TestSampleGroupInfoPO> testSampleGroupInfoPOList){
        List<TestSampleInfoPO> resultSampleList = new ArrayList<>();
        Map<String, TestSampleInfoPO> sampleInfoMap = testSampleInfoPOList.stream()
                .collect(Collectors.toMap(TestSampleInfoPO::getID, Function.identity()));
        Map<String, List<TestSampleGroupInfoPO>> groupInfoMap = testSampleGroupInfoPOList.stream()
                .collect(Collectors.groupingBy(TestSampleGroupInfoPO::getSampleID));

        List<TestSampleGroupInfoPO> testSampleGroupInfoPOS = groupInfoMap.getOrDefault(groupSampleId, Collections.emptyList());
        for (TestSampleGroupInfoPO testSampleGroupInfoPO : testSampleGroupInfoPOS) {
            disassembleGroupSample(testSampleGroupInfoPO.getSampleGroupID(), sampleInfoMap, testSampleGroupInfoPOList, resultSampleList);
        }
        // 按 ID 去重
        resultSampleList = resultSampleList.stream().collect(Collectors.collectingAndThen(Collectors.toCollection(
                () -> new TreeSet<>(Comparator.comparing(o -> o.getID()))), ArrayList::new));
        return resultSampleList;
    }

    private void disassembleGroupSample(String testSampleId, Map<String, TestSampleInfoPO> sampleInfoMap, List<TestSampleGroupInfoPO> testSampleGroupInfoPOList, List<TestSampleInfoPO> resultSampleList) {
        TestSampleInfoPO testSampleInfoPO = sampleInfoMap.get(testSampleId);
        if (testSampleInfoPO == null) {
            return;
        }
        if (SampleType.check(testSampleInfoPO.getSampleType(), SampleType.OriginalSample, SampleType.Sample, SampleType.SubSample)) {
            if (!resultSampleList.contains(testSampleInfoPO)) {
                resultSampleList.add(testSampleInfoPO);
            }
            return;
        }
        if (SampleGroupTypeEnums.check(testSampleInfoPO.getGroupType(), SampleGroupTypeEnums.MIX, SampleGroupTypeEnums.WITH)) {
            List<String> sampleGroupIdList = testSampleGroupInfoPOList.stream()
                    .filter(item -> Func.equalsSafe(item.getSampleID(), testSampleInfoPO.getID()))
                    .map(TestSampleGroupInfoPO::getSampleGroupID)
                    .distinct()
                    .collect(Collectors.toList());

            if (!sampleGroupIdList.isEmpty()) {
                sampleGroupIdList.forEach(sampleGroupId -> disassembleGroupSample(sampleGroupId, sampleInfoMap, testSampleGroupInfoPOList, resultSampleList));
            }
        }
    }




    private void dealShareSolution(SampleBreakDownReq req, GeneralOrderInstanceInfoPO order){
        logger.info("updateBreakDown.shareTestLine, OrderNo_{},Req：{}",req.getOrderNo(), req.getShareTestLine());
        String orderNo = req.getOrderNo();
        UserInfo userInfo = SecurityContextHolder.getUserInfoFillSystem();
        //查询当前Order下的关系 先删除
        ObjectReferInfoExample example = new ObjectReferInfoExample();
        example.createCriteria().andOrderNoEqualTo(orderNo);
        objectReferInfoMapper.deleteByExample(example);
        //新增传进来的数据 - shareTestLine
        List<ObjectReferInfoPO> objectReferInfoPOList = new ArrayList<>();
        if(Func.isNotEmpty(req.getShareTestLine())){
            List<ShareTestLineReq> shareTLList = req.getShareTestLine();
            boolean isChinese = this.isChinese();
            for(ShareTestLineReq shareTestLine : shareTLList){
                ObjectReferInfoPO objectReferInfoPO = new ObjectReferInfoPO();
                objectReferInfoPO.setId(UUID.randomUUID().toString());
                objectReferInfoPO.setOrderNo(orderNo);
                objectReferInfoPO.setReferOrderNo(shareTestLine.getShareFromOrderNo());
                objectReferInfoPO.setShareType(ShareType.SHARE_TEST_LINE.getCode());
                objectReferInfoPO.setShareBy(shareTestLine.getShareFromTestSampleInstanceId());
                objectReferInfoPO.setObjectId(shareTestLine.getTestLineInstanceId());
                objectReferInfoPO.setReferObjectId(shareTestLine.getShareFromTestLineInstanceId());
                objectReferInfoPO.setCreatedBy(userInfo.getRegionAccount());
                objectReferInfoPO.setCreatedDate(DateUtils.now());
                objectReferInfoPO.setModifiedBy(userInfo.getRegionAccount());
                objectReferInfoPO.setModifiedDate(DateUtils.now());
                objectReferInfoPOList.add(objectReferInfoPO);
                //处理Analyte
                //1.获取所有analyte
                TestLineEditDetailRsp detailRsp = testLineMapper.getEditTestLineInfoById(shareTestLine.getTestLineInstanceId());
                List<AnalyteSelectRsp> analyteSelectRspList = testLineQueryService.getAnalyteFromTrims(detailRsp,isChinese);
                //组装数据保存
                if(Func.isNotEmpty(analyteSelectRspList)){
                    //组装Req
                    List<SaveAnalyteReq> saveAnalyteReqList = Lists.newArrayList();
                    for(AnalyteSelectRsp analyte : analyteSelectRspList){
                        SaveAnalyteReq saveAnalyteReq = new SaveAnalyteReq();
                        saveAnalyteReq.setAnalyteBaseId(analyte.getAnalyteBaseId());
                        saveAnalyteReq.setTestAnalyteId(analyte.getTestAnalyteId());
                        saveAnalyteReqList.add(saveAnalyteReq);
                    }
                    List<AnalyteMultipleLanguageInfoPO> saveAnalyteLanguages = Lists.newArrayList();
                    List<OrderLanguageDTO> delDtoList = Lists.newArrayList();
                    List<OrderLanguageRelInfoPO> saveDtoList = Lists.newArrayList();
                    //获取需要保存的analyte对象
                    List<AnalyteInfoPO> analyteSaveList = testLineCmdService.getSaveAnalyteInstance(userInfo.getRegionAccount(),saveAnalyteReqList,shareTestLine.getTestLineInstanceId(),saveAnalyteLanguages,delDtoList,saveDtoList);
                    for (OrderLanguageDTO orderLanguageDTO : delDtoList) {
                        orderLanguageDTO.setOrderId(order.getID());
                    }
                    for (OrderLanguageRelInfoPO orderLanguageRelInfoPO : saveDtoList) {
                        orderLanguageRelInfoPO.setOrderId(order.getID());
                    }
                    //同时删除analyte 和analyte 多语言
                    analyteMapper.deleteByTestLineInstance(shareTestLine.getTestLineInstanceId());
                    if(Func.isNotEmpty(analyteSaveList)){
                        analyteMapper.batchInsert(analyteSaveList);
                    }
                    if(Func.isNotEmpty(saveAnalyteLanguages)){
                        analyteLanguageMapper.batchInsert(saveAnalyteLanguages);
                    }
                    if(Func.isNotEmpty(delDtoList)){
                        orderLanguageRelMapper.batchDelLangRels(delDtoList);
                    }
                    if(Func.isNotEmpty(saveDtoList)){
                        orderLanguageRelMapper.batchInsertOrUpdate(saveDtoList);
                    }
                }
            }
            if(Func.isNotEmpty(objectReferInfoPOList)){
                objectReferInfoMapper.insertBatch(objectReferInfoPOList);
            }
        }
    }

    private boolean isChinese() {
        String defaultLanguageCode = frameWorkClient.getPrimaryLanguageCode(ProductLineContextHolder.getProductLineCode());
        return UserInfoDefaultLanguageCodeEnums.check(defaultLanguageCode,UserInfoDefaultLanguageCodeEnums.zh_cn);
    }

    private List<AnalyteSelectRsp> getAnalyteFromTrims(TestLineEditDetailRsp detailRsp, boolean chinese){
        List<AnalyteSelectRsp> analyteList = Lists.newArrayList();
        QueryTestLineAnalyteReq queryTestLineAnalyteReq = new QueryTestLineAnalyteReq();
        List<TestLineAnalyteReq> analytes = Lists.newArrayList();
        TestLineAnalyteReq testLineAnalyteReq = new TestLineAnalyteReq();
        testLineAnalyteReq.setTestLineVersionId(detailRsp.getTestLineVersionID());
        Set<Integer> standardVersionIds = com.beust.jcommander.internal.Sets.newHashSet();
        standardVersionIds.add(detailRsp.getStandardVersionId());
        testLineAnalyteReq.setStandardVersionIds(standardVersionIds);
        List<Integer> aftifactIds = detailRsp.getAftifactIds();
        if(Func.isNotEmpty(aftifactIds)){
            Integer aid = aftifactIds.stream().filter(p->Func.isNotEmpty(p)&&0!=p).findFirst().orElse(null);
            testLineAnalyteReq.setArtifactId(Func.isEmpty(aid)?null:Long.valueOf(aid));
        }
        analytes.add(testLineAnalyteReq);
        queryTestLineAnalyteReq.setAnalytes(analytes);
        queryTestLineAnalyteReq.setCallerBU(ProductLineContextHolder.getProductLineCode());
        logger.info("analyteFacade---->getAnalyteFromTrims  request: {}", JSON.toJSONString(queryTestLineAnalyteReq));
        com.sgs.trimslocal.facade.model.common.BaseResponse<List<TestLineAnalyteRsp>> res =  analyteFacade.getTestLineAnalyteList(queryTestLineAnalyteReq);
        logger.info("analyteFacade---->getAnalyteFromTrims  response: {}", JSON.toJSONString(res));
        if(Func.isEmpty(res)||Func.isEmpty(res.getData())){
            return analyteList;
        }
        List<TestLineAnalyteRsp> trimsAnalyteList = res.getData();
        trimsAnalyteList.stream().forEach(analyte->{
            AnalyteSelectRsp analyteSelectRsp = new AnalyteSelectRsp();
            analyteSelectRsp.setAnalyteBaseId(analyte.getAnalyteBaseId());
            analyteSelectRsp.setTestAnalyteId(analyte.getTestAnalyteId());
            analyteSelectRsp.setSelectionType(analyte.getSelectionType());
            if(chinese){
                List<TestAnalyteLangReq> analyteLanguages = analyte.getLanguages();
                if(Func.isNotEmpty(analyteLanguages)){
                    analyteSelectRsp.setTestAnalyteDesc(analyteLanguages.stream()
                            .filter(language -> language.getLanguageId()== LanguageType.Chinese.getLanguageId()).findFirst()
                            .orElse(new TestAnalyteLangReq()).getTestAnalyteDesc());
                }
            }
            if(Func.isEmpty(analyteSelectRsp.getTestAnalyteDesc()))
            {
                analyteSelectRsp.setTestAnalyteDesc(analyte.getTestAnalyteDesc());
            }
            analyteList.add(analyteSelectRsp);
        });
        return analyteList;
    }

    private void dealCSPPSubcontract(String orderId, String orderNo){
        //1.获取订单下的所有TL 过滤出已经展开的CSPP
        List<String> needDelTLIdList = new ArrayList<>();
        List<TestLineInstancePO> testLineInstancePOList = testLineMapper.getTestLineByOrderId(orderId);
        if(Func.isNotEmpty(testLineInstancePOList)){
            List<TestLineInstancePO> openedCsppTL = testLineInstancePOList.stream().filter(e -> TestLineType.check(e.getTestLineType(),TestLineType.CSPP_OPEN)).collect(Collectors.toList());
            if(Func.isNotEmpty(openedCsppTL)){
                //获取tre_pp_test_line_relationship 去除订单下所有的PP TL 关系
                PPTestLineRelationshipInfoExample example = new PPTestLineRelationshipInfoExample();
                example.createCriteria().andGeneralOrderInstanceIDEqualTo(orderId);
                List<PPTestLineRelationshipInfoPO> ppTestLineRelationshipInfoPOList = ppTestLineRelationshipInfoMapper.selectByExampleWithBLOBs(example);
                Map<String,List<String>> csppRel = this.findCSPPOpen(ppTestLineRelationshipInfoPOList);
                for(TestLineInstancePO csppOpen : openedCsppTL){
                    //遍历所有的Open CSPP 查询被展开的TL 是否为NA or Cancel 如果都NA 删除cspp Opened 与 分包单的关联
                    List<String> tlIds = csppRel.get(csppOpen.getID());
                    boolean needDel = true;
                    if(Func.isNotEmpty(tlIds)){
                        //子TL不为空
                        for(String id : tlIds){
                            TestLineInstancePO testLine = testLineInstancePOList.stream().filter(e -> Func.equalsSafe(e.getID(),id)).findAny().orElse(null);
                            if(Func.isNotEmpty(testLine) && !com.sgs.framework.model.enums.TestLineStatus.check(testLine.getTestLineStatus(),com.sgs.framework.model.enums.TestLineStatus.NA, com.sgs.framework.model.enums.TestLineStatus.Cancelled)){
                                needDel = false;
                            }
                        }
                    } else {
                        needDel = false;
                    }
                    if(needDel){
                        needDelTLIdList.add(csppOpen.getID());
                    }
                }
                if(Func.isNotEmpty(needDelTLIdList)){
                    SubContractTestLineMappingExample subContractTestLineMappingExample = new SubContractTestLineMappingExample();
                    subContractTestLineMappingExample.createCriteria().andTestLineInstanceIDIn(needDelTLIdList);
                    subContractTestLineMappingMapper.deleteByExample(subContractTestLineMappingExample);
                }
                //删除后遍历分包单是否还有TL 没有则cancel
                //查询Order下的分包单 分别查询分包单下的TLMapper是否还有值
                List<String> cancelSubcontract = Lists.newArrayList();
                QuerySubContractReq querySubContractReq = new QuerySubContractReq();
                querySubContractReq.setOrderNo(orderNo);
                List<QuerySubContractRsp> subcontractList = subContractMapper.querySubcontract(querySubContractReq);
                if(Func.isNotEmpty(subcontractList)){
                    for(QuerySubContractRsp sub : subcontractList){
                        SubContractTestLineMappingExample subContractTestLineMappingExample = new SubContractTestLineMappingExample();
                        subContractTestLineMappingExample.createCriteria().andSubContractIDEqualTo(sub.getId());
                        List<SubContractTestLineMappingPO> mapperList = subContractTestLineMappingMapper.selectByExample(subContractTestLineMappingExample);
                        if(Func.isEmpty(mapperList)){
                            cancelSubcontract.add(sub.getId());
                        }
                    }
                    if(Func.isNotEmpty(cancelSubcontract)){
                        subContractMapper.batchUpdateSubContractStatus(SubContractStatus.CANCELLED.getCode(),"Bom",cancelSubcontract);
                    }
                }
            }
        }
    }

    private Map<String,List<String>> findCSPPOpen(List<PPTestLineRelationshipInfoPO> ppTestLineRelationshipInfoPOList){
        Map<String, List<String>> testLineMap = new HashMap<>();
        if(Func.isNotEmpty(ppTestLineRelationshipInfoPOList)){
            ppTestLineRelationshipInfoPOList.stream().forEach(po ->{
                if(Func.isNotEmpty(po.getExtFields())){
                    String parentTestLineInstanceId = Func.toStr(JSONObject.parseObject(po.getExtFields()).getOrDefault("parentTestLineInstanceId", ""));
                    if(Func.isNotEmpty(parentTestLineInstanceId)){
                        if(testLineMap.containsKey(parentTestLineInstanceId)){
                            testLineMap.get(parentTestLineInstanceId).add(po.getTestLineInstanceID());
                        } else {
                            testLineMap.put(parentTestLineInstanceId,Lists.newArrayList(po.getTestLineInstanceID()));
                        }
                    }
                }
            });
        }
        return testLineMap;
    }

    /**
     * 校验是否含有重复assign的sample
     *
     * @param reqObject
     */
    private void verifyDuplicateSamples(SampleBreakDownReq reqObject, String orderId) {
        Map<String, List<TestMatrixReq>> matrixMaps = reqObject.getMatrixs().stream().collect(
                Collectors.groupingBy(o -> o.getSampleId().toLowerCase()+ "&" + o.getTestLineInstanceId().toLowerCase() + "&" + o.getPpId()));
        Map<String, String> sampleNoMap = reqObject.getSamples().stream().collect(Collectors.toMap(o -> o.getSampleId(), o -> o.getSampleNo()));
        Map<String, PPRelationShipRsp> relationShipRspMap = ppBaseMapper.getPpBaseInfoByOrderId(orderId).stream().collect(Collectors.toMap(o -> o.getPpRelationId(), o -> o));

        StringBuilder sb = new StringBuilder();
        matrixMaps.forEach((k ,v) -> {
            if (v.size() <= 1) {
                return;
            }
            String[] idArr = k.split("&");
            TestLineInstancePO testLineInstancePO = testLineMapper.getBaseTestLineById(idArr[1]);
            String sampleNo = sampleNoMap.get(idArr[0]);
            PPRelationShipRsp ppRelationShipRsp = relationShipRspMap.get(idArr[2]);
            String ppNo = Objects.isNull(ppRelationShipRsp) ? "" : ppRelationShipRsp.getPpNo() + "";
            sb.append(String.format(Constants.DUPLICATE_ASSIGN_SAMPLE_MSG, ppNo, testLineInstancePO.getTestLineID(), sampleNo)).append(";");
        });
        String errMsg = sb.toString();
        if (StringUtils.isNoneEmpty(errMsg)) {
            throw new BizException(errMsg);
        }
    }
    /**
     *
     * @param orderId
     * @param testLines
     */
    private void matrixUpdateCondition(String orderId, List<TestLineInstancePO> testLines){
        List<TestMatrixPO> testMatrixs = testMatrixMapper.getTestMatrixByOrderId(orderId);
        if (testMatrixs == null){
            testMatrixs = Lists.newArrayList();
        }
        Map<String, TestMatrixPO> testMatrixMaps = Maps.newHashMap();
        // Matrix里TestLine对应Assign Sample的数量
        Map<String, Set<String>> matrixTestLineMaps = Maps.newHashMap();
        testMatrixs.forEach(testMatrix->{
            String testLineId = testMatrix.getTestLineInstanceID();
            if (!matrixTestLineMaps.containsKey(testLineId)){
                matrixTestLineMaps.put(testLineId, Sets.newHashSet());
            }
            matrixTestLineMaps.get(testLineId).add(testMatrix.getID());
            testMatrixMaps.put(testMatrix.getID(), testMatrix);
        });
        List<String> delConditionIds = Lists.newArrayList();
        List<TestConditionInfoPO> testConditions = testConditionMapper.getTestConditionListByOrderId(orderId);
        for (TestConditionInfoPO testCondition: testConditions){
            // 如果不存在Matrix，则删除Condition
            if (!testMatrixMaps.containsKey(testCondition.getTestMatrixID())){
                delConditionIds.add(testCondition.getID());
                continue;
            }
            String testLineId = testCondition.getTestLineInstanceID();
            if (matrixTestLineMaps.containsKey(testLineId)){
                matrixTestLineMaps.get(testLineId).remove(testCondition.getTestMatrixID());
            }
        }

        if (!delConditionIds.isEmpty()){
            testConditionMapper.delTestConditionByConditionIds(delConditionIds);
        }

        if (testLines == null || testLines.isEmpty()){
            return;
        }
        List<TestLineInstancePO> updateConditionStatus = Lists.newArrayList();
        for (TestLineInstancePO testLine: testLines){
            testLine.setModifiedDate(DateUtils.getNow());
            ConditionStatus conditionStatus = ConditionStatus.findStatus(testLine.getConditionStatus());
            if (conditionStatus == null || conditionStatus == ConditionStatus.NoCondition){
                // TODO 未找对应的ConditionStatus
                continue;
            }
            // 该Test Line 是否有Assign过Sample，如果没有则判断他原先是否设过Confirmed
            if (!matrixTestLineMaps.containsKey(testLine.getID())){
                if (conditionStatus != ConditionStatus.UnConfirmed){
                    testLine.setConditionStatus(ConditionStatus.UnConfirmed.getStatus());
                    updateConditionStatus.add(testLine);
                }
                continue;
            }
            Set<String> testMatrixIds = matrixTestLineMaps.get(testLine.getID());
            if (testMatrixIds == null || testMatrixIds.isEmpty()){
                testLine.setConditionStatus(ConditionStatus.Confirmed.getStatus());
                updateConditionStatus.add(testLine);
                continue;
            }
            if (conditionStatus == ConditionStatus.Confirmed){
                testLine.setConditionStatus(ConditionStatus.UnConfirmed.getStatus());
                updateConditionStatus.add(testLine);
            }
        }
        if (updateConditionStatus.isEmpty()){
            return;
        }
        testLineMapper.updateBatchConditionStatus(updateConditionStatus);
    }

    /**
     *
     * @param reqObject
     * @return
     */
    private CustomResult checkSampleParameters(SampleBreakDownReq reqObject){
        CustomResult rspReuslt = new CustomResult();
        if (reqObject == null) {
            rspReuslt.setMsg("请求对象不能为空.");
            return rspReuslt;
        }
        if (StringUtils.isBlank(reqObject.getOrderNo())) {
            rspReuslt.setMsg("请求的OrderNo. 不能为空.");
            return rspReuslt;
        }
        List<TestSampleReq> samples = reqObject.getSamples();
        if (samples == null || samples.isEmpty()){
            rspReuslt.setMsg("请求的samples不能为空.");
            return rspReuslt;
        }
        //校验sample是否重复
        rspReuslt = this.checkSampleRepeat(samples);
        if(!rspReuslt.isSuccess()){
            return rspReuslt;
        }
        rspReuslt = this.checkSampleTree(samples);
        if (!rspReuslt.isSuccess()){
            return rspReuslt;
        }
        rspReuslt = new CustomResult();
        List<SampleGroupReq> groups = reqObject.getGroups();
        if (groups == null){
            groups = Lists.newArrayList();
        }
        List<TestMatrixReq> matrixs = reqObject.getMatrixs();
        if (matrixs == null){
            matrixs = Lists.newArrayList();
        }
        Map<String, Boolean> sampleIds = Maps.newHashMap();
        for (TestSampleReq sample: samples) {
            sample.setChem(false);
            SampleType sampleType = SampleType.findType(sample.getSampleType());
            if (sampleType == null) {
                rspReuslt.setMsg(String.format("当前SampleNo(%s)，SampleType类型无效.", sample.getSampleNo()));
                return rspReuslt;
            }
            if (StringUtils.isBlank(sample.getSampleNo())){
                rspReuslt.setMsg(String.format("当前Sample(%s)，SampleNo不能为空.", sample.getSampleId()));
                return rspReuslt;
            }
//            if (StringUtils.isBlank(sample.getSampleDesc())){
//                rspReuslt.setMsg(String.format("当前SampleNo(%s)，SampleDesc不能为空.", sample.getSampleNo()));
//                return rspReuslt;
//            }
            if (!(sampleType == SampleType.OriginalSample || sampleType == SampleType.MixSample) && (sample.getSampleSeq() == null || sample.getSampleSeq().intValue() <= 0)){
                rspReuslt.setMsg(String.format("当前SampleNo(%s)，SampleSeq不能为空或为小于零.", sample.getSampleNo()));
                return rspReuslt;
            }
            if (sample.getNoTest() == null){
                sample.setNoTest(false);
            }
            if (StringUtils.isNotBlank(sample.getExecutionSystemSampleId())){
                sample.setExecutionSystemSampleId(sample.getExecutionSystemSampleId().toLowerCase());
            }
            if (StringUtils.isNotBlank(sample.getSampleId())){
                sample.setSampleId(sample.getSampleId().toLowerCase());
            }
            if (StringUtils.isNotBlank(sample.getSampleParentId())){
                sample.setSampleParentId(sample.getSampleParentId().toLowerCase());
            }
            switch (sampleType){
                case Sample:
                    if (!NumberUtil.isNumber(sample.getSampleNo())){
                        rspReuslt.setMsg(String.format("当前SampleNo(%s)只能是非零的数字.", sample.getSampleNo()));
                        return rspReuslt;
                    }
                    sample.setSampleNo(String.format("%s%s", sampleType.getCategoryChem(), sample.getSampleNo()));
                    break;
                case SubSample:
                    if (!NumberUtil.isLetterDigit(sample.getSampleNo())){
                        rspReuslt.setMsg(String.format("当前SampleNo(%s)只能是数字+字母.", sample.getSampleNo()));
                        return rspReuslt;
                    }
                    sample.setSampleNo(String.format("%s%s", sampleType.getCategoryChem(), sample.getSampleNo()));
                    break;
                case MixSample:
                    if (StringUtils.isNotBlank(sample.getSampleParentId())){
                        rspReuslt.setMsg(String.format("当前SampleNo(%s)的SampleParentId必须为空.", sample.getSampleNo()));
                        return rspReuslt;
                    }
                    break;
                case ShareSample:
                    sample.setSampleNo(String.format("%s%s", sampleType.getCategoryChem(), sample.getSampleNo()));
                    break;
            }
            if (sampleType != SampleType.OriginalSample) {
                sampleIds.put(sample.getSampleId(), sample.getNoTest());
                continue;
            }
            String oldSampleId = sample.getSampleId();
            sample.setSampleId(sample.getExecutionSystemSampleId());
            sampleIds.put(sample.getSampleId(), sample.getNoTest());

            samples.forEach(sampleItem->{
                if (StringUtils.equalsIgnoreCase(sampleItem.getSampleParentId(), oldSampleId)){
                    sampleItem.setSampleParentId(sample.getSampleId());
                }
            });
            groups.forEach(group->{
                if (StringUtils.equalsIgnoreCase(group.getSampleGroupId(), oldSampleId)){
                    group.setSampleGroupId(sample.getSampleId());
                }
                if (StringUtils.equalsIgnoreCase(group.getSampleId(), oldSampleId)){
                    group.setSampleId(sample.getSampleId());
                }
            });
            matrixs.forEach(matrix->{
                if (StringUtils.equalsIgnoreCase(matrix.getSampleId(), oldSampleId)){
                    matrix.setSampleId(sample.getSampleId());
                }
            });
        }

        for (SampleGroupReq group: groups){
            if (StringUtils.isNotBlank(group.getSampleGroupId())){
                group.setSampleGroupId(group.getSampleGroupId().toLowerCase());
            }
            if (StringUtils.isNotBlank(group.getSampleId())){
                group.setSampleId(group.getSampleId().toLowerCase());
            }
            if (!sampleIds.containsKey(group.getSampleGroupId())){
                rspReuslt.setMsg(String.format("Groups集合对象里的SampleGroupId(%s)无效.", group.getSampleGroupId()));
                return rspReuslt;
            }
            if (!sampleIds.containsKey(group.getSampleId())){
                rspReuslt.setMsg(String.format("Groups集合对象里的SampleId(%s)无效.", group.getSampleId()));
                return rspReuslt;
            }
        }

        for (TestMatrixReq matrix: matrixs){
            if (StringUtils.isNotBlank(matrix.getPpId())){
                matrix.setPpId(matrix.getPpId().toLowerCase());
            }
            if (StringUtils.isNotBlank(matrix.getSampleId())){
                matrix.setSampleId(matrix.getSampleId().toLowerCase());
            }
            if (StringUtils.isNotBlank(matrix.getTestLineInstanceId())){
                matrix.setTestLineInstanceId(matrix.getTestLineInstanceId().toLowerCase());
            }
            if (sampleIds.containsKey(matrix.getSampleId()) && sampleIds.get(matrix.getSampleId())){
                rspReuslt.setMsg(String.format("当前Sample(%s)noTest设置为True，无法Matrix.", matrix.getSampleId()));
                return rspReuslt;
            }
        }

        if(! CollectionUtils.isEmpty(reqObject.getSlimMapping())) {
            for (SlimMappingReq slimMappingReq : reqObject.getSlimMapping()) {
                if(StringUtils.isBlank(slimMappingReq.getReportScheme()) && StringUtils.isBlank(slimMappingReq.getTestScheme())){
    //                throw new IllegalArgumentException("sample "+slimMappingReq.getSampleNo()+" testline " + slimMappingReq.getTestLineID() +" has no scheme");
                    rspReuslt.setMsg(String.format("sample(%s) testline(%s)  has no scheme", slimMappingReq.getSampleNo(),slimMappingReq.getTestLineID()));
                    return rspReuslt;
                }

                if(StringUtils.isBlank(slimMappingReq.getSampleNo())){
                    //throw new IllegalArgumentException(" testline " + slimMappingReq.getTestLineID() +" has no sample");
                    rspReuslt.setMsg(String.format("testline(%s) has no sample", slimMappingReq.getTestLineID()));
                    return rspReuslt;
                }

                List<String> schemes = Lists.newArrayList();
                if(StringUtils.isNotBlank(slimMappingReq.getTestScheme())) {
                    String[] testSchemes = doReplaceAndSplit(slimMappingReq.getTestScheme());
                    schemes.addAll(Arrays.asList(testSchemes));
                }
                if(StringUtils.isNotBlank(slimMappingReq.getReportScheme())){
                    String[] reportSchemes = doReplaceAndSplit(slimMappingReq.getReportScheme());
                    schemes.addAll(Arrays.asList(reportSchemes));
                }
                //schemes 必须要有 至少一条
                if(schemes.isEmpty()){
    //                throw new IllegalArgumentException("sample "+slimMappingReq.getSampleNo()+" testline " + slimMappingReq.getTestLineID() +" has no scheme");
                    rspReuslt.setMsg("Test Scheme or Report Scheme has no value");
                }

            }
        }
        rspReuslt.setSuccess(true);
        return rspReuslt;
    }

    /**
     * add by vincent 2020年4月10日11:44:45
     * 校验bom接口回传数据中 sampleNo是否重复
     * @param samples
     * @return
     */
    private CustomResult checkSampleRepeat(List<TestSampleReq> samples){
        CustomResult rspReuslt = new CustomResult();
        rspReuslt.setSuccess(true);
        List<String> allSample = samples.stream().map(sample -> sample.getSampleNo().toUpperCase()).collect(Collectors.toList());
        Set allSampleSet = new HashSet<>();
        allSampleSet.addAll(allSample);
        if(allSample.size()!=allSampleSet.size()){
            rspReuslt.setSuccess(false);
            //找到重复的sample，返回提示信息
            Map<String,Boolean> map = new HashMap<>();
            Set<String> repeatSampleNo = new HashSet<>();
            for (String no : allSample) {
                no = no.trim();
                Boolean isNo = map.get(no);
                if(isNo!=null && isNo){
                    repeatSampleNo.add(no);
                }else{
                    map.put(no,true);
                }
            }
            rspReuslt.setMsg("Sample No[ "+StringUtils.join(repeatSampleNo,",")+" ] can't be repeat");
        }
        return rspReuslt;
    }

    /**
     *
     * @param samples
     * @return
     */
    private CustomResult checkSampleTree(List<TestSampleReq> samples){
        CustomResult rspReuslt = new CustomResult();
        if (samples == null || samples.isEmpty()){
            rspReuslt.setMsg("请求的samples不能为空.");
            return rspReuslt;
        }
        for (TestSampleReq sample: samples) {
            SampleType sampleType = SampleType.findType(sample.getSampleType());
            if (sampleType == null) {
                rspReuslt.setMsg(String.format("当前SampleNo(%s)，SampleType类型无效.", sample.getSampleNo()));
                return rspReuslt;
            }
            switch (sampleType){
                case Sample:
                case SubSample:
                case ShareSample:
                    SampleType parentSampleType = this.findParentSampleType(samples, sample);
                    if (parentSampleType == null || parentSampleType != SampleType.OriginalSample) {
                        rspReuslt.setMsg(String.format("未找到对应的sample(%s) 的父样.", sample.getSampleNo()));
                        return rspReuslt;
                    }
                    break;
            }
        }
        rspReuslt.setSuccess(true);
        return rspReuslt;
    }

    /**
     *
     * @param testSamples
     * @param parentSample
     */
    private void recalculateSort(List<TestSampleReq> testSamples, TestSampleReq parentSample){
        if (testSamples == null || testSamples.isEmpty()){
            return;
        }
        if (parentSample == null){
            parentSample = new TestSampleReq();
        }
        String parentSampleId = parentSample.getSampleId();
        List<TestSampleReq> samples = testSamples.stream().filter(sample -> StringUtil.equalsIgnoreCase(sample.getSampleParentId(), parentSampleId)).collect(Collectors.toList());
        if (samples == null || samples.isEmpty()){
            return;
        }
        samples.sort(new TestSampleComparator(true));
        //int sampleSeq = 101000000;
        // 104 - 999000001 - 1000000
        int sampleSeq = 1, mixSampleSeq = 1;
        for (TestSampleReq sample: samples) {
            SampleType sampleType = SampleType.findType(sample.getSampleType());
            switch (sampleType){
                case OriginalSample:
                    sample.setSampleSeq(sampleType.getSampleSeq() + sampleType.getSeed() * sampleSeq++);
                    break;
                case Sample:
                case ShareSample:
                    sample.setSampleSeq(parentSample.getSampleSeq() + sampleType.getSampleSeq() + sampleType.getSeed() * sampleSeq++);
                    break;
                case SubSample:
                    sample.setSampleSeq(parentSample.getSampleSeq() + sampleSeq++);
                    break;
                case MixSample:
                    sample.setSampleSeq(sampleType.getSampleSeq() + mixSampleSeq++);
                    break;
            }
            this.recalculateSort(testSamples, sample);
        }
    }

    /**
     *
     * @param reqObject
     */
    private CustomResult resetMixSampleNo(SampleBreakDownReq reqObject){
        CustomResult rspReuslt = new CustomResult();
        List<SampleGroupReq> groups = reqObject.getGroups();
        if (groups == null || groups.isEmpty()){
            rspReuslt.setSuccess(true);
            return rspReuslt;
        }
        List<TestSampleReq> testSamples = reqObject.getSamples();
        if (testSamples == null || testSamples.isEmpty()){
            rspReuslt.setSuccess(true);
            return rspReuslt;
        }
        SortedMap<Integer, String> sampleNos = Maps.newTreeMap();
        for (TestSampleReq testSample: testSamples){
            SampleType sampleType = SampleType.findType(testSample.getSampleType());
            if (sampleType == null || sampleType != SampleType.MixSample){
                continue;
            }
            List<SampleGroupReq> sampleGroups = groups.stream().filter(group -> StringUtils.equalsIgnoreCase(group.getSampleId(), testSample.getSampleId())).collect(Collectors.toList());
            if (sampleGroups == null || sampleGroups.isEmpty()){
                continue;
            }
            sampleNos.clear();
            for (SampleGroupReq group: sampleGroups){
                TestSampleReq groupSample = testSamples.stream().filter(sample -> StringUtils.equalsIgnoreCase(sample.getSampleId(), group.getSampleGroupId())).findFirst().orElse(null);
                if (groupSample == null){
                    continue;
                }
                SampleType mixSampleType = SampleType.findType(groupSample.getSampleType());
                if (mixSampleType == SampleType.MixSample && SampleGroupTypeEnums.check(groupSample.getGroupType(),SampleGroupTypeEnums.MIX)){
                    rspReuslt.setMsg(String.format("当前Sample(%s)已是Mix样，不能再做Mix Sample.", groupSample.getSampleNo()));
                    return rspReuslt;
                }
                if (mixSampleType != SampleType.OriginalSample){
                    testSample.setChem(true);
                }
                sampleNos.put(group.getSequence(), groupSample.getSampleNo());
            }
            if (sampleNos.isEmpty()){
                continue;
            }
            if(SampleGroupTypeEnums.check(testSample.getGroupType(),SampleGroupTypeEnums.MIX)){
                testSample.setSampleNo(StringUtils.join(sampleNos.values(), "+"));
            } else if(SampleGroupTypeEnums.check(testSample.getGroupType(),SampleGroupTypeEnums.WITH)){
                testSample.setSampleNo(StringUtils.join(sampleNos.values(),"w/"));
            }
        }
        rspReuslt.setSuccess(true);
        return rspReuslt;
    }

    /**
     * 检查是否NC
     * 1、是否Assign Sample(即matrix)
     * 2、当前是否有子样
     * 3、是否有Mix、Share
     * @param reqObject
     * @param oldSamples
     * @param oldMatrixSampleIds
     * @param oldSampleGroups
     * @return
     */
    private CustomResult checkSampleNc(SampleBreakDownReq reqObject, List<TestSampleInfoPO> oldSamples, Map<String, Set<String>> oldMatrixSampleIds, List<TestSampleGroupInfoPO> oldSampleGroups){
        CustomResult rspReuslt = new CustomResult();
        List<TestSampleReq> samples = reqObject.getSamples();
        if (samples == null || samples.isEmpty()){
            rspReuslt.setMsg("未找到对应的Samples.");
            return rspReuslt;
        }
        if (oldSamples == null){
            oldSamples = Lists.newArrayList();
        }
        Map<String, Boolean> oldSampleIdNCMap = Maps.newHashMap();
        if(!CollectionUtils.isEmpty(oldSamples)){
            oldSampleIdNCMap = oldSamples.stream().collect(Collectors.toMap(TestSampleInfoPO::getID, TestSampleInfoPO::getApplicable, (k1, k2) -> k1));
        }
        if (oldSampleGroups == null){
            oldSampleGroups = Lists.newArrayList();
        }
        List<SampleGroupReq> groups = reqObject.getGroups();
        if (groups == null){
            groups = Lists.newArrayList();
        }
        List<TestMatrixReq> matrixs = reqObject.getMatrixs();
        if (matrixs == null){
            matrixs = Lists.newArrayList();
        }
        for (TestSampleReq sample: samples){
            String sampleParentId = sample.getSampleParentId();
            //因为目前Bom 不回传NC样品了，所以这里，如果是子样的话，需要校验原样是否已经被NC了
            //既然回来了原样，那就说明在Notes不是NC的
            if ( sample.getNoTest() == null || !sample.getNoTest().booleanValue()){
                continue;
            }
            SampleType sampleType = SampleType.findType(sample.getSampleType());
            if (sampleType == SampleType.OriginalSample){
                rspReuslt.setMsg(String.format("当前原样SampleNo(%s)不能NoTest.", sample.getSampleNo()));
                return rspReuslt;
            }
            //这一段的判断逻辑是，利用目前DB中存在的Sample parentId，来找传递进来的NC的SampleID
            Boolean currentDBHasChildSample = oldSamples.stream().filter(oldSample -> StringUtils.equalsIgnoreCase(oldSample.getSampleParentID(), sample.getSampleId())).count() > 0;
            //这一段的判断逻辑是，传递的参数的Sample 是NC的，但是又有子样
            Boolean currentParamDataHasChildSample = samples.stream().filter(newSample -> StringUtils.equalsIgnoreCase(newSample.getSampleParentId(), sample.getSampleId())).count() > 0;
            // 如果有关联关系，那么传递进来的sample 就是有子样，但是又做了NC，业务上是不允许的
            if (currentDBHasChildSample || currentParamDataHasChildSample ){
                rspReuslt.setMsg(String.format("当前SampleNo(%s)存在子样不能NoTest.", sample.getSampleNo()));
                return rspReuslt;
            }
            // 是否Assign Sample(即matrix)
            if (oldMatrixSampleIds.containsKey(sample.getSampleId()) ||
                matrixs.stream().filter(matrix -> StringUtils.equalsIgnoreCase(matrix.getSampleId(), sample.getSampleId())).count() > 0){
                rspReuslt.setMsg(String.format("当前SampleNo(%s)已Assign Sample不能NoTest.", sample.getSampleNo()));
                return rspReuslt;
            }
            //
            if (oldSampleGroups.stream().filter(sampleGroup -> StringUtils.equalsIgnoreCase(sampleGroup.getSampleGroupID(), sample.getSampleId())).count() > 0 ||
                groups.stream().filter(sampleGroup -> StringUtils.equalsIgnoreCase(sampleGroup.getSampleGroupId(), sample.getSampleId())).count() > 0){
                rspReuslt.setMsg(String.format("当前SampleNo(%s)已Share或Mix不能NoTest.", sample.getSampleNo()));
                return rspReuslt;
            }
        }
        rspReuslt.setSuccess(true);
        return rspReuslt;
    }

    /**
     *
     * @param orderId
     * @param reqObject
     * @param oldPPSampleRelMaps
     * @param newSampleIds
     * @param oldTestMatrixMaps
     * @param testLineMaps
     * @return
     */
    private CustomResult handleTestMatrix(
            String orderId,
            SampleBreakDownReq reqObject,
            Map<String, PPTestLineRelInfo> oldPPSampleRelMaps,
            Set<String> newSampleIds,
            Map<String, TestMatrixPO> oldTestMatrixMaps,
            Map<String, TestLineInstancePO> testLineMaps,
            List<TestMatrixExtPO> testMatrixExtInsertList,
            List<TestMatrixExtPO> testMatrixExtUpdateList
    ){
        CustomResult rspReuslt = new CustomResult();
        List<TestMatrixReq> matrixs = reqObject.getMatrixs();
        if (matrixs == null || matrixs.isEmpty()) {
            rspReuslt.setSuccess(true);
            rspReuslt.setData(Lists.newArrayList());
            return rspReuslt;
        }
        List<String> oldTestMatrixIds = new ArrayList<>();
        List<TestMatrixExtPO> oldTestMatrixExtList = new ArrayList<>();
        Map<String,TestMatrixExtPO> oldTestMatrixExtMap = new HashMap<>();
        if(Func.isNotEmpty(oldTestMatrixMaps)){
            //遍历拿到之前的MatrixId
            for(TestMatrixPO item : oldTestMatrixMaps.values()){
                if(Func.isNotEmpty(item.getID())){
                    oldTestMatrixIds.add(item.getID());
                }
            }
            //查询之前是否存在MatrixExt的信息
            if(Func.isNotEmpty(oldTestMatrixIds)){
                oldTestMatrixExtList = testMatrixExtMapper.selectByTestMatrixIds(oldTestMatrixIds);
            }
            if(Func.isNotEmpty(oldTestMatrixExtList)){
                for(TestMatrixExtPO item : oldTestMatrixExtList){
                    oldTestMatrixExtMap.put(item.getTestMatrixId(),item);
                }
            }
        }
        Set<String> testMatrixIds = Sets.newHashSet();
        List<TestMatrixPO> testMatrixs = Lists.newArrayList();
        TestMatrixPO testMatrix;
        for (TestMatrixReq matrix : matrixs) {
            String testLineId = matrix.getTestLineInstanceId();
            if (StringUtils.isBlank(matrix.getPpId()) || StringUtils.isBlank(matrix.getSampleId()) || StringUtils.isBlank(testLineId)) {
                rspReuslt.setMsg("matrix 对象ppId、SampleId、TestLineInstanceId为空.");
                return rspReuslt;
            }
            matrix.setPpId(matrix.getPpId().toLowerCase());
            matrix.setSampleId(matrix.getSampleId().toLowerCase());
            testLineId = testLineId.toLowerCase();
            matrix.setTestLineInstanceId(testLineId);
            if (!newSampleIds.contains(matrix.getSampleId())) {
                rspReuslt.setMsg(String.format("未找到对的SampleId(%s).", matrix.getSampleId()));
                return rspReuslt;
            }
            if (!testLineMaps.containsKey(testLineId)) {
                rspReuslt.setMsg(String.format("未找到对的TestLineInstanceId(%s).", testLineId));
                return rspReuslt;
            }
            String tlRelKey = String.format("%s_%s", matrix.getPpId(), matrix.getTestLineInstanceId());
            PPTestLineRelInfo ppTestLineRel = oldPPSampleRelMaps.get(tlRelKey);
            if (ppTestLineRel == null) {
                rspReuslt.setMsg(String.format("未找到对的PpId(%s).", matrix.getPpId()));
                return rspReuslt;
            }
            if (!StringUtils.equalsIgnoreCase(ppTestLineRel.getTestLineId(), testLineId)){
                rspReuslt.setMsg(String.format("该PpId(%s)下未找到对应的testLineId(%s).", matrix.getPpId(), testLineId));
                return rspReuslt;
            }
            Map<String, String> ppSampleRelMaps = ppTestLineRel.getPpSampleRelMaps();
            Set<String> newSampleRels = ppTestLineRel.getNewSampleRels();
            String matrixKey = String.format("%s_%s", matrix.getSampleId(), testLineId);
            if (oldTestMatrixMaps.containsKey(matrixKey)){
                //保存新的MatrixExt
                TestMatrixExtPO testMatrixExtPO = new TestMatrixExtPO();
                testMatrixExtPO.setTestMatrixId(oldTestMatrixMaps.get(matrixKey).getID());
                if(Func.isNotEmpty(matrix.getApplicationFactorId())){
                    testMatrixExtPO.setApplicationFactorId(JSON.toJSONString(matrix.getApplicationFactorId()));
                }
                testMatrixExtPO.setCreatedBy(reqObject.getUserName());
                testMatrixExtPO.setCreatedDate(DateUtils.getNow());
                testMatrixExtPO.setModifiedBy(reqObject.getUserName());
                testMatrixExtPO.setModifiedDate(DateUtils.getNow());
                if(oldTestMatrixExtMap.containsKey(testMatrixExtPO.getTestMatrixId())){
                    testMatrixExtUpdateList.add(testMatrixExtPO);
                } else {
                    testMatrixExtInsertList.add(testMatrixExtPO);
                }
                oldTestMatrixMaps.remove(matrixKey);
                ppSampleRelMaps = ppTestLineRel.getPpSampleRelMaps();
                if (ppSampleRelMaps.containsKey(matrix.getSampleId())){
                    ppSampleRelMaps.remove(matrix.getSampleId());
                }else{
                    newSampleRels.add(matrix.getSampleId());
                }
                continue;
            }
            if (testMatrixIds.contains(matrixKey)){
                if (ppSampleRelMaps.containsKey(matrix.getSampleId())){
                    ppSampleRelMaps.remove(matrix.getSampleId());
                }else{
                    newSampleRels.add(matrix.getSampleId());
                }
                continue;
            }
            TestLineInstancePO testLine = testLineMaps.get(testLineId);
            TestLineStatus testLineStatus = TestLineStatus.findStatus(testLine.getTestLineStatus());
            if (testLineStatus == null || !(testLineStatus == TestLineStatus.Typing || testLineStatus == TestLineStatus.DR)) {
                rspReuslt.setMsg(String.format("当前TestLineId(%s)，TestLineStatus为(%s)不能建立Matrix.", testLineId, testLineStatus));
                return rspReuslt;
            }
            testMatrixIds.add(matrixKey);
            newSampleRels.add(matrix.getSampleId());

            testMatrix = new TestMatrixPO();
            testMatrix.setID(UUID.randomUUID().toString());
            testMatrix.setGeneralOrderInstanceID(orderId);
            testMatrix.setTestSampleID(matrix.getSampleId());
            testMatrix.setTestLineInstanceID(testLineId);
            testMatrix.setMatrixGroupId(0);
            testMatrix.setMatrixConfirmDate(DateUtils.getNow());
            testMatrix.setActiveIndicator(true);
            testMatrix.setMatrixStatus(MatrixStatus.Typing.getStatus());
            testMatrix.setCreatedBy(reqObject.getUserName());
            testMatrix.setCreatedDate(DateUtils.getNow());
            testMatrix.setModifiedBy(reqObject.getUserName());
            testMatrix.setModifiedDate(DateUtils.getNow());

            //组装TestMatrixExt数据
            TestMatrixExtPO testMatrixExtPO = new TestMatrixExtPO();
            testMatrixExtPO.setTestMatrixId(testMatrix.getID());
            if(Func.isNotEmpty(matrix.getApplicationFactorId())){
                testMatrixExtPO.setApplicationFactorId(JSON.toJSONString(matrix.getApplicationFactorId()));
            }
            testMatrixExtPO.setApplicationFactorId(JSON.toJSONString(matrix.getApplicationFactorId()));
            testMatrixExtPO.setCreatedBy(reqObject.getUserName());
            testMatrixExtPO.setCreatedDate(DateUtils.getNow());
            testMatrixExtPO.setModifiedBy(reqObject.getUserName());
            testMatrixExtPO.setModifiedDate(DateUtils.getNow());
            if(Func.isNotEmpty(testMatrixExtPO.getApplicationFactorId())){
                testMatrixExtInsertList.add(testMatrixExtPO);
            }
            testMatrixs.add(testMatrix);
        }
        rspReuslt.setSuccess(true);
        rspReuslt.setData(testMatrixs);
        return rspReuslt;
    }

    /**
     *
     * @param oldPPSampleRelMaps
     * @param userName
     * @return
     */
    private List<PPSampleRelationshipInfoPO> handlePPSampleRel(Map<String, PPTestLineRelInfo> oldPPSampleRelMaps, List<String> delPPSampleRelIds, String userName){
        List<PPSampleRelationshipInfoPO> ppSampleRels = Lists.newArrayList();
        if (oldPPSampleRelMaps == null || oldPPSampleRelMaps.isEmpty()){
            return ppSampleRels;
        }
        PPSampleRelationshipInfoPO ppSampleRel;
        Iterator<Map.Entry<String, PPTestLineRelInfo>> ppSampleRelMaps = oldPPSampleRelMaps.entrySet().iterator();
        while (ppSampleRelMaps.hasNext()) {
            Map.Entry<String, PPTestLineRelInfo> entry = ppSampleRelMaps.next();
            PPTestLineRelInfo testLineRel = entry.getValue();
            if (testLineRel == null){
                continue;
            }
            Map<String, String> oldPPSampleRels = testLineRel.getPpSampleRelMaps();
            if (oldPPSampleRels != null && oldPPSampleRels.size() > 0){
                delPPSampleRelIds.addAll(oldPPSampleRels.values());
            }
            Set<String> newSampleRels = testLineRel.getNewSampleRels();
            if (newSampleRels == null || newSampleRels.isEmpty()){
                continue;
            }
            for (String sampleId: newSampleRels){
                ppSampleRel = new PPSampleRelationshipInfoPO();
                ppSampleRel.setID(UUID.randomUUID().toString());
                ppSampleRel.setPPTLRelID(testLineRel.getPpTLRelId());
                ppSampleRel.setTestSampleID(sampleId);

                ppSampleRel.setCreatedBy(userName);
                ppSampleRel.setCreatedDate(DateUtils.getNow());
                ppSampleRel.setModifiedBy(userName);
                ppSampleRel.setModifiedDate(DateUtils.getNow());
                ppSampleRels.add(ppSampleRel);
            }
        }
        return ppSampleRels;
    }

    /**
     *
     * @param newTestMatrixs
     * @param reportId
     * @param userName
     * @return
     */
    private List<ReportMatrixRelationShipInfoPO> handleReportMatrixRel(List<TestMatrixPO> newTestMatrixs, String reportId, String userName){
        List<ReportMatrixRelationShipInfoPO> reportMatrixRels = Lists.newArrayList();
        if (newTestMatrixs == null || newTestMatrixs.isEmpty()){
            return reportMatrixRels;
        }
        ReportMatrixRelationShipInfoPO reportMatrixRel;
        for (TestMatrixPO matrix: newTestMatrixs){
            reportMatrixRel = new ReportMatrixRelationShipInfoPO();
            reportMatrixRel.setID(UUID.randomUUID().toString());
            reportMatrixRel.setReportID(reportId);
            reportMatrixRel.setTestMatrixID(matrix.getID());

            reportMatrixRel.setCreatedBy(userName);
            reportMatrixRel.setCreatedDate(DateUtils.getNow());
            reportMatrixRel.setModifiedBy(userName);
            reportMatrixRel.setModifiedDate(DateUtils.getNow());
            reportMatrixRels.add(reportMatrixRel);
        }
        return reportMatrixRels;
    }

    /**
     *
     * @param reqObject
     * @param testSampleGroups
     * @param sample
     * @return
     */
    private CustomResult mixSample(
            SampleBreakDownReq reqObject,
            Map<String, String> oldSampleGroupIds,
            List<TestSampleGroupInfoPO> testSampleGroups,
            TestSampleReq sample) {
        CustomResult rspReuslt = new CustomResult();

        List<SampleGroupReq> groups = reqObject.getGroups();
        if (groups == null || groups.isEmpty()) {
            rspReuslt.setMsg(String.format("未找到对应的Mix Sample(%s)信息.", sample.getSampleNo()));
            return rspReuslt;
        }
        List<SampleGroupReq> sampleGroups = groups.stream().filter(group -> StringUtils.equalsIgnoreCase(group.getSampleId(), sample.getSampleId())).collect(Collectors.toList());
        if (sampleGroups == null || sampleGroups.isEmpty()) {
            rspReuslt.setMsg(String.format("未找到对应的Mix Sample(%s).", sample.getSampleNo()));
            return rspReuslt;
        }
        if (sampleGroups.size() <= 1) {
            //rspReuslt.setMsg("Please select two sample at least!");
            rspReuslt.setMsg(String.format("至少选择两个才能Mix Sample(%s).", sample.getSampleNo()));
            return rspReuslt;
        }
        if (sample.getNoTest().booleanValue()){
            rspReuslt.setMsg(String.format("Mix Sample(%s)不能设置为NoTest.", sample.getSampleNo()));
            return rspReuslt;
        }
        rspReuslt.setSuccess(true);

        this.toTestSampleGroupInfo(oldSampleGroupIds, testSampleGroups, sampleGroups, reqObject.getUserName());
        return rspReuslt;
    }

    /**
     *
     * @param reqObject
     * @param oldSampleGroupIds
     * @param testSampleGroups
     * @param oldSampleMaps
     * @param sample
     * @return
     */
    private CustomResult shareSample(
            SampleBreakDownReq reqObject,
            Map<String, String> oldSampleGroupIds,
            List<TestSampleGroupInfoPO> testSampleGroups,
            Map<String, TestSampleInfoPO> oldSampleMaps,
            TestSampleReq sample) {
        CustomResult rspReuslt = new CustomResult();
        List<SampleGroupReq> groups = reqObject.getGroups();
        if (groups == null || groups.isEmpty()) {
            //rspReuslt.setMsg(String.format("未找到对应的Share Sample(%s)信息.", sample.getSampleNo()));
            sample.setSampleType(SampleType.Sample.getSampleType());
            rspReuslt.setSuccess(true);
            return rspReuslt;
        }
        List<SampleGroupReq> sampleGroups = groups.stream().filter(group -> StringUtils.equalsIgnoreCase(group.getSampleId(), sample.getSampleId())).collect(Collectors.toList());
        if (sampleGroups == null || sampleGroups.isEmpty()) {
            //rspReuslt.setMsg(String.format("未找到对应的Share Sample(%s).", sample.getSampleNo()));
            sample.setSampleType(SampleType.Sample.getSampleType());
            rspReuslt.setSuccess(true);
            return rspReuslt;
        }

        for (SampleGroupReq sampleGroup : sampleGroups) {
            if (StringUtils.isNotBlank(sampleGroup.getSampleGroupId())){
                sampleGroup.setSampleGroupId(sampleGroup.getSampleGroupId().toLowerCase());
            }
            if (StringUtils.isNotBlank(sampleGroup.getSampleId())){
                sampleGroup.setSampleId(sampleGroup.getSampleId().toLowerCase());
            }
            if (!oldSampleMaps.containsKey(sampleGroup.getSampleGroupId())) {
                rspReuslt.setMsg(String.format("未找到Share Sample(%s)原样.", sample.getSampleNo()));
                return rspReuslt;
            }
            TestSampleInfoPO testSamplePO = oldSampleMaps.get(sampleGroup.getSampleGroupId());
            if (!SampleType.equals(testSamplePO.getSampleType(), SampleType.OriginalSample)) {
                rspReuslt.setMsg(String.format("Share Sample(%s)只能是原样.", sample.getSampleNo()));
                return rspReuslt;
            }
        }
        List<TestSampleReq> samples = reqObject.getSamples();
        // 如果就一个原样，且是自己的父样，如果不是则抛异常
        if (sampleGroups.size() == 1) {
            String sampleGroupId = sampleGroups.get(0).getSampleGroupId();
            List<TestSampleReq> testSamples = samples.stream().filter(testSample -> StringUtils.equalsIgnoreCase(testSample.getSampleId(), sampleGroupId)).collect(Collectors.toList());
            if (testSamples == null || testSamples.isEmpty()) {
                rspReuslt.setMsg(String.format("未找到对应的Share Sample(%s).", sample.getSampleNo()));
                return rspReuslt;
            }
            if (!StringUtils.equalsIgnoreCase(testSamples.get(0).getExecutionSystemSampleId(), sample.getExecutionSystemSampleId())) {
                rspReuslt.setMsg(String.format("当前的Share Sample(%s)在不同的原样上.", sample.getSampleNo()));
                return rspReuslt;
            }
            sample.setSampleType(SampleType.Sample.getSampleType());
        }
        if (sampleGroups.stream().filter(group -> StringUtils.equalsIgnoreCase(group.getSampleId(), sample.getSampleId()) && StringUtils.equalsIgnoreCase(group.getSampleGroupId(), sample.getSampleParentId())).count() <= 0){
            rspReuslt.setMsg(String.format("未找到对应的Share Sample(%s)原样.", sample.getSampleNo()));
            return rspReuslt;
        }
        rspReuslt.setSuccess(true);
        if (sampleGroups.size() == 1){
            return rspReuslt;
        }
        this.toTestSampleGroupInfo(oldSampleGroupIds, testSampleGroups, sampleGroups, reqObject.getUserName());
        return rspReuslt;
    }

    /**
     *
     * @param testSampleGroups
     * @param sampleGroups
     * @param userName
     */
    private void toTestSampleGroupInfo(
            Map<String, String> oldSampleGroupIds,
            List<TestSampleGroupInfoPO> testSampleGroups,
            List<SampleGroupReq> sampleGroups,
            String userName){
        TestSampleGroupInfoPO testSampleGroup;
        for (SampleGroupReq sampleGroup: sampleGroups){
            String groupKey = String.format("%s_%s", sampleGroup.getSampleId(), sampleGroup.getSampleGroupId());
            if (oldSampleGroupIds.containsKey(groupKey)){
                oldSampleGroupIds.remove(groupKey);
                continue;
            }
            testSampleGroup = new TestSampleGroupInfoPO();
            // TODO New ID
            testSampleGroup.setID(UUID.randomUUID().toString());
            testSampleGroup.setSampleGroupID(sampleGroup.getSampleGroupId());
            testSampleGroup.setSampleID(sampleGroup.getSampleId());
            testSampleGroup.setMainMaterialFlag(sampleGroup.getMainMaterialFlag());
            testSampleGroup.setSequence(sampleGroup.getSequence());

            testSampleGroup.setActiveIndicator(true);
            testSampleGroup.setCreatedBy(userName);
            testSampleGroup.setCreatedDate(DateUtils.getNow());
            testSampleGroup.setModifiedBy(userName);
            testSampleGroup.setModifiedDate(DateUtils.getNow());

            testSampleGroups.add(testSampleGroup);
        }
    }

    /**
     *
     * @param samples
     * @param oldSamples
     * @param childSample
     * @return
     */
    private  TestSampleReq findParentSample(List<TestSampleReq> samples,List<TestSampleInfoPO> oldSamples, TestSampleReq childSample) {
        if (samples == null || samples.isEmpty()) {
            return null;
        }
        if (StringUtils.isBlank(childSample.getSampleParentId())) {
            return childSample;
        }

        TestSampleInfoPO testSampleInfoPO = oldSamples.stream().filter(sample -> (
                SampleType.equals(sample.getSampleType(), childSample.getSampleType()) &&
                        StringUtils.equalsIgnoreCase(sample.getID(), childSample.getSampleParentId()))
        ).findFirst().orElse(null);
        if(testSampleInfoPO!=null){
            TestSampleReq req = new TestSampleReq();
            req.setSampleId(testSampleInfoPO.getID());
            req.setSampleParentId(testSampleInfoPO.getSampleParentID());
            req.setSampleType(testSampleInfoPO.getSampleType());
            req.setNoTest(testSampleInfoPO.getApplicable());
            return this.findParentSample(samples,oldSamples, req);
        }

        TestSampleReq parentSample = samples.stream().filter(sample ->(
                SampleType.equals(sample.getSampleType(), childSample.getSampleType()) &&
                        StringUtils.equalsIgnoreCase(sample.getSampleId(), childSample.getSampleParentId()))
        ).findFirst().orElse(null);
        if (parentSample != null) {
            return this.findParentSample(samples,oldSamples, parentSample);
        }
        return null;
    }
    /**
     * @param samples
     * @param childSample
     */
    private SampleType findParentSampleType(List<TestSampleReq> samples, TestSampleReq childSample) {
        if (samples == null || samples.isEmpty()) {
            return null;
        }
        if (StringUtils.isBlank(childSample.getSampleParentId())) {
            return SampleType.findType(childSample.getSampleType());
        }
        TestSampleReq parentSample = samples.stream().filter(sample ->(
                SampleType.equals(sample.getSampleType(), childSample.getSampleType()) &&
                        StringUtils.equalsIgnoreCase(sample.getSampleId(), childSample.getSampleParentId()))
        ).findFirst().orElse(null);
        if (parentSample != null) {
            return this.findParentSampleType(samples, parentSample);
        }
        return null;
    }

    /**
     *
     * @param reqObject
     * @return
     */
    public CustomResult getSubContractOriginalSampleList(OrderSubContractReq reqObject){
        CustomResult rspResult = new CustomResult();
        if (reqObject == null){
            rspResult.setMsg("请求的对象不能为空.");
            return rspResult;
        }
        if (StringUtils.isBlank(reqObject.getOrderNo())){
            rspResult.setMsg("请求的订单号不能为空.");
            return rspResult;
        }
        if (StringUtils.isBlank(reqObject.getSubContractNo())){
            rspResult.setMsg("请求SubContractNo不能为空.");
            return rspResult;
        }
        List<TestSampleDTO> originalSampleIds = this.getTestSampleList(reqObject);
        rspResult.setData(originalSampleIds);
        rspResult.setSuccess(true);
        return rspResult;
    }

    public CustomResult deleteSample(SampleReq req){
        CustomResult rspResult = new CustomResult();
        rspResult.setSuccess(true);
        if(StringUtils.isBlank(req.getOrderNo())){
            throw new BizException(ResponseCode.ILLEGAL_ARGUMENT,"delete request ,orderNo params is empty，Please contact the system manager");
        }
        UserInfo user = tokenClient.getUser();
        if(StringUtils.isBlank(req.getSampleId())){
            throw new BizException(ResponseCode.ILLEGAL_ARGUMENT, "params must be not empty!");
        }
        TestMatrixInfoExample testMatrixInfoExample = new TestMatrixInfoExample();
        testMatrixInfoExample.createCriteria().andTestSampleIDEqualTo(req.getSampleId());
        List<TestMatrixInfoPO> testMatrixInfoPOS = testMatrixInfoMapper.selectByExample(testMatrixInfoExample);
        if(testMatrixInfoPOS!=null&&testMatrixInfoPOS.size()>0){
            rspResult.setMsg("The sample has been assign!");
            rspResult.setSuccess(false);
        }else{
            TestSampleInfoExample testSampleInfoExample = new TestSampleInfoExample();
            testSampleInfoExample.createCriteria().andOrderNoEqualTo(req.getOrderNo()).andSampleParentIDEqualTo(req.getSampleId());
            List<TestSampleInfoPO> testSampleInfoPOS = testSampleInfoMapper.selectByExample(testSampleInfoExample);
            if(testSampleInfoPOS!=null && testSampleInfoPOS.size()>0){
                rspResult.setMsg("The sample has sub-samples!");
                rspResult.setSuccess(false);
            }else{
                TestSampleGroupInfoExample testSampleGroupInfoExample = new TestSampleGroupInfoExample();
                testSampleGroupInfoExample.createCriteria().andSampleGroupIDEqualTo(req.getSampleId());
                List<TestSampleGroupInfoPO> testSampleGroupInfoPOS = testSampleGroupInfoMapper.selectByExample(testSampleGroupInfoExample);
                if(testSampleGroupInfoPOS!=null && testSampleGroupInfoPOS.size()>0){
                    rspResult.setMsg("The sample has been done Mix!");
                    rspResult.setSuccess(false);
                }
            }
        }

        if(rspResult.isSuccess()){
            deleteMixSampleById(req.getSampleId(), req.getOrderNo(),user);
        }

        return rspResult;
    }

    public void deleteMixSampleById(String sampleId, String orderNo, UserInfo user) {
        TestSampleInfoPO testSampleInfoPO = testSampleInfoMapper.selectByPrimaryKey(sampleId);
        if(Func.isEmpty(testSampleInfoPO))
        {
            //当Preorder保存，但是没有totest，otsnotes还没有sample信息
            logger.info("=======SampleId:{} 获取到的sample为空=========",sampleId);
            return;
        }

        TestSampleGroupInfoExample testSampleGroupInfoExample = new TestSampleGroupInfoExample();
        testSampleGroupInfoExample.createCriteria().andSampleIDEqualTo(sampleId);
        testSampleGroupInfoMapper.deleteByExample(testSampleGroupInfoExample);
        PPSampleRelationshipInfoExample ppSampleRelationshipInfoExample = new PPSampleRelationshipInfoExample();
        ppSampleRelationshipInfoExample.createCriteria().andTestSampleIDEqualTo(sampleId);
        ppSampleRelationshipInfoMapper.deleteByExample(ppSampleRelationshipInfoExample);
        LimitGroupInstanceExample limitGroupInstanceExample = new LimitGroupInstanceExample();
        limitGroupInstanceExample.createCriteria().andTestSampleIDEqualTo(sampleId);
        limitGroupInstanceMapper.deleteByExample(limitGroupInstanceExample);
        LimitInstanceExample limitInstanceExample = new LimitInstanceExample();
        limitInstanceExample.createCriteria().andTestSampleIDEqualTo(sampleId);
        limitInstanceMapper.deleteByExample(limitInstanceExample);
        ProductAttributeInstanceInfoExample productAttributeInstanceInfoExample = new ProductAttributeInstanceInfoExample();
        productAttributeInstanceInfoExample.createCriteria().andTestSampleIDEqualTo(sampleId);
        productAttributeInstanceInfoMapper.deleteByExample(productAttributeInstanceInfoExample);
        try {

/*            LogInfoUtils.getInstance().createLogInfoGeneralPOAndInsert(LogOperationEnums.del,
                    LogOperationTypeEnums.sample, sample.getId(), null, JSONObject.toJSONString(sample));*/
            testSampleInfoMapper.deleteByPrimaryKey(sampleId);
        } catch (Exception e) {
            logger.error(e.getMessage());
        }
        updateMixSampleNo(orderNo);
    }

    /**
     *
     * @param reqObject
     * @return
     */
    public List<TestSampleDTO> getTestSampleList(OrderSubContractReq reqObject){
        if (StringUtils.isBlank(reqObject.getOrderNo()) || StringUtils.isBlank(reqObject.getSubContractNo())) {
            return Lists.newArrayList();
        }

        List<TestSampleDTO> sampleInfos = subContractMapper.getSubContractSampleList(reqObject);
        if (CollectionUtils.isEmpty(sampleInfos)){
            return Lists.newArrayList();
        }

        // 需要转换的时候，直接返回subcontract下的所有Sample信息
        if (reqObject.getConvertOriginalSample() != null && reqObject.getConvertOriginalSample()) {
            return sampleInfos;
        }

        List<TestSampleInfo> testSamples = testSampleMapper.getTestSampleList(reqObject.getOrderNo());
        if (testSamples == null || testSamples.isEmpty()){
            return sampleInfos;
        }
        Set<String> sampleIds = sampleInfos.stream().map(TestSampleDTO::getID).collect(Collectors.toSet());

        Map<String, TestSampleInfo> sampleMaps = Maps.newHashMap();
        for (TestSampleInfo sample: testSamples){
            if (sampleMaps.containsKey(sample.getSampleId())){
                continue;
            }
            sampleMaps.put(sample.getSampleId(), sample);
        }

        Map<String, SampleType> testSampleIds = Maps.newHashMap();
        for (String sampleId: sampleIds){
            this.testSampleBFS(sampleMaps, testSampleIds, sampleId);
        }

        List<TestSampleInfoPO> testSampleIdList = testSampleMapper.getTestSampleIdList(Lists.newArrayList(testSampleIds.keySet()));
        List<TestSampleDTO> testSampleDTOS = BeanUtil.copyToList(testSampleIdList, TestSampleDTO.class);
        if (reqObject.getSampleType() == 1){
            return testSampleDTOS;
        }

        Set<String> originalSampleIds = Sets.newHashSet();
        for (Map.Entry<String, SampleType> entry: testSampleIds.entrySet()){
            if (entry.getValue() != SampleType.OriginalSample){
                continue;
            }
            originalSampleIds.add(entry.getKey());
        }
        return testSampleDTOS.stream().filter(sample -> originalSampleIds.contains(sample.getID())).collect(Collectors.toList());
    }

    /**
     *
     * @param sampleMaps
     * @param testSampleIds
     * @param testSampleId
     */
    private void testSampleBFS(Map<String, TestSampleInfo> sampleMaps, Map<String, SampleType> testSampleIds, String testSampleId){
        if (StringUtils.isBlank(testSampleId)){
            return;
        }
        TestSampleInfo testSample = sampleMaps.get(testSampleId);
        if (testSample == null){
            return;
        }
        SampleType sampleType = SampleType.findType(testSample.getSampleType());
        if (sampleType == null){
            return;
        }
        if (!testSampleIds.containsKey(testSample.getSampleId())){
            testSampleIds.put(testSample.getSampleId(), sampleType);
        }
        if (sampleType == SampleType.OriginalSample){
            return;
        }
        if (sampleType == SampleType.Sample || sampleType == SampleType.SubSample){
            this.testSampleBFS(sampleMaps, testSampleIds, testSample.getSampleParentId());
            return;
        }
        List<String> sampleGroupIds = testSample.getSampleGroupIds();
        if (sampleGroupIds == null || sampleGroupIds.isEmpty()){
            return;
        }
        for (String sampleGroupId: sampleGroupIds){
            /*if (sampleType == SampleType.ShareSample){
                continue;
            }*/
            this.testSampleBFS(sampleMaps, testSampleIds, sampleGroupId);
        }
    }

    /**
     *
     * @param samples
     * @param sampleIDNeeds
     * @param leafSampleId
     */
    private void getRootSampleById(List<TestSampleInfoPO> samples, Set<String> sampleIDNeeds, String leafSampleId) {
        for (TestSampleInfoPO samplePO : samples) {
            if (!StringUtils.equalsIgnoreCase(samplePO.getID(), leafSampleId)) {
                continue;
            }
            sampleIDNeeds.add(samplePO.getID());
            if (StringUtils.isNotEmpty(samplePO.getSampleParentID())) {
                getRootSampleById(samples, sampleIDNeeds, samplePO.getSampleParentID());
            }
        }
    }

    /**
     * 校验slimMapping
     * @param reqObject
     * @return
     */
    private CustomResult validateToSlimSubcontracts(SampleBreakDownReq reqObject) {
        CustomResult customResult = new CustomResult();
        //校验证Sample是否存在
        List<String> allSampleIds = reqObject.getSamples().stream().map(TestSampleReq::getSampleId).distinct()
            .collect(Collectors.toList());

        if(CollectionUtils.isEmpty(allSampleIds)){
            customResult.setSuccess(false);
            customResult.setMsg("传入的samples为空");
            return customResult;
        }

        if(CollectionUtils.isEmpty(reqObject.getSlimMapping())){
            //如果不传，则直接跳出
            customResult.setSuccess(true);
            return customResult;
        }

        List<String> slimSampleIds = reqObject.getSlimMapping().stream()
            .filter(p -> StringUtils.isNotBlank(p.getSampleId())).map(SlimMappingReq::getSampleId)
            .distinct()
            .collect(Collectors.toList());

        if(CollectionUtils.isEmpty(slimSampleIds)){
            customResult.setSuccess(false);
            customResult.setMsg("slim sample不能为空");
            return customResult;
        }

        for (String slimSampleId : slimSampleIds) {
            boolean present = allSampleIds.stream()
                .anyMatch(p -> StringUtils.equalsIgnoreCase(slimSampleId, p));
            if(! present){
                customResult.setSuccess(false);
                customResult.setMsg("sample"+slimSampleId+"不存在");
                return customResult;
            }
        }

        //校验testLineId是否存在
        List<String> testlineInstanceIds = reqObject.getSlimMapping().stream()
            .filter(p -> StringUtils.isNotBlank(p.getTestLineID())).map(SlimMappingReq::getTestLineInstanceId)
            .distinct()
            .collect(Collectors.toList());
        if(CollectionUtils.isEmpty(testlineInstanceIds)){
            customResult.setSuccess(false);
            customResult.setMsg("传入的testline为空");
            return customResult;
        }
        List<TestLineInstancePO> testLineByIds = testLineMapper.getTestLineByIds(testlineInstanceIds);
        if(CollectionUtils.isEmpty(testLineByIds)){
            customResult.setSuccess(false);
            customResult.setMsg("传入的testline不存在!");
            return customResult;
        }
        if(testlineInstanceIds.size() != testLineByIds.size()){
            customResult.setSuccess(false);
            customResult.setMsg("传入的testline不存在!!");
            return customResult;
        }

        customResult.setSuccess(true);
        return customResult;
    }

    /**
     * 转换原始数据
     * @param order
     * @param reqObject
     * @return
     */
    private List<SlimSubcontractPO> convertToSlimSubcontracts(GeneralOrderInstanceInfoPO order,SampleBreakDownReq reqObject) {
        List<SlimSubcontractPO> slimSubcontractList = Lists.newArrayList();

        List<SlimMappingReq> slimMappingReqs = reqObject.getSlimMapping();
        if(CollectionUtils.isEmpty(slimMappingReqs)){
            return slimSubcontractList;
        }
        List<String> testlineInstanceIds = slimMappingReqs.stream()
            .filter(p -> StringUtils.isNotBlank(p.getTestLineID())).map(p -> p.getTestLineInstanceId())
            .distinct()
            .collect(Collectors.toList());

        //如果回传的TL已经分包，则不再接收所有mapping数据

        SubContractTestLineMappingExample subContractTestLineMappingExample = new SubContractTestLineMappingExample();
        subContractTestLineMappingExample.createCriteria().andTestLineInstanceIDIn(testlineInstanceIds);

        List<SubContractTestLineMappingPO> subContractTestLineMappingPOS = subContractTestLineMappingMapper.selectByExample(subContractTestLineMappingExample);

        if(! CollectionUtils.isEmpty(subContractTestLineMappingPOS)){
            return slimSubcontractList;
        }

        List<String> sampleIds = reqObject.getSamples().stream()
            .filter(p -> StringUtils.isNotBlank(p.getSampleId())).map(p->p.getSampleId())
            .distinct().collect(Collectors.toList());

        List<TestSampleInfoPO> testSampleInfoPOS = Lists.newArrayList(); //testSampleMapper.getTestSampleIdList(sampleIds);
        //merge 新增的sample
        //现在不merge了，以传过的为准
        for (String sampleId : sampleIds) {
            Optional<TestSampleReq> first = reqObject.getSamples().stream()
                .filter(p -> StringUtils.equalsIgnoreCase(p.getSampleId(), sampleId))
                .findFirst();
            if(! first.isPresent()) {
                continue;
            }
            TestSampleReq sampleReq = first.get();
            String material = testSampleLangService.getMaterialTextByMaterialInfos(sampleReq.getMaterials());
            if(SampleType.check(sampleReq.getSampleType(),SampleType.MixSample)){
                List<String> sampleGroupIds = reqObject.getGroups().stream()
                        .filter(groupReq->StringUtils.equalsIgnoreCase(groupReq.getSampleId(),sampleId))
                        .map(SampleGroupReq::getSampleGroupId).collect(Collectors.toList()); // 两个组成样sampleId
                List<String> materials = Lists.newArrayList();
                for (String groupId : sampleGroupIds){
                    Optional<TestSampleReq> group = reqObject.getSamples().stream().filter(p->StringUtils.equalsIgnoreCase(p.getSampleId(),groupId)).findFirst(); //组成样sample信息
                    if(!group.isPresent()){
                        continue;
                    }
                    materials.add(testSampleLangService.getMaterialTextByMaterialInfos(group.get().getMaterials()));
                }
                material = StringUtils.join(materials,"+");
            }
            TestSampleInfoPO testSampleInfoPO = new TestSampleInfoPO();
            testSampleInfoPO.setID(first.get().getSampleId());
            testSampleInfoPO.setSampleNo(first.get().getSampleNo());
            testSampleInfoPO.setColor(first.get().getColor());
            testSampleInfoPO.setMaterial(material);
            testSampleInfoPO.setSampleRemark(first.get().getRemark());
            testSampleInfoPO.setComposition(first.get().getComposition());
            testSampleInfoPO.setSampleType(first.get().getSampleType());
            testSampleInfoPO.setDescription(first.get().getSampleDesc());
            testSampleInfoPOS.add(testSampleInfoPO);
        }

        OrderInfoForSlimRsp orderInfoForSlim = orderClient.getOrderInfoForSlim(order.getOrderNo());

        OrderIdReq orderIdReq = new OrderIdReq();
        orderIdReq.setOrderNo(order.getOrderNo());
        BaseResponse<OrderAllDTO> orderForPe = gpoOrderFacade.getOrderForPe(orderIdReq);
        OrderAllDTO orderAllDTO = orderForPe.getData();

        for (SlimMappingReq slimMappingReq : slimMappingReqs) {
            List<String> schemes = Lists.newArrayList();

            if(StringUtils.isNotBlank(slimMappingReq.getTestScheme())) {
                String[] testSchemes = doReplaceAndSplit(slimMappingReq.getTestScheme());
                schemes.addAll(Arrays.asList(testSchemes));
            }
            if(StringUtils.isNotBlank(slimMappingReq.getReportScheme())){
                String[] reportSchemes = doReplaceAndSplit(slimMappingReq.getReportScheme());
                schemes.addAll(Arrays.asList(reportSchemes));
            }
            //schemes 必须要有 至少一条
            if(schemes.isEmpty()){
                //前面已经作校验，这里可以schemes不可能为空
                throw new IllegalArgumentException("sample "+slimMappingReq.getSampleNo()+" testline " + slimMappingReq.getTestLineID() +" has no scheme");
            }

            Optional<TestSampleInfoPO> optionalTestSampleInfoPO = testSampleInfoPOS.stream()
                .filter(p -> StringUtils.equalsIgnoreCase(p.getID(), slimMappingReq.getSampleId()))
                .findFirst();

            if(optionalTestSampleInfoPO.isPresent()){
                for(String scheme : schemes){
                    if(!StringUtils.isBlank(scheme)){
                        slimSubcontractList.add(
                            convertToSlimSubcontract(slimMappingReq,scheme,order,orderAllDTO,optionalTestSampleInfoPO.get())
                        );
                    }
                }
            }


        }

        return slimSubcontractList;
    }

    private SlimSubcontractPO convertToSlimSubcontract(SlimMappingReq slimMappingReq,String scheme,GeneralOrderInstanceInfoPO generalOrderInstanceInfoPO,OrderAllDTO preOrder,TestSampleInfoPO testSampleInfoPO) {
        SlimSubcontractPO slimSubcontractPO = new SlimSubcontractPO();
        slimSubcontractPO.setID(UUID.randomUUID().toString());
        slimSubcontractPO.setOrderNo(generalOrderInstanceInfoPO.getOrderNo());
        slimSubcontractPO.setTestLineID(Integer.valueOf(slimMappingReq.getTestLineID()));
        slimSubcontractPO.setProductCode(slimMappingReq.getProductCode());
        slimSubcontractPO.setSampleNo(testSampleInfoPO.getSampleNo());
        slimSubcontractPO.setScheme(scheme);
        slimSubcontractPO.setCustomerGroupCode(generalOrderInstanceInfoPO.getCustomerGroupCode());
        slimSubcontractPO.setCustomerGroupName(generalOrderInstanceInfoPO.getCustomerGroupName());
        slimSubcontractPO.setResponsibleTeamCode(generalOrderInstanceInfoPO.getResponsibleTeamCode());

        slimSubcontractPO.setCR(preOrder.getCr());
        slimSubcontractPO.setCustomerName(generalOrderInstanceInfoPO.getCustomerName());
        slimSubcontractPO.setExpectedOrderDueDate(DateUtils.format(preOrder.getExpectedOrderDueDate()));
        //获取productInstance信息
        OrderIdReq orderIdReq = new OrderIdReq();
        orderIdReq.setOrderId(preOrder.getID());
        BaseResponse<List<ProductInstanceDTO>> listBaseResponse = gpoOrderFacade.queryProductInstaceForPe(orderIdReq);
        if (Func.isNotEmpty(listBaseResponse) && Func.isNotEmpty(listBaseResponse.getData())){
            List<ProductInstanceDTO> productInstanceDTOList = listBaseResponse.getData();
            productInstanceDTOList = productInstanceDTOList.stream().filter(item -> Func.isEmpty(item.getHeaderID())).collect(Collectors.toList());
            if(Func.isNotEmpty(productInstanceDTOList)){
                ProductInstanceDTO productInstanceDTO = productInstanceDTOList.get(0);
                slimSubcontractPO.setCountryOfDestination(productInstanceDTO.getCountryOfDestination());
                slimSubcontractPO.setCountryOfOrigin(productInstanceDTO.getCountryOfOrigin());
                slimSubcontractPO.setProductDescription(productInstanceDTO.getProductDescription());
            }
        }
        slimSubcontractPO.setProcedure(slimMappingReq.getProcedure());
        slimSubcontractPO.setSampleColor(testSampleInfoPO.getColor());
        slimSubcontractPO.setSampleMaterial(testSampleInfoPO.getMaterial());
        slimSubcontractPO.setSampleRemark(testSampleInfoPO.getSampleRemark());
        slimSubcontractPO.setComposition(testSampleInfoPO.getComposition());
        slimSubcontractPO.setSampleType(testSampleInfoPO.getSampleType() == SampleType.MixSample.getSampleType() ? "Composite":"Specimen");
        slimSubcontractPO.setSampleDescription(testSampleInfoPO.getDescription());
//        slimSubcontractPO.setActiveIndicator(1);
        slimSubcontractPO.setSyncStatus(0);
        // TODO Mingyang DateUtils.getNow()
        slimSubcontractPO.setCreatedDate(new Date());
        slimSubcontractPO.setModifiedDate(new Date());
        slimSubcontractPO.setModifiedBy("bom");
        slimSubcontractPO.setCreatedBy("bom");


        return slimSubcontractPO;
    }

    @NotNull
    private String[] doReplaceAndSplit(String str){
        return str.replaceAll("，", ",").replaceAll("；", ",")
                .replaceAll(";", ",").split("\\,");
    }

//    @TestLinePending(filedName = "ppTestLineRelIds",type=TestLinePendingTypeEnums.PP_TL_REL_ID_List)
    @AccessPolicyRule(testLinePendingType = TestLinePendingTypeEnums.AssignSamplePpTlId)
    public CustomResult assignSample(AssignSampleReq reqObj) {
        CustomResult<Object> result = new CustomResult<>();
        String orderId = reqObj.getOrderId();
        GeneralOrderInstanceInfoPO order = orderMapper.getOrderInfoByOrderId(orderId);
        if(order==null){
            result.setSuccess(false);
            return result;
        }

        String orderNo = order.getOrderNo();
        //查询所有sample
        List<AssignSampleInfo> allAssignSampleRspList = testSampleExtMapper.queryAllSample(orderNo);
        //进行子样，子子样，共样等展示的拼接
        List<AssignSampleInfo> assignSampleInfos = this.filterSample(reqObj, allAssignSampleRspList);
        //查询当前关联到的所有pptlRel
        List<String> ppTestLineRelIds = reqObj.getPpTestLineRelIds();
        int assignType = reqObj.getAssignType();
        //0是正常assign 1是change里面
        if(assignType==1 && !CollectionUtils.isEmpty(ppTestLineRelIds)){
            List<PPTestLineRelationshipInfoPO> ppTestLineRelListByIds = ppTestLineRelMapper.getPPTestLineRelListByIds(ppTestLineRelIds);
            ppTestLineRelIds = ppTestLineRelListByIds.stream().map(rel->rel.getID()).collect(Collectors.toList());
        }
        //当前TL 对应的所有assignSample
        List<PPSampleRelInfo> assignedSampleList = Lists.newArrayList();
        if(!CollectionUtils.isEmpty(ppTestLineRelIds)){
            assignedSampleList = ppSampleRelMapper.getAssignedSampleByPPTLRelIds(ppTestLineRelIds);
            List<String> testLineInstanceIdList = assignedSampleList.stream().map(PPSampleRelInfo::getTestLineId).distinct().collect(Collectors.toList());
            if(Func.isNotEmpty(testLineInstanceIdList)){
                List<TestLineInstancePO> testLineInstancePOList = testLineMapper.getTestLineByIds(testLineInstanceIdList);
                List<TestLineInstancePO> fcmTestLineList = testLineInstancePOList.stream().filter(tl -> TestLineType.check(tl.getTestLineType(), TestLineType.FCM)).collect(Collectors.toList());
                if(Func.isNotEmpty(fcmTestLineList)){
                    String fcmTestLineIDs = fcmTestLineList.stream().map(TestLineInstancePO::getTestLineID).map(Func::toStr).distinct().collect(Collectors.joining(","));
                    result.setSuccess(false);
                    result.setMsg(messageUtil.get("test.line.type.fcm.not.allow.operation",new Object[]{fcmTestLineIDs}));
                    return result;
                }
            }
        }
        //对已经assign的sample 进行checked标记
        Map<String, Integer> sampelIdActive = assignedSampleList.stream().collect(Collectors.toMap(PPSampleRelInfo::getTestSampleId, PPSampleRelInfo::getActiveIndicator, (k1, k2) -> k2));
        Map<String, List<PPSampleRelInfo>> sampleIdRelInfoMap = assignedSampleList.stream().collect(Collectors.groupingBy(PPSampleRelInfo::getTestSampleId));
        Set<String> assignedSampleIdSet = sampleIdRelInfoMap.keySet();
        List<SampleRsp> sampelResult = new ArrayList<>();
        for (AssignSampleInfo sa : assignSampleInfos) {
            SampleRsp rsp = new SampleRsp();
            String id = sa.getID();
            rsp.setId(id);
            rsp.setSampleSeq(sa.getSampleSeq()+"");
            rsp.setCategory(sa.getCategory());
            rsp.setSampleNo(sa.getSampleNo());
            rsp.setSampleType(sa.getSampleType());
            rsp.setDescription(sa.getDescription());
            rsp.setComposition(sa.getComposition());
            rsp.setMaterial(sa.getMaterial());
            rsp.setColor(sa.getColor());
            rsp.setEndUse(sa.getEndUse());
            rsp.setOtherSampleInfo(sa.getOtherSampleInfo());
            rsp.setSampleRemark(sa.getSampleRemark());
            rsp.setSequenceNo(sa.getSequenceNo());
            rsp.setConvertSampleNo(sa.getConvertSampleNo());
            rsp.setActiveIndicator(sa.getActiveIndicator()?1:0);
            if(assignedSampleIdSet.contains(id)){
                rsp.setActiveIndicator(sampelIdActive.get(id));
                rsp.setChecked(true);
            }
            List<PPSampleRelInfo> ppSampleRels = sampleIdRelInfoMap.get(id);
            if(!CollectionUtils.isEmpty(ppSampleRels)){
                Set<String> assignedPPTLRelIdSet = ppSampleRels.stream().map(rel -> rel.getPpTLRelId()).collect(Collectors.toSet());
                rsp.setPpTlRelIds(Lists.newArrayList(assignedPPTLRelIdSet));
            }
            rsp.setSampleTypeNew(SampleType.getShortMessage(sa.getSampleType()));

            //前端逻辑移到后端
//            if(da.sampleType == 101){
//                da.category = 'O';
//            }else{
//                da.category = 'P';
//            }
            if (StringUtils.isBlank(rsp.getCategory())){
                SampleType sampleType = SampleType.findType(rsp.getSampleType());
                rsp.setCategory(sampleType.getCategoryPhy());
            }

            sampelResult.add(rsp);
        }

        //获取所有pp信息，用来展示
        List<SamplePPSRsp> samplePPSRspList = Lists.newArrayList();
        if(!CollectionUtils.isEmpty(ppTestLineRelIds)){
            samplePPSRspList = ppTestLineRelMapper.getPPInfoByPPTLRelIds(ppTestLineRelIds);
        }
        for (SamplePPSRsp samplePPSRsp : samplePPSRspList) {
            String ppName = samplePPSRsp.getPpName();
            samplePPSRsp.setPpName(StringUtils.isBlank(ppName)?"/":ppName);
        }
        AssignSampleRsp rsp = new AssignSampleRsp();
        rsp.setSamples(sampelResult);
        rsp.setPps(samplePPSRspList);
        result.setSuccess(true);
        result.setData(rsp);
        return result;
    }

    private List<AssignSampleInfo> filterSample(AssignSampleReq reqObj, List<AssignSampleInfo> sampleList) {
        String cancelFlag = reqObj.getCancelFlag();
        String category = reqObj.getCategory();
        int assignType = reqObj.getAssignType();
        if(assignType==0){
            if(StringUtils.isNotBlank(category)){
                if("P".equalsIgnoreCase(category)){
                    sampleList=sampleList.stream().filter(sampl -> (null == sampl.getCategory() || sampl.getCategory().equalsIgnoreCase("p") || sampl.getCategory().equalsIgnoreCase("o"))).collect(Collectors.toList());
                }else if("C".equalsIgnoreCase(category)){
                    sampleList=sampleList.stream().filter(sampl -> (null == sampl.getCategory() || sampl.getCategory().equalsIgnoreCase("c") || sampl.getCategory().equalsIgnoreCase("o"))).collect(Collectors.toList());
                }
            }
            if(StringUtils.isNotBlank(cancelFlag) && "Y".equalsIgnoreCase(cancelFlag)){
                sampleList=sampleList.stream().filter(sampl -> sampl.getActiveIndicator()).collect(Collectors.toList());
            }
        }

        List<AssignSampleInfo> sample101List = sampleList.stream().filter(sa -> sa.getSampleType() != null && sa.getSampleType().compareTo(101) == 0).collect(Collectors.toList());
        List<AssignSampleInfo> sample102List = sampleList.stream().filter(sa -> sa.getSampleType() != null && sa.getSampleType().compareTo(102) == 0).collect(Collectors.toList());
        List<AssignSampleInfo> sample103List = sampleList.stream().filter(sa -> sa.getSampleType() != null && sa.getSampleType().compareTo(103) == 0).collect(Collectors.toList());
        List<AssignSampleInfo> sample104List = sampleList.stream().filter(sa -> sa.getSampleType() != null && sa.getSampleType().compareTo(104) == 0).collect(Collectors.toList());
        List<AssignSampleInfo> sample105List = sampleList.stream().filter(sa -> sa.getSampleType() != null && sa.getSampleType().compareTo(105) == 0).collect(Collectors.toList());
        String redNo =  "<font style='text-decoration:line-through;color:red;'>%s</font>";
        for (AssignSampleInfo sample101 : sample101List) {
            String id101 = sample101.getID();
            String sampleNo101 = sample101.getSampleNo();
            Boolean active101 = sample101.getActiveIndicator();
            String coverSampleNo101 = sampleNo101;
            if(!active101){
                coverSampleNo101 = String.format(redNo, coverSampleNo101);
            }
            sample101.setSequenceNo(sampleNo101);
            sample101.setConvertSampleNo(coverSampleNo101);
            //从102之间找子样
            for (AssignSampleInfo sample102 : sample102List) {
                String sampleParentID102 = sample102.getSampleParentID();
                if(!id101.equals(sampleParentID102)){
                    continue;
                }
                String id102 = sample102.getID();
                String sampleNo102 = sample102.getSampleNo();
                Boolean active102 = sample102.getActiveIndicator();
                String coverSampleNo102 = sampleNo101+"-"+sampleNo102;
                if(!active102){
                    coverSampleNo102 = String.format(redNo,coverSampleNo102);
                }
                sample102.setSequenceNo(sampleNo102);
                sample102.setConvertSampleNo(coverSampleNo102);
                //子子样
                for (AssignSampleInfo sample103 : sample103List) {
                    String sampleParentID103 = sample103.getSampleParentID();
                    if(!id102.equals(sampleParentID103)){
                        continue;
                    }
                    Boolean active103 = sample103.getActiveIndicator();
                    String sampleNo103 = sample103.getSampleNo();
                    String coverSampleNo103 = sampleNo101+"-"+sampleNo102+"-"+sampleNo103;
                    if(!active103){
                        coverSampleNo103 = String.format(redNo,coverSampleNo103);
                    }
                    sample103.setSequenceNo(sampleNo103);
                    sample103.setConvertSampleNo(coverSampleNo103);
                    //sample103.setSampleNo(sampleNo103);
                }
                //sample102.setSampleNo(sampleNo102);

            }
            //从105 找组合样
            for (AssignSampleInfo sample105 : sample105List) {
                String sampleParentID105 = sample105.getSampleParentID();
                if(!id101.equals(sampleParentID105)){
                    continue;
                }
                //需要去group 表查询组合样样品
                String id105 = sample105.getID();
                List<TestSampleInfoPO> groupSampleList = testSampleGroupExtMapper.getSamplesBySampleId(id105);
                if(CollectionUtils.isEmpty(groupSampleList)){
                    continue;
                }
                List<String> noList = groupSampleList.stream().map(g -> g.getSampleNo()).collect(Collectors.toList());
                String join = StringUtils.join(noList, "/");
                Boolean active105 = sample105.getActiveIndicator();
                String sampleNo105 = sample105.getSampleNo();
                String coverSampleNo105 = join+"-"+sampleNo105;
                if(!active105){
                    coverSampleNo105 = String.format(redNo,coverSampleNo105);
                }
                sample105.setConvertSampleNo(coverSampleNo105);
                sample105.setSequenceNo(sampleNo105);
                //子子样
                for (AssignSampleInfo sample103 : sample103List) {
                    String sampleParentID103 = sample103.getSampleParentID();
                    if(!id105.equals(sampleParentID103)){
                        continue;
                    }
                    Boolean active103 = sample103.getActiveIndicator();
                    String sampleNo103 = sample103.getSampleNo();
                    String coverSampleNo103 = sampleNo101+"-"+sampleNo105+"-"+sampleNo103;
                    if(!active103){
                        coverSampleNo103 = String.format(redNo,coverSampleNo103);
                    }
                    sample103.setSequenceNo(sampleNo103);
                    sample103.setConvertSampleNo(coverSampleNo103);
                    //sample103.setSampleNo(sampleNo103);
                }
                //sample105.setSampleNo(sampleNo105);
            }
        }

        for (AssignSampleInfo sample104 : sample104List) {
            String coverSampleNo104 =sample104.getSampleNo();
            Boolean active104 = sample104.getActiveIndicator();
            if(!active104){
                coverSampleNo104 = String.format(redNo,coverSampleNo104);
            }
            sample104.setConvertSampleNo(coverSampleNo104);
        }

        List<AssignSampleInfo> result = Lists.newArrayList();
        result.addAll(sample101List);
        result.addAll(sample102List);
        result.addAll(sample103List);
        result.addAll(sample104List);
        result.addAll(sample105List);
        result.sort((a,b)->a.getSampleSeq().compareTo(b.getSampleSeq()));
        return result;

    }

    public CustomResult checkAssignSample(CheckAssignSampleReq req) {
        CustomResult<Object> result = new CustomResult<>();
        List<String> testLindIds = req.getTestLineIds();
        if(CollectionUtils.isEmpty(testLindIds)){
            result.setSuccess(false);
            result.setMsg("Params miss");
            return result;
        }
        AnalyteInfoExample example = new AnalyteInfoExample();
        example.createCriteria().andTestLineInstanceIDIn(testLindIds);
        List<AnalyteInfoPO> list = analyteInfoMapper.selectByExample(example);
        boolean isEmpty = CollectionUtils.isEmpty(list);
        //TODO 目前还没有anaylte的本地化数据，所以还是校验老的数据 2020年7月15日
        /*
        if(!isEmpty){
            List<Long> baseInfoIdList = list.stream().map(a -> a.getAnalyteBaseId()).collect(Collectors.toList());
            AnalyteBaseInfoExample baseInfoExample = new AnalyteBaseInfoExample();
            baseInfoExample.createCriteria().andIdIn(baseInfoIdList);
            List<AnalyteBaseInfoPO> analyteBaseInfoPOS = analyteBaseInfoMapper.selectByExample(baseInfoExample);
            isEmpty = CollectionUtils.isEmpty(analyteBaseInfoPOS);
        }*/
        result.setSuccess(true);
        //老接口，如果null  就返回true
        result.setData(isEmpty);
        return result;
    }


    /**
     *@Description  简述：
     * <br/>对sample增减，影响不同的表数据
     * <br/>新增sample,需要同步增加test_matrix,report_matrix,pp_sample_rel
     * <br/>删除sample,需要同步删除test_matrx,report_matrix,pp_sample_rel,
     * <br/>position,productAttr,limit,limitGroup,condition,conditionGroup,conditionGroupLanguage
     *
     *@Param
     *@Return
     *<AUTHOR>
     *@CreateDate
     *@ModifyDate 2020年7月6日
     *@Modify Vincent.Zhi
     */
    @AccessPolicyRule(orderStatus = {
            com.sgs.preorder.facade.model.enums.OrderStatus.Completed,
            com.sgs.preorder.facade.model.enums.OrderStatus.Closed,
            com.sgs.preorder.facade.model.enums.OrderStatus.Cancelled },isInclude = false)
    public CustomResult saveAssignSample(SaveAssignSampleReq req) {
        return executeAssignSample(req);
    }

    @NotVerifyToken
    public CustomResult executeAssignSample(SaveAssignSampleReq req) {
        CustomResult result = new CustomResult<>();
        logger.info("saveAssignSample orderNo:{}-req:{}", req.getOrderNo(), JSON.toJSONString(req));
        UserInfo user = SystemContextHolder.getUserInfoFillSystem();
        int assignType = req.getAssignType();
        String orderId = req.getOrderId();
        GeneralOrderInstanceInfoPO orderInfo = this.orderMapper.getOrderInfoByOrderId(orderId);
        if (Func.isEmpty(orderInfo)){
            result.setSuccess(false);
            result.setMsg("订单信息查询失败!");
            return result;
        }
        OrderInfoDto orderInfoDto = orderClient.getOrderInfoByOrderNo(orderInfo.getOrderNo());
        if (Func.isEmpty(orderInfoDto)){
            result.setSuccess(false);
            result.setMsg("订单信息查询失败!");
            return result;
        }
        if (!Func.equals(orderInfoDto.getBUCode(),ProductLineContextHolder.getProductLineCode())){
            result.setSuccess(false);
            result.setMsg("当前BU不允许操作当前订单!");
            return result;
        }
        List<String> sampleIds = req.getSampleIds();
        Map<String, List<String>> samplePPIds = req.getSamplePPIds();
        List<String> ppTestLineRelIds = new ArrayList<>();
        if(assignType==1){
            List<String> finalPpTestLineRelIds = ppTestLineRelIds;
            AtomicReference<Boolean> allSelectPP = new AtomicReference<>(true);
            samplePPIds.forEach((k, v)->{
                if(CollectionUtils.isEmpty(v)){
                    allSelectPP.set(false);
                }
                finalPpTestLineRelIds.addAll(v);
            });
            if(!allSelectPP.get()){
                result.setSuccess(false);
                result.setMsg("Please Select PP for Sample!");
                return result;
            }
            ppTestLineRelIds = finalPpTestLineRelIds;
        }else{
            ppTestLineRelIds = req.getPpTestLineRelIds();
        }
        if(CollectionUtils.isEmpty(ppTestLineRelIds)){
            result.setSuccess(false);
            result.setMsg("请求的TestLine 不能为空.");
            return result;
        }
        //去重
        ppTestLineRelIds=ppTestLineRelIds.stream().collect(Collectors.toSet()).stream().collect(Collectors.toList());
        //vincent 2021年1月13日 校验是否有TL处于Pending状态
        //TODO vincent save方法由于正常assign 和change传递的参数不统一,且change传递的参数没有标识性，导致无法用AOP处理，这里暂时先在代码中做判断
        boolean hasPending = testLinePendingService.hasPendingTestLineByPPTLRelIds(ppTestLineRelIds);
        if(hasPending){
            result.setSuccess(false);
            result.setMsg(TestLinePendingFlagEnums.getPendingMsg());
            return result;
        }


        if(orderInfo==null){
            result.setSuccess(false);
            result.setMsg("Order not exist or param error!");
            return result;
        }
        if(Func.isNotEmpty(orderInfo.getConfirmMatrixDate())&&Func.isEmpty(req.getSampleIds())){
            result.setSuccess(false);
            result.setMsg("Please select at least one sample because TL has been confirmed or limit already exits!");
            return result;
        }
        String orderNo = orderInfo.getOrderNo();
        //1.校验TL,已经TL是否cancel
        List<PPTestLineRelationshipInfoPO> currentOptionsPPTLRelList = this.ppTestLineRelMapper.getByIds(ppTestLineRelIds);
        if(CollectionUtils.isEmpty(currentOptionsPPTLRelList)){
            result.setSuccess(false);
            result.setMsg("请求的TestLine 不能为空.");
            return result;
        }
        Set<String> optionsTLIDSet = currentOptionsPPTLRelList.stream().map(rel -> rel.getTestLineInstanceID()).collect(Collectors.toSet());
        List<TestLineInstancePO> allTestLineList = this.testLineMapper.getTestLineByOrderId(orderId);
        if(CollectionUtils.isEmpty(allTestLineList)){
            result.setSuccess(false);
            result.setMsg("未找到该订单对应的TestLine.");
            return result;
        }
        //找到tl
        List<TestLineInstancePO> dbFindOptionsTestLine = allTestLineList.stream().filter(tl -> optionsTLIDSet.contains(tl.getID())).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(dbFindOptionsTestLine) || dbFindOptionsTestLine.size()!=optionsTLIDSet.size()){
            result.setSuccess(false);
            result.setMsg("未找到TestLine");
            return result;
        }
        List<TestLineInstancePO> hasCancelTL = dbFindOptionsTestLine.stream().filter(tl -> TestLineStatus.check(tl.getTestLineStatus(), TestLineStatus.Cancelled)).collect(Collectors.toList());
        if(!CollectionUtils.isEmpty(hasCancelTL)){
            result.setSuccess(false);
            result.setMsg("TestLine状态已Cancelled，请刷新页面.");
            return result;
        }
        List<TestLineInstancePO> hasNCTL = dbFindOptionsTestLine.stream().filter(tl -> TestLineStatus.check(tl.getTestLineStatus(), TestLineStatus.NC)).collect(Collectors.toList());
        if(!CollectionUtils.isEmpty(hasNCTL)){
            result.setSuccess(false);
            result.setMsg("TestLine is NC,can't assign Sample");
            return result;
        }

        //准备数据 all ppTlRel
        List<PPTestLineRelationshipInfoPO> allPPTLRelList = this.ppTestLineRelMapper.getPPTestLineRelListByOrderId(orderId);
        Map<String, PPTestLineRelationshipInfoPO> pptlRelIdRelPOMap = allPPTLRelList.stream()
                .collect(Collectors.toMap(PPTestLineRelationshipInfoPO::getID, Function.identity(), (k1, k2) -> k1));
        Map<String, List<PPTestLineRelationshipInfoPO>> tlIdPPTLRelListMap = allPPTLRelList.stream()
                .collect(Collectors.groupingBy(PPTestLineRelationshipInfoPO::getTestLineInstanceID));

        //all trims pp tl rel
        Set<Long> trimsPPTLRelSet = allPPTLRelList.stream().map(rel -> rel.getPpArtifactRelId()).collect(Collectors.toSet());
        List<PPArtifactRelInfoWithBLOBs> allTrimsPPTLRelList =  this.trimsPPTestLineRelMapper.queryByIds(Lists.newArrayList(trimsPPTLRelSet));
        Map<Long, PPArtifactRelInfoWithBLOBs> trimsPPTLRelIdMap = allTrimsPPTLRelList.stream().collect(Collectors.toMap(PPArtifactRelInfoWithBLOBs::getId, Function.identity()));
        //all matrix
        List<TestMatrixPO> allTestMatrixList = this.testMatrixMapper.getTestMatrixListByOrderId(orderId);
        //all condition
        List<TestConditionInfoPO> allConditionList = this.testConditionMapper.getTestConditionListByOrderId(orderId);
        //all limit
        List<LimitInstancePO> allLimitList = this.limitMapper.getLimitListByOrderId(orderId);
        //all limit group
        List<LimitGroupInstancePO> allLimitGroupList = this.limitGroupMapper.getLimitGroupListByOrderNo(orderNo);

        //查询当前订单的所有ppsamplerel
        List<PPSampleRelationshipInfoPO> allDBPPSampleRelList = this.ppSampleRelMapper.getPPSampleRelListByOrderId(orderId);
        Map<String, List<PPSampleRelationshipInfoPO>> dbSamplePPRelMap = allDBPPSampleRelList.stream().collect(Collectors.groupingBy(PPSampleRelationshipInfoPO::getTestSampleID));

        //2.处理pp sample rel关系 分为change的操作（只能增加sample或修改sample对应的pp） 和 正常的操作
        List<PPSampleRelInfo> oldPPSampleRelList = this.ppSampleRelMapper.getAssignedSampleByPPTLRelIds(ppTestLineRelIds);
        Map<String, PPSampleRelInfo> ppSampleRelIdPOMap = oldPPSampleRelList.stream().collect(Collectors.toMap(PPSampleRelInfo::getPpSampleRelId, Function.identity(), (k1, k2) -> k2));
        Map<String, List<PPSampleRelInfo>> oldSampleIdPPSampleRelList = oldPPSampleRelList.stream().collect(Collectors.groupingBy(rel -> rel.getTestSampleId()));
        //处理sampleId ppId关联关系，用来处理limitGroup
        Map<String, PPTestLineRelationshipInfoPO> pptlRelIdMap = allPPTLRelList.stream().collect(Collectors.toMap(PPTestLineRelationshipInfoPO::getID, Function.identity()));
        Map<String,List<Long>> sampleIdPPIDListMap = Maps.newHashMap();
        oldSampleIdPPSampleRelList.forEach((sId,relList)->{
            List<Long> ppIdList = sampleIdPPIDListMap.get(sId);
            if(CollectionUtils.isEmpty(ppIdList)){
                ppIdList = new ArrayList<>();
            }
            Set<Long> ppIds = relList.stream().map(rel -> {
                String ppTLRelId = rel.getPpTLRelId();
                PPTestLineRelationshipInfoPO po = pptlRelIdMap.get(ppTLRelId);
                return trimsPPTLRelIdMap.containsKey(po.getPpArtifactRelId()) ? po.getPpBaseId() : null;
            }).collect(Collectors.toSet());
            ppIdList.addAll(ppIds);
            //sample 与 pp的关系 此时的pp是ppBaseID
            sampleIdPPIDListMap.put(sId,ppIdList);
        });

        //修改的要找到 需要增加sampleRel还是删除sampelRel
        List<PPSampleRelationshipInfoPO> waitAddSampleRelList = new ArrayList<>();
        List<String> waitDeleteSampleRelList = new ArrayList<>();

        List<TestMatrixPO> waitDeleteMatrixList = new ArrayList<>();
        List<TestMatrixPO> waitAddMatrixList = new ArrayList<>();
        List<ReportMatrixRelationShipInfoPO> waitAddReportMatrixRelList = Lists.newArrayList();

        if(assignType!=1){
            // 因为同一个tl 可以有多个pp 需要去重处理
            for (String pagePPTestLineRelId : ppTestLineRelIds) {
                //找到增删的sample
                List<String> pageDeleteSampleIdList = new ArrayList<>();
                List<String> pageAddSampleList = new ArrayList<>();
                List<String> pageUpdateSampleList = new ArrayList<>();
                //DB中当前ppTL已经assign的sample
                Set<String> oldAssignSample = oldPPSampleRelList.stream()
                        .filter(oldpsrel->oldpsrel.getPpTLRelId().equals(pagePPTestLineRelId))
                        .map(oldpsrel -> oldpsrel.getTestSampleId()).collect(Collectors.toSet());
                //db没有，页面进来的全部新增
                if(CollectionUtils.isEmpty(oldAssignSample)){
                    pageAddSampleList.addAll(sampleIds);
                }else{
                    //找出db比页面多的就是删除
                    pageDeleteSampleIdList.addAll(oldAssignSample);
                    pageDeleteSampleIdList.removeAll(sampleIds);
                    //找出页面比db多的就是新增
                    pageAddSampleList.addAll(sampleIds);
                    pageAddSampleList.removeAll(oldAssignSample);
                    //去掉新增，就是需要修改的
                    pageUpdateSampleList.addAll(sampleIds);
                    pageUpdateSampleList.removeAll(pageAddSampleList);
                }
                //非change 的assign 保存 是有取消勾选的逻辑的，而且pptlrel有且只有一个,samplePPIds是不会有值的
                //所有sample 需要分配给所有pptlRel
                for (String pageAddSampleId : pageAddSampleList) {
                    PPSampleRelationshipInfoPO po = this.createPPSampleRelPO(pageAddSampleId, pagePPTestLineRelId, user.getRegionAccount());
                    waitAddSampleRelList.add(po);
                }

                for (String pageDeleteSampleId : pageDeleteSampleIdList) {
                    List<PPSampleRelInfo> deleteInfo = oldPPSampleRelList.stream()
                            .filter(psrel->psrel.getTestSampleId().equals(pageDeleteSampleId))
                            .filter(psrel->psrel.getPpTLRelId().equals(pagePPTestLineRelId))
                            .collect(Collectors.toList());
                    if(!CollectionUtils.isEmpty(deleteInfo)){
                        List<String> deleteIds = deleteInfo.stream()
                                .filter(psrel->psrel.getPpTLRelId().equals(pagePPTestLineRelId))
                                .map(info -> info.getPpSampleRelId())
                                .collect(Collectors.toList());
                        waitDeleteSampleRelList.addAll(deleteIds);
                    }
                }

                //处理matrix testline-sample 先处理删除的sample
                String testLineInstanceID = pptlRelIdRelPOMap.get(pagePPTestLineRelId).getTestLineInstanceID();
                Long ppBaseId = pptlRelIdRelPOMap.get(pagePPTestLineRelId).getPpBaseId();
                Integer citationId = allTestLineList.stream().filter(tl -> Func.equalsSafe(tl.getID(),testLineInstanceID)).findAny().orElse(new TestLineInstancePO()).getCitationId();
                //当前tl对应的所有pp
                List<PPTestLineRelationshipInfoPO> currentTLAllPPTLList = tlIdPPTLRelListMap.get(testLineInstanceID);
                for (String pageDeleteSampleId : pageDeleteSampleIdList) {
                    //1根据sampleId 找ppSample关系，除了本身之外还有没有其它 同Tl的pp在使用当前sample，如果没有 就可以删除matrix，否则需要保留
                    //List<PPSampleRelInfo> currentSampleAllPPSampleRelList = oldSampleIdPPSampleRelList.get(pageDeleteSampleId);
                    List<PPSampleRelationshipInfoPO> currentSampleAllPPSampleRelList = dbSamplePPRelMap.get(pageDeleteSampleId);
                    //3 页面传入的pptlrel中，是否有待处理或者已经处理的pptlrelid，因为动作统一，所以如果存在，那么当前matrix是可以删除的
                    //不再有同tl 的其它pp 占用sample 可以删除matrix
                    List<String> currentSampleAssignedPP = currentSampleAllPPSampleRelList.stream().map(ppSampleRelInfo -> ppSampleRelInfo.getPPTLRelID()).collect(Collectors.toList());
                    currentSampleAssignedPP.removeAll(ppTestLineRelIds);
                    //剩余的 pptl 是否和当前tl 相同 如果不同 可以删除matrix 相同的话 就需要保留当前sample 对应的amtrix
                    boolean hasOtherTlAssignSample = false;
                    for (String pptlId : currentSampleAssignedPP) {
                        PPTestLineRelationshipInfoPO ppTestLineRelationshipInfoPO = pptlRelIdRelPOMap.get(pptlId);
                        if(ppTestLineRelationshipInfoPO!=null){
                            String testLineInstanceID1 = ppTestLineRelationshipInfoPO.getTestLineInstanceID();
                            if(testLineInstanceID.equals(testLineInstanceID1)){
                                hasOtherTlAssignSample = true;
                            }
                        }
                    }
                    if(!hasOtherTlAssignSample){
                        List<TestMatrixPO> shouldDeleteMatrix = allTestMatrixList.stream()
                                .filter(m -> m.getTestLineInstanceID().equals(testLineInstanceID)
                                        && m.getTestSampleID().equals(pageDeleteSampleId))
                                .collect(Collectors.toList());
                        waitDeleteMatrixList.addAll(shouldDeleteMatrix);
                    }
                }
                for (String pageAddSampleId : pageAddSampleList) {
                    //源matrix中不存在就可以添加，但是需后续waitAddAMtrixList需要去重，否则matrix会重复
                    long count = allTestMatrixList.stream()
                            .filter(m -> m.getTestLineInstanceID().equals(testLineInstanceID))
                            .filter(m -> m.getTestSampleID().equals(pageAddSampleId)).count();
                    if(count==0){
                        TestMatrixPO po = this.createTestMatrixPO(pageAddSampleId, testLineInstanceID, orderId, user.getRegionAccount(), ppBaseId, citationId);
                        waitAddMatrixList.add(po);
                    }
                }
                /*if(!dealTlSet.contains(testLineInstanceID)){
                    dealTlSet.add(testLineInstanceID);

                }*/
            }
        }else if(assignType==1){//change的处理
            //找到增删的sample
            List<String> pageDeleteSampleIdList = new ArrayList<>();
            List<String> pageAddSampleList = new ArrayList<>();
            List<String> pageUpdateSampleList = new ArrayList<>();
            //DB中当前ppTL已经assign的sample
            Set<String> oldAssignSample = oldPPSampleRelList.stream()
                    .map(oldpsrel -> oldpsrel.getTestSampleId()).collect(Collectors.toSet());
            //db没有，页面进来的全部新增
            if(CollectionUtils.isEmpty(oldAssignSample)){
                pageAddSampleList.addAll(sampleIds);
            }else{
                //找出db比页面多的就是删除
                pageDeleteSampleIdList.addAll(oldAssignSample);
                pageDeleteSampleIdList.removeAll(sampleIds);
                //找出页面比db多的就是新增
                pageAddSampleList.addAll(sampleIds);
                pageAddSampleList.removeAll(oldAssignSample);
                //去掉新增，就是需要修改的
                pageUpdateSampleList.addAll(sampleIds);
                pageUpdateSampleList.removeAll(pageAddSampleList);
            }

            //change 的操作，只会同时存在一个TL 应当sample为切入点进行操作
            for (String addSampleId : pageAddSampleList) {
                List<String> pageSelectPPTLRelList = samplePPIds.get(addSampleId);
                for (String pptlRelId : pageSelectPPTLRelList) {
                    PPSampleRelationshipInfoPO po = this.createPPSampleRelPO(addSampleId, pptlRelId, user.getRegionAccount());
                    waitAddSampleRelList.add(po);
                }
                String testLineInstanceID = dbFindOptionsTestLine.get(0).getID();
                Integer citationId = dbFindOptionsTestLine.get(0).getCitationId();
                Long ppBaseId = allPPTLRelList.stream().filter(e -> Func.equalsSafe(e.getTestLineInstanceID(),testLineInstanceID)).findAny().orElse(new PPTestLineRelationshipInfoPO()).getPpBaseId();
                TestMatrixPO po = this.createTestMatrixPO(addSampleId, testLineInstanceID, orderId, user.getRegionAccount(), ppBaseId, citationId);
                waitAddMatrixList.add(po);
            }
            for (String updateSampleId : pageUpdateSampleList) {
                List<String> pagePPTLRelList = samplePPIds.get(updateSampleId);
                List<PPSampleRelInfo> oldPPSampleRel = oldSampleIdPPSampleRelList.get(updateSampleId);
                if(CollectionUtils.isEmpty(oldPPSampleRel)){
                    for (String pptlRelId : pagePPTLRelList) {
                        PPSampleRelationshipInfoPO po = this.createPPSampleRelPO(updateSampleId, pptlRelId, user.getRegionAccount());
                        waitAddSampleRelList.add(po);
                    }
                }else{
                    Map<String, List<PPSampleRelInfo>> oldPpTlRelIdRelList = oldPPSampleRel.stream().collect(Collectors.groupingBy(rel -> rel.getPpTLRelId()));
                    //db 删除页面，如果有多余数据 就是需要删除的
                    Set<String> oldPptlRelSet = Sets.newHashSet(oldPpTlRelIdRelList.keySet());
                    oldPptlRelSet.removeAll(pagePPTLRelList);
                    for (String shouldDeletePPTLRelId : oldPptlRelSet) {
                        List<PPSampleRelInfo> shouldDeleteList = oldPpTlRelIdRelList.get(shouldDeletePPTLRelId);
                        List<String> deleteIds = shouldDeleteList.stream().map(rel -> rel.getPpSampleRelId()).collect(Collectors.toList());
                        waitDeleteSampleRelList.addAll(deleteIds);
                    }
                    //页面删除DB ，多出来的数据是需要新增的
                    oldPptlRelSet = oldPpTlRelIdRelList.keySet();
                    pagePPTLRelList.removeAll(oldPptlRelSet);
                    for (String shouldAddPPTLRelId : pagePPTLRelList) {
                        PPSampleRelationshipInfoPO po = this.createPPSampleRelPO(updateSampleId, shouldAddPPTLRelId, user.getRegionAccount());
                        waitAddSampleRelList.add(po);
                    }
                }
            }
        }


        //处理waitAddMatrixList 去重
        Map<String, List<TestMatrixPO>> groupAddMatrix = waitAddMatrixList.stream().collect(Collectors.groupingBy(m -> m.getTestLineInstanceID() + "_" + m.getTestSampleID()));
        waitAddMatrixList.clear();
        groupAddMatrix.forEach((k,v)->{
            waitAddMatrixList.add(v.get(0));
        });

        //active report
        ReportInfoPO reportInfoPO = this.reportMapper.getReportByOrderNo(orderNo);
        // 如果Order中有<=1 个Flag = Self Generate/null Report对象，则自动把TL 添加到01Repot Martix中
        // 根据OrderNo查询reportFlag为1 Self Generate 的 report数量
        List<ReportInfoPO> reportListByOrderNo = reportMapper.getReportListByOrderNo(req.getOrderNo());
        ReportInfoPO firstReport = null;
        //根据需要新增的matrix，来新增reportMatrix
        ReportInfoExample example = new ReportInfoExample();
        example.createCriteria().andOrderNoEqualTo(orderNo);
        List<ReportInfoPO> reportList = reportInfoMapper.selectByExample(example);
        if(Func.isNotEmpty(reportList)){
            firstReport = reportList.stream().sorted(Comparator.comparing(ReportInfoPO::getReportNo)).findFirst().orElse(null);
        }
        Boolean autoAssignReportMatrix = cn.hutool.core.util.ObjectUtil.defaultIfNull(req.getAutoAssignReportMatrix(),Boolean.TRUE);
        if(autoAssignReportMatrix
                &&Func.isNotEmpty(reportInfoPO)
                &&ReportStatus.check(reportInfoPO.getReportStatus(),ReportStatus.New,ReportStatus.Draft)){
            for (TestMatrixPO testMatrixPO : waitAddMatrixList) {
                ReportMatrixRelationShipInfoPO po = new ReportMatrixRelationShipInfoPO();
                //过滤出reportFlag为2 Self Generate的report
                List<ReportInfoPO> groupReportList = reportListByOrderNo.stream().filter(s->s.getReportFlag() == null || s.getReportFlag()== ReportFlagEnums.REPORT.getCode()).collect(Collectors.toList());
                if(Func.isEmpty(groupReportList) || groupReportList.size()==1){
                    if(Func.isEmpty(groupReportList)){
                        if(Func.isEmpty(firstReport)){
                            result.setSuccess(false);
                            result.setMsg("not found 01 report");
                            return result;
                        }else{
                            po.setReportID(firstReport.getID());
                        }
                    }else{
                        po.setReportID(groupReportList.get(0).getID());
                    }
                    po.setID(UUID.randomUUID().toString());
                    po.setTestMatrixID(testMatrixPO.getID());
                    po.setModifiedBy(user.getRegionAccount());
                    po.setModifiedDate(DateUtils.getNow());
                    po.setCreatedBy(user.getRegionAccount());
                    po.setCreatedDate(DateUtils.getNow());
                    waitAddReportMatrixRelList.add(po);
                }
            }
        }
        //根据删除的sampelRel 删除对应limitGroup limit 等数据
        List<LimitGroupInstancePO> waitDeleteLimitGroupList = Lists.newArrayList();
        List<String> waitDeleteProductAttributeByLimitGroupIdsList = Lists.newArrayList();
        List<LimitInstancePO> waitDeleteLimitByParamList = Lists.newArrayList();
        List<String> waitDeleteConclusionByPPSampleRelIdList = Lists.newArrayList();
        for (String deleteSampleRelId : waitDeleteSampleRelList) {
            List<Long> ppBaseIdList = sampleIdPPIDListMap.get(deleteSampleRelId);
            List<LimitGroupInstancePO> shouldDeleteLimitGroup = allLimitGroupList.stream()
                    .filter(lg -> deleteSampleRelId.equals(lg.getTestSampleID()) && ppBaseIdList.contains(lg.getPpBaseId()))
                    .collect(Collectors.toList());
            List<String> shouldDeleteLimitGroupIdsList = shouldDeleteLimitGroup.stream().map(l -> l.getID()).collect(Collectors.toList());
            waitDeleteLimitGroupList.addAll(shouldDeleteLimitGroup);
            waitDeleteProductAttributeByLimitGroupIdsList.addAll(shouldDeleteLimitGroupIdsList);
            //删除limit 还有一个 需要用参数去删除，三个参数是sampleId ，tlId,ppBaseId
            PPSampleRelInfo deletePPSampleRelInfo = ppSampleRelIdPOMap.get(deleteSampleRelId);
            String ppTLRelId = deletePPSampleRelInfo.getPpTLRelId();
            //拼装删除数据
            PPTestLineRelationshipInfoPO ppTestLineRelationshipInfoPO = pptlRelIdMap.get(ppTLRelId);
            String testLineInstanceID = ppTestLineRelationshipInfoPO.getTestLineInstanceID();
            Long ppArtifactRelId = ppTestLineRelationshipInfoPO.getPpArtifactRelId();
            Long ppBaseId = trimsPPTLRelIdMap.containsKey(ppArtifactRelId) ? ppTestLineRelationshipInfoPO.getPpBaseId(): null;
            LimitInstancePO deleteLimitInstancePO = new LimitInstancePO();
            deleteLimitInstancePO.setTestSampleID(deleteSampleRelId);
            deleteLimitInstancePO.setTestLineInstanceID(testLineInstanceID);
            deleteLimitInstancePO.setPpBaseId(ppBaseId);
            waitDeleteLimitByParamList.add(deleteLimitInstancePO);
            //删除conclusion的list
            waitDeleteConclusionByPPSampleRelIdList.add(deletePPSampleRelInfo.getPpSampleRelId());
        }

        //从所有存在matrix中的tl 找到
        List<String> waitDeletePositionByMatrixList = Lists.newArrayList();
        List<String> waitDeleteProductAttributeByMatrixList = Lists.newArrayList();
        List<String> waitDeleteConclusionByMatrixIdList = Lists.newArrayList();
        List<String> waitDeleteReportMatrixRelByMatrixIdList = Lists.newArrayList();
        List<String> waitDeleteConditionByIdsList = Lists.newArrayList();
        List<String> waitDeleteConditionGroupByIdsList = Lists.newArrayList();
        List<String> waitDeleteLimitByIdsList = Lists.newArrayList();
        if(!CollectionUtils.isEmpty(waitDeleteMatrixList)){
            //根据matrix的删除，同步删除position
            Set<String> deleteMatrixId = waitDeleteMatrixList.stream().map(m -> m.getID()).collect(Collectors.toSet());
            //根据要删除的matrix，删除对应reportmatrx
            waitDeleteReportMatrixRelByMatrixIdList.addAll(deleteMatrixId);
            waitDeletePositionByMatrixList.addAll(deleteMatrixId);
            //limit 处理
            List<String> shouldDeleteLimitIdList = allLimitList.stream().filter(l -> deleteMatrixId.contains(l.getTestMatrixID())).map(l -> l.getID()).collect(Collectors.toList());
            //同步删除limit /limit lanugae
            waitDeleteLimitByIdsList.addAll(shouldDeleteLimitIdList);
            //根据matrix的删除，同步删除tb_product_attribute_instance
            waitDeleteProductAttributeByMatrixList.addAll(deleteMatrixId);
            //有删除matrix，同步删除conclusion
            waitDeleteConclusionByMatrixIdList.addAll(deleteMatrixId);
            //有删除matrix，需要更新report的recalculartionFlag =2
            if(Func.isNotEmpty(reportInfoPO)) {
                Integer recalculationFlag = reportInfoPO.getRecalculationFlag();
                if (recalculationFlag != null && recalculationFlag.compareTo(1) == 0) {
                    reportInfoPO.setRecalculationFlag(2);
                }
            }
            //根据tl+sampleId找到需要删除的condition
            Set<String> tlSampleIdSet = waitDeleteMatrixList.stream().map(po -> po.getTestLineInstanceID() + "_" + po.getTestSampleID()).collect(Collectors.toSet());
            List<TestConditionInfoPO> shouldDeleteCondition = allConditionList.stream().filter(c -> tlSampleIdSet.contains(c.getTestLineInstanceID() + "_" + c.getTestSampleID())).collect(Collectors.toList());
            List<String> shouldDeleteConditionIds = shouldDeleteCondition.stream().map(c -> c.getID()).collect(Collectors.toList());
            waitDeleteConditionByIdsList.addAll(shouldDeleteConditionIds);
            //同步删除对应的conditongroup/language
            List<String> shouldDeleteConditionGroup = waitDeleteMatrixList.stream()
                    .filter(m -> StringUtils.isNotBlank(m.getTestConditionGroupID()))
                    .map(m -> m.getTestConditionGroupID())
                    .collect(Collectors.toList());
            //从正常db中，查找是否还有关联关系，如果有，就不删除，因为有主外键关系
            List<TestMatrixPO> stillExistsGroup = allTestMatrixList.stream().filter(m -> shouldDeleteConditionGroup.contains(m.getTestConditionGroupID())).collect(Collectors.toList());
            if(CollectionUtils.isEmpty(stillExistsGroup)){
                waitDeleteConditionGroupByIdsList.addAll(shouldDeleteConditionGroup);
            }

        }
        //根据db存留的matrix，找到需要更新tlconditionStatus的TL
        List<TestLineInstancePO> waitUpdateTestLineInsatnce = Lists.newArrayList();
        if(!CollectionUtils.isEmpty(waitAddMatrixList) || !CollectionUtils.isEmpty(waitDeleteMatrixList)){
            List<String> addMatrixTlIdList = waitAddMatrixList.stream().map(m -> m.getTestLineInstanceID()).collect(Collectors.toList());
            List<String> delMatrixTlIdList = waitDeleteMatrixList.stream().map(m -> m.getTestLineInstanceID()).collect(Collectors.toList());

            List<TestLineInstancePO> shouldUpdateConditionStatusTlList = allTestLineList.stream()
                    .filter(tl -> addMatrixTlIdList.contains(tl.getID()) || delMatrixTlIdList.contains(tl.getID()))
                    .filter(tl->tl.getConditionStatus()!=null && tl.getConditionStatus().compareTo(1001)!=0)
                    .collect(Collectors.toList());
            List<TestLineInstancePO> shouldUpdateTLs = shouldUpdateConditionStatusTlList.stream().map(tl -> {
                TestLineInstancePO po = new TestLineInstancePO();
                po.setID(tl.getID());
                po.setConditionStatus(ConditionStatus.UnConfirmed.getStatus());
                po.setModifiedBy(user.getRegionAccount());
                po.setModifiedDate(new Date());
                return po;
            }).collect(Collectors.toList());
            waitUpdateTestLineInsatnce.addAll(shouldUpdateTLs);
        }
        List<ReportMatrixDTO> oldReportMatrixList = new ArrayList<>();
        if(Func.isNotEmpty(optionsTLIDSet)){
            oldReportMatrixList = reportMatrixRelMapper.getReportMatrixByTestLineInstanceIdList(new ArrayList<>(optionsTLIDSet));
        }
        List<ReportMatrixDTO> finalOldReportMatrixList = oldReportMatrixList;
        Integer execute = transactionTemplate.execute(trans -> {
            final int[] i = {1};
            //删除reportMatrix
            if(!CollectionUtils.isEmpty(waitDeleteReportMatrixRelByMatrixIdList)){
                i[0] = this.reportMatrixRelMapper.deleteByMatrixIDList(waitDeleteReportMatrixRelByMatrixIdList);
                this.rollBack(i[0],trans);
            }
            //删除condition
            if(!CollectionUtils.isEmpty(waitDeleteConditionByIdsList)){
                i[0] = this.testConditionMapper.deleteByIds(waitDeleteConditionByIdsList);
                this.rollBack(i[0],trans);
            }

            //删除conditionGroupMultipleLanguage
            if(!CollectionUtils.isEmpty(waitDeleteConditionGroupByIdsList)){
                i[0] = this.testConditionGroupLanguageMapper.deleteByConditionGroupIds(waitDeleteConditionGroupByIdsList);
                this.rollBack(i[0],trans);
            }
            //删除position
            if(!CollectionUtils.isEmpty(waitDeletePositionByMatrixList)){
                i[0] = this.testPositionMapper.deleteBytestMatrixIDs(waitDeletePositionByMatrixList);
                this.rollBack(i[0],trans);
            }
            //删除productAttr
            if(!CollectionUtils.isEmpty(waitDeleteProductAttributeByMatrixList)){
                i[0] = this.productAttrMapper.deleteByTestMatrixIDs(waitDeleteProductAttributeByMatrixList);
                this.rollBack(i[0],trans);
            }
            //删除productAttr
            if(!CollectionUtils.isEmpty(waitDeleteProductAttributeByLimitGroupIdsList)){
                i[0] = this.productAttrMapper.deleteByLimitGroupIds(waitDeleteProductAttributeByLimitGroupIdsList);
                this.rollBack(i[0],trans);
            }
            //删除limit
            if(!CollectionUtils.isEmpty(waitDeleteLimitByParamList)){
                i[0] = this.limitMapper.deleteLimitAndTestDataFailed(waitDeleteLimitByParamList);
                this.rollBack(i[0],trans);
            }
            //通过matrixid 删除limit limitLanguage
            if(!CollectionUtils.isEmpty(waitDeleteLimitByIdsList)){
                i[0] = this.limitMapper.batchDelete(waitDeleteLimitByIdsList);
                this.rollBack(i[0],trans);
            }
            //删除limitGroup
            if(!CollectionUtils.isEmpty(waitDeleteLimitGroupList)){
                List<String> list = waitDeleteLimitGroupList.stream().map(m -> m.getID()).collect(Collectors.toList());
                i[0] = this.limitGroupMapper.batchDelete(list);
                this.rollBack(i[0],trans);
            }
            //删除conclusion
            if(!CollectionUtils.isEmpty(waitDeleteConclusionByPPSampleRelIdList)){
                i[0] = this.conclusionMapper.deleteConclusionByPPSampleRelIDs(waitDeleteConclusionByPPSampleRelIdList);
                this.rollBack(i[0],trans);
            }
            //删除conclusion
            if(!CollectionUtils.isEmpty(waitDeleteConclusionByMatrixIdList)){
                i[0] = this.conclusionMapper.batchDelete(waitDeleteConclusionByMatrixIdList);
                this.rollBack(i[0],trans);
            }
            //删除ppSampleRel
            if(!CollectionUtils.isEmpty(waitDeleteSampleRelList)){
                i[0] = this.ppSampleRelMapper.deleteByIds(waitDeleteSampleRelList);
                this.rollBack(this.ppSampleRelMapper.deleteByIds(waitDeleteSampleRelList),trans);
            }
            //删除conditionGroup
            if(!CollectionUtils.isEmpty(waitDeleteConditionGroupByIdsList)){
                i[0] = this.testConditionGroupMapper.deleteByIds(waitDeleteConditionGroupByIdsList);
                this.rollBack(i[0],trans);
            }
            //删除matrix
            if(!CollectionUtils.isEmpty(waitDeleteMatrixList)){
                List<String> list = waitDeleteMatrixList.stream().map(m -> m.getID()).collect(Collectors.toList());
                i[0] = this.testMatrixMapper.batchDeleteMatrix(list);
                this.rollBack(i[0],trans);
            }

            //添加ppSampleRel
            if(!CollectionUtils.isEmpty(waitAddSampleRelList)){
                i[0] = this.ppSampleRelMapper.batchInsert(waitAddSampleRelList);
                this.rollBack(i[0],trans);
            }
            //添加matrix
            if(!CollectionUtils.isEmpty(waitAddMatrixList)){
                i[0] = this.testMatrixMapper.batchInsert(waitAddMatrixList);
                this.rollBack(i[0],trans);
            }
            //添加reportMatrix
            if(!CollectionUtils.isEmpty(waitAddReportMatrixRelList)){
                i[0] = this.reportMatrixRelMapper.batchInsert(waitAddReportMatrixRelList);
                this.rollBack(i[0],trans);

            }

            //更新testLine
            if(!CollectionUtils.isEmpty(waitUpdateTestLineInsatnce)){
                i[0] = this.testLineMapper.updateBatchConditionStatus(waitUpdateTestLineInsatnce);
                this.rollBack(i[0],trans);
            }
            Set<String> combinedReportIds = new HashSet<>();
            if (Func.isNotEmpty(finalOldReportMatrixList)) {
                combinedReportIds.addAll(
                        finalOldReportMatrixList.stream()
                                .map(ReportMatrixDTO::getReportId)
                                .filter(Objects::nonNull)
                                .collect(Collectors.toSet())
                );
            }
            if (Func.isNotEmpty(waitAddReportMatrixRelList)) {
                combinedReportIds.addAll(
                        waitAddReportMatrixRelList.stream()
                                .map(ReportMatrixRelationShipInfoPO::getReportID)
                                .filter(Objects::nonNull)
                                .collect(Collectors.toSet())
                );
            }
            if(Func.isNotEmpty(combinedReportIds)){
                // 事务提交后再执行updateReportExt
                TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
                    @Override
                    public void afterCommit() {
                        TLDataHeaderSaveReq tlDataHeaderSaveReq = new TLDataHeaderSaveReq();
                        tlDataHeaderSaveReq.setToken(SystemContextHolder.getSgsToken());
                        tlDataHeaderSaveReq.setProductLineCode(ProductLineContextHolder.getProductLineCode());
                        tlDataHeaderSaveReq.setReportIdList(combinedReportIds);
                        tlDataHeaderSaveReq.setCheckFlag(true);
                        tlDataHeaderSaveReq.setOrderNo(orderNo);
                        try {
                            BaseResponse<TLDataHeaderSaveRsp> checkSaveTLReportResponse = reportTempFacade.saveTestDataHeader(tlDataHeaderSaveReq);
                            if(checkSaveTLReportResponse.isFail()){
                                trans.setRollbackOnly();
                                result.setSuccess(false);
                                result.setMsg(checkSaveTLReportResponse.getMessage());
                                if(Func.isNotEmpty(checkSaveTLReportResponse.getData())){
                                    result.setData(checkSaveTLReportResponse.getData().getCheckResultList());
                                }
                            }
                        } catch (Exception e) {
                            logger.error("assign Sample create Tl Report error:{}",e);
                            trans.setRollbackOnly();
                            result.setSuccess(false);
                            result.setMsg(e.getMessage());
                            i[0] = -1;
                        }
                        try {
                            ReportExtForTLUpdateReq reportExtForTLUpdateReq = new ReportExtForTLUpdateReq();
                            reportExtForTLUpdateReq.setReportIdList(new HashSet<>(combinedReportIds));
                            reportExtForTLUpdateReq.setToken(SecurityUtil.getSgsToken());
                            reportExtForTLUpdateReq.setProductLineCode(ProductLineContextHolder.getProductLineCode());
                            reportTempFacade.updateReportExtForTL(reportExtForTLUpdateReq);
                        } catch (Exception e) {
                            logger.error("assignSample updateReportExt error:{}",e);
                        }
                    }
                });
            }
            return i[0];
        });
        if(execute>=0){
            result.setSuccess(true);
            result.setData(true);
        }
        return result;
    }

    /**
     *
     * @param reqObject
     * @return
     */
    public CustomResult syncAzureTestMatrixInfo(SyncAzureTestMatrixInfo reqObject){
        CustomResult rspResult = new CustomResult();

        Integer execute = transactionTemplate.execute(trans -> {
            int i = 0;
            //删除reportMatrix
            if(!CollectionUtils.isEmpty(reqObject.getDelReportMatrixRelIds())){
                i = this.reportMatrixRelMapper.deleteByMatrixIDList(reqObject.getDelReportMatrixRelIds());
                this.rollBack(i,trans);
            }
            //删除condition
            if(!CollectionUtils.isEmpty(reqObject.getDelConditionIds())){
                i = this.testConditionMapper.deleteByIds(reqObject.getDelConditionIds());
                this.rollBack(i,trans);
            }

            //删除conditionGroupMultipleLanguage
            if(!CollectionUtils.isEmpty(reqObject.getDelConditionGroupIds())){
                i = this.testConditionGroupLanguageMapper.deleteByConditionGroupIds(reqObject.getDelConditionGroupIds());
                this.rollBack(i,trans);
            }
            //删除position
            if(!CollectionUtils.isEmpty(reqObject.getDelTestPositionIds())){
                i = this.testPositionMapper.deleteBytestMatrixIDs(reqObject.getDelTestPositionIds());
                this.rollBack(i,trans);
            }
            //删除productAttr
            if(!CollectionUtils.isEmpty(reqObject.getDelProductAttrIds())){
                i = this.productAttrMapper.deleteByTestMatrixIDs(reqObject.getDelProductAttrIds());
                this.rollBack(i,trans);
            }
            //删除productAttr
            if(!CollectionUtils.isEmpty(reqObject.getDelProductAttrLimitGroupIds())){
                i = this.productAttrMapper.deleteByLimitGroupIds(reqObject.getDelProductAttrLimitGroupIds());
                this.rollBack(i,trans);
            }
            //删除limit
            if(!CollectionUtils.isEmpty(reqObject.getDelTestDataIds())){
                i = this.limitMapper.deleteLimitAndTestDataFailed(reqObject.getDelTestDataIds());
                this.rollBack(i,trans);
            }
            //通过matrixid 删除limit limitLanguage
            if(!CollectionUtils.isEmpty(reqObject.getDelLimitIds())){
                i = this.limitMapper.batchDelete(reqObject.getDelLimitIds());
                this.rollBack(i,trans);
            }
            //删除limitGroup
            if(!CollectionUtils.isEmpty(reqObject.getDelLimitGroupIds())){
                i = this.limitGroupMapper.batchDelete(reqObject.getDelLimitGroupIds());
                this.rollBack(i,trans);
            }
            //删除conclusion
            if(!CollectionUtils.isEmpty(reqObject.getDelConclusionPpSampleRelIds())){
                i = this.conclusionMapper.deleteConclusionByPPSampleRelIDs(reqObject.getDelConclusionPpSampleRelIds());
                this.rollBack(i,trans);
            }
            //删除conclusion
            if(!CollectionUtils.isEmpty(reqObject.getDelConclusionIds())){
                i = this.conclusionMapper.batchDelete(reqObject.getDelConclusionIds());
                this.rollBack(i,trans);
            }
            //删除ppSampleRel
            if(!CollectionUtils.isEmpty(reqObject.getDelPpSampleRelIds())){
                i = this.ppSampleRelMapper.deleteByIds(reqObject.getDelPpSampleRelIds());
                this.rollBack(this.ppSampleRelMapper.deleteByIds(reqObject.getDelPpSampleRelIds()),trans);
            }
            //删除conditionGroup
            if(!CollectionUtils.isEmpty(reqObject.getDelTestConditionGroupIds())){
                i = this.testConditionGroupMapper.deleteByIds(reqObject.getDelTestConditionGroupIds());
                this.rollBack(i,trans);
            }
            //删除matrix
            if(!CollectionUtils.isEmpty(reqObject.getDelTestMatrixIds())){
                i = this.testMatrixMapper.batchDeleteMatrix(reqObject.getDelTestMatrixIds());
                this.rollBack(i,trans);
            }

            //添加ppSampleRel
            if(!CollectionUtils.isEmpty(reqObject.getPpSampleRels())){
                i = this.ppSampleRelMapper.batchInsert(reqObject.getPpSampleRels());
                this.rollBack(i,trans);
            }
            //添加matrix
            if(!CollectionUtils.isEmpty(reqObject.getTestMatrixs())){
                i = this.testMatrixMapper.batchInsert(reqObject.getTestMatrixs());
                this.rollBack(i,trans);
            }
            //添加reportMatrix
            if(!CollectionUtils.isEmpty(reqObject.getReportMatrixRels())){
                i = this.reportMatrixRelMapper.batchInsert(reqObject.getReportMatrixRels());
                this.rollBack(i,trans);
            }

            //更新testLine
            if(!CollectionUtils.isEmpty(reqObject.getConditionStatus())){
                i = this.testLineMapper.updateBatchConditionStatus(reqObject.getConditionStatus());
                this.rollBack(i,trans);
            }
            return i;
        });

        return rspResult;
    }

    private void rollBack(int i, TransactionStatus trans){
        if(i<0){
            trans.setRollbackOnly();
        }
    }

    public void updateMixSampleNo(String orderNo) {
        TestSampleInfoExample objTestSampleInfoExample=new TestSampleInfoExample();
        objTestSampleInfoExample.createCriteria().andOrderNoEqualTo(orderNo).andSampleTypeEqualTo(104);
        List<TestSampleInfoPO> mixSampleList=testSampleInfoMapper.selectByExample(objTestSampleInfoExample);
        if (mixSampleList == null || mixSampleList.isEmpty()){
            logger.info("未找到该订单({}), sampleDao.querySampleByOrderNoAndType", orderNo);
            return;
        }
        TestSampleInfoExample objTestSampleInfoExample1=new TestSampleInfoExample();
        objTestSampleInfoExample1.createCriteria().andOrderNoEqualTo(orderNo);
        List<TestSampleInfoPO> sampleList=testSampleInfoMapper.selectByExample(objTestSampleInfoExample1);
        if (sampleList == null || sampleList.isEmpty()){
            logger.info("未找到该订单({}), sampleDao.queryAllSample", orderNo);
            return;
        }
        List<String> sampleIds = mixSampleList.stream().map(samplePO -> samplePO.getID()).collect(Collectors.toList());
        TestSampleGroupInfoExample objTestSampleGroupInfoExample=new TestSampleGroupInfoExample();
        objTestSampleGroupInfoExample.createCriteria().andSampleIDIn(sampleIds);
        List<TestSampleGroupInfoPO> sampleGroups=testSampleGroupInfoMapper.selectByExample(objTestSampleGroupInfoExample);
        if (sampleGroups == null || sampleGroups.isEmpty()){
            logger.info("未找到该订单({}), sampleGroupPODAO.getTestSampleGroupList", orderNo);
            return;
        }

        List<TestSampleInfoPO> samples = Lists.newArrayList();
        List<String> sampleNos, sampleDescs,sampleColors;
        for (TestSampleInfoPO mixSample : mixSampleList) {
            String separator = SampleGroupTypeEnums.check(mixSample.getGroupType(),SampleGroupTypeEnums.WITH)?"w/":"+";
            String separatorStr = SampleGroupTypeEnums.check(mixSample.getGroupType(),SampleGroupTypeEnums.WITH)?" w/ ":"+";
            List<TestSampleGroupInfoPO> groupList = sampleGroups.stream().filter(s->s.getSampleID().equals(mixSample.getID())).collect(Collectors.toList());
            if (groupList == null || groupList.isEmpty()){
                continue;
            }
            sampleNos = Lists.newArrayList();
            sampleDescs = Lists.newArrayList();
            sampleColors = Lists.newArrayList();
            for (TestSampleInfoPO sample : sampleList) {
                for (TestSampleGroupInfoPO group : groupList) {
                    if (!StringUtils.equalsIgnoreCase(group.getSampleGroupID(), sample.getID())){
                        continue;
                    }
                    sampleNos.add(sample.getSampleNo());
                    sampleDescs.add(sample.getDescription());
                    sampleColors.add(sample.getColor());
                }
            }
            mixSample.setSampleNo(StringUtils.join(sampleNos, separator));
            mixSample.setDescription(StringUtils.join(sampleDescs, separatorStr));
            mixSample.setColor(StringUtils.join(sampleColors, separatorStr));
            samples.add(mixSample);
        }
        if (!samples.isEmpty()){

            testSampleMapper.batchUpdateSampleNo(samples);
        }
    }

    private TestMatrixPO createTestMatrixPO(String sampleId,String tlId,String orderId, String userName, Long ppBaseId,Integer citationId){
        TestMatrixPO po = new TestMatrixPO();
        po.setID(UUID.randomUUID().toString());
        po.setModifiedDate(DateUtils.getNow());
        po.setModifiedBy(userName);
        po.setCreatedDate(DateUtils.getNow());
        po.setCreatedBy(userName);
        po.setActiveIndicator(true);
        po.setMatrixGroupId(0);
        po.setTestSampleID(sampleId);
        po.setTestLineInstanceID(tlId);
        po.setGeneralOrderInstanceID(orderId);
        po.setMatrixStatus(MatrixStatus.Typing.getStatus());
        //判断当前TL是否为客供 非客供自动Confirm Matrix
        if(Func.equals(ProductLineContextHolder.getProductLineCode(), ProductLineType.HL.getProductLineAbbr()) && !citationUtil.isProvideByClientStandard(ppBaseId,Func.toLong(citationId))){
            po.setMatrixConfirmDate(DateUtils.getNow());
        }
        return po;
    }

    private PPSampleRelationshipInfoPO createPPSampleRelPO(String sampleId,String pptlId,String userName){
        PPSampleRelationshipInfoPO po = new PPSampleRelationshipInfoPO();
        po.setID(UUID.randomUUID().toString());
        po.setPPTLRelID(pptlId);
        po.setTestSampleID(sampleId);
        po.setCreatedBy(userName);
        po.setModifiedBy(userName);
        po.setCreatedDate(DateUtils.getNow());
        po.setModifiedDate(DateUtils.getNow());
        return po;
    }

    @AccessPolicyRule(subContractType = SubContractOperationTypeEnums.CancelAssignSample)
    public CustomResult assignSampleCancel(AssignSampleCancelReq req) {
        UserInfo user = UserHelper.getLocalUser();
        CustomResult<Boolean> result = new CustomResult<>();
        String testSampleId = req.getTestSampleId();
        String testLineInstanceId = req.getTestLineInstanceId();
        int matrixGroupId = req.getMatrixGroupId();
        if(StringUtils.isBlank(testSampleId) || StringUtils.isBlank(testLineInstanceId)){
            result.setMsg("Params Error");
            result.setSuccess(false);
            return result;
        }

        TestMatrixInfoExample example = new TestMatrixInfoExample();
        example.createCriteria().andTestSampleIDEqualTo(testSampleId)
                .andTestLineInstanceIDEqualTo(testLineInstanceId);
                //.andMatrixGroupIdEqualTo(matrixGroupId);
        List<TestMatrixInfoPO> testMatrixInfoPOS = testMatrixInfoMapper.selectByExample(example);
        if(CollectionUtils.isEmpty(testMatrixInfoPOS)){
            result.setMsg("Query Data Error");
            result.setSuccess(false);
            return result;
        }
        TestMatrixInfoPO testMatrixInfoPO = testMatrixInfoPOS.get(0);
        Boolean activeIndicator = testMatrixInfoPO.getActiveIndicator();

        TestDataInfoPO updatePO = new TestDataInfoPO();
        updatePO.setTestSampleID(testSampleId);
        updatePO.setTestLineInstanceID(testLineInstanceId);
        updatePO.setModifiedBy(user.getRegionAccount());
        updatePO.setModifiedDate(new Date());
        //查询时是true的话，那么更新后就是false，因此testData要增加NoNeedTest
        if(activeIndicator){
            updatePO.setTestValueRemark("NoNeedTest");
        }
        GeneralOrderInstanceInfoPO orderInstanceInfoPO = orderMapper.getOrderByTestLineInstanceId(testLineInstanceId);
        ReportInfoPO reportInfo = reportMapper.getReportByOrderNo(orderInstanceInfoPO.getOrderNo());

        Integer execute = transactionTemplate.execute(trans -> {
            int i = 0;
            //更新matrix表
            for (TestMatrixInfoPO matrixInfoPO : testMatrixInfoPOS) {
                //进行数据更新
                TestMatrixPO matrixPO = new TestMatrixPO();
                matrixPO.setTestLineInstanceID(testLineInstanceId);
                matrixPO.setTestSampleID(testSampleId);
                matrixPO.setMatrixGroupId(matrixInfoPO.getMatrixGroupId());
                matrixPO.setActiveIndicator(!activeIndicator);
                matrixPO.setModifiedBy(user.getRegionAccount());
                matrixPO.setModifiedDate(DateUtils.getNow());
                i = testMatrixMapper.updateActiveByParam(matrixPO);
                if (i < 0) {
                    trans.setRollbackOnly();
                }
            }
            //更新testData表数据为NoNeedTest DIG-2206

            i = testDataMapper.updateTestDataNoNeedTest(updatePO);
            if (i < 0) {
                trans.setRollbackOnly();
            }

            TestMatrixPO matrixPO = new TestMatrixPO();
            matrixPO.setTestLineInstanceID(testLineInstanceId);
            matrixPO.setTestSampleID(testSampleId);
            matrixPO.setActiveIndicator(!activeIndicator);
            matrixPO.setModifiedBy(user.getRegionAccount());
            matrixPO.setModifiedDate(DateUtils.getNow());
            int j = testMatrixMapper.updateBySampleIdAndTestLineId(matrixPO);
            //按照老逻辑，需要继续更新别的，如果i小于等于0，不用触发回滚
            if (j > 0 && reportInfo.getRecalculationFlag() != null && reportInfo.getRecalculationFlag().compareTo(1) == 0) {
                reportInfo.setRecalculationFlag(2);
                reportInfo.setModifiedBy(user.getRegionAccount());
                reportInfo.setModifiedDate(DateUtils.getNow());
                reportMapper.updateRecalculationFlagByReportId(reportInfo);
            }
            return i;
        });

        //TODO 需要插入Log
        result.setSuccess(execute > -1);
        result.setData(true);
        return result;
    }

//    @TestLinePending(filedName = "testLineInstanceId",type=TestLinePendingTypeEnums.TL_ID)
    @AccessPolicyRule(testLinePendingType = TestLinePendingTypeEnums.TestLineInstanceId)
    public CustomResult<List<CopyTestSampleRsp>> copyTestLineGetSample(CopyTestLineGetSampleReq copyTestLineGetSampleReq) {
        String testLineInstanceId =copyTestLineGetSampleReq.getTestLineInstanceId();
        String matrixGroupId = copyTestLineGetSampleReq.getMatrixGroupId();
        CustomResult<List<CopyTestSampleRsp>> rspResult = new CustomResult<>();

        //校验是否内部分包 并且lock
        CustomResult result = testMatrixService.checkTLSubDataLock(copyTestLineGetSampleReq.getTestLineInstanceId());
        if(!result.isSuccess()){
            return result;
        }


        List<CopyTestSampleRsp> list = Lists.newArrayList();
        List<TestSampleInfoPO> copyTestSamples = testSampleExtMapper.getCopyTestSample(matrixGroupId, testLineInstanceId);
        if (! CollectionUtils.isEmpty(copyTestSamples)) {
            for (TestSampleInfoPO copyTestSample : copyTestSamples) {
                CopyTestSampleRsp copyTestSampleRsp = new CopyTestSampleRsp();
                BeanUtils.copyProperties(copyTestSample,copyTestSampleRsp);
                list.add(copyTestSampleRsp);
            }
        }
        rspResult.setData(list);
        rspResult.setSuccess(true);
        return rspResult;
    }

    /**
     *
     * @param sampleReq
     * @return
     */
    public List<TestSampleRsp> getSampleListByOrderNoList(SampleReq sampleReq){
        List<TestSampleRsp> testSampleRspList = Lists.newArrayList();
        if (sampleReq == null || sampleReq.getOrderNoList() == null || sampleReq.getOrderNoList().size() <= 0) {
            return testSampleRspList;
        }

        List<TestSampleInfoPO> testSampleInfoPOList = testSampleExtMapper.getSampleListByOrderNoList(sampleReq.getOrderNoList());
        if (testSampleInfoPOList == null || testSampleInfoPOList.isEmpty()){
            return testSampleRspList;
        }

        if (! CollectionUtils.isEmpty(testSampleInfoPOList)) {
            for (TestSampleInfoPO testSampleInfoPO : testSampleInfoPOList) {
                TestSampleRsp testSampleRsp = new TestSampleRsp();
                BeanUtils.copyProperties(testSampleInfoPO,testSampleRsp);
                testSampleRsp.setId(testSampleInfoPO.getID());
                if (testSampleInfoPO.getActiveIndicator()) {
                    testSampleRsp.setActiveIndicator(ActiveType.Enable.getStatus());
                } else {
                    testSampleRsp.setActiveIndicator(ActiveType.Disable.getStatus());
                }
                testSampleRspList.add(testSampleRsp);
            }
        }

        return testSampleRspList;
    }


    /**
     *
     * @param sampleReq
     * @return
     */
    public BaseResponse<TrackOrderStartDateDTO> querySampleStartDate(SampleStartRequest sampleReq){
        BaseResponse<TrackOrderStartDateDTO> baseResponse = new BaseResponse<>();
        TrackOrderStartDateDTO trackOrderStartDateDTO = new TrackOrderStartDateDTO();
        List<TrackingOrderDTO>  trackingOrderDTOList = new ArrayList<>();
        if(CollectionUtils.isEmpty(sampleReq.getObjectCodes()))
        {
            return  BaseResponse.newFailInstance("request param is null");
        }


        for (String objCode:sampleReq.getObjectCodes())
        {
            if(objCode.split(" ").length !=2 ) {
                //不和规范的 objcode 忽略
                continue;
            }
            String externalOrderNo = objCode.split(" ")[0];
            //转化成内部号
            String orderNo = orderClient.getOrderNoByExternalOrderNo(externalOrderNo, sampleReq.getProductLineCode());
            if(Func.isEmpty(orderNo)){
                orderNo = externalOrderNo;
            }

            String sampleNo = objCode.split(" ")[1];
            if(sampleNo.contains(StringPool.DASH)){
                sampleNo = (sampleNo.split(StringPool.DASH)[0]).trim();
            }
            //校验sampleNo是否有效
            List<String> sampleNos = new ArrayList<>();
            sampleNos.add(sampleNo);
            List<TestSampleInfoPO> testSampleInfoPOList = testSampleMapper.getSamplesByOrderNoAndSampleNos(orderNo, sampleNos);
            //先用OrderNo + SampleNo查询，查询不到再作为MatrixNo查询
            if(Func.isEmpty(testSampleInfoPOList)){
                testSampleInfoPOList = testSampleMapper.getSampleInfoByMatrixNo(objCode);
            }
            if(Func.isEmpty(testSampleInfoPOList)){
                logger.info("[{}],Not Find SampleInfo from GPO",objCode);
                continue;
//                return  BaseResponse.newFailInstance("By " + objCode + " Not Find SampleInfo");
            }
            orderNo = testSampleInfoPOList.get(0).getOrderNo();
            OrderIdReq orderIdReq = new OrderIdReq();
            orderIdReq.setOrderNo(orderNo);
            orderIdReq.setProductLineCode(ProductLineContextHolder.getProductLineCode());
            OrderAllDTO orderAllDTO = gpoOrderFacade.getOrderForPe(orderIdReq).getData();
            if(Func.isEmpty(orderAllDTO)){
                logger.info("[{}],Not Find OrderInfo from GPO",objCode);
                continue;
//                return  BaseResponse.newFailInstance("Not Find OrderInfo");
            }
            TrackingOrderDTO trackingOrderDTO = new TrackingOrderDTO();
            Date orderCompletedDate = orderAllDTO.getCompletedDate();
            BaseResponse<TestRequestRsp> testRequestRspBaseResponse = gpoOrderFacade.getTestRequestByOrderNo(orderIdReq);
            if(Func.isNotEmpty(testRequestRspBaseResponse) && Func.isNotEmpty(testRequestRspBaseResponse.getData())){
                TestRequestRsp testRequestRsp = testRequestRspBaseResponse.getData();
                Integer sampleSaveDuration = testRequestRsp.getSampleSaveDuration();
                if(Func.isEmpty(sampleSaveDuration)){
                    logger.info("[{}],未维护实验室样品保存周期",objCode);
                    continue;
//                    return BaseResponse.newFailInstance("未维护实验室样品保存周期");
                }
                trackingOrderDTO.setSaveDuration(sampleSaveDuration);
                if((Func.isNotEmpty(testRequestRsp.getReturnResidueSampleFlag()) && testRequestRsp.getReturnResidueSampleFlag() ==1) || (Func.isNotEmpty(testRequestRsp.getReturnTestedSampleFlag()) && testRequestRsp.getReturnTestedSampleFlag() == 1)){
                    trackingOrderDTO.setNeedReturnToCust(1);
                }else{
                    trackingOrderDTO.setNeedReturnToCust(0);
                }
            }else{
                trackingOrderDTO.setNeedReturnToCust(0);
            }
            trackingOrderDTO.setObjectCode(objCode);
            trackingOrderDTO.setSaveStartDate(DateUtils.format(orderCompletedDate));
            trackingOrderDTO.setOrderNo(externalOrderNo);
            trackingOrderDTOList.add(trackingOrderDTO);
        }

        trackOrderStartDateDTO.setTrackingOrderList(trackingOrderDTOList);
        baseResponse.setData(trackOrderStartDateDTO);
        return baseResponse;
    }

    /**
     *  批量查询
     * @param sampleReq
     * @return
     */
    public BaseResponse<TrackOrderStartDateDTO> batchQuerySampleStartDate(SampleStartRequest sampleReq){
        BaseResponse<TrackOrderStartDateDTO> baseResponse = new BaseResponse<>();
        TrackOrderStartDateDTO trackOrderStartDateDTO = new TrackOrderStartDateDTO();
        List<TrackingOrderDTO>  trackingOrderDTOList = new ArrayList<>();
        List<String> objectCodes = sampleReq.getObjectCodes();
        if(CollectionUtils.isEmpty(objectCodes)) {
            return  BaseResponse.newFailInstance("request param is null");
        }

        //入参保存
        List<String> externalOrderNos = new ArrayList<>();
        List<TestSampleOrderPO> testSampleOrderPOS = new ArrayList<>();
        for (String objCode:objectCodes) {
            if(objCode.split(" ").length !=2 ) {
                //不和规范的 objcode 忽略
                continue;
            }
            TestSampleOrderPO testSampleOrderPO = new TestSampleOrderPO();
            //外部订单号
            String externalOrderNo = objCode.split(" ")[0];
            externalOrderNos.add(externalOrderNo);
            //样品号
            String sampleNo = objCode.split(" ")[1];
            if(sampleNo.contains(StringPool.DASH)){
                sampleNo = (sampleNo.split(StringPool.DASH)[0]).trim();
            }
            testSampleOrderPO.setExternalOrderNo(externalOrderNo);
            testSampleOrderPO.setSampleNo(sampleNo);
            testSampleOrderPO.setObjectCodes(objCode);
            testSampleOrderPOS.add(testSampleOrderPO);
        }
        //转化成内部号
        List<GpoExternalOrderInfo> gpoExternalOrderInfoList = orderClient.getExternalOrder(externalOrderNos,null, sampleReq.getProductLineCode());
        List<String> orderNoList = new ArrayList<>();
        if(Func.isNotEmpty(gpoExternalOrderInfoList)){
            orderNoList = gpoExternalOrderInfoList.stream().map(item -> Func.isEmpty(item.getOrderNo()) ? item.getExternalOrderNo() : item.getOrderNo()).collect(Collectors.toList());
        }
        OrderNosReq orderNosReq = new OrderNosReq();
        orderNosReq.setOrderNos(orderNoList);
        orderNosReq.setProductLineCode(ProductLineContextHolder.getProductLineCode());
        //查询order信息
        List<OrderAllDTO> orderAllDTOList = gpoOrderFacade.getOrderAllListByOrderNos(orderNosReq).getData();
        //查询request信息
        OrderIdReq orderIdReq = new OrderIdReq();
        orderIdReq.setOrderNos(orderNoList);
        orderIdReq.setProductLineCode(ProductLineContextHolder.getProductLineCode());
        List<TestRequestInfo> testRequestList = gpoOrderFacade.batchQueryTestRequestForPe(orderIdReq).getData();
        //内部号批量查询Sample
        List<TestSampleInfoPO> testSampleInfoPOList = testSampleMapper.getSamplesByOrderNos(orderNoList);
        List<TestSampleMatrixDTO>  testSampleMatrixDTOList = testSampleMapper.querySampleMatrixInfo(null,objectCodes);
        for (TestSampleOrderPO testSampleOrderPO:testSampleOrderPOS){

            String externalOrderNo = testSampleOrderPO.getExternalOrderNo();
            String sampleNo = testSampleOrderPO.getSampleNo();
            String objCode = testSampleOrderPO.getObjectCodes();
            String orderNo;
            GpoExternalOrderInfo gpoExternalOrderInfo = gpoExternalOrderInfoList.stream().filter(item -> Func.equalsSafe(item.getExternalOrderNo(), externalOrderNo)).findAny().orElse(null);
            if(Func.isNotEmpty(gpoExternalOrderInfo) && Func.isNotEmpty(gpoExternalOrderInfo.getOrderNo())){
                orderNo = gpoExternalOrderInfo.getOrderNo();
            } else {
                orderNo = externalOrderNo;
            }
            if(Func.isNotEmpty(testSampleInfoPOList)){
                boolean bySampleExist = testSampleInfoPOList.stream().anyMatch(item -> Func.equalsSafe(item.getOrderNo(), orderNo) && Func.equalsSafe(item.getSampleNo(), sampleNo));
                if(!bySampleExist){
                    if(Func.isNotEmpty(testSampleMatrixDTOList)){
                        boolean byMatrixExist = testSampleMatrixDTOList.stream().anyMatch(item -> Func.equalsSafe(item.getMatrixNo(), objCode));
                        if(!byMatrixExist){
                            logger.info("[{}],Not Find SampleInfo from GPO",objCode);
                            continue;
                        }
                    }
                }
            }else if(Func.isNotEmpty(testSampleMatrixDTOList)){
                boolean byMatrixExist = testSampleMatrixDTOList.stream().anyMatch(item -> Func.equalsSafe(item.getMatrixNo(), orderNo));
                if(!byMatrixExist){
                    logger.info("[{}],Not Find SampleInfo from GPO",objCode);
                    continue;
                }
            }else{
                continue;
            }
            if(Func.isEmpty(orderAllDTOList)){
                logger.info("[{}],Not Find OrderInfo from GPO",objCode);
                continue;
            }
            OrderAllDTO orderAllDTO = orderAllDTOList.stream().filter(item -> Func.equalsSafe(item.getOrderNo(), orderNo)).findAny().orElse(null);
            if(Func.isEmpty(orderAllDTO)){
                logger.info("[{}],Not Find OrderInfo from GPO",objCode);
                continue;
            }
            TrackingOrderDTO trackingOrderDTO = new TrackingOrderDTO();
            Date orderCompletedDate = orderAllDTO.getCompletedDate();

            if(Func.isNotEmpty(testRequestList)){
                TestRequestInfo testRequestInfo = testRequestList.stream().filter(item -> Func.equalsSafe(item.getOrderNo(), orderNo)).findAny().orElse(null);
                if(Func.isNotEmpty(testRequestInfo)){
                    Integer sampleSaveDuration = testRequestInfo.getSampleSaveDuration();
                    if(Func.isEmpty(sampleSaveDuration)){
                        logger.info("[{}],未维护实验室样品保存周期",objCode);
                        continue;
//                    return BaseResponse.newFailInstance("未维护实验室样品保存周期");
                    }
                    trackingOrderDTO.setSaveDuration(sampleSaveDuration);
                    if((Func.isNotEmpty(testRequestInfo.getReturnResidueSampleFlag()) && testRequestInfo.getReturnResidueSampleFlag() ==1) || (Func.isNotEmpty(testRequestInfo.getReturnTestedSampleFlag()) && testRequestInfo.getReturnTestedSampleFlag() == 1)){
                        trackingOrderDTO.setNeedReturnToCust(1);
                    }
                    List<TestRequestContactsInfo> testRequestContactsInfoList = testRequestInfo.getTestRequestContactsInfos();
                    if(Func.isNotEmpty(testRequestContactsInfoList)){
                        TestRequestContactsInfo testRequestContactsInfo = testRequestContactsInfoList.stream().filter(item -> ContactsType.check(item.getContactsType(), ContactsType.ReturnSample)).findAny().orElse(null);
                        if(Func.isNotEmpty(testRequestContactsInfo)){
                            List<String> deliverToList = new ArrayList<>();
                            if(Func.isNotEmpty(testRequestContactsInfo.getDeliverTo())){
                                List<String> deliverToCodeList = Arrays.asList(testRequestContactsInfo.getDeliverTo().split(","));
                                for (String deliverToCode : deliverToCodeList) {
                                    DeliverToType deliverToType = DeliverToType.getCode(deliverToCode);
                                    if(Func.isNotEmpty(deliverToType)  && !Func.equalsSafe(deliverToType.message(),DeliverToType.OTHERS.message())){
                                        deliverToList.add(deliverToType.message());
                                    }
                                }
                            }
                            if(Func.isNotEmpty(testRequestContactsInfo.getDeliverOthers())){
                                deliverToList.add(testRequestContactsInfo.getDeliverOthers());
                            }
                            trackingOrderDTO.setReturnSampleTo(StringUtils.join(deliverToList, ";"));
                        }
                    }
                }
            }
            trackingOrderDTO.setObjectCode(objCode);
            trackingOrderDTO.setSaveStartDate(DateUtils.format(orderCompletedDate));
            trackingOrderDTO.setOrderNo(externalOrderNo);
            trackingOrderDTOList.add(trackingOrderDTO);
        }
        trackOrderStartDateDTO.setTrackingOrderList(trackingOrderDTOList);
        baseResponse.setData(trackOrderStartDateDTO);
        return baseResponse;
    }


    private List<TrackingOrderDTO> querySampleStartDateFromHLPreorder(SampleStartRequest sampleReq){
        return hlPreOrderClient.getTrackingOrderInfo(sampleReq);
    }


    public BaseResponse<List<TrackingStockListDTO>> queryStockList(TrackingStockListReq req){
        if(Func.isEmpty(req) || Func.isEmpty(req.getOrderNos())){
            return BaseResponse.newFailInstance("request param orderNo can't be null");
        }
        List<String> orderNoList = req.getOrderNos();
        GpoExternalOrderNoReq gpoExternalOrderNoReq = new GpoExternalOrderNoReq();
        gpoExternalOrderNoReq.setProductLineCode(ProductLineContextHolder.getProductLineCode());
        gpoExternalOrderNoReq.setOrderNoList(orderNoList);
        List<GpoExternalOrderInfo> gpoExternalOrderInfoList = gpoOrderFacade.getExternalOrder(gpoExternalOrderNoReq).getData();
        if(Func.isEmpty(gpoExternalOrderInfoList)){
            return BaseResponse.newFailInstance("ExternalOrderNo Is Empty");
        }
        List<String> externalOrderNos = gpoExternalOrderInfoList.stream().map(GpoExternalOrderInfo::getExternalOrderNo).distinct().filter(Func::isNotEmpty).collect(Collectors.toList());
        req.setOrderNos(externalOrderNos);
        BaseResponse<List<TrackingStockListRsp>> trackingClientStockListRes = trackingClient.getStockList(req);
        if(trackingClientStockListRes.getStatus()!=200){
            return BaseResponse.newFailInstance(trackingClientStockListRes.getMessage());
        }
        List<TrackingStockListRsp> trackingStockListRspList = trackingClientStockListRes.getData();
        List<TrackingStockListDTO> trackingStockListDTOS = new ArrayList<>();
        if(Func.isNotEmpty(trackingStockListRspList)){
            List<TestSampleMatrixDTO> testSampleMatrixDTOS = testSampleMapper.querySampleMatrixInfo(orderNoList,null);
            Map<String,TestSampleMatrixDTO> matrixSampleMap =  testSampleMatrixDTOS.stream().collect(Collectors.toMap(TestSampleMatrixDTO :: getMatrixNo, o -> o,(key1 , key2)-> key2 ));
            List<String> testLineInstanceIdList = testSampleMatrixDTOS.stream().map(TestSampleMatrixDTO::getTestLineInstanceId).distinct().collect(Collectors.toList());
            TLInstanceDetailReq tlInstanceDetailReq = new TLInstanceDetailReq();
            tlInstanceDetailReq.setTestLineInstanceIds(testLineInstanceIdList);
            OrderTestLineListSearchReq testLineListSearchReq = new OrderTestLineListSearchReq();
            testLineListSearchReq.setTestLineInstanceIdList(testLineInstanceIdList);
            testLineListSearchReq.setOrderNoList(orderNoList);
            List<OrderTestLineListDTO> orderTestLineInfoList = orderTestLineService.getOrderTestLineInfoList(testLineListSearchReq);
            Map<String,OrderTestLineListDTO> testLineMap = new HashMap<>();
            if(Func.isNotEmpty(orderTestLineInfoList)){
                testLineMap =  orderTestLineInfoList.stream().collect(Collectors.toMap(OrderTestLineListDTO :: getTestLineInstanceId, o -> o,(key1 , key2)-> key2 ));
            }
            for (TrackingStockListRsp trackingStockListRsp : trackingStockListRspList) {
                TrackingStockListDTO trackingStockListDTO = new TrackingStockListDTO();
                if(matrixSampleMap.containsKey(trackingStockListRsp.getInstanceCode())){
                    TestSampleMatrixDTO testSampleMatrixDTO = matrixSampleMap.get(trackingStockListRsp.getInstanceCode());
                    trackingStockListDTO.setSampleNo(testSampleMatrixDTO.getSampleNo());
                    OrderTestLineListDTO tlInstanceDetailInfo = testLineMap.get(testSampleMatrixDTO.getTestLineInstanceId());
                    trackingStockListDTO.setTestItemName(tlInstanceDetailInfo.getTestItem());
                    trackingStockListDTO.setTestStandardName(tlInstanceDetailInfo.getTestStandard());
                    trackingStockListDTO.setEngineer(tlInstanceDetailInfo.getEngineer());
                }else{
                    trackingStockListDTO.setSampleNo(trackingStockListRsp.getInstanceCode().contains(" ")?trackingStockListRsp.getInstanceCode().split(" ")[1]:"");
                }
                trackingStockListDTO.setId(trackingStockListRsp.getId());
                trackingStockListDTO.setParentInstanceCode(trackingStockListRsp.getParentInstanceCode());
                trackingStockListDTO.setStatus(trackingStockListRsp.getStatus());
                trackingStockListDTO.setInstanceCode(trackingStockListRsp.getInstanceCode());
                trackingStockListDTO.setObjectType(trackingStockListRsp.getObjectType());
                trackingStockListDTO.setObjectId(trackingStockListRsp.getObjectId());
                trackingStockListDTO.setNeedReturnToCust(trackingStockListRsp.getNeedReturnToCust());
                trackingStockListDTO.setCreatedDate(trackingStockListRsp.getCreatedDate());
                trackingStockListDTO.setFeedBack(trackingStockListRsp.getFeedBack());
                trackingStockListDTO.setLocation(trackingStockListRsp.getLocation());
                trackingStockListDTOS.add(trackingStockListDTO);
            }
        }
        if(Func.isNotEmpty(trackingStockListDTOS)){
            trackingStockListDTOS.sort(Comparator.comparing(TrackingStockListDTO::getCreatedDate).reversed());
        }
        return BaseResponse.newSuccessInstance(trackingStockListDTOS);
    }

    public CustomResult hasSubSampleOrMixBySampleNo(SampleReq req){
        CustomResult rspResult = new CustomResult();
        rspResult.setSuccess(true);
        if(StringUtils.isBlank(req.getSampleId())){
            throw new BizException(ResponseCode.ILLEGAL_ARGUMENT, "params must be not empty!");
        }
        TestSampleInfoExample testSampleInfoExample1 = new TestSampleInfoExample();
        testSampleInfoExample1.createCriteria().andOrderNoEqualTo(req.getOrderNo()).andSampleNoEqualTo(req.getSampleId());
        List<TestSampleInfoPO> testSampleInfoPOS1 = testSampleInfoMapper.selectByExample(testSampleInfoExample1);
        if(Func.isEmpty(testSampleInfoPOS1)){
            rspResult.setMsg("Not found sample for test!");
            rspResult.setSuccess(true);
            return rspResult;
        }

        String sampleId=testSampleInfoPOS1.get(0).getID();

        TestSampleInfoExample testSampleInfoExample = new TestSampleInfoExample();
        testSampleInfoExample.createCriteria().andOrderNoEqualTo(req.getOrderNo()).andSampleParentIDEqualTo(sampleId);
        List<TestSampleInfoPO> testSampleInfoPOS = testSampleInfoMapper.selectByExample(testSampleInfoExample);
        if(testSampleInfoPOS!=null && testSampleInfoPOS.size()>0&&testSampleInfoPOS.stream().anyMatch(e->e.getActiveIndicator())){
            rspResult.setMsg("The sample has sub-samples!");
            rspResult.setSuccess(false);
        }else{
            TestSampleGroupInfoExample testSampleGroupInfoExample = new TestSampleGroupInfoExample();
            testSampleGroupInfoExample.createCriteria().andSampleGroupIDEqualTo(sampleId);
            List<TestSampleGroupInfoPO> testSampleGroupInfoPOS = testSampleGroupInfoMapper.selectByExample(testSampleGroupInfoExample);
            if(testSampleGroupInfoPOS!=null && testSampleGroupInfoPOS.size()>0&&testSampleGroupInfoPOS.stream().anyMatch(e->e.getActiveIndicator())){
                rspResult.setMsg("The sample has been done Mix!");
                rspResult.setSuccess(false);
            }
        }
        rspResult.setData(sampleId);
        return rspResult;
    }

}