package com.sgs.otsnotes.domain.service;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sgs.framework.core.base.CustomResult;
import com.sgs.framework.model.enums.LanguageType;
import com.sgs.framework.tool.utils.Func;
import com.sgs.otsnotes.core.util.NumberUtil;
import com.sgs.otsnotes.dbstorages.mybatis.extmapper.OrderMapper;
import com.sgs.otsnotes.dbstorages.mybatis.extmapper.PPTestLineRelMapper;
import com.sgs.otsnotes.dbstorages.mybatis.extmapper.SubContractExtMapper;
import com.sgs.otsnotes.dbstorages.mybatis.extmapper.TestLineMapper;
import com.sgs.otsnotes.dbstorages.mybatis.model.GeneralOrderInstanceInfoPO;
import com.sgs.otsnotes.dbstorages.mybatis.model.TestLineInstancePO;
import com.sgs.otsnotes.facade.model.common.BaseAopTestLineIdRequest;
import com.sgs.otsnotes.facade.model.common.OtsNotesRequest;
import com.sgs.otsnotes.facade.model.dto.subcontract.SubContractOrderIdDTO;
import com.sgs.otsnotes.facade.model.dto.subcontract.SubContractOrderInfoDTO;
import com.sgs.otsnotes.facade.model.dto.subcontract.SubContractOrderTLInfoDTO;
import com.sgs.otsnotes.facade.model.dto.subcontract.SubOrderTestLineInfoDTO;
import com.sgs.otsnotes.facade.model.enums.SubContractOperationTypeEnums;
import com.sgs.otsnotes.facade.model.enums.SubOperateTypeEnums;
import com.sgs.otsnotes.facade.model.info.testline.PpTestLineRelCitationInfo;
import com.sgs.otsnotes.facade.model.req.sample.SaveAssignSampleReq;
import com.sgs.otsnotes.facade.model.req.testLine.*;
import com.sgs.otsnotes.integration.OrderClient;
import com.sgs.otsnotes.integration.trimslocal.CitationClient;
import com.sgs.preorder.facade.model.dto.order.OrderInfoDto;
import com.sgs.preorder.facade.model.enums.OperationType;
import com.sgs.trimslocal.facade.model.testline.rsp.GetCitationBaseInfoRsp;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service
public class SubContractOperateService {
    private static final Logger logger = LoggerFactory.getLogger(SubContractOperateService.class);

    @Autowired
    private OrderMapper orderMapper;
    @Autowired
    private TestLineMapper testLineMapper;
    @Autowired
    private SubContractExtMapper subContractExtMapper;
    @Autowired
    private PPTestLineRelMapper ppTestLineRelMapper;
    @Autowired
    private OrderClient orderClient;
    @Autowired
    private TestLineCmdService testLineCmdService;
    @Autowired
    private CitationClient citationClient;


    /**
     * 根据 入参 查询 操作是否可以
     *
     * @param reqObject
     * @param contractType
     * @return
     */
    public CustomResult checkSubContractReq(OtsNotesRequest reqObject, SubContractOperationTypeEnums contractType) {
        CustomResult rspResult = new CustomResult();

        // TODO   所有的 testLIneInstanceid
        List<String> paramsList = Lists.newArrayList();

        switch (contractType) {
            case GetConditionList:
            case UpdateCondition:
            case CopyTestLine:
            case CancelTestLine:
            case CancelAssignSample:
            case UpdateTLStatus:
            case NCTestLine:
                if (reqObject instanceof BaseAopTestLineIdRequest) {
                    BaseAopTestLineIdRequest subContract = (BaseAopTestLineIdRequest) reqObject;
                    paramsList.add(subContract.getTestLineInstanceId());
                }
                break;
            case UpdateStandard:
                if (reqObject instanceof SaveTestStandardReq) {
                    SaveTestStandardReq saveStandardReq = (SaveTestStandardReq) reqObject;
                    if (saveStandardReq != null && CollectionUtils.isNotEmpty(saveStandardReq.getStandards())) {
                        paramsList.addAll(saveStandardReq.getStandards().stream().map(StandardReq::getTestLineInstanceId).distinct().collect(Collectors.toList()));
                    }
                }
                break;
            case SaveAssignSample:
                if (reqObject instanceof SaveAssignSampleReq) {
                    SaveAssignSampleReq assignSampleReq = (SaveAssignSampleReq) reqObject;
                    if (assignSampleReq != null && CollectionUtils.isNotEmpty(assignSampleReq.getPpTestLineRelIds())) {
                        List<String> testLineIds = testLineMapper.getTestLineIdsByPpTestLineRelIds(assignSampleReq.getPpTestLineRelIds());
                        if (CollectionUtils.isNotEmpty(testLineIds)) {
                            paramsList.addAll(testLineIds);
                        }
                    }
                }
                break;
            case DelTestLine:
                if (reqObject instanceof DelTestLineReq) {
                    DelTestLineReq delReq = (DelTestLineReq) reqObject;
                    if (delReq != null && CollectionUtils.isNotEmpty(delReq.getPpTestLineRelIds())) {
                        List<String> testLineIds = testLineMapper.getTestLineIdsByPpTestLineRelIds(delReq.getPpTestLineRelIds());
                        if (CollectionUtils.isNotEmpty(testLineIds)) {
                            paramsList.addAll(testLineIds);
                        }
                    }
                }
                break;
        }
        if (paramsList.isEmpty()) {
            return rspResult.fail("Params miss");
        }

        // 根据TestLineInstanceId 获取 orderId
        TestLineInstancePO testLineInfoById = testLineMapper.getTestLineById(paramsList.get(0));
        if (testLineInfoById == null || StringUtils.isBlank(testLineInfoById.getGeneralOrderInstanceID())) {
            return rspResult.fail("Params miss");

        }
        String orderId = testLineInfoById.getGeneralOrderInstanceID();
        if (StringUtils.isBlank(orderId)) {
            return rspResult.fail("Params miss");
        }
        GeneralOrderInstanceInfoPO orderInfoByOrderId = orderMapper.getOrderInfoByOrderId(orderId);
        String orderNo = orderInfoByOrderId.getOrderNo();
        OrderInfoDto orderInfoByOrderNo = orderClient.getOrderInfoByOrderNo(orderNo);
        if (orderInfoByOrderNo == null) {
            return rspResult.fail("Params miss");
        }
        if (OperationType.check(orderInfoByOrderNo.getOperationType(), OperationType.TranslationReport)) {
            // TranslationReport订单 不能做上述操作
            return rspResult.fail(String.format("订单【%s】是TranslationReport，不能%s", orderNo, contractType.getMessage()));
        }
//        // 不需要判断 NULL  NULL的情况为正常订单
//        OperationType operationType = OperationType.findCode(orderInfoByOrderNo.getOperationType());

        return this.checkOrderType(orderNo, contractType, paramsList);

    }

    /**
     * 判断订单是下面的那个类型什么类型
     * 新版内部分包主单
     * 内部分包主单
     * 新版内部分包单子单
     * 内部分包单子单
     *
     * @param orderNo
     * @param subContractOperationTypeEnums
     * @param paramsList
     * @return
     */
    private CustomResult checkOrderType(String orderNo, SubContractOperationTypeEnums subContractOperationTypeEnums,
                                        List<String> paramsList) {
        CustomResult rspResult = new CustomResult();
        List<SubContractOrderTLInfoDTO> orderTLInfo = Lists.newArrayList();
        // tre_order_subcontract_relationship_mapping 中取出 tlIds 为主关系（originalRelId） && 子关系 所处的订单（subRelId）不是内部分包单  此时 此Tl 不能操作
        List<SubContractOrderTLInfoDTO> subContractTLInfo = subContractExtMapper.getSubContractOriginalOrderNo(paramsList);
        // 记录为空 表示操作的订单不是内部分包单和新版内部分包单的主单
        // 继续查询是否为 内部分包单和新版内部分包单
        if (CollectionUtils.isEmpty(subContractTLInfo)) {
            List<SubContractOrderTLInfoDTO> mainContractTLInfo = subContractExtMapper.getSubContractSubOrderNo(paramsList);
            if (CollectionUtils.isEmpty(mainContractTLInfo)) {
                // 此订单 不是上述4种订单 不做判断 直接返回
                rspResult.setSuccess(true);
                rspResult.setMsg(String.format("校验订单%s，操作成功 ", orderNo));
                return rspResult;
            }
            for (SubContractOrderTLInfoDTO mainTl : mainContractTLInfo) {
//                if (paramsList.contains(mainTl.getTestLineInstanceId())) {
                    OrderInfoDto preOrderInfo = orderClient.getOrderInfoByOrderNo(mainTl.getMainOrderNo());
                    if (preOrderInfo == null) {
                        return rspResult.fail("Params miss");
                    }
                    OperationType operation = OperationType.findCode(preOrderInfo.getOperationType());
                    if (operation == OperationType.SubContract) {
                        continue;
                    }
                    SubContractOrderTLInfoDTO orderTL = new SubContractOrderTLInfoDTO();
                    orderTL.setMainOrderNo(mainTl.getOrderNo());
                    orderTL.setTestLineId(mainTl.getTestLineId());
                    orderTLInfo.add(orderTL);
//                }
            }
            if (CollectionUtils.isNotEmpty(orderTLInfo)) {
                // 此订单为 新版内部分包单子单
                return this.checkOrderAndMsg(orderNo, orderTLInfo, subContractOperationTypeEnums.getOperateType(), SubOperateTypeEnums.CanOperateNewSubContract);
            } else {
                // 此订单为 内部分包单子单
                return this.checkOrderAndMsg(orderNo, orderTLInfo, subContractOperationTypeEnums.getOperateType(), SubOperateTypeEnums.CanOperateSubContract);
            }

        }

        // 记录不为空 表示 当前订单是 新版内部分包主单或者 内部分包主单
        for (SubContractOrderTLInfoDTO infoDTO : subContractTLInfo) {
            if (paramsList.contains(infoDTO.getTestLineInstanceId())) {
                OrderInfoDto preOrderInfo = orderClient.getOrderInfoByOrderNo(infoDTO.getSubOrderNo());
                if (preOrderInfo == null) {
                    return rspResult.fail("Params miss");
                }
                OperationType operation = OperationType.findCode(preOrderInfo.getOperationType());
                if (operation == OperationType.SubContract) {
                    continue;
                }
                SubContractOrderTLInfoDTO orderTL = new SubContractOrderTLInfoDTO();
                orderTL.setOrderNo(infoDTO.getSubOrderNo());
                orderTL.setTestLineId(infoDTO.getTestLineId());
                orderTLInfo.add(orderTL);
            }
        }
        if (CollectionUtils.isNotEmpty(orderTLInfo)) {
            // 此订单为 新版内部分包主单
            return this.checkOrderAndMsg(orderNo, orderTLInfo, subContractOperationTypeEnums.getOperateType(), SubOperateTypeEnums.CanOperateMainNewContract);
        } else {
            // 此订单为 内部分包主单
            return this.checkOrderAndMsg(orderNo, orderTLInfo, subContractOperationTypeEnums.getOperateType(), SubOperateTypeEnums.CanOperateMainContract);
        }

    }

    private CustomResult checkOrderAndMsg(String orderNo, List<SubContractOrderTLInfoDTO> orderTLInfo, Integer operateType, SubOperateTypeEnums subOperateTypeEnums) {
        CustomResult rspResult = new CustomResult();
        if (!SubOperateTypeEnums.checkOperate(operateType, subOperateTypeEnums)) {
            StringBuilder sb = new StringBuilder();
            orderTLInfo.forEach(tl -> {
                sb.append(String.format("Tl: %s已经分给订单%s，不能操作。", tl.getTestLineId(), tl.getOrderNo()));
            });
            rspResult.setSuccess(false);
            rspResult.setMsg(String.format("此订单为新版内部分包，不能此操作，原因：%s", sb.toString()));
            return rspResult;
        }
        rspResult.setSuccess(true);
        rspResult.setMsg(String.format("校验订单%s，操作成功 ", orderNo));
        return rspResult;
    }


    /**
     * DIG-7191
     * 单独处理SaveTestLine的逻辑
     * 需要汇总当前Order关联的所有主子单（所有层级）进行TL重复添加的校验，跨订单的情况下即使TL Completed了也不能重复添加
     * 如果有重复的TL，提示TL XXX 在Order XXXXXXX已经存在，不能重复添加
     * 提示语句“Fail to add! TL XXXX already existed in Order XXXXXX.”
     *
     * @param orderId
     * @param testLines
     * @return
     */
    public CustomResult checkAddTestLineForContract(String orderId, List<SaveTestLineReq> testLines, SubContractOrderInfoDTO contractOrderInfo,boolean isMergeTl) {
        CustomResult rspResult = new CustomResult();
        //
        GeneralOrderInstanceInfoPO orderInfo = orderMapper.getOrderInfoByOrderId(orderId);
        if (orderInfo == null) {
            return rspResult.fail("PLEASE CHECK PARAMETER");
        }
        String orderNo = orderInfo.getOrderNo();
        OrderInfoDto preOrderInfo = orderClient.getOrderInfoByOrderNo(orderNo);
        if (OperationType.check(preOrderInfo.getOperationType(), OperationType.TranslationReport)) {
            // TranslationReport订单 不能做上述操作
            return rspResult.fail(String.format("订单【%s】是TranslationReport，不能此操作", orderNo));
        }
        // 内部分包单子单 不能 Add TL
        if (OperationType.check(preOrderInfo.getOperationType(), OperationType.SubContract)) {
            return rspResult.fail("此订单为内部分包单, 不能Add TL/PP TL");
        }
        // 如果当前单是新版内部分包单子单 取它的父单信息
        if (OperationType.check(preOrderInfo.getOperationType(), OperationType.NewSubContract)) {
            SubContractOrderInfoDTO contractOrderInfoBySubOrderId = subContractExtMapper.getContractOrderInfoBySubOrderId(orderId);
            if (contractOrderInfo == null) {
                return rspResult.fail(String.format("此订单【%s】为新版内部分包单, 请检查关系表【tre_order_subcontract_relationship_mapping】", orderNo) );
            }
            BeanUtils.copyProperties(contractOrderInfoBySubOrderId, contractOrderInfo);
        }

        // 在 tre_order_subcontract_relationship_mapping中查找出所有关联的所有层级的TestLine
        // 1.查询此OrderId所有层级的OrderId
        List<String> allOrderId = this.findSubRelationOrderId(Lists.newArrayList(orderId));
        if (CollectionUtils.isEmpty(allOrderId) || CollectionUtils.isEmpty(testLines)) {
            return rspResult.fail("PLEASE CHECK PARAMETER");
        }

        // 2.check 添加的TestLine 是否已经在相关的订单中添加过
        return this.checkAddTlInfo(allOrderId, testLines,isMergeTl);
    }


    /**
     * 递归查出所有层级的orderId
     *
     * @param orderIds
     * @return
     */
    private List<String> findSubRelationOrderId(List<String> orderIds) {
        if (CollectionUtils.isEmpty(orderIds)) {
            return Lists.newArrayList();
        }
        List<SubContractOrderIdDTO> subRelationDtos = subContractExtMapper.getAllSubRelationOrderId(orderIds);
        if (CollectionUtils.isEmpty(subRelationDtos)) {
            return orderIds;
        }
        List<String> relOrderIds = Lists.newArrayList();
        subRelationDtos.forEach(dto -> {
            if (!orderIds.contains(dto.getSubOrderId()) && !relOrderIds.contains(dto.getSubOrderId())) {
                relOrderIds.add(dto.getSubOrderId());
            }
            if (!orderIds.contains(dto.getOriginalOrderId()) && !relOrderIds.contains(dto.getOriginalOrderId())) {
                relOrderIds.add(dto.getOriginalOrderId());
            }
        });
        relOrderIds.removeIf(id -> {
            return orderIds.contains(id);
        });
        orderIds.addAll(relOrderIds);
        if (CollectionUtils.isEmpty(relOrderIds)) {
            return orderIds;
        }
        return this.findSubRelationOrderId(orderIds);
    }

    /**
     * 添加的TestLine 是否已经在相关的订单中添加过
     *
     * @param allOrderId
     * @param testLines
     * @return
     */
    private CustomResult checkAddTlInfo(List<String> allOrderId, List<SaveTestLineReq> testLines,boolean isMergeTl) {
        CustomResult rspResult = new CustomResult(false);
        // 获取订单现有的pp citation 等信息
        Map<String, PpTestLineRelCitationInfo> ppTlCitationMap = Maps.newHashMap();
        List<PpTestLineRelCitationInfo> ppTLRelCitationInfoByOrderId = ppTestLineRelMapper.getPPTestLineRelCitationInfoByOrderId(allOrderId);
        List<GetCitationBaseInfoRsp> trimsCitationBaseInfoList = new ArrayList<>();
        if(Func.isNotEmpty(ppTLRelCitationInfoByOrderId)){
            Set<Long> citationBaseIdSet = ppTLRelCitationInfoByOrderId.stream().map(PpTestLineRelCitationInfo::getCitationBaseId).distinct().filter(Func::isNotEmpty).collect(Collectors.toSet());
            if(Func.isNotEmpty(citationBaseIdSet)){
                trimsCitationBaseInfoList = citationClient.getCitationBaseInfo(citationBaseIdSet, null, LanguageType.English);
            }
        }
        List<GetCitationBaseInfoRsp> finalTrimsCitationBaseInfoList = trimsCitationBaseInfoList;
        ppTLRelCitationInfoByOrderId.forEach(ppTl -> {
            if(Func.isNotEmpty(finalTrimsCitationBaseInfoList)){
                GetCitationBaseInfoRsp citationBaseInfoRsp = finalTrimsCitationBaseInfoList.stream().filter(i -> Func.equals(Func.toStr(ppTl.getCitationBaseId()), Func.toStr(i.getCitationBaseId()))).findFirst().orElse(null);
                if(Func.isNotEmpty(citationBaseInfoRsp)){
                    GetSaveTestLineKeyReq getSaveTestLineKeyReq =  new GetSaveTestLineKeyReq();
                    getSaveTestLineKeyReq.setTestLineVersionId(ppTl.getTestLineVersionId());
                    getSaveTestLineKeyReq.setCitationVersionId(ppTl.getCitationVersionId());
                    getSaveTestLineKeyReq.setCitationSectionId(citationBaseInfoRsp.getCitationSectionId());
                    getSaveTestLineKeyReq.setCitationType(citationBaseInfoRsp.getCitationType());
                    getSaveTestLineKeyReq.setAid(ppTl.getPpArtifactRelId());
                    getSaveTestLineKeyReq.setTestLineType(ppTl.getTestLineType());
                    getSaveTestLineKeyReq.setMergePpTl(isMergeTl);
                    String key = testLineCmdService.getSaveTestLineKey(getSaveTestLineKeyReq);
                    if (ppTlCitationMap.containsKey(key)) {
                        return;
                    }
                    ppTlCitationMap.put(key, ppTl);
                }
            }
//            String key = String.format("%s_%s_%s", ppTl.getTestLineID(), ppTl.getCitationBaseId(), ppTl.getPpArtifactRelId());
        });

        // 判断添加的 TestLine
        List<SubOrderTestLineInfoDTO> lstPPTestLine = Lists.newArrayList();
        for (SaveTestLineReq saveTL : testLines) {
//            Boolean isAddPPTL = NumberUtil.toLong(saveTL.getPpVersionId()) > 0 ? true :false;
            SaveTestLineCitationReq saveTestLineCitationReq = saveTL.getSaveTestLineCitationReq();
            Boolean isAddPPTL = (NumberUtil.toLong(saveTL.getPpVersionId()) > 0 || Func.isNotEmpty(saveTL.getAid()))? true :false;
            // testLineStandards 是指定 CitationBaseId搜索的，理论上citationBaseStandarInfos 中只有一条CitationBaseid
            if(Func.isNotEmpty(saveTestLineCitationReq) || Func.isNotEmpty(saveTL.getCitationBaseId())){
                Integer citationSectionId = null;
                Integer citationType = null;
                if(Func.isNotEmpty(saveTestLineCitationReq)){
                    citationSectionId = saveTestLineCitationReq.getCitationSectionId();
                    citationType = saveTestLineCitationReq.getCitationType();
                }else if(Func.isNotEmpty(finalTrimsCitationBaseInfoList)){
                    GetCitationBaseInfoRsp citationBaseInfoRsp = finalTrimsCitationBaseInfoList.stream().filter(i -> Func.equals(Func.toStr(saveTL.getCitationBaseId()), Func.toStr(i.getCitationBaseId()))).findFirst().orElse(null);
                    if(Func.isNotEmpty(citationBaseInfoRsp)) {
                        citationSectionId = citationBaseInfoRsp.getCitationSectionId();
                        citationType = citationBaseInfoRsp.getCitationType();
                    }
                }

                GetSaveTestLineKeyReq getSaveTestLineKeyReq =  new GetSaveTestLineKeyReq();
                getSaveTestLineKeyReq.setTestLineVersionId(saveTL.getTestLineVersionId());
                getSaveTestLineKeyReq.setCitationVersionId(saveTL.getCitationVersionId());
                getSaveTestLineKeyReq.setCitationSectionId(citationSectionId);
                getSaveTestLineKeyReq.setCitationType(citationType);
                getSaveTestLineKeyReq.setAid(!isAddPPTL ? 0 : saveTL.getArtifactBaseId());
                getSaveTestLineKeyReq.setTestLineType(saveTL.getTestLineType());
                getSaveTestLineKeyReq.setMergePpTl(isMergeTl);
                String addKey = testLineCmdService.getSaveTestLineKey(getSaveTestLineKeyReq);
                if (!ppTlCitationMap.containsKey(addKey)) {
                    continue;
                }
                PpTestLineRelCitationInfo ppTestLineRelCitationInfo = ppTlCitationMap.get(addKey);

                SubOrderTestLineInfoDTO subOrderTestLineInfoDTO = new SubOrderTestLineInfoDTO();
                subOrderTestLineInfoDTO.setOrderNo(ppTestLineRelCitationInfo.getOrderNo());
                subOrderTestLineInfoDTO.setTestLineId(saveTL.getTestLineId());
                subOrderTestLineInfoDTO.setStandardName(saveTL.getStandardName());
                lstPPTestLine.add(subOrderTestLineInfoDTO);
            }else{
                continue;
            }
        }
         /*   String addKey = String.format("%s_%s_%s", saveTL.getTestLineId(),
                    saveTL.getCitationBaseId(),
                    !isAddPPTL ? 0 : saveTL.getArtifactBaseId());*/
        if (CollectionUtils.isNotEmpty(lstPPTestLine)) {
            String msgTelLine = "";
            String msgStand = "";
            Set<String> msgOrderNo = new HashSet<>();
            for (SubOrderTestLineInfoDTO item : lstPPTestLine) {
                msgOrderNo.add(item.getOrderNo());
                msgTelLine = msgTelLine.concat(String.format(" [%s] ", item.getTestLineId().toString()));
                msgStand = msgStand.concat(String.format(" [%s] ", item.getStandardName()));
            }
            rspResult.setMsg(String.format("TL ID: %s Standard: %s is exists in orderNo: %s and can not be added .", msgTelLine, msgStand, StringUtils.join(msgOrderNo,",")));
            return rspResult;
        }
        rspResult.setSuccess(true);
        return rspResult;
    }


}
