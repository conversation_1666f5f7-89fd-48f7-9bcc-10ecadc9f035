/**
 * MergeTestReportBinaryResponse.java
 *
 * This file was auto-generated from WSDL
 * by the Apache Axis 1.4 Apr 22, 2006 (06:55:48 PDT) WSDL2Java emitter.
 */

package com.sgs.otsnotes.domain.service.webservice.ots;

public class MergeTestReportBinaryResponse  implements java.io.Serializable {
    private byte[] mergeTestReportBinaryResult;

    private String errMsg;

    public MergeTestReportBinaryResponse() {
    }

    public MergeTestReportBinaryResponse(
           byte[] mergeTestReportBinaryResult,
           String errMsg) {
           this.mergeTestReportBinaryResult = mergeTestReportBinaryResult;
           this.errMsg = errMsg;
    }


    /**
     * Gets the mergeTestReportBinaryResult value for this MergeTestReportBinaryResponse.
     * 
     * @return mergeTestReportBinaryResult
     */
    public byte[] getMergeTestReportBinaryResult() {
        return mergeTestReportBinaryResult;
    }


    /**
     * Sets the mergeTestReportBinaryResult value for this MergeTestReportBinaryResponse.
     * 
     * @param mergeTestReportBinaryResult
     */
    public void setMergeTestReportBinaryResult(byte[] mergeTestReportBinaryResult) {
        this.mergeTestReportBinaryResult = mergeTestReportBinaryResult;
    }


    /**
     * Gets the errMsg value for this MergeTestReportBinaryResponse.
     * 
     * @return errMsg
     */
    public String getErrMsg() {
        return errMsg;
    }


    /**
     * Sets the errMsg value for this MergeTestReportBinaryResponse.
     * 
     * @param errMsg
     */
    public void setErrMsg(String errMsg) {
        this.errMsg = errMsg;
    }

    private Object __equalsCalc = null;
    public synchronized boolean equals(Object obj) {
        if (!(obj instanceof MergeTestReportBinaryResponse)) return false;
        MergeTestReportBinaryResponse other = (MergeTestReportBinaryResponse) obj;
        if (obj == null) return false;
        if (this == obj) return true;
        if (__equalsCalc != null) {
            return (__equalsCalc == obj);
        }
        __equalsCalc = obj;
        boolean _equals;
        _equals = true && 
            ((this.mergeTestReportBinaryResult==null && other.getMergeTestReportBinaryResult()==null) || 
             (this.mergeTestReportBinaryResult!=null &&
              java.util.Arrays.equals(this.mergeTestReportBinaryResult, other.getMergeTestReportBinaryResult()))) &&
            ((this.errMsg==null && other.getErrMsg()==null) || 
             (this.errMsg!=null &&
              this.errMsg.equals(other.getErrMsg())));
        __equalsCalc = null;
        return _equals;
    }

    private boolean __hashCodeCalc = false;
    public synchronized int hashCode() {
        if (__hashCodeCalc) {
            return 0;
        }
        __hashCodeCalc = true;
        int _hashCode = 1;
        if (getMergeTestReportBinaryResult() != null) {
            for (int i=0;
                 i<java.lang.reflect.Array.getLength(getMergeTestReportBinaryResult());
                 i++) {
                Object obj = java.lang.reflect.Array.get(getMergeTestReportBinaryResult(), i);
                if (obj != null &&
                    !obj.getClass().isArray()) {
                    _hashCode += obj.hashCode();
                }
            }
        }
        if (getErrMsg() != null) {
            _hashCode += getErrMsg().hashCode();
        }
        __hashCodeCalc = false;
        return _hashCode;
    }

    // Type metadata
    private static org.apache.axis.description.TypeDesc typeDesc =
        new org.apache.axis.description.TypeDesc(MergeTestReportBinaryResponse.class, true);

    static {
        typeDesc.setXmlType(new javax.xml.namespace.QName("http://ots.sgs.com/", ">MergeTestReportBinaryResponse"));
        org.apache.axis.description.ElementDesc elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("mergeTestReportBinaryResult");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "MergeTestReportBinaryResult"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "base64Binary"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("errMsg");
        elemField.setXmlName(new javax.xml.namespace.QName("http://ots.sgs.com/", "errMsg"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
    }

    /**
     * Return type metadata object
     */
    public static org.apache.axis.description.TypeDesc getTypeDesc() {
        return typeDesc;
    }

    /**
     * Get Custom Serializer
     */
    public static org.apache.axis.encoding.Serializer getSerializer(
           String mechType,
           Class _javaType,
           javax.xml.namespace.QName _xmlType) {
        return 
          new  org.apache.axis.encoding.ser.BeanSerializer(
            _javaType, _xmlType, typeDesc);
    }

    /**
     * Get Custom Deserializer
     */
    public static org.apache.axis.encoding.Deserializer getDeserializer(
           String mechType,
           Class _javaType,
           javax.xml.namespace.QName _xmlType) {
        return 
          new  org.apache.axis.encoding.ser.BeanDeserializer(
            _javaType, _xmlType, typeDesc);
    }

}
