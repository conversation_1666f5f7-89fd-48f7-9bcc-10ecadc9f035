package com.sgs.otsnotes.domain.service.lims.report.impl;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.fastjson.JSON;
import com.beust.jcommander.internal.Lists;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.core.base.ResponseCode;
import com.sgs.framework.i18n.util.MessageUtil;
import com.sgs.framework.model.enums.ReportLanguage;
import com.sgs.framework.tool.utils.CollectionUtil;
import com.sgs.framework.tool.utils.Func;
import com.sgs.framework.tool.utils.ProductLineContextHolder;
import com.sgs.gpo.facade.temp.OrderTempFacade;
import com.sgs.otsnotes.core.enums.ReportFlagEnums;
import com.sgs.otsnotes.dbstorages.mybatis.extmapper.SubReportExtMapper;
import com.sgs.otsnotes.dbstorages.mybatis.mapper.ReportFileMapper;
import com.sgs.otsnotes.dbstorages.mybatis.mapper.ReportInfoMapper;
import com.sgs.otsnotes.dbstorages.mybatis.mapper.SubContractMapper;
import com.sgs.otsnotes.dbstorages.mybatis.model.*;
import com.sgs.otsnotes.domain.service.gpn.report.IGpnReportService;
import com.sgs.otsnotes.domain.service.lims.report.IReportService;
import com.sgs.otsnotes.facade.model.dto.GpnSubReportDTO;
import com.sgs.otsnotes.facade.model.enums.ReportFileType;
import com.sgs.otsnotes.facade.model.enums.ReportRequirementEnum;
import com.sgs.otsnotes.facade.model.enums.ReportStatus;
import com.sgs.otsnotes.facade.model.enums.SubContractStatusEnum;
import com.sgs.otsnotes.facade.model.req.lims.report.SendDraftCheckReq;
import com.sgs.otsnotes.facade.model.req.lims.report.SendFinalCheckReq;
import com.sgs.otsnotes.facade.model.rsp.conclusion.CheckReportConclusionRsp;
import com.sgs.otsnotes.integration.CustomerClient;
import com.sgs.otsnotes.integration.OrderClient;
import com.sgs.preorder.facade.OrderFacade;
import com.sgs.preorder.facade.model.dto.order.OrderAllDTO;
import com.sgs.preorder.facade.model.enums.CaseType;
import com.sgs.preorder.facade.model.enums.DraftReportRequiredEnum;
import com.sgs.preorder.facade.model.enums.OrderStatus;
import com.sgs.preorder.facade.model.info.TestRequestInfo;
import com.sgs.preorder.facade.model.info.externalOrder.GpoExternalOrderInfo;
import com.sgs.preorder.facade.model.req.OrderIdReq;
import com.sgs.preorder.facade.model.req.OrderNosReq;
import com.sgs.preorder.facade.model.req.externalOrder.GpoExternalOrderNoReq;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Service
@AllArgsConstructor
@Slf4j
public class ReportServiceImpl implements IReportService {
    private MessageUtil messageUtil;
    private ReportInfoMapper reportInfoMapper;
    private OrderFacade gpoOrderFacade;
    private ReportFileMapper reportFileMapper;
    private IGpnReportService gpnReportService;
    private SubContractMapper subContractMapper;
    private SubReportExtMapper subReportExtMapper;
    private OrderClient orderClient;
    private OrderFacade orderFacade;
    private CustomerClient customerClient;
    private OrderTempFacade orderTempFacade;

    @Override
    public BaseResponse sendDraftCheck(SendDraftCheckReq sendDraftCheckReq) {
        //返回值
        BaseResponse response = new BaseResponse();
        response.setStatus(ResponseCode.SUCCESS.getCode());
        //参数校验
        if (Func.isEmpty(sendDraftCheckReq) || Func.isEmpty(sendDraftCheckReq.getReportIdList())) {
            response.setStatus(ResponseCode.FAIL.getCode());
            response.setMessage(messageUtil.get("param.miss.report.reportId"));
            return response;
        }
        if(Func.isEmpty(sendDraftCheckReq.getOrderNoList())){
            response.setStatus(ResponseCode.FAIL.getCode());
            response.setMessage(messageUtil.get("param.miss.order.orderNo"));
            return response;
        }
        List<String> reportIdList = sendDraftCheckReq.getReportIdList();
        List<String> orderNoList = sendDraftCheckReq.getOrderNoList();
        //查询报告信息
        ReportInfoExample example = new ReportInfoExample();
        example.createCriteria().andIDIn(reportIdList);
        List<ReportInfoPO> reportInfoPOS = reportInfoMapper.selectByExample(example);
        if (Func.isEmpty(reportInfoPOS)) {
            response.setStatus(ResponseCode.FAIL.getCode());
            response.setMessage(messageUtil.get("report.result.empty"));
            return response;
        }
        List<String> trueOrderNoList = reportInfoPOS.stream().map(ReportInfoPO::getOrderNo).collect(Collectors.toList());
        //查询订单信息
        OrderNosReq orderNosReq = new OrderNosReq();
        orderNosReq.setOrderNos(orderNoList);
        BaseResponse<List<OrderAllDTO>> checkOrderListBaseResponse = gpoOrderFacade.getOrderAllListByOrderNos(orderNosReq);
        if (Func.isEmpty(checkOrderListBaseResponse) || Func.isEmpty(checkOrderListBaseResponse.getData())) {
            response.setStatus(ResponseCode.FAIL.getCode());
            response.setMessage(messageUtil.get("order.result.empty"));
            return response;
        }
        log.info("sendDraftCheck orderInfo:{}", JSON.toJSONString(checkOrderListBaseResponse));

        //查询trueOrderNo订单信息
        orderNosReq = new OrderNosReq();
        orderNosReq.setOrderNos(trueOrderNoList);
        BaseResponse<List<OrderAllDTO>> orderListBaseResponse = gpoOrderFacade.getOrderAllListByOrderNos(orderNosReq);
        if (Func.isEmpty(orderListBaseResponse) || Func.isEmpty(orderListBaseResponse.getData())) {
            response.setStatus(ResponseCode.FAIL.getCode());
            response.setMessage(messageUtil.get("order.result.empty"));
            return response;
        }
        List<String> allOrderNoList = new ArrayList<>();
        allOrderNoList.addAll(trueOrderNoList);

        List<OrderAllDTO> checkOrderAllDTOS = checkOrderListBaseResponse.getData();
        OrderIdReq orderIdReq = new OrderIdReq();
        orderIdReq.setOrderNos(trueOrderNoList);
        List<TestRequestInfo> testRequestInfoList = gpoOrderFacade.batchQueryTestRequestForPe(orderIdReq).getData();

        //校验报告状态 是否是 Approved/HosReviewed
        List<ReportInfoPO> reportForStatusCheck = reportInfoPOS.stream().
                filter(reportInfoPO -> !ReportStatus.check(reportInfoPO.getReportStatus(), ReportStatus.Approved, ReportStatus.HostReviewed)).collect(Collectors.toList());
        if (Func.isNotEmpty(reportForStatusCheck)) {
            List<String> reportNos = reportForStatusCheck.stream().map(ReportInfoPO::getReportNo).collect(Collectors.toList());
            response.setStatus(ResponseCode.FAIL.getCode());
            response.setMessage(messageUtil.get("report.status.not", new Object[]{StringUtils.join(reportNos, ", "), "Approved/HostReviewed"}));
            return response;
        }
        //校验支付状态
        for (OrderAllDTO checkOrderAllDTO : checkOrderAllDTOS) {
            log.info("{},deliverPermission checkIsPaymented result:{}",checkOrderAllDTO.getOrderNo(),Func.toStr(checkOrderAllDTO.getEnableDelivery()));
        }
        List<OrderAllDTO> orderAllDTOUnPaid = checkOrderAllDTOS.stream().
                filter(orderAllDTO -> !Func.equals(CaseType.IDB.getStatus(), orderAllDTO.getCaseType()) && !Func.equals(CaseType.IDN.getStatus(), orderAllDTO.getCaseType()) && !Func.equals(CaseType.IDNTJ.getStatus(), orderAllDTO.getCaseType())
                        && (Func.isEmpty(orderAllDTO.getEnableDelivery()) || Func.equals(0, orderAllDTO.getEnableDelivery()))).collect(Collectors.toList());
        if (Func.isNotEmpty(orderAllDTOUnPaid)) {
            List<String> orderNos = orderAllDTOUnPaid.stream().map(OrderAllDTO::getOrderNo).collect(Collectors.toList());
            response.setStatus(ResponseCode.FAIL.getCode());
            response.setMessage(messageUtil.get("order.enableDelivery.no", new Object[]{StringUtils.join(orderNos, ", ")}));
            return response;
        }

        List<OrderAllDTO> orderAllDTOPending = checkOrderAllDTOS.stream().
                filter(orderAllDTO -> Func.equals(com.sgs.preorder.facade.model.enums.OrderStatus.Pending.getStatus(), orderAllDTO.getOrderStatus())).collect(Collectors.toList());
        if (Func.isNotEmpty(orderAllDTOPending)) {
            List<String> orderNos = orderAllDTOPending.stream().map(OrderAllDTO::getOrderNo).distinct().collect(Collectors.toList());
            response.setStatus(ResponseCode.FAIL.getCode());
            response.setMessage(messageUtil.get("order.status.is", new Object[]{StringUtils.join(orderNos, ", "), "Pending"}));
            return response;
        }
        //校验订单为need draft
        List<OrderAllDTO> orderAllDTONotNeedDraft = checkOrderAllDTOS.stream().
                filter(orderAllDTO -> Func.equals(DraftReportRequiredEnum.NO.getStatus(), orderAllDTO.getDraftReportRequired())).collect(Collectors.toList());
        if (Func.isNotEmpty(orderAllDTONotNeedDraft)) {
            List<String> orderNos = orderAllDTONotNeedDraft.stream().map(OrderAllDTO::getOrderNo).distinct().collect(Collectors.toList());
            response.setStatus(ResponseCode.FAIL.getCode());
            response.setMessage(messageUtil.get("order.needDraft.no", new Object[]{StringUtils.join(orderNos, ", ")}));
            return response;
        }
        if (Func.isNotEmpty(checkOrderAllDTOS) && checkOrderAllDTOS.size() > 1) {
            //校验applicant是否相同
            List<OrderAllDTO> orderAllDTOApplicant = checkOrderAllDTOS.stream().
                    filter(orderAllDTO -> Func.isEmpty(orderAllDTO.getApplicantBossNo())).collect(Collectors.toList());
            if (Func.isNotEmpty(orderAllDTOApplicant) && orderAllDTOApplicant.size() < checkOrderAllDTOS.size()) {
                List<String> orderNos = orderAllDTOApplicant.stream().map(OrderAllDTO::getOrderNo).distinct().collect(Collectors.toList());
                response.setStatus(ResponseCode.FAIL.getCode());
                response.setMessage(messageUtil.get("order.applicant.empty", new Object[]{StringUtils.join(orderNos, ", ")}));
                return response;
            } else if (Func.isEmpty(orderAllDTOApplicant)) {
                Set<String> applicantBoosNo = checkOrderAllDTOS.stream().collect(Collectors.groupingBy(OrderAllDTO::getApplicantBossNo)).keySet();
                if (applicantBoosNo.size() > 1) {
                    response.setStatus(ResponseCode.FAIL.getCode());
                    response.setMessage(messageUtil.get("order.applicant.different"));
                    return response;
                }
            }
        }

        //订单类型统一校验
        Set<String> caseTypeList = checkOrderAllDTOS.stream().collect(Collectors.groupingBy(OrderAllDTO::getCaseType)).keySet();
        if (caseTypeList.size() > 1) {
            response.setStatus(ResponseCode.FAIL.getCode());
            response.setMessage(messageUtil.get("order.caseType.different"));
            return response;
        }
        //draft 文件校验
        ReportFileExample reportFileExample = new ReportFileExample();
        reportFileExample.createCriteria().andReportIDIn(reportIdList);
        List<ReportFilePO> reportFilePOS = reportFileMapper.selectByExample(reportFileExample);
        List<String> noFileReportNo = new ArrayList<>();
        for (ReportInfoPO reportInfoPO : reportInfoPOS) {
            //如果是外部系统回的报告，存在报告就校验通过
            if (!Func.equals(ReportFlagEnums.REPORT.getCode(), reportInfoPO.getReportFlag())) {
                continue;
            }
            //获取订单信息
            OrderAllDTO orderAllDTO = orderListBaseResponse.getData().stream().filter(i -> StringUtils.equalsIgnoreCase(i.getOrderNo(), reportInfoPO.getOrderNo())).findAny().orElse(null);
            if (Func.isEmpty(orderAllDTO)) {
                response.setStatus(ResponseCode.FAIL.getCode());
                response.setMessage(messageUtil.get("report.order.result.empty", new Object[]{reportInfoPO.getReportNo()}));
                return response;
            }
            String reportLanguage = orderAllDTO.getReportLanguage();
            //报告下文件
            List<ReportFilePO> reportFilePOSList = null;
            TestRequestInfo testRequestInfo = testRequestInfoList.stream().filter(i -> StringUtils.equalsIgnoreCase(i.getOrderId(), orderAllDTO.getID())).findFirst().orElse(null);
            if (Func.isNotEmpty(testRequestInfo) && ReportRequirementEnum.check(testRequestInfo.getReportRequirement(), ReportRequirementEnum.Customer_Report_Word, ReportRequirementEnum.Sub_Report_Word)) {
                reportFilePOSList = reportFilePOS.stream()
                        .filter(reportFilePO -> Func.equals(reportInfoPO.getReportNo(), reportFilePO.getReportNo()) && ReportFileType.check(reportFilePO.getReportFileType(), ReportFileType.Word)).collect(Collectors.toList());
            } else if (Func.isNotEmpty(testRequestInfo) && ReportRequirementEnum.check(testRequestInfo.getReportRequirement(), ReportRequirementEnum.Customer_Report_PDF)) {
                reportFilePOSList = reportFilePOS.stream()
                        .filter(reportFilePO -> Func.equals(reportInfoPO.getReportNo(), reportFilePO.getReportNo()) && ReportFileType.check(reportFilePO.getReportFileType(), ReportFileType.DraftPDF)).collect(Collectors.toList());
            }
            if (Func.isEmpty(reportFilePOSList)) {
                noFileReportNo.add(reportInfoPO.getReportNo());
            } else if (Func.equals(ReportLanguage.EnglishAndChineseReport.getCode(), reportLanguage) && reportFilePOSList.size() < 2) {
                noFileReportNo.add(reportInfoPO.getReportNo());
            }
        }
        if (Func.isNotEmpty(noFileReportNo)) {
            response.setStatus(ResponseCode.FAIL.getCode());
            response.setMessage(messageUtil.get("report.file.empty", new Object[]{noFileReportNo}));
            return response;
        }
        return response;
    }

    @Override
    public BaseResponse sendFinalCheck(SendFinalCheckReq sendFinalCheckReq) {
        //返回值
        BaseResponse response = new BaseResponse();
        response.setStatus(ResponseCode.SUCCESS.getCode());
        //参数校验
        if (Func.isEmpty(sendFinalCheckReq) || Func.isEmpty(sendFinalCheckReq.getReportIdList())) {
            response.setStatus(ResponseCode.FAIL.getCode());
            response.setMessage(messageUtil.get("param.miss.report.reportId"));
            return response;
        }
        List<String> reportIdList = sendFinalCheckReq.getReportIdList();
        //查询报告信息
        ReportInfoExample example = new ReportInfoExample();
        example.createCriteria().andIDIn(reportIdList);
        List<ReportInfoPO> reportInfoPOS = reportInfoMapper.selectByExample(example);
        if (Func.isEmpty(reportInfoPOS)) {
            response.setStatus(ResponseCode.FAIL.getCode());
            response.setMessage(messageUtil.get("report.result.empty"));
            return response;
        }
        List<String> orderNoList = reportInfoPOS.stream().map(ReportInfoPO::getOrderNo).collect(Collectors.toList());
        //查询订单信息
        OrderNosReq orderNosReq = new OrderNosReq();
        orderNosReq.setOrderNos(orderNoList);
        BaseResponse<List<OrderAllDTO>> orderListBaseResponse = gpoOrderFacade.getOrderAllListByOrderNos(orderNosReq);
        if (Func.isEmpty(orderListBaseResponse) || Func.isEmpty(orderListBaseResponse.getData())) {
            response.setStatus(ResponseCode.FAIL.getCode());
            response.setMessage(messageUtil.get("order.result.empty"));
            return response;
        }
        List<OrderAllDTO> orderAllDTOS = orderListBaseResponse.getData();

        BaseResponse<List<CheckReportConclusionRsp>> checkRes = gpnReportService.checkReportConclusion(reportIdList, "deliver");
        if (checkRes.getStatus() != ResponseCode.SUCCESS.getCode()) {
            return checkRes;
        }
        GpoExternalOrderNoReq gpoExternalOrderNoReq = new GpoExternalOrderNoReq();
        gpoExternalOrderNoReq.setProductLineCode(ProductLineContextHolder.getProductLineCode());
        gpoExternalOrderNoReq.setOrderNoList(orderNoList);
        List<GpoExternalOrderInfo> externalOrderInfoList = orderFacade.getExternalOrder(gpoExternalOrderNoReq).getData();
        List<String> pendingOrderNoList = orderAllDTOS.stream().filter(i -> OrderStatus.checkStatus(i.getOrderStatus(), OrderStatus.Pending)).map(OrderAllDTO::getOrderNo).collect(Collectors.toList());
        if (Func.isNotEmpty(pendingOrderNoList)) {
            List<String> externalOrderNoList = new ArrayList<>();
            if(Func.isNotEmpty(externalOrderInfoList)){
                for (String pendingOrderNo : pendingOrderNoList) {
                    GpoExternalOrderInfo externalOrderInfo = externalOrderInfoList.stream().filter(i -> Func.equals(i.getOrderNo(), pendingOrderNo)).findAny().orElse(null);
                    if(Func.isNotEmpty(externalOrderInfo) && Func.isNotEmpty(externalOrderInfo.getExternalOrderNo())){
                        String externalOrderNo = externalOrderInfo.getExternalOrderNo();
                        externalOrderNoList.add(externalOrderNo);
                    }else{
                        externalOrderNoList.add(pendingOrderNo);
                    }
                }
            }
            response.setMessage(messageUtil.get("order.status.is", new Object[]{StringUtils.join(externalOrderNoList, ", "), "Pending"}));
            response.setStatus(500);
            return response;
        }
        List<String> notDeliverReportNoList = new ArrayList<>();
        List<String> checkDeliverReportNoList = new ArrayList<>();

        List<String> unPaymentOrderNoList = Lists.newArrayList();
        List<String> noFileList = Lists.newArrayList();
        for (ReportInfoPO reportInfoPO : reportInfoPOS) {
            OrderAllDTO orderAllDTO = orderAllDTOS.stream().filter(i -> Func.equals(i.getOrderNo(), reportInfoPO.getOrderNo())).findAny().orElse(null);
//            //查询Customer配置
//            //GPO2-14351 查询Buyer配置是否not need dss
//            OrderIdReq orderIdReq = new OrderIdReq();
//            orderIdReq.setOrderNo(orderAllDTO.getOrderNo());
//            Boolean buyerNotNeedDssFlag = false;
//            BaseResponse<List<CustomerInstanceDTO>> orderCustomerRes = gpoOrderFacade.queryCustomerForPe(orderIdReq);
//            if(Func.isNotEmpty(orderCustomerRes) && Func.isNotEmpty(orderCustomerRes.getData())){
//                List<CustomerInstanceDTO> customerList = orderCustomerRes.getData();
//                CustomerInstanceDTO buyer = customerList.stream().filter(e ->
//                        Func.isNotEmpty(e.getCustomerUsage()) && Func.equalsSafe(e.getCustomerUsage(), CustomerUsage.Buyer.getCode())).findAny().orElse(null);
//                if(Func.isNotEmpty(buyer)){
//                    //查询客户配置
//                    CustomerDetailReq customerDetailReq = new CustomerDetailReq();
//                    customerDetailReq.setCustomerId(buyer.getCustomerId());
//                    customerDetailReq.setBuCode(orderAllDTO.getBUCode());
//                    customerDetailReq.setLocationCode(orderAllDTO.getLocationCode());
//                    CustomerExtNewListRsp customerExt = customerClient.getCustomerExtInfoByBu(customerDetailReq);
//                    if(Func.isNotEmpty(customerExt) && Func.isNotEmpty(customerExt.getNotNeedDSS())){
//                        if(customerExt.getNotNeedDSS().equals(1)){
//                            buyerNotNeedDssFlag = true;
//                        }
//                    }
//                }
//            }
//            Boolean notNeedFlag = buyerNotNeedDssFlag;
//            if(notNeedFlag){
//                //查询DSS报告是否回来
//                OrderAttachmentQueryReq orderAttachmentQueryReq = new OrderAttachmentQueryReq();
//                orderAttachmentQueryReq.setReportNo(reportInfoPO.getReportNo());
//                orderAttachmentQueryReq.setBusinessType("RWD");
//                orderAttachmentQueryReq.setProductLineCode(ProductLineContextHolder.getProductLineCode());
//                BaseResponse<List<OrderAttachmentRsp>> orderAttachment = orderFacade.getOrderAttachment(orderAttachmentQueryReq);
//                if(orderAttachment.isSuccess() && Func.isEmpty(orderAttachment.getData())){
//                    notDeliverReportNoList.add(reportInfoPO.getActualReportNo());
//                    break;
//                }
//            }
            String externalOrderNo = reportInfoPO.getOrderNo();
            if(Func.isNotEmpty(externalOrderInfoList)){
                GpoExternalOrderInfo externalOrderInfo = externalOrderInfoList.stream().filter(i -> Func.equals(i.getOrderNo(), reportInfoPO.getOrderNo())).findAny().orElse(null);
                if(Func.isNotEmpty(externalOrderInfo) && Func.isNotEmpty(externalOrderInfo.getExternalOrderNo())){
                    externalOrderNo = externalOrderInfo.getExternalOrderNo();
                }
            }
            boolean hasUploadSubcontract = false;
            //验证是否有 subcontract ,有subcontract 就可以发送邮件
            SubContractExample subcontractExample = new SubContractExample();
            subcontractExample.createCriteria().andOrderNoEqualTo(reportInfoPO.getOrderNo()).andStatusNotEqualTo(SubContractStatusEnum.Cancelled.getStatus());
            List<SubContractPO> subcontractList = subContractMapper.selectByExample(subcontractExample);

            if (CollectionUtils.isNotEmpty(subcontractList)) {
                List<String> subContractNoList = subcontractList.stream().map(sub -> sub.getSubContractNo()).distinct().collect(Collectors.toList());
                //查看是否有上传 SubReport 类型的文件
                List<GpnSubReportDTO> subReportDTOS = subReportExtMapper.queryGpnSubReportByOrderNo(reportInfoPO.getOrderNo());
                if (CollectionUtil.isNotEmpty(subReportDTOS)) {
                    if (CollectionUtils.isNotEmpty(subReportDTOS.stream().filter(e -> "SubReport".equals(e.getFileType())).collect(Collectors.toList()))) {
                        hasUploadSubcontract = true;
                    }
                }
            }
            if (!hasUploadSubcontract) {
                if (ReportStatus.New.getCode() == reportInfoPO.getReportStatus().intValue() ||
                        ReportStatus.Draft.getCode() == reportInfoPO.getReportStatus().intValue()) {
                    checkDeliverReportNoList.add(reportInfoPO.getReportNo());
                }
            }
            String flag = gpnReportService.deliverPermission(reportInfoPO.getID(), orderAllDTO);
            if ("forbid".equals(flag)) {
                //验证不通过，不能deliver
                notDeliverReportNoList.add(reportInfoPO.getActualReportNo());
            }

            if ("unPaymented".equals(flag)) {
                //验证不通过，不能deliver
                unPaymentOrderNoList.add(externalOrderNo);
            }

            if (!gpnReportService.reportDeliveryFileCheck(reportInfoPO.getID(), false)) {
                noFileList.add(reportInfoPO.getActualReportNo());
            }
        }
        if (CollectionUtils.isNotEmpty(notDeliverReportNoList)) {
            response.setMessage(messageUtil.get("report.status.not", new Object[]{StringUtils.join(notDeliverReportNoList, ", "), "Approved/Confirmed/Completed"}));
            response.setStatus(500);
            return response;
        }
        if (CollectionUtils.isNotEmpty(unPaymentOrderNoList)) {
            response.setMessage(messageUtil.get("order.enableDelivery.no", new Object[]{StringUtils.join(unPaymentOrderNoList, ", ")}));
            response.setStatus(500);
            return response;
        }
        if (Func.isNotEmpty(noFileList)) {
            response.setMessage(messageUtil.get("report.file.empty", new Object[]{noFileList}));
            response.setStatus(500);
            return response;
        }
        //check完成更新Invoice PDF
        com.sgs.gpo.facade.model.preorder.order.req.OrderIdReq orderIdReq = new com.sgs.gpo.facade.model.preorder.order.req.OrderIdReq();
        orderIdReq.setOrderNoList(orderNoList.stream().collect(Collectors.toSet()));
        orderTempFacade.updateInvoice(orderIdReq);
        return response;
    }
}
