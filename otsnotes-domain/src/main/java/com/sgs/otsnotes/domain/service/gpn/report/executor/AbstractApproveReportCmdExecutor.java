package com.sgs.otsnotes.domain.service.gpn.report.executor;

import cn.hutool.core.util.NumberUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.sgs.core.domain.UserInfo;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.core.base.ResponseCode;
import com.sgs.framework.core.context.SystemContextHolder;
import com.sgs.framework.core.exception.Assert;
import com.sgs.framework.core.exception.BizException;
import com.sgs.framework.core.util.IdUtil;
import com.sgs.framework.facade.domain.rsp.BuParamValueRsp;
import com.sgs.framework.log.model.SystemLog;
import com.sgs.framework.model.enums.ProductLineType;
import com.sgs.framework.model.enums.*;
import com.sgs.framework.tool.utils.CollectionUtil;
import com.sgs.framework.tool.utils.DateUtil;
import com.sgs.framework.tool.utils.Func;
import com.sgs.framework.tool.utils.ProductLineContextHolder;
import com.sgs.gpo.facade.model.report.req.ReportEntryModeUpdateReq;
import com.sgs.gpo.facade.model.report.req.ReportTestResultStatusUpdateReq;
import com.sgs.gpo.facade.temp.ReportTempFacade;
import com.sgs.grus.bizlog.BizLogClient;
import com.sgs.grus.bizlog.info.BizLogInfo;
import com.sgs.otsnotes.core.config.GPOConfig;
import com.sgs.otsnotes.core.config.InterfaceConfig;
import com.sgs.otsnotes.core.constants.BizLogConstant;
import com.sgs.otsnotes.core.constants.Constants;
import com.sgs.otsnotes.core.enums.ApproveType;
import com.sgs.otsnotes.core.enums.ReportFlagEnums;
import com.sgs.otsnotes.core.enums.ReportType;
import com.sgs.otsnotes.core.kafka.Producer;
import com.sgs.otsnotes.dbstorages.mybatis.enums.CustomerUsage;
import com.sgs.otsnotes.dbstorages.mybatis.extmapper.ConclusionListExtMapper;
import com.sgs.otsnotes.dbstorages.mybatis.extmapper.ObjectOperationLogInfoExtMapper;
import com.sgs.otsnotes.dbstorages.mybatis.extmapper.OrderMapper;
import com.sgs.otsnotes.dbstorages.mybatis.extmapper.ReportFileExtMapper;
import com.sgs.otsnotes.dbstorages.mybatis.extmapper.SubContractExtMapper;
import com.sgs.otsnotes.dbstorages.mybatis.extmodel.ConclusionListInfo;
import com.sgs.otsnotes.dbstorages.mybatis.mapper.ReportDeliverHistoryInfoMapper;
import com.sgs.otsnotes.dbstorages.mybatis.mapper.ReportFileHistoryInfoMapper;
import com.sgs.otsnotes.dbstorages.mybatis.mapper.ReportFileMapper;
import com.sgs.otsnotes.dbstorages.mybatis.mapper.ReportTemplateInfoMapper;
import com.sgs.otsnotes.dbstorages.mybatis.model.*;
import com.sgs.otsnotes.domain.service.ConclusionService;
import com.sgs.otsnotes.domain.service.digitalReport.DigitalReportService;
import com.sgs.otsnotes.domain.service.digitalReport.dto.DigitalReportConfigDTO;
import com.sgs.otsnotes.domain.service.digitalReport.request.UpdateReportReq;
import com.sgs.otsnotes.domain.service.digitalReport.response.UpdateReportRes;
import com.sgs.otsnotes.domain.service.gpn.report.IGpnReportService;
import com.sgs.otsnotes.domain.service.gpn.report.cmd.AbstractApproveReportCmd;
import com.sgs.otsnotes.domain.service.gpn.report.cmd.ReportCmd;
import com.sgs.otsnotes.domain.service.gpn.sendEmail.ISendEmailService;
import com.sgs.otsnotes.domain.service.gpn.status.IGpnStatusService;
import com.sgs.otsnotes.domain.service.gpn.tracking.ITrackingService;
import com.sgs.otsnotes.domain.service.reportFile.IReportFileService;
import com.sgs.otsnotes.facade.model.dto.*;
import com.sgs.otsnotes.facade.model.enums.ReportStatus;
import com.sgs.otsnotes.facade.model.enums.*;
import com.sgs.otsnotes.facade.model.info.subcontract.SubContractInfo;
import com.sgs.otsnotes.facade.model.kafka.GeneralMessage;
import com.sgs.otsnotes.facade.model.kafka.KafkaTopicConsts;
import com.sgs.otsnotes.facade.model.kafka.ReportApproveMessage;
import com.sgs.otsnotes.facade.model.kafka.SyncStatusMessage;
import com.sgs.otsnotes.facade.model.req.gpn.GpnLabListReq;
import com.sgs.otsnotes.facade.model.req.report.GenerateDssPdfReq;
import com.sgs.otsnotes.facade.model.req.status.SaveGpnStatusReq;
import com.sgs.otsnotes.facade.model.rsp.LabInfo;
import com.sgs.otsnotes.facade.model.rsp.PageInfoList;
import com.sgs.otsnotes.integration.*;
import com.sgs.preorder.facade.DeliveryLogFacade;
import com.sgs.preorder.facade.OrderFacade;
import com.sgs.preorder.facade.OrderReportFacade;
import com.sgs.preorder.facade.model.dto.customer.CustomerInstanceDTO;
import com.sgs.preorder.facade.model.dto.order.OrderAllDTO;
import com.sgs.preorder.facade.model.dto.order.OrderInfoDto;
import com.sgs.preorder.facade.model.dto.order.OrderTrfDTO;
import com.sgs.preorder.facade.model.enums.CaseType;
import com.sgs.preorder.facade.model.enums.ContactsType;
import com.sgs.preorder.facade.model.enums.DeliveryAction;
import com.sgs.preorder.facade.model.enums.OperationType;
import com.sgs.preorder.facade.model.info.OrderTrfRelInfo;
import com.sgs.preorder.facade.model.info.TestRequestContactsInfo;
import com.sgs.preorder.facade.model.info.TestRequestInfo;
import com.sgs.preorder.facade.model.req.DeliveryLog.DeliveryLogReq;
import com.sgs.preorder.facade.model.req.OrderIdReq;
import com.sgs.preorder.facade.model.req.SaveReportReq;
import com.sgs.preorder.facade.model.req.customer.CustomerDetailReq;
import com.sgs.preorder.facade.model.rsp.customer.CustomerExtNewListRsp;
import com.sgs.preorder.facade.model.rsp.order.OrderReferenceNoRsp;
import com.sgs.user.facade.domain.userInfo.UserDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.util.ObjectUtils;

import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
public abstract class AbstractApproveReportCmdExecutor extends AbstractReportCmdExecutor {
    @Autowired
    protected SubContractExtMapper subContractExtMapper;

    @Autowired
    protected ReportFileExtMapper reportFileExtMapper;

    @Autowired
    private ReportFileMapper reportFileMapper;
    @Autowired
    private OrderMapper orderMapper;
    @Autowired
    private ReportTemplateInfoMapper reportTemplateInfoMapper;


    @Autowired
    protected OrderFacade gpoOrderFacade;
    @Autowired
    private BizLogClient bizLogClient;
    @Autowired
    private TokenClient tokenClient;

    @Autowired
    private TransactionTemplate transactionTemplate;

    @Autowired
    private FrameWorkClient frameWorkClient;

    @Autowired
    private OrderReportFacade gpoOrderReportFacade;

    @Autowired
    private ITrackingService trackingService;
    @Autowired
    private ConclusionListExtMapper conclusionListExtMapper;
    @Autowired
    private ConclusionService conclusionService;

    @Autowired
    private Producer producer;
    @Autowired
    private GPOConfig gpoConfig;

    @Autowired
    private OrderClient orderClient;

    @Autowired
    private DeliveryLogFacade deliveryLogFacade;

    @Autowired
    @Lazy
    private IGpnReportService iGpnReportService;

    @Autowired
    private ISendEmailService sendEmailService;

    @Autowired
    private InterfaceConfig interfaceConfig;
    @Autowired
    private ReportDeliverHistoryInfoMapper deliverHistoryInfoMapper;
    @Autowired
    private UserManagementClient userManagementClient;
    @Autowired
    private IReportFileService reportFileService;
    @Autowired
    private IGpnStatusService gpnStatusService;
    @Autowired
    private ReportFileHistoryInfoMapper reportFileHistoryInfoMapper;
//    @Autowired
//    private OnlineConfig onlineConfig;
    @Autowired
    private DigitalReportService digitalReportService;
    @Autowired
    private CustomerClient customerClient;
    @Autowired
    private ReportTempFacade reportTempFacade;
    @Autowired
    private ObjectOperationLogInfoExtMapper objectOperationLogInfoExtMapper;

    @Override
    protected  void validateBizRule(ReportContext reportContext) {
        AbstractApproveReportCmd reportCmd = reportContext.getReportCmd();
        //操作分包单时校验报告下是否存在最终报告
        if (Func.equals(ReportFlagEnums.SUB_REPORT.getCode(), reportCmd.getReportType())) {
            checkReportFileByReportRequirement(reportContext);
        }
        //校验word是否存在
        else {
            checkReportWordByReportRequirement(reportContext);
        }
        // 执行业务状态校验
        if(ApproveType.check(reportCmd.getApproveType(),ApproveType.APPROVE)){
            BaseResponse<GpnOrderReportDetailDTO> reportDetailBaseResponse = iGpnReportService.getReportById(reportContext.getReportId());
            Assert.isTrue(reportDetailBaseResponse.isSuccess(), ResponseCode.ILLEGAL_ARGUMENT,"报告信息不存在.");
            GpnReportButtonDisabledDTO reportButtonDisabled = reportDetailBaseResponse.getData().getReportButtonDisabled();
            Assert.isTrue(!reportButtonDisabled.isApproveButtonDisabled(), ResponseCode.INTERNAL_SERVER_ERROR,"报告状态已经更新，请刷新后重新操作.");
        }
    }

    /**
     * 校验报告是否存在最终报告
     *
     * @param reportContext
     * @return
     */
    private void checkReportFileByReportRequirement(ReportContext reportContext) {
        ReportInfoPO reportInfoPO = reportContext.getReportInfoPO();
        ReportCmd reportCmd = reportContext.getReportCmd();
        //校验附件
        //查询需要附件类型
        OrderIdReq orderIdReq = new OrderIdReq();
        orderIdReq.setOrderNo(reportInfoPO.getOrderNo());
        orderIdReq.setProductLineCode(reportCmd.getProductLineCode());
        BaseResponse<TestRequestInfo> testRequestInfoBaseResponse = gpoOrderFacade.queryRestRequestForPe(orderIdReq);
        // 查看是reportRequirement
        String reportRequirement = null;
        String reportLanguage = null;
        if (ResponseCode.SUCCESS.getCode() == testRequestInfoBaseResponse.getStatus()) {
            TestRequestInfo testRequestInfo = testRequestInfoBaseResponse.getData();
            if (Func.isNotEmpty(testRequestInfo) && Func.isNotEmpty(testRequestInfo.getReportRequirement())) {
                reportRequirement = testRequestInfo.getReportRequirement();
                reportLanguage = testRequestInfo.getReportLanguage();
            }
        }
        Assert.notNull(reportRequirement, "订单下Report Requirement字段没有选择！");

        //查询附件
        ReportFilePO reportFilePO = new ReportFilePO();
        reportFilePO.setReportID(reportContext.getReportId());
        if (Func.equals(ReportRequirementEnum.Customer_Report_PDF.getCode(), reportRequirement)) {
            reportFilePO.setReportFileType(ReportFileType.PDF.getCode());
        } else if (Func.equals(ReportRequirementEnum.Sub_Report_Word.getCode(), reportRequirement)) {
            reportFilePO.setReportFileType(ReportFileType.Word.getCode());
        } else if (Func.equals(ReportRequirementEnum.Customer_Report_Word.getCode(), reportRequirement)) {
            reportFilePO.setReportFileType(ReportFileType.Word.getCode());
        }
        //查询
        //判断是否有draft PDF

        List<ReportFilePO> reportFilePOList = reportFileExtMapper.getReportFileInfoList(reportFilePO);
        Assert.isTrue(Func.isNotEmpty(reportFilePOList), ResponseCode.ILLEGAL_ARGUMENT, "报告下最终报告不存在，请稍后重试！");

        // 双语报告下校验中英文报告都已经存在
        if (Func.equalsSafe(reportLanguage, ReportLanguage.EnglishAndChineseReport.getCode())) {
            Assert.isTrue(reportFilePOList.size() == 2, ResponseCode.ILLEGAL_ARGUMENT, "[双语报告] 报告回写还没全部完成！");
            ReportFilePO englishReport = reportFilePOList.stream().filter(report -> Func.equalsSafe(report.getLanguageID(), LanguageType.English.getLanguageId())).findAny().orElse(null);
            ReportFilePO cnReport = reportFilePOList.stream().filter(report -> Func.equalsSafe(report.getLanguageID(), LanguageType.Chinese.getLanguageId())).findAny().orElse(null);
            Assert.isTrue(Func.isNotEmpty(englishReport) && Func.isNotEmpty(cnReport), ResponseCode.ILLEGAL_ARGUMENT, "[双语报告] 报告回写还没全部完成！");
        }
    }
    /**
     * 生成pdf之前校验word是否生成
     *
     * @param reportContext
     * @return
     */
    private void checkReportWordByReportRequirement(ReportContext reportContext) {
        ReportInfoPO reportInfoPO = reportContext.getReportInfoPO();
        ReportCmd reportCmd = reportContext.getReportCmd();
        //校验附件
        //查询需要附件类型
        OrderIdReq orderIdReq = new OrderIdReq();
        orderIdReq.setOrderNo(reportInfoPO.getOrderNo());
        orderIdReq.setProductLineCode(reportCmd.getProductLineCode());
        BaseResponse<TestRequestInfo> testRequestInfoBaseResponse = gpoOrderFacade.queryRestRequestForPe(orderIdReq);
        // 查看是reportRequirement
        String reportLanguage = null;
        int languageId = LanguageType.English.getLanguageId();
        if (ResponseCode.SUCCESS.getCode() == testRequestInfoBaseResponse.getStatus()) {
            TestRequestInfo testRequestInfo = testRequestInfoBaseResponse.getData();
            if (Func.isNotEmpty(testRequestInfo)) {
                reportLanguage = testRequestInfo.getReportLanguage();
                if (Func.equalsSafe(ReportLanguage.ChineseReportOnly.getCode(), testRequestInfo.getReportLanguage())) {
                    languageId = LanguageType.Chinese.getLanguageId();
                }else if (Func.equalsSafe(ReportLanguage.MultilingualReport.getCode(), testRequestInfo.getReportLanguage())) {
                    languageId = LanguageType.EnglishAndChinese.getLanguageId();
                }
            }

        } else {

            Assert.isTrue(false, ResponseCode.ILLEGAL_ARGUMENT, "查询报告语言失败");
        }
        //查询附件
        ReportFilePO reportFilePO = new ReportFilePO();
        reportFilePO.setReportID(reportContext.getReportId());
        reportFilePO.setReportFileType(ReportFileType.Word.getCode());
        //查询
        List<ReportFilePO> reportFilePOList = reportFileExtMapper.getReportFileInfoList(reportFilePO);
        Assert.isTrue(Func.isNotEmpty(reportFilePOList), ResponseCode.ILLEGAL_ARGUMENT, "查询word为空！");

        ReportFilePO reportFilePOEn = reportFilePOList.stream().filter(e ->
                e.getLanguageID().intValue() == LanguageType.English.getLanguageId() &&
                        Func.isNotEmpty(e.getCloudID())).findFirst().orElse(null);
        ReportFilePO reportFilePOCn = reportFilePOList.stream().filter(e ->
                e.getLanguageID().intValue() == LanguageType.Chinese.getLanguageId() &&
                        Func.isNotEmpty(e.getCloudID())).findFirst().orElse(null);
        ReportFilePO reportFilePOEnAndCn = reportFilePOList.stream().filter(e ->
                e.getLanguageID().intValue() == LanguageType.EnglishAndChinese.getLanguageId() &&
                        Func.isNotEmpty(e.getCloudID())).findFirst().orElse(null);

        if (Func.equals(ReportLanguage.EnglishAndChineseReport.getCode(), reportLanguage)) {
            Assert.isTrue((Func.isNotEmpty(reportFilePOEn) && Func.isNotEmpty(reportFilePOCn)),
                    ResponseCode.ILLEGAL_ARGUMENT, "The report did not return all word document！");
        } else if (Func.equals(LanguageType.English.getLanguageId(), languageId)) {
            Assert.isTrue(Func.isNotEmpty(reportFilePOEn),
                    ResponseCode.ILLEGAL_ARGUMENT, "The report did not return all word document！");
        } else if (Func.equals(LanguageType.Chinese.getLanguageId(), languageId)) {
            Assert.isTrue(Func.isNotEmpty(reportFilePOCn),
                    ResponseCode.ILLEGAL_ARGUMENT, "The report did not return all word document！");
        } else if (Func.equals(LanguageType.EnglishAndChinese.getLanguageId(), languageId)) {
            Assert.isTrue(Func.isNotEmpty(reportFilePOEnAndCn),
                    ResponseCode.ILLEGAL_ARGUMENT, "The report did not return all word document！");
        }


    }

    @Override
    protected void executeImpl(ReportContext reportContext) {
        ReportInfoPO reportInfoPO = reportContext.getReportInfoPO();
        AbstractApproveReportCmd reportCmd = reportContext.getReportCmd();
        //更新 report
        Integer oldStatus = reportInfoPO.getReportStatus();
        AtomicBoolean allReportFileApprovedFlag = new AtomicBoolean(false);
        String finalUserName = reportContext.getOpUserName();

        //report对应订单信息
        OrderIdReq orderIdReq = new OrderIdReq();
        orderIdReq.setOrderNo(reportInfoPO.getOrderNo());
        BaseResponse<OrderAllDTO> orderRes = gpoOrderFacade.getOrderForPe(orderIdReq);

        Assert.notNull(orderRes, "查询订单信息失败" + reportInfoPO.getOrderNo());
        Assert.notNull(orderRes.getData(), "查询订单信息失败" + reportInfoPO.getOrderNo());

        OrderAllDTO orderAllDTO = orderRes.getData();

        //查询需要附件类型 用于Approve后，判断如果Report Requirment!=PDF清空签名
        orderIdReq.setProductLineCode(reportCmd.getProductLineCode());
        BaseResponse<TestRequestInfo> testRequestInfoBaseResponse = gpoOrderFacade.queryRestRequestForPe(orderIdReq);
        // 查看是reportRequirement Approve后，判断如果Report Requirment!=PDF，调用DigitalReport.updateReport接口 : 传对应的签名书签, 清空签名
        String reportRequirement = null;
        if (ResponseCode.SUCCESS.getCode() == testRequestInfoBaseResponse.getStatus()) {
            TestRequestInfo testRequestInfo = testRequestInfoBaseResponse.getData();
            if (Func.isNotEmpty(testRequestInfo) && Func.isNotEmpty(testRequestInfo.getReportRequirement())) {
                reportRequirement = testRequestInfo.getReportRequirement();
            }
        }
        Assert.notNull(reportRequirement, "订单下Report Requirement字段没有选择！");

        //GPO2-14351 查询Buyer配置是否not need dss
        //查询Customer Buyer
        Boolean buyerNotNeedDssFlag = false;
        BaseResponse<List<CustomerInstanceDTO>> orderCustomerRes = gpoOrderFacade.queryCustomerForPe(orderIdReq);
        if(Func.isNotEmpty(orderCustomerRes) && Func.isNotEmpty(orderCustomerRes.getData())){
            List<CustomerInstanceDTO> customerList = orderCustomerRes.getData();
            CustomerInstanceDTO buyer = customerList.stream().filter(e ->
                    Func.isNotEmpty(e.getCustomerUsage()) && Func.equalsSafe(e.getCustomerUsage(),CustomerUsage.Buyer.getCode())).findAny().orElse(null);
            if(Func.isNotEmpty(buyer)){
                //查询客户配置
                CustomerDetailReq customerDetailReq = new CustomerDetailReq();
                customerDetailReq.setCustomerId(buyer.getCustomerId());
                customerDetailReq.setBuCode(orderAllDTO.getBUCode());
                customerDetailReq.setLocationCode(orderAllDTO.getLocationCode());
                CustomerExtNewListRsp customerExt = customerClient.getCustomerExtInfoByBu(customerDetailReq);
                if(Func.isNotEmpty(customerExt) && Func.isNotEmpty(customerExt.getNotNeedDSS())){
                    if(customerExt.getNotNeedDSS().equals(1)){
                        buyerNotNeedDssFlag = true;
                    }
                }
            }
        }
        Boolean notNeedFlag = buyerNotNeedDssFlag;
        Boolean isNeedDraft = isNeedDraft(reportCmd.getOrderNo());
        //旧approve日期，回滚使用
        Date approveDateOld = reportInfoPO.getApproverDate();
        String approverByOld = reportInfoPO.getApproverBy();

        Date approveDate = Func.isEmpty(reportInfoPO.getApproverDate()) ? new Date() : reportInfoPO.getApproverDate();
        reportInfoPO.setApproverDate(approveDate);
//        reportInfoPO.setApproverBy(finalUserName);
        //todo 因为后面也要更新report表，先拿带事务外
        approveReportFile(reportContext);
        //更新file中的approve
        editFileApproveDate(reportCmd.getReportId(), reportCmd.getReportFileId(), reportInfoPO,reportCmd.isBlockSend(), reportRequirement);

        allReportFileApprovedFlag.set(checkAllReportFileApproved(reportCmd.getReportId()));

//        Integer approveStatusOld =reportInfoPO.getApproveStatus();
        if (allReportFileApprovedFlag.get()) {
            assembleReport(reportInfoPO);
            reportInfoMapper.updateByPrimaryKeySelective(reportInfoPO);
            BaseResponse baseResponse = new BaseResponse();
            boolean temp = Boolean.TRUE.equals(transactionTemplate.execute((transactionStatus) -> {
                try {
                    /**
                     * 自己报告：更新报告状态为Approved、审核状态为Draft
                     * 分包报告：更新审核状态为Approved
                     */

                    //TODO RPC操作
                    // 更新GPO Report状态为Approved；
                    SaveReportReq saveReportReq = new SaveReportReq();
                    saveReportReq.setReportNo(reportInfoPO.getReportNo());
                    saveReportReq.setReportStatus(ReportStatus.Approved.getCode());
                    saveReportReq.setReportApprovalDate(approveDate);
                    saveReportReq.setApproverBy(finalUserName);
                    updateGpoReportInfo(saveReportReq);

                    //TODO RPC操作
                    // 自己出报告，调用Word->PDF
                    if (ObjectUtils.isEmpty(reportCmd.getReportType())
                            || Func.equals(ReportFlagEnums.REPORT.getCode(), reportCmd.getReportType())) {
                        GenerateDssPdfReq generateDssPdfReq = new GenerateDssPdfReq();
                        generateDssPdfReq.setReportId(reportCmd.getReportId());
                        generateDssPdfReq.setOrderId(reportCmd.getOrderNo());
                        generateDssPdfReq.setBuCode(ProductLineContextHolder.getProductLineCode());
                        generateDssPdfReq.setIsNeedDraft(isNeedDraft);
                        generateDssPdfReq.setNotNeedDSS(notNeedFlag);
                        iGpnReportService.approveReportToDss(generateDssPdfReq);
                    }

                    //判断发送邮件   21/11/30修改
                    if(!reportCmd.isBlockSend()){
                        String messageSendEmail = this.approveSendEmail(reportInfoPO, reportCmd.getReportType());
                        if (Func.isNotEmpty(messageSendEmail)) {
                            baseResponse.setStatus(ResponseCode.FAIL.getCode());
                            baseResponse.setMessage(messageSendEmail);
                            transactionStatus.setRollbackOnly();
                            return false;
                        }
                    }
                    //GPO2-7302  内部分包，保存返回subReport定时任务
                    if (Func.equals(ReportFlagEnums.REPORT.getCode(), reportCmd.getReportType())
                            && iGpnReportService.existsMatchTrfRelation(orderAllDTO.getOrderNo(), RefSystemIdEnum.SubContract)
                            && OperationType.check(orderAllDTO.getOperationType(), OperationType.SubContract, OperationType.NewSubContract, OperationType.LightSubContract, OperationType.SubContractBind)) {
                        this.subReportDeliveryLogSave(reportInfoPO.getID(), finalUserName);
                    }
                    //记录Report Status更新为Approved
                    SaveGpnStatusReq saveGpnStatusReq = new SaveGpnStatusReq();
                    saveGpnStatusReq.setOrderNo(reportInfoPO.getOrderNo());
                    saveGpnStatusReq.setObjectNo(reportInfoPO.getReportNo());
                    saveGpnStatusReq.setStatusOperation(TbStatusType.Report_Approved);
                    saveGpnStatusReq.setRemark("Report Approve操作");
                    gpnStatusService.saveReportStatus(saveGpnStatusReq);
                    if (Func.isNotEmpty(reportCmd.getReportFileId())) {
                        ReportFilePO reportFilePO = reportFileMapper.selectByPrimaryKey(reportCmd.getReportFileId());
                        if(Func.isNotEmpty(reportFilePO) && ReportFileType.check(reportFilePO.getReportFileType(),ReportFileType.TestResult)){
                            TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
                                @Override
                                public void afterCommit() {
                                    try {
                                        ReportTestResultStatusUpdateReq reportTestResultStatusUpdateReq = new ReportTestResultStatusUpdateReq();
                                        reportTestResultStatusUpdateReq.setReportIdList(Sets.newHashSet(reportInfoPO.getID()));
                                        reportTestResultStatusUpdateReq.setEntryMode(ReportEntryModeEnum.TEST_RESULT_REPORT.getCode());
                                        reportTestResultStatusUpdateReq.setProductLineCode(Func.isNotBlank(reportCmd.getProductLineCode())?reportCmd.getProductLineCode():ProductLineContextHolder.getProductLineCode());
                                        reportTestResultStatusUpdateReq.setToken(SystemContextHolder.getSgsToken());
                                        reportTempFacade.updateReportTestResultStatus(reportTestResultStatusUpdateReq);

                                        ReportEntryModeUpdateReq reportEntryModeUpdateReq = new ReportEntryModeUpdateReq();
                                        reportEntryModeUpdateReq.setReportIdList(Sets.newHashSet(reportInfoPO.getID()));
                                        reportEntryModeUpdateReq.setEntryMode(ReportEntryModeEnum.TEST_RESULT_REPORT.getCode());
                                        reportEntryModeUpdateReq.setProductLineCode(Func.isNotBlank(reportCmd.getProductLineCode())?reportCmd.getProductLineCode():ProductLineContextHolder.getProductLineCode());
                                        reportEntryModeUpdateReq.setToken(SystemContextHolder.getSgsToken());
                                        reportTempFacade.updateReportEntryMode(reportEntryModeUpdateReq);
                                    } catch (Exception e) {

                                    }
                                }
                            });
                        }
                    }
                    return true;
                } catch (Exception e) {
                    log.error("Approve Report error:{}", e);
                    transactionStatus.setRollbackOnly();
                    return false;
                }

            }));
            if (!temp) {
                //回滚report状态
                reportInfoPO.setReportStatus(oldStatus);
                reportInfoPO.setApproverDate(approveDateOld);
//                reportInfoPO.setApproverBy(approverByOld);
                reportInfoMapper.updateByPrimaryKeySelective(reportInfoPO);
                if (Func.equals(ResponseCode.SUCCESS.getCode(), baseResponse.getStatus())) {
                    baseResponse.setStatus(ResponseCode.FAIL.getCode());
                    baseResponse.setMessage("Approve Report error");
                }
                Assert.isTrue(ResponseCode.SUCCESS.getCode() == baseResponse.getStatus(), ResponseCode.FAIL, baseResponse.getMessage());
            }
        } else {
            log.info("有多份报告文件且存在暂未未审批的报告");
        }
        if (allReportFileApprovedFlag.get()) {
            if(ApproveType.check(reportCmd.getApproveType(),ApproveType.APPROVE)){
                trackingService.sendApprove(reportInfoPO.getOrderNo(), reportCmd.getToken(), reportContext.getOpUserName(), reportInfoPO.getReportNo());
            }
            // Need draft report的订单：report approve时不触发to CP
            Integer draftReportRequired = orderAllDTO.getDraftReportRequired();
            log.info("approveReport->draftReportRequired:{}", draftReportRequired);

            //保存bizLog状态变化
            BizLogInfo bizLog = new BizLogInfo();
            bizLog.setBu(orderAllDTO.getBUCode());
            bizLog.setLab(orderAllDTO.getLocationCode());
            bizLog.setOpUser(reportContext.getOpUserName());
            bizLog.setBizId(reportInfoPO.getReportNo());
            if(ApproveType.check(reportCmd.getApproveType(),ApproveType.HOST_REVIEW)){
                bizLog.setOpType(ReportOperationTypeEnum.REVIEW.getType());
            }else{
                bizLog.setOpType(ReportOperationTypeEnum.APPROVE.getType());
            }
            bizLog.setBizOpType(BizLogConstant.REPORT_STATUS_CHANGE_HISTORY);
            bizLog.setNewVal(reportInfoPO.getReportStatus());
            bizLog.setOriginalVal(oldStatus);
            log.info("approve report bizLog param: {}", JSON.toJSONString(bizLog));
            bizLogClient.doSend(bizLog);


            BizLogInfo bizLogInfo = new BizLogInfo();
            bizLogInfo.setBu(orderAllDTO.getBUCode());
            bizLogInfo.setLab(orderAllDTO.getLocationCode());
            bizLogInfo.setOpUser(reportContext.getOpUserName());
            bizLogInfo.setBizId(reportInfoPO.getOrderNo());
            bizLogInfo.setBizOpType(BizLogConstant.REPORT_OPERATION_HISTORY);
            if(ApproveType.check(reportCmd.getApproveType(),ApproveType.HOST_REVIEW)){
                bizLogInfo.setOpType("Host Review");
                bizLogInfo.setNewVal("HOST Review, ReportNo:[" + orderClient.getExternalReportNo(reportInfoPO.getReportNo()) + "]");
            }else{
                if (Func.equals(ReportFlagEnums.REPORT.getCode(), reportCmd.getReportType())) {
                    bizLogInfo.setOpType("Approve");

                    bizLogInfo.setNewVal("Approved[" + orderClient.getExternalReportNo(reportInfoPO.getReportNo()) + "]");
                } else {
                    bizLogInfo.setOpType("Review");
                    bizLogInfo.setNewVal("Sub Report Review, ReportNo:[" + orderClient.getExternalReportNo(reportInfoPO.getReportNo()) + "]");
                }
            }
            bizLogClient.doSend(bizLogInfo);
            try {
                doSendMessageOnApprove(reportInfoPO, tokenClient.getUser());
                // TO CP PlayLoad 任务维护
                insertCpTask(reportInfoPO);
            } catch (Exception e) {
                log.error(e.getMessage());
            }
            Date now = new Date();
            //记录Approve OperationLog
            ObjectOperationLogInfoPO objectOperationLogInfoPO = new ObjectOperationLogInfoPO();
            objectOperationLogInfoPO.setId(IdUtil.uuId());
            objectOperationLogInfoPO.setObject(OperationObjectEnum.Report.getCode());
            objectOperationLogInfoPO.setAction(OperationActionEnum.Approve.getCode());
            objectOperationLogInfoPO.setObjectNo(reportInfoPO.getReportNo());
            objectOperationLogInfoPO.setCreatedDate(now);
            objectOperationLogInfoPO.setCreatedBy(finalUserName);
            objectOperationLogInfoPO.setModifiedDate(now);
            objectOperationLogInfoPO.setModifiedBy(finalUserName);
            objectOperationLogInfoPO.setOperateDate(now);
            objectOperationLogInfoPO.setOperator(finalUserName);
            objectOperationLogInfoPO.setActiveIndicator(ActiveType.Enable.getStatus());
            objectOperationLogInfoExtMapper.insertBatch(Lists.newArrayList(objectOperationLogInfoPO));
        }

    }

    abstract void assembleReport(ReportInfoPO reportInfoPO);


    private void approveReportFile(ReportContext reportContext) {
        AbstractApproveReportCmd reportCmd = reportContext.getReportCmd();
        ReportFilePO reportFilePO = new ReportFilePO();
        reportFilePO.setStatus(ReportFileStatus.APPROVED.getCode());
        if (Func.isNotEmpty(reportCmd.getReportFileId())) {
            //更新reportFile的status为 approved
            reportFilePO.setID(reportCmd.getReportFileId());
            reportFileMapper.updateByPrimaryKeySelective(reportFilePO);
        } else {
            ReportFileExample reportFileExample = new ReportFileExample();
            reportFileExample.createCriteria().andReportIDEqualTo(reportCmd.getReportId())
                    .andReportFileTypeEqualTo(ReportFileType.Word.getCode());
            reportFileMapper.updateByExampleSelective(reportFilePO, reportFileExample);
        }
    }

    /**
     * approve时更新文件approveDate
     *
     * @param reportId
     * @param reportFileId
     * @param reportRequirement 2024年12月20日 增加reportRequirement传参，清空签名 jire GPO2-15690
     * @return
     */
    protected String editFileApproveDate(String reportId, String reportFileId, ReportInfoPO reportInfoPO,boolean blockSend, String reportRequirement) {
        //需要更新的数据
        List<ReportFilePO> reportFilePOList = null;
        //判断是否是单个文件approve
        if (Func.isNotEmpty(reportFileId)) {
            ReportFilePO reportFilePO = reportFileMapper.selectByPrimaryKey(reportFileId);
            reportFilePOList = new ArrayList<>();
            if(Func.isNotEmpty(reportFilePO)) {
                reportFilePOList.add(reportFilePO);
            }
        } else {
            ReportFileExample reportFileExample = new ReportFileExample();
            reportFileExample.createCriteria().andReportIDEqualTo(reportId).andReportFileTypeEqualTo(ReportFileType.Word.getCode());
            reportFilePOList = reportFileMapper.selectByExample(reportFileExample);
        }

        Assert.isTrue(Func.isNotEmpty(reportFilePOList), ResponseCode.ILLEGAL_ARGUMENT, "更新文件approveDate时未查询到文件");

        //获取buId
        if(Func.isEmpty(reportInfoPO)){
            reportInfoPO = reportInfoMapper.selectByPrimaryKey(reportId);
        }
        LabInfo labInfo = this.getLabInfoByOrderNo(reportInfoPO.getOrderNo());

        Assert.notNull(labInfo, "获取order对应lab信息失败");
        Assert.notNull(labInfo.getProductLineID(), "获取order对应Product信息失败");
        //更新文件
        for (ReportFilePO reportFilePO : reportFilePOList) {
            String cloudId = null;
            try {
                cloudId = this.editApproveDate(reportInfoPO,reportFilePO,  labInfo,blockSend, reportRequirement);
            } catch (Exception e) {
                e.printStackTrace();
                log.info("文件更新approveDate失败：{}：{}", reportFilePO.getFilename(), e.getMessage());
                BizException.throwBizException(ResponseCode.FAIL,e.getMessage());
//                return "更新文件" + reportFilePO.getFilename() + " 时失败";
            }

            if (Func.isNotEmpty(cloudId)) {
                reportFilePO.setCloudID(cloudId);
                reportFileMapper.updateByPrimaryKeySelective(reportFilePO);
                //保存history
                ReportFileHistoryInfoPO reportFileHistoryInfoPO = new ReportFileHistoryInfoPO();
                reportFileHistoryInfoPO.setId(UUID.randomUUID().toString());
                reportFileHistoryInfoPO.setReportId(reportFilePO.getReportID());
                reportFileHistoryInfoPO.setReportNo(reportFilePO.getReportNo());
                reportFileHistoryInfoPO.setReportFileId(reportFilePO.getID());
                reportFileHistoryInfoPO.setCloudId(reportFilePO.getCloudID());
                reportFileHistoryInfoPO.setRemark("Approve Report");
                reportFileHistoryInfoPO.setCreatedDate(new Date());
                reportFileHistoryInfoPO.setCreatedBy("approveReport");
                reportFileHistoryInfoMapper.insert(reportFileHistoryInfoPO);
            }
        }
        return null;
    }

    /**
     * 根据订单获取bu
     *
     * @param orderNo
     * @return
     */
    private LabInfo getLabInfoByOrderNo(String orderNo) {
        GeneralOrderInstanceInfoPO generalOrderInstanceInfoPO = orderMapper.getOrderInfo(orderNo);
        if (Func.isEmpty(generalOrderInstanceInfoPO) || Func.isEmpty(generalOrderInstanceInfoPO.getLabCode())) {
            return null;
        }
        //查询lab信息
        GpnLabListReq gpnLabListReq = new GpnLabListReq();
        gpnLabListReq.setPage("1");
        gpnLabListReq.setRows("1");
        gpnLabListReq.setLabCode(generalOrderInstanceInfoPO.getLabCode());
        PageInfoList pageInfoList = frameWorkClient.getLabInfo(gpnLabListReq);
        if (Func.isEmpty(pageInfoList) || Func.isEmpty(pageInfoList.getRows()) || Func.isEmpty(pageInfoList.getRows().get(0))) {
            return null;
        } else {
            return pageInfoList.getRows().get(0);
        }
    }


    /**
     * 修改approveDate
     *
     * @param reportInfoPO
     * @param reportFilePO
     * @param labInfo
     * @param reportRequirement 2024年12月20日 增加reportRequirement传参，清空签名 jire GPO2-15690
     * @return
     * @throws Exception
     */
    private String editApproveDate(ReportInfoPO reportInfoPO,ReportFilePO reportFilePO,  LabInfo labInfo, boolean blockSend, String reportRequirement) throws Exception {
        log.info("editApproveDate reportInfo:{}",Func.toJson(reportInfoPO));
        Boolean updateActualCompleteDate = Func.isEmpty(reportInfoPO.getParentReportNo());
        String buCode = labInfo.getProductLineAbbr();
        String cloudId = reportFilePO.getCloudID();
        String reportId = reportInfoPO.getID();
        String reportNo = reportInfoPO.getReportNo();
        Integer languageId = reportFilePO.getLanguageID();
        Date approveDate = reportInfoPO.getApproverDate();
        Integer signatureLanguage = reportInfoPO.getSignatureLanguage();
        if(Func.isEmpty(signatureLanguage)){
            signatureLanguage = 0;
        }
        //获取模板信息
        ReportTemplateInfoExample reportTemplateInfoExample = new ReportTemplateInfoExample();
        ReportTemplateInfoExample.Criteria criteria = reportTemplateInfoExample.createCriteria();
        criteria.andReportIdEqualTo(reportId);
        if(LanguageType.check(languageId,LanguageType.EnglishAndChinese)){
            languageId = LanguageType.English.getLanguageId();
        }
        criteria.andLanguageIdEqualTo(languageId);
        List<ReportTemplateInfoPO> reportTemplateInfoPOs = reportTemplateInfoMapper.selectByExample(reportTemplateInfoExample);
        List<UpdateReportReq.BookMark> bookmarksList = new ArrayList<>();
        String approver = reportInfoPO.getApprover();
        if (Func.isNotEmpty(reportTemplateInfoPOs)) {
            String templateConfig = reportTemplateInfoPOs.get(0).getTemplateConfig();
            if (Func.isNotEmpty(templateConfig)) {
                List<DigitalReportConfigDTO> digitalReportConfigDTOList = JSONObject.parseArray(templateConfig, DigitalReportConfigDTO.class);
                if(Func.isNotEmpty(digitalReportConfigDTOList)){
                    //Final Word报告清空电子书签
                    if((ReportRequirementEnum.check(reportRequirement,ReportRequirementEnum.Sub_Report_Word,ReportRequirementEnum.Customer_Report_Word))){
                        for (DigitalReportConfigDTO digitalReportConfigDTO : digitalReportConfigDTOList) {
                            if(Constants.APPROVEDATE_BOOKMARK_LIST.stream().anyMatch(s -> s.equalsIgnoreCase(digitalReportConfigDTO.getFieldName()))){
                                UpdateReportReq.BookMark bookMark = Func.copy(digitalReportConfigDTO, UpdateReportReq.BookMark.class);
                                if(Constants.BOOKMARK.ACTUAL_COMPLETE_DATE.equalsIgnoreCase(digitalReportConfigDTO.getFieldName())){
                                    if ((updateActualCompleteDate)) {
                                        bookMark.setValue(DateUtil.formatDateTime(approveDate));
                                        bookmarksList.add(bookMark);
                                    }
                                }else{
                                    bookMark.setValue(DateUtil.formatDateTime(approveDate));
                                    bookmarksList.add(bookMark);
                                }
                            }else if (StringUtils.equalsIgnoreCase(buCode, ProductLineType.MR.getProductLineAbbr()) && Constants.BOOKMARK.CLOUDID.equalsIgnoreCase(Func.toStr(digitalReportConfigDTO.getFieldName()))){
                                UpdateReportReq.BookMark bookMark = Func.copy(digitalReportConfigDTO, UpdateReportReq.BookMark.class);
                                bookMark.setValue("");
                                bookmarksList.add(bookMark);
                            }else if(StringUtils.equalsIgnoreCase(buCode, ProductLineType.MR.getProductLineAbbr()) && Func.isNotEmpty(approver) && Constants.BOOKMARK.USER_NAME.equalsIgnoreCase(Func.toStr(digitalReportConfigDTO.getFieldName()))){
                                Map<String, Object> approvesMap = JSON.parseObject(approver);
                                List<UserSignatureDTO> userSignatureDTOList = null;
                                UpdateReportReq.BookMark bookMark = Func.copy(digitalReportConfigDTO, UpdateReportReq.BookMark.class);
                                if(approvesMap.containsKey(LanguageType.English.getCode()) && (SignatureLanguage.check(signatureLanguage,SignatureLanguage.English) ||  (SignatureLanguage.check(signatureLanguage,SignatureLanguage.Follow_Report) && LanguageType.check(languageId,LanguageType.English)))){
                                    //有英文签名图 ，且(signLanguage英文 或 follow Report Language(EN、Muli、En & CHI)  )
                                    userSignatureDTOList =JSON.parseArray(approvesMap.get(LanguageType.English.getCode()).toString(), UserSignatureDTO.class);
                                }else if(approvesMap.containsKey(LanguageType.Chinese.getCode()) && (SignatureLanguage.check(signatureLanguage,SignatureLanguage.Chinese) ||  (SignatureLanguage.check(signatureLanguage,SignatureLanguage.Follow_Report)  && LanguageType.check(languageId,LanguageType.Chinese)))){
                                    //有中文签名图 ，且(signLanguage中文 或 follow Report Language(CHI、En & CHI))
                                    userSignatureDTOList = JSON.parseArray(approvesMap.get(LanguageType.Chinese.getCode()).toString(), UserSignatureDTO.class);
                                }
                                if(Func.isNotEmpty(userSignatureDTOList)){
                                    bookMark.setValue(userSignatureDTOList.get(0).getSignatureName());
                                    bookmarksList.add(bookMark);
                                }
                            }
                        }
                    }else{
                        for (DigitalReportConfigDTO digitalReportConfigDTO : digitalReportConfigDTOList) {
                            if (Constants.ALL_BOOKMARK_LIST.stream().anyMatch(s -> s.equalsIgnoreCase(digitalReportConfigDTO.getFieldName()))) {
                                UpdateReportReq.BookMark bookMark = Func.copy(digitalReportConfigDTO, UpdateReportReq.BookMark.class);
                                if(Constants.APPROVEDATE_BOOKMARK_LIST.stream().anyMatch(s -> s.equalsIgnoreCase(digitalReportConfigDTO.getFieldName()))){
                                    if(Constants.BOOKMARK.ACTUAL_COMPLETE_DATE.equalsIgnoreCase(digitalReportConfigDTO.getFieldName())){
                                        if ((updateActualCompleteDate)) {
                                            bookMark.setValue(DateUtil.formatDateTime(approveDate));
                                            bookmarksList.add(bookMark);
                                        }
                                    }else{
                                        bookMark.setValue(DateUtil.formatDateTime(approveDate));
                                        bookmarksList.add(bookMark);
                                    }

                                } else if (StringUtils.equalsIgnoreCase(buCode, ProductLineType.MR.getProductLineAbbr()) && ReportRequirementEnum.check(reportRequirement,ReportRequirementEnum.Customer_Report_PDF) && Constants.SIGN_BOOKMARK_LIST.stream().anyMatch(s -> s.equalsIgnoreCase(digitalReportConfigDTO.getFieldName()))) {
                                    if(Func.isNotEmpty(approver)){
                                        Map<String, Object> approvesMap = JSON.parseObject(approver);
                                        List<UserSignatureDTO> userSignatureDTOList = null;
                                        if(approvesMap.containsKey(LanguageType.English.getCode()) && (SignatureLanguage.check(signatureLanguage,SignatureLanguage.English) ||  (SignatureLanguage.check(signatureLanguage,SignatureLanguage.Follow_Report) && LanguageType.check(languageId,LanguageType.English)))){
                                            //有英文签名图 ，且(signLanguage英文 或 follow Report Language(EN、Muli、En & CHI)  )
                                            userSignatureDTOList =JSON.parseArray(approvesMap.get(LanguageType.English.getCode()).toString(), UserSignatureDTO.class);
                                        }else if(approvesMap.containsKey(LanguageType.Chinese.getCode()) && (SignatureLanguage.check(signatureLanguage,SignatureLanguage.Chinese) ||  (SignatureLanguage.check(signatureLanguage,SignatureLanguage.Follow_Report)  && LanguageType.check(languageId,LanguageType.Chinese)))){
                                            //有中文签名图 ，且(signLanguage中文 或 follow Report Language(CHI、En & CHI))
                                            userSignatureDTOList = JSON.parseArray(approvesMap.get(LanguageType.Chinese.getCode()).toString(), UserSignatureDTO.class);
                                        }
                                        if(Func.isNotEmpty(userSignatureDTOList)){
                                            UserSignatureDTO userSignatureDTO = userSignatureDTOList.get(0);
                                            if(StringUtils.equalsIgnoreCase(digitalReportConfigDTO.getFieldName(),Constants.BOOKMARK.USER_NAME)){
                                                bookMark.setValue(userSignatureDTO.getSignatureName());
                                                bookmarksList.add(bookMark);
                                            }else if(StringUtils.equalsIgnoreCase(digitalReportConfigDTO.getFieldName(),Constants.BOOKMARK.CLOUDID)){
                                                bookMark.setValue(Constants.CLOUD_PREFIX + userSignatureDTO.getAutographId());
                                                bookmarksList.add(bookMark);
                                            }else if(StringUtils.equalsIgnoreCase(digitalReportConfigDTO.getFieldName(),Constants.BOOKMARK.TITLE)){
                                                bookMark.setValue(userSignatureDTO.getTitle());
                                                bookmarksList.add(bookMark);
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }

                }
            }
        }

        if (Func.isAnyNotEmpty(bookmarksList) && !blockSend) {
            UpdateReportReq updateReportReq = new UpdateReportReq();
            UpdateReportReq.UpdateReportFile updateReportFile = new UpdateReportReq.UpdateReportFile();
            updateReportFile.setReportFileId(reportNo);
            updateReportFile.setCloudId(cloudId);
            updateReportFile.setBookmarksList(bookmarksList);
            List<UpdateReportReq.UpdateReportFile> fileList = new ArrayList<>();
            fileList.add(updateReportFile);
            updateReportReq.setFileList(fileList);
            updateReportReq.setProductLineCode(buCode);
            BaseResponse<UpdateReportRes> baseResponse = digitalReportService.updateReport(updateReportReq);
            if (Func.isEmpty(baseResponse) || Func.isEmpty(baseResponse.getStatus())
                    || baseResponse.getStatus() != 200
                    || Func.isEmpty(baseResponse.getData())) {
                throw new BizException("更新文件失败:调用digital服务异常");
            }
            UpdateReportRes updateReportRes = baseResponse.getData();
            if (Func.isEmpty(updateReportRes.getResult()) || !NumberUtil.equals(updateReportRes.getResult().intValue(), 0)) {
                throw new BizException("更新文件失败:" + updateReportRes.getErrMsg());
            }
            if (Func.isEmpty(updateReportRes.getFileList())) {
                throw new BizException("更新文件失败:" + updateReportRes.getErrMsg());
            }
            UpdateReportRes.FileList updateFile = updateReportRes.getFileList().stream().filter(i -> Func.equals(i.getReportFileId(), reportNo)).findAny().orElse(null);
            if (Func.isNotEmpty(updateFile)) {
                if (Func.isEmpty(updateReportRes.getResult()) || !NumberUtil.equals(updateFile.getResult().intValue(), 0)) {
                    throw new BizException("更新文件失败:" + updateFile.getErrMsg());
                }
                if (Func.isNotEmpty(updateFile.getCloudId())) {
                    cloudId = updateFile.getCloudId();
                }
            }
            log.info("update Repoer res:{}", JSON.toJSONString(baseResponse));
        } else {
            log.info("{},没有书签Id配置", reportNo);
        }

        return cloudId;

    }


    private boolean checkAllReportFileApproved(String reportId) {
        //查询报告文件数量
//        List<ReportFilePO> reportFilePOS = getReportFilePOS(reportId);
        ReportFileExample objReportFileExample = new ReportFileExample();
        objReportFileExample.createCriteria().andReportIDEqualTo(reportId).andReportFileTypeEqualTo(ReportFileType.Word.getCode());
        List<ReportFilePO> reportFilePOS = reportFileMapper.selectByExample(objReportFileExample);
        //过滤出已审批的报告文件
        long approvedReportFileCount = reportFilePOS.stream().filter(reportFilePO -> Func.equals(ReportFileStatus.APPROVED.getCode(), reportFilePO.getStatus())).count();
        return approvedReportFileCount == reportFilePOS.size();
    }

    private void updateGpoReportInfo(SaveReportReq saveReportReq) {
        UserInfo userInfo = tokenClient.getUser();
        saveReportReq.setProductLineCode(ProductLineContextHolder.getProductLineCode());
        saveReportReq.setModifiedDate(new Date());
        if (Func.isNotEmpty(userInfo)) {
            saveReportReq.setModifiedBy(userInfo.getRegionAccount());
        }
        gpoOrderReportFacade.updateReport(saveReportReq);
    }

    /**
     * 判断是否需要draft报告
     *
     * @param orderNo
     * @return
     */
    protected boolean isNeedDraft(String orderNo) {
        boolean needDraft = false;
        OrderIdReq orderIdReq = new OrderIdReq();
        orderIdReq.setOrderNo(orderNo);
        orderIdReq.setProductLineCode(ProductLineContextHolder.getProductLineCode());
        BaseResponse<TestRequestInfo> baseResponse = gpoOrderFacade.queryRestRequestForPe(orderIdReq);
        if (ResponseCode.SUCCESS.getCode() == baseResponse.getStatus()) {
            TestRequestInfo testRequestInfo = baseResponse.getData();
            if (!ObjectUtils.isEmpty(testRequestInfo) && !ObjectUtils.isEmpty(testRequestInfo.getDraftReportRequired())) {
                needDraft = (testRequestInfo.getDraftReportRequired().intValue() == 1);
            }
        }
        return needDraft;
    }


    private void doSendMessageOnApprove(ReportInfoPO report, UserInfo user) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

        // 获取overallconclusion
        List<ConclusionListInfo> conclusionListPos = conclusionListExtMapper.queryAllConclusionDictionaryList();
        //List<ConclusionListPO> conclusionListPos = conclusionListDAO.queryAllConclusionDictionaryList();
        List<ConclusionInfoPO> overAllConclusion = conclusionService.getConclusionByConclusionType(report.getID(), 604);
        //List<ConclusionPO> overAllConclusion = conclusionDAO.getConclusionByConclusionType(report.getId(), 603);

        String overAllConclusionId = "";
        String conclusion = "";
        if (overAllConclusion != null && overAllConclusion.size() > 0) {
            overAllConclusionId = overAllConclusion.get(0).getConclusionID();

            for (ConclusionListInfo conclusionList : conclusionListPos) {
                if (conclusionList.getID().equals(overAllConclusionId)) {
                    conclusion = conclusionList.getDescription();
                    break;
                }
            }
        }

        ReportApproveMessage msg = new ReportApproveMessage();
        msg.setApprovedBy(user.getName());
        msg.setOrderNo(report.getOrderNo());
        msg.setReportNo(report.getReportNo());
        msg.setReportApprovedDate(report.getApproverDate() == null ? null : sdf.format(report.getApproverDate()));
        msg.setOverAllConclusion(conclusion);

        SyncStatusMessage message = new SyncStatusMessage();
        message.setReportApproveMessage(msg);

        GeneralMessage<SyncStatusMessage> generalMessage = new GeneralMessage<>();
        generalMessage.setActionType("reportApprove");
        generalMessage.setProductLineCode(ProductLineContextHolder.getProductLineCode());
        generalMessage.setUserName(user.getName());
        generalMessage.setData(message);

        producer.doSend(KafkaTopicConsts.TOPIC_OTSNOTES_SYNCSTATUS, generalMessage);
    }


    private void insertCpTask(ReportInfoPO reportInfo) {
        DeliveryLogReq deliveryLogReq = new DeliveryLogReq();
        deliveryLogReq.setBuCode(ProductLineContextHolder.getProductLineCode());
        deliveryLogReq.setObjectType(Constants.DELIVERY_LOG.REPORT_PLAY_LOAD);
        deliveryLogReq.setAction(DeliveryAction.SendPlayLoadToCp.getStatus());
        deliveryLogReq.setObjectNo(reportInfo.getReportNo());
        Map<String, String> data = new HashMap<>();
        data.put("orderNo", reportInfo.getOrderNo());
        data.put("reportNo", reportInfo.getReportNo());
        deliveryLogReq.setData(JSON.toJSONString(data));
        UserInfo userInfo = tokenClient.getUser();
        if (Func.isNotEmpty(userInfo)) {
            deliveryLogReq.setCreatedBy(userInfo.getRegionAccount());
        }
        deliveryLogFacade.add(deliveryLogReq);
    }

    /**
     * approve接口发送邮件
     *
     * @param reportInfoPO
     * @param reportType
     */
    private String approveSendEmail(ReportInfoPO reportInfoPO, Integer reportType) {
        String message = null;

        //-----准备数据
        OrderAllDTO orderAllDTO = this.getOrderForPe(reportInfoPO.getOrderNo());
        //判断是否为IDB报告
        if (!Func.equals(CaseType.IDB.getStatus(), orderAllDTO.getCaseType())) {
            return null;
        }
        boolean needReview = ReportWorkFlow.check(reportInfoPO.getWorkFlow(), ReportWorkFlow.HostReview);
        //需要send draft PDF
        OrderIdReq orderIdReq = new OrderIdReq();
        orderIdReq.setOrderNo(reportInfoPO.getOrderNo());
        orderIdReq.setProductLineCode(ProductLineContextHolder.getProductLineCode());
        BaseResponse<TestRequestInfo> baseResponse = gpoOrderFacade.queryRestRequestForPe(orderIdReq);
        //判断Draft Report Required
        Boolean draftReportRequired = false;
        if (Func.isNotEmpty(baseResponse) && Func.isNotEmpty(baseResponse.getData())
                && Func.equals(1, baseResponse.getData().getDraftReportRequired())) {
            draftReportRequired = true;
        }
        UserInfo userInfo = tokenClient.getUser();
        String userName = null;
        if (Func.isNotEmpty(userInfo)) {
            userName = userInfo.getRegionAccount();
        }
        String labCode = tokenClient.getUserLabCode();

        //查询是否需要自动delivery soft copy
        Boolean autoSoftCopy = true;
        BuParamValueRsp buParamValueRsp = frameWorkClient.getBuParamValue(ProductLineContextHolder.getProductLineCode(),
                orderAllDTO.getLabDTO().getLabCode(), Constants.BU_PARAM.REPORT.GROUP, Constants.BU_PARAM.REPORT.AUTO_DELIVERY_SOFT_COPY.CODE);
        if (Func.isNotEmpty(buParamValueRsp) && Func.isNotEmpty(buParamValueRsp.getParamValue()) && Func.equals(buParamValueRsp.getParamValue(), Constants.BU_PARAM.REPORT.AUTO_DELIVERY_SOFT_COPY.VALUES.NO)) {
            autoSoftCopy = false;
        }
//        String onlineLabs = onlineConfig.getLabCodes();
//        log.info("onlineConfig: {}", JSON.toJSONString(onlineLabs));
//        String orderLab = orderAllDTO.getLabDTO().getLabCode();
//        if (Func.isNotEmpty(onlineLabs)) {
//            log.info("orderLab: {}", orderLab);
//            autoSoftCopy = !(Arrays.asList(onlineLabs.split(",")).contains(orderLab));
//            log.info("autoSoftCopy: {}", autoSoftCopy);
//        }
        //-----初始值
        List<ReportFilePO> draftPdfList = null;
        Boolean sendConfirmEmail = false;
        //定时任务执行softDeliveryCopy
        Boolean softDeliveryCopyTiming = false;
        //直接执行softDeliveryCopy
        Boolean softDeliveryCopyNow = false;
        Boolean sendDraftEmailTiming = false;
        Boolean sendDraftEmailNow = false;
        Boolean sendReviewEmail = false;


        //-----条件判断
        //需要draft 且 OperationType ！= 5
        if (draftReportRequired && !Func.equals(OperationType.SubContract.getStatus(), orderAllDTO.getOperationType())) {
            //操作approve  且 不需要review
            if (Func.equals(ReportFlagEnums.REPORT.getCode(), reportType) && !needReview) {
                sendConfirmEmail = true;
            } else if (Func.equals(ReportFlagEnums.SUB_REPORT.getCode(), reportType)) {//操作review时
                sendConfirmEmail = true;
            }
        } else if (draftReportRequired && Func.equals(OperationType.SubContract.getStatus(), orderAllDTO.getOperationType())) {//需要draft 且 OperationType == 5
            //review操作或者approve不需要review时 需要发送draft邮件
            if (Func.equals(ReportFlagEnums.SUB_REPORT.getCode(), reportType) || !needReview) {
                //校验最总报告是否存在
                //查询附件
                draftPdfList = reportFileService.getDraftReportFile(reportInfoPO.getID());

             /*   ReportFilePO reportFilePO = new ReportFilePO();
                reportFilePO.setReportID(reportInfoPO.getID());
                reportFilePO.setReportFileType(ReportFileType.DraftPDF.getCode());
                //查询
                draftPdfList= reportFileExtMapper.getReportFileInfoList(reportFilePO);*/
                //报告不为空直接发送,为空走定时任务发送
                if (Func.isNotEmpty(draftPdfList)) {
                    sendDraftEmailNow = true;
                } else {
                    sendDraftEmailTiming = true;
                }

            }
        } else if (!draftReportRequired) {//不需要draft
            //review操作或者approve不需要review时 需要执行softDeliveryCopy
            if (Func.equals(ReportFlagEnums.SUB_REPORT.getCode(), reportType)) {
                softDeliveryCopyNow = true;
            } else if (!needReview) {
                softDeliveryCopyTiming = true;
            }
        }
        //判断是否为approve操作
        if (Func.equals(ReportFlagEnums.REPORT.getCode(), reportType)) {
            sendReviewEmail = true;
        }


        //-----判断操作  只会有一个操作  GPO2-7324 -- 修改发送草稿确认邮件也要通过配置判断
        if (sendConfirmEmail && autoSoftCopy) {
            this.reportApproveSendEmail(reportInfoPO.getID());
        }

        if (softDeliveryCopyTiming && autoSoftCopy) {
            //保存任务
            DeliveryLogReq deliveryLogReq = new DeliveryLogReq();
            deliveryLogReq.setBuCode(ProductLineContextHolder.getProductLineCode());
            deliveryLogReq.setObjectType(Constants.DELIVERY_LOG.REPORT);
            deliveryLogReq.setAction(DeliveryAction.DeliverySoftCopy.getStatus());
            deliveryLogReq.setObjectNo(reportInfoPO.getID());
            Map<String, String> data = new HashMap<>();
            data.put("labCode", orderAllDTO.getLabDTO().getLabCode());
            deliveryLogReq.setData(JSON.toJSONString(data));
            deliveryLogReq.setCreatedBy(userName);
            deliveryLogFacade.add(deliveryLogReq);
        } else if (softDeliveryCopyNow && autoSoftCopy) {
            //执行softCopy
            message = iGpnReportService.deliverySoftCopy(reportInfoPO.getID(), orderAllDTO.getBUCode(), userName, labCode);
        }

        if (sendDraftEmailNow) {
            this.draftSendEmail(draftPdfList, reportInfoPO.getOrderNo(), reportInfoPO.getReportNo(), reportInfoPO.getID());
        } else if (sendDraftEmailTiming) {
            //保存定时任务
            DeliveryLogReq deliveryLogReq = new DeliveryLogReq();
            deliveryLogReq.setBuCode(ProductLineContextHolder.getProductLineCode());
            deliveryLogReq.setObjectType(Constants.DELIVERY_LOG.REPORT);
            deliveryLogReq.setAction(DeliveryAction.SendDraftEmail.getStatus());
            deliveryLogReq.setObjectNo(reportInfoPO.getID());
            Map<String, String> data = new HashMap<>();
            data.put("labCode", tokenClient.getUserLabCode());
            deliveryLogReq.setData(JSON.toJSONString(data));
            deliveryLogReq.setCreatedBy(userName);
            deliveryLogFacade.add(deliveryLogReq);
        }

        if (sendReviewEmail) {
            //发送Review邮件
            this.autoSendReviewEmail(reportInfoPO);
        }
        log.info("report操作approve/review操作===============>>>>>>>>>>>>>>>>>>");
        log.info("reportInfo:{}", reportInfoPO);
        log.info("sendConfirmEmail:{}", sendConfirmEmail);
        log.info("softDeliveryCopyTiming:{}", softDeliveryCopyTiming);
        log.info("softDeliveryCopyNow:{}", softDeliveryCopyNow);
        log.info("sendDraftEmailTiming:{}", sendDraftEmailTiming);
        log.info("sendDraftEmailNow:{}", sendDraftEmailNow);
        log.info("sendReviewEmail:{}", sendReviewEmail);
        log.info("draftReportRequired:{}", draftReportRequired);
        log.info("needReview:{}", needReview);
        log.info("caseType:{}", orderAllDTO.getCaseType());
        log.info("OperationType:{}", orderAllDTO.getOperationType());
        log.info("reportType:{}", reportType);
        log.info("autoSoftCopy:{}", autoSoftCopy);
        return message;
    }

    /**
     * 发送review邮件
     *
     * @param reportInfoPO
     * @param reportInfoPO
     */
    private void autoSendReviewEmail(ReportInfoPO reportInfoPO) {
        //判断是否需要review
        if (!ReportWorkFlow.check(reportInfoPO.getWorkFlow(), ReportWorkFlow.HostReview)) {
            return;
        }
        //查询订单信息，review、subcontractFrom
        OrderIdReq orderIdReq = new OrderIdReq();
        orderIdReq.setOrderNo(reportInfoPO.getOrderNo());
        BaseResponse<OrderAllDTO> orderAllDTOBaseResponse = gpoOrderFacade.getOrderForPe(orderIdReq);
        //判断订单信息是否为空
        if (Func.isEmpty(orderAllDTOBaseResponse) || Func.isEmpty(orderAllDTOBaseResponse.getData())) {
            return;
        }
        OrderAllDTO orderAllDTO = orderAllDTOBaseResponse.getData();
        //判断subReportReviewer是否为空
        if (Func.isEmpty(orderAllDTO.getSubReportReviewerId())) {
            return;
        }
        //查询subReportReviewer邮箱
        UserDTO userDTO = userManagementClient.getUserInfoById(orderAllDTO.getSubReportReviewerId());
        if (Func.isEmpty(userDTO) || Func.isEmpty(userDTO.getEmail())) {
            return;
        }
        String emailTo = userDTO.getEmail();
        String emailCc = orderAllDTO.getIdbLabEmail();
        String externalReportNo = orderClient.getExternalReportNo(reportInfoPO.getOrderNo(), reportInfoPO.getReportNo());
//        String subject = "Review报告-" + externalReportNo;
        String subject = "报告审核通知：" + externalReportNo;
        OrderIdReq orderIdReq1 = new OrderIdReq();
        orderIdReq1.setOrderNo(reportInfoPO.getOrderNo());
        orderIdReq1.setProductLineCode(ProductLineContextHolder.getProductLineCode());
        List<OrderReferenceNoRsp> orderReferenceNoRspList = gpoOrderFacade.getReferenceNoByOrderNo(orderIdReq1).getData();
        if (Func.isNotEmpty(orderReferenceNoRspList)) {
            OrderReferenceNoRsp orderReferenceNoRsp = orderReferenceNoRspList.get(0);
            if (Func.isNotEmpty(orderReferenceNoRsp.getSgsExternalReferenceNo())) {
                subject = subject + "-" + orderReferenceNoRsp.getSgsExternalReferenceNo();
            }
            if (Func.isNotEmpty(orderReferenceNoRsp.getCustomerReferenceNo())) {
                subject = subject + "-" + orderReferenceNoRsp.getCustomerReferenceNo();
            }
        }
        String templateCode = "GPO_ReportReview";
        //参数
        Map<String, Object> params = new HashMap<>();
        //base64加密
        String baseReport = Base64.getEncoder().encodeToString(reportInfoPO.getID().getBytes(StandardCharsets.UTF_8));
        params.put("approveUrl", interfaceConfig.getBaseUrl() + "/gpn-api/report/review/email/report/" + baseReport + "/" + ProductLineContextHolder.getProductLineCode() + "/" + emailTo + "/" + tokenClient.getUserLabCode());
        params.put("extranetApproveUrl", interfaceConfig.getExtranetUrl() + "/gpn-api/report/review/email/report/" + baseReport + "/" + ProductLineContextHolder.getProductLineCode() + "/" + emailTo + "/" + tokenClient.getUserLabCode());
        EmailAutoSendDTO emailAutoSendDTO = new EmailAutoSendDTO();
        emailAutoSendDTO.setTemplateVariables(params);
        emailAutoSendDTO.setMailSubject(subject);
        emailAutoSendDTO.setTemplateCode(templateCode);
        List<String> emailToList = new ArrayList<>();
        emailToList.add(emailTo);
        emailAutoSendDTO.setMailTo(emailToList);
        List<String> emailCcList = new ArrayList<>();
        if(Func.isNotEmpty(emailCc)){
            String[] emilCcs = emailCc.split(";");
            for (String email : emilCcs) {
                emailCcList.add(email);
            }
        }
        emailAutoSendDTO.setMailCc(emailCcList);
        //保存日志
        UserInfo user = tokenClient.getUser();
        String userName = "";
        if (Func.isEmpty(user)) {
            userName = user.getRegionAccount();
        } else {
            userName = "system";
        }
        SystemLog systemLog = new SystemLog();
        systemLog.setObjectType("autoSendReviewEmail");
        systemLog.setObjectNo(reportInfoPO.getReportNo());
        systemLog.setProductLineCode(orderAllDTO.getLabDTO().getBuCode());
        systemLog.setType(SystemLogType.API.getType());
        systemLog.setRemark("approve时发送Review邮件");
        systemLog.setCreateBy(userName);
        systemLog.setLocationCode(orderAllDTO.getLocationCode());
        systemLog.setProductLineCode(ProductLineContextHolder.getProductLineCode());
        systemLog.setOperationType("Send Review Email");
        emailAutoSendDTO.setTemplateType(EmailTemplateTypeEnums.REPORT.getCode());
        //log所需参数
        emailAutoSendDTO.setLabCode(orderAllDTO.getLabDTO().getLabCode());
        sendEmailService.sendEmail(emailAutoSendDTO, systemLog);

    }

    private void subReportDeliveryLogSave(String reportId, String userName) {
        //保存任务
        DeliveryLogReq deliveryLogReq = new DeliveryLogReq();
        deliveryLogReq.setBuCode(ProductLineContextHolder.getProductLineCode());
        deliveryLogReq.setObjectType(Constants.DELIVERY_LOG.REPORT);
        deliveryLogReq.setAction(DeliveryAction.SubReportDelivery.getStatus());
        deliveryLogReq.setObjectNo(reportId);
        deliveryLogReq.setCreatedBy(userName);
        deliveryLogFacade.add(deliveryLogReq);
    }


    /**
     * 发送draft邮件--线程内需要有buCode
     *
     * @param reportFilePOList
     * @param orderNo
     * @param reportNo
     */
    private void draftSendEmail(List<ReportFilePO> reportFilePOList, String orderNo, String reportNo, String reportId) {
//        String externalOrderNo = orderClient.getExternalOrderNo(orderNo);
        String externalReportNo = orderClient.getExternalReportNo(orderNo, reportNo);
        String subject = "SGS Draft Test Report" + externalReportNo;
        OrderIdReq refOrderIdReq = new OrderIdReq();
        refOrderIdReq.setOrderNo(orderNo);
        refOrderIdReq.setProductLineCode(ProductLineContextHolder.getProductLineCode());
        List<OrderReferenceNoRsp> orderReferenceNoRspList = gpoOrderFacade.getReferenceNoByOrderNo(refOrderIdReq).getData();
        if (Func.isNotEmpty(orderReferenceNoRspList)) {
            OrderReferenceNoRsp orderReferenceNoRsp = orderReferenceNoRspList.get(0);
            if (Func.isNotEmpty(orderReferenceNoRsp.getSgsExternalReferenceNo())) {
                subject = subject + "-" + orderReferenceNoRsp.getSgsExternalReferenceNo();
            }
            if (Func.isNotEmpty(orderReferenceNoRsp.getCustomerReferenceNo())) {
                subject = subject + "-" + orderReferenceNoRsp.getCustomerReferenceNo();
            }
        }

        String templateCode = "GPO_ReportDraft";
        List<String> mailTo = this.getSoftCopyDeliverToEmail(orderNo, ContactsType.SoftCopy.getType());
        List<String> mailCc = this.getSoftCopyDeliverToEmail(orderNo, ContactsType.SoftCopyDeliveryCc.getType());
        EmailAutoSendDTO emailAutoSendDTO = new EmailAutoSendDTO();
        emailAutoSendDTO.setMailSubject(subject);
        emailAutoSendDTO.setTemplateCode(templateCode);
        emailAutoSendDTO.setMailTo(mailTo);
        emailAutoSendDTO.setMailCc(mailCc);
        //附件
        //设置到邮件中的附件
        List<EmailAttachmentDTO> emailAttachmentDTOS = new ArrayList<>();
        EmailAttachmentDTO emailAttachmentDTO = null;
        for (ReportFilePO reportFile : reportFilePOList) {
            emailAttachmentDTO = new EmailAttachmentDTO(reportFile.getCloudID(), reportFile.getFilename(), null);
            emailAttachmentDTOS.add(emailAttachmentDTO);
        }
        emailAutoSendDTO.setOssFiles(emailAttachmentDTOS);
        UserInfo user = tokenClient.getUser();
        OrderIdReq orderIdReq = new OrderIdReq();
        orderIdReq.setOrderNo(orderNo);
        BaseResponse<OrderAllDTO> orderForPe = gpoOrderFacade.getOrderForPe(orderIdReq);
        String userName = "";
        if (Func.isNotEmpty(user)) {
            userName = user.getRegionAccount();
        } else {
            userName = "system";
        }
        //保存日志
        SystemLog systemLog = new SystemLog();
        systemLog.setObjectType("autoSendDraftEmail");
        systemLog.setObjectNo(reportNo);
        systemLog.setProductLineCode(ProductLineContextHolder.getProductLineCode());
        systemLog.setType(SystemLogType.API.getType());
        systemLog.setRemark("approve时发送Draft邮件");
        systemLog.setCreateBy(userName);
        if (Func.isNotEmpty(orderForPe)) {
            systemLog.setLocationCode(orderForPe.getData().getLocationCode());
            //log所需参数
            emailAutoSendDTO.setLabCode(orderForPe.getData().getLabDTO().getLabCode());
        }
        systemLog.setOperationType("Send Draft Email");
        emailAutoSendDTO.setTemplateType(EmailTemplateTypeEnums.DRAFT_REPORT.getCode());

        sendEmailService.sendEmail(emailAutoSendDTO, systemLog);

        // deliver history
        ReportDeliverHistoryInfoPO deliverHistoryInfoPO = new ReportDeliverHistoryInfoPO();
        deliverHistoryInfoPO.setReportId(reportId);
        deliverHistoryInfoPO.setDeliveryType(ReportType.DRAFT_REPORT.getMessage());
        deliverHistoryInfoPO.setSoftcopyDeliveredDate(new Date());
        deliverHistoryInfoPO.setSoftcopyDeliveredBy(userName);
        saveOrUpdateDeliverHistory(deliverHistoryInfoPO, userName);
//        orderForPe.getData().getOldOrderNo();
        if (Func.isNotEmpty(orderForPe)) {
            OrderAllDTO orderAllDTO = orderForPe.getData();
            BizLogInfo bizLog = new BizLogInfo();
            bizLog.setBizOpType(BizLogConstant.REPORT_OPERATION_HISTORY);
            bizLog.setOpUser(userName);
            bizLog.setBu(orderAllDTO.getLabDTO().getLabCode().split(" ")[1]);
            bizLog.setLab(orderAllDTO.getLabDTO().getLabCode().split(" ")[0]);
            bizLog.setBizId(orderAllDTO.getOrderNo());
            bizLog.setNewVal("[" + externalReportNo + "] 发送草稿确认邮件");
            bizLog.setOpType(Constants.BIZ_LOG.OP_TYPE.DELIVER_DRAFT);
            bizLogClient.doSend(bizLog);
            String oldOrderNo = orderAllDTO.getOldOrderNo();

            com.sgs.preorder.facade.model.req.OrderNosReq orderNosReq = new com.sgs.preorder.facade.model.req.OrderNosReq();
            orderNosReq.setOrderNos(Lists.newArrayList(orderNo));
            BaseResponse<List<OrderTrfDTO>> orderTrfRes = gpoOrderFacade.getOrderTrfByOrderNos(orderNosReq);

            if (Func.isNotEmpty(orderTrfRes) && Func.isNotEmpty(orderTrfRes.getData())) {
                OrderTrfDTO orderTrfDTO = orderTrfRes.getData().get(0);
                if(Func.isNotEmpty(orderTrfDTO) && Func.isNotEmpty(orderTrfDTO.getOrderTrfRelInfos())){
                    List<OrderTrfRelInfo> orderTrfRelInfos = orderTrfDTO.getOrderTrfRelInfos();
                    OrderTrfRelInfo orderTrfRelInfo = orderTrfRelInfos.stream().filter(i -> RefSystemIdEnum.check(i.getRefSystemId(), RefSystemIdEnum.SubContract)).findFirst().orElse(null);
                    if(Func.isNotEmpty(orderTrfRelInfo) && Func.isNotEmpty(orderTrfRelInfo.getRefNo())){
                        SubContractInfo subContractInfo = subContractExtMapper.getSubContractInfo(orderTrfRelInfo.getRefNo());
                        if (Func.isNotEmpty(subContractInfo)) {
                            String mainOrderNo = subContractInfo.getOrderNo();
                            if (Func.isNotEmpty(mainOrderNo)) {
                                oldOrderNo = mainOrderNo;
                            }
                        }
                    }
                }
            }
            if (Func.isNotEmpty(oldOrderNo) && (iGpnReportService.existsMatchTrfRelation(orderAllDTO.getOrderNo(),RefSystemIdEnum.SubContract, RefSystemIdEnum.ExternalSubContract) || OperationType.check(orderAllDTO.getOperationType(), OperationType.SubContract, OperationType.NewSubContract, OperationType.LightSubContract, OperationType.SubContractBind) && Func.equals(CaseType.IDB.getStatus(), orderAllDTO.getCaseType()))) {
                OrderIdReq oldOrderReq = new OrderIdReq();
                oldOrderReq.setOrderNo(oldOrderNo);
                BaseResponse<OrderInfoDto> oldOrderResp = gpoOrderFacade.getOrderInfoByOrderNo(oldOrderReq);
                OrderInfoDto oldOrderInfo = oldOrderResp.getData();
                //bizLog记录到分包方上，
                if (Func.isNotEmpty(oldOrderInfo)) {
                    bizLog.setBu(oldOrderInfo.getBUCode());
                    bizLog.setLab(oldOrderInfo.getLocationCode());
                    bizLog.setBizId(oldOrderNo);
                    bizLog.setNewVal("[" + externalReportNo + "] 发送草稿确认邮件");
                    bizLogClient.doSend(bizLog);
                }
            }
        }
    }

    /**
     * 查询订单信息
     *
     * @param orderNo
     * @return
     */
    private OrderAllDTO getOrderForPe(String orderNo) {
        OrderIdReq orderIdReq = new OrderIdReq();
        orderIdReq.setOrderNo(orderNo);
        orderIdReq.setProductLineCode(ProductLineContextHolder.getProductLineCode());
        BaseResponse<OrderAllDTO> baseResponse = gpoOrderFacade.getOrderForPe(orderIdReq);
        return baseResponse.getData();
    }


    /**
     * 获取订单deliveryTo/Cc邮箱
     *
     * @param orderNo
     * @param contactsType
     * @return
     */
    private List<String> getSoftCopyDeliverToEmail(String orderNo, Integer contactsType) {
        OrderIdReq req = new OrderIdReq();
        req.setOrderNo(orderNo);
        req.setProductLineCode(ProductLineContextHolder.getProductLineCode());
        BaseResponse<List<CustomerInstanceDTO>> baseResponse = gpoOrderFacade.queryCustomerForPe(req);
        OrderAllDTO orderAllDTO = gpoOrderFacade.getOrderForPe(req).getData();
        List<CustomerInstanceDTO> customerInstanceDTOS = baseResponse.getData();
        OrderIdReq objOrderIdReq = new OrderIdReq();
        objOrderIdReq.setOrderNo(orderAllDTO.getOrderNo());
        objOrderIdReq.setProductLineCode(ProductLineContextHolder.getProductLineCode());
        BaseResponse<TestRequestInfo> testRequestInfoBaseResponse = gpoOrderFacade.queryRestRequestForPe(objOrderIdReq);
        TestRequestInfo testRequestInfo = testRequestInfoBaseResponse.getData();
        List<TestRequestContactsInfo> serviceRequirementContractPOList = testRequestInfo.getTestRequestContactsInfos();
        // 获取 softcopy to
        TestRequestContactsInfo softcopyTo = serviceRequirementContractPOList.stream()
                .filter(srcpo -> Func.equals(srcpo.getContactsType(), contactsType)).findFirst().orElse(null);
        CustomerInstanceDTO applicant = customerInstanceDTOS.stream().filter(e -> e.getCustomerUsage().equals(CustomerUsage.Applicant.getCode())).findFirst().orElse(null);
        CustomerInstanceDTO payer = customerInstanceDTOS.stream().filter(e -> e.getCustomerUsage().equals(CustomerUsage.Payer.getCode())).findFirst().orElse(null);
        CustomerInstanceDTO buyer = customerInstanceDTOS.stream().filter(e -> e.getCustomerUsage().equals(CustomerUsage.Buyer.getCode())).findFirst().orElse(null);
        CustomerInstanceDTO subContract = customerInstanceDTOS.stream().filter(e -> e.getCustomerUsage().equals(CustomerUsage.SUBCONTRACTFROM.getCode())).findFirst().orElse(null);

        String softcopyEmail = initServiceReqEmail(orderAllDTO, softcopyTo, applicant, payer, buyer, subContract);
        if (Func.isNotEmpty(softcopyEmail)) {
            List<String> toEmailList = Arrays.asList(softcopyEmail.split(";"));
            return toEmailList;
        } else {
            return new ArrayList<>();
        }

    }

    private String initServiceReqEmail(OrderAllDTO orderAllDTO, TestRequestContactsInfo requirementContract, CustomerInstanceDTO applicant, CustomerInstanceDTO payer, CustomerInstanceDTO buyer, CustomerInstanceDTO subContract) {
        String eamilAddress = null;
        if (!ObjectUtils.isEmpty(requirementContract)) {
            // delever_to：delever_to： 1: Applicant 2: Payer 3: Buyer 4: Agent
            // 5:Others 6:Subcontract From 7:CS 8: Sales
            List<String> emailList = new ArrayList<>();
            if (StringUtils.isNotEmpty(requirementContract.getDeliverTo())) {
                String[] deliverTo = requirementContract.getDeliverTo().split(",");
                for (String deliver : deliverTo) {
                    if (deliver.equals(DeliverToType.APPLICANT.getCode()) && applicant != null) {
                        emailList.addAll(this.parseEmail(applicant.getContactPersonEmail()));
//                        emailList.add(applicant.getContactPersonEmail());
                    } else if (deliver.equals(DeliverToType.PAYER.getCode()) && payer != null) {
                        emailList.addAll(this.parseEmail(payer.getContactPersonEmail()));
                    } else if (deliver.equals(DeliverToType.BUYER.getCode()) && buyer != null) {
                        emailList.addAll(this.parseEmail(buyer.getContactPersonEmail()));
                    } else if (deliver.equals(DeliverToType.SUBCONTRACT_FROM.getCode()) && subContract != null) {
                        emailList.addAll(this.parseEmail(subContract.getContactPersonEmail()));
                    } else if (deliver.equals(DeliverToType.CS.getCode())) {
                        emailList.addAll(this.parseEmail(orderAllDTO.getcSEmail()));
                    } else if (deliver.equals(DeliverToType.SALES.getCode()) && Func.isNotEmpty(orderAllDTO.getSalesPerson())) {
                        String salesEmail = this.getEmailByRegionAccount(orderAllDTO.getSalesPerson());
                        if (Func.isNotEmpty(salesEmail)) {
                            emailList.add(salesEmail);
                        }
                    }
                }
            }

            if (StringUtils.isNotEmpty(requirementContract.getDeliverOthers())) {
                // deliver others
                emailList.add(requirementContract.getDeliverOthers());
            }

            eamilAddress = emailList.stream().distinct().filter(email -> StringUtils.isNotEmpty(email))
                    .collect(Collectors.joining(";"));
        }

        return eamilAddress;
    }

    /**
     * 解析邮箱
     *
     * @param email
     * @return
     */
    private List<String> parseEmail(String email) {
        List<String> emailList = new ArrayList<>();
        if (Func.isEmpty(email)) {
            return emailList;
        }
        String[] emails = email.split(";");
        for (String emailText : emails) {
            emailList.add(emailText);
        }
        return emailList;
    }

    /**
     * 根据用户名获取邮箱
     *
     * @param regionAccount
     * @return
     */
    private String getEmailByRegionAccount(String regionAccount) {
        String email = null;
        Map<String, Object> params = Maps.newHashMap();
        params.put("page", 1);
        params.put("rows", 10);
        params.put("regionAccount", regionAccount);
        params.put("accurateFlag",1);
        String userJson = frameWorkClient.queryUserInfoList(params);
        log.info("queryUserInfoList:{}", userJson);
        if (Func.isNotEmpty(userJson)) {
            JSONObject jsonObject = JSON.parseObject(userJson);
            JSONArray jsonArray = JSONArray.parseArray(jsonObject.get("list").toString());
            if (Func.isNotEmpty(jsonArray)) {
                JSONObject json = jsonArray.getJSONObject(0);
                email = json.getString("email");
            }
        }
        return email;
    }


    private void saveOrUpdateDeliverHistory(ReportDeliverHistoryInfoPO deliverHistoryInfoPO, String userName) {
        if (Func.isEmpty(userName)) {
            UserInfo userInfo = tokenClient.getUser();
            if (Func.isNotEmpty(userInfo)) {
                userName = userInfo.getRegionAccount();
            }
        }

        if (Func.isEmpty(deliverHistoryInfoPO) || Func.isEmpty(deliverHistoryInfoPO.getDeliveryType()) || Func.isEmpty(deliverHistoryInfoPO.getReportId())) {
            return;
        }

        deliverHistoryInfoPO.setModifiedBy(userName);
        deliverHistoryInfoPO.setModifiedDate(new Date());
        ReportDeliverHistoryInfoExample example = new ReportDeliverHistoryInfoExample();
        example.createCriteria().andDeliveryTypeEqualTo(deliverHistoryInfoPO.getDeliveryType()).
                andReportIdEqualTo(deliverHistoryInfoPO.getReportId()).andActiveIndicatorEqualTo(true);
        List<ReportDeliverHistoryInfoPO> reportDeliverHistoryInfoPOS = deliverHistoryInfoMapper.selectByExample(example);
        if (CollectionUtil.isEmpty(reportDeliverHistoryInfoPOS)) {
            deliverHistoryInfoPO.setId(Func.randomUUID());
            deliverHistoryInfoPO.setActiveIndicator(true);
            deliverHistoryInfoPO.setCreatedDate(new Date());
            deliverHistoryInfoPO.setCreatedBy(userName);
            deliverHistoryInfoPO.setModifiedDate(new Date());
            deliverHistoryInfoPO.setModifiedBy(userName);
            deliverHistoryInfoPO.setModifiedDate(new Date());
            deliverHistoryInfoMapper.insertSelective(deliverHistoryInfoPO);
        } else {
            deliverHistoryInfoPO.setId(reportDeliverHistoryInfoPOS.get(0).getId());
            deliverHistoryInfoMapper.updateByPrimaryKeySelective(deliverHistoryInfoPO);
        }
    }

    /**
     * 保存confirm邮件任务
     *
     * @param reportId
     */
    private void reportApproveSendEmail(String reportId) {
        //保存任务
        DeliveryLogReq deliveryLogReq = new DeliveryLogReq();
        deliveryLogReq.setBuCode(ProductLineContextHolder.getProductLineCode());
        deliveryLogReq.setObjectType(Constants.DELIVERY_LOG.REPORT);
        deliveryLogReq.setAction(DeliveryAction.ConfirmEmail.getStatus());
        deliveryLogReq.setObjectNo(reportId);
        UserInfo userInfo = tokenClient.getUser();
        if (Func.isNotEmpty(userInfo)) {
            deliveryLogReq.setCreatedBy(userInfo.getRegionAccount());
        }
        deliveryLogFacade.add(deliveryLogReq);
    }

}
