package com.sgs.otsnotes.domain.service.digitalReport;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.text.StrPool;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.beust.jcommander.internal.Maps;
import com.beust.jcommander.internal.Sets;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.core.base.CustomResult;
import com.sgs.framework.model.common.attachment.AttachmentBO;
import com.sgs.framework.model.common.contact.ContactPersonBO;
import com.sgs.framework.model.common.customer.CustomerBO;
import com.sgs.framework.model.common.customer.CustomerContactBO;
import com.sgs.framework.model.common.customer.CustomerLanguageBO;
import com.sgs.framework.model.common.dff.DFFAttrBO;
import com.sgs.framework.model.common.dff.DFFAttrLanguageBO;
import com.sgs.framework.model.common.productsample.ProductBO;
import com.sgs.framework.model.common.productsample.SampleBO;
import com.sgs.framework.model.common.servicerequirement.ReportLanguageBO;
import com.sgs.framework.model.common.servicerequirement.ServiceRequirementBO;
import com.sgs.framework.model.common.servicerequirement.ServiceRequirementReportBO;
import com.sgs.framework.model.common.signatures.SignaturesBO;
import com.sgs.framework.model.enums.DataEntryMode;
import com.sgs.framework.model.enums.LanguageType;
import com.sgs.framework.model.enums.ReportLanguage;
import com.sgs.framework.model.enums.SgsSystem;
import com.sgs.framework.model.order.order.*;
import com.sgs.framework.model.order.trf.TrfBO;
import com.sgs.framework.model.report.DigitalReportBO;
import com.sgs.framework.model.report.report.ReportHeaderBO;
import com.sgs.framework.model.report.report.v2.ReportCertificateBO;
import com.sgs.framework.model.report.subreport.SubReportBO;
import com.sgs.framework.model.report.template.AccreditationBO;
import com.sgs.framework.model.report.template.ReportTemplateBO;
import com.sgs.framework.model.test.conclusion.ConclusionBO;
import com.sgs.framework.model.test.conclusion.v2.ConclusionExtBO;
import com.sgs.framework.tool.utils.Func;
import com.sgs.framework.tool.utils.ProductLineContextHolder;
import com.sgs.otsnotes.core.constants.Constants;
import com.sgs.otsnotes.core.enums.ReportFlagEnums;
import com.sgs.otsnotes.core.util.DateFormatUtil;
import com.sgs.otsnotes.core.util.NumberUtil;
import com.sgs.otsnotes.dbstorages.mybatis.enums.CustomerUsage;
import com.sgs.otsnotes.dbstorages.mybatis.extmapper.*;
import com.sgs.otsnotes.dbstorages.mybatis.extmodel.ConclusionListInfo;
import com.sgs.otsnotes.dbstorages.mybatis.extmodel.masterlist.*;
import com.sgs.otsnotes.dbstorages.mybatis.mapper.ReportTypeMapper;
import com.sgs.otsnotes.dbstorages.mybatis.model.*;
import com.sgs.otsnotes.domain.service.MasterService;
import com.sgs.otsnotes.domain.service.digitalReport.dto.JobDTO;
import com.sgs.otsnotes.domain.service.digitalReport.dto.SubcontractDTO;
import com.sgs.otsnotes.domain.service.digitalReport.dto.*;
import com.sgs.otsnotes.domain.service.digitalReport.dto.v2.DigitalReportDataSourceDTO2;
import com.sgs.otsnotes.domain.service.digitalReport.dto.v2.SampleAttrDTO;
import com.sgs.otsnotes.domain.service.digitalReport.dto.v2.SampleDTO;
import com.sgs.otsnotes.domain.service.digitalReport.enums.TestResultItemType;
import com.sgs.otsnotes.domain.service.digitalReport.request.DigitalReportReq;
import com.sgs.otsnotes.domain.service.digitalReport.request.GenerateReportRequest;
import com.sgs.otsnotes.domain.service.digitalReport.request.VariableDataList;
import com.sgs.otsnotes.domain.service.digitalReport.request.v2.BuildDigitalReportRequestReq;
import com.sgs.otsnotes.domain.service.digitalReport.request.v2.DigitalReportReq2;
import com.sgs.otsnotes.domain.service.digitalReport.request.v2.VariableDataList2;
import com.sgs.otsnotes.domain.service.gpn.job.IJobService;
import com.sgs.otsnotes.domain.service.gpn.subcontract.ISubContractService;
import com.sgs.otsnotes.domain.service.gpn.testline.ITestLineService;
import com.sgs.otsnotes.domain.service.productlineservice.ProductLineServiceHolder;
import com.sgs.otsnotes.facade.model.common.ResponseCode;
import com.sgs.otsnotes.facade.model.dto.ReportDTO;
import com.sgs.otsnotes.facade.model.dto.*;
import com.sgs.otsnotes.facade.model.dto.dataEntry.*;
import com.sgs.otsnotes.facade.model.dto.report.ReportSubReportDTO;
import com.sgs.otsnotes.facade.model.enums.ObjectType;
import com.sgs.otsnotes.facade.model.enums.ReportStatus;
import com.sgs.otsnotes.facade.model.enums.*;
import com.sgs.otsnotes.facade.model.gpn.citation.info.CitationInfo;
import com.sgs.otsnotes.facade.model.gpn.citation.info.CitationLanguageInfo;
import com.sgs.otsnotes.facade.model.gpn.subreport.info.SubReportInfo;
import com.sgs.otsnotes.facade.model.gpn.subreport.req.SubReportReq;
import com.sgs.otsnotes.facade.model.gpn.testline.info.TLInstanceDetailInfo;
import com.sgs.otsnotes.facade.model.gpn.testline.info.TestLineNameInfo;
import com.sgs.otsnotes.facade.model.gpn.testline.info.TestLineNameLanguageInfo;
import com.sgs.otsnotes.facade.model.gpn.testline.req.TLInstanceDetailReq;
import com.sgs.otsnotes.facade.model.info.matrix.PrintTestMatrixInfo;
import com.sgs.otsnotes.facade.model.info.subcontract.SubContractInfo;
import com.sgs.otsnotes.facade.model.info.subcontract.SubcontractRequirementInfo;
import com.sgs.otsnotes.facade.model.req.ConclusionReq;
import com.sgs.otsnotes.facade.model.req.DataEntryListReq;
import com.sgs.otsnotes.facade.model.req.GeneralReportDataReq;
import com.sgs.otsnotes.facade.model.req.GeneralReportMatrixDataReq;
import com.sgs.otsnotes.facade.model.req.gpn.GpnQuerySubContractReq;
import com.sgs.otsnotes.facade.model.req.gpn.JobListReq;
import com.sgs.otsnotes.facade.model.req.master.MasterListReq;
import com.sgs.otsnotes.facade.model.req.report.UserSignatureQueryRequest;
import com.sgs.otsnotes.facade.model.req.starlims.SubContractContactInfo;
import com.sgs.otsnotes.facade.model.rsp.AccreditationRsp;
import com.sgs.otsnotes.facade.model.rsp.AccreditationVO;
import com.sgs.otsnotes.facade.model.rsp.LabClaimer;
import com.sgs.otsnotes.facade.model.rsp.SodaConclusionRsp;
import com.sgs.otsnotes.facade.model.rsp.dataentry.form.AnalyteInstanceStatusEnum;
import com.sgs.otsnotes.facade.model.rsp.gpn.GpnSubContractRsp;
import com.sgs.otsnotes.facade.model.rsp.gpn.JobListRsp;
import com.sgs.otsnotes.integration.*;
import com.sgs.otsnotes.integration.quotation.QuotationClient;
import com.sgs.preorder.facade.DffFacade;
import com.sgs.preorder.facade.OrderFacade;
import com.sgs.preorder.facade.OrderReportFacade;
import com.sgs.preorder.facade.ToDoListFacade;
import com.sgs.preorder.facade.model.dto.customer.CustomerInstanceDTO;
import com.sgs.preorder.facade.model.dto.dff.DffFormAttrDTO;
import com.sgs.preorder.facade.model.dto.dff.TestrptCoverpageDffSimpleDTO;
import com.sgs.preorder.facade.model.dto.order.*;
import com.sgs.preorder.facade.model.enums.*;
import com.sgs.preorder.facade.model.info.ProductInfo;
import com.sgs.preorder.facade.model.info.TestRequestContactsInfo;
import com.sgs.preorder.facade.model.info.TestRequestInfo;
import com.sgs.preorder.facade.model.req.*;
import com.sgs.preorder.facade.model.req.todolist.GpoParcelTodoListDTO;
import com.sgs.preorder.facade.model.rsp.OrderAttachmentRsp;
import com.sgs.preorder.facade.model.rsp.ReportReceiverRsp;
import com.sgs.preorder.facade.model.rsp.TestRequestRsp;
import com.sgs.preorder.facade.model.rsp.customer.CustomerSimplifyInfoRsp;
import com.sgs.preorder.facade.model.rsp.order.OrderReferenceNoRsp;
import com.sgs.preorder.facade.model.rsp.parcel.ParcelTodoListRsp;
import com.sgs.priceengine.facade.QuotationFacade;
import com.sgs.priceengine.facade.model.DTO.QuotationServiceItemDTO;
import com.sgs.priceengine.facade.model.enums.ReportLanguageType;
import com.sgs.priceengine.facade.model.request.QueryQuotationHisRequest;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

import static java.lang.Math.floor;
import static java.lang.Math.log;


@Service
public class DigitalReportMappingService {
    private final static Logger logger = LoggerFactory.getLogger(DigitalReportService.class);
    @Autowired
    private ReportMapper reportMapper;
    @Autowired
    private OrderFacade orderFacade;
    @Autowired
    private OrderClient orderClient;
    @Autowired
    private OrderReportFacade orderReportFacade;
    @Autowired
    private ITestLineService testLineService;
    @Autowired
    private DffFacade dffFacade;
    @Autowired
    private TestSampleExtMapper testSampleExtMapper;
    @Autowired
    private ReportFileExtMapper reportFileExtMapper;
    @Autowired
    private SubReportExtMapper subReportMapper;
    @Autowired
    private QuotationClient quotationClient;
    @Autowired
    private FrameWorkClient frameWorkClient;
    @Autowired
    private OrderMapper orderMapper;
    @Autowired
    private TestMatrixMapper testMatrixMapper;
    @Autowired
    private ReportTypeMapper reportTypeMapper;
    @Autowired
    private MasterService masterService;
    @Autowired
    private SubContractExtMapper subContractExtMapper;
    @Autowired
    private SubContractRequirementContactsExtMapper subContractRequirementContactsExtMapper;
    @Autowired
    private CustomerClient customerClient;
    @Autowired
    private QuotationFacade quotationFacade;
    @Autowired
    private ReportReworkApproverRecordsInfoExtMapper reportReworkApproverRecordsInfoExtMapper;
    @Autowired
    private ToDoListFacade toDoListFacade;
    @Autowired
    private DataEntryClient dataEntryClient;
    @Autowired
    private ReportMatrixRelMapper reportMatrixRelMapper;
    @Autowired
    private TokenClient tokenClient;
    @Autowired
    private DffClient dffClient;
    @Autowired
    private ConclusionListExtMapper conclusionListExtMapper;


    public OrderAllDTO getOrder(String orderId,String buCode)
    {
        //合并导出的时候，这个order 只获取order相同的属性
        OrderIdReq orderIdReq = new OrderIdReq();
        orderIdReq.setOrderId(orderId);
        orderIdReq.setProductLineCode(buCode);
        return  orderFacade.getOrderForPe(orderIdReq).getData();
    }

    //查询客户列表
    private List<CustomerInstanceDTO> getCustomerList(OrderIdReq req){
        return  orderFacade.queryCustomerForPe(req).getData();
    }

    public BaseResponse<DigitalReportDataSourceDTO> buildDigitalReportDataSource(GenerateReportRequest reportRequest, DigitalReportInfoDTO infoData) {
        DigitalReportDataSourceDTO digitalReportInfo=new DigitalReportDataSourceDTO();
        digitalReportInfo.setVariableDataList(new VariableDataList());
        digitalReportInfo.setProductLineCode(reportRequest.getProductLineCode());
        try{
            // 存储拼接完成的业务数据对象
            DigitalReportReq tsMappingResultData = new DigitalReportReq();
            // report 信息查询
            initReport(digitalReportInfo,reportRequest);
            // 查询客户列表
            initCustomerInfo(digitalReportInfo,reportRequest);
            // 查询dff相关的信息
            initDffInfo(digitalReportInfo,reportRequest);
            // TODO 拼装CareLabel
            // signatures 信息查询
            initSignature(digitalReportInfo,reportRequest);
            // samples信息需要调用接口查询
            initSamples(digitalReportInfo,reportRequest);
            if(Func.equals(ReportActionType.GENERATE_PRELIM_RESULT.getCode(),reportRequest.getReportActionType())){
                // Service Item
                initServiceItem(digitalReportInfo,reportRequest);
            }else{
                // test line 信息
                initTestResult(digitalReportInfo,reportRequest);
            }
            // TODO Attachment 处理
            // Conclusion
            initConclusionList(digitalReportInfo,reportRequest);
            initExtConclusionSummary(digitalReportInfo,reportRequest);
            // extConclusionSummary
            // subcontractReport 信息tb_report_files
            tsMappingResultData.setDatasource(digitalReportInfo);
            tsMappingResultData.setInfo(infoData);
        }catch (Exception e){
            logger.error(e.getMessage(),e);
            logger.info("==========buildDigitalReportDataSoucre->errorMessage：{}============", e.getMessage());
            e.printStackTrace();
            return BaseResponse.newFailInstance("to digital report construct convert error");
        }
        return  BaseResponse.newSuccessInstance(digitalReportInfo);
    }


    private void initConclusionList(DigitalReportDataSourceDTO digitalReportInfo, GenerateReportRequest reportRequest){
        // 基于报告查询Conclusion集合
        ConclusionReq conclusionReq = new ConclusionReq();
        conclusionReq.setReportId(reportRequest.getReportId());
        conclusionReq.setProductLineCode(reportRequest.getProductLineCode());
        BaseResponse<List<SodaConclusionRsp>> conclusionRes = dataEntryClient.getConclusionList(conclusionReq);
        if(conclusionRes.isSuccess() && Func.isNotEmpty(conclusionRes.getData())){
            digitalReportInfo.getVariableDataList().setConclusionList(conclusionRes.getData());
        }
    }


    private void initExtConclusionSummary(DigitalReportDataSourceDTO digitalReportInfo, GenerateReportRequest reportRequest){
        // 基于报告查询Conclusion集合
        ConclusionReq conclusionReq = new ConclusionReq();
        conclusionReq.setReportId(reportRequest.getReportId());
        conclusionReq.setProductLineCode(reportRequest.getProductLineCode());
        BaseResponse<List<ConclusionExtBO>> conclusionRes = dataEntryClient.getConclusionExtList(conclusionReq);
        if(conclusionRes.isSuccess() && Func.isNotEmpty(conclusionRes.getData())){
            digitalReportInfo.getVariableDataList().setExtConclusionSummaryList(conclusionRes.getData());
        }
    }

    public BaseResponse<DigitalReportDataSourceDTO> buildDigitalReportDataSourceExt(GenerateReportRequest reportRequest) {
        DigitalReportDataSourceDTO digitalReportInfo=new DigitalReportDataSourceDTO();
        digitalReportInfo.setVariableDataList(new VariableDataList());
        digitalReportInfo.setProductLineCode(reportRequest.getProductLineCode());
        try{
            // 调用Soda查询TL相关的信息
            assembleTestLineFromSoda(digitalReportInfo,reportRequest);
            // header
            assembleHeader(digitalReportInfo,reportRequest);
            // order
            assembleOrder(digitalReportInfo,reportRequest);
            // trfList
            assembleTrfList(digitalReportInfo,reportRequest);
            // subReportList
            assembleSubReport(digitalReportInfo,reportRequest);
            // reportFileList
            assembleReportFile(digitalReportInfo,reportRequest);
            // reportTemplate
            assembleReportTemplate(digitalReportInfo,reportRequest);
            // conclusion
            initConclusionList(digitalReportInfo,reportRequest);
            initExtConclusionSummary(digitalReportInfo,reportRequest);
        }catch (Exception e){
            logger.error(e.getMessage(),e);
            logger.info("==========buildDigitalReportDataSoucre->errorMessage：{}============", e.getMessage());
            e.printStackTrace();
            return BaseResponse.newFailInstance("to digital report construct convert error");
        }
        return  BaseResponse.newSuccessInstance(digitalReportInfo);
    }

    private void assembleSubReport(DigitalReportDataSourceDTO digitalReportInfo, GenerateReportRequest reportRequest){
        // 查询SubReport信息
        SubReportReq subReportReq = new SubReportReq();
        subReportReq.setReportNo(reportRequest.getReportNo());
        if(ReportLanguage.checkLanguage(reportRequest.getOrderReportLanguage(),ReportLanguage.MultilingualReport)){
            subReportReq.setLanguageId(LanguageType.EnglishAndChinese.getLanguageId());
        }else{
            subReportReq.setLanguageId(reportRequest.getLanguageId());
        }
        subReportReq.setActiveStatus(true);
        List<SubReportInfo> subReportInfos = subReportMapper.getSubReports(subReportReq);
        List<SubReportInfo> sortSubReportInfos = new ArrayList<>();
        // 优先取Report直接关联的SubReport
        if (Func.isNotEmpty(subReportInfos)) {
            //排序 JobReport在前面,分包出去的在后面
            List<SubReportInfo> jobReportList = subReportInfos.stream().filter(i -> SubReportObjectTypeEnums.check(i.getObjectType(), SubReportObjectTypeEnums.job)).collect(Collectors.toList());
            List<SubReportInfo> subReportInfoList = subReportInfos.stream().filter(i ->
                    !SubReportObjectTypeEnums.check(i.getObjectType(), SubReportObjectTypeEnums.job,SubReportObjectTypeEnums.subcontract)
                            && !ObjectType.check(i.getObjectType(),ObjectType.SubContract)).collect(Collectors.toList());
            if(Func.isNotEmpty(jobReportList)){
                jobReportList = jobReportList.stream().sorted(Comparator.comparing(SubReportInfo::getObjectNo).thenComparing(SubReportInfo::getSubReportNo)  )
                        .collect(Collectors.toList());
                sortSubReportInfos.addAll(jobReportList);
            }
            if(Func.isNotEmpty(subReportInfoList)){
                sortSubReportInfos.addAll(subReportInfoList);
            }
            //处理分包单返回的
            List<SubReportInfo> subcontractReportList = subReportInfos.stream().filter(i -> SubReportObjectTypeEnums.check(i.getObjectType(), SubReportObjectTypeEnums.subcontract) || ObjectType.check(i.getObjectType(), ObjectType.SubContract)).collect(Collectors.toList());
            if (Func.isNotEmpty(subcontractReportList)){
                Map<String,List<SubReportInfo>> groupBySubReport = subcontractReportList.stream().collect(Collectors.groupingBy(SubReportInfo :: getSubReportNo));
                for (String subReport : groupBySubReport.keySet()){
                    List<SubReportInfo> subReports = groupBySubReport.get(subReport);
                    SubReportInfo testResultFile = subReports.stream().filter(subReportInfo -> ReportFileType.check(Integer.valueOf(subReportInfo.getReportFileType()),ReportFileType.TestResult)).findFirst().orElse(null);
                    SubReportInfo wordFile = subReports.stream().filter(subReportInfo -> ReportFileType.check(Integer.valueOf(subReportInfo.getReportFileType()),ReportFileType.Word)).findFirst().orElse(null);
                    if (Func.isNotEmpty(testResultFile)){
                        sortSubReportInfos.add(testResultFile);
                    }else if (Func.isNotEmpty(wordFile)){
                        sortSubReportInfos.add(wordFile);
                    }
                }
            }
        }
        // 查询订单下匹配不到Report的SubReport数据
        SubReportReq subReportReqNew = new SubReportReq();
        subReportReqNew.setOrderNo(reportRequest.getOrderNo());
        List<SubReportInfo> subReportInfosNoMatrix = subReportMapper.getSubReportsNoMatrix(subReportReqNew);
        if(Func.isNotEmpty(subReportInfosNoMatrix)){
            sortSubReportInfos.addAll(subReportInfosNoMatrix);
        }
        if(Func.isNotEmpty(sortSubReportInfos)){
            List<SubReportBO> subReportBOList = Lists.newArrayList();
            Map<String,List<SubReportInfo>> subReportMaps = sortSubReportInfos.stream().collect(Collectors.groupingBy(SubReportInfo::getSubReportNo));
            subReportMaps.forEach((k,v)->{
                SubReportBO subReportBO = new SubReportBO();
                subReportBO.setSubReportNo(k);
                if(Func.isNotEmpty(v)){
                    List<AttachmentBO> subReportFileList = Lists.newArrayList();
                    v.stream().forEach(item->{
                        AttachmentBO attachmentBO = new AttachmentBO();
                        attachmentBO.setCloudId(Constants.CLOUD_PREFIX + item.getCloudId());
                        if(isSubcontractReport(item)){
                            attachmentBO.setFileType(TestResultItemType.SUBCONTRACT.getType());
                        }else{
                            attachmentBO.setFileType(TestResultItemType.TEST_RESULT.getType());
                        }
                        subReportFileList.add(attachmentBO);
                    });
                    subReportBO.setSubReportFileList(subReportFileList);
                }
                subReportBOList.add(subReportBO);
            });
            digitalReportInfo.getVariableDataList().setSubReportList(subReportBOList);
        }
    }
    private void assembleReportFile(DigitalReportDataSourceDTO digitalReportInfo, GenerateReportRequest reportRequest){

    }

    private void assembleReportTemplate(DigitalReportDataSourceDTO digitalReportInfo, GenerateReportRequest reportRequest){
        ReportTemplateBO reportTemplate = new ReportTemplateBO();
        AccreditationBO accreditation = new AccreditationBO();
        LabDTO labDTO = reportRequest.getOrder().getLabDTO();
        Integer languageId = reportRequest.getLanguageId();
        GpnOrderReportDetailDTO reportDTO = reportMapper.queryReportDetailByReportId(reportRequest.getReportId());
        List<QualificationTypeValue> qualificationTypeList = this.getQualificationType(reportDTO.getAccreditationLogoPath(),labDTO.getLabCode());
        if(Func.isNotEmpty(qualificationTypeList)){
            accreditation.setType(qualificationTypeList.stream().map(QualificationTypeValue::getType).collect(Collectors.toList()));
        }
        if(Func.isNotEmpty(reportDTO.getAccreditationLogoPath())){
            accreditation.setAccreditationLogo(Constants.CLOUD_PREFIX+reportDTO.getAccreditationLogoPath());
        }
        reportTemplate.setAccreditation(accreditation);
        String labClaimer = frameWorkClient.getLabClaimerCn(labDTO.getLabCode(),languageId);
        if(Func.isNotEmpty(labClaimer)){
            reportTemplate.setLabClaimer(Constants.CLOUD_PREFIX + labClaimer);
        }
        LabClaimer labGBClaimer =  frameWorkClient.getLabGBClaimer(languageId,reportRequest.getOrder().getBUID(),labDTO.getLocationID());
        if(Func.isNotEmpty(labGBClaimer)){
            reportTemplate.setLabGBClaimer(Constants.CLOUD_PREFIX + labGBClaimer.getLabGBClaimer());
            reportTemplate.setGbCompanyChop(Constants.CLOUD_PREFIX + labGBClaimer.getGbCompanyChop());
        }
        List<SignaturesBO> signatureList = Lists.newArrayList();
        reportTemplate.setSignatureList(signatureList);
        digitalReportInfo.getVariableDataList().setReportTemplate(reportTemplate);
    }

    private void assembleTrfList(DigitalReportDataSourceDTO digitalReportInfo, GenerateReportRequest reportRequest){
        OrderNosReq orderNosReq = new OrderNosReq();
        orderNosReq.setOrderNos(Lists.newArrayList(reportRequest.getOrderNo()));
        orderNosReq.setProductLineCode(ProductLineContextHolder.getProductLineCode());
        BaseResponse<List<OrderTrfDTO>> orderTrfRes = orderFacade.getOrderTrfByOrderNos(orderNosReq);
        if(Func.isNotEmpty(orderTrfRes) && Func.isNotEmpty(orderTrfRes.getData())){
            OrderTrfDTO orderTrfDTOList = orderTrfRes.getData().get(0);
            if(Func.isNotEmpty(orderTrfDTOList) && Func.isNotEmpty(orderTrfDTOList.getOrderTrfRelInfos())){
                List<TrfBO> trfList = Lists.newArrayList();
                orderTrfDTOList.getOrderTrfRelInfos().stream().forEach(orderTrfDTO -> {
                    TrfBO rdTrfDTO = new TrfBO();
                    rdTrfDTO.setTrfNo(orderTrfDTO.getRefNo());
                    rdTrfDTO.setRefSystemId(orderTrfDTO.getRefSystemId());
                    trfList.add(rdTrfDTO);
                });
                digitalReportInfo.getVariableDataList().setTrfList(trfList);
            }
        }

    }

    private void assembleOrder(DigitalReportDataSourceDTO digitalReportInfo, GenerateReportRequest reportRequest){
        OrderBO order = digitalReportInfo.getVariableDataList().getOrder();
        if(null == order){
            order = new OrderBO();
        }
        digitalReportInfo.getVariableDataList().setOrder(order);
        String orderNo = reportRequest.getOrderNo();
        String buCode = ProductLineContextHolder.getProductLineCode();
        OrderIdReq orderIdReq = new OrderIdReq();
        orderIdReq.setOrderNo(orderNo);
        orderIdReq.setProductLineCode(buCode);
        BaseResponse<OrderAllDTO> orderAllDTORes = orderFacade.getOrderForPe(orderIdReq);
        OrderAllDTO orderAllDTO = orderAllDTORes.getData();
        BaseResponse<TestRequestInfo> testRequestRsp = orderFacade.queryRestRequestForPe(orderIdReq);
        TestRequestInfo orderTestRequest = testRequestRsp.getData();
        ReportReceiverReq reportReceiverReq = new ReportReceiverReq();
        reportReceiverReq.setOrderNo(orderNo);
        reportReceiverReq.setProductLineCode(buCode);
        BaseResponse<ReportReceiverRsp> reportReceiverRsp = orderFacade.getReportReceiverInfo(reportReceiverReq);
        ReportReceiverRsp reportReceiver = reportReceiverRsp.getData();
        BaseResponse<List<OrderAttachmentDTO>> orderAttachmentResponse =orderFacade.queryOrderAttachmentForPe(orderIdReq);
        List<OrderAttachmentDTO> orderAttachmentList = orderAttachmentResponse.getData();
        // header
        assembleOrderHeader(digitalReportInfo,orderAllDTO);
        // contactPersonList
        order.setContactPersonList(convertOrderPerson(orderAllDTO));
        // flags
        OrderFlagBO flags = new OrderFlagBO();
        flags.setToDMFlag(orderAllDTO.getToDMFlag());
        flags.setSelfTestFlag(orderAllDTO.getSelfTestFlag());
        order.setFlags(flags);
        // customerList
        List<CustomerInstanceDTO> customerList = getCustomerList(orderIdReq);
        if(Func.isNotEmpty(customerList)) {
            List<CustomerBO> customerBOList = Lists.newArrayList();
            customerList.stream().forEach(applicantInstance -> {
                customerBOList.add(convertCustomer(applicantInstance));
            });
            order.setCustomerList(customerBOList);
        }

        BaseResponse<List<ProductInstanceDTO>> productInstanceListRes =  orderFacade.queryProductInstaceForPe(orderIdReq);
        if(Func.isNotEmpty(productInstanceListRes) && Func.isNotEmpty(productInstanceListRes.getData())){
            // productList
            order.setProductList(assembleProduct(productInstanceListRes.getData()));
            // sampleList
            order.setSampleList(assembleSample(productInstanceListRes.getData()));
        }
        // serviceRequirement
        order.setServiceRequirement(assembleServiceRequirement(orderTestRequest,reportReceiver));
        // others
        OrderOthersBO others = new OrderOthersBO();
        others.setOrderRemark(orderAllDTO.getRemark());
        order.setOthers(others);
        // attachmentList
        if(Func.isNotEmpty(orderAttachmentList)){
            List<AttachmentBO> attachmentList = Lists.newArrayList();
            orderAttachmentList.stream().forEach(attachment->{
                AttachmentBO rdAttachment = new AttachmentBO();
                rdAttachment.setCloudId(attachment.getCloudID());
                rdAttachment.setFileName(attachment.getAttachmentName());
                rdAttachment.setFilePath(attachment.getPath());
                attachmentList.add(rdAttachment);
            });
            order.setAttachmentList(attachmentList);
        }
    }
    private List<SampleBO> assembleSample(List<ProductInstanceDTO> productInsList){
        productInsList = productInsList.stream().filter(i->Func.isNotEmpty(i.getHeaderID())).sorted(Comparator.comparing(ProductInstanceDTO::getSampleID)).collect(Collectors.toList());
        Set<String> formIdSets = productInsList.stream().map(ProductInstanceDTO::getdFFFormID).collect(Collectors.toSet());
        List<SampleBO> sampleList =Lists.newArrayList();
        if(Func.isNotEmpty(formIdSets)){
            String primaryLanguageCode = frameWorkClient.getPrimaryLanguageCode(ProductLineContextHolder.getProductLineCode());
            int primaryLanguageId = LanguageType.findCode(primaryLanguageCode).getLanguageId();
            List<DffFormAttrDTO> allDffFormAttrDTOList = dffClient.getDffFormAttrByDffFormIdList(formIdSets);
            List<DffFormAttrDTO> dffFormAttrDTOListEn = new ArrayList<>();
            List<DffFormAttrDTO> dffFormAttrDTOListCn = new ArrayList<>();
            List<DffFormAttrDTO> defaultLanguageDffFormAttrDTOList = new ArrayList<>();
            if(Func.isNotEmpty(allDffFormAttrDTOList)){
                dffFormAttrDTOListEn = allDffFormAttrDTOList.stream().filter(i -> LanguageType.check(i.getLanguageID(), LanguageType.English)).collect(Collectors.toList());
                dffFormAttrDTOListCn = allDffFormAttrDTOList.stream().filter(i -> LanguageType.check(i.getLanguageID(), LanguageType.Chinese)).collect(Collectors.toList());
                defaultLanguageDffFormAttrDTOList = allDffFormAttrDTOList.stream().filter(i -> Func.isNotEmpty(i.getLanguageID()) && i.getLanguageID()==primaryLanguageId).collect(Collectors.toList());
                if(Func.isEmpty(defaultLanguageDffFormAttrDTOList)){
                    defaultLanguageDffFormAttrDTOList = allDffFormAttrDTOList.stream().filter(i -> Func.isNotEmpty(i.getLanguageID()) && i.getLanguageID()!=primaryLanguageId).collect(Collectors.toList());
                }
            }
            List<ProductInstanceDTO> productInstanceDTOListEn = productInsList.stream().filter(i -> LanguageType.check(i.getLanguageID(), LanguageType.English)).collect(Collectors.toList());
            List<ProductInstanceDTO> productInstanceDTOListCn = productInsList.stream().filter(i -> LanguageType.check(i.getLanguageID(), LanguageType.Chinese)).collect(Collectors.toList());
            List<ProductInstanceDTO> defaultLanguageList = productInsList.stream().filter(i -> Func.isNotEmpty(i.getLanguageID()) && i.getLanguageID()==primaryLanguageId).collect(Collectors.toList());
            if(Func.isEmpty(defaultLanguageList)){
                defaultLanguageList = productInsList.stream().filter(i -> Func.isNotEmpty(i.getLanguageID()) && i.getLanguageID()!=primaryLanguageId).collect(Collectors.toList());
            }
            if(Func.isNotEmpty(defaultLanguageList)){
                for (ProductInstanceDTO productInstanceDTO : defaultLanguageList) {
                    SampleBO sampleDTO = new SampleBO();
                    sampleDTO.setTestSampleInstanceId(productInstanceDTO.getSampleID());
                    sampleDTO.setTemplateId("");
                    List<DFFAttrBO> sampleAttrList = Lists.newArrayList();
                    Map<String, Object> defaultProductMap = BeanUtil.beanToMap(productInstanceDTO, false, true);
                    if(Func.isNotEmpty(defaultLanguageDffFormAttrDTOList)){
                        defaultLanguageDffFormAttrDTOList = defaultLanguageDffFormAttrDTOList.stream().sorted(Comparator.comparing(item -> (Func.isEmpty(item.getSequence())?0:Integer.parseInt(item.getSequence())), Comparator.nullsLast(Integer::compareTo))).collect(Collectors.toList());
                        for (DffFormAttrDTO dffFormAttrDTO : defaultLanguageDffFormAttrDTOList) {
                            DFFAttrBO sampleAttrDTO = new DFFAttrBO();
                            sampleAttrDTO.setLabelName(dffFormAttrDTO.getDispalyName());
                            sampleAttrDTO.setLabelCode(dffFormAttrDTO.getFieldCode());
                            sampleAttrDTO.setCustomerLabel("");
                            sampleAttrDTO.setValue(Func.toStr(defaultProductMap.getOrDefault(StrUtil.lowerFirst(dffFormAttrDTO.getFieldCode()),null)));
                            sampleAttrDTO.setSeq(Func.toInt(dffFormAttrDTO.getSequence()));
                            sampleAttrDTO.setDataType(dffFormAttrDTO.getFieldType());
                            List<DFFAttrLanguageBO> languageList = Lists.newArrayList();
                            //英文
                            ProductInstanceDTO productInstanceDTOEn = productInstanceDTOListEn.stream().filter(i -> Func.equals(i.getSampleID(), productInstanceDTO.getSampleID())).findAny().orElse(null);
                            if(Func.isNotEmpty(dffFormAttrDTOListEn) && Func.isNotEmpty(productInstanceDTOEn)){
                                DffFormAttrDTO dffFormAttrDTOEN = dffFormAttrDTOListEn.stream().filter(i -> StringUtils.equalsIgnoreCase(i.getFieldCode(), dffFormAttrDTO.getFieldCode())).findAny().orElse(null);
                                if(Func.isNotEmpty(dffFormAttrDTOEN)){
                                    Map<String, Object> productEnMap = BeanUtil.beanToMap(productInstanceDTOEn, false, true);
                                    DFFAttrLanguageBO sampleAttrLanguage = new DFFAttrLanguageBO();
                                    sampleAttrLanguage.setLabelName(dffFormAttrDTOEN.getDispalyName());
                                    sampleAttrLanguage.setValue(Func.toStr(productEnMap.getOrDefault(StrUtil.lowerFirst(dffFormAttrDTOEN.getFieldCode()),null)));
                                    sampleAttrLanguage.setLanguageId(LanguageType.English.getLanguageId());
                                    languageList.add(sampleAttrLanguage);
                                }
                            }

                            //中文
                            ProductInstanceDTO productInstanceDTOCn = productInstanceDTOListCn.stream().filter(i -> Func.equals(i.getSampleID(), productInstanceDTO.getSampleID())).findAny().orElse(null);
                            if(Func.isNotEmpty(dffFormAttrDTOListCn) && Func.isNotEmpty(productInstanceDTOCn)){
                                DffFormAttrDTO dffFormAttrDTOCN = dffFormAttrDTOListCn.stream().filter(i -> StringUtils.equalsIgnoreCase(i.getFieldCode(), dffFormAttrDTO.getFieldCode())).findAny().orElse(null);
                                if(Func.isNotEmpty(dffFormAttrDTOCN)){
                                    Map<String, Object> productCnMap = BeanUtil.beanToMap(productInstanceDTOCn, false, true);
                                    DFFAttrLanguageBO sampleAttrLanguage = new DFFAttrLanguageBO();
                                    sampleAttrLanguage.setLabelName(dffFormAttrDTOCN.getDispalyName());
                                    sampleAttrLanguage.setValue(Func.toStr(productCnMap.getOrDefault(StrUtil.lowerFirst(dffFormAttrDTOCN.getFieldCode()),null)));
                                    sampleAttrLanguage.setLanguageId(LanguageType.Chinese.getLanguageId());
                                    languageList.add(sampleAttrLanguage);
                                }
                            }
                            sampleAttrDTO.setLanguageList(languageList);
                            sampleAttrList.add(sampleAttrDTO);
                        }
                    }
                    sampleDTO.setSampleAttrList(sampleAttrList);
                    sampleList.add(sampleDTO);
                }
            }
        }
        return sampleList;
    }
    private List<ProductBO> assembleProduct(List<ProductInstanceDTO> productInsList){
        productInsList = productInsList.stream().filter(i->Func.isEmpty(i.getHeaderID())).collect(Collectors.toList());
        Set<String> formIdSets = productInsList.stream().map(ProductInstanceDTO::getdFFFormID).collect(Collectors.toSet());
        List<ProductBO> rdProductList =Lists.newArrayList();
        if(Func.isNotEmpty(formIdSets)){
            String primaryLanguageCode = frameWorkClient.getPrimaryLanguageCode(ProductLineContextHolder.getProductLineCode());
            int primaryLanguageId = LanguageType.findCode(primaryLanguageCode).getLanguageId();
            List<DffFormAttrDTO> allDffFormAttrDTOList = dffClient.getDffFormAttrByDffFormIdList(formIdSets);
            List<DffFormAttrDTO> dffFormAttrDTOListEn = new ArrayList<>();
            List<DffFormAttrDTO> dffFormAttrDTOListCn = new ArrayList<>();
            List<DffFormAttrDTO> defaultLanguageDffFormAttrDTOList = new ArrayList<>();
            if(Func.isNotEmpty(allDffFormAttrDTOList)){
                dffFormAttrDTOListEn = allDffFormAttrDTOList.stream().filter(i -> LanguageType.check(i.getLanguageID(), LanguageType.English)).collect(Collectors.toList());
                dffFormAttrDTOListCn = allDffFormAttrDTOList.stream().filter(i -> LanguageType.check(i.getLanguageID(), LanguageType.Chinese)).collect(Collectors.toList());
                defaultLanguageDffFormAttrDTOList = allDffFormAttrDTOList.stream().filter(i -> Func.isNotEmpty(i.getLanguageID()) && i.getLanguageID()==primaryLanguageId).collect(Collectors.toList());
                if(Func.isEmpty(defaultLanguageDffFormAttrDTOList)){
                    defaultLanguageDffFormAttrDTOList = allDffFormAttrDTOList.stream().filter(i -> Func.isNotEmpty(i.getLanguageID()) && i.getLanguageID()!=primaryLanguageId).collect(Collectors.toList());
                }
            }
            List<ProductInstanceDTO> productInstanceDTOListEn = productInsList.stream().filter(i -> LanguageType.check(i.getLanguageID(), LanguageType.English)).collect(Collectors.toList());
            List<ProductInstanceDTO> productInstanceDTOListCn = productInsList.stream().filter(i -> LanguageType.check(i.getLanguageID(), LanguageType.Chinese)).collect(Collectors.toList());
            List<ProductInstanceDTO> defaultLanguageList = productInsList.stream().filter(i -> Func.isNotEmpty(i.getLanguageID()) && i.getLanguageID()==primaryLanguageId).collect(Collectors.toList());
            if(Func.isEmpty(defaultLanguageList)){
                defaultLanguageList = productInsList.stream().filter(i -> Func.isNotEmpty(i.getLanguageID()) && i.getLanguageID()!=primaryLanguageId).collect(Collectors.toList());
            }
            if(Func.isNotEmpty(defaultLanguageList)){
                for (ProductInstanceDTO productInstanceDTO : defaultLanguageList) {
                    ProductBO rdProductDTO = new ProductBO();
                    rdProductDTO.setProductInstanceId(productInstanceDTO.getSampleID());
                    rdProductDTO.setTemplateId("");
                    List<DFFAttrBO> rdProductAttrList = Lists.newArrayList();
                    Map<String, Object> defaultProductMap = BeanUtil.beanToMap(productInstanceDTO, false, true);
                    if(Func.isNotEmpty(defaultLanguageDffFormAttrDTOList)){
                        defaultLanguageDffFormAttrDTOList = defaultLanguageDffFormAttrDTOList.stream().sorted(Comparator.comparing(item -> (Func.isEmpty(item.getSequence())?0:Integer.parseInt(item.getSequence())), Comparator.nullsLast(Integer::compareTo))).collect(Collectors.toList());
                        for (DffFormAttrDTO dffFormAttrDTO : defaultLanguageDffFormAttrDTOList) {
                            DFFAttrBO sampleAttrDTO = new DFFAttrBO();
                            sampleAttrDTO.setLabelName(dffFormAttrDTO.getDispalyName());
                            sampleAttrDTO.setLabelCode(dffFormAttrDTO.getFieldCode());
                            sampleAttrDTO.setCustomerLabel("");
                            sampleAttrDTO.setValue(Func.toStr(defaultProductMap.getOrDefault(StrUtil.lowerFirst(dffFormAttrDTO.getFieldCode()),null)));
                            sampleAttrDTO.setSeq(Func.toInt(dffFormAttrDTO.getSequence()));
                            sampleAttrDTO.setDataType(dffFormAttrDTO.getFieldType());
                            List<DFFAttrLanguageBO> languageList = Lists.newArrayList();
                            //英文
                            ProductInstanceDTO productInstanceDTOEn = productInstanceDTOListEn.stream().filter(i -> Func.equals(i.getSampleID(), productInstanceDTO.getSampleID())).findAny().orElse(null);
                            if(Func.isNotEmpty(dffFormAttrDTOListEn) && Func.isNotEmpty(productInstanceDTOEn)){
                                DffFormAttrDTO dffFormAttrDTOEN = dffFormAttrDTOListEn.stream().filter(i -> StringUtils.equalsIgnoreCase(i.getFieldCode(), dffFormAttrDTO.getFieldCode())).findAny().orElse(null);
                                if(Func.isNotEmpty(dffFormAttrDTOEN)){
                                    Map<String, Object> productEnMap = BeanUtil.beanToMap(productInstanceDTOEn, false, true);
                                    DFFAttrLanguageBO sampleAttrLanguage = new DFFAttrLanguageBO();
                                    sampleAttrLanguage.setLabelName(dffFormAttrDTOEN.getDispalyName());
                                    sampleAttrLanguage.setValue(Func.toStr(productEnMap.getOrDefault(StrUtil.lowerFirst(dffFormAttrDTOEN.getFieldCode()),null)));
                                    sampleAttrLanguage.setLanguageId(LanguageType.English.getLanguageId());
                                    languageList.add(sampleAttrLanguage);
                                }
                            }

                            //中文
                            ProductInstanceDTO productInstanceDTOCn = productInstanceDTOListCn.stream().filter(i -> Func.equals(i.getSampleID(), productInstanceDTO.getSampleID())).findAny().orElse(null);
                            if(Func.isNotEmpty(dffFormAttrDTOListCn) && Func.isNotEmpty(productInstanceDTOCn)){
                                DffFormAttrDTO dffFormAttrDTOCN = dffFormAttrDTOListCn.stream().filter(i -> StringUtils.equalsIgnoreCase(i.getFieldCode(), dffFormAttrDTO.getFieldCode())).findAny().orElse(null);
                                if(Func.isNotEmpty(dffFormAttrDTOCN)){
                                    Map<String, Object> productCnMap = BeanUtil.beanToMap(productInstanceDTOCn, false, true);
                                    DFFAttrLanguageBO sampleAttrLanguage = new DFFAttrLanguageBO();
                                    sampleAttrLanguage.setLabelName(dffFormAttrDTOCN.getDispalyName());
                                    sampleAttrLanguage.setValue(Func.toStr(productCnMap.getOrDefault(StrUtil.lowerFirst(dffFormAttrDTOCN.getFieldCode()),null)));
                                    sampleAttrLanguage.setLanguageId(LanguageType.Chinese.getLanguageId());
                                    languageList.add(sampleAttrLanguage);
                                }
                            }
                            sampleAttrDTO.setLanguageList(languageList);
                            rdProductAttrList.add(sampleAttrDTO);
                        }
                    }
                    rdProductDTO.setProductAttrList(rdProductAttrList);
                    rdProductList.add(rdProductDTO);
                }
            }
        }
        return rdProductList;
    }

    private ServiceRequirementBO assembleServiceRequirement(TestRequestInfo orderTestRequest,ReportReceiverRsp reportReceiver){
        ServiceRequirementBO serviceRequirement = new ServiceRequirementBO();
        ServiceRequirementReportBO orderReportRequirement = new ServiceRequirementReportBO();
        orderReportRequirement.setReportLanguage(Integer.valueOf(orderTestRequest.getReportLanguage()));
        orderReportRequirement.setReportHeader(reportReceiver.getReportHeaderEN());
        orderReportRequirement.setReportAddress(reportReceiver.getReportAddressEN());
//        orderReportRequirement.setAccreditation(orderTestRequest.getReportAccreditationNeededFlag());
        orderReportRequirement.setNeedConclusion(orderTestRequest.getNeedConclusion());
        orderReportRequirement.setNeedDraft(orderTestRequest.getDraftReportRequired());
        orderReportRequirement.setNeedPhoto(orderTestRequest.getTakePhotoFlag());
        List<ReportLanguageBO> languageList = Lists.newArrayList();
        ReportLanguageBO reportLanguage = new ReportLanguageBO();
        reportLanguage.setLanguageId(LanguageType.Chinese.getLanguageId());
        reportLanguage.setReportHeader(reportReceiver.getReportHeaderCN());
        reportLanguage.setReportAddress(reportReceiver.getReportAddressCN());
        languageList.add(reportLanguage);
        orderReportRequirement.setLanguageList(languageList);
        serviceRequirement.setReport(orderReportRequirement);
        return serviceRequirement;
    }
    private List<ContactPersonBO> convertOrderPerson(OrderAllDTO orderAllDTO){
        List<ContactPersonBO> contactPersonList = Lists.newArrayList();
        // Sales
        if(Func.isNotEmpty(orderAllDTO.getSalesPerson())){
            ContactPersonBO salesPerson = new ContactPersonBO();
            salesPerson.setContactUsage(OrderPersonType.SALES.getCode());
            salesPerson.setContactName(orderAllDTO.getSalesPerson());
            salesPerson.setContactEmail(orderAllDTO.getSalesPersonEmail());
            salesPerson.setRegionAccount(orderAllDTO.getSalesPerson());
            contactPersonList.add(salesPerson);
        }
        // cs
        if(Func.isNotEmpty(orderAllDTO.getcSName())){
            ContactPersonBO csPerson = new ContactPersonBO();
            csPerson.setContactUsage("cs");
            csPerson.setContactName(orderAllDTO.getcSContact());
            csPerson.setContactEmail(orderAllDTO.getcSEmail());
            csPerson.setRegionAccount(orderAllDTO.getcSName());
            contactPersonList.add(csPerson);
        }
        // subReportReviewerName
        if(Func.isNotEmpty(orderAllDTO.getSubReportReviewerName())){
            ContactPersonBO subReportReviewer = new ContactPersonBO();
            subReportReviewer.setContactUsage(OrderPersonType.SUB_REPORT_REVIEWER.getCode());
            subReportReviewer.setContactName(orderAllDTO.getcSContact());
            subReportReviewer.setRegionAccount(orderAllDTO.getcSName());
            contactPersonList.add(subReportReviewer);
        }
        return contactPersonList;
    }

    private CustomerBO convertCustomer(CustomerInstanceDTO customerInstance){
        CustomerBO rdCustomer = new CustomerBO();
        rdCustomer.setCustomerUsage(GpoCustomerType.enumOf(customerInstance.getCustomerUsage()).getStatus());
        rdCustomer.setBossNo(customerInstance.getBossNumber());
        rdCustomer.setCustomerGroupCode(customerInstance.getCustomerGroupId());
        rdCustomer.setCustomerName(customerInstance.getCustomerNameEN());
        rdCustomer.setCustomerAddress(customerInstance.getCustomerAddressEN());
        List<CustomerLanguageBO> languageList = Lists.newArrayList();
        CustomerLanguageBO rdCustomerLanguageDTO = new CustomerLanguageBO();
        rdCustomerLanguageDTO.setLanguageId(LanguageType.Chinese.getLanguageId());
        rdCustomerLanguageDTO.setCustomerAddress(customerInstance.getCustomerAddressCN());
        rdCustomerLanguageDTO.setCustomerName(customerInstance.getCustomerNameCN());
        languageList.add(rdCustomerLanguageDTO);
        rdCustomer.setLanguageList(languageList);
        List<CustomerContactBO> customerContactList = Lists.newArrayList();
        CustomerContactBO applicantContact = new CustomerContactBO();
        applicantContact.setContactEmail(customerInstance.getContactPersonEmail());
        applicantContact.setContactName(customerInstance.getContactPersonName());
        applicantContact.setBossContactId(customerInstance.getBossContactID());
        applicantContact.setContactFAX(customerInstance.getContactPersonFax());
        applicantContact.setContactMobile(customerInstance.getContactPersonPhone1());
        applicantContact.setContactTelephone(customerInstance.getContactPersonPhone2());
        applicantContact.setBossSiteUseId(customerInstance.getBossSiteUseID());
        applicantContact.setCustomerContactAddressId(customerInstance.getCustomerContactId());
        applicantContact.setCustomerContactId(customerInstance.getCustomerContactId());
        customerContactList.add(applicantContact);
        rdCustomer.setCustomerContactList(customerContactList);
        return rdCustomer;
    }

    private void assembleOrderHeader(DigitalReportDataSourceDTO digitalReportInfo,OrderAllDTO orderAllDTO){
        if(Func.isEmpty(orderAllDTO)){
            return;
        }
        OrderHeaderBO header = digitalReportInfo.getVariableDataList().getOrder().getHeader();
        if(null == header){
            header = new OrderHeaderBO();
            digitalReportInfo.getVariableDataList().getOrder().setHeader(header);
        }
        //header.setSystemId(SgsSystem.GPO.getSgsSystemId());
        header.setOrderId(orderAllDTO.getOrderId());
        header.setOrderNo(orderAllDTO.getOrderNo());
        header.setOriginalOrderNo(orderAllDTO.getOrderNo());
        header.setOrderStatus(orderAllDTO.getOrderStatus());
        header.setServiceType(orderAllDTO.getServiceLevel());
        header.setOrderType(orderAllDTO.getOrderType());
        header.setOperationType(orderAllDTO.getOperationType());
        header.setOperationMode(orderAllDTO.getOperationMode());
        header.setProductCategory(orderAllDTO.getProductCategory());
        header.setIdbLab(orderAllDTO.getIdbLab());
        header.setTat(orderAllDTO.getTAT());
        header.setSampleReceiveDate(orderAllDTO.getSampleReceiveDate());
        header.setSampleResubmissionDate(orderAllDTO.getSampleResubmissionDate());
        header.setFurtherInformationReceivingDate(orderAllDTO.getFurtherInformationReceivingDate());
        header.setServiceStartDate(orderAllDTO.getServiceStartDate());
        header.setOrderExpectDueDate(orderAllDTO.getExpectedOrderDueDate());
        header.setCreateBy(orderAllDTO.getCreatedBy());
        header.setCreateDate(orderAllDTO.getCreatedDate());
        OrderPaymentBO orderPayment = new OrderPaymentBO();
        orderPayment.setPaymentStatus(orderAllDTO.getPaymentStatus());
        orderPayment.setCurrency(orderAllDTO.getCurrencyID());
        orderPayment.setTotalAmount(orderAllDTO.getTotalPrice());
        header.setPayment(orderPayment);
    }

    private void assembleHeader(DigitalReportDataSourceDTO digitalReportInfo, GenerateReportRequest reportRequest){
        ReportHeaderBO header = digitalReportInfo.getVariableDataList().getHeader();
        if(null == header){
            header = new ReportHeaderBO();
        }
        header.setSystemId(SgsSystem.GPO.getSgsSystemId());
        ReportDTO reportDto = reportMapper.getByReportNo(reportRequest.getReportNo());
        if(Func.isNotEmpty(reportDto)){
            header.setReportId(reportDto.getId());
            header.setReportNo(reportDto.getActualReportNo());
            header.setIssuedReportNo(reportDto.getActualReportNo());
            header.setReportStatus(reportDto.getReportStatus());
            header.setReportDueDate(reportDto.getReportDueDate());
            header.setSoftCopyDeliveryDate(reportDto.getSoftcopyDeliveryDate());
            header.setApproveBy(reportDto.getApproverBy());
            header.setApproveDate(reportDto.getApproverDate());
            header.setCertificateName(reportDto.getCertificateName());
            header.setCreateBy(reportDto.getCreatedBy());
            header.setCreateDate(reportDto.getCreatedDate());
            ConclusionListInfo conclusionListInfo = conclusionListExtMapper.queryReportConclusion(reportDto.getId());
            if(Func.isNotEmpty(conclusionListInfo)){
                ConclusionBO reportConclusion = new ConclusionBO();
                if(Func.isNotEmpty(conclusionListInfo.getConclusionCode())){
                    reportConclusion.setConclusionCode(conclusionListInfo.getConclusionCode().toString());
                }
                header.setConclusion(reportConclusion);
            }
        }
    }


    private void initSignature(DigitalReportDataSourceDTO digitalReportInfo, GenerateReportRequest generateReportRequset) {
        String reportNo = generateReportRequset.getReportNo();
        Integer languageId = generateReportRequset.getLanguageId();
        List<SignaturesInfoDTO> signatureDTOList = new ArrayList<>();
        List<SignaturesInfoDTO> editorSignatureDTOList = new ArrayList<>();
        List<SignaturesInfoDTO> reviewerSignatureDTOList = new ArrayList<>();
        List<SignaturesInfoDTO> csSignList = new ArrayList<>();
        if (StringUtils.isNotEmpty(reportNo)) {
            //查询公司名称
            Map<String,String> param = new HashMap<>();
            param.put("legalEntityCode",generateReportRequset.getOrder().getLegalEntityCode());
            param.put("locationCode",generateReportRequset.getOrder().getLocationCode());
            String companyName = frameWorkClient.uploadByStream(param,generateReportRequset.getLanguageId());
            ReportInfoPO reportInfoPO = reportMapper.getReportInfoByReportNo(reportNo);
                String approvesList = reportInfoPO.getApprover();
                String editor = reportInfoPO.getEditor();
                String reviewer = reportInfoPO.getReviewer();
                Integer signatureLanguage = reportInfoPO.getSignatureLanguage();
                if(Func.isEmpty(signatureLanguage)){
                    signatureLanguage = 0;
                }
            //approver

                if (StringUtils.isNotEmpty(approvesList)) {
                    Map<String, Object> approvesMap = JSON.parseObject(approvesList);
                    List<UserSignatureVO> userSignatureVOs = new ArrayList<>();
                    if(approvesMap.containsKey(LanguageType.English.getCode()) && (SignatureLanguage.check(signatureLanguage,SignatureLanguage.English) ||  (SignatureLanguage.check(signatureLanguage,SignatureLanguage.Follow_Report) && LanguageType.check(languageId,LanguageType.English)))){
                        //有英文签名图 ，且(signLanguage英文 或 follow Report Language(EN、Muli、En & CHI)  )
                        userSignatureVOs =JSON.parseArray(approvesMap.get(LanguageType.English.getCode()).toString(), UserSignatureVO.class);
                    }else if(approvesMap.containsKey(LanguageType.Chinese.getCode()) && (SignatureLanguage.check(signatureLanguage,SignatureLanguage.Chinese) ||  (SignatureLanguage.check(signatureLanguage,SignatureLanguage.Follow_Report)  && LanguageType.check(languageId,LanguageType.Chinese)))){
                        //有中文签名图 ，且(signLanguage中文 或 follow Report Language(CHI、En & CHI))
                        userSignatureVOs =JSON.parseArray(approvesMap.get(LanguageType.Chinese.getCode()).toString(), UserSignatureVO.class);
                    }

                    if (CollectionUtils.isNotEmpty(userSignatureVOs)) {
                        for (UserSignatureVO userSignatureVO : userSignatureVOs) {
                            SignaturesInfoDTO signatureDTO = new SignaturesInfoDTO();
                            signatureDTO.setCompanyName(companyName);
                            signatureDTO.setCloudId(Constants.CLOUD_PREFIX+userSignatureVO.getAutographId());
                            signatureDTO.setUserName(userSignatureVO.getSignatureName());
                            signatureDTO.setTitle(userSignatureVO.getTitle());
                            signatureDTO.setBranch(userSignatureVO.getBranch());
                            signatureDTOList.add(signatureDTO);
                        }
                    }
            }
                //editor
            if (Func.isNotEmpty(editor)){
                Map<String, Object> editorMap = JSON.parseObject(editor);
                List<UserSignatureVO> userSignatureVOs = new ArrayList<>();
                if(editorMap.containsKey(LanguageType.English.getCode()) && (SignatureLanguage.check(signatureLanguage,SignatureLanguage.English) ||  (SignatureLanguage.check(signatureLanguage,SignatureLanguage.Follow_Report) && LanguageType.check(languageId,LanguageType.English)))){
                    //有英文签名图 ，且(signLanguage英文 或 follow Report Language(EN、Muli、En & CHI)  )
                    userSignatureVOs =JSON.parseArray(editorMap.get(LanguageType.English.getCode()).toString(), UserSignatureVO.class);
                }else if(editorMap.containsKey(LanguageType.Chinese.getCode()) && (SignatureLanguage.check(signatureLanguage,SignatureLanguage.Chinese) ||  (SignatureLanguage.check(signatureLanguage,SignatureLanguage.Follow_Report)  && LanguageType.check(languageId,LanguageType.Chinese)))){
                    //有中文签名图 ，且(signLanguage中文 或 follow Report Language(CHI、En & CHI))
                    userSignatureVOs =JSON.parseArray(editorMap.get(LanguageType.Chinese.getCode()).toString(), UserSignatureVO.class);
                }
                if (CollectionUtils.isNotEmpty(userSignatureVOs)) {
                    for (UserSignatureVO userSignatureVO : userSignatureVOs) {
                        SignaturesInfoDTO signatureDTO = new SignaturesInfoDTO();
                        signatureDTO.setCompanyName(companyName);
                        signatureDTO.setCloudId(Constants.CLOUD_PREFIX+userSignatureVO.getAutographId());
                        signatureDTO.setUserName(userSignatureVO.getSignatureName());
                        signatureDTO.setTitle(userSignatureVO.getTitle());
                        signatureDTO.setBranch(userSignatureVO.getBranch());
                        editorSignatureDTOList.add(signatureDTO);
                    }
                }
            }

            //reviewer
            if (Func.isNotEmpty(reviewer)){
                Map<String, Object> reviewerMap = JSON.parseObject(reviewer);
                List<UserSignatureVO> userSignatureVOs = new ArrayList<>();
                if(reviewerMap.containsKey(LanguageType.English.getCode()) && (SignatureLanguage.check(signatureLanguage,SignatureLanguage.English) ||  (SignatureLanguage.check(signatureLanguage,SignatureLanguage.Follow_Report) && LanguageType.check(languageId,LanguageType.English)))){
                    //有英文签名图 ，且(signLanguage英文 或 follow Report Language(EN、Muli、En & CHI)  )
                    userSignatureVOs =JSON.parseArray(reviewerMap.get(LanguageType.English.getCode()).toString(), UserSignatureVO.class);
                }else if(reviewerMap.containsKey(LanguageType.Chinese.getCode()) && (SignatureLanguage.check(signatureLanguage,SignatureLanguage.Chinese) ||  (SignatureLanguage.check(signatureLanguage,SignatureLanguage.Follow_Report)  && LanguageType.check(languageId,LanguageType.Chinese)))){
                    //有中文签名图 ，且(signLanguage中文 或 follow Report Language(CHI、En & CHI))
                    userSignatureVOs =JSON.parseArray(reviewerMap.get(LanguageType.Chinese.getCode()).toString(), UserSignatureVO.class);
                }
                if (CollectionUtils.isNotEmpty(userSignatureVOs)) {
                    for (UserSignatureVO userSignatureVO : userSignatureVOs) {
                        SignaturesInfoDTO signatureDTO = new SignaturesInfoDTO();
                        signatureDTO.setCompanyName(companyName);
                        signatureDTO.setCloudId(Constants.CLOUD_PREFIX+userSignatureVO.getAutographId());
                        signatureDTO.setUserName(userSignatureVO.getSignatureName());
                        signatureDTO.setTitle(userSignatureVO.getTitle());
                        signatureDTO.setBranch(userSignatureVO.getBranch());
                        reviewerSignatureDTOList.add(signatureDTO);
                    }
                }

            }
            if(Func.isNotEmpty(digitalReportInfo.getVariableDataList().getReport().getResponsibleCS())){
                String csName = digitalReportInfo.getVariableDataList().getReport().getResponsibleCS();
                UserSignatureQueryRequest userSignReq = new UserSignatureQueryRequest();
                List<String> languageCodes = Lists.newArrayList(LanguageType.English.getCode(),LanguageType.Chinese.getCode());
                userSignReq.setLanguageCodes(languageCodes);
                userSignReq.setLabCode(generateReportRequset.getOrder().getLabDTO().getLabCode());
                userSignReq.setIsSignature("1");
                userSignReq.setAuthSignTypes(Lists.newArrayList(SignTypeEnums.PROJECT_ENGINEER.getCode()));
                List<UserSignatureDTO> userSignatureList = frameWorkClient.queryUserSinature(userSignReq);
                logger.info("获取到的Signature信息:{}",userSignatureList);
                if(Func.isNotEmpty(userSignatureList)){
                    String languageCode = null;
                    if(SignatureLanguage.check(signatureLanguage,SignatureLanguage.Follow_Report)){
                        languageCode = LanguageType.check(languageId,LanguageType.Chinese)?LanguageType.Chinese.getCode():LanguageType.English.getCode();
                    }else if(SignatureLanguage.check(signatureLanguage,SignatureLanguage.English)){
                        languageCode = LanguageType.English.getCode();
                    } else if(SignatureLanguage.check(signatureLanguage,SignatureLanguage.Chinese)){
                        languageCode = LanguageType.Chinese.getCode();
                    }
                    String finalLanguageCode = languageCode;
                    userSignatureList.stream().filter(sign->StringUtils.equalsIgnoreCase(csName,sign.getRegionAccount())
                            && Func.equalsSafe(sign.getLanguageCode(), finalLanguageCode)).forEach(sign->{
                        SignaturesInfoDTO signaturesInfoDTO = new SignaturesInfoDTO();
                        signaturesInfoDTO.setCompanyName(companyName);
                        signaturesInfoDTO.setCloudId(Constants.CLOUD_PREFIX+sign.getAutographId());
                        signaturesInfoDTO.setUserName(sign.getSignatureName());
                        logger.info("打印sign:{}",sign);
                        signaturesInfoDTO.setTitle(sign.getTitle());
                        signaturesInfoDTO.setBranch(sign.getBranch());
                        csSignList.add(signaturesInfoDTO);
                    });
                }
            }
        }
        digitalReportInfo.getVariableDataList().setSignatures(signatureDTOList);
        digitalReportInfo.getVariableDataList().setEditorsSign(editorSignatureDTOList);
        digitalReportInfo.getVariableDataList().setReviewersSign(reviewerSignatureDTOList);
        digitalReportInfo.getVariableDataList().setCsSign(csSignList);
        logger.info("打印digitalReportInfo:{}",digitalReportInfo.getVariableDataList());
    }


    private void initDff2(DigitalReportDataSourceDTO2 digitalReportDataSourceDTO2,OrderAllDTO orderAllDTO){
        // 查询订单下的ProductList
        OrderIdReq orderIdReq = new OrderIdReq();
        orderIdReq.setOrderNo(orderAllDTO.getOrderNo());
        orderIdReq.setProductLineCode(orderAllDTO.getLabDTO().getBuCode());
        BaseResponse<List<ProductInstanceDTO>> productInstanceListRes =  orderFacade.queryProductInstaceForPe(orderIdReq);
        if(Func.isEmpty(productInstanceListRes)|| Func.isEmpty(productInstanceListRes.getData())){
            return;
        }
        List<DFFInfoDTO> dffFields = Lists.newArrayList();
        List<ProductInstanceDTO> productDtoList = productInstanceListRes.getData();
        List<ProductInfo> productInfoList = Lists.newArrayList();
        productDtoList.stream().forEach(product->{
            ProductInfo productInfo = new ProductInfo();
            Func.copy(product,productInfo);
            productInfo.setOrderId(product.getGeneralOrderID());
            productInfoList.add(productInfo);
        });
        BaseResponse<List<TestrptCoverpageDffSimpleDTO>> baseResponse = dffFacade.convertProductToRow(productInfoList);
        if(ResponseCode.SUCCESS.getCode() == baseResponse.getStatus()) {
            List<TestrptCoverpageDffSimpleDTO> dffList = baseResponse.getData();
            if(CollectionUtils.isNotEmpty(dffList))
            {
                for (TestrptCoverpageDffSimpleDTO sample:dffList)
                {
                    DFFInfoDTO dffInfoDTO = new DFFInfoDTO();
                    dffInfoDTO.setFieldName(sample.getFieldCode());
                    dffInfoDTO.setValue(sample.getDisplayData());
                    dffInfoDTO.setLabel(sample.getLabelname());
                    dffInfoDTO.setSeq(sample.getDisplayedSeqInReport());
                    dffInfoDTO.setDataType(sample.getFieldType());
                    dffFields.add(dffInfoDTO);
                }
            }
        }
        digitalReportDataSourceDTO2.getVariableDataList().setDff(dffFields);
    }

    private void initSampleList(DigitalReportDataSourceDTO2 digitalReportDataSourceDTO2,OrderAllDTO orderAllDTO){
        // 查询订单下的ProductList
        OrderIdReq orderIdReq = new OrderIdReq();
        orderIdReq.setOrderNo(orderAllDTO.getOrderNo());
        orderIdReq.setProductLineCode(orderAllDTO.getLabDTO().getBuCode());
        BaseResponse<List<ProductInstanceDTO>> productInstanceListRes =  orderFacade.queryProductInstaceForPe(orderIdReq);
        if(Func.isEmpty(productInstanceListRes)|| Func.isEmpty(productInstanceListRes.getData())){
            return;
        }
        List<ProductInstanceDTO> productDtoList = productInstanceListRes.getData();
        productDtoList = productDtoList.stream().filter(i->Func.isNotEmpty(i.getHeaderID())).sorted(Comparator.comparing(ProductInstanceDTO::getSampleID)).collect(Collectors.toList());
        Set<String> formIdSets = productDtoList.stream().map(ProductInstanceDTO::getdFFFormID).collect(Collectors.toSet());
        List<SampleDTO> sampleList =new ArrayList<>();
        if(Func.isNotEmpty(formIdSets)){
            String primaryLanguageCode = frameWorkClient.getPrimaryLanguageCode(ProductLineContextHolder.getProductLineCode());
            int primaryLanguageId = LanguageType.findCode(primaryLanguageCode).getLanguageId();
            List<DffFormAttrDTO> allDffFormAttrDTOList = dffClient.getDffFormAttrByDffFormIdList(formIdSets);
            List<DffFormAttrDTO> dffFormAttrDTOListEn = new ArrayList<>();
            List<DffFormAttrDTO> dffFormAttrDTOListCn = new ArrayList<>();
            List<DffFormAttrDTO> defaultLanguageDffFormAttrDTOList = new ArrayList<>();
            if(Func.isNotEmpty(allDffFormAttrDTOList)){
                dffFormAttrDTOListEn = allDffFormAttrDTOList.stream().filter(i -> LanguageType.check(i.getLanguageID(), LanguageType.English)).collect(Collectors.toList());
                dffFormAttrDTOListCn = allDffFormAttrDTOList.stream().filter(i -> LanguageType.check(i.getLanguageID(), LanguageType.Chinese)).collect(Collectors.toList());
                defaultLanguageDffFormAttrDTOList = allDffFormAttrDTOList.stream().filter(i -> Func.isNotEmpty(i.getLanguageID()) && i.getLanguageID()==primaryLanguageId).collect(Collectors.toList());
                if(Func.isEmpty(defaultLanguageDffFormAttrDTOList)){
                    defaultLanguageDffFormAttrDTOList = allDffFormAttrDTOList.stream().filter(i -> Func.isNotEmpty(i.getLanguageID()) && i.getLanguageID()!=primaryLanguageId).collect(Collectors.toList());
                }
            }
            List<ProductInstanceDTO> productInstanceDTOListEn = productDtoList.stream().filter(i -> LanguageType.check(i.getLanguageID(), LanguageType.English)).collect(Collectors.toList());
            List<ProductInstanceDTO> productInstanceDTOListCn = productDtoList.stream().filter(i -> LanguageType.check(i.getLanguageID(), LanguageType.Chinese)).collect(Collectors.toList());

            List<ProductInstanceDTO> defaultLanguageList = productDtoList.stream().filter(i -> Func.isNotEmpty(i.getLanguageID()) && i.getLanguageID()==primaryLanguageId).collect(Collectors.toList());
            if(Func.isEmpty(defaultLanguageList)){
                defaultLanguageList = productDtoList.stream().filter(i -> Func.isNotEmpty(i.getLanguageID()) && i.getLanguageID()!=primaryLanguageId).collect(Collectors.toList());
            }
            if(Func.isNotEmpty(defaultLanguageList)){
                for (ProductInstanceDTO productInstanceDTO : defaultLanguageList) {
                    SampleDTO sampleDTO = new SampleDTO();
                    sampleDTO.setSampleId(productInstanceDTO.getSampleID());
                    sampleDTO.setTemplateId("");
                    List<SampleAttrDTO> sampleAttrList = new ArrayList<>();
                    Map<String, Object> defaultProductMap = BeanUtil.beanToMap(productInstanceDTO, false, true);
                    if(Func.isNotEmpty(defaultLanguageDffFormAttrDTOList)){
                        defaultLanguageDffFormAttrDTOList = defaultLanguageDffFormAttrDTOList.stream().sorted(Comparator.comparing(item -> (Func.isEmpty(item.getSequence())?0:Integer.parseInt(item.getSequence())), Comparator.nullsLast(Integer::compareTo))).collect(Collectors.toList());
                        for (DffFormAttrDTO dffFormAttrDTO : defaultLanguageDffFormAttrDTOList) {
                            SampleAttrDTO sampleAttrDTO = new SampleAttrDTO();
                            sampleAttrDTO.setLabelName(dffFormAttrDTO.getDispalyName());
                            sampleAttrDTO.setLabelCode(dffFormAttrDTO.getFieldCode());
                            sampleAttrDTO.setCustomerLabel("");
                            sampleAttrDTO.setValue(Func.toStr(defaultProductMap.getOrDefault(StrUtil.lowerFirst(dffFormAttrDTO.getFieldCode()),null)));
                            sampleAttrDTO.setSeq(Func.toInt(dffFormAttrDTO.getSequence()));
                            sampleAttrDTO.setDataType(dffFormAttrDTO.getFieldType());
                            List<SampleAttrDTO.SampleAttrLanguage> languageList = new ArrayList<>();
                            //英文
                            ProductInstanceDTO productInstanceDTOEn = productInstanceDTOListEn.stream().filter(i -> Func.equals(i.getSampleID(), productInstanceDTO.getSampleID())).findAny().orElse(null);
                            if(Func.isNotEmpty(dffFormAttrDTOListEn) && Func.isNotEmpty(productInstanceDTOEn)){
                                DffFormAttrDTO dffFormAttrDTOEN = dffFormAttrDTOListEn.stream().filter(i -> StringUtils.equalsIgnoreCase(i.getFieldCode(), dffFormAttrDTO.getFieldCode())).findAny().orElse(null);
                                if(Func.isNotEmpty(dffFormAttrDTOEN)){
                                    Map<String, Object> productEnMap = BeanUtil.beanToMap(productInstanceDTOEn, false, true);
                                    SampleAttrDTO.SampleAttrLanguage sampleAttrLanguage = new SampleAttrDTO.SampleAttrLanguage();
                                    sampleAttrLanguage.setLabelName(dffFormAttrDTOEN.getDispalyName());
                                    sampleAttrLanguage.setValue(Func.toStr(productEnMap.getOrDefault(StrUtil.lowerFirst(dffFormAttrDTOEN.getFieldCode()),null)));
                                    sampleAttrLanguage.setLanguageId(LanguageType.English.getLanguageId());
                                    languageList.add(sampleAttrLanguage);
                                }
                            }

                            //中文
                            ProductInstanceDTO productInstanceDTOCn = productInstanceDTOListCn.stream().filter(i -> Func.equals(i.getSampleID(), productInstanceDTO.getSampleID())).findAny().orElse(null);
                            if(Func.isNotEmpty(dffFormAttrDTOListCn) && Func.isNotEmpty(productInstanceDTOCn)){
                                DffFormAttrDTO dffFormAttrDTOCN = dffFormAttrDTOListCn.stream().filter(i -> StringUtils.equalsIgnoreCase(i.getFieldCode(), dffFormAttrDTO.getFieldCode())).findAny().orElse(null);
                                if(Func.isNotEmpty(dffFormAttrDTOCN)){
                                    Map<String, Object> productCnMap = BeanUtil.beanToMap(productInstanceDTOCn, false, true);
                                    SampleAttrDTO.SampleAttrLanguage sampleAttrLanguage = new SampleAttrDTO.SampleAttrLanguage();
                                    sampleAttrLanguage.setLabelName(dffFormAttrDTOCN.getDispalyName());
                                    sampleAttrLanguage.setValue(Func.toStr(productCnMap.getOrDefault(StrUtil.lowerFirst(dffFormAttrDTOCN.getFieldCode()),null)));
                                    sampleAttrLanguage.setLanguageId(LanguageType.Chinese.getLanguageId());
                                    languageList.add(sampleAttrLanguage);
                                }
                            }
                            sampleAttrDTO.setLanguageList(languageList);
                            sampleAttrList.add(sampleAttrDTO);
                        }
                    }
                    sampleDTO.setSampleAttrList(sampleAttrList);
                    sampleList.add(sampleDTO);
                }
            }
        }
        logger.info("sampleList:{}",JSON.toJSONString(sampleList));
        digitalReportDataSourceDTO2.getVariableDataList().setSampleList(sampleList);
    }

    private void initReport2(DigitalReportDataSourceDTO2 digitalReportDataSourceDTO2,OrderAllDTO orderAllDTO) {
        ReportInfoDTO reportInfoDTO = new ReportInfoDTO();
        if(Func.isNotEmpty(orderAllDTO)){
            ReportReceiverReq reportReceiverReq = new ReportReceiverReq();
            reportReceiverReq.setOrderNo(orderAllDTO.getOrderNo());
            reportReceiverReq.setProductLineCode(orderAllDTO.getProductLineCode());
            BaseResponse<ReportReceiverRsp> reportReceiverRsp = orderFacade.getReportReceiverInfo(reportReceiverReq);
            if(Func.isNotEmpty(reportReceiverRsp)&&Func.isNotEmpty(reportReceiverRsp.getData())){
                ReportReceiverRsp reportReceiver = reportReceiverRsp.getData();
                reportInfoDTO.setReportHeaderAddress(reportReceiver.getReportAddressEN());
                reportInfoDTO.setReportAddressCN(reportReceiver.getReportAddressCN());
                reportInfoDTO.setReportHeader(reportReceiver.getReportHeaderEN());
                reportInfoDTO.setReportHeaderCN(reportReceiver.getReportHeaderCN());
            }
        }
        digitalReportDataSourceDTO2.getVariableDataList().setReport(reportInfoDTO);

    }
        private void initReport(DigitalReportDataSourceDTO digitalReportInfo, GenerateReportRequest reportRequset){
        ReportInfoDTO reportInfoDTO = new ReportInfoDTO();
        reportInfoDTO.setExternalReportNo(reportRequset.getExternalReportNo());
        OrderAllDTO order = reportRequset.getOrder();
        if(Func.isNotEmpty(order)){
            // orderNo
            String externalOrderNo = orderClient.getExternalOrderNo(order.getOrderNo());
            reportInfoDTO.setOrderNo(externalOrderNo);
            // jobExpectDueDate
            reportInfoDTO.setJobExpectDueDate(Func.isNotEmpty(order.getJobExpectDueDate())?Func.formatDateTime(order.getJobExpectDueDate()):null);
            // sgsReferenceOrderNo
            //order.operationtype = 5 取oldreference nobn                                        zAAAAAAAA?

            assembleReportReferenceNo(reportInfoDTO, order.getOrderNo());

            // sampleConfirmDate
            reportInfoDTO.setSampleConfirmDate(Func.isNotEmpty(order.getSampleConfirmDate())?Func.formatDateTime(order.getSampleConfirmDate()):null);
            // ActualStartDate
            reportInfoDTO.setServiceStartDate(Func.isNotEmpty(order.getServiceStartDate())?Func.formatDateTime(order.getServiceStartDate()):null);
            // ActualCompleteDate
            //reportInfoDTO.setActualCompleteDate(Func.isNotEmpty(order.getModifitedDate())?Func.formatDateTime(order.getModifitedDate()):null);
            // orderClosedDate
            //reportInfoDTO.setOrderClosedDate(Func.isNotEmpty(order.getModifitedDate())?Func.formatDateTime(order.getModifitedDate()):null);
            // orderCreatedDate
            reportInfoDTO.setOrderCreatedDate(Func.isNotEmpty(order.getCreatedDate())?Func.formatDateTime(order.getCreatedDate()):null);
            // responsibleCS
            reportInfoDTO.setResponsibleCS(order.getcSName());
            reportInfoDTO.setResponsibleCSEmail(order.getcSEmail());
            reportInfoDTO.setResponsibleCSTel(order.getcSContact());
            // responsibleTeam
            reportInfoDTO.setResponsibleTeam(order.getResponsibleTeamCode());
            //sampleReceivingDate
            reportInfoDTO.setSampleReceivingDate(Func.isNotEmpty(order.getSampleReceiveDate())?Func.formatDateTime(order.getSampleReceiveDate()):null);
            //orderExpectDueDate
            reportInfoDTO.setOrderExpectDueDate(Func.isNotEmpty(order.getExpectedOrderDueDate())?Func.formatDateTime(order.getExpectedOrderDueDate()):null);
            //reportDueDate
            reportInfoDTO.setReportDueDate(Func.isNotEmpty(reportRequset.getReportDueDate())?Func.formatDateTime(reportRequset.getReportDueDate()):null);
            //sampleResubmissionDate
            reportInfoDTO.setSampleResubmissionDate(Func.isNotEmpty(order.getSampleResubmissionDate())?Func.formatDateTime(order.getSampleResubmissionDate()):null);
            //furtherInformationReceivingDate
            reportInfoDTO.setFurtherInformationReceivingDate(Func.isNotEmpty(order.getFurtherInformationReceivingDate())?Func.formatDateTime(order.getFurtherInformationReceivingDate()):null);
            //OrderConfirmDate
            reportInfoDTO.setOrderConfirmDate(Func.isNotEmpty(order.getOrderConfirmDate())?Func.formatDateTime(order.getOrderConfirmDate()):null);
        }
        if(Func.isNotEmpty(reportRequset.getReportNo())){
            // TODO 后边需要整体优化一下，现在因为字段不能保证都能取到，先保持原有的方法实现
            // 查询report 基本信息
            ReportDTO reportDto = reportMapper.getByReportNo(reportRequset.getReportNo());
            GpnOrderReportDetailDTO reportDTO = reportMapper.queryReportDetailByReportId(reportDto.getId());
            //20230824 新需求 加入CertificateId字段 去查询CloudKey最新数据
            if(Func.isNotEmpty(reportDTO.getCertificateId())){
                String labcode = reportDTO.getLabCode();
                AccreditationRsp accreditationRsp = frameWorkClient.getAccreditationInfo(labcode,reportDTO.getAccreditationLogoPath());
                List<AccreditationVO> accreditationVOList = accreditationRsp.getRows();
                if(Func.isNotEmpty(accreditationVOList)){
                    for(AccreditationVO item : accreditationVOList){
                        if(item.getId().equals(reportDTO.getCertificateId())){
                            reportDTO.setAccreditationLogoPath(item.getFileCloudKey());
                            reportDTO.setAccreditationRemark(item.getAcceditationRemark());
                            reportDTO.setAccreditationStatement(item.getStatement());
                        }
                    }
                }
            }
            String countryOfDestination = reportDto.getCountryOfDestination();
            String reportCountryOfDestinations = "";
            if(Func.isNotEmpty(countryOfDestination)){
                try {
                    List<ReportCountryOfDestinationDTO> reportCountryOfDestinationDTOS = JSONObject.parseArray(countryOfDestination, ReportCountryOfDestinationDTO.class);
                    if(Func.isNotEmpty(reportCountryOfDestinationDTOS)){
                        reportCountryOfDestinations = reportCountryOfDestinationDTOS.stream().filter(item -> NumberUtil.equals(item.getLanguageId(), reportRequset.getLanguageId()))
                                .sorted(Comparator.comparing(ReportCountryOfDestinationDTO::getSequence,Comparator.nullsLast(Integer::compareTo)))
                                .map(ReportCountryOfDestinationDTO::getValue).collect(Collectors.joining(","));
                    }
                } catch (Exception e) {
                    logger.error("parse reportCountryOfDestination Json Error:{}",e);
                }
            }
            reportInfoDTO.setReportCountryOfDestination(reportCountryOfDestinations);
            List<ReportCertificateBO> reportCertificateBOList = reportMapper.queryReportCertificate(reportDto.getId());
            if(Func.isNotEmpty(reportCertificateBOList)){
                reportCertificateBOList =reportCertificateBOList.stream().filter(item->Func.equalsSafe(Func.toStr(item.getStatus()),"1")).collect(Collectors.toList());
            }
            List<DataDictionary> dataDictionaryList = frameWorkClient.getDataDictionaryList(SgsSystem.GPO.getSgsSystemId() + "", Constants.DATADICTIONARY_TYPE.CERTIFICATE_TYPE, Func.toStr(reportRequset.getOrder().getBUID()));
            for (ReportCertificateBO certificateBO : reportCertificateBOList) {
                if(Func.isNotEmpty(dataDictionaryList)){
                    DataDictionary dataDictionary = dataDictionaryList.stream().filter(item -> Func.equalsSafe(item.getSysKey(), certificateBO.getCertificateType())).findAny().orElse(null);
                    if(Func.isNotEmpty(dataDictionary)){
                        certificateBO.setCertificateTypeDisplay(dataDictionary.getSysValue());
                    }
                }
            }
            reportInfoDTO.setReportCertificateList(reportCertificateBOList);
            //处理外部号
            String externalReportNo = orderClient.getExternalReportNo(order.getOrderNo(), reportDTO.getReportNo());
            reportInfoDTO.setReportNo(externalReportNo);
            reportInfoDTO.setAccreditationLogo(Constants.CLOUD_PREFIX+reportDTO.getAccreditationLogoPath());
            reportInfoDTO.setAccreditationRemark(reportDTO.getAccreditationRemark());
            reportInfoDTO.setStatement(reportDTO.getAccreditationStatement());
            reportInfoDTO.setApproverDate(Func.isNotEmpty(reportDTO.getApprovedDate())?Func.formatDateTime(reportDTO.getApprovedDate()):null);
            if(LanguageType.check(reportRequset.getLanguageId(),LanguageType.English)){
                if(Func.isNotEmpty(reportDTO.getQrcodeEnPath()) && !Func.equalsSafe(ReportRequirementEnum.Sub_Report_Word.getCode(),order.getReportRequirement())&&
                        !Func.equalsSafe(ReportRequirementEnum.Customer_Report_Word.getCode(),order.getReportRequirement())) {
                    reportInfoDTO.setQrcodePath(Constants.CLOUD_PREFIX + reportDTO.getQrcodeEnPath());
                }
            }else {
                //GPO2-1481 如果出双语报告，则中文报告的报告号、文件名加’_CN’
                if (ReportLanguageType.DUAL.getCode().equals(reportRequset.getReportLanguageType())) {
                    reportInfoDTO.setReportNo(externalReportNo+"_CN");
                }
                if(Func.isNotEmpty(reportDTO.getQrcodeCnPath()) && !Func.equalsSafe(ReportRequirementEnum.Sub_Report_Word.getCode(),order.getReportRequirement())&&
                        !Func.equalsSafe(ReportRequirementEnum.Customer_Report_Word.getCode(),order.getReportRequirement())) {
                    reportInfoDTO.setQrcodePath(Constants.CLOUD_PREFIX + reportDTO.getQrcodeCnPath());
                }
            }
            //查询report header 信息
            OrderIdReq orderIdReq = new OrderIdReq();
            orderIdReq.setOrderId(reportRequset.getOrder().getOrderId());
            orderIdReq.setProductLineCode(reportRequset.getProductLineCode());
            OrderReportReceiverDTO orderReportReceiverDTO = orderReportFacade.getOrderReportReceiveByOrderId(orderIdReq).getData();
            if(StringUtils.isNotEmpty(orderReportReceiverDTO.getReportHeader()))
            {
                int headInx = orderReportReceiverDTO.getReportHeader().indexOf("|||");
                String headTemp = orderReportReceiverDTO.getReportHeader();
                if(headInx !=-1)
                {
                    String head = LanguageType.check(reportRequset.getLanguageId(),LanguageType.English)?
                            headTemp.substring(headInx+3):headTemp.substring(0,headInx);
                    reportInfoDTO.setReportHeader(head);
                    List<String> reportHeaderList = Arrays.asList(orderReportReceiverDTO.getReportHeader().split("\\|\\|\\|"));
                    if(reportHeaderList.size()>1){
                        reportInfoDTO.setReportHeaderCN(reportHeaderList.get(0));
                    }
                }else {
                    reportInfoDTO.setReportHeader(headTemp);
                }
            }
            if(StringUtils.isNotEmpty(orderReportReceiverDTO.getReportDeliveredTo()))
            {
                int headInx = orderReportReceiverDTO.getReportDeliveredTo().indexOf("|||");
                String deliverTemp = orderReportReceiverDTO.getReportDeliveredTo();
                if(headInx != -1)
                {
                    deliverTemp = LanguageType.check(reportRequset.getLanguageId(),LanguageType.English)?
                            deliverTemp.substring(headInx+3):deliverTemp.substring(0,headInx);
                    List<String> reportAddressList = Arrays.asList(orderReportReceiverDTO.getReportDeliveredTo().split("\\|\\|\\|"));
                    if(reportAddressList.size()>1){
                        reportInfoDTO.setReportAddressCN(reportAddressList.get(0));
                    }
                }
                reportInfoDTO.setReportDeliveredTo(deliverTemp);
                reportInfoDTO.setReportHeaderAddress(deliverTemp);
            }

            //判断报告是否为翻译件
            if (Func.isNotEmpty(order) && Func.isNotEmpty(order.getOperationType()) && Func.equals(OperationType.TranslationReport.getStatus(),order.getOperationType())){
                if (Func.isNotEmpty(reportDto.getReportTypeId())){
                    //查询reportType
                    ReportTypePO reportTypePO = reportTypeMapper.selectByPrimaryKey(reportDto.getReportTypeId());
                    if(Func.isNotEmpty(reportTypePO)){
                        String amendRemarkRule = reportTypePO.getAmendRemarkRule();
                        //外部号处理
                        String externalRefReportNo = orderClient.getExternalReportNo(order.getRefReportNo());
                        //字符替换
                        String translationReportStatement = this.getAmendRemark(amendRemarkRule,externalRefReportNo,null);
                        reportInfoDTO.setTranslationReportStatement(translationReportStatement);
                    }
                }
            }

            //判断是否是Amend产生的新单 GPO2-13493
            if (Func.isNotEmpty(order) && Func.isNotEmpty(order.getOperationType()) &&  OperationType.check(order.getOperationType(),OperationType.Extract,OperationType.TranslationReport) && Func.isNotEmpty(reportDto.getRootReportNo())) {
                //取RootReportNo对应的ApproveDate
                ReportDTO rootReport = reportMapper.getByReportNo(reportDto.getRootReportNo());
                if(Func.isNotEmpty(rootReport) && Func.isNotEmpty(rootReport.getApproverDate())){
                    reportInfoDTO.setActualCompleteDate(Func.formatDateTime(rootReport.getApproverDate()));
                }
                // GPO2-14300 测试开始日期取OldReportNo对应的order confirm date
                OrderInfoDto rootOrderDto = orderClient.getOrderInfoByOrderNo(rootReport.getOrderNo());
                if(Func.isNotEmpty(rootOrderDto)){
                    reportInfoDTO.setOrderConfirmDate(Func.isNotEmpty(rootOrderDto.getOrderConfirmDate())?Func.formatDateTime(rootOrderDto.getOrderConfirmDate()):null);
                }
            }

            //判断是否是rework的单子--GPO2-6952
            ReportDTO parentReport = reportMapper.getByReportNo(reportDto.getParentReportNo());
            if (Func.isNotEmpty(reportDto.getParentReportNo()) && Func.isNotEmpty(parentReport) && ReportStatus.check(parentReport.getReportStatus(),ReportStatus.Reworked) && reportDto.getReportNo().contains("-")){
                //取源单的approveDate
                String reportNoSourceNo = reportDto.getReportNo().substring(0,reportDto.getReportNo().indexOf("-"));
                if (Func.isNotEmpty(reportNoSourceNo)){
                    //查询源单信息
                    ReportDTO reportNoSource = reportMapper.getByReportNo(reportNoSourceNo);
                    if (Func.isNotEmpty(reportNoSource) && Func.isNotEmpty(reportNoSource.getApproverDate())){
                        reportInfoDTO.setActualCompleteDate(Func.formatDateTime(reportNoSource.getApproverDate()));
                    }
                }
            }
            reportInfoDTO.setQualificationType(this.getQualificationType(reportDTO.getAccreditationLogoPath(),order.getLabDTO().getLabCode()));
        }
        if(Func.isNotEmpty(order.getLabDTO())) {
            reportInfoDTO.setLabName(order.getLabDTO().getLabName());
        }
        if(Func.equals(reportRequset.getReportActionType(),ReportActionType.GENERATE_PRELIM_RESULT.getCode())){
            reportInfoDTO.setPrelimResultNo(reportRequset.getObjectNo());
            reportInfoDTO.setPrelimResultCreateDate(Func.formatDate(new Date()));
        }
        //报告页脚图片
        //判断中英文
        String locationCode = reportRequset.getOrder().getLocationCode() + " " + reportRequset.getProductLineCode();
        reportInfoDTO.setLabClaimer(Constants.CLOUD_PREFIX + frameWorkClient.getLabClaimerCn(locationCode,reportRequset.getLanguageId()));
        LabClaimer labClaimer =  frameWorkClient.getLabGBClaimer(reportRequset.getLanguageId(),order.getBUID(),order.getLocationID());
        if(Func.isNotEmpty(labClaimer)){
                if(Func.isNotEmpty(labClaimer.getLabGBClaimer())){
                    reportInfoDTO.setLabGBClaimer(Constants.CLOUD_PREFIX +labClaimer.getLabGBClaimer());
                }
                if(Func.isNotEmpty(labClaimer.getGbCompanyChop())){
                    reportInfoDTO.setGbCompanyChop(Constants.CLOUD_PREFIX +labClaimer.getGbCompanyChop());
                }
        }
        digitalReportInfo.getVariableDataList().setReport(reportInfoDTO);
    }

    private  void assembleReportReferenceNo(ReportInfoDTO reportInfoDTO, String orderNo) {
        OrderIdReq orderIdReq = new OrderIdReq();
        orderIdReq.setOrderNo(orderNo);
        orderIdReq.setProductLineCode(ProductLineContextHolder.getProductLineCode());
        List<OrderReferenceNoRsp> orderReferenceNoRsps = orderFacade.getReferenceNoByOrderNo(orderIdReq).getData();
        String referenceNo = "";
        if(Func.isNotEmpty(orderReferenceNoRsps)){
            OrderReferenceNoRsp orderReferenceNoRsp = orderReferenceNoRsps.get(0);

            String sgsReferenceOrderNo = orderReferenceNoRsp.getReferenceOrderNo();
            reportInfoDTO.setSgsRefrenceOrderNo(Func.toStr(sgsReferenceOrderNo));
            String customerReferenceNo = orderReferenceNoRsp.getCustomerReferenceNo();
            reportInfoDTO.setCustomerReferenceNo(Func.toStr(customerReferenceNo));
            if(Func.isNotEmpty(reportInfoDTO.getExternalReportNo())){
                referenceNo+=reportInfoDTO.getExternalReportNo();
            }
//            else if(Func.isNotEmpty(orderReferenceNoRsp.getSgsReferenceNo())){
//                referenceNo +=orderReferenceNoRsp.getSgsReferenceNo();
//            }
            else if(Func.isNotEmpty(sgsReferenceOrderNo)){
                referenceNo +=sgsReferenceOrderNo;
            }
            if(Func.isNotEmpty(customerReferenceNo)){
                referenceNo += StrPool.COMMA;
                referenceNo += customerReferenceNo;
            }
        }
        if(StrUtil.endWith(referenceNo,StrPool.COMMA)){
            referenceNo = StrUtil.removeSuffix(referenceNo,StrPool.COMMA);
        }
        if(StrUtil.startWith(referenceNo,StrPool.COMMA)){
            referenceNo = StrUtil.removePrefix(referenceNo,StrPool.COMMA);
        }
        reportInfoDTO.setReferenceNo(referenceNo);
    }

    /**
     *
     * @param
     * @return
     */
    private List<QualificationTypeValue> getQualificationType(String certificateFileCloudKey,String labCode){
        List<QualificationTypeValue> qualificationTypeValues = Lists.newArrayList();
        if(Func.isEmpty(labCode)){
            return qualificationTypeValues;
        }
        //调用CS接口获取accreditationTypeList
        AccreditationRsp accreditationInfo = frameWorkClient.getAccreditationInfo(labCode, certificateFileCloudKey);
        if(Func.isNotEmpty(accreditationInfo) && Func.isNotEmpty(accreditationInfo.getRows())){
            List<AccreditationVO> accreditationInfoRows = accreditationInfo.getRows();
            AccreditationVO accreditationVO = accreditationInfoRows.stream().filter(item -> {
                return StringUtils.equalsIgnoreCase(certificateFileCloudKey, item.getFileCloudKey());
            }).findFirst().orElse(null);
            if(Func.isNotEmpty(accreditationVO) && Func.isNotEmpty(accreditationVO.getAccreditationTypeList())){
                List<String> accreditationTypeList = accreditationVO.getAccreditationTypeList();
                for (String accreditationType : accreditationTypeList) {
                    QualificationTypeValue qualificationTypeValue = new QualificationTypeValue();
                    qualificationTypeValue.setType(accreditationType);
                    qualificationTypeValues.add(qualificationTypeValue);
                }
            }
        }
        return qualificationTypeValues;
    }


    private String getAmendRemark(String amendRemarkRule,String reportNo,String Date){
        if (Func.isEmpty(amendRemarkRule)){
            return null;
        }
        if (amendRemarkRule.toLowerCase().contains("{reportno}")){
            amendRemarkRule = amendRemarkRule.replaceAll("\\{ReportNo\\}",reportNo);
        }
        return amendRemarkRule;
    }
    private void initProtocolData(DigitalReportDataSourceDTO digitalReportInfo, GenerateReportRequest reportRequest){
        String reportNo = reportRequest.getReportNo();
        // 查询ReportMatrix
        List<ReportMatrixDTO> reportMatrixDTOList = reportMatrixRelMapper.getReportMatrixListByReportNo(reportNo);
        if(Func.isEmpty(reportMatrixDTOList)){
            return;
        }
        String buCode = ProductLineContextHolder.getProductLineCode();
        if(Func.isEmpty(buCode)){
            return;
        }
        String token = tokenClient.getToken();
        if(Func.isEmpty(token)){
            return;
        }
        GeneralReportDataReq req = new GeneralReportDataReq();
        req.setReportNo(reportNo);
        req.setProductLineCode(buCode);
        req.setToken(token);
        req.setOrderNo(reportRequest.getOrderNo());
        Map<String, List<ReportMatrixDTO>> testMatrixMap = reportMatrixDTOList.stream().collect(Collectors.groupingBy(ReportMatrixDTO::getTestLineInstanceId));
        if(Func.isEmpty(testMatrixMap)){
            return;
        }
        List<GeneralReportMatrixDataReq> reportMatrixList = Lists.newArrayList();
        testMatrixMap.forEach((key,val)->{
            if(Func.isEmpty(val)){
                return;
            }
            GeneralReportMatrixDataReq generalReportMatrixDataReq = new GeneralReportMatrixDataReq();
            generalReportMatrixDataReq.setTestLineInstanceId(key);
            generalReportMatrixDataReq.setTestMatrixIds(val.stream().map(ReportMatrixDTO::getTestMatrixId).collect(Collectors.toSet())
                    .stream().collect(Collectors.toList()));
            reportMatrixList.add(generalReportMatrixDataReq);
        });
        req.setReportMatrixs(reportMatrixList);
        ProtocolReportRespDTO protocolReportRes = dataEntryClient.getProtocolReportData(req);
        if(Func.isNotEmpty(protocolReportRes) && Func.isNotEmpty(protocolReportRes.getProtocolStructure())){
            // 设置Conclusion
            List<ProtocolStructureRespDTO> protocolStructures = protocolReportRes.getProtocolStructure();
            protocolStructures.stream().forEach(ps->{
                List<GenerateReportRspDTO> testResultList = ps.getTestResult();
                if(Func.isNotEmpty(testResultList)){
                    testResultList.stream().forEach(testResult->{
                        String testMatrixId = null;
                        List<DataEntryTableMatrixConclusionRespDTO> conclusions = testResult.getConclusions();
                        List<DataEntryTableConclusionListRespDTO> conclusionList = testResult.getConclusionList();
                        if(Func.isNotEmpty(conclusions) && Func.isNotEmpty(conclusionList)){
                            if(Func.isNotEmpty(testResult.getTestLine()) && Func.isNotEmpty(testResult.getTestLine().getConditionGroupList())){
                                DataEntryTableConditionGroupRespDTO conditionGroupResp = testResult.getTestLine().getConditionGroupList().get(0);
                                if(Func.isNotEmpty(conditionGroupResp.getMatrixList())){
                                    DataEntryTableMatrixRespDTO matrixRespDTO = conditionGroupResp.getMatrixList().get(0);
                                    testMatrixId = matrixRespDTO.getTestMatrixId();
                                }
                            }
                            // 基于testMatrixId匹配
                            if(Func.isNotEmpty(testMatrixId)){
                                String finalTestMatrixId = testMatrixId;
                                DataEntryTableMatrixConclusionRespDTO matrixConclusion = conclusions.stream().filter(conclusion->Func.equalsSafe(conclusion.getTestMatrixId(), finalTestMatrixId)).findAny().orElse(null);
                                if(Func.isNotEmpty(matrixConclusion)){
                                    conclusionList.stream().forEach(conclusion->{
                                        if(Func.isNotEmpty(conclusion.getConclusionSettingList())){
                                            DataEntryTableConclusionSettingRespDTO conclusionSettingRes = conclusion.getConclusionSettingList().stream().filter(setting->Func.equalsSafe(setting.getConclusionSettingId(),matrixConclusion.getConclusionSettingId()))
                                                    .findAny().orElse(null);
                                            if(Func.isNotEmpty(conclusionSettingRes)){
                                                testResult.getTestLine().setConclusion(conclusionSettingRes.getDisplayDesc());
                                                return;
                                            }
                                        }
                                    });
                                }
                            }
                        }
                    });
                }
            });
//            digitalReportInfo.getVariableDataList().setProtocolStructure(protocolStructures);
        }

    }

    private void assembleTestLineFromSoda(DigitalReportDataSourceDTO digitalReportInfo, GenerateReportRequest reportRequest){
        String reportNo = reportRequest.getReportNo();
        // 查询ReportMatrix
        List<ReportMatrixDTO> reportMatrixDTOList = reportMatrixRelMapper.getReportMatrixListByReportNo(reportNo);
        if(Func.isEmpty(reportMatrixDTOList)){
            return;
        }
        String buCode = ProductLineContextHolder.getProductLineCode();
        if(Func.isEmpty(buCode)){
            return;
        }
        String token = tokenClient.getToken();
        if(Func.isEmpty(token)){
            return;
        }
        List<String> testMatrixIdList = reportMatrixDTOList.stream().map(ReportMatrixDTO::getTestMatrixId).distinct().collect(Collectors.toList());
        DataEntryListReq req = new DataEntryListReq();
        req.setProductLineCode(buCode);
        req.setToken(token);
        req.setTestMatrixIdList(testMatrixIdList);
        if(Func.isNotEmpty(reportRequest.getTemplate())
                && ProtocolStructureFlag.check(reportRequest.getTemplate().getProtocolStructureFlag(),ProtocolStructureFlag.Protocol)){
            req.setDataEntryMode(DataEntryMode.ProtocolResultEntry.getMode());
        }
        DigitalReportBO dataEntryDTO = dataEntryClient.queryDataEntryList(req);
        if(Func.isNotEmpty(dataEntryDTO)){
            VariableDataList variableDataList = digitalReportInfo.getVariableDataList();
            variableDataList.setHeader(dataEntryDTO.getHeader());
            variableDataList.setTestSampleList(dataEntryDTO.getTestSampleList());
            variableDataList.setTestLineList(dataEntryDTO.getTestLineList());
            variableDataList.setConditionGroupList(dataEntryDTO.getConditionGroupList());
            variableDataList.setReportMatrixList(dataEntryDTO.getReportMatrixList());
            variableDataList.setReportConclusionList(dataEntryDTO.getReportConclusionList());
            variableDataList.setTestData(dataEntryDTO.getTestData());
        }
    }

    private void initTestResult(DigitalReportDataSourceDTO digitalReportInfo, GenerateReportRequest reportRequest){
        List<TestResultInfoDTO> testLineInfoDTOS = new ArrayList<>();
        // 添加TestResult
        ReportFilePO reportFilePO = new ReportFilePO();
        reportFilePO.setReportFileType(ReportFileType.TestResult.getCode());
        reportFilePO.setReportID(reportRequest.getReportId());
        Integer reportFileLanguage =  reportRequest.getLanguageId();
        List<ReportFilePO> testReportList = reportFileExtMapper.getReportFileInfoList(reportFilePO);
        // testResult
        if(Func.isNotEmpty(testReportList)&&com.sgs.otsnotes.facade.model.enums.ReportActionType.GENERATE_REPORT.getCode().equals(reportRequest.getReportActionType())){
           List<ReportFilePO> temp = testReportList.stream().filter(reportFilePO1->(Func.equals(reportFilePO1.getLanguageID(),reportFileLanguage))).collect(Collectors.toList());
            if(Func.isNotEmpty(temp)) {
                TestResultInfoDTO testLineInfoDTO = new TestResultInfoDTO();
                testLineInfoDTO.setCloudId(Constants.CLOUD_PREFIX + temp.get(0).getCloudID());
                testLineInfoDTO.setType(TestResultItemType.TEST_RESULT.getType());
                testLineInfoDTOS.add(testLineInfoDTO);
            }
        }
        TLInstanceDetailReq tlInstanceDetailReq = new TLInstanceDetailReq();
        tlInstanceDetailReq.setOrderNo(reportRequest.getOrderNo());
        tlInstanceDetailReq.setReportId(reportRequest.getReportId());
        BaseResponse<List<TLInstanceDetailInfo>> response = testLineService.getTLInstanceDetails(tlInstanceDetailReq);
        if(ResponseCode.SUCCESS.getCode() == response.getStatus())
        {
            List<TLInstanceDetailInfo>  testLineList = response.getData();
            if(CollectionUtils.isNotEmpty(testLineList)) {
                //查询测试中照片
                List<TestSamplePhotoInfoDTO> samplePhotoInfoDTOS = this.getTestedSamplePhoto(reportRequest.getOrderNo(),reportRequest.getReportId(),testLineList.stream().map(TLInstanceDetailInfo :: getId).collect(Collectors.toList()),reportRequest.getLanguageId());
                Map<String,List<TestSamplePhotoInfoDTO>> groupByTlId = null;
                if (Func.isNotEmpty(samplePhotoInfoDTOS)){
                    groupByTlId = samplePhotoInfoDTOS.stream().collect(Collectors.groupingBy(TestSamplePhotoInfoDTO :: getTestLineInstanceId));
                }
                // 查询SODA系统TL信息
                Map<String,GenerateReportRspDTO> generateReportRspMap = this.getTestLineInfo(reportRequest.getReportNo());
                for(int i=0;i<testLineList.size();i++) {
                    TestResultInfoDTO testLineInfoDTO = new TestResultInfoDTO();
                    testLineInfoDTO.setSeq(i+1);
                    TLInstanceDetailInfo currentTlInstance = testLineList.get(i);
                    TestLineNameInfo testLineNameInfo = currentTlInstance.getTestLineName();
                    TestLineNameLanguageInfo testItemNameCN = null;
                    if(Func.isNotEmpty(testLineNameInfo.getLanguages())){
                        testItemNameCN = testLineNameInfo.getLanguages().stream().filter(language -> {return Func.equals(language.getLanguageId(), LanguageType.Chinese.getLanguageId());}).findFirst().orElse(null);
                    }
                    CitationInfo citationInfo = currentTlInstance.getCitation();
                    CitationLanguageInfo citationCN = null;
                    if(Func.isNotEmpty(citationInfo.getLanguages())){
                        citationCN = citationInfo.getLanguages().stream().filter(language -> {return Func.equals(language.getLanguageId(), LanguageType.Chinese.getLanguageId());}).findFirst().orElse(null);
                    }
                    String testItemName = null;

                    if(LanguageType.check(reportRequest.getLanguageId(),LanguageType.English) || Func.isEmpty(testItemNameCN)){
                        testItemName = testLineNameInfo.getTestLineName();
                    }else {
                        testItemName = testItemNameCN.getTestLineName();
                    }
                    //判断一下非空
                    String standName = null;
                    if(LanguageType.check(reportRequest.getLanguageId(),LanguageType.English) || Func.isEmpty(citationCN)){
                        standName = citationInfo.getCitationName();
                    }else {
                        standName = citationCN.getCitationName();
                    }
//                        String standName = LanguageType.English.getCode().equals(reportRequest.getLanguage())
//                                ?citationEN.getCitationName():citationCN.getCitationName();
//                        String sectionName = LanguageType.English.getCode().equals(reportRequest.getLanguage())
//                                ?testLineList.get(i).getCitationSectionName():Func.isEmpty(testLineList.get(i).getCitationSectionNameCN())?testLineList.get(i).getCitationSectionName():testLineList.get(i).getCitationSectionNameCN();
                    //语言是中文则设置中文
                    testLineInfoDTO.setTestLineName(testItemName);
                    if(Func.isNotEmpty(currentTlInstance.getClientStandard())){
                        standName = currentTlInstance.getClientStandard() + " & " + standName;
                    }
                    testLineInfoDTO.setTestLineCitation(standName);
                    testLineInfoDTO.setCitationFullName(standName);

//                        testLineInfoDTO.setSectionName(sectionName);
                    testLineInfoDTO.setTestLineId(currentTlInstance.getTestLineId());
                    // 现在先传空值，以后会更新这里
                    testLineInfoDTO.setCombineCondition("");
                    testLineInfoDTO.setTestPhoto(groupByTlId.get(currentTlInstance.getId()));
                    if(ReportActionType.GENERATE_REPORT.getCode().equals(reportRequest.getReportActionType())) {
                        //TODO 区分分包 tl和非分包的TL
                        // dataInfo
                        testLineInfoDTO.setType(TestResultItemType.DATA_INFO.getType());
                    }
                    if(generateReportRspMap.containsKey(currentTlInstance.getId())){
                        GenerateReportRspDTO currentTlRsp = generateReportRspMap.get(currentTlInstance.getId());
                        testLineInfoDTO.setTestLine(currentTlRsp.getTestLine());
                        testLineInfoDTO.setTestDatas(currentTlRsp.getTestDatas());
                        testLineInfoDTO.setLimits(currentTlRsp.getLimits());
                        testLineInfoDTO.setSampleClaimRels(currentTlRsp.getSampleClaimRels());
                        testLineInfoDTO.setAnalyteList(currentTlRsp.getAnalyteList());
                        testLineInfoDTO.setTestPositions(currentTlRsp.getTestPositions());
                    }
                    testLineInfoDTOS.add(testLineInfoDTO);
                }
            }

        }
        // 添加SubReport
        if(com.sgs.otsnotes.facade.model.enums.ReportActionType.GENERATE_REPORT.getCode().equals(reportRequest.getReportActionType())) {
            SubReportReq subReportReq = new SubReportReq();
            subReportReq.setReportNo(reportRequest.getReportNo());
            if(ReportLanguage.checkLanguage(reportRequest.getOrderReportLanguage(),ReportLanguage.MultilingualReport)){
                subReportReq.setLanguageId(LanguageType.EnglishAndChinese.getLanguageId());
            }else{
                subReportReq.setLanguageId(reportRequest.getLanguageId());
            }
            subReportReq.setActiveStatus(true);
            List<SubReportInfo> subReportInfos = subReportMapper.getSubReports(subReportReq);
            List<SubReportInfo> sortSubReportInfos = new ArrayList<>();
            // 优先取Report直接关联的SubReport
            if (Func.isNotEmpty(subReportInfos)) {
                //排序 JobReport在前面,分包出去的在后面
                List<SubReportInfo> jobReportList = subReportInfos.stream().filter(i -> SubReportObjectTypeEnums.check(i.getObjectType(), SubReportObjectTypeEnums.job)).collect(Collectors.toList());
                List<SubReportInfo> subReportInfoList = subReportInfos.stream().filter(i ->
                        !SubReportObjectTypeEnums.check(i.getObjectType(), SubReportObjectTypeEnums.job,SubReportObjectTypeEnums.subcontract)
                                && !ObjectType.check(i.getObjectType(),ObjectType.SubContract)).collect(Collectors.toList());
                if(Func.isNotEmpty(jobReportList)){
                    jobReportList = jobReportList.stream().sorted(Comparator.comparing(SubReportInfo::getObjectNo).thenComparing(SubReportInfo::getSubReportNo)  )
                            .collect(Collectors.toList());
                    sortSubReportInfos.addAll(jobReportList);
                }
                if(Func.isNotEmpty(subReportInfoList)){
                    sortSubReportInfos.addAll(subReportInfoList);
                }
                //处理分包单返回的
                List<SubReportInfo> subcontractReportList = subReportInfos.stream().filter(i -> SubReportObjectTypeEnums.check(i.getObjectType(), SubReportObjectTypeEnums.subcontract) || ObjectType.check(i.getObjectType(), ObjectType.SubContract)).collect(Collectors.toList());
                if (Func.isNotEmpty(subcontractReportList)){
                    Map<String,List<SubReportInfo>> groupBySubReport = subcontractReportList.stream().collect(Collectors.groupingBy(SubReportInfo :: getSubReportNo));
                    for (String subReport : groupBySubReport.keySet()){
                        List<SubReportInfo> subReports = groupBySubReport.get(subReport);
                        SubReportInfo testResultFile = subReports.stream().filter(subReportInfo -> ReportFileType.check(Integer.valueOf(subReportInfo.getReportFileType()),ReportFileType.TestResult)).findFirst().orElse(null);
                        SubReportInfo wordFile = subReports.stream().filter(subReportInfo -> ReportFileType.check(Integer.valueOf(subReportInfo.getReportFileType()),ReportFileType.Word)).findFirst().orElse(null);
                        if (Func.isNotEmpty(testResultFile)){
                            sortSubReportInfos.add(testResultFile);
                        }else if (Func.isNotEmpty(wordFile)){
                            sortSubReportInfos.add(wordFile);
                        }
                    }
                }
            }
            // 查询订单下匹配不到Report的SubReport数据
            SubReportReq subReportReqNew = new SubReportReq();
            subReportReqNew.setOrderNo(reportRequest.getOrderNo());
            List<SubReportInfo> subReportInfosNoMatrix = subReportMapper.getSubReportsNoMatrix(subReportReqNew);
            if(Func.isNotEmpty(subReportInfosNoMatrix)){
                sortSubReportInfos.addAll(subReportInfosNoMatrix);
            }
            if(Func.isNotEmpty(sortSubReportInfos)){
                Collection<SubReportInfo> subReportList  = uniqueSubReportByCloudId(sortSubReportInfos);
                for (SubReportInfo subReport : subReportList) {
                    TestResultInfoDTO testResultInfoDTO = new TestResultInfoDTO();
                    testResultInfoDTO.setCloudId(Constants.CLOUD_PREFIX + subReport.getCloudId());
                    if(isSubcontractReport(subReport)){
                        testResultInfoDTO.setType(TestResultItemType.SUBCONTRACT.getType());
                    }else{
                        testResultInfoDTO.setType(TestResultItemType.TEST_RESULT.getType());
                    }
                    // subReport
                    testLineInfoDTOS.add(testResultInfoDTO);
                }
            }
        }

        // 查询TestLineReport
//        TestLineAttachmentInfoExample example = new TestLineAttachmentInfoExample();
//        example.createCriteria().andReportIdEqualTo(reportRequest.getReportId()).andFileStatusEqualTo(1);
//        List<TestLineAttachmentInfoPO> testLineAttachmentList = testLineAttachmentInfoMapper.selectByExample(example);
//        if(Func.isNotEmpty(testLineAttachmentList)){
//            for (TestLineAttachmentInfoPO attachment : testLineAttachmentList) {
//                TestResultInfoDTO testResultInfoDTO = new TestResultInfoDTO();
//                testResultInfoDTO.setType(TestResultItemType.TEST_LINE_REPORT.getType());
//                testResultInfoDTO.setCloudId(attachment.getCloudId());
//                testLineInfoDTOS.add(testResultInfoDTO);
//            }
//        }
        digitalReportInfo.getVariableDataList().setTestResult(testLineInfoDTOS);

    }


    private Map<String,GenerateReportRspDTO> getTestLineInfo(String reportNo){
        Map<String,GenerateReportRspDTO> generateReportRspMap = Maps.newHashMap();
        // 查询ReportMatrix
        List<ReportMatrixDTO> reportMatrixDTOList = reportMatrixRelMapper.getReportMatrixListByReportNo(reportNo);
        if(Func.isEmpty(reportMatrixDTOList)){
            return null;
        }
        String buCode = ProductLineContextHolder.getProductLineCode();
        if(Func.isEmpty(buCode)){
            return null;
        }
        String token = tokenClient.getToken();
        if(Func.isEmpty(token)){
            return null;
        }
        GeneralReportDataReq req = new GeneralReportDataReq();
        req.setReportNo(reportNo);
        req.setProductLineCode(buCode);
        req.setToken(token);
        Map<String, List<ReportMatrixDTO>> testMatrixMap = reportMatrixDTOList.stream().collect(Collectors.groupingBy(ReportMatrixDTO::getTestLineInstanceId));
        if(Func.isEmpty(testMatrixMap)){
            return null;
        }
        List<GeneralReportMatrixDataReq> reportMatrixList = Lists.newArrayList();
        testMatrixMap.forEach((key,val)->{
            GeneralReportMatrixDataReq generalReportMatrixDataReq = new GeneralReportMatrixDataReq();
            if(Func.isEmpty(val)){
                return;
            }
            generalReportMatrixDataReq.setTestLineInstanceId(key);
            generalReportMatrixDataReq.setTestMatrixIds(val.stream().map(ReportMatrixDTO::getTestMatrixId).collect(Collectors.toSet())
                    .stream().collect(Collectors.toList()));
            reportMatrixList.add(generalReportMatrixDataReq);
        });
        req.setReportMatrixs(reportMatrixList);
        List<GenerateReportRspDTO> generateReportRspDTOList = dataEntryClient.getGeneralReportData(req);
        if(Func.isNotEmpty(generateReportRspDTOList)){
            generateReportRspDTOList.stream().forEach(res->{
                // reportUnit 处理
                if(Func.isNotEmpty(res.getAnalyteList())){
                    res.getAnalyteList().stream().forEach(analyte->{
                        if(Func.equalsSafe(AnalyteInstanceStatusEnum.NOUNIT.getName(),analyte.getReportUnit())){
                            analyte.setReportUnit(null);
                        }
                    });
                }
                DataEntryTableTestLineRespDTO dataEntryTableTestLineRes = res.getTestLine();
                if(Func.isNotEmpty(dataEntryTableTestLineRes)){
                    String testLineInstanceId = dataEntryTableTestLineRes.getTestLineInstanceId();
                    if(!generateReportRspMap.containsKey(testLineInstanceId)){
                        generateReportRspMap.put(testLineInstanceId,res);
                    }
                }
            });
        }
        return generateReportRspMap;
    }

    private static Collection<SubReportInfo> uniqueSubReportByCloudId(List<SubReportInfo> sortSubReportInfos) {
        Map<String,SubReportInfo> subReportMap = new TreeMap<>();
        for (SubReportInfo subReport : sortSubReportInfos) {
            String cloudId = subReport.getCloudId();
            if(!(cloudId.toUpperCase().lastIndexOf(Constants.FILE.TYPE.PDF)!=(cloudId.length()-3))){
                continue;
            }
            subReportMap.put(cloudId,subReport);
        }
        return subReportMap.values();
    }

    private static boolean isSubcontractReport(SubReportInfo report) {
        return SubReportObjectTypeEnums.check(report.getObjectType(), SubReportObjectTypeEnums.subcontract,SubReportObjectTypeEnums.slimjob,SubReportObjectTypeEnums.starlims)
                || ObjectType.check(report.getObjectType(), ObjectType.SubContract,ObjectType.StarLims);
    }

    /**
     * 查询TL对应照片
     * @param orderNo
     * @param testLineInstanceIds
     * @return
     */
    private List<TestSamplePhotoInfoDTO> getTestedSamplePhoto(String orderNo, String reportId, List<String> testLineInstanceIds, Integer languageId){
        List<TestSamplePhotoInfoDTO> testSamplePhotoInfos = new ArrayList<>();
        //查询订单信息
        GeneralOrderInstanceInfoPO generalOrderInstanceInfoPO = orderMapper.getOrderInfo(orderNo);
        if (Func.isEmpty(generalOrderInstanceInfoPO)){
            return testSamplePhotoInfos;
        }
        //查询对应matrix
        List<TestMatrixDTO> testMatrixDTOS = testMatrixMapper.getMatrixByTlAndOrderId(generalOrderInstanceInfoPO.getID(),reportId,testLineInstanceIds);
        if (Func.isEmpty(testMatrixDTOS)){
            return testSamplePhotoInfos;
        }

        // 初始化SamplePhoto结构
        for (TestMatrixDTO testMatrixDTO : testMatrixDTOS) {
            testSamplePhotoInfos.add(initTestSamplePhotoStructure(testMatrixDTO, languageId));
        }

        // 设置照片信息
        Map<String,List<TestMatrixDTO>> groupById = testMatrixDTOS.stream().collect(Collectors.groupingBy(TestMatrixDTO :: getID));
        //根据matrix查询图片
        QueryOrderAttachmentReq queryOrderAttachmentReq = new QueryOrderAttachmentReq();
        queryOrderAttachmentReq.setProductLineCode(ProductLineContextHolder.getProductLineCode());
        queryOrderAttachmentReq.setObjectIds(testMatrixDTOS.stream().map(TestMatrixDTO :: getID).collect(Collectors.toList()));
        BaseResponse<List<OrderAttachmentRsp>> baseResponse =orderFacade.queryOrderAttachment(queryOrderAttachmentReq);
        //判断查询是否成功
        if (Func.isEmpty(baseResponse) || Func.isEmpty(baseResponse.getData())){
            return testSamplePhotoInfos;
        }
        List<OrderAttachmentRsp> orderAttachmentRsps = baseResponse.getData();
        //处理 数据
        for (OrderAttachmentRsp orderAttachmentRsp : orderAttachmentRsps){
            //取Matrix信息
            TestMatrixDTO testMatrixDTO = groupById.get(orderAttachmentRsp.getObjectID()).get(0);
            if (Func.isEmpty(testMatrixDTO)){
                continue;
            }
            TestSamplePhotoInfoDTO testSamplePhotoInfoDTO = testSamplePhotoInfos.stream().filter(testSamplePhotoTemp -> {return Func.equals(testSamplePhotoTemp.getMatrixId(),testMatrixDTO.getID());}).findFirst().orElse(null);
            if(Func.isEmpty(testSamplePhotoInfoDTO)){
                continue;
            }
            //设置Photo信息
            PhotoDTO photo = testSamplePhotoInfoDTO.getPhotos().stream().filter(photoTemp -> {return Func.equals(photoTemp.getPhotoType(),orderAttachmentRsp.getPhotoType())&&Func.isEmpty(photoTemp.getCloudId());}).findFirst().orElse(null);
            if(Func.isNotEmpty(photo)) {
                photo.setAttachmentId(orderAttachmentRsp.getID());
                photo.setCloudId(Constants.CLOUD_PREFIX + orderAttachmentRsp.getCloudID());
                //名称规则(按语言取) ： Type EN + 空格+ SampleNo+ “_” + 流水号
                String photoType;
                String separator;
                if (LanguageType.check(languageId,LanguageType.Chinese)){
                    photoType = SamplePhotoFileTypeEnums.getTypeCn(orderAttachmentRsp.getPhotoType());
                    separator = "-样品 ";
                } else {
                    photoType = SamplePhotoFileTypeEnums.getTypeEn(orderAttachmentRsp.getPhotoType());
                    separator = "-Sample ";
                }
                photo.setAttachmentName(photoType + separator + orderAttachmentRsp.getSampleNo());
                photo.setPhotoSeq(orderAttachmentRsp.getSuffixNum());
            }else{
                photo = new PhotoDTO();
                photo.setAttachmentId(orderAttachmentRsp.getID());
                photo.setPhotoType(orderAttachmentRsp.getPhotoType());
                photo.setCloudId(Constants.CLOUD_PREFIX + orderAttachmentRsp.getCloudID());
                //名称规则(按语言取) ： Type EN + 空格+ SampleNo+ “_” + 流水号
                String photoType;
                String separator;
                if (LanguageType.check(languageId,LanguageType.Chinese)){
                    photoType = SamplePhotoFileTypeEnums.getTypeCn(orderAttachmentRsp.getPhotoType());
                    separator = "-样品 ";
                } else {
                    photoType = SamplePhotoFileTypeEnums.getTypeEn(orderAttachmentRsp.getPhotoType());
                    separator = "-Sample ";
                }
                //photo.setPhotoTypeDisplayName(photoType);
                photo.setAttachmentName(photoType + separator + orderAttachmentRsp.getSampleNo());
                photo.setPhotoSeq(orderAttachmentRsp.getSuffixNum());
                testSamplePhotoInfoDTO.getPhotos().add(photo);
            }
        }
        //Photo重新排序
        for (TestSamplePhotoInfoDTO testSamplePhotoInfo : testSamplePhotoInfos) {
            Comparator typeComparator = new Comparator<PhotoDTO>() {
                public int compare(PhotoDTO next, PhotoDTO up) {
                    List<Integer> typeSeqs = Arrays.asList(1,3,2,4);
                    return typeSeqs.indexOf(next.getPhotoType())-typeSeqs.indexOf(up.getPhotoType());
                }
            };

            Comparator seqComparator = new Comparator<PhotoDTO>() {
                public int compare(PhotoDTO next, PhotoDTO up) {
                    return  Func.toInt(up.getPhotoType())-Func.toInt(next.getPhotoType()) ;
                }
            };
            testSamplePhotoInfo.getPhotos().sort(typeComparator.thenComparing(seqComparator));
        }
        return testSamplePhotoInfos;
    }

    private TestSamplePhotoInfoDTO initTestSamplePhotoStructure(TestMatrixDTO testMatrix, Integer languageId){
        TestSamplePhotoInfoDTO testPhoto = new TestSamplePhotoInfoDTO();
        testPhoto.setMatrixId(testMatrix.getID());
        testPhoto.setTestLineInstanceId(testMatrix.getTestLineInstanceID());
        testPhoto.setSampleNo(testMatrix.getSampleNo());
        testPhoto.setSampleDescription(testMatrix.getSampleDescription());
        //原样→测试中→测试前→测试后
        List<PhotoDTO> photos = new ArrayList<>();
        PhotoDTO originalPhoto = new PhotoDTO();
        PhotoDTO middlePhoto = new PhotoDTO();
        PhotoDTO beforePhoto = new PhotoDTO();
        PhotoDTO afterPhoto = new PhotoDTO();
        if (LanguageType.check(languageId,LanguageType.Chinese)){
            String separator= "-样品 ";
            originalPhoto.setPhotoType(SamplePhotoFileTypeEnums.O.getType());
            originalPhoto.setAttachmentName(SamplePhotoFileTypeEnums.O.getTypeCn()+separator+testMatrix.getSampleNo());
            middlePhoto.setPhotoType(SamplePhotoFileTypeEnums.M.getType());
            middlePhoto.setAttachmentName(SamplePhotoFileTypeEnums.M.getTypeCn()+separator+testMatrix.getSampleNo());
            beforePhoto.setPhotoType(SamplePhotoFileTypeEnums.B.getType());
            beforePhoto.setAttachmentName(SamplePhotoFileTypeEnums.B.getTypeCn()+separator+testMatrix.getSampleNo());
            afterPhoto.setPhotoType(SamplePhotoFileTypeEnums.A.getType());
            afterPhoto.setAttachmentName(SamplePhotoFileTypeEnums.A.getTypeCn()+separator+testMatrix.getSampleNo());
        }else {
            String separator= "-Sample ";
            originalPhoto.setPhotoType(SamplePhotoFileTypeEnums.O.getType());
            originalPhoto.setAttachmentName(SamplePhotoFileTypeEnums.O.getTypeEn()+separator+testMatrix.getSampleNo());
            middlePhoto.setPhotoType(SamplePhotoFileTypeEnums.M.getType());
            middlePhoto.setAttachmentName(SamplePhotoFileTypeEnums.M.getTypeEn()+separator+testMatrix.getSampleNo());
            beforePhoto.setPhotoType(SamplePhotoFileTypeEnums.B.getType());
            beforePhoto.setAttachmentName(SamplePhotoFileTypeEnums.B.getTypeEn()+separator+testMatrix.getSampleNo());
            afterPhoto.setPhotoType(SamplePhotoFileTypeEnums.A.getType());
            afterPhoto.setAttachmentName(SamplePhotoFileTypeEnums.A.getTypeEn()+separator+testMatrix.getSampleNo());
        }
        photos.add(originalPhoto);
        photos.add(middlePhoto);
        photos.add(beforePhoto);
        photos.add(afterPhoto);
        testPhoto.setPhotos(photos);
        return testPhoto;
    }

    private void initSamples(DigitalReportDataSourceDTO digitalReportInfo, GenerateReportRequest reportRequest){
        List<SamplesInfoDTO> samples = new ArrayList<>();
        ReportSampleVO reportSampleVO = new ReportSampleVO();
        reportSampleVO.setReportId(reportRequest.getReportId());
        reportSampleVO.setMatrixActiveIndicator(1);
        List<TestSampleInfoPO> testSamples = testSampleExtMapper.querySampleListByReportId(reportSampleVO);
        //查询sample中英文信息
        OrderIdReq orderIdReq = new OrderIdReq();
        orderIdReq.setOrderId(reportRequest.getOrder().getID());
        BaseResponse<List<ProductInstanceDTO>> dffSamplesResponse = orderFacade.queryProductInstaceForPe(orderIdReq);
        BaseResponse<List<OrderAttachmentDTO>> orderAttachmentResponse =orderFacade.queryOrderAttachmentForPe(orderIdReq);
        if (Func.isEmpty(dffSamplesResponse) || Func.isEmpty(dffSamplesResponse.getData())){
            return;
        }
        List<ProductInstanceDTO> productInstanceDTOS = dffSamplesResponse.getData();
        //描述信息组装成map
        Map<String,String> groupBySampleNo = new HashMap<>();
        productInstanceDTOS.stream().forEach(productInstanceDTO -> {
            //判断中英文是否匹配，headid不为空
            if (LanguageType.check(reportRequest.getLanguageId(),LanguageType.English)){
                if (Func.isNotEmpty(productInstanceDTO.getHeaderID()) &&Func.isNotEmpty(productInstanceDTO.getLanguageID()) &&
                        Func.equals(productInstanceDTO.getLanguageID(),LanguageType.English.getLanguageId())){
                    groupBySampleNo.put(productInstanceDTO.getSampleID(),productInstanceDTO.getOtherSampleInformation());
                }
            }else if (LanguageType.check(reportRequest.getLanguageId(),LanguageType.Chinese)){
                if (Func.isNotEmpty(productInstanceDTO.getHeaderID()) &&Func.isNotEmpty(productInstanceDTO.getLanguageID()) &&
                        Func.equals(productInstanceDTO.getLanguageID(),LanguageType.Chinese.getLanguageId())){
                    groupBySampleNo.put(productInstanceDTO.getSampleID(),productInstanceDTO.getOtherSampleInformation());
                }
            }
        });
        if(Func.isNotEmpty(testSamples)){
            testSamples.stream().forEach(sample -> {
                SamplesInfoDTO samplesInfo = new SamplesInfoDTO();
                samplesInfo.setSampleNo(sample.getSampleNo());
                samplesInfo.setSampleDescription(groupBySampleNo.get(sample.getSampleNo()));
                if(ResponseCode.SUCCESS.getCode()==orderAttachmentResponse.getStatus() && Func.isNotEmpty(orderAttachmentResponse.getData())){
                    List<SamplePhotoInfoDTO> photos = new ArrayList<>();
                    // 样品层面的SamplePhoto
                    orderAttachmentResponse.getData().stream().filter(samplePhoto->
                            BusinessType.SamplePhoto.getCode().equals(samplePhoto.getBusinessType())
                            &&sample.getID().equals(samplePhoto.getObjectID())).forEach(samplePhotoItem->{
                        SamplePhotoInfoDTO samplePhoto = new SamplePhotoInfoDTO();
                        samplePhoto.setAttachmentId(samplePhotoItem.getID());
                        samplePhoto.setCloudId(Constants.CLOUD_PREFIX+samplePhotoItem.getCloudID());
                        samplePhoto.setSampleDescription(groupBySampleNo.get(sample.getSampleNo()));
                        //名称规则(按语言取) ： Type EN + 空格+ SampleNo+ “_” + 流水号
                        String photoType;
                        String separator;
                        if (LanguageType.check(reportRequest.getLanguageId(),LanguageType.Chinese)){
                            photoType = SamplePhotoFileTypeEnums.getTypeCn(samplePhotoItem.getPhotoType());
                            separator = "-样品 ";
                        }else {
                            photoType = SamplePhotoFileTypeEnums.getTypeEn(samplePhotoItem.getPhotoType());
                            separator = "-Sample ";
                        }
                        samplePhoto.setAttachmentName(photoType + separator + samplePhotoItem.getSampleNo());
                        photos.add(samplePhoto);
                        samplesInfo.setPhotos(photos);
                    });
                    // 订单层面的SamplePhoto
                    orderAttachmentResponse.getData().stream().filter(samplePhoto->
                            BusinessType.SamplePhoto.getCode().equals(samplePhoto.getBusinessType())
                                    &&Func.isEmpty(samplePhoto.getObjectID())).forEach(samplePhotoItem->{
                        SamplePhotoInfoDTO samplePhoto = new SamplePhotoInfoDTO();
                        samplePhoto.setAttachmentId(samplePhotoItem.getID());
                        samplePhoto.setCloudId(Constants.CLOUD_PREFIX+samplePhotoItem.getCloudID());
                        samplePhoto.setSampleDescription(groupBySampleNo.get(sample.getSampleNo()));
                        samplePhoto.setAttachmentName(samplePhotoItem.getAttachmentName());
                        photos.add(samplePhoto);
                        samplesInfo.setPhotos(photos);
                    });
                }
                samples.add(samplesInfo);
            });
        }
        //
        if(Func.isNotEmpty(samples)){
            // 按照sampleNo升序排序
            samples.sort(Comparator.comparing(SamplesInfoDTO::getSampleNo,(x,y)->{
                if (x.length() > y.length()){
                    return 1;
                }else if (x.length() < y.length()){
                    return -1;
                }else {
                    return x.compareTo(y);
                }
            }));
            // 动态生成reportSampleSeq 从A — ZZ
            int sampleSize = samples.size();
            for(int i=1;i<=sampleSize;i++){
                samples.get(i-1).setReportSampleSeq(getAlphabetIndex(i));
            }
        }
        digitalReportInfo.getVariableDataList().setSampleList(samples);
    }

    /**
     * 动态生成序号从 A - ZZ
     * @param n
     * @return
     */
    private static String getAlphabetIndex(int n) {
        char[] buf = new char[(int) floor(log(25 * (n + 1)) / log(26))];
        for (int i = buf.length - 1; i >= 0; i--) {
            n--;
            buf[i] = (char) ('A' + n % 26);
            n /= 26;
        }
        return new String(buf);
    }

    private CustomerInfoDTO getCustomerInfo(String orderNo,Integer languageId){
        CustomerInfoDTO customerInfoDTO = new CustomerInfoDTO();
        OrderIdReq orderIdReq = new OrderIdReq();
        orderIdReq.setOrderNo(orderNo);
        orderIdReq.setProductLineCode(ProductLineContextHolder.getProductLineCode());
        List<CustomerInstanceDTO> customerList = getCustomerList(orderIdReq);
        if(Func.isNotEmpty(customerList)) {
            //Applicat
            CustomerInstanceDTO applicantInstance = customerList.stream().filter(cus -> GpoCustomerType.Applicant.getCode().equals(cus.getCustomerUsage())).
                    findFirst().orElse(null);
            if (Func.isNotEmpty(applicantInstance)) {
                customerInfoDTO.setApplicantCompanyName(getCustomerName(applicantInstance, languageId));
                customerInfoDTO.setApplicantCompanyAddress(applicantInstance.getCustomerAddressCN());
                customerInfoDTO.setApplicantContactEmail(applicantInstance.getContactPersonEmail());
                customerInfoDTO.setApplicantContactName(applicantInstance.getContactPersonName());
                customerInfoDTO.setApplicantContactTel(applicantInstance.getContactPersonPhone1());
            }
            //buyer
            CustomerInstanceDTO buyerInstance = customerList.stream().filter(cus -> GpoCustomerType.Buyer.getCode().equals(cus.getCustomerUsage())).
                    findFirst().orElse(null);
            if (Func.isNotEmpty(buyerInstance)) {
                customerInfoDTO.setBuyerCompanyName(getCustomerName(buyerInstance, languageId));
                customerInfoDTO.setBuyerCompanyAddress(buyerInstance.getCustomerAddressCN());
                customerInfoDTO.setBuyerContactEmail(buyerInstance.getContactPersonEmail());
                customerInfoDTO.setBuyerContactName(buyerInstance.getContactPersonName());
                customerInfoDTO.setBuyerContactTel(buyerInstance.getContactPersonPhone1());
            }
            //payer
            CustomerInstanceDTO payerInstance = customerList.stream().filter(cus -> GpoCustomerType.Payer.getCode().equals(cus.getCustomerUsage())).
                    findFirst().orElse(null);
            if (Func.isNotEmpty(payerInstance)) {
                customerInfoDTO.setPayerCompanyName(getCustomerName(payerInstance, languageId));
                customerInfoDTO.setPayerCompanyAddress(payerInstance.getCustomerAddressCN());
                customerInfoDTO.setPayerContactEmail(payerInstance.getContactPersonEmail());
                customerInfoDTO.setPayerContactName(payerInstance.getContactPersonName());
                customerInfoDTO.setPayerContactTel(payerInstance.getContactPersonPhone1());
            }
            //supplier
            CustomerInstanceDTO supplierInstance = customerList.stream().filter(cus -> GpoCustomerType.Supplier.getCode().equals(cus.getCustomerUsage())).
                    findFirst().orElse(null);
            if (Func.isNotEmpty(supplierInstance)) {
                customerInfoDTO.setSupplierCompanyName(getCustomerName(supplierInstance, languageId));
                customerInfoDTO.setSupplierCompanyAddress(supplierInstance.getCustomerAddressCN());
                customerInfoDTO.setSupplierContactEmail(supplierInstance.getContactPersonEmail());
                customerInfoDTO.setSupplierContactName(supplierInstance.getContactPersonName());
                customerInfoDTO.setSupplierContactTel(supplierInstance.getContactPersonPhone1());
            }
            //agent
            CustomerInstanceDTO agentInstance = customerList.stream().filter(cus -> GpoCustomerType.Agent.getCode().equals(cus.getCustomerUsage())).
                    findFirst().orElse(null);
            if (Func.isNotEmpty(agentInstance)) {
                customerInfoDTO.setAgentCompanyName(getCustomerName(agentInstance, languageId));
                customerInfoDTO.setAgentCompanyAddress(agentInstance.getCustomerAddressCN());
                customerInfoDTO.setAgentContactEmail(agentInstance.getContactPersonEmail());
                customerInfoDTO.setAgentContactName(agentInstance.getContactPersonName());
                customerInfoDTO.setAgentContactTel(agentInstance.getContactPersonPhone1());
            }
            //manufacturer
            CustomerInstanceDTO manufacturerIns = customerList.stream().filter(cus -> GpoCustomerType.Manufacture.getCode().equals(cus.getCustomerUsage())).
                    findFirst().orElse(null);
            if (Func.isNotEmpty(manufacturerIns)) {
                customerInfoDTO.setManufacturerCompanyName(getCustomerName(manufacturerIns, languageId));
                customerInfoDTO.setManufacturerCompanyAddress(manufacturerIns.getCustomerAddressCN());
                customerInfoDTO.setManufacturerContactEmail(manufacturerIns.getContactPersonEmail());
                customerInfoDTO.setManufacturerContactName(manufacturerIns.getContactPersonName());
                customerInfoDTO.setManufacturerContactTel(manufacturerIns.getContactPersonPhone1());
            }
            //OEM
            CustomerInstanceDTO oemIns = customerList.stream().filter(cus -> GpoCustomerType.OEM.getCode().equals(cus.getCustomerUsage())).
                    findFirst().orElse(null);
            if (Func.isNotEmpty(oemIns)) {
                customerInfoDTO.setOemCompanyName(getCustomerName(oemIns, languageId));
                customerInfoDTO.setOemCompanyAddress(oemIns.getCustomerAddressCN());
                customerInfoDTO.setOemContactEmail(oemIns.getContactPersonEmail());
                customerInfoDTO.setOemContactName(oemIns.getContactPersonName());
                customerInfoDTO.setOemContactTel(oemIns.getContactPersonPhone1());
                customerInfoDTO.setOemGroupCode(oemIns.getBuyerGroup());
                customerInfoDTO.setOemGroupName(oemIns.getBuyerGroupName());

            }
            // SUBCONTRACTFROM
            CustomerInstanceDTO subContractCus = customerList.stream().filter(cus -> GpoCustomerType.SUBCONTRACTFROM.getCode().equals(cus.getCustomerUsage())).
                    findFirst().orElse(null);
            if(Func.isNotEmpty(subContractCus)){
                customerInfoDTO.setSubcontractFromLab(subContractCus.getCustomerId());
            }
        }
        return customerInfoDTO;
    }

    private void initCustomerInfo(DigitalReportDataSourceDTO digitalReportInfo, GenerateReportRequest reportRequset){
        CustomerInfoDTO customerInfoDTO = new CustomerInfoDTO();
        OrderIdReq orderIdReq = new OrderIdReq();
        orderIdReq.setOrderId(reportRequset.getOrder().getOrderId());
        orderIdReq.setProductLineCode(digitalReportInfo.getProductLineCode());
        List<CustomerInstanceDTO> customerList = getCustomerList(orderIdReq);
        Integer languageId = reportRequset.getLanguageId();
        if(Func.isNotEmpty(customerList)) {
            //Applicat
            CustomerInstanceDTO applicantInstance = customerList.stream().filter(cus -> GpoCustomerType.Applicant.getCode().equals(cus.getCustomerUsage())).
                    findFirst().orElse(null);
            if (Func.isNotEmpty(applicantInstance)) {
                customerInfoDTO.setApplicantCompanyName(getCustomerName(applicantInstance, languageId));
                customerInfoDTO.setApplicantCompanyAddress(getAddressName(applicantInstance, languageId));
                customerInfoDTO.setApplicantContactEmail(applicantInstance.getContactPersonEmail());
                customerInfoDTO.setApplicantContactName(applicantInstance.getContactPersonName());
                customerInfoDTO.setApplicantContactTel(applicantInstance.getContactPersonPhone1());
            }
            //buyer
            CustomerInstanceDTO buyerInstance = customerList.stream().filter(cus -> GpoCustomerType.Buyer.getCode().equals(cus.getCustomerUsage())).
                    findFirst().orElse(null);
            if (Func.isNotEmpty(buyerInstance)) {
                customerInfoDTO.setBuyerCompanyName(getCustomerName(buyerInstance, languageId));
                customerInfoDTO.setBuyerCompanyAddress(getAddressName(buyerInstance, languageId));
                customerInfoDTO.setBuyerContactEmail(buyerInstance.getContactPersonEmail());
                customerInfoDTO.setBuyerContactName(buyerInstance.getContactPersonName());
                customerInfoDTO.setBuyerContactTel(buyerInstance.getContactPersonPhone1());
            }
            //payer
            CustomerInstanceDTO payerInstance = customerList.stream().filter(cus -> GpoCustomerType.Payer.getCode().equals(cus.getCustomerUsage())).
                    findFirst().orElse(null);
            if (Func.isNotEmpty(payerInstance)) {
                customerInfoDTO.setPayerCompanyName(getCustomerName(payerInstance, languageId));
                customerInfoDTO.setPayerCompanyAddress(getAddressName(payerInstance, languageId));
                customerInfoDTO.setPayerContactEmail(payerInstance.getContactPersonEmail());
                customerInfoDTO.setPayerContactName(payerInstance.getContactPersonName());
                customerInfoDTO.setPayerContactTel(payerInstance.getContactPersonPhone1());
            }
            //supplier
            CustomerInstanceDTO supplierInstance = customerList.stream().filter(cus -> GpoCustomerType.Supplier.getCode().equals(cus.getCustomerUsage())).
                    findFirst().orElse(null);
            if (Func.isNotEmpty(supplierInstance)) {
                customerInfoDTO.setSupplierCompanyName(getCustomerName(supplierInstance, languageId));
                customerInfoDTO.setSupplierCompanyAddress(getAddressName(supplierInstance, languageId));
                customerInfoDTO.setSupplierContactEmail(supplierInstance.getContactPersonEmail());
                customerInfoDTO.setSupplierContactName(supplierInstance.getContactPersonName());
                customerInfoDTO.setSupplierContactTel(supplierInstance.getContactPersonPhone1());
            }
            //agent
            CustomerInstanceDTO agentInstance = customerList.stream().filter(cus -> GpoCustomerType.Agent.getCode().equals(cus.getCustomerUsage())).
                    findFirst().orElse(null);
            if (Func.isNotEmpty(agentInstance)) {
                customerInfoDTO.setAgentCompanyName(getCustomerName(agentInstance, languageId));
                customerInfoDTO.setAgentCompanyAddress(getAddressName(agentInstance, languageId));
                customerInfoDTO.setAgentContactEmail(agentInstance.getContactPersonEmail());
                customerInfoDTO.setAgentContactName(agentInstance.getContactPersonName());
                customerInfoDTO.setAgentContactTel(agentInstance.getContactPersonPhone1());
            }
            //manufacturer
            CustomerInstanceDTO manufacturerIns = customerList.stream().filter(cus -> GpoCustomerType.Manufacture.getCode().equals(cus.getCustomerUsage())).
                    findFirst().orElse(null);
            if (Func.isNotEmpty(manufacturerIns)) {
                customerInfoDTO.setManufacturerCompanyName(getCustomerName(manufacturerIns, languageId));
                customerInfoDTO.setManufacturerCompanyAddress(getAddressName(manufacturerIns, languageId));
                customerInfoDTO.setManufacturerContactEmail(manufacturerIns.getContactPersonEmail());
                customerInfoDTO.setManufacturerContactName(manufacturerIns.getContactPersonName());
                customerInfoDTO.setManufacturerContactTel(manufacturerIns.getContactPersonPhone1());
            }
            //OEM
            CustomerInstanceDTO oemIns = customerList.stream().filter(cus -> GpoCustomerType.OEM.getCode().equals(cus.getCustomerUsage())).
                    findFirst().orElse(null);
            if (Func.isNotEmpty(oemIns)) {
                customerInfoDTO.setOemCompanyName(getCustomerName(oemIns, languageId));
                customerInfoDTO.setOemCompanyAddress(getAddressName(oemIns, languageId));
                customerInfoDTO.setOemContactEmail(oemIns.getContactPersonEmail());
                customerInfoDTO.setOemContactName(oemIns.getContactPersonName());
                customerInfoDTO.setOemContactTel(oemIns.getContactPersonPhone1());
                customerInfoDTO.setOemGroupCode(oemIns.getBuyerGroup());
                customerInfoDTO.setOemGroupName(oemIns.getBuyerGroupName());

            }
        }
        digitalReportInfo.getVariableDataList().setCustomers(customerInfoDTO);
    }

    private String getCustomerName(CustomerInstanceDTO customerInstanceDTO,Integer languageId){
        String customerName = customerInstanceDTO.getCustomerNameEN();
        if(!LanguageType.check(languageId,LanguageType.English)){
            customerName = StringUtils.isNotEmpty(customerInstanceDTO.getCustomerNameCN())?customerInstanceDTO.getCustomerNameCN():customerInstanceDTO.getCustomerNameEN();
        }
        return customerName;
    }

    private String getAddressName(CustomerInstanceDTO customerInstanceDTO,Integer languageId){
        String addressName = customerInstanceDTO.getCustomerAddressEN();
        if(!LanguageType.check(languageId,LanguageType.English)){
            addressName = StringUtils.isNotEmpty(customerInstanceDTO.getCustomerAddressCN())?customerInstanceDTO.getCustomerAddressCN():customerInstanceDTO.getCustomerAddressEN();
        }
        return addressName;
    }

    private void initDffInfo(DigitalReportDataSourceDTO digitalReportInfo, GenerateReportRequest reportRequset){
        List<DFFInfoDTO> dffFields = new ArrayList<>();

        int languageId = reportRequset.getLanguageId();
        TestrptCoverpageDffReq req = new TestrptCoverpageDffReq();
        req.setProductLineCode(reportRequset.getProductLineCode());
        req.setReportNo(reportRequset.getReportNo());
        req.setLanguageID(languageId);
        BaseResponse<List<TestrptCoverpageDffSimpleDTO>> baseResponse =  dffFacade.queryCoverPageDffForReport(req);
        if(ResponseCode.SUCCESS.getCode() == baseResponse.getStatus()) {
            List<TestrptCoverpageDffSimpleDTO> dffList = baseResponse.getData();
            if(CollectionUtils.isNotEmpty(dffList))
            {
                //过滤
                List<TestrptCoverpageDffSimpleDTO> sampleList =  dffList.stream()
                        .filter(dff -> dff.getDisplayedInReportFlag().intValue() ==1&&Func.isNotEmpty(dff.getDisplayData()))
                        .sorted(Comparator.comparing(TestrptCoverpageDffSimpleDTO::getDisplayedSeqInReport))
                        .collect(Collectors.toList());
                for (TestrptCoverpageDffSimpleDTO sample:sampleList)
                {
                    DFFInfoDTO dffInfoDTO = new DFFInfoDTO();
                    dffInfoDTO.setFieldName(sample.getFieldCode());
                    dffInfoDTO.setValue(sample.getDisplayData());
                    dffInfoDTO.setLabel(sample.getLabelname());
                    dffInfoDTO.setSeq(sample.getDisplayedSeqInReport());
                    dffInfoDTO.setDataType(sample.getFieldType());
                    dffFields.add(dffInfoDTO);
                }
            }
        }
        digitalReportInfo.getVariableDataList().setDff(dffFields);
    }

    private void initServiceItem(DigitalReportDataSourceDTO digitalReportInfo, GenerateReportRequest reportRequset){
        QueryQuotationHisRequest queryQuotationHisRequest = new QueryQuotationHisRequest();
        queryQuotationHisRequest.setOrderId(reportRequset.getOrder().getID());
        queryQuotationHisRequest.setProductLineCode(reportRequset.getProductLineCode());
        queryQuotationHisRequest.setSystemId(SgsSystem.GPO.getSgsSystemId());
        BaseResponse<List<QuotationServiceItemDTO>> response = quotationClient.queryQuotationHisListByVersion(queryQuotationHisRequest);
        if(ResponseCode.SUCCESS.getCode() == response.getStatus()&&Func.isNotEmpty(response.getData())){
            List<ServiceItemDTO> serviceItemDTOS = new ArrayList<>();
            response.getData().forEach(quotationServiceItemDTO -> {
                ServiceItemDTO serviceItemDTO = new ServiceItemDTO();
//                serviceItemDTO.setItemID(quotationServiceItemDTO.getServiceItemId());
//                serviceItemDTO.setSeq(quotationServiceItemDTO.getSequenceNo());
                serviceItemDTO.setServiceName(quotationServiceItemDTO.getServiceItemName());
                String standName = quotationServiceItemDTO.getCitationName();
                if(Func.isNotEmpty(quotationServiceItemDTO.getClientStandard())){
                    standName = quotationServiceItemDTO.getClientStandard() + " & " + standName;
                }
                serviceItemDTO.setTestLineCitation(standName);

                serviceItemDTO.setSectionName(quotationServiceItemDTO.getSectionName());
                serviceItemDTOS.add(serviceItemDTO);
            });
            digitalReportInfo.getVariableDataList().setServiceItem(serviceItemDTOS);
        }
    }

    public BaseResponse<DigitalReportReq> buildDigitalReportRequest(GenerateReportRequest reportRequest) {
        BaseResponse baseResponse = new BaseResponse();
        baseResponse.setStatus(ResponseCode.SUCCESS.getCode());
        DigitalReportInfoDTO infoData = new DigitalReportInfoDTO();

        String groupCode = "General";//默认General
        if(StringUtils.isNotEmpty(reportRequest.getGroupCode()) && !StringUtils.equalsIgnoreCase(reportRequest.getGroupCode(),"null"))
        {
            groupCode = reportRequest.getGroupCode();
        }
        String orderNo = reportRequest.getOrderNo();
        String externalReportNo = orderClient.getExternalReportNo(orderNo, reportRequest.getReportNo());

        infoData.setInstanceNumber(externalReportNo);
        if(ReportActionType.DOWNLOAD_TL_TEMPLATE.getCode().equals(reportRequest.getReportActionType())){
            infoData.setSubReportType(Constants.DIGITAL_REPORT.SUBREPORT_TYPE.TESTLINE);
            //testResult时传0
            infoData.setReportType(GenerateReportTypeEnums.TEST_RESULT.getCode());
        }else if (ReportActionType.GENERATE_REPORT.getCode().equals(reportRequest.getReportActionType())){
            infoData.setReportType(GenerateReportTypeEnums.TEST_RESULT.getCode());
        }
        infoData.setTemplateId(reportRequest.getTemplate().getTemplateID());
        infoData.setTemplateSettingID(reportRequest.getTemplate().getTemplateSettingID());
        infoData.setLanguageID(reportRequest.getTemplate().getLanguageID());
        infoData.setGroupCode(groupCode);
        //不知道DOWNLOAD_WORKSHEET这是什么情况下传的，先注释掉
//        if(ReportActionType.DOWNLOAD_WORKSHEET.getCode().equals(reportRequest.getReportActionType())){
//            infoData.setReportType(1);
//        }else{
//            infoData.setReportType(0);
//        }
        infoData.setBuCode(reportRequest.getProductLineCode());
        BaseResponse<DigitalReportDataSourceDTO> dataSourceResponse;
        if(Func.isNotEmpty(reportRequest.getTemplate()) &&
                ProtocolStructureFlag.check(reportRequest.getTemplate().getProtocolStructureFlag(),ProtocolStructureFlag.Protocol)){
            dataSourceResponse = buildDigitalReportDataSourceExt(reportRequest);
        }else {
            dataSourceResponse = buildDigitalReportDataSource(reportRequest,infoData);
        }
        logger.info("{}-buildDigitalReportDataSoucre res:{}",reportRequest.getReportNo(), JSON.toJSONString(dataSourceResponse));
        if (!Func.equals(ResponseCode.SUCCESS.getCode(),dataSourceResponse.getStatus())){
            logger.info("==========buildDigitalReportDataSoucre->errorMessage：{}============", dataSourceResponse.getMessage());
            baseResponse.setStatus(ResponseCode.FAIL.getCode());
            baseResponse.setMessage(dataSourceResponse.getMessage());
            return baseResponse;
        }
        DigitalReportReq digitalReportReq = new DigitalReportReq(infoData,dataSourceResponse.getData());
        logger.info("==========TsMappingResult initTsMappingResult：{}============", JSON.toJSONString(digitalReportReq));
        baseResponse.setData(digitalReportReq);
        return  baseResponse;
    }

    public CustomResult<DigitalReportReq2> buildDigitalReportRequest2(BuildDigitalReportRequestReq req) {
        CustomResult<DigitalReportReq2> baseResponse = new CustomResult();
        baseResponse.setSuccess(true);
        DigitalReportInfoDTO infoData = new DigitalReportInfoDTO();

        String groupCode = "General";
        String orderNo = req.getOrderNo();
        String externalOrderNo = orderClient.getExternalOrderNo(orderNo);
        if(Func.isEmpty(externalOrderNo)){
            baseResponse.setSuccess(false);
            baseResponse.setMsg("Order 信息不存在");
            return baseResponse;
        }
        infoData.setInstanceNumber(externalOrderNo);
        infoData.setGroupCode(groupCode);
        infoData.setBuCode(req.getProductLineCode());
        infoData.setLanguageID(9601);
        BaseResponse<DigitalReportDataSourceDTO2> dataSourceResponse;
        if(PrintType.check(req.getPrintType(),PrintType.REVISE_REPORT_FORM)){
            dataSourceResponse = buildDataSourceForReviseReport(req);
        }else {
            dataSourceResponse = buildDigitalReportDataSource2(req);
        }
        logger.info("buildDigitalReportDataSource2 res:{}",JSON.toJSONString(dataSourceResponse));
        if (!Func.equals(ResponseCode.SUCCESS.getCode(),dataSourceResponse.getStatus())){
            logger.info("==========buildDigitalReportDataSource2->errorMessage：{}============", dataSourceResponse.getMessage());
            baseResponse.setSuccess(false);
            baseResponse.setMsg(dataSourceResponse.getMessage());
            return baseResponse;
        }
        DigitalReportReq2 digitalReportReq = new DigitalReportReq2(infoData,dataSourceResponse.getData(),req.getConvertToPdf());
        logger.info("==========TsMappingResult2 initTsMappingResult2：{}============", JSON.toJSONString(digitalReportReq));
        baseResponse.setData(digitalReportReq);
        return baseResponse;
    }

    public BaseResponse<DigitalReportDataSourceDTO2> buildDataSourceForReviseReport(BuildDigitalReportRequestReq req){
        BaseResponse<DigitalReportDataSourceDTO2> response = new BaseResponse<>();
        DigitalReportDataSourceDTO2 digitalReportInfo = new DigitalReportDataSourceDTO2();
        ReviseFormDTO reviseForm = new ReviseFormDTO();

        digitalReportInfo.setVariableDataList(new VariableDataList2());
        digitalReportInfo.setProductLineCode(req.getProductLineCode());
        // 订单信息
        OrderIdReq orderIdReq = new OrderIdReq();
        orderIdReq.setOrderNo(req.getOrderNo());
        orderIdReq.setProductLineCode(req.getProductLineCode());
        BaseResponse<OrderAllDTO> orderForPe = orderFacade.getOrderForPe(orderIdReq);
        if (Func.isEmpty(orderForPe) || Func.isEmpty(orderForPe.getData())){
            return BaseResponse.newFailInstance("order不存在");
        }
        OrderAllDTO orderAllDTO = orderForPe.getData();
        ReportDTO oldReportDTO = reportMapper.getByReportNo(req.getOldReportNo());
        if(Func.isEmpty(oldReportDTO)){
            return BaseResponse.newFailInstance("OldReportNo不存在");
        }
        // 查询申请单信息
        ReportReworkApproverRecordsInfoPO reportReworkInfo = reportReworkApproverRecordsInfoExtMapper.queryReworkApproveRecord(oldReportDTO.getId(),req.getNewReportNo());
        if(Func.isEmpty(reportReworkInfo) || Func.isEmpty(reportReworkInfo.getApplyId())){
            if(Func.isNotEmpty(req.getNewReportNo())){
                ReportDTO newReportDTO = reportMapper.getByReportNo(req.getNewReportNo());
                // 查询订单下的01报告
                OrderIdReq newOrderReq = new OrderIdReq();
                newOrderReq.setOrderNo(newReportDTO.getOrderNo());
                newOrderReq.setProductLineCode(ProductLineContextHolder.getProductLineCode());
                BaseResponse<Boolean> isAmendResponse = orderFacade.isAmendOrder(newOrderReq);
                if(Func.isNotEmpty(isAmendResponse) && Func.isNotEmpty(isAmendResponse.getData()) && isAmendResponse.getData()){
                    ReportInfoPO firstReport = reportMapper.getFirstReportByOrderNo(newReportDTO.getOrderNo());
                    reportReworkInfo = reportReworkApproverRecordsInfoExtMapper.queryReworkApproveRecord(oldReportDTO.getId(),firstReport.getReportNo());
                }
            }
        }
//        if(Func.isEmpty(reportReworkInfo) || Func.isEmpty(reportReworkInfo.getApplyId())){
//            reportReworkInfo = reportReworkApproverRecordsInfoExtMapper.queryReworkApproveRecord(oldReportDTO.getId(),null);
//        }
        if(Func.isEmpty(reportReworkInfo) || Func.isEmpty(reportReworkInfo.getApplyId())){
            return BaseResponse.newFailInstance("当前Report为“在线生成改单申请表”功能上线之前的改单记录，无法生成“改单申请表”，请线下处理.");
        }
        //查询客户信息
        BaseResponse<List<CustomerInstanceDTO>> customer= orderFacade.queryCustomerForPe(orderIdReq);
        if (Func.isNotEmpty(customer) || Func.isNotEmpty(customer.getData())){
            List<CustomerInstanceDTO> customerInstanceDTOList=customer.getData();
            CustomerInstanceDTO applicant=customerInstanceDTOList.stream()
                    .filter(e->e.getCustomerUsage().equals(CustomerUsage.Applicant.getCode())).findFirst().orElse(null);
            if(Func.isNotEmpty(applicant)){
                CustomerDTO applicantDTO = new CustomerDTO();
                applicantDTO.setCustomerName(applicant.getCustomerNameEN());
                applicantDTO.setCustomerNameCN(applicant.getCustomerNameCN());
                if(Func.isNotEmpty(applicant.getBossNumber())){
                    applicantDTO.setCustomerNo(applicant.getBossNumber().toString());
                }
                // applicant
                reviseForm.setApplicant(applicantDTO);
            }
        }
        // labCode
        reviseForm.setLabCode(orderAllDTO.getLabDTO().getLabCode());

        reviseForm.setApplyUser(reportReworkInfo.getApplyBy());
        //
        if(Func.isNotEmpty(reportReworkInfo.getApplyBy())){
            UserDTO userDTO = getUserByRegionAccount(reportReworkInfo.getApplyBy());
            reviseForm.setApplyUserEmail(userDTO.getEmail());
            reviseForm.setApplyUserTel(userDTO.getTelephone());
        }
        if(Func.isNotEmpty(reportReworkInfo.getApplyTime())){
            reviseForm.setApplyDate(DateFormatUtil.formatDate("yyyy/MM/dd HH:mm",reportReworkInfo.getApplyTime()));
        }
        // originalReport 信息
        ReportInfoDTO originalReport = new ReportInfoDTO();
        originalReport.setReportNo(oldReportDTO.getActualReportNo());
        if(Func.isNotEmpty(oldReportDTO.getReportDueDate())){
            originalReport.setReportDueDate(DateFormatUtil.formatDate("yyyy/MM/dd HH:mm",oldReportDTO.getReportDueDate()));
        }
        if(Func.isNotEmpty(oldReportDTO.getApproverDate())){
            originalReport.setApprovedDate(DateFormatUtil.formatDate("yyyy/MM/dd HH:mm",oldReportDTO.getApproverDate()));
        }
        if(Func.isNotEmpty(oldReportDTO.getSoftcopyDeliveryDate())){
            originalReport.setSoftcopyDeliveredDate(DateFormatUtil.formatDate("yyyy/MM/dd HH:mm",oldReportDTO.getSoftcopyDeliveryDate()));
        }
        BaseResponse<TestRequestInfo> testRequestInfoBaseResponse = orderFacade.queryRestRequestForPe(orderIdReq);
        if(Func.isNotEmpty(testRequestInfoBaseResponse)&& Func.isNotEmpty(testRequestInfoBaseResponse.getData())){
            TestRequestInfo testRequestInfo = testRequestInfoBaseResponse.getData();
            Set<String> softCopyEmailList = getOrderContactEmailList(orderAllDTO,testRequestInfo,ContactsType.SoftCopy.getType());
            if(Func.isNotEmpty(softCopyEmailList)){
                originalReport.setSoftcopyDeliveryTo(softCopyEmailList.stream().distinct().collect(Collectors.joining("<br>")));
            }
            Integer hardCopyFlag = testRequestInfo.getHardCopyFlag();
            if(Func.isNotEmpty(hardCopyFlag) && Func.equalsSafe(hardCopyFlag,1)){
                originalReport.setHardCopyRequired("需要");
            }else {
                originalReport.setHardCopyRequired("不需要");
            }
            originalReport.setHardcopyDeliveryWay(testRequestInfo.getHardCopyReportDeliverWay());
            originalReport.setHardcopyDeliveryTo(getHardCopyDeliverTo(oldReportDTO.getReportNo()));
//            Set<String> hardCopyEmailList = getOrderContactEmailList(orderAllDTO,testRequestInfo,ContactsType.HardCopy.getType());
//            if(Func.isNotEmpty(hardCopyEmailList)){
//                originalReport.setHardcopyDeliveryTo(hardCopyEmailList.stream().distinct().collect(Collectors.joining("<br>")));
//            }
        }
        reviseForm.setOriginalReport(originalReport);

        // NewReport信息
        Integer signatureLanguage = null;
        ReportInfoDTO newReport = new ReportInfoDTO();
        if(Func.isNotEmpty(req.getNewReportNo())){
            ReportDTO newReportDTO = reportMapper.getByReportNo(req.getNewReportNo());
            if(Func.isEmpty(newReportDTO)){
                return BaseResponse.newFailInstance("NewReportNo不存在");
            }
            signatureLanguage = newReportDTO.getSignatureLanguage();
            newReport.setReportNo(newReportDTO.getActualReportNo());
            if(Func.isNotEmpty(newReportDTO.getReportDueDate())){
                newReport.setReportDueDate(DateFormatUtil.formatDate("yyyy/MM/dd HH:mm",newReportDTO.getReportDueDate()));
            }else if(Func.isNotEmpty(reportReworkInfo.getNewReportExpectDueDate())){
                newReport.setReportDueDate(DateFormatUtil.formatDate("yyyy/MM/dd HH:mm",reportReworkInfo.getNewReportExpectDueDate()));
            }
            //-------------------------华丽的分割线-------------------------//
            //FOR - JIRA 11183 Edison Liu
            //-------------------------华丽的分割线-------------------------//
//            if(Func.isNotEmpty(newReportDTO.getReportDueDate())){
//                newReport.setReportDueDate(DateFormatUtil.formatDate("yyyy/MM/dd HH:mm",newReportDTO.getReportDueDate()));
//            }
//            else {
//                newReport.setReportDueDate(DateFormatUtil.formatDate("yyyy/MM/dd HH:mm",reportReworkInfo.getNewReportExpectDueDate()));
//            }

            //-------------------------华丽的分割线-------------------------//
            //FOR - JIRA 10553 Edison Liu
            //GPO 的Revise Report Form 的JSON中NewReport对象下增加两个字段传值：
            //OrderNo：NewReport的Order No.
            //ResponsibleCS：NewReport的Order的Responsible CS。

            //TODO 值是否重要，如果没有数据是否需要返回失败并提示
//            if(Func.isNotEmpty(newReportDTO.getOrderNo())) {
//                newReport.setOrderNo(newReportDTO.getOrderNo());
//            }
//            OrderIdReq orderIdReqFor10553 = new OrderIdReq();
//            orderIdReqFor10553.setOrderNo(newReportDTO.getOrderNo());
//            BaseResponse<MasterOrderInfoDTO> baseResponseMasterOrderInfoDTOFor10553 =orderFacade.getMasterOrderInfo(orderIdReqFor10553);
//            MasterOrderInfoDTO masterOrderInfoDTOFor10553 = baseResponseMasterOrderInfoDTOFor10553.getData();
//            if(Func.isNotEmpty(masterOrderInfoDTOFor10553.getResponsibleCS())) {
//                newReport.setResponsibleCS(masterOrderInfoDTOFor10553.getResponsibleCS());
//            }
            //-------------------------华丽的分割线-------------------------//
//            logger.info("edison orderNo & cs:{}", newReportDTO.getOrderNo()+"-------"+masterOrderInfoDTOFor10553.getResponsibleCS());
        }
        if(Func.isNotEmpty(req.getNewOrderNo())){
            newReport.setOrderNo(req.getNewOrderNo());
            OrderIdReq orderReq = new OrderIdReq();
            orderReq.setOrderNo(req.getNewOrderNo());
            BaseResponse<MasterOrderInfoDTO> orderRes =orderFacade.getMasterOrderInfo(orderReq);
            MasterOrderInfoDTO masterOrderInfo = orderRes.getData();
            if(Func.isNotEmpty(masterOrderInfo.getResponsibleCS())) {
                newReport.setResponsibleCS(masterOrderInfo.getResponsibleCS());
            }
        }
        reviseForm.setNewReport(newReport);
        //reviseType
        AmendType reviseType = AmendType.findCode(reportReworkInfo.getReviseType());
        if(Func.isNotEmpty(reviseType)){
            reviseForm.setReviseType(reviseType.getCode());
        }
        // reviseReasonType
        reviseForm.setReviseReasonType(getReviseReasonTypeDesc(reportReworkInfo.getReasonType(),orderAllDTO.getBUID().toString()));
        // reviseReason
        reviseForm.setReviseReason(reportReworkInfo.getReason());
        // reviseCausedBy
        reviseForm.setReviseCausedBy(reportReworkInfo.getCausedby());
        reviseForm.setResponsiblePerson(reportReworkInfo.getResponsiblePerson());
        // freeRevise
        if(Func.isNotEmpty(reportReworkInfo.getFreeRevise())){
            reviseForm.setFreeRevise(FreeReviseEnums.enumOf(reportReworkInfo.getFreeRevise()).getName());
        }
        reviseForm.setApproveBy(getApproveName(reportReworkInfo.getApproverBy()));

        this.setReviseApproveSign(reviseForm,orderAllDTO.getLabDTO().getLabCode(),orderAllDTO,signatureLanguage);
        if(Func.isNotEmpty(reportReworkInfo.getApproverTime())){
            reviseForm.setApproveDate(DateFormatUtil.formatDate("yyyy/MM/dd HH:mm",reportReworkInfo.getApproverTime()));
        }
        reviseForm.setReplyRemark(reportReworkInfo.getAuditRemark());
        digitalReportInfo.getVariableDataList().setReviseForm(reviseForm);
        response.setData(digitalReportInfo);

        //打印输出
        logger.info("edison response:{}", response);

        return response;
    }

    private void setReviseApproveSign(ReviseFormDTO reviseForm,String labCode,OrderAllDTO orderAllDTO ,Integer signatureLanguage){
        String reportLanguage = orderAllDTO.getReportLanguage();
        //设置approveBy签名
        UserSignatureQueryRequest userSignReq = new UserSignatureQueryRequest();
        userSignReq.setLanguageCodes(Arrays.asList(LanguageType.English.getCode(),LanguageType.Chinese.getCode()));
        userSignReq.setLabCode(labCode);
        userSignReq.setIsSignature("1");
        userSignReq.setAuthSignTypes(com.google.common.collect.Lists.newArrayList(SignTypeEnums.REVISE_APPROVE.getCode()));
        List<UserSignatureDTO> userSignatureVO = frameWorkClient.queryUserSinature(userSignReq);
        List<UserSignatureDTO> approverSignCnList = new ArrayList<>();
        if(Func.isNotEmpty(userSignatureVO)){
            approverSignCnList = userSignatureVO.stream().filter(i->StringUtils.equalsIgnoreCase(i.getLanguageCode(),LanguageType.Chinese.getCode())).collect(Collectors.toList());
            approverSignCnList = approverSignCnList.stream().filter(i->  (StringUtils.equalsIgnoreCase(reviseForm.getApproveBy(),i.getRegionAccount()) || StringUtils.equalsIgnoreCase(reviseForm.getApproveBy(),Func.toStr(i.getEmail()).contains("@")?i.getEmail().split("@")[0]:"")) && Func.isNotEmpty(i.getAuthSignTypeKey()) && i.getAuthSignTypeKey().stream().anyMatch(k->Func.equals(k,Func.toStr(SignTypeEnums.REVISE_APPROVE.getCode())))).collect(Collectors.toList());
        }
        if(com.alibaba.dubbo.common.utils.CollectionUtils.isNotEmpty(approverSignCnList))
        {
            Map<String,String> param = new HashMap<>();
            param.put("legalEntityCode",orderAllDTO.getLegalEntityCode());
            param.put("locationCode",orderAllDTO.getLocationCode());
            String companyName = frameWorkClient.uploadByStream(param,LanguageType.Chinese.getLanguageId());

            UserSignatureDTO userSignatureDTO = approverSignCnList.get(0);
            SignaturesInfoDTO signaturesInfoDTO = new SignaturesInfoDTO();
            signaturesInfoDTO.setCloudId(Constants.CLOUD_PREFIX+userSignatureDTO.getAutographId());
            signaturesInfoDTO.setCompanyName(companyName);
            signaturesInfoDTO.setUserName(userSignatureDTO.getSignatureName());
            signaturesInfoDTO.setTitle(userSignatureDTO.getTitle());
            signaturesInfoDTO.setBranch(userSignatureDTO.getBranch());
            reviseForm.setApproverSign(signaturesInfoDTO);
        }
    }

    private String getApproveName(String approveName){
        if(Func.isEmpty(approveName) || !approveName.contains("@")){
            return approveName;
        }
        return approveName.split("@")[0];
    }

    private String getReviseReasonTypeDesc(Integer reason,String buId){
        if(Func.isNotEmpty(reason)){
            return frameWorkClient.getDataDictionaryValue(Constants.DATADICTIONARY_TYPE.ReworkReasonType,reason,buId);
        }
        return "";
    }

    /**
     * 根据用户名获取邮箱
     *
     * @param regionAccount
     * @return
     */
    private UserDTO getUserByRegionAccount(String regionAccount) {
        UserDTO userDTO = new UserDTO();
        Map<String, Object> params = Maps.newHashMap();
        params.put("page", 1);
        params.put("rows", 10);
        params.put("regionAccount", regionAccount);
        params.put("accurateFlag",1);
        String userJson = frameWorkClient.queryUserInfoList(params);
        logger.info("getUserByRegionAccount:{}", userJson);
        if (Func.isNotEmpty(userJson)) {
            JSONObject jsonObject = JSON.parseObject(userJson);
            JSONArray jsonArray = JSONArray.parseArray(jsonObject.get("list").toString());
            if (Func.isNotEmpty(jsonArray)) {
                JSONObject json = jsonArray.getJSONObject(0);
                String email = json.getString("email");
                String tel = json.getString("telephone");
                if(Func.isNotEmpty(email)){
                    userDTO.setEmail(email);
                }
                if(Func.isNotEmpty(tel)){
                    userDTO.setTelephone(tel);
                }
            }
        }
        return userDTO;
    }


    public BaseResponse<DigitalReportDataSourceDTO2> buildDigitalReportDataSource2(BuildDigitalReportRequestReq req) {
        DigitalReportDataSourceDTO2 digitalReportInfo=new DigitalReportDataSourceDTO2();
        digitalReportInfo.setVariableDataList(new VariableDataList2());
        digitalReportInfo.setProductLineCode(req.getProductLineCode());
        try{
            // 订单信息
            OrderIdReq orderIdReq = new OrderIdReq();
            orderIdReq.setOrderNo(req.getOrderNo());
            orderIdReq.setProductLineCode(req.getProductLineCode());
            BaseResponse<OrderAllDTO> orderForPe = orderFacade.getOrderForPe(orderIdReq);
            if (Func.isEmpty(orderForPe) || Func.isEmpty(orderForPe.getData())){
                BaseResponse.newFailInstance("order不存在");
            }
            OrderAllDTO orderAllDTO = orderForPe.getData();
            // MasterList详细信息
            MasterListReq masterListRequest  = new MasterListReq();
            masterListRequest.setProductLineCode(masterListRequest.getSourceProductLineCode());
            masterListRequest.setLabSectionList(req.getLabSectionList());
            masterListRequest.setOrderNo(req.getOrderNo());
            masterListRequest.setTestLineInstanceId(req.getTestLineInstanceId());
            masterListRequest.setPrintType(req.getPrintType());
            CustomResult masterListInfoRes = masterService.showMasterList(masterListRequest);
            if(Func.isEmpty(masterListInfoRes)|| Func.isEmpty(masterListInfoRes.getData())){
                return  BaseResponse.newSuccessInstance(digitalReportInfo);
            }
            MasterListDataMR masterListData = (MasterListDataMR)masterListInfoRes.getData();
            String language =frameWorkClient.getPrimaryLanguageCode(masterListRequest.getProductLineCode());
            int languageId = UserInfoDefaultLanguageCodeEnums.getIdByCode(language);
            // 订单信息
            initOrder(digitalReportInfo,masterListData,orderAllDTO);
            initReport2(digitalReportInfo,orderAllDTO);
            // Customer
            digitalReportInfo.getVariableDataList().setCustomers(getCustomerInfo(req.getOrderNo(),languageId));
            // reportSummary
            initReportSummary(digitalReportInfo,masterListData);
            // testLine
            digitalReportInfo.getVariableDataList().setTestLine(getTestLine(masterListData.getTestLineListMRS(),orderAllDTO.getID()));
            // job
            initJob(digitalReportInfo,masterListData);
            // DFF
            initDff2(digitalReportInfo,orderAllDTO);
            // sampleList
            initSampleList(digitalReportInfo,orderAllDTO);

            // subcontract
            digitalReportInfo.getVariableDataList().setSubcontract(
                    getSubcontract(req.getSubContractList(),masterListData.getTestLineListMRS(),orderAllDTO));
        }catch (Exception e){
            logger.error(e.getMessage(),e);
            logger.info("==========buildDigitalReportDataSoucre->errorMessage：{}============", e.getMessage());
            e.printStackTrace();
            return BaseResponse.newFailInstance("to digital report construct convert error");
        }
        return  BaseResponse.newSuccessInstance(digitalReportInfo);
    }

    private void initOrder(DigitalReportDataSourceDTO2 digitalReportInfo ,MasterListDataMR masterListData,OrderAllDTO orderAllDTO){
        OrderInfoDTO order = new OrderInfoDTO();
        MasterHeadData headData = masterListData.getHeadData();
        ReportSummaryVO reportSummary = masterListData.getReportSummary();
        if(Func.isEmpty(headData)||Func.isEmpty(order)){
            return;
        }
        OrderIdReq orderIdReq = new OrderIdReq();
        orderIdReq.setOrderNo(headData.getOrderNo());
        orderIdReq.setProductLineCode(ProductLineContextHolder.getProductLineCode());
        BaseResponse<TestRequestRsp> orderTestRequestRes = orderFacade.getTestRequestByOrderNo(orderIdReq);
        BaseResponse<List<ProductInstanceDTO>> productRes = orderFacade.queryProductInstaceForPe(orderIdReq);
        if(Func.isNotEmpty(productRes) || Func.isNotEmpty(productRes.getData())){
            List<ProductInstanceDTO> productInstanceList = productRes.getData();
            // 中文信息
            ProductInstanceDTO productInstanceCN = productInstanceList.stream().filter(item->Func.isEmpty(item.getHeaderID())&&
                    LanguageType.check(item.getLanguageID(),LanguageType.Chinese)).findFirst().orElse(null);
            if(Func.isNotEmpty(productInstanceCN)){
                order.setManufacturerCnName(productInstanceCN.getFactoryName());
                order.setBuyerCnName(productInstanceCN.getBuyerAliase());
            }
            // 英文信息
            ProductInstanceDTO productInstanceEN = productInstanceList.stream().filter(item->Func.isEmpty(item.getHeaderID())&&
                    LanguageType.check(item.getLanguageID(),LanguageType.English)).findFirst().orElse(null);
            if(Func.isNotEmpty(productInstanceEN)){
                order.setManufacturerEnName(productInstanceEN.getFactoryName());
                order.setBuyerEnName(productInstanceEN.getBuyerAliase());
            }
        }
        order.setOrderNo(orderAllDTO.getOrderNo());
        if(Func.isNotEmpty(orderAllDTO.getExpectedOrderDueDate())){
            order.setOrderExpectDueDate(DateFormatUtil.formatDate("MM-dd HH:mm",orderAllDTO.getExpectedOrderDueDate()));
        }
        order.setPrintTime(DateFormatUtil.formatDate("MM-dd HH:mm",new Date()));
        order.setServiceType(headData.getServiceType());
        LabDTO labDTO = orderAllDTO.getLabDTO();
        order.setLabCode(labDTO.getLabCode());
        order.setProductLineCode(labDTO.getBuCode());
        order.setLocationCode(labDTO.getLocationCode());
        order.setLabName(labDTO.getLabName());
        order.setSubcontractFrom(headData.getSubContractFromLabCode());
        order.setResponsibleCS(headData.getCsName());
        if(Func.isNotEmpty(orderAllDTO.getOrderConfirmDate())){
            order.setOrderConfirmDate(DateFormatUtil.formatDate("MM-dd HH:mm",orderAllDTO.getOrderConfirmDate()));
        }
        if(Func.isNotEmpty(orderAllDTO.getCreatedDate())){
            order.setOrderCreatedDate(DateFormatUtil.formatDate("MM-dd HH:mm",orderAllDTO.getCreatedDate()));
        }
        if(Func.isNotEmpty(orderAllDTO.getServiceStartDate())){
            order.setServiceStartDate(DateFormatUtil.formatDate("MM-dd HH:mm",orderAllDTO.getServiceStartDate()));
        }
        if(Func.isNotEmpty(orderAllDTO.getSampleConfirmDate())){
            order.setSampleConfirmDate(DateFormatUtil.formatDate("MM-dd HH:mm",orderAllDTO.getSampleConfirmDate()));
        }
        order.setResponsibleTeam(orderAllDTO.getResponsibleTeamCode());
        order.setResponsibleCSTel(orderAllDTO.getcSContact());
        order.setResponsibleCSEmail(orderAllDTO.getcSEmail());
        order.setTlQty(headData.getTlCount());
//        order.setPhotoRequired(headData.getPhotoRequest());
        order.setPhotoRequest(headData.getPhotoRequest());
        order.setNeedConclusion(headData.getCommentRequired());
        order.setReportLanguage(headData.getReportLanguage());
        order.setReturnSampleWay(headData.getSampleReturn());
        String testOnly = "No";
        if(OperationModeEnum.check(orderAllDTO.getOperationMode(),OperationModeEnum.TestingOnly)){
            testOnly = "Yes";
        }
        order.setTestOnly(testOnly);
        String needNotification = "No";

        order.setNeedNotification(needNotification);
        if(Func.isNotEmpty(orderTestRequestRes) && Func.isNotEmpty(orderTestRequestRes.getData())){
            TestRequestRsp testRequestRsp = orderTestRequestRes.getData();
            order.setQualificatate(testRequestRsp.getQualificationType());
            String photoRequired = Func.equalsSafe(testRequestRsp.getTakePhotoFlag(),1)?"Yes":"No";
            order.setPhotoRequired(photoRequired);
        }
        List<OrderReferenceNoRsp> orderReferenceNoRes = orderFacade.getReferenceNoByOrderNo(orderIdReq).getData();
        //GPO2-14291 合并subcontract order no、Customer Reference No 显示
        // subcontract order no、Customer Reference No都有值/只有subcontract order no
        // 只有Customer Reference No有值，取Customer Reference No
        String referenceNo = "";
        if(Func.isNotEmpty(orderReferenceNoRes)){
            OrderReferenceNoRsp orderReferenceNoRsp = orderReferenceNoRes.get(0);
            String sgsReferenceOrderNo = orderReferenceNoRsp.getReferenceOrderNo();
            order.setSgsReferenceOrderNo(Func.toStr(sgsReferenceOrderNo));
            order.setCustomerReferenceNo(Func.toStr(orderReferenceNoRsp.getCustomerReferenceNo()));
//            if(Func.isNotEmpty(sgsReferenceOrderNo)){
//                referenceNo +=sgsReferenceOrderNo;
//                if(Func.isNotEmpty(order.getCustomerReferenceNo())){
//                    referenceNo += "-";
//                    referenceNo += order.getCustomerReferenceNo();
//                }
//            }else if(Func.isNotEmpty(order.getCustomerReferenceNo())){
//                referenceNo += order.getCustomerReferenceNo();
//            }

            referenceNo = Func.isNotEmpty(sgsReferenceOrderNo)?sgsReferenceOrderNo:Func.toStr(orderReferenceNoRsp.getCustomerReferenceNo());
            if(Func.isEmpty(referenceNo)){
                referenceNo = "/";
            }
        }
        order.setReferenceNo(referenceNo);
        order.setRemark(headData.getOrderRemark());
        if(Func.isNotEmpty(reportSummary)){
            order.setSampleQty(reportSummary.getSampleNumber());
            order.setReportQty(reportSummary.getReportNumber());
        }
        order.setResponseTSE(orderAllDTO.getTsPerson());
        order.setLabTeam(headData.getLabTeam());
        order.setKa(headData.getKa());
        order.setSampleSendTo(headData.getSampleDeliverTo());
        order.setKaList(orderAllDTO.getKaList());
//        ReportReceiverReq reportReceiverReq = new ReportReceiverReq();
//        reportReceiverReq.setOrderNo(orderAllDTO.getOrderNo());
//        reportReceiverReq.setProductLineCode(orderAllDTO.getProductLineCode());
//        BaseResponse<ReportReceiverRsp> reportReceiverRsp = orderFacade.getReportReceiverInfo(reportReceiverReq);
//        if(Func.isNotEmpty(reportReceiverRsp)&&Func.isNotEmpty(reportReceiverRsp.getData())){
//            ReportReceiverRsp reportReceiver = reportReceiverRsp.getData();
//            order.setReportAddress(reportReceiver.getReportAddressEN());
//            order.setReportAddressCN(reportReceiver.getReportAddressCN());
//            order.setReportHeader(reportReceiver.getReportHeaderEN());
//            order.setReportHeaderCN(reportReceiver.getReportHeaderCN());
//        }
        digitalReportInfo.getVariableDataList().setOrder(order);
    }

    private List<SubcontractDTO> getSubcontract(List<SubContractInfo> subContractInfoList,List<TestLineListMR> testLineListMRList,OrderAllDTO orderAllDTO){
        List<SubcontractDTO> subcontractList = Lists.newArrayList();
        if(Func.isNotEmpty(subContractInfoList)){
            List<String> subcontractNoList = subContractInfoList.stream().map(SubContractInfo::getSubContractNo).filter(Func::isNotEmpty).distinct().collect(Collectors.toList());
            subContractInfoList.stream().forEach(s->{
                SubcontractDTO subcontractDTO = new SubcontractDTO();
                // 主信息
                String subcontractId = s.getId();
                // 主信息查询
                GpnQuerySubContractReq req = new GpnQuerySubContractReq();
                req.setSubContractId(subcontractId);
                req.setProductLineCode(ProductLineContextHolder.getProductLineCode());
                CustomResult subcontractRes = ProductLineServiceHolder.getProductLineService(ISubContractService.class).querySubContract(req);
                if(Func.isNotEmpty(subcontractRes) && Func.isNotEmpty(subcontractRes.getData())){
                    GpnSubContractRsp gpnSubContractRsp = (GpnSubContractRsp)subcontractRes.getData();
                    SubcontractRequirementInfo subcontractRequirement = gpnSubContractRsp.getSubcontractRequirement();

                    subcontractDTO.setSubcontractNo(gpnSubContractRsp.getSubContractNo());
                    subcontractDTO.setReportNo("");
                    subcontractDTO.setOtherReferenceNo(gpnSubContractRsp.getSlimJobNo());
                    subcontractDTO.setSamplePreparationBeforeSubcontract("");
                    if(Func.isNotEmpty(s.getCreatedDate())){
                        subcontractDTO.setSubcontractCreatedDate(DateFormatUtil.formatDate("yyyy/MM/dd HH:mm",s.getCreatedDate()));
                    }
                    if(Func.isNotEmpty(orderAllDTO.getLabDTO())){
                        subcontractDTO.setSubcontractFrom(orderAllDTO.getLabDTO().getLabCode());
                    }
                    subcontractDTO.setSubcontractFromContactName(orderAllDTO.getcSName());
                    List<String> emailList = getSubContractContactInfoList(subcontractId,orderAllDTO.getOrderNo(),orderAllDTO.getcSEmail());
                    if(Func.isNotEmpty(emailList)){
                        subcontractDTO.setSoftCopyDeliveryTo(emailList.stream().distinct().collect(Collectors.joining("<br>")));
                    }
                    subcontractDTO.setSubcontractFromContactTel(orderAllDTO.getcSContact());
                    subcontractDTO.setSubcontractFromContactEmail(orderAllDTO.getcSEmail());
                    subcontractDTO.setSubcontractTo(gpnSubContractRsp.getSubContractLabCode());
                    subcontractDTO.setSubcontractToContactEmail(gpnSubContractRsp.getSubContractContractEmail());
                    subcontractDTO.setSubcontractToContactName(gpnSubContractRsp.getSubContractContract());
                    subcontractDTO.setSubcontractToContactTel(gpnSubContractRsp.getSubContractContractTel());
                    if(Func.isNotEmpty(gpnSubContractRsp.getSubContractExpectDueDate())){
                        subcontractDTO.setExpectDueDate(DateFormatUtil.formatDate("yyyy/MM/dd HH:mm",gpnSubContractRsp.getSubContractExpectDueDate()));
                    }
                    subcontractDTO.setServiceType(gpnSubContractRsp.getSubContractServiceType());
                    subcontractDTO.setSubcontractRemark(gpnSubContractRsp.getSubContractRemark());

                    subcontractDTO.setAdditionalInfo(gpnSubContractRsp.getAdditionalInfo());
                    if(Func.isNotEmpty(gpnSubContractRsp.getStartDate())){
                        subcontractDTO.setStartDate(DateFormatUtil.formatDate("yyyy/MM/dd HH:mm",gpnSubContractRsp.getStartDate()));
                    }
                    if(Func.isNotEmpty(gpnSubContractRsp.getCompleteDate())){
                        subcontractDTO.setCompleteDate(DateFormatUtil.formatDate("yyyy/MM/dd HH:mm",gpnSubContractRsp.getCompleteDate()));
                    }
                    subcontractDTO.setSubcontractTat(gpnSubContractRsp.getSubcontractTat());

                    if(Func.isNotEmpty(subcontractRequirement)){
                        subcontractDTO.setQualificatate(subcontractRequirement.getQualificationType());
                        subcontractDTO.setReportType(ReportRequirementEnum.getMessage(subcontractRequirement.getReportRequirement()));
                        subcontractDTO.setReportQty(subcontractRequirement.getReportQty());
                        String deliveryWay = "Softcopy";
                        if(Func.equalsSafe(subcontractRequirement.getHardCopyFlag(),1)){
                            deliveryWay = "Softcopy; Hardcopy";
                        }
                        subcontractDTO.setDeliveryWay(deliveryWay);
                        subcontractDTO.setSubcontractFee(subcontractRequirement.getSubcontractFee());
                        subcontractDTO.setSubcontractFeeCurrency(subcontractRequirement.getSubcontractFeeCurrency());
                        subcontractDTO.setOtherRequest(subcontractRequirement.getOtherRequirements());
                        // Photo Required Subcontract.photo requried
                        String photoRequired = Func.equalsSafe(subcontractRequirement.getTakePhotoFlag(),1)?"Yes":"No";
                        subcontractDTO.setPhotoRequired(photoRequired);
                        // Draft Required Subcontract.Draft required
                        String draftRequired = Func.equalsSafe(subcontractRequirement.getDraftReportRequired(),1)?"Yes":"No";
                        subcontractDTO.setDraftRequired(draftRequired);
                        String hardCopyRequired = Func.equalsSafe(subcontractRequirement.getHardCopyFlag(),1)?"Yes":"No";
                        subcontractDTO.setHardcopyRequired(hardCopyRequired);
                        // Sample Return Subcontract.sample return有勾选，Yes,否则为No
                        String sampleReturn = Func.equalsSafe(subcontractRequirement.getReturnResidueSampleFlag(),1)
                                || Func.equalsSafe(subcontractRequirement.getReturnTestedSampleFlag(),1) || Func.isNotEmpty(subcontractRequirement.getReturnResidueSampleRemark())
                                ?"Yes":"No";
                        subcontractDTO.setSampleReturn(sampleReturn);
                        // QR Code Subcontract.QR code
                        String qrCode = Func.equalsSafe(subcontractRequirement.getQrcodeFlag(),"Y")?"Yes":"No";
                        subcontractDTO.setQrCode(qrCode);
                        subcontractDTO.setInvoiceDeliverWay(subcontractRequirement.getInvoiceDeliverWay());
                        subcontractDTO.setHardCopyReportDeliverWay(subcontractRequirement.getHardCopyReportDeliverWay());

                        Integer needConclusion = subcontractRequirement.getNeedConclusion();
                        String needConclusionDesc = null;
                        if(Func.isNotEmpty(needConclusion)){
                            if(needConclusion.compareTo(0) == 0){
                                needConclusionDesc = "Not Need Conclusion";
                            }else if(needConclusion.compareTo(1) == 0){
                                needConclusionDesc = "Report Conclusion";
                            }else if(needConclusion.compareTo(2) == 0){
                                needConclusionDesc = "Need TL Conclusion";
                            }else if(needConclusion.compareTo(3) == 0){
                                needConclusionDesc = "Not Need TL Conclusion";
                            }else if(needConclusion.compareTo(4) == 0){
                                needConclusionDesc = "Part TL Conclusion";
                            }
                        }
                        subcontractDTO.setNeedConclusion(needConclusionDesc);
                    }

                }
                // 查询分包单对应的TL
                List<String> subContractTestLineList = subContractExtMapper.queryTestLineIdBySubContractId(subcontractId);
                if(Func.isNotEmpty(subContractTestLineList)){
                    List<TestLineListMR> testLineListMRListNew = testLineListMRList.stream().filter(tl->
                        subContractTestLineList.contains(tl.getId())
                    ).collect(Collectors.toList());
                    subcontractDTO.setTestLine(getTestLine(testLineListMRListNew,orderAllDTO.getID()));
                }
                // 汇总去重TL对应的Sample信息
                if(Func.isNotEmpty(subcontractDTO.getTestLine())){
                    List<TestLineInfoDTO> testLineList = subcontractDTO.getTestLine();
                    List<String> sampleIds = Lists.newArrayList();
                    List<SampleLanguageInfoDTO> samples = Lists.newArrayList();
                    testLineList.stream().forEach(tl->{
                        List<SampleLanguageInfoDTO> tlSamples = tl.getSamples();
                        if(Func.isNotEmpty(tlSamples)){
                            tlSamples.stream().forEach(sample->{
                                if(Func.isNotEmpty(sample.getEnglishSample())){
                                        if(!sampleIds.contains(sample.getEnglishSample().getSampleID())){
                                            sampleIds.add(sample.getEnglishSample().getSampleID());
                                            samples.add(sample);
                                        }
                                }
                                if(Func.isNotEmpty(sample.getChineseSample())){
                                    if(!sampleIds.contains(sample.getChineseSample().getSampleID())){
                                        sampleIds.add(sample.getChineseSample().getSampleID());
                                        samples.add(sample);
                                    }
                                }
                            });
                        }
                    });
                    if(Func.isNotEmpty(samples)){
                        List<SamplesInfoDTO> enList = samples.stream().map(e -> e.getEnglishSample()).filter(Func::isNotEmpty).collect(Collectors.toList());
                        List<SamplesInfoDTO> cnList = samples.stream().map(e -> e.getChineseSample()).filter(Func::isNotEmpty).collect(Collectors.toList());
                        if(Func.isNotEmpty(enList)){
                            //排序
                            samples.sort(Comparator.comparing(item -> item.getEnglishSample().getSort(), Comparator.nullsLast(BigDecimal::compareTo)));
                        } else if(Func.isNotEmpty(cnList)){
                            samples.sort(Comparator.comparing(item -> item.getChineseSample().getSort(), Comparator.nullsLast(BigDecimal::compareTo)));
                        }
                    }
                    subcontractDTO.setSubcontractSampleQty(samples.size());
                    subcontractDTO.setSubcontractSamples(samples);
                }
                ReportLanguage reportLanguage = ReportLanguage.findName(orderAllDTO.getReportLanguage());
                if (reportLanguage != null) {
                    subcontractDTO.setLanguage(reportLanguage.getName());
                }
                List<ReportInfoDTO> reportList = new ArrayList<>();
                List<ReportInfoPO> reportInfoPOList = reportMapper.queryReportBySubContracts(subcontractNoList);

                if(Func.isNotEmpty(reportInfoPOList)) {
                    reportInfoPOList = reportInfoPOList.stream()
                            .filter(item -> Func.isNotEmpty(item.getReportNo()) && !ReportStatus.checkCategory(item.getReportStatus(),Constants.REPORT.STATUS_CATEGORY.INACTIVE)
                                    && ReportFlagEnums.check(item.getReportFlag(),ReportFlagEnums.REPORT)).collect(Collectors.collectingAndThen(
                            Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(ReportInfoPO::getReportNo))), ArrayList::new));
                    reportInfoPOList.forEach(item -> {
                        ReportInfoDTO reportInfoDTO = new ReportInfoDTO();
                        String displayReportNo = item.getReportNo();
                        if (Func.isNotEmpty(item.getActualReportNo())) {
                            displayReportNo = item.getActualReportNo();
                        }
                        reportInfoDTO.setReportNo(displayReportNo);
                        reportList.add(reportInfoDTO);
                    });
                }
                if(reportList.size()>0){
                    subcontractDTO.setReportList(reportList);
                }
                subcontractList.add(subcontractDTO);
            });
        }
        return subcontractList;
    }


    private List<String> getSubContractContactInfoList(String subContractId,String orderNo,String csEmail){
        // 类型：1：softcopy , 2：hardcocpy , 3：Reort, 4: return sample, 5: invoice , 6: prelam,7: prelam cc,  8: softcopy cc
        List<String> emailList = Lists.newArrayList();
        SubcontractRequirementContactsPO subReqContacts = subContractRequirementContactsExtMapper.getSubRequirementContactsByOrderId(subContractId,1);
        if (subReqContacts == null){
            return emailList;
        }
        List<SubContractContactInfo> subContracts = Lists.newArrayList();
        SubContractContactInfo subContract = new SubContractContactInfo();
        String deliverOthers = subReqContacts.getDeliver_others();
        if (StringUtils.isNotBlank(deliverOthers)){
            subContract.setContract(deliverOthers);
            subContract.setContractEmail(deliverOthers);
            subContracts.add(subContract);
        }
        String deliverTo = subReqContacts.getDeliver_to();
        if (Func.isNotEmpty(deliverTo)){
            for (String deliver: deliverTo.split(",")) {
                CustomerType customerType = CustomerType.findStatus(NumberUtil.toInt(deliver));
                if (customerType == null){
                    continue;
                }
                subContract = new SubContractContactInfo();
                switch (customerType){
                    case Applicant:
                    case Payer:
                    case Buyer:
                    case Agent:
                    case Supplier:
                    case Manufacture:
                        CustomResult<CustomerSimplifyInfoRsp> rspResult = customerClient.getCustomerSimplifyInfo(orderNo, customerType);
                        CustomerSimplifyInfoRsp customer = rspResult.getData();
                        if (customer == null){
                            continue;
                        }
                        subContract.setContract(customer.getContactPersonName());
                        subContract.setContractEmail(customer.getContactPersonEmail());
                        subContracts.add(subContract);
                        break;
                    case SUBCONTRACTFROM:
                        subContract.setContractEmail(csEmail);
                        subContracts.add(subContract);
                        break;
                }
            }
        }

        if(Func.isNotEmpty(subContracts)){
            emailList = subContracts.stream().filter(item->Func.isNotEmpty(item.getContractEmail())).map(SubContractContactInfo::getContractEmail).distinct().collect(Collectors.toList());
        }
        return emailList;
    }

    private String getHardCopyDeliverTo(String reportNo){
        String hardCopyDeliverTo = "";
        GpoParcelTodoListDTO gpoParcelTodoListReq = new GpoParcelTodoListDTO();
        gpoParcelTodoListReq.setReportNo(reportNo);
        gpoParcelTodoListReq.setExpressContent(ExpressContentType.HARDCOPY_REPORT.getCode());
        gpoParcelTodoListReq.setBuCode(ProductLineContextHolder.getProductLineCode());
        gpoParcelTodoListReq.setProductLineCode(ProductLineContextHolder.getProductLineCode());
        BaseResponse<List<ParcelTodoListRsp>> parcelRes  = toDoListFacade.selectParcelTodoList(gpoParcelTodoListReq);
        Set<String> deliverToSet = Sets.newHashSet();
        if(Func.isNotEmpty(parcelRes) && Func.isNotEmpty(parcelRes.getData())){
            List<ParcelTodoListRsp> parcelTodoList = parcelRes.getData();
            parcelTodoList.stream().forEach(parcel->{
                List<String> deliverToList = Lists.newArrayList();
                if(Func.isNotEmpty(parcel.getAddress())){
                    deliverToList.add(parcel.getAddress());
                }
                if(Func.isNotEmpty(parcel.getContact())){
                    deliverToList.add(parcel.getContact());
                }
                if(Func.isNotEmpty(parcel.getContractTel())){
                    deliverToList.add(parcel.getContractTel());
                }
                if(Func.isNotEmpty(deliverToList)){
                    deliverToSet.add(deliverToList.stream().collect(Collectors.joining("/")));
                }

            });
        }
        if(Func.isNotEmpty(deliverToSet)){
            return deliverToSet.stream().collect(Collectors.joining("<br>"));
        }
        return hardCopyDeliverTo;
    }

    /**
     * 查询订单勾选的
     * @param orderDTO
     * @return
     */
    private Set<String> getOrderContactEmailList(OrderAllDTO orderDTO,TestRequestInfo testRequestInfo,Integer contactType){
        if(Func.isEmpty(orderDTO) || Func.isEmpty(contactType)){
            return null;
        }
        Set<String> emailList = Sets.newHashSet();
        if(Func.isEmpty(testRequestInfo) || Func.isEmpty(testRequestInfo.getTestRequestContactsInfos())){
            return emailList;
        }
        List<TestRequestContactsInfo> testRequestContactsInfos = testRequestInfo.getTestRequestContactsInfos();
        testRequestContactsInfos = testRequestContactsInfos.stream().filter(item->Func.equalsSafe(contactType,item.getContactsType())).collect(Collectors.toList());
        if(Func.isEmpty(testRequestContactsInfos)){
            return emailList;
        }
        TestRequestContactsInfo testRequestContactsInfo = testRequestContactsInfos.get(0);
        //
        String others = testRequestContactsInfo.getDeliverOthers();
        if(Func.isNotEmpty(others)){
            emailList.add(others);
        }
        String deliverTo = testRequestContactsInfo.getDeliverTo();
        if (Func.isNotEmpty(deliverTo)){
            for (String deliver: deliverTo.split(",")) {
                CustomerType customerType = CustomerType.findStatus(NumberUtil.toInt(deliver));
                if (customerType == null){
                    continue;
                }
                switch (customerType){
                    case Applicant:
                    case Payer:
                    case Buyer:
                    case Agent:
                    case Supplier:
                    case Manufacture:
                    case SUBCONTRACTFROM:
                        CustomResult<CustomerSimplifyInfoRsp> rspResult = customerClient.getCustomerSimplifyInfo(orderDTO.getOrderNo(), customerType);
                        CustomerSimplifyInfoRsp customer = rspResult.getData();
                        if (customer == null){
                            continue;
                        }
                        if(Func.isNotEmpty(customer.getContactPersonEmail())){
                            emailList.add(customer.getContactPersonEmail());
                        }
                        break;
                    case CS:
                        if(Func.isNotEmpty(orderDTO.getcSEmail())){
                            emailList.add(orderDTO.getcSEmail());
                        }
                        break;
                    case SALES:
                        if(Func.isNotEmpty(orderDTO.getSalesPerson())){
                            emailList.add(orderDTO.getSalesPersonEmail());
                        }
                        break;
                }
            }
        }
        return emailList;
    }



    private void initReportSummary(DigitalReportDataSourceDTO2 digitalReportInfo ,MasterListDataMR masterListData){
        List<ReportSummaryDTO> reportSummaryList = Lists.newArrayList();
        if(Func.isEmpty(masterListData)||Func.isEmpty(masterListData.getReportSummary())){
            return;
        }
        ReportSummaryVO reportSummary = masterListData.getReportSummary();
        List<TestLineForSummaryVO> testLines = reportSummary.getTestLines();
        Map<String,ReportDTO> reportDTOMap = Maps.newHashMap();
        if(Func.isNotEmpty(testLines)){
            testLines.stream().forEach(testLine->{
                ReportSummaryDTO reportSummaryDTO = new ReportSummaryDTO();
                reportSummaryDTO.setReportNo(testLine.getExternalReportNo());
                reportSummaryDTO.setTestlineName(testLine.getTestLineName());
                reportSummaryDTO.setCombinedSamples(testLine.getSampleNos());
                reportSummaryDTO.setLabTeam(testLine.getLabTeamName());
                reportSummaryDTO.setRemark(testLine.getOrderTestLineRemark());
                reportSummaryDTO.setTestlineDueDate(testLine.getTestDueDate());
                reportSummaryDTO.setJobNo(testLine.getJobNo());
                List<String> combinedNos = Lists.newArrayList();
                if(Func.isNotEmpty(testLine.getReportNo())){
                    combinedNos.add(testLine.getReportNo());
                }
                if(Func.isNotEmpty(testLine.getJobNo())){
                    combinedNos.add(testLine.getJobNo());
                }
                reportSummaryDTO.setCombinedReportJobNo(combinedNos.stream().collect(Collectors.joining("-")));
                String reportNo = testLine.getReportNo();
                ReportDTO reportDTO= null;
                if(reportDTOMap.containsKey(reportNo)){
                     reportDTO = reportDTOMap.get(reportNo);
                }else {
                     reportDTO = reportMapper.getByReportNo(reportNo);
                    reportDTOMap.put(reportNo,reportDTO);
                }
                if(Func.isNotEmpty(reportDTO)&&Func.isNotEmpty(reportDTO.getReportDueDate())){
                    reportSummaryDTO.setReportDueDate(DateFormatUtil.formatDate("MM-dd HH:mm",reportDTO.getReportDueDate()));
                }
                reportSummaryList.add(reportSummaryDTO);

            });
        }
        digitalReportInfo.getVariableDataList().setReportSummary(reportSummaryList);
    }

    private List<TestLineInfoDTO> getTestLine(List<TestLineListMR> testLineListMRList,String orderId){
        List<TestLineInfoDTO> testLines = Lists.newArrayList();
        if(Func.isEmpty(testLineListMRList)){
            return testLines;
        }

        AtomicReference<Integer> sequenceNo = new AtomicReference<>(1);
        testLineListMRList.stream().forEach(testLineListMR -> {
            TestLineInfoDTO testLineInfoDTO = new TestLineInfoDTO();
            testLineInfoDTO.setTestLineInstanceId(testLineListMR.getId());
            testLineInfoDTO.setTestLineId(testLineListMR.getTestLineID());
            testLineInfoDTO.setEvaluationAlias(testLineListMR.getTestItemName());
            testLineInfoDTO.setStandardName(testLineListMR.getStandardName());
            testLineInfoDTO.setTestLineNo(testLineListMR.getTestItemNo());
            testLineInfoDTO.setTestLineDueDate(testLineListMR.getTestDueDate());
            testLineInfoDTO.setLabTeam(testLineListMR.getLabTeam());
            testLineInfoDTO.setTestEngineer(testLineListMR.getEngineer());
            testLineInfoDTO.setCombinedSampleName(testLineListMR.getSampleName());
            testLineInfoDTO.setCombinedSampleNo(testLineListMR.getSampleNo());
            testLineInfoDTO.setCombinedSampleMaterial(testLineListMR.getMaterial());
            testLineInfoDTO.setCombinedSampleDescription(testLineListMR.getSampleDesc());
            testLineInfoDTO.setCombinedSamplePreparation(testLineListMR.getSampleSegegrationWIText());
            testLineInfoDTO.setWI(testLineListMR.getWiForTestText());
            testLineInfoDTO.setCombinedTestStandard(testLineListMR.getStandardName());
            testLineInfoDTO.setCombinedTestCondition(testLineListMR.getTestCondition());
            testLineInfoDTO.setCombinedAnalyte(testLineListMR.getAnalyte());
            testLineInfoDTO.setCombinedRemark(testLineListMR.getRemark());
//            testLineInfoDTO.setQuotationRemark(quotationRemarkList.stream().collect(Collectors.joining("<br/>")));

            testLineInfoDTO.setLabSection(testLineListMR.getLabSectionName());
            List<PrintTestMatrixInfo> testMatrixInfoList = testLineListMR.getPrintTestMatrixInfoList();
            if(Func.isNotEmpty(testMatrixInfoList)){
                List<MatrixDTO> matrixList = Lists.newArrayList();
                testMatrixInfoList.stream().forEach(tm->{
                    MatrixDTO matrixDTO = new MatrixDTO();
                    matrixDTO.setSampleNo(tm.getSampleNo());
                    matrixDTO.setMatrixBarcode(tm.getMatrixNo());
                    matrixDTO.setTestMatrixId(tm.getMatrixId());
                    matrixList.add(matrixDTO);
                });
                testLineInfoDTO.setMatrixList(matrixList);
            }
            List<ReportSampleVO> reportSampleList = testLineListMR.getReportSampleList();
            if(Func.isNotEmpty(reportSampleList)){
                List<ReportSampleDTO> reportSampleDTOList = Lists.newArrayList();
                reportSampleList.stream().forEach(rs ->{
                    ReportSampleDTO reportSampleDTO = new ReportSampleDTO();
                    reportSampleDTO.setReportNo(rs.getExternalReportNo());
                    reportSampleDTO.setSampleNo(rs.getSampleDesc());
                    reportSampleDTO.setReportId(rs.getReportId());
                    Set<String> labTeamSet = new HashSet<>();
                    for(TestLineListMR item : testLineListMRList){
                        if(Func.isNotEmpty(item.getReportSampleList())){
                            List<ReportSampleVO> sampleList =item.getReportSampleList();
                            for(ReportSampleVO sample : sampleList){
                                if(sample.getReportNo().equals(rs.getExternalReportNo()) && Func.isNotEmpty(item.getLabTeamCode())){
                                    labTeamSet.add(item.getLabTeamCode());
                                }
                            }
                        }
                    }
                    String labTeamStr = new String();
                    if(Func.isNotEmpty(labTeamSet)){
                        labTeamStr = String.join(",",labTeamSet);
                    }
                    reportSampleDTO.setLabTeam(labTeamStr);
                    reportSampleDTOList.add(reportSampleDTO);
                });
                testLineInfoDTO.setReportSampleList(reportSampleDTOList);
            }
            // Sample信息
            List<ProductInstanceDTO> samples = testLineListMR.getSamples();
            if(Func.isNotEmpty(samples)){
                // 中英文处理
                Map<String,List<ProductInstanceDTO>> sampleMap = samples.stream()
                        .sorted(Comparator.comparing(ProductInstanceDTO::getSampleID).thenComparing(ProductInstanceDTO::getLanguageID))
                        .collect(Collectors.groupingBy(ProductInstanceDTO::getSampleID));
                List<SampleLanguageInfoDTO> sampleLanguages = Lists.newArrayList();
                sampleMap.forEach((key,val)->{
                    SampleLanguageInfoDTO sampleLanguageInfoDTO = new SampleLanguageInfoDTO();
                    ProductInstanceDTO enProductInstance = val.stream().filter(p->Func.equalsSafe(p.getLanguageID(),LanguageType.English.getLanguageId())).findFirst().orElse(null);
                    if(Func.isNotEmpty(enProductInstance)){
                        sampleLanguageInfoDTO.setEnglishSample(getSamplesInfoDTO(enProductInstance));
                    }
                    ProductInstanceDTO cnProductInstance = val.stream().filter(p->Func.equalsSafe(p.getLanguageID(),LanguageType.Chinese.getLanguageId())).findFirst().orElse(null);
                    if(Func.isNotEmpty(cnProductInstance)){
                        sampleLanguageInfoDTO.setChineseSample(getSamplesInfoDTO(cnProductInstance));
                    }
                    sampleLanguages.add(sampleLanguageInfoDTO);
                });
                testLineInfoDTO.setSamples(sampleLanguages);
            }
            testLineInfoDTO.setSequenceNo(sequenceNo.getAndSet(sequenceNo.get() + 1));
            testLineInfoDTO.setSampleSendTo(testLineListMR.getSampleDeliverTo());
            testLines.add(testLineInfoDTO);
        });
        return  testLines;
    }

    private SamplesInfoDTO getSamplesInfoDTO(ProductInstanceDTO productInstanceDTO){
        SamplesInfoDTO samplesInfo = new SamplesInfoDTO();
        Func.copy(productInstanceDTO,samplesInfo);
//        samplesInfo.setSampleNo(productInstanceDTO.getSampleID());
//        samplesInfo.setSampleName(productInstanceDTO.getProductDescription());
//        samplesInfo.setMaterial(productInstanceDTO.getFiberComposition());
//        samplesInfo.setConstruction(productInstanceDTO.getConstruction());
//        samplesInfo.setProductSpec(productInstanceDTO.getPeformanceCode());
//        samplesInfo.setLotNo(productInstanceDTO.getLotNo());
//        samplesInfo.setStyleNo(productInstanceDTO.getStyleNo());
//        samplesInfo.setHeatNo(productInstanceDTO.getRefCode6());
//        samplesInfo.setBuyer(productInstanceDTO.getBuyerAliase());
//        samplesInfo.setManufacturer(productInstanceDTO.getFactoryName());
//        samplesInfo.setOtherInfo(productInstanceDTO.getSpecialProductAttribute1());
//        samplesInfo.setItemNo(productInstanceDTO.getItemNo());
//        samplesInfo.setModelNo(productInstanceDTO.getStyleNo());
//        samplesInfo.setSampleDescription(productInstanceDTO.getOtherSampleInformation());
        // photos
        List<SamplePhotoInfoDTO> photos = Lists.newArrayList();
        samplesInfo.setPhotos(photos);
        // reportSampleSeq
        samplesInfo.setReportSampleSeq("");
        return samplesInfo;
    }

    private void initJob(DigitalReportDataSourceDTO2 digitalReportInfo ,MasterListDataMR masterListData){
        List<JobDTO> jobList = Lists.newArrayList();
        List<JobTestLineVO> jobTestLineList = masterListData.getJobTestLineList();
        if(Func.isEmpty(jobTestLineList)){
            return;
        }
        Map<String,JobListRsp> jobMap = Maps.newHashMap();
        // 查询Job 信息
        JobListReq jobListReq = new JobListReq();
        jobListReq.setOrderNo(masterListData.getHeadData().getOrderNo());
        OrderIdReq orderIdReq = new OrderIdReq();
        orderIdReq.setOrderNo(masterListData.getHeadData().getOrderNo());
        orderIdReq.setProductLineCode(ProductLineContextHolder.getProductLineCode());
        BaseResponse<OrderAllDTO> orderForPe = orderFacade.getOrderForPe(orderIdReq);
        OrderAllDTO orderAllDTO = orderForPe.getData();

        int page = 1;
        int rows = 9999;
        CustomResult<PageInfo<JobListRsp>> jobRes = ProductLineServiceHolder.getProductLineService(IJobService.class).queryJob(jobListReq,page,rows);
        if(Func.isNotEmpty(jobRes)&&Func.isNotEmpty(jobRes.getData())){
            List<JobListRsp> jobInfoList = jobRes.getData().getList();
            if(Func.isNotEmpty(jobInfoList)){
                jobMap  = jobInfoList.stream().collect(Collectors.toMap(JobListRsp::getJobNo,job->job));
            }
        }
        Map<String, JobListRsp> finalJobMap = jobMap;
        jobTestLineList.stream().forEach(job->{
            JobDTO jobDTO = new JobDTO();
            jobDTO.setJobNo(job.getJobNo());
            jobDTO.setJobDueDate(job.getJobDueDate());
            jobDTO.setTestLine(getTestLine(job.getTestLineListMRList(),orderAllDTO.getID()));
            if(finalJobMap.containsKey(job.getJobNo())){
                JobListRsp jobListRsp = finalJobMap.get(job.getJobNo());
                if(Func.isNotEmpty(jobListRsp)){
                    jobDTO.setJobAmount(jobListRsp.getTotalAmountDisplay());
                    jobDTO.setLabSection(jobListRsp.getLabSection());
                }
            }
            jobList.add(jobDTO);
        });
        digitalReportInfo.getVariableDataList().setJob(jobList);
    }


}
