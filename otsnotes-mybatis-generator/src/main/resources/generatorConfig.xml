<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE generatorConfiguration
  PUBLIC "-//mybatis.org//DTD MyBatis Generator Configuration 1.0//EN"
  "http://mybatis.org/dtd/mybatis-generator-config_1_0.dtd">

<generatorConfiguration>

    <context id="SqlServerTables" targetRuntime="MyBatis3">
        <!-- 自动识别数据库关键字，默认false，如果设置为true，根据SqlReservedWords中定义的关键字列表；
        一般保留默认值，遇到数据库关键字（Java关键字），使用columnOverride覆盖-->
        <property name="autoDelimitKeywords" value="true"/>
        <!--beginningDelimiter、endingDelimiter默认为（"），但Mysql中不能这么写，所以要将这两个默认值改为“单反引号（'）”-->
        <property name="beginningDelimiter" value="`"/>
        <property name="endingDelimiter" value="`"/>

        <plugin type="org.mybatis.generator.plugins.ToStringPlugin"/>
        <plugin type="com.sgs.otsnotes.mybatis.generator.RenameModelPlugin">
            <property name="prefixes2Remove" value="tb,sgs"/>
            <property name="suffix2Append" value="PO"/>
        </plugin>

        <plugin type="com.sgs.otsnotes.mybatis.generator.ForceCreateUpdateTimePlugin">
            <property name="insertTimeColumns" value="create_time"/>
            <property name="lastUpdateTimeColumns" value="modify_time"/>
            <property name="dbCurrentTimeExpr" value="now()"/>
        </plugin>

        <commentGenerator type="com.sgs.otsnotes.mybatis.generator.DBCommentGenerator">
            <property name="suppressAllComments" value="true"/>
        </commentGenerator>

        <jdbcConnection driverClass="com.mysql.jdbc.Driver"
          connectionURL="*********************************************************************************************************************************************************"
          userId="gpo_user"
          password="SH187_pYg65_Lk58h_Pa12r">
            <property name="remarksReporting" value="true"/>
        </jdbcConnection>

        <javaModelGenerator targetPackage="com.sgs.otsnotes.dbstorages.mybatis.model"
          targetProject="otsnotes-dbstorages\src\main\java">
            <property name="enableSubPackages" value="false"/>
            <property name="trimStrings" value="true"/>
        </javaModelGenerator>

        <sqlMapGenerator targetPackage="sqlmap.autogenerated" targetProject="otsnotes-dbstorages\src\main\resources">
        </sqlMapGenerator>

        <javaClientGenerator type="XMLMAPPER" targetPackage="com.sgs.otsnotes.dbstorages.mybatis.mapper"
                             targetProject="otsnotes-dbstorages\src\main\java">
        </javaClientGenerator>


<!--        <table tableName="tb_test_sample" domainObjectName="TestSample">
                        <property name="useActualColumnNames" value="true"/>
        </table>-->

<!--        <table tableName="tb_test_line_instance_multiplelanguage" domainObjectName="TestLineInstanceMultipleLanguageInfo">
            <property name="useActualColumnNames" value="true"/>
           &lt;!&ndash;ignoreColumn 设置被忽略的列名&ndash;&gt;
            <ignoreColumn column="LastModifiedTimestamp" />
        </table>-->

        <table tableName="tb_object_refer" domainObjectName="ObjectReferInfo">
            <ignoreColumn column="last_modified_timestamp" />
        </table>

<!--        <table tableName="tb_report" domainObjectName="ReportInfo">-->
<!--            <property name="useActualColumnNames" value="true"/>-->
<!--        </table>-->

<!--        <table tableName="tb_report_file" domainObjectName="ReportFile">
            <property name="useActualColumnNames" value="true"/>
        </table>-->
        <!--<table tableName="tb_test_matrix" domainObjectName="TestMatrixInfo">
            <property name="useActualColumnNames" value="true"/>
        </table>
        <table tableName="tb_test_line_instance" domainObjectName="TestLineInstance">
            <property name="useActualColumnNames" value="true"/>
        </table>
        <table tableName="tb_test_line_instance_multiplelanguage" domainObjectName="TestLineInstanceMultipleLanguageInfo">
            <property name="useActualColumnNames" value="true"/>
        </table>
        <table tableName="tb_analyte_instance" domainObjectName="AnalyteInfo">
            <property name="useActualColumnNames" value="true"/>
        </table>
        <table tableName="tb_analyte_instance_multiplelanguage" domainObjectName="AnalyteMultipleLanguageInfo">
            <property name="useActualColumnNames" value="true"/>
        </table>-->

        <!--<table tableName="tre_pp_test_line_relationship_multiplelanguage" domainObjectName="PPTestLineRelationshipMultipleLanguageInfo">-->
        <!--<property name="useActualColumnNames" value="true"/>-->
        <!--</table>-->
        <!--<table tableName="tb_lab_section_instance" domainObjectName="LabSectionInfo">
            <property name="useActualColumnNames" value="true"/>
        </table>
        <table tableName="tb_limit_instance" domainObjectName="LimitInstance">
            <property name="useActualColumnNames" value="true"/>
        </table>
        <table tableName="tb_limit_group_instance" domainObjectName="LimitGroupInstance">
            <property name="useActualColumnNames" value="true"/>
        </table>
        <table tableName="tb_limit_instance_multiplelanguage" domainObjectName="LimitMultipleLanguageInfo">
            <property name="useActualColumnNames" value="true"/>
        </table>-->


        <!--<table tableName="tre_pp_test_line_relationship" domainObjectName="PPTestLineRelationshipInfo">
            <property name="useActualColumnNames" value="true"/>
        </table>
        <table tableName="tb_pp_instance" domainObjectName="PPInstanceInfo">
            <property name="useActualColumnNames" value="true"/>
        </table>
        <table tableName="tre_pp_condition_relationship" domainObjectName="PPConditionRelationshipInfo">
            <property name="useActualColumnNames" value="true"/>
        </table>
        <table tableName="tre_pp_sample_relationship" domainObjectName="PPSampleRelationshipInfo">
            <property name="useActualColumnNames" value="true"/>
        </table>-->


        <!--<table tableName="tb_pp_instance_multiplelanguage" domainObjectName="PPMultipleLanguageInfo">
            <property name="useActualColumnNames" value="true"/>
        </table>
        <table tableName="tre_pp_test_line_relationship_multiplelanguage" domainObjectName="PPTestLineRelationshipMultipleLanguageInfo">
            <property name="useActualColumnNames" value="true"/>
        </table>
        <table tableName="tre_pp_condition_relationship_multiplelanguage" domainObjectName="PPConditionRelationshipMultipleLanguageInfo">
            <property name="useActualColumnNames" value="true"/>
        </table>


        <table tableName="tb_product_attribute_instance" domainObjectName="ProductAttributeInstanceInfo">
            <property name="useActualColumnNames" value="true"/>
        </table>
        <table tableName="tre_report_matrix_relationship" domainObjectName="ReportMatrixRelationShipInfo">
            <property name="useActualColumnNames" value="true"/>
        </table>-->

        <!--<table tableName="tb_test_sample" domainObjectName="TestSample">
            <property name="useActualColumnNames" value="true"/>
        </table>-->
        <!--<table tableName="tb_test_sample_group" domainObjectName="TestSampleGroupInfo">
            <property name="useActualColumnNames" value="true"/>
        </table>
        <table tableName="tb_slim_subcontract" domainObjectName="SlimSubcontract">
            <property name="useActualColumnNames" value="true"/>
        </table>



        <table tableName="tb_test_condition_instance_multiplelanguage" domainObjectName="TestConditionInstanceMultipleLanguage">
            <property name="useActualColumnNames" value="true"/>
        </table>
        <table tableName="tb_test_condition_group" domainObjectName="TestConditionGroupInfo">
            <property name="useActualColumnNames" value="true"/>
        </table>
        <table tableName="tb_test_condition_group_multiplelanguage" domainObjectName="TestConditionGroupMultipleLanguage">
            <property name="useActualColumnNames" value="true"/>
        </table>-->


        <!-- <table tableName="tb_test_data" domainObjectName="TestDataInfo">
             <property name="useActualColumnNames" value="true"/>
         </table>
         <table tableName="tb_test_position" domainObjectName="TestPositionInfo">
             <property name="useActualColumnNames" value="true"/>
         </table>
         <table tableName="tb_test_specimen" domainObjectName="TestSpecimenInfo">
             <property name="useActualColumnNames" value="true"/>
         </table>
 -->
<!--        <table tableName="tre_pp_test_line_relationship" domainObjectName="PPTestLineRelationshipInfo">
            <property name="useActualColumnNames" value="true"/>
        </table>-->

    </context>

</generatorConfiguration>
