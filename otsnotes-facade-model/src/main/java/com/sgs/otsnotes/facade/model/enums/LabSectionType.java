package com.sgs.otsnotes.facade.model.enums;

import java.util.HashMap;
import java.util.Map;


public enum LabSectionType {
    Lab(1, "Lab"),
    SubContractLab(2, "subContractLab"),
    NoLab(3, "noLab");

    private Integer code;
    private String name;

    LabSectionType(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public Integer getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static final Map<Integer, LabSectionType> maps = new HashMap<Integer, LabSectionType>() {
        private static final long serialVersionUID = -8986866330615001847L;
        {
            for (LabSectionType enu : LabSectionType.values()) {
                put(enu.getCode(), enu);
            }
        }
    };

    public static LabSectionType getLabSectionName(Integer code) {
        if (code == null || !maps.containsKey(code.intValue())) {
            return null;
        }
        return maps.get(code.intValue());
    }

    /**
     *
     * @param code
     * @param labType
     * @return
     */
    public static boolean check(Integer code, LabSectionType labType) {
        if (code == null || !maps.containsKey(code.intValue())){
            return false;
        }
        return maps.get(code.intValue()) == labType;
    }
}
