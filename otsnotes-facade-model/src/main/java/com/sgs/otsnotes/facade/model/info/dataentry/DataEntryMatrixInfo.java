package com.sgs.otsnotes.facade.model.info.dataentry;

import com.sgs.framework.core.base.BaseProductLine;

import java.util.List;

public class DataEntryMatrixInfo extends BaseProductLine {
    /**
     *
     */
    private String testMatrixId;
    /**
     *
     */
    private String ppNo;
    /**
     *
     */
    private String sampleId;
    /**
     *
     */
    private String sampleNo;
    /**
     *
     */
    private Integer sampleSeq;
    /**
     *
     */
    private String conclusionDesc;
    /**
     *
     */
    private List<String> usageTypes;
    /**
     *
     */
    private Integer activeIndicator;
    /**
     *
     */
    private Boolean sampleReferDataFlag;

    public String getTestMatrixId() {
        return testMatrixId;
    }

    public void setTestMatrixId(String testMatrixId) {
        this.testMatrixId = testMatrixId;
    }

    public String getPpNo() {
        return ppNo;
    }

    public void setPpNo(String ppNo) {
        this.ppNo = ppNo;
    }

    public String getSampleId() {
        return sampleId;
    }

    public void setSampleId(String sampleId) {
        this.sampleId = sampleId;
    }

    public String getSampleNo() {
        return sampleNo;
    }

    public void setSampleNo(String sampleNo) {
        this.sampleNo = sampleNo;
    }

    public Integer getSampleSeq() {
        return sampleSeq;
    }

    public void setSampleSeq(Integer sampleSeq) {
        this.sampleSeq = sampleSeq;
    }

    public String getConclusionDesc() {
        return conclusionDesc;
    }

    public void setConclusionDesc(String conclusionDesc) {
        this.conclusionDesc = conclusionDesc;
    }

    public List<String> getUsageTypes() {
        return usageTypes;
    }

    public void setUsageTypes(List<String> usageTypes) {
        this.usageTypes = usageTypes;
    }

    public Integer getActiveIndicator() {
        return activeIndicator;
    }

    public void setActiveIndicator(Integer activeIndicator) {
        this.activeIndicator = activeIndicator;
    }

    public Boolean isSampleReferDataFlag() {
        return sampleReferDataFlag;
    }

    public void setSampleReferDataFlag(Boolean sampleReferDataFlag) {
        this.sampleReferDataFlag = sampleReferDataFlag;
    }
}
