package com.sgs.otsnotes.facade.model.enums;

import java.util.HashMap;
import java.util.Map;

public enum TrimsArtifactType {
    TestLine(0, "TL"),
    PP(1, "PP");

    private int status;
    private String code;


    TrimsArtifactType(int status, String code) {
        this.status = status;
        this.code = code;
    }

    public String getCode() {
        return code;
    }

    public int getStatus() {
        return status;
    }

    static Map<Integer, TrimsArtifactType> maps = new HashMap<>();
    static Map<String, TrimsArtifactType> codeMaps = new HashMap<>();

    static {
        for (TrimsArtifactType status : TrimsArtifactType.values()) {
            maps.put(status.getStatus(), status);
            codeMaps.put(status.getCode().toLowerCase(), status);
        }
    }

    ;

    public static TrimsArtifactType getStatus(Integer status) {
        if (status == null || !maps.containsKey(status)) {
            return null;
        }
        return maps.get(status);
    }

    public static TrimsArtifactType getCode(String code) {
        if (code == null || !codeMaps.containsKey(code.toLowerCase())) {
            return null;
        }
        return codeMaps.get(code.toLowerCase());
    }

    public static boolean checkStatus(TrimsArtifactType type, TrimsArtifactType... artifactTypes) {
        if (type == null || artifactTypes == null || artifactTypes.length <= 0) {
            return false;
        }
        return check(type.getStatus(), artifactTypes);
    }

    /**
     * @param type
     * @param artifactTypes
     * @return
     */
    public static boolean check(Integer type, TrimsArtifactType... artifactTypes) {
        if (type == null || !maps.containsKey(type.intValue()) || artifactTypes == null || artifactTypes.length <= 0) {
            return false;
        }
        for (TrimsArtifactType pricStatus : artifactTypes) {
            if (type.intValue() == pricStatus.getStatus()) {
                return true;
            }
        }
        return false;
    }

    /**
     * @param code
     * @param artifactTypes
     * @return
     */
    public static boolean check(String code, TrimsArtifactType... artifactTypes) {
        if (code == null || !codeMaps.containsKey(code.toLowerCase()) || artifactTypes == null || artifactTypes.length <= 0) {
            return false;
        }
        int status = codeMaps.get(code.toLowerCase()).getStatus();
        for (TrimsArtifactType tls : artifactTypes) {
            if (tls.getStatus() == status) {
                return true;
            }
        }
        return false;
    }

    /**
     * @param artifactTypes
     * @return
     */
    public boolean check(TrimsArtifactType... artifactTypes) {
        if (artifactTypes == null || artifactTypes.length <= 0) {
            return false;
        }
        for (TrimsArtifactType tlType : artifactTypes) {
            if (this.getStatus() == tlType.getStatus()) {
                return true;
            }
        }
        return false;
    }
}
