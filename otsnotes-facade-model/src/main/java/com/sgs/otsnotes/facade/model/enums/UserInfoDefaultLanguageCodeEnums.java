package com.sgs.otsnotes.facade.model.enums;

import org.springframework.util.StringUtils;

/**
 * @ClassName UserInfoDefaultLanguageCodeEnums
 * @Description TODO
 * <AUTHOR>
 * @Date 1/22/2021
 */
public enum  UserInfoDefaultLanguageCodeEnums {
    en_us(1,"EN"),
    zh_cn(2,"CHI");
    private int id;
    private String code;


    UserInfoDefaultLanguageCodeEnums(int id, String code) {
        this.id = id;
        this.code = code;
    }


    public String getCode() {
        return code;
    }

    public int getId() {
        return id;
    }

    public static int getIdByCode(String  code){
        for (UserInfoDefaultLanguageCodeEnums enums : UserInfoDefaultLanguageCodeEnums.values()){
            if (enums.code.equals(code)){
                return enums.id;
            }
        }
        return 0;
    }

    public static boolean check(String code, UserInfoDefaultLanguageCodeEnums...enums){
        if(StringUtils.isEmpty(code)){
            return false;
        }
        for (UserInfoDefaultLanguageCodeEnums anEnum : enums) {
            if(anEnum.code.equals(code)){
                return true;
            }
        }
        return false;
    }
}
