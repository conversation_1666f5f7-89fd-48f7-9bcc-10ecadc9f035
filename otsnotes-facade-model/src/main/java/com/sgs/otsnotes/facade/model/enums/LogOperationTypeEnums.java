
package com.sgs.otsnotes.facade.model.enums;

/**
 * @description   :  LogOperationTypeEnums
 * <AUTHOR>  <PERSON><PERSON>.<PERSON>  Sun He<PERSON>yuan
 * @createDate    :  2020/7/2 3:43 PM
 * @updateUser    :  Killian.Sun  Sun He<PERSON>yuan
 * @updateDate    :  2020/7/2 3:43 PM
 * @updateRemark  :
 * @version       :  1.0
 */

public enum LogOperationTypeEnums {
	/** sample 1*/
	sample(1),
	/** testline 2*/
	testline(2),
	/** condition 3*/
	condition(3),
	/** matrix 4*/
	matrix(4),

	retest(5),

	report(6),

	reportMD5(7),

	copyLimit(8);

	private Integer code;

	LogOperationTypeEnums(Integer code) {
		this.code = code;
	}

	public Integer getCode() {

		return code;
	}

}
