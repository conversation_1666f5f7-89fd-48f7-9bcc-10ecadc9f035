package com.sgs.otsnotes.facade.model.enums;

import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR> vincent
 * @version V1.0
 * @Project: sgs-otsnotes
 * @Description: TestLine的状态枚举
 * @date Date : 2019年06月14日 15:03
 */
public enum TestLineStatus {
	Typing(701, "Typing","Edit"),
	Submit(702, "Submitted","Edit"),
	Completed(703, "Completed","Lock"),
	SubContracted(704, "Subcontracted","Lock"),
	Entered(705, "Entered","Edit"),
	Cancelled(706, "Cancelled","invalid"),
	NC(707, "Not Test","Lock"),// Not Test、NC
	DR(708, "Document Review","Lock"),
	NA(709, "NA","Lock"),
	Pending(799, "Pending","Lock");//PendingFlag为1时,TestLineStatus为799,DB中不会保存改值，仅做页面展示使用

	private int status;
	private String message;
	private String category;

	TestLineStatus(int status, String message,String category) {
		this.status = status;
		this.message = message;
		this.category = category;
	}

	public String getMessage() {
		return message;
	}

	public int getStatus() {
		return status;
	}

	public String getCategory() {
		return category;
	}

	public static final Map<Integer, TestLineStatus> maps = new HashMap<Integer, TestLineStatus>() {
		private static final long serialVersionUID = -8986866330615001847L;
		{
			for (TestLineStatus enu : TestLineStatus.values()) {
				put(enu.getStatus(), enu);
			}
		}
	};

	public static String getMessage(Integer status) {
		if (status == null || !maps.containsKey(status.intValue())) {
			return null;
		}
		return maps.get(status.intValue()).getMessage();
	}

	public static TestLineStatus findStatus(Integer status) {
		if (status == null || !maps.containsKey(status.intValue())) {
			return null;
		}
		return maps.get(status.intValue());
	}

	/**
	 *
	 * @param status
	 * @param testLineStatus
	 * @return
	 */
	public static boolean check(Integer status, TestLineStatus... testLineStatus) {
		if (status == null || !maps.containsKey(status.intValue()) || testLineStatus == null || testLineStatus.length <= 0){
			return false;
		}
		for (TestLineStatus tlStatus: testLineStatus){
			if (status.intValue() == tlStatus.getStatus()){
				return true;
			}
		}
		return false;
	}

	/**
	 *
	 * @param testLineStatus
	 * @return
	 */
	public boolean check(TestLineStatus... testLineStatus){
		if (testLineStatus == null || testLineStatus.length <= 0){
			return false;
		}
		for (TestLineStatus status: testLineStatus){
			if (this.getStatus() == status.getStatus()){
				return true;
			}
		}
		return false;
	}
	public static boolean checkCategory(Integer status, String... category){
		if (status == null || !maps.containsKey(status.intValue()) || category == null || category.length <= 0) {
			return false;
		}
		TestLineStatus testLineStatus = maps.get(status.intValue());
		for (String testLineCategory: category){
			if (StringUtils.equalsIgnoreCase(testLineStatus.getCategory() , testLineCategory)){
				return true;
			}
		}
		return false;
	}
}
