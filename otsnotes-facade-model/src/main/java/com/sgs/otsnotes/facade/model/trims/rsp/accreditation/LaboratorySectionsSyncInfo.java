package com.sgs.otsnotes.facade.model.trims.rsp.accreditation;

import com.sgs.framework.core.base.BaseProductLine;
import lombok.Data;

@Data
public class LaboratorySectionsSyncInfo extends BaseProductLine {
    /**
     *
     */
    private Integer laboratorySectionId;

    public Integer getLaboratorySectionId() {
        return laboratorySectionId;
    }

    public void setLaboratorySectionId(Integer laboratorySectionId) {
        this.laboratorySectionId = laboratorySectionId;
    }
}