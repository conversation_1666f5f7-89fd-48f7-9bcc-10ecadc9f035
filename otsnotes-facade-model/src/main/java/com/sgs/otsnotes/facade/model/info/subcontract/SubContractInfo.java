package com.sgs.otsnotes.facade.model.info.subcontract;

import com.sgs.framework.core.base.BaseProductLine;
import com.sgs.otsnotes.facade.model.rsp.LabInfo;

import java.math.BigDecimal;
import java.util.Date;

public class SubContractInfo extends BaseProductLine {
    /**
     * ID VARCHAR(36) 必填<br>
     *
     */
    private String id;

    /**
     * OrderNo VARCHAR(30) 必填<br>
     *
     */
    private String orderNo;
    private String generalOrderId;

    /**
     * SubContractNo VARCHAR(50)<br>
     *
     */
    private String subContractNo;

    /**
     * SubContractLabName VARCHAR(100)<br>
     *
     */
    private String subContractLabName;

    /**
     * SubContractServiceType VARCHAR(20)<br>
     *
     */
    private String subContractServiceType;

    /**
     * SubContractExpectDueDate TIMESTAMP(19)<br>
     *
     */
    private Date subContractExpectDueDate;

    /**
     * AdditionalInfo VARCHAR(200)<br>
     *
     */
    private String additionalInfo;

    /**
     * SubContractRemark VARCHAR(500)<br>
     *
     */
    private String subContractRemark;

    /**
     * SubContractLabCode VARCHAR(30)<br>
     *
     */
    private String subContractLabCode;

    private Integer subContractLabId;
    private String subContractFromLabCode;

    /**
     * SyncStatus INTEGER(10)<br>
     * 同步状态：0-pending,1-trigger,2-get master job,3-get report；只有HK需要同步，其他lab状态为0
     */
    private Integer syncStatus;

    /**
     * StartDate TIMESTAMP(19)<br>
     * 分包开始时间
     */
    private Date startDate;

    /**
     * CompleteDate TIMESTAMP(19)<br>
     * 分包完成时间
     */
    private Date completeDate;

    /**
     * SubContractOrder INTEGER(10) 默认值[0]<br>
     * 分包类型(0：普通【默认值】、1：内部分包)
     */
    private Integer subContractOrder;

    /**
     * DataLock INTEGER(10) 默认值[0]<br>
     * 数据锁定（0：未锁定【默认值】、1：已锁定）
     */
    private Integer dataLock;

    /**
     * ModifiedBy VARCHAR(50)<br>
     *
     */
    private String modifiedBy;


    /**
     * ModifiedDate TIMESTAMP(19)<br>
     *
     */
    private Date modifiedDate;
    private Integer status;
    private String reportRequirement;
    private String reportTemplate;
    private String orderLabCode;
    private Date createdDate;

    /**
     * SubContractContract VARCHAR(50)<br>
     * 分包联系人
     */
    private String subContractContract;

    /**
     * SubContractContractTel VARCHAR(50)<br>
     * 分包联系人电话
     */
    private String subContractContractTel;

    /**
     * SubContractContractEmail VARCHAR(50)<br>
     * 分包联系人邮件
     */
    private String subContractContractEmail;

    private Integer subcontractTat;

    private String subcontractFeeCurrency;
    private BigDecimal subcontractFee;
    private String subReportReviewer;
    private String subReportReviewerId;
    private String subReportReviewerEmail;

    private String csName;
    private String csContact;
    private String csEmail;
    private LabInfo labInfo;
    private Integer operationModel;

    private String extData;

    private String buCode;
    private String locationCode;

    public String getExtData() {
        return extData;
    }

    public void setExtData(String extData) {
        this.extData = extData;
    }

    public Integer getOperationModel() {
        return operationModel;
    }

    public void setOperationModel(Integer operationModel) {
        this.operationModel = operationModel;
    }

    public String getSubReportReviewerEmail() {
        return subReportReviewerEmail;
    }

    public void setSubReportReviewerEmail(String subReportReviewerEmail) {
        this.subReportReviewerEmail = subReportReviewerEmail;
    }

    public String getSubReportReviewerId() {
        return subReportReviewerId;
    }

    public void setSubReportReviewerId(String subReportReviewerId) {
        this.subReportReviewerId = subReportReviewerId;
    }

    public LabInfo getLabInfo() {
        return labInfo;
    }

    public void setLabInfo(LabInfo labInfo) {
        this.labInfo = labInfo;
    }

    public String getCsName() {
        return csName;
    }

    public void setCsName(String csName) {
        this.csName = csName;
    }

    public String getCsContact() {
        return csContact;
    }

    public void setCsContact(String csContact) {
        this.csContact = csContact;
    }

    public String getCsEmail() {
        return csEmail;
    }

    public void setCsEmail(String csEmail) {
        this.csEmail = csEmail;
    }

    public String getSubReportReviewer() {
        return subReportReviewer;
    }

    public void setSubReportReviewer(String subReportReviewer) {
        this.subReportReviewer = subReportReviewer;
    }

    public String getSubcontractFeeCurrency() {
        return subcontractFeeCurrency;
    }

    public void setSubcontractFeeCurrency(String subcontractFeeCurrency) {
        this.subcontractFeeCurrency = subcontractFeeCurrency;
    }

    public BigDecimal getSubcontractFee() {
        return subcontractFee;
    }

    public void setSubcontractFee(BigDecimal subcontractFee) {
        this.subcontractFee = subcontractFee;
    }

    public Integer getSubcontractTat() {
        return subcontractTat;
    }

    public void setSubcontractTat(Integer subcontractTat) {
        this.subcontractTat = subcontractTat;
    }

    public String getOrderLabCode() {
        return orderLabCode;
    }

    public void setOrderLabCode(String orderLabCode) {
        this.orderLabCode = orderLabCode;
    }

    public String getReportRequirement() {
        return reportRequirement;
    }

    public void setReportRequirement(String reportRequirement) {
        this.reportRequirement = reportRequirement;
    }

    public String getReportTemplate() {
        return reportTemplate;
    }

    public void setReportTemplate(String reportTemplate) {
        this.reportTemplate = reportTemplate;
    }

    public String getModifiedBy() {
        return modifiedBy;
    }

    public void setModifiedBy(String modifiedBy) {
        this.modifiedBy = modifiedBy;
    }

    public Date getModifiedDate() {
        return modifiedDate;
    }

    public void setModifiedDate(Date modifiedDate) {
        this.modifiedDate = modifiedDate;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getSubContractNo() {
        return subContractNo;
    }

    public void setSubContractNo(String subContractNo) {
        this.subContractNo = subContractNo;
    }

    public String getSubContractLabName() {
        return subContractLabName;
    }

    public void setSubContractLabName(String subContractLabName) {
        this.subContractLabName = subContractLabName;
    }

    public String getSubContractServiceType() {
        return subContractServiceType;
    }

    public void setSubContractServiceType(String subContractServiceType) {
        this.subContractServiceType = subContractServiceType;
    }

    public Date getSubContractExpectDueDate() {
        return subContractExpectDueDate;
    }

    public void setSubContractExpectDueDate(Date subContractExpectDueDate) {
        this.subContractExpectDueDate = subContractExpectDueDate;
    }

    public String getAdditionalInfo() {
        return additionalInfo;
    }

    public void setAdditionalInfo(String additionalInfo) {
        this.additionalInfo = additionalInfo;
    }

    public String getSubContractRemark() {
        return subContractRemark;
    }

    public void setSubContractRemark(String subContractRemark) {
        this.subContractRemark = subContractRemark;
    }

    public String getSubContractLabCode() {
        return subContractLabCode;
    }

    public void setSubContractLabCode(String subContractLabCode) {
        this.subContractLabCode = subContractLabCode;
    }


    public Integer getSubContractLabId() {
        return subContractLabId;
    }

    public void setSubContractLabId(Integer subContractLabId) {
        this.subContractLabId = subContractLabId;
    }

    public Integer getSyncStatus() {
        return syncStatus;
    }

    public void setSyncStatus(Integer syncStatus) {
        this.syncStatus = syncStatus;
    }

    public Date getStartDate() {
        return startDate;
    }

    public void setStartDate(Date startDate) {
        this.startDate = startDate;
    }

    public Date getCompleteDate() {
        return completeDate;
    }

    public void setCompleteDate(Date completeDate) {
        this.completeDate = completeDate;
    }

    public Integer getSubContractOrder() {
        return subContractOrder;
    }

    public void setSubContractOrder(Integer subContractOrder) {
        this.subContractOrder = subContractOrder;
    }

    public Integer getDataLock() {
        return dataLock;
    }

    public void setDataLock(Integer dataLock) {
        this.dataLock = dataLock;
    }

    public String getSubContractContract() {
        return subContractContract;
    }

    public void setSubContractContract(String subContractContract) {
        this.subContractContract = subContractContract;
    }

    public String getSubContractContractTel() {
        return subContractContractTel;
    }

    public void setSubContractContractTel(String subContractContractTel) {
        this.subContractContractTel = subContractContractTel;
    }

    public String getSubContractContractEmail() {
        return subContractContractEmail;
    }

    public void setSubContractContractEmail(String subContractContractEmail) {
        this.subContractContractEmail = subContractContractEmail;
    }

    public String getSubContractFromLabCode() {
        return subContractFromLabCode;
    }

    public void setSubContractFromLabCode(String subContractFromLabCode) {
        this.subContractFromLabCode = subContractFromLabCode;
    }

    public Date getCreatedDate() {
        return createdDate;
    }

    public void setCreatedDate(Date createdDate) {
        this.createdDate = createdDate;
    }

    public String getGeneralOrderId() {
        return generalOrderId;
    }

    public void setGeneralOrderId(String generalOrderId) {
        this.generalOrderId = generalOrderId;
    }

    public String getBuCode() {
        return buCode;
    }

    public void setBuCode(String buCode) {
        this.buCode = buCode;
    }

    public String getLocationCode() {
        return locationCode;
    }

    public void setLocationCode(String locationCode) {
        this.locationCode = locationCode;
    }
}
