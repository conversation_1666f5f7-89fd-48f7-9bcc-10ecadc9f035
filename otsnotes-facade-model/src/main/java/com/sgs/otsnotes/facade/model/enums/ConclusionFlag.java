package com.sgs.otsnotes.facade.model.enums;

import java.util.HashMap;
import java.util.Map;

public enum ConclusionFlag {
    Calculate(0, "Calculate"),
    Ignore(1, "Ignore"),
    DataOnly(2, "Data Only");

    private int flag;
    private String message;

    ConclusionFlag(int flag, String message) {
        this.flag = flag;
        this.message = message;
    }

    public int getFlag() {
        return flag;
    }

    public String getMessage() {
        return message;
    }

    static Map<Integer, ConclusionFlag> maps = new HashMap<>();

    static {
        for (ConclusionFlag flag : ConclusionFlag.values()) {
            maps.put(flag.getFlag(), flag);
        }
    }

    public static ConclusionFlag find(Integer flag) {
        if (flag == null || !maps.containsKey(flag.intValue())){
            return ConclusionFlag.Calculate;
        }
        return maps.get(flag);
    }

    public static boolean check(Integer flag, ConclusionFlag conclusionFlag) {
        if (flag == null || !maps.containsKey(flag.intValue())){
            return false;
        }
        return maps.get(flag.intValue()) == conclusionFlag;
    }
}
