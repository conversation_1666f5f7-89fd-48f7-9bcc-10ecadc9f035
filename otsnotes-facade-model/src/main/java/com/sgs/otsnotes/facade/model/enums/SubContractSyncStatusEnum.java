package com.sgs.otsnotes.facade.model.enums;

import java.util.HashMap;
import java.util.Map;

/**
 * @Author: Jinx
 * @Date: 2019-06-13 14:05
 * @Description:
 **/
public enum SubContractSyncStatusEnum {

    pending(0),
    trigger(1),
    getJob(2),
    getReport(3)
    ;

    private int value = 1;

    public int getValue() {
        return value;
    }

    public void setValue(int value) {
        this.value = value;
    }

    SubContractSyncStatusEnum(int value){
        this.value = value;
    }

    public static final Map<Integer, SubContractSyncStatusEnum> maps = new HashMap<Integer, SubContractSyncStatusEnum>() {
        private static final long serialVersionUID = -8986866330615001847L;
        {
            for (SubContractSyncStatusEnum type : SubContractSyncStatusEnum.values()) {
                put(type.getValue(), type);
            }
        }
    };

    public static SubContractSyncStatusEnum findType(Integer type) {
        if (type == null || !maps.containsKey(type.intValue())) {
            return null;
        }
        return maps.get(type.intValue());
    }

    /**
     *
     * @param type
     * @param syncStatus
     * @return
     */
    public static boolean check(Integer type, SubContractSyncStatusEnum syncStatus) {
        if (syncStatus == null || !maps.containsKey(type.intValue())){
            return false;
        }
        return maps.get(type.intValue()) == syncStatus;
    }
}
