package com.sgs.otsnotes.facade.model.enums.starlims;

import org.springframework.util.StringUtils;

import java.util.HashMap;
import java.util.Map;

public enum StarLimsTrackTypeEnums {
    CommitFolder("CommitFolder"),
    FolderCreation("FolderCreation"),
    DueDateUpdate("DueDateUpdate"),
    FolderReported("FolderReported");

    private String trackType;
    StarLimsTrackTypeEnums(String trackType){
        this.trackType = trackType;
    }

    public String getTrackType() {
        return trackType;
    }

    /**
     * 区分大小写 ，严格比较
     * @param trackType
     * @return
     */
    public static boolean check(String trackType , StarLimsTrackTypeEnums ... enums){
        if(StringUtils.isEmpty(trackType)){
            return false;
        }
        for (StarLimsTrackTypeEnums value : enums) {
            if(value.trackType.equals(trackType)){
                return true;
            }
        }
        return false;
    }

    public static final Map<String, StarLimsTrackTypeEnums> maps = new HashMap<String, StarLimsTrackTypeEnums>() {
        {
            for (StarLimsTrackTypeEnums starLimsTrack : StarLimsTrackTypeEnums.values()) {
                put(starLimsTrack.trackType, starLimsTrack);
            }
        }
    };
    public static StarLimsTrackTypeEnums findTrackType(String  trackType) {
        if (trackType == null || !maps.containsKey(trackType)) {
            return null;
        }
        return maps.get(trackType);
    }

}
