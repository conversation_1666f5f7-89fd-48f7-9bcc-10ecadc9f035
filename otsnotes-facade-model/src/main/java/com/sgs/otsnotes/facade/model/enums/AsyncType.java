package com.sgs.otsnotes.facade.model.enums;

import java.util.HashMap;
import java.util.Map;

public enum AsyncType {
    Order(0, "Order"),
    Report(1, "Report"),
    TestLine(2, "TestLine"),
    CopyReport(3, "CopyReport"),
    PpMatrixSampleRel(4, "PpMatrixSampleRel"),
    ReportLang(5, "ReportLang"),
    PPTestLineRel(6, "PPTestLineRel"),
    TestCondition(7, "TestCondition"),
    TestConditionGroup(8, "TestConditionGroup"),
    TestConditionLang(9, "TestConditionLang"),
    TestConditionGroupLang(10, "TestConditionGroupLang"),
    TestLineCustomerApp(11, "TestLineCustomerApp"),
    Citation(12, "Citation"),
    LabSection(13, "LabSection"),
    TestLineMatrix(14, "TestLineMatrix"),
    PpArtifactRel(15, "PpArtifactRel"),
    SubcontractRel(16, "SubcontractRel"),
    PpCondition(17, "PpCondition"),
    ReferDataRel(18, "ReferDataRel"),
    TestLineBaseInfo(19, "TestLineBaseInfo"),
    CitationName(20, "CitationName"),
    WiForSample(50, "WiForSample"),
    Analyte(51, "Analyte"),
    WiForCS(52, "WiForCS"),
    TLName(53, "TLName"),
    PP(55, "PP")
    ;

//    LabSection(1, "LabSection"),
//    WiForSample(2, "WiForSample"),
//    Analyte(3, "Analyte"),
//    WiForCS(4, "WiForCS"),
//    Citation(5, "Citation"),
//    Condition(6, "Condition"),
//    TLName(20, "TLName")
//            ;
    private Integer type;
    private String code;

    AsyncType(Integer type, String code) {
        this.type = type;
        this.code = code;
    }

    public String getCode() {
        return code;
    }

    public Integer getType() {
        return type;
    }

    static Map<Integer, AsyncType> typeMaps = new HashMap();
    static Map<String, AsyncType> codeMaps = new HashMap<>();
    static {
        for (AsyncType enu : AsyncType.values()) {
            typeMaps.put(enu.getType(), enu);
            codeMaps.put(enu.getCode(), enu);
        }
    }

    public static AsyncType findType(Integer type) {
        if (type == null || !typeMaps.containsKey(type.intValue())) {
            return null;
        }
        return typeMaps.get(type.intValue());
    }

    /**
     *
     * @param type
     * @param asyncType
     * @return
     */
    public static boolean check(Integer type, AsyncType asyncType) {
        if (type == null || !typeMaps.containsKey(type.intValue())){
            return false;
        }
        return typeMaps.get(type.intValue()) == asyncType;
    }

    public static boolean check(Integer asyncType) {
        if (asyncType == null){
            return false;
        }
        return typeMaps.containsKey(asyncType);
    }

}
