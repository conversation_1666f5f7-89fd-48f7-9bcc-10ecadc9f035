package com.sgs.otsnotes.facade.model.enums;

import java.util.HashMap;
import java.util.Map;

public enum WorkSheetTypeEnums {
    None(0, "None"),
    TestReport(1, "generateTestReport"),
    MergeReportSection(2, "getMergedReportbySection"),
    ProtocolReport(3, "getProtocolReport");

    private int status;
    private String code;

    WorkSheetTypeEnums(int status, String code) {
        this.status = status;
        this.code = code;
    }

    public int getStatus() {
        return status;
    }

    public String getCode() {
        return this.code;
    }

    static Map<Integer, WorkSheetTypeEnums> maps = new HashMap<>();

    static {
        for (WorkSheetTypeEnums type : WorkSheetTypeEnums.values()) {
            maps.put(type.getStatus(), type);
        }
    }

    public static WorkSheetTypeEnums findCode(Integer code) {
        if (code == null || !maps.containsKey(code.intValue())){
            return WorkSheetTypeEnums.None;
        }
        return maps.get(code);
    }

    public static boolean check(Integer status) {
        if (status == null){
            return false;
        }
        return maps.containsKey(status.intValue());
    }

    /**
     *
     * @param status
     * @param type
     * @return
     */
    public static boolean check(Integer status, WorkSheetTypeEnums type) {
        if (status == null || !maps.containsKey(status.intValue())){
            return false;
        }
        return maps.get(status.intValue()) == type;
    }
}
