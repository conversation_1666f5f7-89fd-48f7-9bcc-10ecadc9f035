package com.sgs.otsnotes.facade.model.trims.req;

import com.sgs.otsnotes.facade.model.trims.TrimsSyncBaseReq;

import java.util.List;

public class TestLineSyncReq extends TrimsSyncBaseReq {
    /**
     *
     */
    private List<Integer> ids;
    /**
     *
     */
    private String lastModifyFrom;

    public List<Integer> getIds() {
        return ids;
    }

    public void setIds(List<Integer> ids) {
        this.ids = ids;
    }

    public String getLastModifyFrom() {
        return lastModifyFrom;
    }

    public void setLastModifyFrom(String lastModifyFrom) {
        this.lastModifyFrom = lastModifyFrom;
    }
}
