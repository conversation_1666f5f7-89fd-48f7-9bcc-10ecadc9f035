package com.sgs.otsnotes.facade.model.info.subcontract;

import com.sgs.framework.core.base.BaseProductLine;
import com.sgs.otsnotes.facade.model.annotation.ObjectSetting;
import com.sgs.otsnotes.facade.model.common.StringUtil;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

public class SubcontractRequirementInfo extends BaseProductLine {
    /**
     * ID VARCHAR(36) 必填<br>
     * ID
     */
    private String ID;

    /**
     * GeneralOrderInstanceID VARCHAR(36) 必填<br>
     * GeneralOrderInstanceID
     */
    private String generalOrderInstanceID;

    /**
     * SubContractNo VARCHAR(50)<br>
     * SubContractNo
     */
    private String subContractNo;

    /**
     * SubContractID VARCHAR(36) 必填<br>
     * SubContractID
     */
    private String subContractID;

    /**
     * EvaluationBasis VARCHAR(250)<br>
     * Judgement Principle
     */
    private String evaluationBasis;

    /**
     * OtherRequirements VARCHAR(500)<br>
     * Other Requirements
     */
    private String otherRequirements;

    /**
     * Qualification VARCHAR(250)<br>
     * Qualification flags CMA/CMAF flag:  1-Non; 2-CNAS;3-CMA Or CMAF; 4-CANS,CMA Or CMAF,   relate to the TB_SystemConstants  SysCode
     */
    private String qualification;

    /**
     * ReoprtDate TIMESTAMP(19)<br>
     * the expect Report receive Date
     */
    private Date reoprtDate;

    /**
     * ReportLanguage VARCHAR(50)<br>
     * Report Language:1-English report (be sure to complete information in English);2-Chinese report ( charge 200 yuan), these two types can be selected both.,   relate to the TB_SystemConstants  SysCode
     */
    private String reportLanguage;

    /**
     * ReportManner INTEGER(10)<br>
     * Ways of reporting:1-A Report Containing One Sample; 2-A Report Containing Multiple Samples; 3-Test Report Template(Need Product Standards ),   relate to the TB_SystemConstants  SysCode
     */
    private Integer reportManner;

    /**
     * ReportType VARCHAR(50)<br>
     * Report Type: 1-Electronic Report; 2-Paper Reports, these two types can be selected both.,   relate to the TB_SystemConstants  SysCode
     */
    private String reportType;

    /**
     * ReportRequirement VARCHAR(50)<br>
     * Report Requirement：1-Customer Report (PDF)；2-Sub Report(Word)；3-PDF&Word
     */
    @ObjectSetting(code = "reportRequirement")
    private String reportRequirement;

    /**
     * ReportTemplate VARCHAR(50)<br>
     * Report Template
     */
    private String reportTemplate;

    /**
     * ReportQty INTEGER(10)<br>
     * Report Qty
     */
    private Integer reportQty;

    /**
     * ResultJudgingFlag BIT<br>
     * Result Judging0-no; 1-yes
     */
    private Integer resultJudgingFlag;

    /**
     * ServiceType INTEGER(10)<br>
     * Service Type:1-Conventional tests (default service);2-Urgent Service,   relate to the TB_SystemConstants  SysCode
     */
    private Integer serviceType;

    /**
     * DisplaySupplierFlag BIT<br>
     * Display supplier or not:0-no, 1-yes
     */
    private Integer displaySupplierFlag;

    /**
     * CommentFlag BIT<br>
     */
    @ObjectSetting(code = "commentFlag")
    private Integer commentFlag;

    /**
     * HardCopyFlag BIT<br>
     */
    @ObjectSetting(code = "hardCopyFlag")
    private Integer hardCopyFlag;

    /**
     * ChineseReportFlag BIT<br>
     */
    private Integer chineseReportFlag;

    /**
     * TakePhotoFlag BIT<br>
     */
    @ObjectSetting(code = "takePhotoFlag")
    private Integer takePhotoFlag;

    /**
     * ConfirmCoverPageFlag BIT<br>
     */
    @ObjectSetting(code = "confirmCoverPageFlag")
    private Integer confirmCoverPageFlag;

    /**
     * PackageIndicator VARCHAR(10)<br>
     * full package / partial package / incomplete (single choice),
     * F- full package
     * P- partial package
     * I-  incomplete
     * This code & display value  should be configed in TB_SystemConstans.
     */
    private String packageIndicator;

    /**
     * TakePhotoRemark VARCHAR(500)<br>
     */
    @ObjectSetting(code = "takePhotoRemark")
    private String takePhotoRemark;

    /**
     * ReturnResidueSampleFlag BIT<br>
     * need Return Residue Sample ,  0-no; 1-yes
     */
    private Integer returnResidueSampleFlag;

    /**
     * ReturnTestedSampleFlag BIT<br>
     * need Return Tested Sample ,  0-no; 1-yes
     */
    private Integer returnTestedSampleFlag;

    /**
     * ReturnResidueSampleRemark VARCHAR(500)<br>
     * remark of Return Residue Sample
     */
    private String returnResidueSampleRemark;

    /**
     * ReturnTestedSampleRemark VARCHAR(500)<br>
     * remark of Return Tested Sample
     */
    private String returnTestedSampleRemark;

    /**
     * ReportAccreditationNeededFlag BIT<br>
     * need Accreditation info on report  ,  0-no; 1-yes
     */
    private Integer reportAccreditationNeededFlag;

    /**
     * HardCopyToApplicantFlag BIT<br>
     * the flag to indicate the report hard copy need send to order applicant, 0 - N, 1 - Yes
     */
    private Integer hardCopyToApplicantFlag;

    /**
     * HardCopyToPayertFlag BIT<br>
     * the flag to indicate the report hard copy need send to order payer, 0 - N, 1 - Yes
     */
    private Integer hardCopyToPayertFlag;

    /**
     * HardCopyToOther VARCHAR(500)<br>
     * other address which will send report hard copy
     */
    private String hardCopyToOther;

    /**
     * SoftCopyToApplicantFlag BIT<br>
     * the flag to indicate the report soft  copy need send to order applicnt, 0 - N, 1 - Yes
     */
    private Integer softCopyToApplicantFlag;

    /**
     * SoftCopyToPayerFlag BIT<br>
     * the flag to indicate the report soft  copy need send to order payer, 0 - N, 1 - Yes
     */
    private Integer softCopyToPayerFlag;

    /**
     * SoftCopyToOther VARCHAR(500)<br>
     * other address which will send report soft copy
     */
    private String softCopyToOther;

    /**
     * HtmlString VARCHAR(10000)<br>
     */
    private String htmlString;

    /**
     * PdfReportSecurity BIT 默认值[0]<br>
     */
    private Integer pdfReportSecurity;

    /**
     * ActiveIndicator BIT 默认值[1] 必填<br>
     * 0: inactive, 1: active
     */
    private Integer activeIndicator;

    /**
     * CreatedBy VARCHAR(50)<br>
     * Founder of the UserName
     */
    private String createdBy;

    /**
     * CreatedDate TIMESTAMP(19)<br>
     * Creation time
     */
    private Date createdDate;

    /**
     * ModifiedBy VARCHAR(50)<br>
     * Edit the UserName (like CreateBy for the first time)
     */
    private String modifiedBy;

    /**
     * ModifiedDate TIMESTAMP(19)<br>
     * Edit the Date (like CreateDate for the first time)
     */
    private Date modifiedDate;

    /**
     * PaymentRemark VARCHAR(500)<br>
     */
    @ObjectSetting(code = "paymentRemark")
    private String paymentRemark;

    /**
     * QualificationType VARCHAR(50)<br>
     */
    @ObjectSetting(code = "qualificationType")
    private String qualificationType;

    @ObjectSetting(code = "convertInHouseMethodFlag")
    private Integer convertInHouseMethodFlag;

    /**
     * DraftReportRequired TINYINT(3)<br>
     */
    @ObjectSetting(code = "isDraftReportRequired")
    private Integer draftReportRequired;

    /**
     * ProformaInvoice TINYINT(3)<br>
     */
    @ObjectSetting(code = "isProformaInvoice")
    private Integer proformaInvoice;

    /**
     * LiquidTestSample TINYINT(3)<br>
     */
    @ObjectSetting(code = "liquidTestSample")
    private Integer liquidTestSample;

    /**
     * VatType TINYINT(3)<br>
     */
    @ObjectSetting(code = "vatType")
    private Integer vatType;

    /**
     * PhotoRequest VARCHAR(250)<br>
     * Photo Request
     */
    @ObjectSetting(code = "photoRequest")
    private String photoRequest;

    /**
     * NeedConclusion BIT<br>
     * Need Conclusion， 0-no; 1-yes
     */
    @ObjectSetting(code = "needConclusion")
    private Integer needConclusion;

    /**
     * HardCopyReportDeliverWay VARCHAR(500)<br>
     * HardCopy Report Deliver Way
     */
    @ObjectSetting(code = "hardCopyReportDeliverWay")
    private String hardCopyReportDeliverWay;

    /**
     * InvoiceDeliverWay VARCHAR(500)<br>
     * Invoice Deliver Way
     */
    @ObjectSetting(code = "invoiceDeliverWay")
    private String invoiceDeliverWay;

    /**
     * SubcontractFee INTEGER(10)<br>
     * Subcontract Fee
     */
    private BigDecimal subcontractFee;

    /**
     * SubcontractFeeCurrency VARCHAR(50)<br>
     * Subcontract Fee Currency
     */
    private String subcontractFeeCurrency;

    /**
     * QrcodeFlag VARCHAR(500)<br>
     * QrcodeFlag
     */
    @ObjectSetting(code = "qrCodeFlag")
    private String qrcodeFlag;

    private List<SubcontractRequirementContactsInfo> subcontractRequirementContactsInfos;

    public List<SubcontractRequirementContactsInfo> getSubcontractRequirementContactsInfos() {
        return subcontractRequirementContactsInfos;
    }

    public void setSubcontractRequirementContactsInfos(List<SubcontractRequirementContactsInfo> subcontractRequirementContactsInfos) {
        this.subcontractRequirementContactsInfos = subcontractRequirementContactsInfos;
    }

    /**
     * ID VARCHAR(36) 必填<br>
     * 获得 ID
     */
    public String getID() {
        return ID;
    }

    /**
     * ID VARCHAR(36) 必填<br>
     * 设置 ID
     */
    public void setID(String ID) {
        this.ID = ID == null ? null : ID.trim();
    }

    /**
     * GeneralOrderInstanceID VARCHAR(36) 必填<br>
     * 获得 GeneralOrderInstanceID
     */
    public String getGeneralOrderInstanceID() {
        return generalOrderInstanceID;
    }

    /**
     * GeneralOrderInstanceID VARCHAR(36) 必填<br>
     * 设置 GeneralOrderInstanceID
     */
    public void setGeneralOrderInstanceID(String generalOrderInstanceID) {
        this.generalOrderInstanceID = generalOrderInstanceID == null ? null : generalOrderInstanceID.trim();
    }

    /**
     * SubContractID VARCHAR(36) 必填<br>
     * 获得 SubContractID
     */
    public String getSubContractID() {
        return subContractID;
    }

    /**
     * SubContractID VARCHAR(36) 必填<br>
     * 设置 SubContractID
     */
    public void setSubContractID(String subContractID) {
        this.subContractID = subContractID == null ? null : subContractID.trim();
    }

    public String getSubContractNo() {
        return subContractNo;
    }

    public void setSubContractNo(String subContractNo) {
        this.subContractNo = subContractNo;
    }

    /**
     * EvaluationBasis VARCHAR(250)<br>
     * 获得 Judgement Principle
     */
    public String getEvaluationBasis() {
        return evaluationBasis;
    }

    /**
     * EvaluationBasis VARCHAR(250)<br>
     * 设置 Judgement Principle
     */
    public void setEvaluationBasis(String evaluationBasis) {
        this.evaluationBasis = evaluationBasis == null ? null : evaluationBasis.trim();
    }

    /**
     * OtherRequirements VARCHAR(500)<br>
     * 获得 Other Requirements
     */
    public String getOtherRequirements() {
        return otherRequirements;
    }

    /**
     * OtherRequirements VARCHAR(500)<br>
     * 设置 Other Requirements
     */
    public void setOtherRequirements(String otherRequirements) {
        this.otherRequirements = otherRequirements == null ? null : otherRequirements.trim();
    }

    /**
     * Qualification VARCHAR(250)<br>
     * 获得 Qualification flags CMA/CMAF flag:  1-Non; 2-CNAS;3-CMA Or CMAF; 4-CANS,CMA Or CMAF,   relate to the TB_SystemConstants  SysCode
     */
    public String getQualification() {
        return qualification;
    }

    /**
     * Qualification VARCHAR(250)<br>
     * 设置 Qualification flags CMA/CMAF flag:  1-Non; 2-CNAS;3-CMA Or CMAF; 4-CANS,CMA Or CMAF,   relate to the TB_SystemConstants  SysCode
     */
    public void setQualification(String qualification) {
        this.qualification = qualification == null ? null : qualification.trim();
    }

    /**
     * ReoprtDate TIMESTAMP(19)<br>
     * 获得 the expect Report receive Date
     */
    public Date getReoprtDate() {
        return reoprtDate;
    }

    /**
     * ReoprtDate TIMESTAMP(19)<br>
     * 设置 the expect Report receive Date
     */
    public void setReoprtDate(Date reoprtDate) {
        this.reoprtDate = reoprtDate;
    }

    /**
     * ReportLanguage VARCHAR(50)<br>
     * 获得 Report Language:1-English report (be sure to complete information in English);2-Chinese report ( charge 200 yuan), these two types can be selected both.,   relate to the TB_SystemConstants  SysCode
     */
    public String getReportLanguage() {
        return reportLanguage;
    }

    /**
     * ReportLanguage VARCHAR(50)<br>
     * 设置 Report Language:1-English report (be sure to complete information in English);2-Chinese report ( charge 200 yuan), these two types can be selected both.,   relate to the TB_SystemConstants  SysCode
     */
    public void setReportLanguage(String reportLanguage) {
        this.reportLanguage = reportLanguage == null ? null : reportLanguage.trim();
    }

    /**
     * ReportManner INTEGER(10)<br>
     * 获得 Ways of reporting:1-A Report Containing One Sample; 2-A Report Containing Multiple Samples; 3-Test Report Template(Need Product Standards ),   relate to the TB_SystemConstants  SysCode
     */
    public Integer getReportManner() {
        return reportManner;
    }

    /**
     * ReportManner INTEGER(10)<br>
     * 设置 Ways of reporting:1-A Report Containing One Sample; 2-A Report Containing Multiple Samples; 3-Test Report Template(Need Product Standards ),   relate to the TB_SystemConstants  SysCode
     */
    public void setReportManner(Integer reportManner) {
        this.reportManner = reportManner;
    }

    /**
     * ReportType VARCHAR(50)<br>
     * 获得 Report Type: 1-Electronic Report; 2-Paper Reports, these two types can be selected both.,   relate to the TB_SystemConstants  SysCode
     */
    public String getReportType() {
        return reportType;
    }

    /**
     * ReportType VARCHAR(50)<br>
     * 设置 Report Type: 1-Electronic Report; 2-Paper Reports, these two types can be selected both.,   relate to the TB_SystemConstants  SysCode
     */
    public void setReportType(String reportType) {
        this.reportType = reportType == null ? null : reportType.trim();
    }

    /**
     * ReportRequirement VARCHAR(50)<br>
     * 获得 Report Requirement：1-Customer Report (PDF)；2-Sub Report(Word)；3-PDF&Word
     */
    public String getReportRequirement() {
        return reportRequirement;
    }

    /**
     * ReportRequirement VARCHAR(50)<br>
     * 设置 Report Requirement：1-Customer Report (PDF)；2-Sub Report(Word)；3-PDF&Word
     */
    public void setReportRequirement(String reportRequirement) {
        this.reportRequirement = reportRequirement == null ? null : reportRequirement.trim();
    }

    /**
     * ReportTemplate VARCHAR(50)<br>
     * 获得 Report Template
     */
    public String getReportTemplate() {
        return reportTemplate;
    }

    /**
     * ReportTemplate VARCHAR(50)<br>
     * 设置 Report Template
     */
    public void setReportTemplate(String reportTemplate) {
        this.reportTemplate = reportTemplate == null ? null : reportTemplate.trim();
    }

    /**
     * ReportQty INTEGER(10)<br>
     * 获得 Report Qty
     */
    public Integer getReportQty() {
        return reportQty;
    }

    /**
     * ReportQty INTEGER(10)<br>
     * 设置 Report Qty
     */
    public void setReportQty(Integer reportQty) {
        this.reportQty = reportQty;
    }

    /**
     * ResultJudgingFlag BIT<br>
     * 获得 Result Judging0-no; 1-yes
     */
    public Integer getResultJudgingFlag() {
        return resultJudgingFlag;
    }

    /**
     * ResultJudgingFlag BIT<br>
     * 设置 Result Judging0-no; 1-yes
     */
    public void setResultJudgingFlag(Integer resultJudgingFlag) {
        this.resultJudgingFlag = resultJudgingFlag;
    }

    /**
     * ServiceType INTEGER(10)<br>
     * 获得 Service Type:1-Conventional tests (default service);2-Urgent Service,   relate to the TB_SystemConstants  SysCode
     */
    public Integer getServiceType() {
        return serviceType;
    }

    /**
     * ServiceType INTEGER(10)<br>
     * 设置 Service Type:1-Conventional tests (default service);2-Urgent Service,   relate to the TB_SystemConstants  SysCode
     */
    public void setServiceType(Integer serviceType) {
        this.serviceType = serviceType;
    }

    /**
     * DisplaySupplierFlag BIT<br>
     * 获得 Display supplier or not:0-no, 1-yes
     */
    public Integer getDisplaySupplierFlag() {
        return displaySupplierFlag;
    }

    /**
     * DisplaySupplierFlag BIT<br>
     * 设置 Display supplier or not:0-no, 1-yes
     */
    public void setDisplaySupplierFlag(Integer displaySupplierFlag) {
        this.displaySupplierFlag = displaySupplierFlag;
    }

    /**
     * CommentFlag BIT<br>
     * 获得
     */
    public Integer getCommentFlag() {
        return commentFlag;
    }

    /**
     * CommentFlag BIT<br>
     * 设置
     */
    public void setCommentFlag(Integer commentFlag) {
        this.commentFlag = commentFlag;
    }

    /**
     * HardCopyFlag BIT<br>
     * 获得
     */
    public Integer getHardCopyFlag() {
        return hardCopyFlag;
    }

    /**
     * HardCopyFlag BIT<br>
     * 设置
     */
    public void setHardCopyFlag(Integer hardCopyFlag) {
        this.hardCopyFlag = hardCopyFlag;
    }

    /**
     * ChineseReportFlag BIT<br>
     * 获得
     */
    public Integer getChineseReportFlag() {
        return chineseReportFlag;
    }

    /**
     * ChineseReportFlag BIT<br>
     * 设置
     */
    public void setChineseReportFlag(Integer chineseReportFlag) {
        this.chineseReportFlag = chineseReportFlag;
    }

    /**
     * TakePhotoFlag BIT<br>
     * 获得
     */
    public Integer getTakePhotoFlag() {
        return takePhotoFlag;
    }

    /**
     * TakePhotoFlag BIT<br>
     * 设置
     */
    public void setTakePhotoFlag(Integer takePhotoFlag) {
        this.takePhotoFlag = takePhotoFlag;
    }

    /**
     * ConfirmCoverPageFlag BIT<br>
     * 获得
     */
    public Integer getConfirmCoverPageFlag() {
        return confirmCoverPageFlag;
    }

    /**
     * ConfirmCoverPageFlag BIT<br>
     * 设置
     */
    public void setConfirmCoverPageFlag(Integer confirmCoverPageFlag) {
        this.confirmCoverPageFlag = confirmCoverPageFlag;
    }

    /**
     * PackageIndicator VARCHAR(10)<br>
     * 获得 full package / partial package / incomplete (single choice),
     * F- full package
     * P- partial package
     * I-  incomplete
     * This code & display value  should be configed in TB_SystemConstans.
     */
    public String getPackageIndicator() {
        return packageIndicator;
    }

    /**
     * PackageIndicator VARCHAR(10)<br>
     * 设置 full package / partial package / incomplete (single choice),
     * F- full package
     * P- partial package
     * I-  incomplete
     * This code & display value  should be configed in TB_SystemConstans.
     */
    public void setPackageIndicator(String packageIndicator) {
        this.packageIndicator = packageIndicator == null ? null : packageIndicator.trim();
    }

    /**
     * TakePhotoRemark VARCHAR(500)<br>
     * 获得
     */
    public String getTakePhotoRemark() {
        return takePhotoRemark;
    }

    /**
     * TakePhotoRemark VARCHAR(500)<br>
     * 设置
     */
    public void setTakePhotoRemark(String takePhotoRemark) {
        this.takePhotoRemark = takePhotoRemark == null ? null : takePhotoRemark.trim();
    }

    /**
     * ReturnResidueSampleFlag BIT<br>
     * 获得 need Return Residue Sample ,  0-no; 1-yes
     */
    public Integer getReturnResidueSampleFlag() {
        return returnResidueSampleFlag;
    }

    /**
     * ReturnResidueSampleFlag BIT<br>
     * 设置 need Return Residue Sample ,  0-no; 1-yes
     */
    public void setReturnResidueSampleFlag(Integer returnResidueSampleFlag) {
        this.returnResidueSampleFlag = returnResidueSampleFlag;
    }

    /**
     * ReturnTestedSampleFlag BIT<br>
     * 获得 need Return Tested Sample ,  0-no; 1-yes
     */
    public Integer getReturnTestedSampleFlag() {
        return returnTestedSampleFlag;
    }

    /**
     * ReturnTestedSampleFlag BIT<br>
     * 设置 need Return Tested Sample ,  0-no; 1-yes
     */
    public void setReturnTestedSampleFlag(Integer returnTestedSampleFlag) {
        this.returnTestedSampleFlag = returnTestedSampleFlag;
    }

    /**
     * ReturnResidueSampleRemark VARCHAR(500)<br>
     * 获得 remark of Return Residue Sample
     */
    public String getReturnResidueSampleRemark() {
        return returnResidueSampleRemark;
    }

    /**
     * ReturnResidueSampleRemark VARCHAR(500)<br>
     * 设置 remark of Return Residue Sample
     */
    public void setReturnResidueSampleRemark(String returnResidueSampleRemark) {
        this.returnResidueSampleRemark = returnResidueSampleRemark == null ? null : returnResidueSampleRemark.trim();
    }

    /**
     * ReturnTestedSampleRemark VARCHAR(500)<br>
     * 获得 remark of Return Tested Sample
     */
    public String getReturnTestedSampleRemark() {
        return returnTestedSampleRemark;
    }

    /**
     * ReturnTestedSampleRemark VARCHAR(500)<br>
     * 设置 remark of Return Tested Sample
     */
    public void setReturnTestedSampleRemark(String returnTestedSampleRemark) {
        this.returnTestedSampleRemark = returnTestedSampleRemark == null ? null : returnTestedSampleRemark.trim();
    }

    /**
     * ReportAccreditationNeededFlag BIT<br>
     * 获得 need Accreditation info on report  ,  0-no; 1-yes
     */
    public Integer getReportAccreditationNeededFlag() {
        return reportAccreditationNeededFlag;
    }

    /**
     * ReportAccreditationNeededFlag BIT<br>
     * 设置 need Accreditation info on report  ,  0-no; 1-yes
     */
    public void setReportAccreditationNeededFlag(Integer reportAccreditationNeededFlag) {
        this.reportAccreditationNeededFlag = reportAccreditationNeededFlag;
    }

    /**
     * HardCopyToApplicantFlag BIT<br>
     * 获得 the flag to indicate the report hard copy need send to order applicant, 0 - N, 1 - Yes
     */
    public Integer getHardCopyToApplicantFlag() {
        return hardCopyToApplicantFlag;
    }

    /**
     * HardCopyToApplicantFlag BIT<br>
     * 设置 the flag to indicate the report hard copy need send to order applicant, 0 - N, 1 - Yes
     */
    public void setHardCopyToApplicantFlag(Integer hardCopyToApplicantFlag) {
        this.hardCopyToApplicantFlag = hardCopyToApplicantFlag;
    }

    /**
     * HardCopyToPayertFlag BIT<br>
     * 获得 the flag to indicate the report hard copy need send to order payer, 0 - N, 1 - Yes
     */
    public Integer getHardCopyToPayertFlag() {
        return hardCopyToPayertFlag;
    }

    /**
     * HardCopyToPayertFlag BIT<br>
     * 设置 the flag to indicate the report hard copy need send to order payer, 0 - N, 1 - Yes
     */
    public void setHardCopyToPayertFlag(Integer hardCopyToPayertFlag) {
        this.hardCopyToPayertFlag = hardCopyToPayertFlag;
    }

    /**
     * HardCopyToOther VARCHAR(500)<br>
     * 获得 other address which will send report hard copy
     */
    public String getHardCopyToOther() {
        return hardCopyToOther;
    }

    /**
     * HardCopyToOther VARCHAR(500)<br>
     * 设置 other address which will send report hard copy
     */
    public void setHardCopyToOther(String hardCopyToOther) {
        this.hardCopyToOther = hardCopyToOther == null ? null : hardCopyToOther.trim();
    }

    /**
     * SoftCopyToApplicantFlag BIT<br>
     * 获得 the flag to indicate the report soft  copy need send to order applicnt, 0 - N, 1 - Yes
     */
    public Integer getSoftCopyToApplicantFlag() {
        return softCopyToApplicantFlag;
    }

    /**
     * SoftCopyToApplicantFlag BIT<br>
     * 设置 the flag to indicate the report soft  copy need send to order applicnt, 0 - N, 1 - Yes
     */
    public void setSoftCopyToApplicantFlag(Integer softCopyToApplicantFlag) {
        this.softCopyToApplicantFlag = softCopyToApplicantFlag;
    }

    /**
     * SoftCopyToPayerFlag BIT<br>
     * 获得 the flag to indicate the report soft  copy need send to order payer, 0 - N, 1 - Yes
     */
    public Integer getSoftCopyToPayerFlag() {
        return softCopyToPayerFlag;
    }

    /**
     * SoftCopyToPayerFlag BIT<br>
     * 设置 the flag to indicate the report soft  copy need send to order payer, 0 - N, 1 - Yes
     */
    public void setSoftCopyToPayerFlag(Integer softCopyToPayerFlag) {
        this.softCopyToPayerFlag = softCopyToPayerFlag;
    }

    /**
     * SoftCopyToOther VARCHAR(500)<br>
     * 获得 other address which will send report soft copy
     */
    public String getSoftCopyToOther() {
        return softCopyToOther;
    }

    /**
     * SoftCopyToOther VARCHAR(500)<br>
     * 设置 other address which will send report soft copy
     */
    public void setSoftCopyToOther(String softCopyToOther) {
        this.softCopyToOther = softCopyToOther == null ? null : softCopyToOther.trim();
    }

    /**
     * HtmlString VARCHAR(10000)<br>
     * 获得
     */
    public String getHtmlString() {
        return htmlString;
    }

    /**
     * HtmlString VARCHAR(10000)<br>
     * 设置
     */
    public void setHtmlString(String htmlString) {
        this.htmlString = htmlString == null ? null : htmlString.trim();
    }

    /**
     * PdfReportSecurity BIT 默认值[0]<br>
     * 获得
     */
    public Integer getPdfReportSecurity() {
        return pdfReportSecurity;
    }

    /**
     * PdfReportSecurity BIT 默认值[0]<br>
     * 设置
     */
    public void setPdfReportSecurity(Integer pdfReportSecurity) {
        this.pdfReportSecurity = pdfReportSecurity;
    }

    /**
     * ActiveIndicator BIT 默认值[1] 必填<br>
     * 获得 0: inactive, 1: active
     */
    public Integer getActiveIndicator() {
        return activeIndicator;
    }

    /**
     * ActiveIndicator BIT 默认值[1] 必填<br>
     * 设置 0: inactive, 1: active
     */
    public void setActiveIndicator(Integer activeIndicator) {
        this.activeIndicator = activeIndicator;
    }

    /**
     * CreatedBy VARCHAR(50)<br>
     * 获得 Founder of the UserName
     */
    public String getCreatedBy() {
        return createdBy;
    }

    /**
     * CreatedBy VARCHAR(50)<br>
     * 设置 Founder of the UserName
     */
    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy == null ? null : createdBy.trim();
    }

    /**
     * CreatedDate TIMESTAMP(19)<br>
     * 获得 Creation time
     */
    public Date getCreatedDate() {
        return createdDate;
    }

    /**
     * CreatedDate TIMESTAMP(19)<br>
     * 设置 Creation time
     */
    public void setCreatedDate(Date createdDate) {
        this.createdDate = createdDate;
    }

    /**
     * ModifiedBy VARCHAR(50)<br>
     * 获得 Edit the UserName (like CreateBy for the first time)
     */
    public String getModifiedBy() {
        return modifiedBy;
    }

    /**
     * ModifiedBy VARCHAR(50)<br>
     * 设置 Edit the UserName (like CreateBy for the first time)
     */
    public void setModifiedBy(String modifiedBy) {
        this.modifiedBy = modifiedBy == null ? null : modifiedBy.trim();
    }

    /**
     * ModifiedDate TIMESTAMP(19)<br>
     * 获得 Edit the Date (like CreateDate for the first time)
     */
    public Date getModifiedDate() {
        return modifiedDate;
    }

    /**
     * ModifiedDate TIMESTAMP(19)<br>
     * 设置 Edit the Date (like CreateDate for the first time)
     */
    public void setModifiedDate(Date modifiedDate) {
        this.modifiedDate = modifiedDate;
    }

    /**
     * PaymentRemark VARCHAR(500)<br>
     * 获得
     */
    public String getPaymentRemark() {
        return paymentRemark;
    }

    /**
     * PaymentRemark VARCHAR(500)<br>
     * 设置
     */
    public void setPaymentRemark(String paymentRemark) {
        this.paymentRemark = paymentRemark == null ? null : paymentRemark.trim();
    }

    /**
     * QualificationType VARCHAR(50)<br>
     * 获得
     */
    public String getQualificationType() {
        return qualificationType;
    }

    /**
     * QualificationType VARCHAR(50)<br>
     * 设置
     */
    public void setQualificationType(String qualificationType) {
        this.qualificationType = qualificationType;
    }

    public Integer getConvertInHouseMethodFlag() {
        return convertInHouseMethodFlag;
    }

    public void setConvertInHouseMethodFlag(Integer convertInHouseMethodFlag) {
        this.convertInHouseMethodFlag = convertInHouseMethodFlag;
    }

    /**
     * DraftReportRequired TINYINT(3)<br>
     * 获得
     */
    public Integer getDraftReportRequired() {
        return draftReportRequired;
    }

    /**
     * DraftReportRequired TINYINT(3)<br>
     * 设置
     */
    public void setDraftReportRequired(Integer draftReportRequired) {
        this.draftReportRequired = draftReportRequired;
    }

    /**
     * ProformaInvoice TINYINT(3)<br>
     * 获得
     */
    public Integer getProformaInvoice() {
        return proformaInvoice;
    }

    /**
     * ProformaInvoice TINYINT(3)<br>
     * 设置
     */
    public void setProformaInvoice(Integer proformaInvoice) {
        this.proformaInvoice = proformaInvoice;
    }

    /**
     * LiquidTestSample TINYINT(3)<br>
     * 获得
     */
    public Integer getLiquidTestSample() {
        return liquidTestSample;
    }

    /**
     * LiquidTestSample TINYINT(3)<br>
     * 设置
     */
    public void setLiquidTestSample(Integer liquidTestSample) {
        this.liquidTestSample = liquidTestSample;
    }

    /**
     * VatType TINYINT(3)<br>
     * 获得
     */
    public Integer getVatType() {
        return vatType;
    }

    /**
     * VatType TINYINT(3)<br>
     * 设置
     */
    public void setVatType(Integer vatType) {
        this.vatType = vatType;
    }

    public String getPhotoRequest() {
        return photoRequest;
    }

    public void setPhotoRequest(String photoRequest) {
        this.photoRequest = photoRequest;
    }

    public Integer getNeedConclusion() {
        return needConclusion;
    }

    public void setNeedConclusion(Integer needConclusion) {
        this.needConclusion = needConclusion;
    }

    public String getHardCopyReportDeliverWay() {
        return hardCopyReportDeliverWay;
    }

    public void setHardCopyReportDeliverWay(String hardCopyReportDeliverWay) {
        this.hardCopyReportDeliverWay = hardCopyReportDeliverWay;
    }

    public String getInvoiceDeliverWay() {
        return invoiceDeliverWay;
    }

    public void setInvoiceDeliverWay(String invoiceDeliverWay) {
        this.invoiceDeliverWay = invoiceDeliverWay;
    }

    /**
     * SubcontractFee BigDecimal(18,5)<br>
     * 获得 Subcontract Fee
     */
    public BigDecimal getSubcontractFee() {
        return subcontractFee;
    }

    /**
     * SubcontractFee BigDecimal(18,5)<br>
     * 设置 Subcontract Fee
     */
    public void setSubcontractFee(BigDecimal subcontractFee) {
        this.subcontractFee = subcontractFee;
    }


    /**
     * SubcontractFeeCurrency VARCHAR(50)<br>
     * 获得 Subcontract Fee Currency
     */
    public String getSubcontractFeeCurrency() {
        return subcontractFeeCurrency;
    }

    /**
     * SubcontractFeeCurrency VARCHAR(50)<br>
     * 设置 Subcontract Fee Currency
     */
    public void setSubcontractFeeCurrency(String subcontractFeeCurrency) {
        this.subcontractFeeCurrency = subcontractFeeCurrency == null ? null : subcontractFeeCurrency.trim();
    }

    /**
     * QrcodeFlag VARCHAR(500)<br>
     * 获得 QrcodeFlag
     */
    public String getQrcodeFlag() {
        return qrcodeFlag;
    }

    /**
     * QrcodeFlag VARCHAR(500)<br>
     * 设置 QrcodeFlag
     */
    public void setQrcodeFlag(String qrcodeFlag) {
        this.qrcodeFlag = qrcodeFlag == null ? null : qrcodeFlag.trim();
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int hashCode = 1;

        hashCode = prime * hashCode + (StringUtil.hashCode(ID));
        hashCode = prime * hashCode + (StringUtil.hashCode(generalOrderInstanceID));
        hashCode = prime * hashCode + (StringUtil.hashCode(subContractID));
        hashCode = prime * hashCode + (StringUtil.hashCode(subContractNo));
        hashCode = prime * hashCode + (StringUtil.hashCode(evaluationBasis));

        hashCode = prime * hashCode + (StringUtil.hashCode(otherRequirements));
        hashCode = prime * hashCode + (StringUtil.hashCode(qualification));
        hashCode = prime * hashCode + (StringUtil.hashCode(reoprtDate));
        hashCode = prime * hashCode + (StringUtil.hashCode(reportLanguage));

        hashCode = prime * hashCode + (StringUtil.hashCode(reportManner));
        hashCode = prime * hashCode + (StringUtil.hashCode(reportType));
        hashCode = prime * hashCode + (StringUtil.hashCode(reportRequirement));
        hashCode = prime * hashCode + (StringUtil.hashCode(reportTemplate));
        hashCode = prime * hashCode + (StringUtil.hashCode(reportQty));
        hashCode = prime * hashCode + (StringUtil.hashCode(resultJudgingFlag));
        hashCode = prime * hashCode + (StringUtil.hashCode(serviceType));
        hashCode = prime * hashCode + (StringUtil.hashCode(displaySupplierFlag));
        hashCode = prime * hashCode + (StringUtil.hashCode(commentFlag));

        hashCode = prime * hashCode + (StringUtil.hashCode(hardCopyFlag));
        hashCode = prime * hashCode + (StringUtil.hashCode(chineseReportFlag));
        hashCode = prime * hashCode + (StringUtil.hashCode(takePhotoFlag));
        hashCode = prime * hashCode + (StringUtil.hashCode(confirmCoverPageFlag));
        hashCode = prime * hashCode + (StringUtil.hashCode(packageIndicator));
        hashCode = prime * hashCode + (StringUtil.hashCode(takePhotoRemark));

        hashCode = prime * hashCode + (StringUtil.hashCode(returnResidueSampleFlag));
        hashCode = prime * hashCode + (StringUtil.hashCode(returnTestedSampleFlag));
        hashCode = prime * hashCode + (StringUtil.hashCode(returnResidueSampleRemark));
        hashCode = prime * hashCode + (StringUtil.hashCode(returnTestedSampleRemark));
        hashCode = prime * hashCode + (StringUtil.hashCode(reportAccreditationNeededFlag));
        hashCode = prime * hashCode + (StringUtil.hashCode(hardCopyToApplicantFlag));

        hashCode = prime * hashCode + (StringUtil.hashCode(hardCopyToPayertFlag));
        hashCode = prime * hashCode + (StringUtil.hashCode(hardCopyToOther));
        hashCode = prime * hashCode + (StringUtil.hashCode(softCopyToApplicantFlag));
        hashCode = prime * hashCode + (StringUtil.hashCode(softCopyToPayerFlag));
        hashCode = prime * hashCode + (StringUtil.hashCode(softCopyToOther));
        hashCode = prime * hashCode + (StringUtil.hashCode(htmlString));
        hashCode = prime * hashCode + (StringUtil.hashCode(pdfReportSecurity));
        hashCode = prime * hashCode + (StringUtil.hashCode(paymentRemark));

        hashCode = prime * hashCode + (StringUtil.hashCode(needConclusion));
        hashCode = prime * hashCode + (StringUtil.hashCode(photoRequest));
        hashCode = prime * hashCode + (StringUtil.hashCode(hardCopyReportDeliverWay));
        hashCode = prime * hashCode + (StringUtil.hashCode(invoiceDeliverWay));

        hashCode = prime * hashCode + (StringUtil.hashCode(subcontractFee));
        hashCode = prime * hashCode + (StringUtil.hashCode(subcontractFeeCurrency));
        hashCode = prime * hashCode + (StringUtil.hashCode(qrcodeFlag));
        return hashCode;
    }

}