package com.sgs.otsnotes.facade.model.common;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.sgs.framework.core.base.BaseProductLine;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.ConstraintViolation;
import javax.validation.Validation;
import javax.validation.Validator;
import java.util.Set;

/**
 *
 */
@Deprecated
public abstract class OtsNotesRequest extends BaseProductLine {
    private static final long serialVersionUID = -7140385409591586152L;

    private static Validator VALIDATOR = Validation.buildDefaultValidatorFactory().getValidator();
    /**
     *
     */
    @JsonProperty("sgsToken")
    @ApiModelProperty(hidden = true)
    private String token;
    /**
     * 请求ID
     */
    @JsonInclude(JsonInclude.Include.NON_DEFAULT)
    @ApiModelProperty(hidden = true)
    private String requestId;
    /**
     *
     */
    private Boolean shuntLab;

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    /**
     *
     * @return
     */
    public String getRequestId() {
        return requestId;
    }

    public void setRequestId(String requestId) {
        this.requestId = requestId;
    }

    public void validate() {
        StringBuilder errorMsgs = new StringBuilder();
        Set<ConstraintViolation<OtsNotesRequest>> violations = VALIDATOR.validate(this);
        if (violations != null && violations.size() > 0) {
            for (ConstraintViolation<OtsNotesRequest> violation : violations) {
                errorMsgs.append(violation.getPropertyPath()).append(":").append(violation.getMessage()).append("|");
            }
            throw new IllegalArgumentException(errorMsgs.substring(0, errorMsgs.length() - 1));
        }
    }

    /**
     * 一般请求，requestId不强制必填
     * @return
     */
    public boolean requireRequestId( ) {
        return false;
    }

    public Boolean getShuntLab() {
        return shuntLab;
    }

    public void setShuntLab(Boolean shuntLab) {
        this.shuntLab = shuntLab;
    }
}
