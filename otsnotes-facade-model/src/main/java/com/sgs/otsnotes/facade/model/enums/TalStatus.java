package com.sgs.otsnotes.facade.model.enums;

import java.util.HashMap;
import java.util.Map;

public enum TalStatus {
    None(0, "None"),
    Active(1, "Active"),
    PhaseOut(2, "PhaseOut"),
    Inactive(3, "Inactive");

    private final int status;
    private final String code;

    TalStatus(int status, String code) {
        this.status = status;
        this.code = code;
    }

    public int getStatus() {
        return status;
    }

    public String getCode() {
        return this.code;
    }

    static Map<Integer, TalStatus> maps = new HashMap<>();
    public static final Map<String, TalStatus> codeMaps = new HashMap<String, TalStatus>() {
        private static final long serialVersionUID = -8986866330615001847L;
        {
            for (TalStatus enu : TalStatus.values()) {
                put(enu.getCode().toLowerCase(), enu);
                maps.put(enu.getStatus(), enu);
            }
        }
    };

    public static TalStatus getCode(String code) {
        if (code == null || !codeMaps.containsKey(code.toLowerCase())) {
            return null;
        }
        return codeMaps.get(code.toLowerCase());
    }

    public static boolean check(Integer talStatus, TalStatus... talStatuItems) {
        if (talStatus == null || !maps.containsKey(talStatus.intValue())) {
            return false;
        }
        return check(maps.get(talStatus.intValue()), talStatuItems);
    }

    public static boolean check(TalStatus talStatus, TalStatus... talStatuItems) {
        if (talStatus == null || talStatuItems == null || talStatuItems.length <= 0){
            return false;
        }
        for (TalStatus talStatuItem: talStatuItems){
            if (talStatuItem.getStatus() == talStatus.getStatus()){
                return true;
            }
        }
        return false;
    }
}
