package com.sgs.otsnotes.facade.model.trims;

import com.sgs.framework.core.base.BaseProductLine;

public class TrimsSyncBaseRsp extends BaseProductLine {
    /**
     *
     */
    private String status;
    /**
     *
     */
    private String massage;
    /**
     *
     */
    private String code;

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getMassage() {
        return massage;
    }

    public void setMassage(String massage) {
        this.massage = massage;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }
}
