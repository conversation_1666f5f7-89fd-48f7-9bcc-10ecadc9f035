package com.sgs.otsnotes.facade.model.enums;

import java.util.HashMap;
import java.util.Map;

/**
 * 过滤类型（1：customer、2：PPStandard），默认为：1
 */
public enum FilterType {
    Customer(1, "Customer"),
    PPStandard(2, "PPStandard");

    private int type;
    private String code;

    FilterType(int type, String code) {
        this.type = type;
        this.code = code;
    }

    public int getType() {
        return type;
    }

    public String getCode() {
        return this.code;
    }

    static Map<Integer, FilterType> maps = new HashMap<>();

    static {
        for (FilterType type : FilterType.values()) {
            maps.put(type.getType(), type);
        }
    }

    /**
     *
     * @param status
     * @return
     */
    public static FilterType findType(Integer status) {
        if (status == null || !maps.containsKey(status.intValue())){
            return FilterType.Customer;
        }
        return maps.get(status);
    }

    /**
     *
     * @param code
     * @return
     */
    public static FilterType findCode(Integer code) {
        if (code == null || !maps.containsKey(code.intValue())){
            return FilterType.Customer;
        }
        return maps.get(code);
    }

    /**
     *
     * @param status
     * @return
     */
    public static boolean check(Integer status) {
        if (status == null){
            return false;
        }
        return maps.containsKey(status.intValue());
    }

    /**
     *
     * @param status
     * @param type
     * @return
     */
    public static boolean check(Integer status, FilterType type) {
        if (status == null || !maps.containsKey(status.intValue())){
            return false;
        }
        return maps.get(status.intValue()) == type;
    }
}
