package com.sgs.otsnotes.facade.model.enums;

/**
 * 报告section code定义
 * <AUTHOR>
 * @date 2020/7/18 18:06
 */
public enum ReportTemplateSectionCodeEnum {

    TestResult(1306, "Test Result"),

    CoverPager_Header(1313, "Coverpager - Header"),

    Tested_Sample_Photo(1323, "Tested Sample Photo"),

    CoverPage_ProposedCareInstruction(1315, "CoverPage-ProposedCareInstruction"),

    Coverpager_Overall_Conclusion(1321, "Coverpager - Overall Conclusion"),

    Conclusion_Summary(1317, "Conclusion Summary"),

    Signature(1307, "Signature"),

    Coverpage_OTS(1322, "Coverpage - OTS"),

    Conclusion_Interpretation(1319, "Conclusion-Interpretation"),

    Sample_Photos(1310, "Sample Photos"),

    Cover_Page(1301, "Cover Page"),

    Selected_Test_Line(1330, "Selected Test Line"),

    Full_Test_Line(1331, "Full Test Line"),

    Conclusion(1305, "Conclusion"),

    Cover_Page_Tracking(1304, "Cover Page - Tracking"),

    Protocol_Document(1326, "Protocol Document"),

    COC(1311, "COC"),

    Selected_DR_Test_Line(1332, "Selected DR Test Line"),

    Conclusion_Fail_Remark(1318, "Conclusion-Fail Remark"),

    Claim_Verification(1325, "Claim Verification"),

    Conclusion_Executive_Summary(1324, "Conclusion-Executive Summary"),

    Coverpager_SampleDescription(1314, "Coverpager - SampleDescription"),

    TRF(1316, "TRF"),

    Cover_Page_Test_Performed(1303, "Cover Page - Test Performed"),

    Cover_Page_DFF_Order_Information(1302, "Cover Page - DFF Order Information"),

    Appendix(1309, "Appendix"),

    CoverPage_RecommendCareInstruction(1320, "CoverPage-RecommendCareInstruction"),

    Test_Line(1327, "Test Line"),

    Component_List(1308, "Component List"),

    PP(1328, "PP"),

    Section(1329, "Section");

    private final int code;
    private final String name;

    ReportTemplateSectionCodeEnum(int code, String name) {
        this.code = code;
        this.name = name;
    }

    public int getCode() {
        return code;
    }

    public String getName() {
        return this.name;
    }

    public static ReasonTypeEnum getCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (ReasonTypeEnum item : ReasonTypeEnum.values()) {
            if (code.equals(item.getCode())) {
                return item;
            }
        }
        return null;
    }
    public static boolean check(Integer code,ReportTemplateSectionCodeEnum...enums){
        if(code==null){
            return false;
        }
        for (ReportTemplateSectionCodeEnum anEnum : enums) {
            if(code.compareTo(anEnum.code)==0){
                return true;
            }
        }
        return false;
    }
}
