package com.sgs.otsnotes.facade.model.enums;

import java.util.HashMap;
import java.util.Map;

public enum ConfirmMatrixTypeEnum {
    Chem(2,"C", "chem"),
    Phy(1,"P","phy"),
    All(0,"A", "all");

    private int code;
    private  String shortMessage;
    private String message;

    ConfirmMatrixTypeEnum(int code,String shortMessage, String message) {
        this.code = code;
        this.shortMessage=shortMessage;
        this.message = message;
    }

    public int getCode() {
        return code;
    }

    public String getShortMessage() {
        return this.shortMessage;
    }

    public String getMessage() {
        return this.message;
    }

    public static final Map<Integer, ConfirmMatrixTypeEnum> maps = new HashMap<Integer, ConfirmMatrixTypeEnum>() {
        private static final long serialVersionUID = -8986866330615001847L;
        {
            for (ConfirmMatrixTypeEnum confirmMatrixTypeEnum : ConfirmMatrixTypeEnum.values()) {
                put(confirmMatrixTypeEnum.getCode(), confirmMatrixTypeEnum);
            }
        }
    };
    public static final Map<String, ConfirmMatrixTypeEnum> mapsByShortMessage = new HashMap<String, ConfirmMatrixTypeEnum>() {
        private static final long serialVersionUID = -8986866330615001847L;
        {
            for (ConfirmMatrixTypeEnum confirmMatrixTypeEnum : ConfirmMatrixTypeEnum.values()) {
                put(confirmMatrixTypeEnum.getShortMessage(), confirmMatrixTypeEnum);
            }
        }
    };
    public static ConfirmMatrixTypeEnum getByShortMessage(String  shortMessage) {
        if (shortMessage == null || !mapsByShortMessage.containsKey(shortMessage)){
            return null;
        }
        return mapsByShortMessage.get(shortMessage);
    }

    public static ConfirmMatrixTypeEnum getCode(Integer code) {
        if (code == null || !maps.containsKey(code.intValue())){
            return null;
        }
        return maps.get(code.intValue());
    }

    public boolean check(ConfirmMatrixTypeEnum... confirmMatrixTypeEnums){
        if (confirmMatrixTypeEnums == null || confirmMatrixTypeEnums.length <= 0){
            return false;
        }
        for (ConfirmMatrixTypeEnum confirmMatrixTypeEnum: confirmMatrixTypeEnums){
            if (this.getCode() == confirmMatrixTypeEnum.getCode()){
                return true;
            }
        }
        return false;
    }

    public static boolean checkStatus(Integer status, ConfirmMatrixTypeEnum... confirmMatrixTypeEnums){
        if (status == null || !maps.containsKey(status.intValue()) || confirmMatrixTypeEnums == null || confirmMatrixTypeEnums.length <= 0) {
            return false;
        }
        for (ConfirmMatrixTypeEnum confirmMatrixTypeEnum: confirmMatrixTypeEnums){
            if (status.intValue() == confirmMatrixTypeEnum.getCode()){
                return true;
            }
        }
        return false;
    }
}
