package com.sgs.otsnotes.facade.model.req;

import com.sgs.otsnotes.facade.model.common.OtsNotesRequest;

import java.util.Set;

public class QueryCuttingRequestReq extends OtsNotesRequest {
    /**
     *
     */
    private String orderId;
    /**
     *
     */
    private Set<Integer> testLineVersionIds;
    /**
     *
     */
    private Integer customerAccountId;

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public Set<Integer> getTestLineVersionIds() {
        return testLineVersionIds;
    }

    public void setTestLineVersionIds(Set<Integer> testLineVersionIds) {
        this.testLineVersionIds = testLineVersionIds;
    }

    public Integer getCustomerAccountId() {
        return customerAccountId;
    }

    public void setCustomerAccountId(Integer customerAccountId) {
        this.customerAccountId = customerAccountId;
    }
}
