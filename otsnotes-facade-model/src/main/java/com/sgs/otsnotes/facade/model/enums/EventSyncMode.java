package com.sgs.otsnotes.facade.model.enums;

import java.util.HashMap;
import java.util.Map;

public enum EventSyncMode {
    IgnoreUpdate(0, "Ignore Update"),
    PartialUpdate(1, "Partial Update"),
    FullUpdate(2, "Full Update");

    private int status;
    private String code;

    EventSyncMode(int status, String code) {
        this.status = status;
        this.code = code;
    }

    public int getStatus() {
        return status;
    }

    public String getCode() {
        return code;
    }

    public static final Map<Integer, EventSyncMode> maps = new HashMap<Integer, EventSyncMode>() {
        private static final long serialVersionUID = -8986866330615001847L;
        {
            for (EventSyncMode syncType : EventSyncMode.values()) {
                put(syncType.getStatus(), syncType);
            }
        }
    };

    /**
     *
     * @param status
     * @return
     */
    public static EventSyncMode findStatus(Integer status) {
        if (status == null || !maps.containsKey(status)) {
            return null;
        }
        return maps.get(status);
    }

    /**
     *
     * @param status
     * @param syncMode
     * @return
     */
    public static boolean check(Integer status, EventSyncMode syncMode) {
        if (status == null || syncMode == null || !maps.containsKey(status)){
            return false;
        }
        return maps.get(status) == syncMode;
    }

    /**
     *
     * @param syncMode
     * @return
     */
    public boolean check(EventSyncMode syncMode) {
        if (syncMode == null){
            return false;
        }
        return this.getStatus() == syncMode.getStatus();
    }
}
