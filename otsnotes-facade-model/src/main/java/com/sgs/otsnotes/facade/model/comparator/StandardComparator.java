package com.sgs.otsnotes.facade.model.comparator;

import com.sgs.otsnotes.facade.model.rsp.testLine.StandardRsp;

import java.util.Comparator;

public class StandardComparator implements Comparator<StandardRsp> {
    /**
     * 是否为升序
     */
    private boolean isAsc;
    /**
     *
     * @param isAsc
     */
    public StandardComparator(boolean isAsc) {
        this.isAsc = isAsc;
    }

    /**
     * 排序号升序、日期升序
     *
     * @param o1
     * @param o2
     * @return
     */
    @Override
    public int compare(StandardRsp o1, StandardRsp o2) {
        Integer citationSeq1 = o1.getCitationSeq();
        if (citationSeq1 == null){
            citationSeq1 = 0;
        }
        Integer citationSeq2 = o2.getCitationSeq();
        if (citationSeq2 == null){
            citationSeq2 = 0;
        }
        int index = Integer.compare(citationSeq1.intValue(), citationSeq2.intValue());
        if (index < 0) {
            return isAsc ? -1 : 1;
        }
        if (index > 0) {
            return isAsc ? 1 : -1;
        }
        return index;
    }

}
