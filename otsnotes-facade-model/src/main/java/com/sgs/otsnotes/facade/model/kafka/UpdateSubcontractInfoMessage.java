package com.sgs.otsnotes.facade.model.kafka;

import com.sgs.framework.core.base.BaseProductLine;
import com.sgs.otsnotes.facade.model.gpn.subcontract.info.SubContractExternalRelationshipInfo;
import com.sgs.otsnotes.facade.model.info.SubContractTestLineInfo;
import com.sgs.otsnotes.facade.model.info.SubContractTestLineMappingInfo;
import com.sgs.otsnotes.facade.model.info.subcontract.SubContractInfo;
import com.sgs.otsnotes.facade.model.po.SubcontractRelInfoPO;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/8/6 18:03
 */
@Data
public class UpdateSubcontractInfoMessage extends BaseProductLine {
    private static final long serialVersionUID = 932346385704794908L;

    private SubContractTestLineInfo testLineInfo;

    private SubContractInfo subContractPO;

    private SubContractExternalRelationshipInfo subContractExternalRelationshipInfo;

    private List<SubContractTestLineMappingInfo> subContractTestLineMappingInfos;

    private List<SubcontractRelInfoPO> subcontractRelInfoPOS;

    private List<SubcontractRelInfoPO> needUpdateRels;

    private List<Long> delSubcontractRelMappingIds;

}
