package com.sgs.otsnotes.facade.model.enums;

import java.util.HashMap;
import java.util.Map;

/**
 * 0：PP
 * 1：Section
 * 2：TestLine
 * 3：Citation
 * 4：Analyte
 * 5：ConditionType
 * 6：Condition
 * 7：Position
 * 8：UsageType
 * 9：PPTestline
 */
public enum LangTypeEnum {
    PP(0, "PP"),
    Section(1, "Section"),
    TestLine(2, "TestLine"),
    Citation(3, "Citation"),
    Analyte(4, "Analyte"),
    ConditionType(5, "ConditionType"),
    Condition(6, "Condition"),
    Position(7, "Position"),
    UsageType(8, "UsageType"),
    PpArtifactRel(9, "PpArtifactRel"),
    AnalyteUnit(10, "AnalyteUnit"),
    PpCitation(11, "PpCitation");

    private int type;
    private String message;

    LangTypeEnum(int type, String message) {
        this.type = type;
        this.message = message;
    }

    public int getType() {
        return type;
    }

    public String getMessage() {
        return message;
    }

    public static final Map<Integer, LangTypeEnum> maps = new HashMap<Integer, LangTypeEnum>() {
        private static final long serialVersionUID = -8986866330615001847L;
        {
            for (LangTypeEnum langType : LangTypeEnum.values()) {
                put(langType.getType(), langType);
            }
        }
    };

    public static LangTypeEnum findType(Integer type) {
        if (type == null || !maps.containsKey(type.intValue())){
            return null;
        }
        return maps.get(type);
    }

    /**
     *
     * @param type
     * @param types
     * @return
     */
    public static boolean check(Integer type, LangTypeEnum types) {
        if (type == null || !maps.containsKey(type.intValue())){
            return false;
        }
        return maps.get(type.intValue()) == types;
    }
}
