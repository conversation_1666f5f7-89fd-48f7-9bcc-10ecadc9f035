package com.sgs.otsnotes.facade.model.enums;


import java.util.HashMap;
import java.util.Map;

public enum AmendType {
    None(0, "None"),
    Extract(1, "Extract"),
    Replaced(2, "Replaced"),
    Supplement(3,
            OrderBizType.Order.getStatus() |
                    OrderBizType.Report.getStatus() |
                    OrderBizType.Sample.getStatus(),
            "Supplement"),
    Original(4, "Original"),
    TranslationReport(5, "TranslationReport"),
    Rework(6,"Rework");

    private int status;
    private long bizType;
    private String code;

    AmendType(int status, String code) {
        this.status = status;
        this.code = code;
    }

    AmendType(int status, long bizType, String code) {
        this(status, code);
        this.bizType = bizType;
    }

    public int getStatus() {
        return status;
    }

    public long getBizType() {
        return bizType;
    }

    public String getCode() {
        return this.code;
    }

    static Map<Integer, AmendType> maps = new HashMap<>();

    static {
        for (AmendType type : AmendType.values()) {
            maps.put(type.getStatus(), type);
        }
    }

    public static AmendType findCode(Integer code) {
        if (code == null || !maps.containsKey(code.intValue())){
            return null;
        }
        return maps.get(code);
    }

    public static boolean check(Integer status) {
        if (status == null){
            return false;
        }
        return maps.containsKey(status.intValue());
    }

    /**
     *
     * @param status
     * @param amendType
     * @return
     */
    public static boolean check(Integer status, AmendType amendType) {
        if (status == null || !maps.containsKey(status.intValue())){
            return false;
        }
        return maps.get(status.intValue()) == amendType;
    }
    public static boolean check(Integer status, AmendType... amendTypes){
        if (status == null || !maps.containsKey(status) || amendTypes == null || amendTypes.length <= 0) {
            return false;
        }
        for (AmendType amendType: amendTypes){
            if (status == amendType.getStatus()){
                return true;
            }
        }
        return false;
    }
    /**
     * 如果修改类型为Supplement，则跳过此步骤（Supplement无需Copy Matrix）
     * @param bizType
     * @return
     */
    public boolean check(OrderBizType bizType){
        if (this.getBizType() <= 0 || status != AmendType.Supplement.getStatus()){
            return true;
        }
        return bizType == OrderBizType.Pre || ((this.getBizType() & bizType.getStatus()) > 0);
    }
}
