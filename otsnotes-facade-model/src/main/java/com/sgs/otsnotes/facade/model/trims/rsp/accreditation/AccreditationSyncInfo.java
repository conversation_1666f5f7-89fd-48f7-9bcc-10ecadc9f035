package com.sgs.otsnotes.facade.model.trims.rsp.accreditation;

import com.sgs.framework.core.base.BaseProductLine;

import java.util.List;

/**
 * @ClassName AccreditationSyncInfo
 * @Description 同步labSection
 * <AUTHOR>
 * @Date 12/24/2020
 */

public class AccreditationSyncInfo extends BaseProductLine {

    private Integer accreditationId;
    private Integer tlVersionIdentifier;
    private List<AccreditationDetailsSyncInfo> accreditationDetails;

    public Integer getAccreditationId() {
        return accreditationId;
    }

    public void setAccreditationId(Integer accreditationId) {
        this.accreditationId = accreditationId;
    }

    public Integer getTlVersionIdentifier() {
        return tlVersionIdentifier;
    }

    public void setTlVersionIdentifier(Integer tlVersionIdentifier) {
        this.tlVersionIdentifier = tlVersionIdentifier;
    }

    public List<AccreditationDetailsSyncInfo> getAccreditationDetails() {
        return accreditationDetails;
    }

    public void setAccreditationDetails(List<AccreditationDetailsSyncInfo> accreditationDetails) {
        this.accreditationDetails = accreditationDetails;
    }
}
