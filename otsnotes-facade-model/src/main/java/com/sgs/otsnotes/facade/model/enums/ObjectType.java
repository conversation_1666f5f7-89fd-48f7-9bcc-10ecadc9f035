package com.sgs.otsnotes.facade.model.enums;

import java.util.HashMap;
import java.util.Map;

public enum ObjectType {
    Order("order", "order"),
    TestLine("testLine", "testLine"),
    Job("job", "job"),
    SubContract("subContract", "subContract"),
    StarLims("starLims", "starLims"),
    Report("report", "report");

    private final String code;
    private final String message;

    ObjectType(String code, String message) {
        this.code = code;
        this.message = message;
    }

    public String getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }

    public static final Map<String, ObjectType> maps = new HashMap<String, ObjectType>() {
        private static final long serialVersionUID = -8986866330615001847L;

        {
            for (ObjectType type : ObjectType.values()) {
                put(type.getCode().toLowerCase(), type);
            }
        }
    };

    public static ObjectType getCode(String code) {
        if (code == null || !maps.containsKey(code.toLowerCase())) {
            return null;
        }
        return maps.get(code.toLowerCase());
    }

    public static boolean check(String code, ObjectType... objectTypes) {
        if (code == null || objectTypes == null || !maps.containsKey(code.toLowerCase())) {
            return false;
        }
        for (ObjectType anEnum : objectTypes) {
            if(anEnum.getCode().equals(code)){
                return true;
            }
        }
        return false;
    }

}
