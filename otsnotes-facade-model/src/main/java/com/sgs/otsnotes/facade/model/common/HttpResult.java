package com.sgs.otsnotes.facade.model.common;

import com.sgs.framework.core.base.BaseProductLine;

public class HttpResult<T> extends BaseProductLine {
    /**
     * 是否操作成功0失败 1成功
     */
    private boolean isSuccess;
    /**
     * 返回结果数据
     */
    private T result;
    /**
     *
     */
    private String errorMessage;
    /**
     *
     */
    private String placeholderMessage;

    public boolean isSuccess() {
        return isSuccess;
    }

    public void setIsSuccess(boolean success) {
        isSuccess = success;
    }

    public T getResult() {
        return result;
    }

    public void setResult(T result) {
        this.result = result;
    }

    public String getErrorMessage() {
        return errorMessage;
    }

    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }

    public String getPlaceholderMessage() {
        return placeholderMessage;
    }

    public void setPlaceholderMessage(String placeholderMessage) {
        this.placeholderMessage = placeholderMessage;
    }
}
