package com.sgs.otsnotes.facade.model.info;

import com.sgs.framework.core.base.BaseRequest;
import com.sgs.otsnotes.facade.model.enums.AmendType;
import com.sgs.otsnotes.facade.model.enums.OrderCopyType;
import com.sgs.otsnotes.facade.model.po.SubcontractRelInfoPO;
import com.sgs.otsnotes.facade.model.req.copy.CustomerCopyReq;
import com.sgs.otsnotes.facade.model.req.copy.LabCopyReq;

import java.util.*;

public class OrderCopyInfo extends BaseRequest {
    /**
     *
     */
    public OrderCopyInfo(){
        matrixIds = new HashMap();
        testLineInstanceIds = new HashMap();
        oldTestLineMaps = new HashMap();
        sampleIds = new HashMap();
        conditionIds = new HashMap();
        conditionGroupIds = new HashMap();
        positionIds = new HashMap();
        testSpecimenIds = new HashMap();
        analyteIds = new HashMap();
        ppIds = new HashMap();
        oldPPSampleRelIds = new HashMap();
        oldRelIds = new HashMap();
        oldLimitGroupIds = new HashMap();
        oldTestDataIds = new HashMap();
        testLineConditionGroupIds = new HashMap();
        cancelTestLineIds = new HashMap();
    }
    /**
     *
     */
    private OrderCopyType copyType;
    /**
     *
     */
    private String oldOrderId;
    /**
     *
     */
    private String orderId;
    /**
     *
     */
    private String orderNo;
    /**
     *
     */
    private String oldOrderNo;
    /**
     *
     */
    private String reportId;
    /**
     *
     */
    private String oldReportNo;
    /**
     *
     */
    private String reportNo;
    /**
     *
     */
    private String subContractNo;
    /**
     *
     */
    private String userName;
    /**
     *
     */
    private LabCopyReq lab;
    /**
     *
     */
    private CustomerCopyReq customer;
    /**
     *
     */
    private AmendType amendType;
    /**
     *
     */
    private Date reportDueDate;
    /**
     *
     */
    private List<String> oldMatrixIds;
    /**
     *
     */
    private Map<String, String> matrixIds;
    /**
     * oldId，newId
     */
    private Map<String, String> testLineInstanceIds;
    /**
     * oldId,oldTestLineId
     */
    private Map<String, Integer> oldTestLineMaps;
    /**
     * 新订单<TestLineVersionId, TestLineInstanceID>   LabSection 用
     */
    private Map<Integer, Set<String>> testLineVersionIdMaps;
    /**
     *
     */
    private Map<String, String> sampleIds;
    /**
     *
     */
    private Map<String, String> conditionIds;
    /**
     *
     */
    private Map<String, String> conditionGroupIds;
    /**
     *
     */
    private Map<String, String> positionIds;
    /**
     *
     */
    private Map<String, String> testSpecimenIds;
    /**
     *
     */
    private Map<String, String> analyteIds;
    /**
     *
     */
    private Map<String, String> ppIds;
    /**
     *
     */
    private Map<String, String> oldSampleIds;
    /**
     *
     */
    private Map<String, String> oldOriginalSampleIds;
    /**
     * add by vincent 2020年2月25日 DIG-3382
     */
    private Map<String,String> oldPPSampleRelIds;
    /**
     *
     */
    private Map<String,String> oldLimitGroupIds;
    /**
     *
     */
    private Map<String,String> oldTestDataIds;
    /**
     *
     */
    private Map<String,String> testLineConditionGroupIds;
    /**
     *
     */
    private Map<String, Boolean> cancelTestLineIds;;

    /**
     * OriginalRelId，
     */
    private Map<String, SubcontractRelInfoPO> oldRelIds;

    public OrderCopyType getCopyType() {
        return copyType;
    }

    public void setCopyType(OrderCopyType copyType) {
        this.copyType = copyType;
    }

    public String getOldOrderId() {
        return oldOrderId;
    }

    public void setOldOrderId(String oldOrderId) {
        this.oldOrderId = oldOrderId;
    }

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getOldOrderNo() {
        return oldOrderNo;
    }

    public void setOldOrderNo(String oldOrderNo) {
        this.oldOrderNo = oldOrderNo;
    }

    public String getReportId() {
        return reportId;
    }

    public void setReportId(String reportId) {
        this.reportId = reportId;
    }

    public String getOldReportNo() {
        return oldReportNo;
    }

    public void setOldReportNo(String oldReportNo) {
        this.oldReportNo = oldReportNo;
    }

    public String getReportNo() {
        return reportNo;
    }

    public void setReportNo(String reportNo) {
        this.reportNo = reportNo;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public LabCopyReq getLab() {
        return lab;
    }

    public void setLab(LabCopyReq lab) {
        this.lab = lab;
    }

    public CustomerCopyReq getCustomer() {
        return customer;
    }

    public void setCustomer(CustomerCopyReq customer) {
        this.customer = customer;
    }

    public AmendType getAmendType() {
        return amendType;
    }

    public void setAmendType(AmendType amendType) {
        this.amendType = amendType;
    }

    public Date getReportDueDate() {
        return reportDueDate;
    }

    public void setReportDueDate(Date reportDueDate) {
        this.reportDueDate = reportDueDate;
    }

    public List<String> getOldMatrixIds() {
        return oldMatrixIds;
    }

    public void setOldMatrixIds(List<String> oldMatrixIds) {
        this.oldMatrixIds = oldMatrixIds;
    }

    public Map<String, String> getMatrixIds() {
        return matrixIds;
    }

    public void setMatrixIds(Map<String, String> matrixIds) {
        this.matrixIds = matrixIds;
    }

    public Map<String, String> getTestLineInstanceIds() {
        return testLineInstanceIds;
    }

    public void setTestLineInstanceIds(Map<String, String> testLineInstanceIds) {
        this.testLineInstanceIds = testLineInstanceIds;
    }

    public Map<String, Integer> getOldTestLineMaps() {
        return oldTestLineMaps;
    }

    public void setOldTestLineMaps(Map<String, Integer> oldTestLineMaps) {
        this.oldTestLineMaps = oldTestLineMaps;
    }

    public Map<Integer, Set<String>> getTestLineVersionIdMaps() {
        return testLineVersionIdMaps;
    }

    public void setTestLineVersionIdMaps(Map<Integer, Set<String>> testLineVersionIdMaps) {
        this.testLineVersionIdMaps = testLineVersionIdMaps;
    }

    public Map<String, String> getSampleIds() {
        return sampleIds;
    }

    public void setSampleIds(Map<String, String> sampleIds) {
        this.sampleIds = sampleIds;
    }

    public Map<String, String> getConditionIds() {
        return conditionIds;
    }

    public void setConditionIds(Map<String, String> conditionIds) {
        this.conditionIds = conditionIds;
    }

    public Map<String, String> getConditionGroupIds() {
        return conditionGroupIds;
    }

    public void setConditionGroupIds(Map<String, String> conditionGroupIds) {
        this.conditionGroupIds = conditionGroupIds;
    }

    public Map<String, String> getPositionIds() {
        return positionIds;
    }

    public void setPositionIds(Map<String, String> positionIds) {
        this.positionIds = positionIds;
    }

    public Map<String, String> getTestSpecimenIds() {
        return testSpecimenIds;
    }

    public void setTestSpecimenIds(Map<String, String> testSpecimenIds) {
        this.testSpecimenIds = testSpecimenIds;
    }

    public Map<String, String> getAnalyteIds() {
        return analyteIds;
    }

    public void setAnalyteIds(Map<String, String> analyteIds) {
        this.analyteIds = analyteIds;
    }

    public Map<String, String> getPpIds() {
        return ppIds;
    }

    public void setPpIds(Map<String, String> ppIds) {
        this.ppIds = ppIds;
    }

    public Map<String, String> getOldSampleIds() {
        return oldSampleIds;
    }

    public void setOldSampleIds(Map<String, String> oldSampleIds) {
        this.oldSampleIds = oldSampleIds;
    }

    public Map<String, String> getOldOriginalSampleIds() {
        return oldOriginalSampleIds;
    }

    public void setOldOriginalSampleIds(Map<String, String> oldOriginalSampleIds) {
        this.oldOriginalSampleIds = oldOriginalSampleIds;
    }

    public Map<String, String> getOldPPSampleRelIds() {
        return oldPPSampleRelIds;
    }

    public void setOldPPSampleRelIds(Map<String, String> oldPPSampleRelIds) {
        this.oldPPSampleRelIds = oldPPSampleRelIds;
    }

    public Map<String, String> getOldLimitGroupIds() {
        return oldLimitGroupIds;
    }

    public void setOldLimitGroupIds(Map<String, String> oldLimitGroupIds) {
        this.oldLimitGroupIds = oldLimitGroupIds;
    }

    public Map<String, SubcontractRelInfoPO> getOldRelIds() {
        return oldRelIds;
    }

    public void setOldRelIds(Map<String, SubcontractRelInfoPO> oldRelIds) {
        this.oldRelIds = oldRelIds;
    }

    public String getSubContractNo() {
        return subContractNo;
    }

    public void setSubContractNo(String subContractNo) {
        this.subContractNo = subContractNo;
    }

    public Map<String, String> getOldTestDataIds() {
        return oldTestDataIds;
    }

    public void setOldTestDataIds(Map<String, String> oldTestDataIds) {
        this.oldTestDataIds = oldTestDataIds;
    }

    public Map<String, String> getTestLineConditionGroupIds() {
        return testLineConditionGroupIds;
    }

    public void setTestLineConditionGroupIds(Map<String, String> testLineConditionGroupIds) {
        this.testLineConditionGroupIds = testLineConditionGroupIds;
    }

    public Map<String, Boolean> getCancelTestLineIds() {
        return cancelTestLineIds;
    }

    public void setCancelTestLineIds(Map<String, Boolean> cancelTestLineIds) {
        this.cancelTestLineIds = cancelTestLineIds;
    }
}
