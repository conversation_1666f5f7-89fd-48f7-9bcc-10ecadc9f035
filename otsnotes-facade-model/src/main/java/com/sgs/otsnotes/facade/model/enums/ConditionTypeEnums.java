package com.sgs.otsnotes.facade.model.enums;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */

public enum ConditionTypeEnums {
    None(0, "None"),
    Parent(1, "Parent Condition"),
    Child(2, "Child Condition");

    private int code;
    private String message;

    ConditionTypeEnums(int value, String message) {
        this.code = value;
        this.message = message;
    }

    public int getCode() {
        return this.code;
    }

    public String getMessage() {
        return message;
    }

    static Map<Integer, ConditionTypeEnums> maps = new HashMap<>();

    static {
        for (ConditionTypeEnums type : ConditionTypeEnums.values()) {
            maps.put(type.getCode(), type);
        }
    }

    public static ConditionTypeEnums findCode(Integer code) {
        if (code == null || !maps.containsKey(code.intValue())){
            return null;
        }
        ConditionTypeEnums type = maps.get(code.intValue());
        if (type == null) {
            /*throw new IllegalArgumentException("ConclusionType not found" + code);*/
            return null;
        }
        return type;
    }

    public static boolean check(Integer code) {
        if (code == null || !maps.containsKey(code.intValue())){
            return false;
        }
        return maps.get(code.intValue()) != null;
    }

    public static boolean check(Integer code, ConditionTypeEnums conditionType) {
        if (code == null || !maps.containsKey(code.intValue())){
            return false;
        }
        return maps.get(code.intValue()) != conditionType;
    }
}
