package com.sgs.otsnotes.facade.model.enums;

import java.util.HashMap;
import java.util.Map;

public enum TrimsPricingStatus {
    None(0, "None"),
    Active(1, "Active"),
    PhaseOut(2, "PhaseOut"),
    Inactive(3, "Inactive"),
    Delete(10, "Delete");

    private int status;
    private String code;


    TrimsPricingStatus(int status, String code) {
        this.status = status;
        this.code = code;
    }

    public String getCode() {
        return code;
    }

    public int getStatus() {
        return status;
    }

    static Map<Integer, TrimsPricingStatus> maps = new HashMap<>();
    static Map<String, TrimsPricingStatus> codeMaps = new HashMap<>();

    static {
        for (TrimsPricingStatus status : TrimsPricingStatus.values()) {
            maps.put(status.getStatus(), status);
            codeMaps.put(status.getCode().toLowerCase(), status);
        }
    };

    public static TrimsPricingStatus getStatus(Integer status) {
        if (status == null || !maps.containsKey(status)) {
            return null;
        }
        return maps.get(status);
    }

    public static TrimsPricingStatus getCode(String code) {
        if (code == null || !codeMaps.containsKey(code.toLowerCase())) {
            return null;
        }
        return codeMaps.get(code.toLowerCase());
    }

    public static boolean checkStatus(TrimsPricingStatus status, TrimsPricingStatus... pricingStatus) {
        if (status == null || pricingStatus == null || pricingStatus.length <= 0){
            return false;
        }
        return check(status.getStatus(), pricingStatus);
    }

    /**
     *
     * @param status
     * @param pricingStatus
     * @return
     */
    public static boolean check(Integer status, TrimsPricingStatus... pricingStatus) {
        if (status == null || !maps.containsKey(status.intValue()) || pricingStatus == null || pricingStatus.length <= 0){
            return false;
        }
        for (TrimsPricingStatus pricStatus: pricingStatus){
            if (status.intValue() == pricStatus.getStatus()){
                return true;
            }
        }
        return false;
    }

    /**
     *
     * @param code
     * @param pricStatus
     * @return
     */
    public static boolean check(String code, TrimsPricingStatus... pricStatus) {
        if (code == null || !codeMaps.containsKey(code.toLowerCase()) || pricStatus == null || pricStatus.length <= 0){
            return false;
        }
        int status = codeMaps.get(code.toLowerCase()).getStatus();
        for (TrimsPricingStatus tls: pricStatus){
            if (tls.getStatus() == status){
                return true;
            }
        }
        return false;
    }

    /**
     *
     * @param pricingStatus
     * @return
     */
    public boolean check(TrimsPricingStatus... pricingStatus){
        if (pricingStatus == null || pricingStatus.length <= 0){
            return false;
        }
        for (TrimsPricingStatus tlStatus: pricingStatus){
            if (this.getStatus() == tlStatus.getStatus()){
                return true;
            }
        }
        return false;
    }
}
