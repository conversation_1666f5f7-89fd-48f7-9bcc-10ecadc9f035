package com.sgs.otsnotes.facade.model.comparator;

import com.sgs.otsnotes.facade.model.enums.SampleType;
import com.sgs.otsnotes.facade.model.req.TestSampleReq;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

public class TestSampleComparator implements Comparator<TestSampleReq> {
    /**
     * 是否为升序
     */
    private boolean isAsc;

    /**
     * @param isAsc
     */
    public TestSampleComparator(boolean isAsc) {
        this.isAsc = isAsc;
    }

    /**
     * 排序号升序、日期升序
     *
     * @param o1
     * @param o2
     * @return
     */
    @Override
    public int compare(TestSampleReq o1, TestSampleReq o2) {
        SampleType o1SampleType = SampleType.findType(o1.getSampleType());
        if (o1SampleType != null && o1SampleType == SampleType.OriginalSample) {
            if (o1.getSampleSeq() == null) {
                o1.setSampleSeq(Integer.MAX_VALUE);
            }
        }
        SampleType o2SampleType = SampleType.findType(o2.getSampleType());
        if (o2SampleType != null && o2SampleType == SampleType.OriginalSample) {
            if (o2.getSampleSeq() == null) {
                o2.setSampleSeq(Integer.MAX_VALUE);
            }
        }
        if (o1.getSampleSeq() == null) {
            o1.setSampleSeq(Integer.MAX_VALUE - 1);
        }
        if (o2.getSampleSeq() == null) {
            o2.setSampleSeq(Integer.MAX_VALUE - 1);
        }
        int index = Integer.compare(o1.getSampleSeq(), o2.getSampleSeq());
        if (index < 0) {
            return isAsc ? -1 : 1;
        }
        if (index > 0) {
            return isAsc ? 1 : -1;
        }
        String s1 = o1.getSampleNo();
        String s2 = o2.getSampleNo();
        List<Integer> parts1 = parseParts(s1);
        List<Integer> parts2 = parseParts(s2);

        int maxSize = Math.max(parts1.size(), parts2.size());
        for (int i = 0; i < maxSize; i++) {
            if (i >= parts1.size()) {
                return isAsc ? -1 : 1;
            }
            if (i >= parts2.size()) {
                return isAsc ? 1 : -1;
            }
            int cmp = parts1.get(i).compareTo(parts2.get(i));
            if (cmp != 0) {
                return isAsc?cmp:-cmp;
            }
        }
        return 0;
    }

    private static List<Integer> parseParts(String s) {
        List<Integer> parts = new ArrayList<>();
        Matcher matcher = Pattern.compile("\\d+").matcher(s);
        while (matcher.find()) {
            parts.add(Integer.parseInt(matcher.group()));
        }
        return parts;
    }
}
