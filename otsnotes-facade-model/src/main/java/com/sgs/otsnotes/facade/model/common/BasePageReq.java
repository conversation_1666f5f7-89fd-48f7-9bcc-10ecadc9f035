package com.sgs.otsnotes.facade.model.common;

/**
 * @Author: Jinx
 * @Date: 2019-06-14 17:05
 * @Description:
 **/
public class BasePageReq{

    private int pageSize = 10;
    private int pageNum = 1; //与pagehelper保持一致

    /**
     * 前台传入参数：排序字段该字段默认值为
     */
    private String sidx;
    /**
     * 前台传入参数：排序类型
     */
    private String sord;

    public int getPageSize() {
        return pageSize;
    }

    public void setPageSize(int pageSize) {
        this.pageSize = pageSize;
    }

    public int getPageNum() {
        return pageNum;
    }

    public void setPageNum(int pageNum) {
        this.pageNum = pageNum;
    }

    public String getSidx() {
        return sidx;
    }

    public void setSidx(String sidx) {
        this.sidx = sidx;
    }

    public String getSord() {
        return sord;
    }

    public void setSord(String sord) {
        this.sord = sord;
    }
}
