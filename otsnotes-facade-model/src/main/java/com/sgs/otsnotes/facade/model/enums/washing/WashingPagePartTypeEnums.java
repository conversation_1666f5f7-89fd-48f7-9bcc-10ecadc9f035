package com.sgs.otsnotes.facade.model.enums.washing;

import org.springframework.util.StringUtils;

/**
 * 打印模块类型
 */
public enum WashingPagePartTypeEnums {
    orderNo("1"),
    buyer("2"),
    equipment("3"),
    condition("4"),
    afterWashing("5"),
    conditionAndOther("6");
    private String partType;
    WashingPagePartTypeEnums(String partType){
        this.partType = partType;
    }

    public String getPartType() {
        return partType;
    }

    public static WashingPagePartTypeEnums getPartTypeByPartTypeValue(String partType){
        if(StringUtils.isEmpty(partType)){
            return null;
        }
        for (WashingPagePartTypeEnums value : WashingPagePartTypeEnums.values()) {
            if(partType.equals(value.partType)){
                return value;
            }
        }
        return null;
    }
}
