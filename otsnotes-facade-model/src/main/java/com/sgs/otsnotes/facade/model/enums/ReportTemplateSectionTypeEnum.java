package com.sgs.otsnotes.facade.model.enums;

/**
 * 报告section类型
 * <AUTHOR>
 * @date 2020/7/17 16:49
 */
public enum ReportTemplateSectionTypeEnum {

    Image(1201, "images"),
    WordFile(1202, "word file"),
    <PERSON>(1203, "jasper"),
    WordTemplate(1204, "word template"),
    BlankNode(1205, "blank node"),

    ;

    private final int code;
    private final String name;

    ReportTemplateSectionTypeEnum(int code, String name) {
        this.code = code;
        this.name = name;
    }

    public int getCode() {
        return code;
    }

    public String getName() {
        return this.name;
    }

    public static ReasonTypeEnum getCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (ReasonTypeEnum item : ReasonTypeEnum.values()) {
            if (code.equals(item.getCode())) {
                return item;
            }
        }
        return null;
    }
}
