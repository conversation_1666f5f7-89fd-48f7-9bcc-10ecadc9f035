package com.sgs.otsnotes.facade.model.enums;

import java.util.HashMap;
import java.util.Map;

public enum TrimsPpStatus {
    None(0, "None"),
    Active(1, "Active"),
    PhaseOut(2, "PhaseOut"),
    Inactive(3, "Inactive");

    private int status;
    private String code;

    TrimsPpStatus(int status, String code) {
        this.status = status;
        this.code = code;
    }

    public String getCode() {
        return code;
    }

    public int getStatus() {
        return status;
    }

    static Map<Integer, TrimsPpStatus> maps = new HashMap<>();
    static Map<String, TrimsPpStatus> codeMaps = new HashMap<>();

    static {
        for (TrimsPpStatus status : TrimsPpStatus.values()) {
            maps.put(status.getStatus(), status);
            codeMaps.put(status.getCode().toLowerCase(), status);
        }
    };

    public static TrimsPpStatus getStatus(Integer status) {
        if (status == null || !maps.containsKey(status)) {
            return null;
        }
        return maps.get(status);
    }

    /**
     *
     * @param status
     * @param ppStatus
     * @return
     */
    public static boolean check(Integer status, TrimsPpStatus... ppStatus) {
        if (status == null || !maps.containsKey(status.intValue()) || ppStatus == null || ppStatus.length <= 0){
            return false;
        }
        for (TrimsPpStatus tlStatus: ppStatus){
            if (status.intValue() == tlStatus.getStatus()){
                return true;
            }
        }
        return false;
    }

    public static TrimsPpStatus getCode(String code) {
        if (code == null || !codeMaps.containsKey(code.toLowerCase())) {
            return null;
        }
        return codeMaps.get(code.toLowerCase());
    }

    /**
     *
     * @param code
     * @param ppStatuss
     * @return
     */
    public static boolean check(String code, TrimsPpStatus... ppStatuss) {
        if (code == null || !codeMaps.containsKey(code.toLowerCase()) || ppStatuss == null || ppStatuss.length <= 0){
            return false;
        }
        TrimsPpStatus trimsPpStatus = codeMaps.get(code.toLowerCase());
        for (TrimsPpStatus ppStatus: ppStatuss){
            if (ppStatus.getStatus() == trimsPpStatus.getStatus()){
                return true;
            }
        }
        return false;
    }

    /**
     *
     * @param ppStatus
     * @return
     */
    public boolean check(TrimsPpStatus... ppStatus){
        if (ppStatus == null || ppStatus.length <= 0){
            return false;
        }
        for (TrimsPpStatus tlStatus: ppStatus){
            if (this.getStatus() == tlStatus.getStatus()){
                return true;
            }
        }
        return false;
    }
}
