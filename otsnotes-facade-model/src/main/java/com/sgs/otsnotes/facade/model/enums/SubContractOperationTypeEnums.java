package com.sgs.otsnotes.facade.model.enums;

import java.util.HashMap;
import java.util.Map;

/**
 *
 */
public enum SubContractOperationTypeEnums {
    NONE(0, "none", 15, "None"),
    // 测试
    GetConditionList(1, "testLineInstanceId", 15, "GetConditionList"),
    UpdateCondition(2, "testLineInstanceId", 13, "UpdateCondition"),
    // save TestLine 单独处理 需要校验相关订单的是否包含此TestLine
    SaveTestLine(3, "", 15, "SaveTestLine"),
    SaveAssignSample(4, "testLineIds", 9, "AssignSample"),
    DelTestLine(5, "testLineInstanceId", 8, "DelTestLine"),
    CopyTestLine(6, "testLineInstanceId", 9, "CopyTestLine"),
    UpdateStandard(7, "standards.testLineInstanceId", 13, "UpdateStandard"),
    CancelTestLine(8, "testLineInstanceId", 9, "CancelTestLine"),
    NCTestLine(9, "testLineInstanceId", 8, "NCTestLine"),
    CancelAssignSample(10, "testLineInstanceId", 13, "CancelAssignSample Or NoCancelAssignSample"),
    UpdateBreakDown(11, "testLineInstanceId", 13, "Bom 回传"),
    UpdateTLStatus(12, "testLineInstanceId", 13, "update TL Status"),
    ;

    /**
     *
     */
    private Integer type;
    /**
     * 操作需要解析的 请求参数名
     */
    private String filedName;

    /**
     * 是否可以操作 使用位运算
     * <p>
     * <p>
     * 1: 订单为 内部分包单主单 可以操作
     * 2: 订单为 新版内部分包单主单 可以操作
     * 4: 订单为 内部分包单子单 可以操作
     * 8: 订单为 新版内部分包单子单 可以操作
     */
    private Integer operateType;
    /**
     * 操作的message
     */
    private String message;

    SubContractOperationTypeEnums(Integer type, String filedName, Integer operateType, String message) {
        this.type = type;
        this.filedName = filedName;
        this.operateType = operateType;
        this.message = message;
    }

    public Integer getType() {
        return type;
    }

    public String getMessage() {
        return message;
    }

    public String getFiledName() {
        return filedName;
    }

    public Integer getOperateType() {
        return operateType;
    }

    /**
     * 位运算 计算是否可以操作
     *
     * @param type
     * @param subContractOperationTypeEnums
     * @return
     */
    public static boolean checkOperate(Integer type, SubContractOperationTypeEnums subContractOperationTypeEnums) {
        if (type == null || subContractOperationTypeEnums == null) {
            return false;
        }
        return (type & subContractOperationTypeEnums.getType()) > 0;
    }

    public static final Map<Integer, SubContractOperationTypeEnums> maps = new HashMap<Integer, SubContractOperationTypeEnums>() {
        private static final long serialVersionUID = -8986866330615001847L;

        {
            for (SubContractOperationTypeEnums enu : SubContractOperationTypeEnums.values()) {
                put(enu.getType(), enu);
            }
        }
    };

    public static SubContractOperationTypeEnums getSubContractOperationType(Integer type) {
        if (type == null || !maps.containsKey(type.intValue())) {
            return null;
        }
        return maps.get(type.intValue());
    }

    public static boolean check(Integer type, SubContractOperationTypeEnums... enums) {
        if (type == null) {
            return false;
        }
        for (SubContractOperationTypeEnums anEnum : enums) {
            if (anEnum.type.compareTo(type) == 0) {
                return true;
            }
        }
        return false;
    }

    public static SubContractOperationTypeEnums get(Integer type) {
        if (type == null) {
            return null;
        }
        SubContractOperationTypeEnums[] values = SubContractOperationTypeEnums.values();
        for (SubContractOperationTypeEnums value : values) {
            if (value.type.compareTo(type) == 0) {
                return value;
            }
        }
        return null;
    }
}
