package com.sgs.otsnotes.facade.model.enums;

import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

public enum SampleType {
    OriginalSample(101, "O","O",100*1000000, 1000000,"原样","O"),
    <PERSON><PERSON>(102, "P","C",100000, 1000,"子样", "C"),
    SubSample(103, "P","C",0,"子子样", "SC"),
    MixSample(104, "P","C",999*1000000,"Mix 样", "G"),
    ShareSample(105, "P","C",100000, 1000,"共享样", "S");

    private final int sampleType;
    private int seed = 1;
    private final String categoryPhy;
    private final String categoryChem;
    private final int sampleSeq;
    private final String message;
    private final String shortMessage;

    SampleType(int sampleType, String categoryPhy, String categoryChem, int sampleSeq, String message,String shortMessage) {
        this.sampleType = sampleType;
        this.categoryPhy = categoryPhy;
        this.categoryChem = categoryChem;
        this.sampleSeq = sampleSeq;
        this.message = message;
        this.shortMessage=shortMessage;
    }

    SampleType(int sampleType, String categoryPhy, String categoryChem, int sampleSeq, int seed, String message,String shortMessage) {
        this(sampleType, categoryPhy,categoryChem,sampleSeq,message, shortMessage);
        this.seed = seed;
    }

    public int getSampleType() {
        return sampleType;
    }

    public String getCategoryPhy() {
        return categoryPhy;
    }

    public String getShortMessage() {
        return shortMessage;
    }

    public String getCategoryChem() {
        return categoryChem;
    }

    public String getMessage() {
        return message;
    }

    public int getSampleSeq() {
        return sampleSeq;
    }

    public int getSeed() {
        return seed;
    }

    public static final Map<Integer, SampleType> maps = new HashMap<Integer, SampleType>() {
        private static final long serialVersionUID = -8986866330615001847L;{
            for (SampleType sampleType : SampleType.values()) {
                put(sampleType.getSampleType(), sampleType);
            }
        }
    };

    public static final Map<String, SampleType> smMaps = new HashMap<String, SampleType>() {
        {
            for (SampleType sampleType : SampleType.values()) {
                put(sampleType.getShortMessage(), sampleType);
            }
        }
    };

    public static boolean containsSampleType(Integer sampleType){
        return maps.containsKey(sampleType);
    }

    public static SampleType findType(Integer sampleType) {
        return Optional.ofNullable(maps.get(sampleType)).orElse(null);
    }

    public static String getMessage(Integer type) {
        if (type == null || !maps.containsKey(type.intValue())) {
            return null;
        }
        return maps.get(type).getMessage();
    }

    public static String getShortMessage(Integer type) {
        if (type == null || !maps.containsKey(type.intValue())) {
            return null;
        }
        return maps.get(type).getShortMessage();
    }

    public static boolean equals(Integer parentSampleType, Integer childSampleType) {
        if (parentSampleType == null || childSampleType == null){
            return false;
        }
        if (!maps.containsKey(parentSampleType.intValue()) || !maps.containsKey(childSampleType.intValue())){
            return false;
        }
        SampleType parentType = maps.get(parentSampleType.intValue());
        SampleType childType = maps.get(childSampleType.intValue());
        if (childType == SampleType.Sample || childType == SampleType.ShareSample){
            return parentType == SampleType.OriginalSample;
        }
        if (childType == SampleType.SubSample){
            return parentType == SampleType.Sample || parentType == SampleType.ShareSample;
        }
        return false;
    }

    public static boolean equals(Integer type, SampleType sampleType) {
        if (type == null || !maps.containsKey(type.intValue())){
            return false;
        }
        return maps.get(type.intValue()) == sampleType;
    }

    public static boolean check(Integer sampleType, SampleType... sampleTypes){
        if (sampleType == null || sampleType.intValue() <= 0 || sampleTypes == null || sampleTypes.length <= 0){
            return false;
        }
        for (SampleType st: sampleTypes){
            if (sampleType.intValue() == st.getSampleType()){
                return true;
            }
        }
        return false;
    }

    public static boolean check(SampleType sampleType, SampleType... sampleTypes){
        if (sampleType == null){
            return false;
        }
        return check(sampleType.getSampleType(), sampleTypes);
    }

    public static SampleType findTypeBySM(String shortMessage){
        if (shortMessage == null) {
            return null;
        }
        return smMaps.get(shortMessage.toUpperCase());
    }
}


