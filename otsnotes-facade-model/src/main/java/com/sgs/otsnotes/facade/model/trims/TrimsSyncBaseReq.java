package com.sgs.otsnotes.facade.model.trims;

import com.sgs.framework.core.base.BaseProductLine;

public class TrimsSyncBaseReq extends BaseProductLine {
    /**
     *
     */
    public TrimsSyncBaseReq(){
        this.caller = "SODA";
        this.securityCode = "ab94@rBe9%dbs";
    }
    /**
     *
     */
    private String caller;
    /**
     *
     */
    private String securityCode;

    public String getCaller() {
        return caller;
    }

    public void setCaller(String caller) {
        this.caller = caller;
    }

    public String getSecurityCode() {
        return securityCode;
    }

    public void setSecurityCode(String securityCode) {
        this.securityCode = securityCode;
    }
}
