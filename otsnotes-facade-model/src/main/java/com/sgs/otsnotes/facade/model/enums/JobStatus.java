package com.sgs.otsnotes.facade.model.enums;

import java.util.HashMap;
import java.util.Map;

public enum JobStatus {
    New(1101, "New"),
    Testing(1102, "Testing"),
    Closed(1103, "Closed"),
    Cancelled(1104, "Cancelled"),
    Completed(1105,"Completed"),
    PENDING(1106, "Pending");

    private Integer status;
    private String message;

    JobStatus(Integer status, String message) {
        this.status = status;
        this.message = message;
    }

    public String getMessage() {
        return message;
    }

    public Integer getStatus() {
        return status;
    }

    public static final Map<Integer, JobStatus> maps = new HashMap<Integer, JobStatus>() {
        private static final long serialVersionUID = -8986866330615001847L;
        {
            for (JobStatus enu : JobStatus.values()) {
                put(enu.getStatus(), enu);
            }
        }
    };

    public static JobStatus getJobStatus(Integer status) {
        if (status == null || !maps.containsKey(status.intValue())) {
            return null;
        }
        return maps.get(status.intValue());
    }

    /**
     *
     * @param status
     * @param jobStatuses
     * @return
     */
    public static boolean check(Integer status, JobStatus ...jobStatuses) {
        if (status == null || !maps.containsKey(status.intValue()) || jobStatuses == null || jobStatuses.length <= 0){
            return false;
        }
        for (JobStatus jobStatus: jobStatuses){
            if (status.intValue() == jobStatus.getStatus()){
                return true;
            }
        }
        return false;
    }
}