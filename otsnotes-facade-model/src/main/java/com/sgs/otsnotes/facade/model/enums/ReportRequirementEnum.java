package com.sgs.otsnotes.facade.model.enums;

import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 1:Customer Report(PDF),2:Sub Report (Word)
 */
public enum ReportRequirementEnum {
    Customer_Report_PDF("1", "Customer Report(PDF)"),
    Sub_Report_Word("2", "Sub Report (Word)"),
    Customer_Report_Word("3", "Customer Report(Word)"),
    Test_Only("4", "Testing Only");


    private String code;
    private String message;

    ReportRequirementEnum(String code, String message) {
        this.code = code;
        this.message = message;
    }

    public String getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }

    public static String getMessage(String code) {
        if (code == null) {
            return null;
        }
        for (ReportRequirementEnum item : ReportRequirementEnum.values()) {
            if (code.equals(item.getCode())) {
                return item.getMessage();
            }
        }
        return "";
    }

    public static ReportRequirementEnum enumOf(String code) {
        for (ReportRequirementEnum reportRequirementEnum : ReportRequirementEnum.values()) {
            if (StringUtils.equals(reportRequirementEnum.getCode(),code)) {
                return reportRequirementEnum;
            }
        }
        return null;
    }

    public static List<ReportRequirementEnum> getListByOrderType(String orderType,String businessObject,String orderReportRequirement){
        List<ReportRequirementEnum> list = new ArrayList<>();
        if(!StringUtils.equalsIgnoreCase("subcontract",businessObject)){
            if(StringUtils.equalsIgnoreCase("Local",orderType) || StringUtils.equalsIgnoreCase("Oversea Payment",orderType)){
                list.add(Customer_Report_PDF);
            }else if(StringUtils.equalsIgnoreCase("IDB",orderType) || StringUtils.equalsIgnoreCase("IDN",orderType)){
                list.add(Customer_Report_PDF);
                list.add(Customer_Report_Word);
                list.add(Sub_Report_Word);
            }else{
                list.add(Customer_Report_PDF);
                list.add(Customer_Report_Word);
                list.add(Sub_Report_Word);
            }
        } else{
            ReportRequirementEnum reportRequirementEnum = enumOf(orderReportRequirement);
            if(reportRequirementEnum!=null){
                if(reportRequirementEnum==Sub_Report_Word){
                    list.add(Customer_Report_Word);
                }
                list.add(reportRequirementEnum);
            }
            if(reportRequirementEnum != Test_Only){
                list.add(Sub_Report_Word);
            }
            list.add(Test_Only);
        }
        list = list.stream().distinct().collect(Collectors.toList());
        return list;
    }


    /**
     * @param status
     * @param rrStatus
     * @return
     */
    public static boolean check(String status, ReportRequirementEnum... rrStatus) {
        if (status == null ||rrStatus==null){
            return false;
        }
        for (ReportRequirementEnum re :rrStatus) {
            if(re==null){
                continue;
            }
            if(re.code.equals(status)){
                return true;
            }
        }
        return false;
    }
    public static void main(String[] args) {
        List<ReportRequirementEnum> listByOrderType = getListByOrderType("Local", "subcontract", "1");
        System.out.println(listByOrderType);
    }
}
