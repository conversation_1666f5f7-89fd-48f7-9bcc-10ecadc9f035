package com.sgs.otsnotes.facade.model.annotation;

import com.sgs.otsnotes.facade.model.enums.OrderCopyType;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Target({ElementType.ANNOTATION_TYPE, ElementType.FIELD, ElementType.METHOD, ElementType.PARAMETER})
@Retention(RetentionPolicy.RUNTIME)
public @interface CopyEventType {
    /**
     *
     * @return
     */
    OrderCopyType[] copyType();
    /**
     *
     * @return
     *//*
    String[] bindMethods();*/

    int execSeq();
}
