package com.sgs.otsnotes.facade.model.common;

import com.sgs.framework.core.base.BaseProductLine;

/**
 * Created by <PERSON> on 2019/05/09.
 */
@Deprecated
public class CustomResult<T> extends BaseProductLine {
    /*@JsonProperty("Success")*/
    /**
     *
     */
    public CustomResult(){

    }
    /**
     *
     * @param isSuccess
     */
    public CustomResult(boolean isSuccess){
        this.success = isSuccess;
    }

    private boolean success;
    /**
     *
     */
    private T data;
    /**
     *
     */
    private boolean ignore;
    /**
     *
     */
    private int count;
    /**
     *
     */
    private String msg;
    /**
     *
     */
    private String stackTrace;

    public boolean isSuccess() {
        return success;
    }

    public void setSuccess(boolean success) {
        this.success = success;
    }

    public T getData() {
        return data;
    }

    public void setData(T data) {
        this.data = data;
    }

    public boolean isIgnore() {
        return ignore;
    }

    public void setIgnore(boolean ignore) {
        this.ignore = ignore;
    }

    public int getCount() {
        return count;
    }

    public void setCount(int count) {
        this.count = count;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public String getStackTrace() {
        return stackTrace;
    }

    public void setStackTrace(String stackTrace) {
        this.stackTrace = stackTrace;
    }

    public static <T> CustomResult newSuccessInstance(){
        CustomResult result = new CustomResult<T>();
        result.setSuccess(true);
        return result;
    }

    public CustomResult data(T data) {
        this.setData(data);
        return this;
    }

    public CustomResult fail() {
        this.setSuccess(false);
        return this;
    }

    public CustomResult fail(String message) {
        this.setMsg(message);
        this.setSuccess(false);
        return this;
    }
    public CustomResult success() {
        this.setSuccess(true);
        return this;
    }

}
