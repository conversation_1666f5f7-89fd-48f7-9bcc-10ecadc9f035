package com.sgs.otsnotes.facade.model.trims.rsp;

import com.sgs.otsnotes.facade.model.trims.TrimsSyncBaseRsp;

import java.util.List;

public class ConditionSyncRsp extends TrimsSyncBaseRsp {
    /**
     *
     */
    private List<ConditionSyncInfo> data;

    public List<ConditionSyncInfo> getData() {
        return data;
    }

    public void setData(List<ConditionSyncInfo> data) {
        this.data = data;
    }
}
