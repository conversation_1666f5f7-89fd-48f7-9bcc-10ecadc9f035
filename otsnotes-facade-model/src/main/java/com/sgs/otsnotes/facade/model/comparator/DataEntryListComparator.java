package com.sgs.otsnotes.facade.model.comparator;

import com.google.common.base.Strings;
import com.sgs.otsnotes.facade.model.enums.DataEntryType;
import com.sgs.otsnotes.facade.model.info.dataentry.DataEntryInfo;

import java.util.Comparator;

public class DataEntryListComparator implements Comparator<DataEntryInfo> {
    /**
     * 是否为升序
     */
    private boolean isAsc;
    /**
     *
     * @param isAsc
     */
    public DataEntryListComparator(boolean isAsc) {
        this.isAsc = isAsc;
    }

    private DataEntryType dataEntryType;

    public DataEntryListComparator(){
        this.dataEntryType = DataEntryType.Lab;
    }

    public DataEntryListComparator(DataEntryType dataEntryType){
        this.dataEntryType = dataEntryType;
    }
    @Override
    public int compare(DataEntryInfo d1, DataEntryInfo d2) {
        int compareTo = 0;
        switch (this.dataEntryType){
            case Lab:
                Integer labSectionSeq1 = d1.getLabSectionSeq();
                if (labSectionSeq1 == null){
                    labSectionSeq1 = 0;
                }
                Integer labSectionSeq2 = d2.getLabSectionSeq();
                if (labSectionSeq2 == null){
                    labSectionSeq2 = 0;
                }
                String code1 = d1.getLabSectionCode();
                String code2 = d2.getLabSectionCode();
                String e1 = Strings.nullToEmpty(d1.getEvaluationAlias());
                String e2 = Strings.nullToEmpty(d2.getEvaluationAlias());
                compareTo = Integer.compare(labSectionSeq1, labSectionSeq2);
                if(compareTo == 0) {//seq一致，按照code排
                    compareTo = code1.compareTo(code2);
                }
                // 如果LabSectionCode一致，就按照TestLine的EvaluationAlias排序
                if(compareTo == 0) {
                    compareTo = e1.compareTo(e2);
                }
                if(compareTo == 0) {
                    compareTo = Integer.compare(d1.getTestLineId(), d2.getTestLineId());
                }
                return compareTo;
            case Sub:
                String name1 = d1.getSubContractName();
                String name2 = d2.getSubContractName();
                String ea1 = Strings.nullToEmpty(d1.getEvaluationAlias());
                String ea2 = Strings.nullToEmpty(d2.getEvaluationAlias());
                //sub没有Sequence 先按照labName排序，如果一致，就按照testLine的EvalUationAlias排序
                if(name1 == null || name2 == null){
                    return ea1.compareTo(ea2);
                }
                if(!name1.equalsIgnoreCase(name2)) {
                    return name1.compareTo(name2);
                }
                if(compareTo == 0) {
                    compareTo = ea1.compareTo(ea2);
                }
                if(compareTo == 0) {
                    compareTo = Integer.compare(d1.getTestLineId(), d2.getTestLineId());
                }
                return compareTo;
        }
        return compareTo;
    }

    /*@Override
    public int compare(DataEntryInfo d1, DataEntryInfo d2) {
        DataEntryType dataEntryType1 = d1.getDataEntryType();
        DataEntryType dataEntryType2 = d2.getDataEntryType();
        int index = Integer.compare(dataEntryType1.getStatus(), dataEntryType2.getStatus());
        if (index < 0) {
            return isAsc ? -1 : 1;
        }
        switch (dataEntryType1){
            case Lab:
                Integer labSectionSeq1 = d1.getLabSectionSeq();
                if (labSectionSeq1 == null){
                    labSectionSeq1 = 0;
                }
                Integer labSectionSeq2 = d2.getLabSectionSeq();
                if (labSectionSeq2 == null){
                    labSectionSeq2 = 0;
                }
                index = Integer.compare(labSectionSeq1, labSectionSeq2);
                if (index < 0) {
                    return isAsc ? -1 : 1;
                }
                String code1 = Strings.nullToEmpty(d1.getLabSectionCode());
                String code2 = Strings.nullToEmpty(d2.getLabSectionCode());
                index = code1.compareTo(code2);
                if (index < 0) {
                    return isAsc ? -1 : 1;
                }
                break;
            case Sub:
                String name1 = Strings.nullToEmpty(d1.getSubContractName());
                String name2 = Strings.nullToEmpty(d2.getSubContractName());
                index = name1.compareTo(name2);
                if (index < 0) {
                    return isAsc ? -1 : 1;
                }
                break;
        }
        String ea1 = Strings.nullToEmpty(d1.getEvaluationAlias());
        String ea2 = Strings.nullToEmpty(d2.getEvaluationAlias());
        return ea1.compareTo(ea2);
    }*/
}
