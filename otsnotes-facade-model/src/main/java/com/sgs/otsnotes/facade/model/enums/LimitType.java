package com.sgs.otsnotes.facade.model.enums;

/**
 * @Author: mingyang.chen
 * @Date: 2020/12/10 14:27
 */
public enum LimitType {
    Client(0,"Client"),
    Global(1,"Global"),

    ;
    private final int type;
    private final String message;

    public static LimitType getType(String key) {
        for (LimitType item : LimitType.values()) {
            if (item.name().equals(key) || item.message.equals(key)){
                return item;
            }
        }
        return Client;
    }

    LimitType(int type, String message) {
        this.type = type;
        this.message = message;
    }

    public int getType() {
        return type;
    }

    public String getMessage() {
        return message;
    }

}
