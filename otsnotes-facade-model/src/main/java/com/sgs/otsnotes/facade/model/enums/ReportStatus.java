package com.sgs.otsnotes.facade.model.enums;

import java.util.HashMap;
import java.util.Map;

public enum ReportStatus {
    New(201, "New","Edit"),
    Cancelled(202, "Cancelled","Inactive"),
    Approved(203, "Approved","Lock"),
    Draft(204, "Draft","Edit"),
    Reworked(205, "Reworked","Inactive"),
    Combined(206, "Combined","Edit"),
    Replaced(207, "Replaced","Inactive"),
    Completed(208, "Completed","Closed"),
    Pending(209, "Pending","Block"),
    Confirmed(210, "Confirmed","Lock"),
    TypingFinished(211, "TypingFinished","Edit"),
    Reviewed(212, "Reviewed","Edit"),
    HostReviewed(213, "HostReviewed","Lock");

    private int code;
    private String message;
    private String category;

    ReportStatus(int code, String message,String category) {
        this.code = code;
        this.message = message;
        this.category = category;
    }

    public int getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }

    public String getCategory(){
        return category;
    }

    public static final Map<Integer, ReportStatus> maps = new HashMap<Integer, ReportStatus>() {
        private static final long serialVersionUID = -8986866330615001847L;
        {
            for (ReportStatus reportStatus : ReportStatus.values()) {
                put(reportStatus.getCode(), reportStatus);
            }
        }
    };

    public static ReportStatus getCode(Integer code) {
        if (code == null || !maps.containsKey(code.intValue())){
            return null;
        }
        return maps.get(code.intValue());
    }

    public boolean check(ReportStatus... reportStatus){
        if (reportStatus == null || reportStatus.length <= 0){
            return false;
        }
        for (ReportStatus status: reportStatus){
            if (this.getCode() == status.getCode()){
                return true;
            }
        }
        return false;
    }

    public static boolean checkStatus(ReportStatus status, ReportStatus... reportStatus){
        if (status == null || reportStatus == null || reportStatus.length <= 0) {
            return false;
        }
        return check(status.getCode(), reportStatus);
    }

    public static boolean check(Integer status, ReportStatus... reportStatus){
        if (status == null || !maps.containsKey(status.intValue()) || reportStatus == null || reportStatus.length <= 0) {
            return false;
        }
        for (ReportStatus reportStatu: reportStatus){
            if (status.intValue() == reportStatu.getCode()){
                return true;
            }
        }
        return false;
    }

    public static boolean checkCategory(Integer status, String... category){
        if (status == null || !maps.containsKey(status.intValue()) || category == null || category.length <= 0) {
            return false;
        }
        ReportStatus reportStatus = maps.get(status.intValue());
        for (String reportCategory: category){
            if (reportStatus.getCategory() == reportCategory){
                return true;
            }
        }
        return false;
    }

    public static ReportStatus enumOf(int code) {
        for (ReportStatus type : ReportStatus.values()) {
            if (type.getCode() == code) {
                return type;
            }
        }
        return null;
    }
}
