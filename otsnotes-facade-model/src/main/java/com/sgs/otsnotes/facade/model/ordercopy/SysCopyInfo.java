package com.sgs.otsnotes.facade.model.ordercopy;

import com.sgs.otsnotes.facade.model.annotation.MergeTransactionCallback;
import com.sgs.otsnotes.facade.model.enums.OrderCopyType;
import com.sgs.otsnotes.facade.model.info.user.UserLabBuInfo;
import com.sgs.otsnotes.facade.model.po.SubcontractRelInfoPO;
import com.sgs.otsnotes.facade.model.req.copy.SyncCustomerInfo;
import com.sgs.otsnotes.facade.model.req.copy.SyncSampleInfo;
import io.swagger.annotations.ApiModelProperty;

import java.util.*;

public class SysCopyInfo {

    public SysCopyInfo() {
        oldPpIds = new HashMap();
        oldSampleIds = new HashMap();
        oldTestLineInstanceIds = new HashMap();
        oldTestLineNewFlags = new HashMap();
        oldPpTestLineRelIds = new HashMap();
        oldPpSampleRelIds = new HashMap();
        oldTestMatrixIds = new HashMap();
        oldReportMatrixIds = new HashMap();
        oldPpBaseIds = new HashMap<>();
        oldTestConditionIds = new HashMap<>();
        testLineVersionIds = new HashMap<>();
        citationVersionIds = new HashMap<>();
        oldRelIds = new HashMap();
        oldTestPositionIds = new HashMap();
        cancelTestLineIds = new HashMap();
        oldLimitGroupIds = new HashMap();
        oldConditionGroupIds = new HashMap();
        oldTestAnalyteIds = new HashMap();
        oldTestSpecimenIds = new HashMap();
        oldTestDataIds = new HashMap();
        noCopyTestLineMaps = new HashMap<>();
        noCopyConditionTLInstanceId = new ArrayList<>();
        oldMatrixIds = new TreeSet<>();
        testLineConditionGroupIds = new HashMap<>();
        cancelSampleIds = new HashMap<>();
        reportIdMap = new HashMap<>();
        reportNoMap = new HashMap<>();
    }

    /**
     *  参照 OrderCopyType
     */
    private OrderCopyType copyType;
    /**
     *
     */
    private String orderId;
    /**
     *
     */
    private String orderNo;
    /**
     *
     */
    private String oldOrderId;
    /**
     *
     */
    private String oldOrderNo;
    /**
     *
     */
    private String reportId;
    /**
     *
     */
    private String reportNo;
    private String externalReportNo;
    /**
     *
     */
    private String oldReportId;
    /**
     *
     */
    private String oldReportNo;
    /**
     *
     */
    private Date reportDueDate;
    private Date orderDueDate;
    private Date jobDueDate;
    /**
     *
     */
    private String subContractNo;
    /**
     * AmendReport 默认按Order处理
     * true  按Report处理
     * false 按Order 处理
     */
    private Boolean amendByReport;

    private String externalOrderNo;
    private String rootExternalOrderNo;
    /**
     * 需要在Amend同时 创建的内部分包 Id
     */
    private List<String> subContractIds;
    /**
     * 创建订单时计算的subcontractExpectDueDate
     */
    private Date subcontractExpectDueDate;
    /**
     *
     */
    private UserLabBuInfo labBu;
    /**
     *
     */
    private SyncCustomerInfo customer;
    /**
     *
     */
    private List<SyncSampleInfo> samples;
    /**
     *
     */
    private Map<String, String> oldPpIds;
    /**
     *
     */
    private Map<String, String> oldSampleIds;
    /**
     *
     */
    private Map<String, String> oldOriginalSampleIds;
    /**
     * oldId，newId
     */
    private Map<String, String> oldTestLineInstanceIds;

    /**
     * oldId，newId
     */
    /**
     * oldId，是否是新增的TestLine
     */
    private Map<String, Boolean> oldTestLineNewFlags;
    private Map<String,String> reportIdMap;
    private Map<String,String> reportNoMap;
    /**
     * 新订单<TestLineVersionId, TestLineInstanceID>   LabSection 用
     */
    private Map<Integer, Set<String>> testLineVersionIdMaps;
    /**
     *
     */
    private Map<String, String> oldPpTestLineRelIds;
    /**
     *
     */
    private Map<String, String> oldPpSampleRelIds;
    /**
     * 不copy的Matrix 列表
     */
    private Map<Integer, NoCopyTestLineInfo> noCopyTestLineMaps;
    /**
     * 保存 < oldMatrixId, newMatrixID>
     */
    private Map<String, String> oldTestMatrixIds;

    /**
     * 保存 < oldReportMatrixId, newReportMatrixID>
     */
    private Map<String, String> oldReportMatrixIds;

    /**
     * 内部分包 同步时 需要Cancel的Sample
     */
    Map<String, Boolean> cancelSampleIds;
    /**
     * 只针对
     * 1、AmendReport
     * 2、SplitReport
     */
    private Set<String> oldMatrixIds;
    /**
     * 保存 < oldConditionId, newConditionId>
     */
    private Map<String, String> oldTestConditionIds;
    /**
     *
     */
    private Map<String, String> testLineConditionGroupIds;
    /**
     * 保存 <newTestLineInstanceId, newTestLineVersionId>
     */
    private Map<String, Integer> testLineVersionIds;
    /**
     * 保存 [op<newCitationVersionId, newCitationVersionId>
     */
    private Map<String, Integer> citationVersionIds;
    /**
     *
     */
    private Map<Long, Long> oldPpBaseIds;
    /**
     * OriginalRelId，
     */
    private Map<String, SubcontractRelInfoPO> oldRelIds;
    /**
     *
     */
    private Map<String, Boolean> cancelTestLineIds;;
    /**
     *
     */
    private Map<String, String> oldLimitGroupIds;
    /**
     *
     */
    private Map<String, String> oldConditionGroupIds;
    /**
     *
     */
    private Map<String, String> oldTestAnalyteIds;
    /**
     *
     */
    private Map<String, String> oldTestPositionIds;
    /**
     *
     */
    private Map<String, String> oldTestSpecimenIds;
    /**
     *
     */
    private Map<String, String> oldTestDataIds;
    /**
     * <TestLineInstanceId, Set<Aid>>
     */
    private Map<String, Set<Long>> testLineInstanceAids;
    /**
     * 准备 condition数据时，不copy的TestLine标记
     */
    private List<String> noCopyConditionTLInstanceId;

    /**
     * copy order 的 qrcodeflag
     */
    private String newOrderQrcodeFlag;

    private String sgsToken;

    private boolean extendsReportMatrix = false;
    private List<String> oldReportNoList;
    /**
     * POSL-5888 是否需要转换Sample
     */
    @ApiModelProperty("是否需要转换Sample")
    private Boolean convertOriginalSample;

    public String getSgsToken() {
        return sgsToken;
    }

    public void setSgsToken(String sgsToken) {
        this.sgsToken = sgsToken;
    }

    private MergeTransactionCallback task;

    public MergeTransactionCallback getTask() {
        return task;
    }

    public void setTask(MergeTransactionCallback task) {
        this.task = task;
    }

    public String getNewOrderQrcodeFlag() {
        return newOrderQrcodeFlag;
    }

    public void setNewOrderQrcodeFlag(String newOrderQrcodeFlag) {
        this.newOrderQrcodeFlag = newOrderQrcodeFlag;
    }

    public OrderCopyType getCopyType() {
        return copyType;
    }

    public void setCopyType(OrderCopyType copyType) {
        this.copyType = copyType;
    }

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getOldOrderId() {
        return oldOrderId;
    }

    public void setOldOrderId(String oldOrderId) {
        this.oldOrderId = oldOrderId;
    }

    public String getOldOrderNo() {
        return oldOrderNo;
    }

    public void setOldOrderNo(String oldOrderNo) {
        this.oldOrderNo = oldOrderNo;
    }

    public String getReportId() {
        return reportId;
    }

    public void setReportId(String reportId) {
        this.reportId = reportId;
    }

    public String getReportNo() {
        return reportNo;
    }

    public void setReportNo(String reportNo) {
        this.reportNo = reportNo;
    }

    public String getOldReportId() {
        return oldReportId;
    }

    public void setOldReportId(String oldReportId) {
        this.oldReportId = oldReportId;
    }

    public String getOldReportNo() {
        return oldReportNo;
    }

    public void setOldReportNo(String oldReportNo) {
        this.oldReportNo = oldReportNo;
    }

    public Date getReportDueDate() {
        return reportDueDate;
    }

    public void setReportDueDate(Date reportDueDate) {
        this.reportDueDate = reportDueDate;
    }

    public String getSubContractNo() {
        return subContractNo;
    }

    public void setSubContractNo(String subContractNo) {
        this.subContractNo = subContractNo;
    }

    public Boolean getAmendByReport() {
        return amendByReport;
    }

    public void setAmendByReport(Boolean amendByReport) {
        this.amendByReport = amendByReport;
    }

    public List<String> getSubContractIds() {
        return subContractIds;
    }

    public void setSubContractIds(List<String> subContractIds) {
        this.subContractIds = subContractIds;
    }

    public Date getSubcontractExpectDueDate() {
        return subcontractExpectDueDate;
    }

    public void setSubcontractExpectDueDate(Date subcontractExpectDueDate) {
        this.subcontractExpectDueDate = subcontractExpectDueDate;
    }

    public UserLabBuInfo getLabBu() {
        return labBu;
    }

    public void setLabBu(UserLabBuInfo labBu) {
        this.labBu = labBu;
    }

    public SyncCustomerInfo getCustomer() {
        return customer;
    }

    public void setCustomer(SyncCustomerInfo customer) {
        this.customer = customer;
    }

    public List<SyncSampleInfo> getSamples() {
        return samples;
    }

    public void setSamples(List<SyncSampleInfo> samples) {
        this.samples = samples;
    }

    public Map<String, String> getOldPpIds() {
        return oldPpIds;
    }

    public void setOldPpIds(Map<String, String> oldPpIds) {
        this.oldPpIds = oldPpIds;
    }

    public Map<String, String> getOldSampleIds() {
        return oldSampleIds;
    }

    public void setOldSampleIds(Map<String, String> oldSampleIds) {
        this.oldSampleIds = oldSampleIds;
    }

    public Map<String, String> getOldOriginalSampleIds() {
        return oldOriginalSampleIds;
    }

    public void setOldOriginalSampleIds(Map<String, String> oldOriginalSampleIds) {
        this.oldOriginalSampleIds = oldOriginalSampleIds;
    }

    public Map<String, String> getOldTestLineInstanceIds() {
        return oldTestLineInstanceIds;
    }

    public void setOldTestLineInstanceIds(Map<String, String> oldTestLineInstanceIds) {
        this.oldTestLineInstanceIds = oldTestLineInstanceIds;
    }

    public Map<String, Boolean> getOldTestLineNewFlags() {
        return oldTestLineNewFlags;
    }

    public void setOldTestLineNewFlags(Map<String, Boolean> oldTestLineNewFlags) {
        this.oldTestLineNewFlags = oldTestLineNewFlags;
    }

    public Map<Integer, Set<String>> getTestLineVersionIdMaps() {
        return testLineVersionIdMaps;
    }

    public void setTestLineVersionIdMaps(Map<Integer, Set<String>> testLineVersionIdMaps) {
        this.testLineVersionIdMaps = testLineVersionIdMaps;
    }

    public Map<String, String> getOldPpTestLineRelIds() {
        return oldPpTestLineRelIds;
    }

    public void setOldPpTestLineRelIds(Map<String, String> oldPpTestLineRelIds) {
        this.oldPpTestLineRelIds = oldPpTestLineRelIds;
    }

    public Map<String, String> getOldPpSampleRelIds() {
        return oldPpSampleRelIds;
    }

    public void setOldPpSampleRelIds(Map<String, String> oldPpSampleRelIds) {
        this.oldPpSampleRelIds = oldPpSampleRelIds;
    }

    public Map<Integer, NoCopyTestLineInfo> getNoCopyTestLineMaps() {
        return noCopyTestLineMaps;
    }

    public void setNoCopyTestLineMaps(Map<Integer, NoCopyTestLineInfo> noCopyTestLineMaps) {
        this.noCopyTestLineMaps = noCopyTestLineMaps;
    }

    public Map<String, String> getOldTestMatrixIds() {
        return oldTestMatrixIds;
    }

    public void setOldTestMatrixIds(Map<String, String> oldTestMatrixIds) {
        this.oldTestMatrixIds = oldTestMatrixIds;
    }

    public Map<String, String> getOldReportMatrixIds() {
        return oldReportMatrixIds;
    }

    public void setOldReportMatrixIds(Map<String, String> oldReportMatrixIds) {
        this.oldReportMatrixIds = oldReportMatrixIds;
    }

    public Map<String, Boolean> getCancelSampleIds() {
        return cancelSampleIds;
    }

    public void setCancelSampleIds(Map<String, Boolean> cancelSampleIds) {
        this.cancelSampleIds = cancelSampleIds;
    }

    public Set<String> getOldMatrixIds() {
        return oldMatrixIds;
    }

    public void setOldMatrixIds(Set<String> oldMatrixIds) {
        this.oldMatrixIds = oldMatrixIds;
    }

    public Map<String, String> getOldTestConditionIds() {
        return oldTestConditionIds;
    }

    public void setOldTestConditionIds(Map<String, String> oldTestConditionIds) {
        this.oldTestConditionIds = oldTestConditionIds;
    }

    public Map<String, String> getTestLineConditionGroupIds() {
        return testLineConditionGroupIds;
    }

    public void setTestLineConditionGroupIds(Map<String, String> testLineConditionGroupIds) {
        this.testLineConditionGroupIds = testLineConditionGroupIds;
    }

    public Map<String, Integer> getTestLineVersionIds() {
        return testLineVersionIds;
    }

    public void setTestLineVersionIds(Map<String, Integer> testLineVersionIds) {
        this.testLineVersionIds = testLineVersionIds;
    }

    public Map<String, Integer> getCitationVersionIds() {
        return citationVersionIds;
    }

    public void setCitationVersionIds(Map<String, Integer> citationVersionIds) {
        this.citationVersionIds = citationVersionIds;
    }

    public Map<Long, Long> getOldPpBaseIds() {
        return oldPpBaseIds;
    }

    public void setOldPpBaseIds(Map<Long, Long> oldPpBaseIds) {
        this.oldPpBaseIds = oldPpBaseIds;
    }

    public Map<String, SubcontractRelInfoPO> getOldRelIds() {
        return oldRelIds;
    }

    public void setOldRelIds(Map<String, SubcontractRelInfoPO> oldRelIds) {
        this.oldRelIds = oldRelIds;
    }

    public Map<String, Boolean> getCancelTestLineIds() {
        return cancelTestLineIds;
    }

    public void setCancelTestLineIds(Map<String, Boolean> cancelTestLineIds) {
        this.cancelTestLineIds = cancelTestLineIds;
    }

    public Map<String, String> getOldLimitGroupIds() {
        return oldLimitGroupIds;
    }

    public void setOldLimitGroupIds(Map<String, String> oldLimitGroupIds) {
        this.oldLimitGroupIds = oldLimitGroupIds;
    }

    public Map<String, String> getOldConditionGroupIds() {
        return oldConditionGroupIds;
    }

    public void setOldConditionGroupIds(Map<String, String> oldConditionGroupIds) {
        this.oldConditionGroupIds = oldConditionGroupIds;
    }

    public Map<String, String> getOldTestAnalyteIds() {
        return oldTestAnalyteIds;
    }

    public void setOldTestAnalyteIds(Map<String, String> oldTestAnalyteIds) {
        this.oldTestAnalyteIds = oldTestAnalyteIds;
    }

    public Map<String, String> getOldTestPositionIds() {
        return oldTestPositionIds;
    }

    public void setOldTestPositionIds(Map<String, String> oldTestPositionIds) {
        this.oldTestPositionIds = oldTestPositionIds;
    }

    public Map<String, String> getOldTestSpecimenIds() {
        return oldTestSpecimenIds;
    }

    public void setOldTestSpecimenIds(Map<String, String> oldTestSpecimenIds) {
        this.oldTestSpecimenIds = oldTestSpecimenIds;
    }

    public Map<String, String> getOldTestDataIds() {
        return oldTestDataIds;
    }

    public Map<String, Set<Long>> getTestLineInstanceAids() {
        return testLineInstanceAids;
    }

    public void setTestLineInstanceAids(Map<String, Set<Long>> testLineInstanceAids) {
        this.testLineInstanceAids = testLineInstanceAids;
    }

    public void setOldTestDataIds(Map<String, String> oldTestDataIds) {
        this.oldTestDataIds = oldTestDataIds;
    }

    public List<String> getNoCopyConditionTLInstanceId() {
        return noCopyConditionTLInstanceId;
    }

    public void setNoCopyConditionTLInstanceId(List<String> noCopyConditionTLInstanceId) {
        this.noCopyConditionTLInstanceId = noCopyConditionTLInstanceId;
    }

    public String getExternalOrderNo() {
        return externalOrderNo;
    }

    public void setExternalOrderNo(String externalOrderNo) {
        this.externalOrderNo = externalOrderNo;
    }

    public String getRootExternalOrderNo() {
        return rootExternalOrderNo;
    }

    public void setRootExternalOrderNo(String rootExternalOrderNo) {
        this.rootExternalOrderNo = rootExternalOrderNo;
    }

    public String getExternalReportNo() {
        return externalReportNo;
    }

    public void setExternalReportNo(String externalReportNo) {
        this.externalReportNo = externalReportNo;
    }

    public Date getOrderDueDate() {
        return orderDueDate;
    }

    public void setOrderDueDate(Date orderDueDate) {
        this.orderDueDate = orderDueDate;
    }

    public Map<String, String> getReportIdMap() {
        return reportIdMap;
    }

    public void setReportIdMap(Map<String, String> reportIdMap) {
        this.reportIdMap = reportIdMap;
    }

    public Map<String, String> getReportNoMap() {
        return reportNoMap;
    }

    public void setReportNoMap(Map<String, String> reportNoMap) {
        this.reportNoMap = reportNoMap;
    }

    public Date getJobDueDate() {
        return jobDueDate;
    }

    public void setJobDueDate(Date jobDueDate) {
        this.jobDueDate = jobDueDate;
    }

    public Boolean getConvertOriginalSample() {
        return convertOriginalSample;
    }

    public void setConvertOriginalSample(Boolean convertOriginalSample) {
        this.convertOriginalSample = convertOriginalSample;
    }

    public boolean isExtendsReportMatrix() {
        return extendsReportMatrix;
    }

    public void setExtendsReportMatrix(boolean extendsReportMatrix) {
        this.extendsReportMatrix = extendsReportMatrix;
    }

    public List<String> getOldReportNoList() {
        return oldReportNoList;
    }

    public void setOldReportNoList(List<String> oldReportNoList) {
        this.oldReportNoList = oldReportNoList;
    }
}
