package com.sgs.otsnotes.facade.model.enums;

import java.util.HashMap;
import java.util.Map;

public enum SubContractLabCodeEnum {
    HKCCL("HK CCL", "HK CCL"),
    GZCCL("GZ CCL", "GZ CCL"),
    SHCCL("SH CCL", "SH CCL"),
    NJCCL("NJ CCL", "NJ CCL");

    private String code;
    private String msg;

    SubContractLabCodeEnum(String code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public String getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }

    public static final Map<String, SubContractLabCodeEnum> maps = new HashMap<String, SubContractLabCodeEnum>() {
        private static final long serialVersionUID = -8986866330615001847L;
        {
            for (SubContractLabCodeEnum enu : SubContractLabCodeEnum.values()) {
                put(enu.getCode().toUpperCase(), enu);
            }
        }
    };

    public static SubContractLabCodeEnum findType(String code) {
        if (code == null) {
            return null;
        }
        return maps.get(code.toUpperCase());
    }

    /**
     *
     * @param code
     * @param enums
     * @return
     */
    public static boolean check(String code, SubContractLabCodeEnum... enums) {
        if (code == null || enums == null || enums.length <= 0){
            return false;
        }
        SubContractLabCodeEnum subContractLabCodeEnum = maps.get(code.toUpperCase());
        if (subContractLabCodeEnum == null){
            return false;
        }
        for (SubContractLabCodeEnum enu: enums) {
            if (enu == subContractLabCodeEnum){
                return true;
            }
        }
        return false;
    }
}
