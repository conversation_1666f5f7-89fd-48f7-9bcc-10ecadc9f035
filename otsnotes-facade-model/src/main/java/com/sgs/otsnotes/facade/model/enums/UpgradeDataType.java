package com.sgs.otsnotes.facade.model.enums;

import java.util.HashMap;
import java.util.Map;

public enum UpgradeDataType {
    None(0, "None"),
    TestLine(1, "TestLine"),
    ProtocolPackage(2, "ProtocolPackage"),
    UsageType(3, "UsageType"),
    ProductAttr(4, "ProductAttr"),
    TestStandard(5, "TestStandard"),
    Regulation(6, "Regulation"),
    TestCondition(7, "Regulation"),
    LabSection(8, "LabSection");

    private int status;
    private String code;

    UpgradeDataType(int status, String code) {
        this.status = status;
        this.code = code;
    }

    public String getCode() {
        return code;
    }

    public int getStatus() {
        return status;
    }

    static Map<Integer, UpgradeDataType> maps = new HashMap<>();
    static Map<String, UpgradeDataType> codeMaps = new HashMap<>();

    static {
        for (UpgradeDataType dataType : UpgradeDataType.values()) {
            maps.put(dataType.getStatus(), dataType);
            codeMaps.put(dataType.getCode(), dataType);
        }
    };

    public static UpgradeDataType getStatus(Integer status) {
        if (status == null || !maps.containsKey(status)) {
            return null;
        }
        return maps.get(status);
    }

    /**
     *
     * @param status
     * @param dataTypes
     * @return
     */
    public static boolean check(Integer status, UpgradeDataType... dataTypes) {
        if (status == null || !maps.containsKey(status.intValue()) || dataTypes == null || dataTypes.length <= 0){
            return false;
        }
        for (UpgradeDataType dataType: dataTypes){
            if (status.intValue() == dataType.getStatus()){
                return true;
            }
        }
        return false;
    }

    /**
     *
     * @param dataTypes
     * @return
     */
    public boolean check(UpgradeDataType... dataTypes){
        if (dataTypes == null || dataTypes.length <= 0){
            return false;
        }
        for (UpgradeDataType dataType: dataTypes){
            if (this.getStatus() == dataType.getStatus()){
                return true;
            }
        }
        return false;
    }

}
