package com.sgs.otsnotes.facade.model.enums;

import java.util.HashMap;
import java.util.Map;

public enum TableType {
    None(0, "None"),
    Order(1, "tb_general_order_instance"),//
    Report(2, "tb_report"),
    Sample(3, "tb_test_sample"),
    SampleGroup(4, "tb_test_sample_group"),
    TestLine(5, "tb_test_line_instance"),
    PP(6, "tb_pp_instance"),
    PPTestLineRel(7, "tre_pp_test_line_relationship"),
    PPConditionRel(8, "tre_pp_condition_relationship"),
    PPSampleRel(9, "tre_pp_sample_relationship"),
    TestCondition(10, "tb_test_condition_instance"),
    TestConditionGroup(11, "tb_test_condition_group"),
    Analyte(12, "tb_analyte_instance"),
    Matrix(13, "tb_test_matrix"),
    LabSection(14, "tb_lab_section_instance"),
    Limit(15, "tb_limit_instance"),
    LimitGroup(16, "tb_limit_group_instance"),
    ProductAttr(17, "tb_product_attribute_instance"),
    SubContractTestLineMapping(18, "tb_sub_contract"),
    TestPosition(19, "tb_test_position"),
    TestSpecimen(20, "tb_test_specimen"),
    TestData(21, "tb_test_data"),
    ReportMatrixRel(22, "tre_report_matrix_relationship"),
    Conclusion(23, "tb_conclusion"),
    CrossLab(24, "tb_order_cross_lab_rel"),

    PPLanguage(25, "tb_pp_instance_multiplelanguage"),
    PPTestLineRelLanguage(26, "tre_pp_test_line_relationship_multiplelanguage"),
    PPConditionRelLanguage(27, "tre_pp_condition_relationship_multiplelanguage"),
    TestLineLanguage(28, "tb_test_line_instance_multiplelanguage"),

    ConditionLanguage(29, "tb_test_condition_instance_multiplelanguage"),
    ConditionGroupLanguage(30, "tb_test_condition_group_multiplelanguage"),
    AnalyteLanguage(31, "tb_analyte_instance_multiplelanguage"),
    LimitLanguage(32, "tb_limit_instance_multiplelanguage");

    private final int tableId;
    private final String tableName;

    TableType(int tableId, String tableName) {
        this.tableId = tableId;
        this.tableName = tableName;
    }

    public int getTableId() {
        return tableId;
    }

    public String getTableName() {
        return tableName;
    }

    public static final Map<Integer, TableType> maps = new HashMap<Integer, TableType>() {
        private static final long serialVersionUID = -8986866330615001847L;
        {
            for (TableType type : TableType.values()) {
                put(type.getTableId(), type);
            }
        }
    };

    /**
     *
     * @param tableId
     * @param tableTypes
     * @return
     */
    public static boolean check(Integer tableId, TableType... tableTypes) {
        if (tableId == null || !maps.containsKey(tableId.intValue()) || tableTypes == null || tableTypes.length <= 0){
            return false;
        }
        for (TableType tableType: tableTypes){
            if (maps.get(tableId.intValue()) == tableType){
                return true;
            }
        }
        return false;
    }

    /**
     *
     * @param tableType
     * @return
     */
    public static TableType getTableType(Integer tableType) {
        if (tableType == null || !maps.containsKey(tableType.intValue())) {
            return null;
        }
        return maps.get(tableType.intValue());
    }
}
