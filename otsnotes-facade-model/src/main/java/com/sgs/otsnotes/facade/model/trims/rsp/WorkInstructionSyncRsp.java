package com.sgs.otsnotes.facade.model.trims.rsp;

public class WorkInstructionSyncRsp {
    /**
     *
     */
    private Integer workingInstructionCategoryId;
    /**
     *
     */
    private String workingInstructionText;
    /**
     *
     */
    private String workingInstructionCategoryName;

    public Integer getWorkingInstructionCategoryId() {
        return workingInstructionCategoryId;
    }

    public void setWorkingInstructionCategoryId(Integer workingInstructionCategoryId) {
        this.workingInstructionCategoryId = workingInstructionCategoryId;
    }

    public String getWorkingInstructionText() {
        return workingInstructionText;
    }

    public void setWorkingInstructionText(String workingInstructionText) {
        this.workingInstructionText = workingInstructionText;
    }

    public String getWorkingInstructionCategoryName() {
        return workingInstructionCategoryName;
    }

    public void setWorkingInstructionCategoryName(String workingInstructionCategoryName) {
        this.workingInstructionCategoryName = workingInstructionCategoryName;
    }
}
