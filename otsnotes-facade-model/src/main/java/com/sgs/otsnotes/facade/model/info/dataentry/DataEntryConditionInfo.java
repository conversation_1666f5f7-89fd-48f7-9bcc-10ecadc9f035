package com.sgs.otsnotes.facade.model.info.dataentry;

import com.sgs.framework.core.base.BaseProductLine;

import java.util.Date;

public class DataEntryConditionInfo extends BaseProductLine {
    /**
     * conditionGroupID
     */
    private String conditionGroupID;
    /**
     * footNotes
     */
    private String footNotes;

    /**
     * ModifiedDate TIMESTAMP(19)<br>
     * ModifiedDate
     */
    private Date modifiedDate;

    /**
     * ModifiedBy VARCHAR(50)<br>
     * ModifiedBy
     */
    private String modifiedBy;

    public String getConditionGroupID() {
        return conditionGroupID;
    }

    public void setConditionGroupID(String conditionGroupID) {
        this.conditionGroupID = conditionGroupID;
    }

    public String getFootNotes() {
        return footNotes;
    }

    public void setFootNotes(String footNotes) {
        this.footNotes = footNotes;
    }

    public Date getModifiedDate() {
        return modifiedDate;
    }

    public void setModifiedDate(Date modifiedDate) {
        this.modifiedDate = modifiedDate;
    }

    public String getModifiedBy() {
        return modifiedBy;
    }

    public void setModifiedBy(String modifiedBy) {
        this.modifiedBy = modifiedBy;
    }
}
