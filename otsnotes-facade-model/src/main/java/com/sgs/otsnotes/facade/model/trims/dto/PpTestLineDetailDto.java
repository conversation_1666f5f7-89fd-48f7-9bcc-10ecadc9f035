//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//

package com.sgs.otsnotes.facade.model.trims.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.sgs.framework.core.base.BaseProductLine;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@ApiModel(
        value = "PpTestLineDetailDto",
        description = "PpTestLineDetailDto"
)
@Data
public class PpTestLineDetailDto extends BaseProductLine {
    @ApiModelProperty(
            notes = "只有Pp过来的TestLine才有PpBaseId(tb_trims_pp_baseinfo.Id)"
    )
    private Long ppBaseId;
    @ApiModelProperty(
            notes = "只有Pp过来的TestLine才有PpNo(tb_trims_pp_baseinfo.PpNo)"
    )
    private Integer ppNo;
    @ApiModelProperty(
            notes = " PP的别名 (tb_trims_pp_baseinfo.ppName)"
    )
    private String ppName;
    @ApiModelProperty(
            notes = "只有Pp过来的TestLine才有PpName(tb_trims_pp_baseinfo.ppType)  对应 Trims字段：CSPP   0:Pp    1:CSPP"
    )
    private String ppType;
    @ApiModelProperty(
            notes = "(tb_trims_testline_baseinfo.productLineAbbr)  "
    )
    private String productLineAbbr;
    @ApiModelProperty(
            required = true,
            notes = "PP 的版本Id (tb_trims_pp_baseinfo.PpVersionId)"
    )
    private Integer ppVersionId;
    @ApiModelProperty(
            notes = "当PP是SubPP时，该值取RootPpBaseId,否则取他自己的PP为RootPpBaseId"
    )
    @JsonInclude(Include.NON_DEFAULT)
    private Long rootPpBaseId;
    @ApiModelProperty(
            notes = "对应 Trims 的AId"
    )
    private Integer aid;
    @ApiModelProperty(
            required = true,
            notes = " tb_trims_testline_baseinfo.Id"
    )
    private Long testLineBaseId;
    @ApiModelProperty(
            required = true,
            notes = "测试Id tb_trims_testline_baseinfo.TestLineId"
    )
    private Integer testLineId;
    @ApiModelProperty(
            required = true,
            notes = "TestLine别名 "
    )
    @JsonInclude(Include.NON_DEFAULT)
    private String evaluationAlias;
    @ApiModelProperty(
            required = true,
            notes = "TestLine 版本"
    )
    private Integer testLineVersionId;
    @ApiModelProperty(
            required = true,
            notes = "tb_trims_testline_baseinfo.TestItemVersionId"
    )
    private Integer testItemVersionId;
    @ApiModelProperty(
            required = true,
            notes = "tb_trims_artifact_citation_relationship.Id"
    )
    private Long artifactBaseId;
    @ApiModelProperty(
            required = true,
            notes = "0：Add TL、1：Add PP"
    )
    private int artifactType;
    @ApiModelProperty(
            required = true,
            notes = "tb_trims_artifact_citation_relationship.Id"
    )
    private Long citationBaseId;
    @ApiModelProperty(
            required = true,
            notes = "tb_trims_artifact_citation_relationship.CitationId"
    )
    private Integer citationId;
    @ApiModelProperty(
            required = true,
            notes = "标准名称 tb_trims_artifact_citation_relationship.citationName"
    )
    @JsonInclude(Include.NON_DEFAULT)
    private String citationName;
    @JsonIgnore
    private Integer citationType;
    @ApiModelProperty(
            required = true,
            notes = "标准Section 名称 tb_trims_artifact_citation_relationship.citationSectionName"
    )
    @JsonInclude(Include.NON_DEFAULT)
    private String citationSectionName;
    @ApiModelProperty(
            required = true,
            notes = "tb_trims_artifact_citation_relationship.ppNotes"
    )
    @JsonInclude(Include.NON_DEFAULT)
    private String ppNotes;
    @ApiModelProperty(
            required = true,
            notes = "tb_trims_artifact_citation_relationship.sectionName"
    )
    @JsonInclude(Include.NON_DEFAULT)
    private String sectionName;
    @ApiModelProperty(
            required = true,
            notes = "tb_trims_artifact_citation_relationship.CitationVersionId"
    )
    @JsonInclude(Include.NON_DEFAULT)
    private Integer citationVersionId;
    @ApiModelProperty(
            required = true,
            notes = "tb_trims_labsection_baseinfo.LabSectionName"
    )
    @JsonInclude(Include.NON_DEFAULT)
    private String labSectionName;
    @ApiModelProperty(
            required = true,
            notes = "tb_trims_labsection_baseinfo.LabSectionId"
    )
    @JsonInclude(Include.NON_DEFAULT)
    private Integer labSectionId;
    @ApiModelProperty(
            required = true,
            notes = "labSection相关信息 labSection Base Id"
    )
    @JsonInclude(Include.NON_DEFAULT)
    private Long labSectionBaseId;
    @ApiModelProperty(
            required = true,
            notes = "labSection相关信息"
    )
    @JsonInclude(Include.NON_DEFAULT)
    private List<TestLineLabSectionDto> labSections;
    @ApiModelProperty(
            required = true,
            notes = "tb_trims_pp_section_baseinfo.Id"
    )
    @JsonInclude(Include.NON_DEFAULT)
    private Long sectionBaseId;
    @ApiModelProperty(
            notes = "在Show All TL列表里展示，取值tb_trims_pp_section_baseinfo.SectionLevel"
    )
    @JsonInclude(Include.NON_DEFAULT)
    private String sectionLevel;
    @ApiModelProperty(
            notes = "TlExecutionClassificationCode(tre_trims_pp_artifact_relationship.TlExecutionClassificationCode)"
    )
    @JsonInclude(Include.NON_DEFAULT)
    private Integer tlExecutionClassificationCode;
    @ApiModelProperty(
            notes = "TlExecutionClassificationCode(tre_trims_pp_artifact_relationship.CitationPpStandardSection)"
    )
    @JsonInclude(Include.NON_DEFAULT)
    @JsonIgnore
    private String citationPpStandardSection;
    @ApiModelProperty(
            notes = "排序 TestLineSeq(tre_trims_pp_artifact_relationship.TestLineSeq)"
    )
    @JsonInclude(Include.NON_DEFAULT)
    private Integer testLineSeq;
    @ApiModelProperty(
            notes = "SL = citationname + citationSectionname ,MR = PpCitationName + citationPpStandardSection ||  + '&' + citationName + 空格 + citationSectionName "
    )
    private String citationFullName;
    @ApiModelProperty(
            required = true,
            notes = " 返回 包括多语言"
    )
    @JsonInclude(Include.NON_DEFAULT)
    private List<PpTestLineLangRsp> languages;
    @ApiModelProperty(
            notes = "testLine类型 默认0; 1(testItemName等于Pretreatment); 2(testItemName等于OOB Test)"
    )
    private Integer testLineType;
    @ApiModelProperty(
            notes = "testLineStatus  状态 0：Disable、1：Active、2：PhaseOut、3：Inactive"
    )
    private Integer testLineStatus;
    @ApiModelProperty(
            notes = "ppTestLine AF属性"
    )
    @JsonInclude(Include.NON_DEFAULT)
    private List<TrimsApplicationFactorDto> applicationFactors;
    @ApiModelProperty(
            notes = "citationSectionId"
    )
    @JsonInclude(Include.NON_DEFAULT)
    private Integer citationSectionId;



    @ApiModelProperty(
            notes = "客户白名单标志：0无白名单控制，1白名单，2不在白名单"
    )
    private Integer  customerTestlineFlag;
}
