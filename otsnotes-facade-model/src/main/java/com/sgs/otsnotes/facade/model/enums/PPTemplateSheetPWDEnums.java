package com.sgs.otsnotes.facade.model.enums;

/**
 * @ClassName PPTemplateSheetPWDEnums
 * @Description TODO
 * <AUTHOR>
 * @Date 8/14/2020
 */
public enum  PPTemplateSheetPWDEnums {
    data_sheet("dataSheet"),
    sample_sheet("sampleSheet");
    String pwd;
    PPTemplateSheetPWDEnums(String pwd){
        this.pwd = pwd;
    }

    public String getPwd() {
        return pwd;
    }

    public static boolean check(String pwd,PPTemplateSheetPWDEnums enums){
        return  enums.getPwd().equals(pwd);
    }
}
