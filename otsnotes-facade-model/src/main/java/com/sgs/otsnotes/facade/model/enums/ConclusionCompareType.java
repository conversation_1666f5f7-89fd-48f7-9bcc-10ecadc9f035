package com.sgs.otsnotes.facade.model.enums;

import java.util.HashMap;
import java.util.Map;

public enum  ConclusionCompareType {
    None(0,"None"),
    IdCompareObjectId(1,"Id To ObjectId"),
    ColumnIdCompareObjectId(2,"ColumnId To ObjectId"),
    IdCompareTestLineInstanceId(3,"Id To TestLineInstanceId");

    private int type;
    private String name;

    ConclusionCompareType(int type, String name) {
        this.type = type;
        this.name = name;
    }

    public int getType() {
        return type;
    }

    public String getName() {
        return name;
    }

    public static final Map<Integer, ConclusionCompareType> maps = new HashMap<Integer, ConclusionCompareType>() {
        private static final long serialVersionUID = -8986866330615001847L;
        {
            for (ConclusionCompareType compareType : ConclusionCompareType.values()) {
                put(compareType.getType(), compareType);
            }
        }
    };

}
