package com.sgs.otsnotes.facade.model.enums;

import java.util.HashMap;
import java.util.Map;

/**
 * @ClassName AnalyteSectionTypeEnums
 * @Description Analyte selection type 枚举
 * <AUTHOR>
 * @Date 1/14/2021
 */
public enum  AnalyteSectionTypeEnums {

    None(0,"None"),
    /**
     * 必选
     * */
    Mandatory(1,"UnSelected"),
    /**
     * 可选*/
    Preselection(2,"No need selection");

    private Integer type;
    private String name;
    AnalyteSectionTypeEnums(Integer type,String name){
        this.type = type;
        this.name = name;
    }

    public Integer getType() {
        return type;
    }
    public String getName() {
        return name;
    }

    static Map<Integer, AnalyteSectionTypeEnums> typeMaps = new HashMap();
    static {
        for (AnalyteSectionTypeEnums analyteSectionType : AnalyteSectionTypeEnums.values()) {
            typeMaps.put(analyteSectionType.getType(), analyteSectionType);
        }
    }

    public static AnalyteSectionTypeEnums findType(Integer type) {
        if (type == null || !typeMaps.containsKey(type.intValue())) {
            return null;
        }
        return typeMaps.get(type.intValue());
    }

    public static boolean check(Integer type, AnalyteSectionTypeEnums...enums){
        if(type==null){
            return false;
        }
        for (AnalyteSectionTypeEnums anEnum : enums) {
            if(anEnum.type.compareTo(type)==0){
                return true;
            }
        }

        return false;
    }
}
