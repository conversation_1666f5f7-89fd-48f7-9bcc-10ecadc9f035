package com.sgs.otsnotes.facade.model.rsp;

/**
 * @ClassName MatrixTableListRsp
 * @Description MatrixTable 组件返回数据对象 中，对sample的处理
 * matrix 存在cancel一说，如果cancel 对应sample需要标红 删除线的
 * <AUTHOR>
 * @Date 8/28/2020
 */
public class MatrixTableSampleNoListRsp {
    private String sampleNo;
    private Boolean active;


    public String getSampleNo() {
        return sampleNo;
    }

    public void setSampleNo(String sampleNo) {
        this.sampleNo = sampleNo;
    }

    public Boolean getActive() {
        return active;
    }

    public void setActive(Boolean active) {
        this.active = active;
    }
}
