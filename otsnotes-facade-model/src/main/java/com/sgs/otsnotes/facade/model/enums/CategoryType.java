package com.sgs.otsnotes.facade.model.enums;


/**
 * @Author: mingyang.chen
 * @Date: 2021/1/15 17:52
 */
public enum CategoryType {
    Original("Original","O"),Chem("Chem","C"),<PERSON>y("<PERSON>y","P")
    ;
    private final String name;
    private final String code;

    CategoryType(String name, String code) {
        this.name = name;
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public String getCode() {
        return code;
    }

}
