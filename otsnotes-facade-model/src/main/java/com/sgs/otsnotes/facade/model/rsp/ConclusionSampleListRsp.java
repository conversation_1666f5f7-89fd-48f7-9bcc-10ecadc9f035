package com.sgs.otsnotes.facade.model.rsp;

import com.fasterxml.jackson.annotation.JsonIgnore;

public class ConclusionSampleListRsp {

	@JsonIgnore
	private String id;
	@JsonIgnore
	private Integer ppId;
	@JsonIgnore
	private Integer ppNo;
	private Integer calculateConclusionFlag;
	private Integer testLineId;
	@JsonIgnore
	private String evaluationAlias;
	@JsonIgnore
	private String generalOrderInstanceId;
	@JsonIgnore
	private String testLineEvaluation;

	@JsonIgnore
	private String testLineInstanceId;
	private String sampleNo;
	private String sampleId;
	@JsonIgnore
	private String ppSampleRelId;
	@JsonIgnore
	private String concludionId;
	@JsonIgnore
	private String concludionInsId;
	@JsonIgnore
	private String conclusionSettingID;
	@JsonIgnore
	private String sampleType;
	private String conclusionDescription;
	private String conclusionRemark;
	@JsonIgnore
	private String description;
	private String className;

	// 前端未使用，用到排序
	@JsonIgnore
	private String subContractLabName;
	@JsonIgnore
	private Integer labSectionSeq;
	@JsonIgnore
	private String labSectionName;

	private String mid;

	public String getMid() {
		return mid;
	}

	public void setMid(String mid) {
		this.mid = mid;
	}

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public Integer getCalculateConclusionFlag() {
		return calculateConclusionFlag;
	}

	public void setCalculateConclusionFlag(Integer calculateConclusionFlag) {
		this.calculateConclusionFlag = calculateConclusionFlag;
	}

	public String getEvaluationAlias() {
		return evaluationAlias;
	}

	public void setEvaluationAlias(String evaluationAlias) {
		this.evaluationAlias = evaluationAlias;
	}

	public String getTestLineEvaluation() {
		return testLineEvaluation;
	}

	public void setTestLineEvaluation(String testLineEvaluation) {
		this.testLineEvaluation = testLineEvaluation;
	}

	public String getSampleNo() {
		return sampleNo;
	}

	public void setSampleNo(String sampleNo) {
		this.sampleNo = sampleNo;
	}

	public String getConclusionDescription() {
		return conclusionDescription;
	}

	public void setConclusionDescription(String conclusionDescription) {
		this.conclusionDescription = conclusionDescription;
	}

	public String getSubContractLabName() {
		return subContractLabName;
	}

	public void setSubContractLabName(String subContractLabName) {
		this.subContractLabName = subContractLabName;
	}

	public Integer getPpId() {
		return ppId;
	}

	public void setPpId(Integer ppId) {
		this.ppId = ppId;
	}

	public Integer getPpNo() {
		return ppNo;
	}

	public void setPpNo(Integer ppNo) {
		this.ppNo = ppNo;
	}

	public String getDescription() {
		return description;
	}

	public void setDescription(String description) {
		this.description = description;
	}

	public Integer getTestLineId() {
		return testLineId;
	}

	public void setTestLineId(Integer testLineId) {
		this.testLineId = testLineId;
	}

	public String getGeneralOrderInstanceId() {
		return generalOrderInstanceId;
	}

	public void setGeneralOrderInstanceId(String generalOrderInstanceId) {
		this.generalOrderInstanceId = generalOrderInstanceId;
	}

	public String getTestLineInstanceId() {
		return testLineInstanceId;
	}

	public void setTestLineInstanceId(String testLineInstanceId) {
		this.testLineInstanceId = testLineInstanceId;
	}

	public String getSampleId() {
		return sampleId;
	}

	public void setSampleId(String sampleId) {
		this.sampleId = sampleId;
	}

	public String getPpSampleRelId() {
		return ppSampleRelId;
	}

	public void setPpSampleRelId(String ppSampleRelId) {
		this.ppSampleRelId = ppSampleRelId;
	}

	public String getConclusionRemark() {
		return conclusionRemark;
	}

	public void setConclusionRemark(String conclusionRemark) {
		this.conclusionRemark = conclusionRemark;
	}

	public Integer getLabSectionSeq() {
		return labSectionSeq;
	}

	public void setLabSectionSeq(Integer labSectionSeq) {
		this.labSectionSeq = labSectionSeq;
	}

	public String getLabSectionName() {
		return labSectionName;
	}

	public void setLabSectionName(String labSectionName) {
		this.labSectionName = labSectionName;
	}

	public String getClassName() {
		return className;
	}

	public void setClassName(String className) {
		this.className = className;
	}

	public String getConcludionId() {
		return concludionId;
	}

	public void setConcludionId(String concludionId) {
		this.concludionId = concludionId;
	}

	public String getConclusionSettingID() {
		return conclusionSettingID;
	}

	public void setConclusionSettingID(String conclusionSettingID) {
		this.conclusionSettingID = conclusionSettingID;
	}

	public String getConcludionInsId() {
		return concludionInsId;
	}

	public void setConcludionInsId(String concludionInsId) {
		this.concludionInsId = concludionInsId;
	}

	public String getSampleType() {
		return sampleType;
	}

	public void setSampleType(String sampleType) {
		this.sampleType = sampleType;
	}
}
