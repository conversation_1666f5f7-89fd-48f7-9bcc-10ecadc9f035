package com.sgs.otsnotes.facade.model.enums;

import java.util.HashMap;
import java.util.Map;

public enum PriorityLevel {
    Fail(10, "Fail"),
    DataOnly(20, "Data Only"),
    Inconclusive(30, "Inconclusive"),
    Pass(40, "Pass"),
    Exempt(50, "Exempt"),
    NA(60, "NA");

    private final int level;
    private final String message;

    PriorityLevel(int level, String message) {
        this.level = level;
        this.message = message;
    }

    public int getLevel() {
        return level;
    }

    public String getMessage() {
        return message;
    }

    public static final Map<Integer, PriorityLevel> maps = new HashMap<Integer, PriorityLevel>() {
        private static final long serialVersionUID = -8986866330615001847L;
        {
            for (PriorityLevel enu : PriorityLevel.values()) {
                put(enu.getLevel(), enu);
            }
        }
    };

    public static PriorityLevel getLevel(Integer code) {
        if (code == null || !maps.containsKey(code.intValue())) {
            return null;
        }
        return maps.get(code.intValue());
    }
}
