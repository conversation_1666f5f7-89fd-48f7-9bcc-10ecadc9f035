package com.sgs.otsnotes.facade.model.enums;

import java.util.HashMap;
import java.util.Map;

public enum UsageTypeEnum {
	General(0, "General"),
	Customer(1, "Customer");

	private int status;
	private String code;

	UsageTypeEnum(int status, String code) {
		this.status = status;
		this.code = code;
	}

	public String getCode() {
		return code;
	}

	public int getStatus() {
		return status;
	}

	static Map<Integer, UsageTypeEnum> maps = new HashMap<>();
	static Map<String, UsageTypeEnum> codeMaps = new HashMap<>();

	static {
		for (UsageTypeEnum enu : UsageTypeEnum.values()) {
			maps.put(enu.getStatus(), enu);
			codeMaps.put(enu.getCode(), enu);
		}
	};

	public static UsageTypeEnum findCode(String code) {
		if (code == null || !maps.containsKey(code)) {
			return null;
		}
		return maps.get(code);
	}

	public static UsageTypeEnum getStatus(Integer status) {
		if (status == null || status.intValue() <= 0) {
			return UsageTypeEnum.General;
		}
		return UsageTypeEnum.Customer;
	}

	/**
	 *
	 * @param status
	 * @param usageTypeEnum
	 * @return
	 */
	public static boolean check(Integer status, UsageTypeEnum... usageTypeEnum) {
		if (status == null || !maps.containsKey(status.intValue()) || usageTypeEnum == null || usageTypeEnum.length <= 0){
			return false;
		}
		for (UsageTypeEnum tlStatus: usageTypeEnum){
			if (status.intValue() == tlStatus.getStatus()){
				return true;
			}
		}
		return false;
	}

	/**
	 *
	 * @param usageTypeEnum
	 * @return
	 */
	public boolean check(UsageTypeEnum... usageTypeEnum){
		if (usageTypeEnum == null || usageTypeEnum.length <= 0){
			return false;
		}
		for (UsageTypeEnum status: usageTypeEnum){
			if (this.getStatus() == status.getStatus()){
				return true;
			}
		}
		return false;
	}
}
