package com.sgs.otsnotes.facade.model.enums;

import java.util.HashMap;
import java.util.Map;

public enum PreOrderStatus {
    New(1, (1 << 0),"New"){
        @Override
        public int getRuleId() {
            return (PreOrderStatus.Confirmed.getCode() | PreOrderStatus.Testing.getCode() | PreOrderStatus.Completed.getCode() | PreOrderStatus.Pending.getCode() | PreOrderStatus.Cancelled.getCode());
        }
    },
    Quoted(2,(1 << 1), "Quoted"),
    Confirmed(3,(1 << 2), "Confirmed"),
    Invoiced(4,(1 << 3), "Invoiced"),
    Closed(5,(1 << 4), "Closed"),
    Pending(6,(1 << 5), "Pending"),
    Cancelled(7,(1 << 6), "Cancelled"),
    Testing(8,(1 << 7), "Testing"),
    Reporting(9,(1 << 8), "Reporting"),
    Completed(10,(1 << 9),"Completed");

    private int status;
    private int code;
    private String message;

    PreOrderStatus(int status, int code, String message) {
        this.status = status;
        this.code = code;
        this.message = message;
    }

    public String getMessage() {
        return message;
    }

    public int getStatus() {
        return status;
    }

    public int getCode() {
        return code;
    }

    public int getRuleId(){
        return 0;
    }

    public static final Map<Integer, PreOrderStatus> maps = new HashMap<Integer, PreOrderStatus>() {
        private static final long serialVersionUID = -8986866330615001847L;
        {
            for (PreOrderStatus enu : PreOrderStatus.values()) {
                put(enu.getStatus(), enu);
            }
        }
    };

    public static PreOrderStatus getOrderStatus(Integer status) {
        if (status == null || !maps.containsKey(status.intValue())) {
            return null;
        }
        return maps.get(status.intValue());
    }

    public static String getMessage(Integer status) {
        if (status == null || !maps.containsKey(status.intValue())) {
            return null;
        }
        return maps.get(status).getMessage();
    }

    public static boolean checkStatus(Integer status, PreOrderStatus... orderStatus){
        if (status == null || !maps.containsKey(status.intValue()) || orderStatus == null || orderStatus.length <= 0) {
            return false;
        }
        for (PreOrderStatus orderStatu: orderStatus){
            if (status.intValue() == orderStatu.getStatus()){
                return true;
            }
        }
        return false;
    }

    public static boolean checkStatus(Integer status, int newStatus){
        if (status == null || !maps.containsKey(status.intValue()) || !maps.containsKey(newStatus)) {
            return false;
        }
        return (maps.get(status).getRuleId() & maps.get(newStatus).getCode()) > 0;
    }
}
