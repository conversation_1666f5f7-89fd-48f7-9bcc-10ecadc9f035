package com.sgs.otsnotes.facade.model.enums;

/**
 * @Author: mingyang.chen
 * @Date: 2020/12/10 14:27
 */
public enum LimitDataType {
    Data(0,"Data"),
    Numeric(1,"Numeric"),

    ;
    private final int type;
    private final String message;

    public static LimitDataType getType(String key) {
        for (LimitDataType item : LimitDataType.values()) {
            if (item.name().equals(key) || item.message.equals(key)){
                return item;
            }
        }
        return Data;
    }

    LimitDataType(int type, String message) {
        this.type = type;
        this.message = message;
    }

    public int getType() {
        return type;
    }

    public String getMessage() {
        return message;
    }

}
