package com.sgs.otsnotes.facade.model.enums;

public enum ReasonTypeEnum {
    None(0, "未知"),
    TestTesultError(1, "测试结果错误"),
    SampleError(2, "样品信息错误"),
    LackofTechnicalCapability(3, "技术能力欠缺"),
    TestCustomerApplications(4, "测试项目与客户申请不符"),
    ManualUpdateUnderstandingError(5, "Manual更新理解错误"),
    SubcontractingLaboratoryError(6, "分包实验室错误"),
    ReportingComplianceError(7, "报告合规性错误"),
    NonLaboratoryError(8, "非实验室错误"),
    Other(99, "其它（写明具体错误）");

    private final int code;
    private final String message;

    ReasonTypeEnum(int code, String message) {
        this.code = code;
        this.message = message;
    }

    public int getCode() {
        return code;
    }

    public String message() {
        return this.message;
    }

    public static ReasonTypeEnum getCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (ReasonTypeEnum item : ReasonTypeEnum.values()) {
            if (code.equals(item.getCode())) {
                return item;
            }
        }
        return null;
    }
}
