package com.sgs.otsnotes.facade.model.enums;

import java.util.HashMap;
import java.util.Map;

public enum ConclusionDimType {
    None(0, "None"),
    PpDim(1, "PP"),
    SectionDim(2, "Section"),
    MatrixDim(4, "Matrix");

    private int code;
    private String message;

    ConclusionDimType(int code, String message) {
        this.code = code;
        this.message = message;
    }

    public int getCode() {
        return code;
    }

    public String getMessage() {
        return this.message;
    }

    static Map<Integer, ConclusionDimType> maps = new HashMap<>();

    static {
        for (ConclusionDimType type : ConclusionDimType.values()) {
            maps.put(type.getCode(), type);
        }
    }

    public static ConclusionDimType findCode(Integer code) {
        if (code == null || !maps.containsKey(code.intValue())){
            return null;
        }
        return maps.get(code);
    }

    public static boolean check(Integer status) {
        if (status == null){
            return false;
        }
        return maps.containsKey(status.intValue());
    }

    /**
     *
     * @param status
     * @param type
     * @return
     */
    public static boolean check(Integer status, ConclusionDimType type) {
        if (status == null || !maps.containsKey(status.intValue())){
            return false;
        }
        return maps.get(status.intValue()) == type;
    }
}
