package com.sgs.otsnotes.facade.model.comparator;

import com.sgs.otsnotes.facade.model.dto.TestLineMatrixConditionDTO;

import java.util.Comparator;

public class TestLineMatrixConditionComparator implements Comparator<TestLineMatrixConditionDTO> {
    /**
     * 是否为升序
     */
    private boolean isAsc;
    /**
     *
     * @param isAsc
     */
    public TestLineMatrixConditionComparator(boolean isAsc) {
        this.isAsc = isAsc;
    }

    /**
     * 排序号升序、日期升序
     *
     * @param c1
     * @param c2
     * @return
     */
    @Override
    public int compare(TestLineMatrixConditionDTO c1, TestLineMatrixConditionDTO c2) {
        Integer conditionTypeBlockLeve11 = c1.getTestConditionTypeSeq();
        if (conditionTypeBlockLeve11 == null){
            conditionTypeBlockLeve11 = 0;
        }
        Integer conditionTypeBlockLeve12 = c2.getTestConditionTypeSeq();
        if (conditionTypeBlockLeve12 == null){
            conditionTypeBlockLeve12 = 0;
        }
        int index = Integer.compare(conditionTypeBlockLeve11, conditionTypeBlockLeve12);
        if (index < 0) {
            return isAsc ? -1 : 1;
        }
        if (index > 0) {
            return isAsc ? 1 : -1;
        }
        return index;
    }
}
