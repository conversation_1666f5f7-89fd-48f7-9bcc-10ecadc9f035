package com.sgs.otsnotes.facade.model.enums;

/**
 * tre_trims_testline_work_instruction_relationship 表 categoryId枚举
 * <AUTHOR>
 */
public enum CategoryEnums {
    ClientService(1),
    EquipmentOperation(2),
    Reporting (3),
    ResultEvaluation(4),
    SafetyPrecaution(5),
    SampleManagement(6),
    SamplePreparation_Classfication(7),
    SamplePreparation_Cutting(8),
    SamplePreparation_LaboratoryTesting(9),
    SamplePreparation_Weighing(10),
    TestingPrecaution(11),
    TestingProcedure(12),
    TechnicalInterpretation(13),
    WIForTesting(14),
    WIForCS(15),
    SamplePreparation_Injection(16),
    SamplePreparation_Tableting(17),
    SamplePreparationForTableting(19);


    private Integer code;
    CategoryEnums(Integer code){
        this.code = code;
    }

    public Integer getCode() {
        return code;
    }


    public static boolean checkCode(Integer code,CategoryEnums ... enums){
        if(code==null){
            return false;
        }
        for (CategoryEnums anEnum : enums) {
            if(anEnum.getCode().compareTo(code)==0){
                return true;
            }
        }
        return false;
    }

}
