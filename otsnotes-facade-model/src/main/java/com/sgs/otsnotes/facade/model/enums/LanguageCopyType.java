package com.sgs.otsnotes.facade.model.enums;

import java.util.HashMap;
import java.util.Map;

public enum LanguageCopyType {
    None(0, "None"),
    TestLine(1, "TestLine"),
    Analyte(2, "Analyte"),
    Limit(4, "Limit"),
    TestCondition(8, "TestCondition"),
    TestConditionGroup(16, "TestConditionGroup"),
    PP(32, "PP"),
    PPTestLineRel(64, "PPTestLineRel"),
    PPConditionRel(128, "PPConditionRel"),
    TestPosition(256, "TestPosition");

    private int status;
    private String code;

    LanguageCopyType(int status, String code) {
        this.status = status;
        this.code = code;
    }

    public int getStatus() {
        return status;
    }

    public String getCode() {
        return this.code;
    }

    static Map<Integer, LanguageCopyType> maps = new HashMap<>();

    static {
        for (LanguageCopyType type : LanguageCopyType.values()) {
            maps.put(type.getStatus(), type);
        }
    }

    public static LanguageCopyType findCode(Integer code) {
        if (code == null || !maps.containsKey(code.intValue())){
            return null;
        }
        return maps.get(code);
    }

    /**
     *
     * @param languageTypes
     * @param copyType
     * @return
     */
    public static boolean check(LanguageCopyType[] languageTypes, LanguageCopyType copyType) {
        if (languageTypes == null || languageTypes.length <= 0){
            return false;
        }
        for (LanguageCopyType languageType: languageTypes){
            if (languageType == copyType){
                return true;
            }
        }
        return false;
    }
}
