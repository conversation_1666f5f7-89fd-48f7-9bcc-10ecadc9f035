package com.sgs.otsnotes.facade.model.enums;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020/9/22 17:26
 */
public enum ProductLineAbbrEnum {
    SL(0, "SL"),
    HL(1, "HL"),
    <PERSON>hemLab(2, "CChemLab"),
    AFL(3, "AFL"),
    FTS(4, "FTS"),
    IND_Metal(5, "IND-Metal"),
;
    private final int id;
    private final String code;

    ProductLineAbbrEnum(int id, String code) {
        this.id = id;
        this.code = code;
    }

    public int getId() {
        return id;
    }

    public String getCode() {
        return code;
    }

    public static final Map<Integer, ProductLineAbbrEnum> maps = new HashMap<Integer, ProductLineAbbrEnum>() {
        private static final long serialVersionUID = -8986866330615001847L;
        {
            for (ProductLineAbbrEnum enu : ProductLineAbbrEnum.values()) {
                put(enu.getId(), enu);
            }
        }
    };

    public static ProductLineAbbrEnum getCode(Integer status) {
        if (status == null || !maps.containsKey(status.intValue())) {
            return null;
        }
        return maps.get(status.intValue());
    }
}
