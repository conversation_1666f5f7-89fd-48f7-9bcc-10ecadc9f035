package com.sgs.otsnotes.facade.model.enums;

import java.util.HashMap;
import java.util.Map;

public enum ClientSpecifiedTypeEnum {
    Trims(0,"Trims"),
    ClientSpecified(1,"Client Specified");

    private Integer type;
    private String code;

    ClientSpecifiedTypeEnum(int type, String code) {
        this.type = type;
        this.code = code;
    }

    public Integer getType() {
        return type;
    }

    public String getCode() {
        return code;
    }

    public static final Map<Integer, ClientSpecifiedTypeEnum> maps = new HashMap<Integer, ClientSpecifiedTypeEnum>() {
        private static final long serialVersionUID = -8986866330615001847L;
        {
            for (ClientSpecifiedTypeEnum specifiedType : ClientSpecifiedTypeEnum.values()) {
                put(specifiedType.getType(), specifiedType);
            }
        }
    };

    public static ClientSpecifiedTypeEnum findType(Integer type) {
        if (type == null || !maps.containsKey(type.intValue())) {
            return ClientSpecifiedTypeEnum.Trims;
        }
        return maps.get(type);
    }

    public static boolean check(Integer type, ClientSpecifiedTypeEnum specifiedType) {
        if (type == null || !maps.containsKey(type.intValue())){
            return false;
        }
        return maps.get(type.intValue()) == specifiedType;
    }

    /**
     *
     * @param type
     * @param specifiedType
     * @return
     */
    public static boolean check(Integer type, Integer specifiedType) {
        return check(type, ClientSpecifiedTypeEnum.findType(specifiedType));
    }
}
