package com.sgs.otsnotes.facade.model.trims.rsp.accreditation;

import com.sgs.framework.core.base.BaseProductLine;

import java.util.List;

public class AccreditationDetailsSyncInfo extends BaseProductLine {


    private Integer accreditationDetailId;
    private List<LaboratoriesSyncInfo> laboratories;

    public Integer getAccreditationDetailId() {
        return accreditationDetailId;
    }

    public void setAccreditationDetailId(Integer accreditationDetailId) {
        this.accreditationDetailId = accreditationDetailId;
    }

    public List<LaboratoriesSyncInfo> getLaboratories() {
        return laboratories;
    }

    public void setLaboratories(List<LaboratoriesSyncInfo> laboratories) {
        this.laboratories = laboratories;
    }
}