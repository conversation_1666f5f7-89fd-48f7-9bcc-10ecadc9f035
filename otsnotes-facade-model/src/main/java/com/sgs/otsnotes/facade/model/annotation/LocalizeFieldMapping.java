package com.sgs.otsnotes.facade.model.annotation;

import com.sgs.otsnotes.facade.model.localize.LocalizeCategoryConsts;

import java.lang.annotation.*;

/**
 * 多语言字段映射
 * 用于字段名不一致的场景
 * <AUTHOR>
 * @date 2020/12/31 17:26
 */
@Target({ElementType.TYPE})
@Repeatable(LocalizeFieldMappings.class)
@Retention(RetentionPolicy.RUNTIME)
public @interface LocalizeFieldMapping {

    /**
     * 翻译对象
     * @see LocalizeCategoryConsts
     * @return
     */
    String objName();
    /**
     * 对应主键
     * @return
     */
    String keyName();

    /**
     * 对应多语言的字段
     * @return 字段名
     */
    String fieldName();
}
