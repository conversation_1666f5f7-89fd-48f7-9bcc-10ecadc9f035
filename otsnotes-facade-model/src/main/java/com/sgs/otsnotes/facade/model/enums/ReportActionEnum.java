package com.sgs.otsnotes.facade.model.enums;

public enum ReportActionEnum {
    None(0, "None"),
    Approval(1, "Approve"),
    Reject(2, "Reject");

    private final int code;
    private final String message;

    ReportActionEnum(int code, String message) {
        this.code = code;
        this.message = message;
    }

    public int getCode() {
        return code;
    }

    public String message() {
        return this.message;
    }

    public static ReportActionEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (ReportActionEnum item : ReportActionEnum.values()) {
            if (code.equals(item.getCode())) {
                return item;
            }
        }
        return null;
    }
}
