package com.sgs.otsnotes.facade.model.enums;

import java.util.HashMap;
import java.util.Map;

public enum EnvironmentType {
    Local("local", "Local"),
    Dev("dev", false, "Dev"),
    Test("test", "Test"),
    Uat("uat", "Uat"),
    Prod("production", "Production");

    private String code;
    private boolean isWrite;
    private String message;

    EnvironmentType(String code, String message) {
        this.code = code;
        this.isWrite = true;
        this.message = message;
    }

    EnvironmentType(String code, boolean isWrite, String message) {
        this(code, message);
        this.isWrite = isWrite;
    }

    public String getCode() {
        return code;
    }

    public boolean isWrite() {
        return isWrite;
    }

    public String getMessage() {
        return message;
    }

    static Map<String, EnvironmentType> maps = new HashMap<>();

    static {
        for (EnvironmentType type : EnvironmentType.values()) {
            maps.put(type.getCode(), type);
        }
    }

    public static EnvironmentType findCode(String code) {
        if (code == null){
            return null;
        }
        return maps.get(code.toLowerCase());
    }

    public boolean check(EnvironmentType envType) {
        if (envType == null){
            return false;
        }
        return this == envType;
    }
}
