package com.sgs.otsnotes.facade.model.enums;

import java.util.HashMap;
import java.util.Map;

public enum ConclusionCalcType {
    None(0, "None"),
    TestLine(1, "TestLine"),
    OriginalSample(2, "OriginalSample"),
    Section(4, "Section"),
    PP(8, "PP"),
    Report(16, "Section"),
    PrelimResult(17, "PrelimResult"),;

    private int status;
    private String code;

    ConclusionCalcType(int status, String code) {
        this.status = status;
        this.code = code;
    }

    public int getStatus() {
        return status;
    }

    public String getCode() {
        return this.code;
    }

    static Map<Integer, ConclusionCalcType> maps = new HashMap<>();

    static {
        for (ConclusionCalcType calcType : ConclusionCalcType.values()) {
            maps.put(calcType.getStatus(), calcType);
        }
    }

    public static ConclusionCalcType findCode(Integer status) {
        if (status == null || !maps.containsKey(status.intValue())){
            return null;
        }
        return maps.get(status);
    }

    public static boolean check(Integer status) {
        if (status == null){
            return false;
        }
        return maps.containsKey(status.intValue());
    }

    /**
     *
     * @param status
     * @param calcType
     * @return
     */
    public static boolean check(Integer status, ConclusionCalcType calcType) {
        if (status == null || !maps.containsKey(status.intValue())){
            return false;
        }
        return maps.get(status.intValue()) == calcType;
    }
}
