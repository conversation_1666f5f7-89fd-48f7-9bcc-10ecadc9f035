package com.sgs.otsnotes.facade.model.common;

/**
 *
 */
public enum ResponseCode {
    SUCCESS(200, "操作成功"),

    ILLEGAL_ARGUMENT(100, "错误的请求参数"),

    ErrDuplicateReq(197, "重复请求"),

    // 明确知道失败原因，但客户端不关心，统一返回请求处理失败
    FAIL(198, "请求处理失败"),

    TokenExpire(201, "用户权限过期，请重新登录"),

    APPROVER_REPORT_CHECK_TL_FAIL(205, "Testline 没有 complated"),
    APPROVER_REPORT_CHECK_DFF_CONCLUSION_FAIL(206, "Testline 没有 complated"),
    NEED_CONCLUSION_TIPS(207, "CONCLUSION为空提示"),
    NEED_CONCLUSION_AND_TL_TIPS(208, "CONCLUSION为空并且TL提示"),


    GENERATE_TEST_RESULT_FILE_HAS_ENTERED(301, "REPORT FILE HAS ENTERED"),

    UNKNOWN(500, "系统异常，请稍后重试");

    private int code;

    private String message;

    ResponseCode(int code, String message) {
        this.code = code;
        this.message = message;
    }

    public int getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }

    /**
     * 通过代码获取枚举项
     *
     * @param code
     * @return
     */
    public static ResponseCode getByCode(int code) {
        for (ResponseCode errorCode : ResponseCode.values()) {
            if (errorCode.getCode() == code) {
                return errorCode;
            }
        }
        return null;
    }
}
