package com.sgs.otsnotes.facade.model.enums;

import java.util.HashMap;
import java.util.Map;

public enum ProductAttrType {
    None(0, "none"),
    TestLine(1, "testLine"),
    AnalyteLimit(2, "analyteLimit"),
    ProtocolPackage(3, "protocolPackage");

    private final int status;
    private final String attrType;

    ProductAttrType(int status, String attrType) {
        this.status = status;
        this.attrType = attrType;
    }

    public int getStatus() {
        return status;
    }

    public String getAttrType() {
        return this.attrType;
    }

    public static final Map<Integer, ProductAttrType> maps = new HashMap<Integer, ProductAttrType>() {
        private static final long serialVersionUID = -8986866330615001847L;
        {
            for (ProductAttrType enu : ProductAttrType.values()) {
                put(enu.getStatus(), enu);
            }
        }
    };

    public static ProductAttrType getCode(Integer status) {
        if (status == null || !maps.containsKey(status.intValue())) {
            return null;
        }
        return maps.get(status.intValue());
    }
}
