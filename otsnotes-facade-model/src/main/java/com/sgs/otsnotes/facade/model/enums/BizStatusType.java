package com.sgs.otsnotes.facade.model.enums;

import java.util.HashMap;
import java.util.Map;

public enum BizStatusType {
    None(0, "None"),
    UnlockExecOrder(1, "OrderSyncService.unlockExecOrderInfo"),
    SlimRTF(2,true, "ScanSlimFTPToGetRTF.parseRTFFile");

    private int bizType;
    private boolean oldStatus;
    private String message;

    BizStatusType(int bizType, String message) {
        this.bizType = bizType;
        this.message = message;
    }

    BizStatusType(int bizType, boolean oldStatus, String message) {
        this(bizType, message);
        this.oldStatus = oldStatus;
    }

    public int getBizType() {
        return bizType;
    }

    public boolean isOldStatus() {
        return oldStatus;
    }

    public String getMessage() {
        return message;
    }

    static Map<Integer, BizStatusType> maps = new HashMap<>();

    static {
        for (BizStatusType type : BizStatusType.values()) {
            maps.put(type.getBizType(), type);
        }
    }

    public static BizStatusType findCode(Integer code) {
        if (code == null || !maps.containsKey(code.intValue())){
            return null;
        }
        return maps.get(code);
    }

    /**
     *
     * @param bizStatusTypes
     * @param bizStatusType
     * @return
     */
    public static boolean check(BizStatusType[] bizStatusTypes, BizStatusType bizStatusType) {
        if (bizStatusTypes == null || bizStatusTypes.length <= 0){
            return false;
        }
        for (BizStatusType statusType: bizStatusTypes){
            if (statusType == bizStatusType){
                return true;
            }
        }
        return false;
    }

    /**
     *
     * @param bizType
     * @param bizTypes
     * @return
     */
    public static boolean check(Integer bizType, BizStatusType... bizTypes) {
        if (bizType == null || !maps.containsKey(bizType.intValue()) || bizTypes == null || bizTypes.length <= 0){
            return false;
        }
        for (BizStatusType statusType: bizTypes){
            if (bizType.intValue() == statusType.getBizType()){
                return true;
            }
        }
        return false;
    }
}
