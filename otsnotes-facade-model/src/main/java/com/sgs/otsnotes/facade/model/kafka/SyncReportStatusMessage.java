package com.sgs.otsnotes.facade.model.kafka;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2020/8/6 18:01
 */
public class SyncReportStatusMessage implements Serializable {
    private static final long serialVersionUID = 361058472897233574L;
    private String orderNo;
    private String reportNo;
    private String reportStatus;

    public static long getSerialVersionUID() {
        return serialVersionUID;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getReportNo() {
        return reportNo;
    }

    public void setReportNo(String reportNo) {
        this.reportNo = reportNo;
    }

    public String getReportStatus() {
        return reportStatus;
    }

    public void setReportStatus(String reportStatus) {
        this.reportStatus = reportStatus;
    }
}
