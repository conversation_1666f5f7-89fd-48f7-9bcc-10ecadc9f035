package com.sgs.otsnotes.facade.model.enums;

/**
 * @description   :   DIG-2167 OTSNotesInterfaces
 * <AUTHOR>  <PERSON><PERSON>.<PERSON>  Sun He<PERSON>yuan
 * @createDate    :  2020/7/3 10:51 AM
 * @updateUser    :  Kill<PERSON>.Sun  Sun He<PERSON>yuan
 * @updateDate    :  2020/7/3 10:51 AM
 * @updateRemark  :
 * @version       :  1.0
 */
public enum LogOperationEnums {
	ins("INS"), del("DEL"), upd("UPD"),can("CAN");
	
	private String desc;

	LogOperationEnums(String desc) {
		this.desc = desc;
	}

	public String getDesc() {

		return desc;
	}

}
