package com.sgs.otsnotes.facade.model.enums;

import java.util.HashMap;
import java.util.Map;

import static com.sgs.otsnotes.facade.model.enums.ConclusionColumnType.*;

public enum ConclusionHeadType {
    None(0, Select,"None"),
    Conclusion(1, Select,"Conclusion"),
    TestLineID(2, Text, "TestLine ID"),
    TestItem(3, Text,"TestItem"),
    SampleNo(4, Text,"SampleNo"),
    SectionID(5, Text,"SectionID"),
    SectionName(6, Text,"SectionName"),
    Section(7, Text,"Section"),
    TestLine(8, Text,"TestLine"),
    PPName(9, Text,"PPName"),
    Remark(10, Input,"Remark");

    private Integer id;
    private String name;
    private ConclusionColumnType columnType = ConclusionColumnType.Text;

    ConclusionHeadType(Integer id, String name) {
        this.id = id;
        this.name = name;
    }

    ConclusionHeadType(Integer id, ConclusionColumnType columnType, String name) {
        this(id, name);
        this.columnType = columnType;
    }

    public int getId() {
        return id;
    }

    public String getName() {
        return name;
    }

    public ConclusionColumnType getColumnType() {
        return columnType;
    }

    public static final Map<Integer, ConclusionHeadType> maps = new HashMap<Integer, ConclusionHeadType>() {
        private static final long serialVersionUID = -8986866330615001847L;
        {
            for (ConclusionHeadType conclusionHeadType : ConclusionHeadType.values()) {
                put(conclusionHeadType.id, conclusionHeadType);
            }
        }
    };

    public static ConclusionHeadType findCode(Integer id) {
        if (id == null || !maps.containsKey(id.intValue())) {
            return ConclusionHeadType.Conclusion;
        }
        return maps.get(id);
    }

    public static boolean check(Integer id, ConclusionHeadType conclusionMode) {
        if (id == null || !maps.containsKey(id.intValue())){
            return false;
        }
        return maps.get(id.intValue()) == conclusionMode;
    }

    public boolean check(ConclusionHeadType... conclusionHeadTypes) {
        if (conclusionHeadTypes == null || conclusionHeadTypes.length <= 0){
            return false;
        }
        for (ConclusionHeadType conclusionType : conclusionHeadTypes) {
            if (this.getId() == conclusionType.getId()){
                return true;
            }
        }
        return false;
    }

    /**
     *
     * @param id
     * @param conclusionMode
     * @return
     */
    public static boolean check(Integer id, Integer conclusionMode) {
        return check(id, ConclusionHeadType.findCode(conclusionMode));
    }

}
