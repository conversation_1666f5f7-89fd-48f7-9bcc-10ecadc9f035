package com.sgs.otsnotes.facade.model.enums;

/**
 * @description   :  ActionEnums
 * <AUTHOR>  <PERSON><PERSON>.<PERSON>
 * @createDate    :  2020/7/2 3:41 PM
 * @updateUser    :  Killian.Sun  Sun He<PERSON>
 * @updateDate    :  2020/7/2 3:41 PM
 * @updateRemark  :
 * @version       :  1.0
 */
public enum ActionEnums {
    JOB_VALIDATE("Job Validate"),
    RETEST("Lab Retest"),
    SAVE_SUBMIT("Submit Test Data"),
    VALIDATE("Valiate Test Data"),
    RETURN("Return Test Data"),
    SAVE("Save Conclusion"),
    REJECT("Reject Report"),
    CONFIRM_MATRIX("Confirm Matrix");

    private String action;
    ActionEnums(String action){
        this.action = action;
    }

    public String getAction() {
        return action;
    }
}
