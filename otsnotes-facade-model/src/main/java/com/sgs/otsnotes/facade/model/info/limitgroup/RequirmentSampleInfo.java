package com.sgs.otsnotes.facade.model.info.limitgroup;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.sgs.framework.core.base.BaseProductLine;
import com.sgs.otsnotes.facade.model.info.trims.PPSampleLimitGroupInfo;

import java.util.List;

public class RequirmentSampleInfo extends BaseProductLine {
    /**
     *
     */
    private List<PPSampleLimitGroupInfo> ppSampleRels;
    /**
     *
     */
    private String sampleId;
    /**
     *
     */
    private String sampleNo;
    /**
     *
     */
    private String category;
    /**
     *
     */
    private String sampleParentId;
    /**
     *
     */
    private int sampleType;
    /**
     *
     */
    private String sampleTypeShort;
    /**
     *
     */
    private String description;
    /**
     *
     */
    private String composition;
    /**
     *
     */
    private String color;
    /**
     *
     */
    @JsonIgnore
    private Integer sampleSeq;

    public List<PPSampleLimitGroupInfo> getPpSampleRels() {
        return ppSampleRels;
    }

    public void setPpSampleRels(List<PPSampleLimitGroupInfo> ppSampleRels) {
        this.ppSampleRels = ppSampleRels;
    }

    public String getSampleId() {
        return sampleId;
    }

    public void setSampleId(String sampleId) {
        this.sampleId = sampleId;
    }

    public String getSampleNo() {
        return sampleNo;
    }

    public void setSampleNo(String sampleNo) {
        this.sampleNo = sampleNo;
    }

    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    public String getSampleParentId() {
        return sampleParentId;
    }

    public void setSampleParentId(String sampleParentId) {
        this.sampleParentId = sampleParentId;
    }

    public int getSampleType() {
        return sampleType;
    }

    public void setSampleType(int sampleType) {
        this.sampleType = sampleType;
    }

    public String getSampleTypeShort() {
        return sampleTypeShort;
    }

    public void setSampleTypeShort(String sampleTypeShort) {
        this.sampleTypeShort = sampleTypeShort;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getComposition() {
        return composition;
    }

    public void setComposition(String composition) {
        this.composition = composition;
    }

    public String getColor() {
        return color;
    }

    public void setColor(String color) {
        this.color = color;
    }

    public Integer getSampleSeq() {
        return sampleSeq;
    }

    public void setSampleSeq(Integer sampleSeq) {
        this.sampleSeq = sampleSeq;
    }
}
