package com.sgs.otsnotes.facade.model.enums;

import java.util.HashMap;
import java.util.Map;

/**
 * @ClassName OrderLanguageTypeEnums
 * @Description TODO
 * <AUTHOR>
 * @Date 7/29/2020
 * '0：PP
 * 1：Section
 * 2：TestLine
 * 3：Citation
 * 4：Analyte
 * 5：ConditionType
 * 6：Condition
 * 7：Position
 * 8：UsageType
 * 9：PPTestline',
 * 10：AnalyteUnit',
 */
public enum OrderLanguageTypeEnums {
    PP(0),
    Section(1),
    TestLine(2),
    Citation(3),
    Analyte(4),
    ConditionType(5),
    Condition(6),
    Position(7),
    UsageType(8),
    PPTestline(9),
    AnalyteUnit(10);

    private Integer type;

    OrderLanguageTypeEnums(Integer type){
        this.type = type;
    }

    public Integer getType() {
        return type;
    }


    public static final Map<Integer, OrderLanguageTypeEnums> maps = new HashMap<Integer, OrderLanguageTypeEnums>() {
        {
            for (OrderLanguageTypeEnums enu : OrderLanguageTypeEnums.values()) {
                put(enu.getType(), enu);
            }
        }
    };

    public static OrderLanguageTypeEnums findType(Integer status) {
        if (status == null || !maps.containsKey(status.intValue())) {
            return null;
        }
        return maps.get(status.intValue());
    }

    /**
     *
     * @param status
     * @param type
     * @return
     */
    public static boolean check(Integer status, OrderLanguageTypeEnums ... type) {
        if (status == null || !maps.containsKey(status.intValue())){
            return false;
        }
        for (OrderLanguageTypeEnums enums : type) {
            if(enums.getType()==status.intValue()){
                return true;
            }
        }
        return false;
    }
}
