package com.sgs.otsnotes.facade.model.enums;

import java.util.HashMap;
import java.util.Map;

public enum EventSyncType {
    None(0, "None"),
    UsageType(10, EventSyncMode.FullUpdate, "UsageType"),
    ProductAttribute(20, EventSyncMode.FullUpdate, "ProductAttribute"),
    TestCondition(30, EventSyncMode.FullUpdate, "TestCondition"),
    TestStandard(40, EventSyncMode.FullUpdate, "TestStandard"),
    Regulation(50, EventSyncMode.FullUpdate, "Regulation"),
    TestLimitGroup(60,"TestLimitGroup"),
    Unit(70,"Unit"),
    TestAnalyte(80,"TestAnalyte"),
    TestAnalyteLimit(90,10,true, EventSyncMode.FullUpdate, "TestAnalyteLimit"),

    TestLine(100,true,20, "TestLine"),
    ProtocolPackage(110, true,"ProtocolPackage"),
    LabSection(120,true,"LabSection"),
    Accreditation(130,"Accreditation");

    private int status;
    private boolean isUpgradeMd5 = false;
    private int limit = 30;
    /**
     * TODO 如果为：PartialUpdate，这里要考虑升级问题
     */
    private EventSyncMode syncMode = EventSyncMode.PartialUpdate;
    private String code;

    EventSyncType(int status, String code) {
        this.status = status;
        this.code = code;
    }

    EventSyncType(int status, int limit, String code) {
        this(status, code);
        this.limit = limit;
    }

    EventSyncType(int status, int limit, EventSyncMode syncMode, String code) {
        this(status, syncMode, code);
        this.limit = limit;
    }

    EventSyncType(int status, int limit, boolean isUpgradeMd5, EventSyncMode syncMode, String code) {
        this(status, limit, syncMode, code);
        this.isUpgradeMd5 = isUpgradeMd5;
    }

    EventSyncType(int status, EventSyncMode syncMode, String code) {
        this(status, code);
        this.syncMode = syncMode;
    }

    EventSyncType(int status, boolean isUpgradeMd5, String code) {
        this(status, code);
        this.isUpgradeMd5 = isUpgradeMd5;
    }

    EventSyncType(int status, boolean isUpgradeMd5, int limit, String code) {
        this(status, limit, code);
        this.isUpgradeMd5 = isUpgradeMd5;
    }

    public int getStatus() {
        return status;
    }

    public String getCode() {
        return code;
    }

    public int getLimit() {
        return limit;
    }

    public EventSyncMode getSyncMode() {
        return syncMode;
    }

    public boolean isUpgradeMd5() {
        return isUpgradeMd5;
    }

    public static final Map<String, EventSyncType> codeMaps = new HashMap<String, EventSyncType>();
    public static final Map<Integer, EventSyncType> maps = new HashMap<Integer, EventSyncType>() {
        private static final long serialVersionUID = -8986866330615001847L;
        {
            for (EventSyncType syncType : EventSyncType.values()) {
                put(syncType.getStatus(), syncType);
                codeMaps.put(syncType.getCode().toLowerCase(), syncType);
            }
        }
    };

    /**
     *
     * @param status
     * @return
     */
    public static EventSyncType findStatus(Integer status) {
        if (status == null || !maps.containsKey(status)) {
            return null;
        }
        return maps.get(status);
    }

    public static EventSyncType findCode(String code) {
        if (code == null || !codeMaps.containsKey(code.toLowerCase())) {
            return null;
        }
        return codeMaps.get(code.toLowerCase());
    }

    /**
     *
     * @param status
     * @param code
     * @return
     */
    public static boolean check(Integer status, String code) {
        if (status == null || code == null || !maps.containsKey(status)){
            return false;
        }
        return maps.get(status).getCode().equalsIgnoreCase(code);
    }

    public static boolean check(Integer status, EventSyncType... trimsSyncTypes) {
        if (status == null || !maps.containsKey(status.intValue()) || trimsSyncTypes == null || trimsSyncTypes.length <= 0){
            return false;
        }
        for (EventSyncType syncType: trimsSyncTypes){
            if (status.intValue() == syncType.getStatus()){
                return true;
            }
        }
        return false;
    }

    /**
     *
     * @param trimsSyncTypes
     * @return
     */
    public boolean check(EventSyncType... trimsSyncTypes){
        if (trimsSyncTypes == null || trimsSyncTypes.length <= 0){
            return false;
        }
        for (EventSyncType syncType: trimsSyncTypes){
            if (this.getStatus() == syncType.getStatus()){
                return true;
            }
        }
        return false;
    }
}
