package com.sgs.otsnotes.facade.model.dto;

public class GenerateTemplateDTO  {

	private Integer	templateSettingID;
	private String templateName;
	private String container;
	private String containerTemplateFilePath;
	private String otsFlag;
	
	private String templateFilePath;
	private String templateID;
	private String dataObject;

	private Integer buSubTypeID;
	private String templateTypeId;
	private String sectionId;
	private Integer applicationID;
	private Integer languageID;
	
	public Integer getTemplateSettingID() {
		return templateSettingID;
	}
	public void setTemplateSettingID(Integer templateSettingID) {
		this.templateSettingID = templateSettingID;
	}
	public String getTemplateName() {
		return templateName;
	}
	public void setTemplateName(String templateName) {
		this.templateName = templateName;
	}
	public String getContainer() {
		return container;
	}
	public void setContainer(String container) {
		this.container = container;
	}
	public String getContainerTemplateFilePath() {
		return containerTemplateFilePath;
	}
	public void setContainerTemplateFilePath(String containerTemplateFilePath) {
		this.containerTemplateFilePath = containerTemplateFilePath;
	}
	public String getOtsFlag() {
		return otsFlag;
	}
	public void setOtsFlag(String otsFlag) {
		this.otsFlag = otsFlag;
	}
	public String getTemplateFilePath() {
		return templateFilePath;
	}
	public void setTemplateFilePath(String templateFilePath) {
		this.templateFilePath = templateFilePath;
	}
	public String getTemplateID() {
		return templateID;
	}
	public void setTemplateID(String templateID) {
		this.templateID = templateID;
	}
	public String getDataObject() {
		return dataObject;
	}
	public void setDataObject(String dataObject) {
		this.dataObject = dataObject;
	}

	public Integer getBuSubTypeID() {
		return buSubTypeID;
	}

	public void setBuSubTypeID(Integer buSubTypeID) {
		this.buSubTypeID = buSubTypeID;
	}

	public String getTemplateTypeId() {
		return templateTypeId;
	}

	public void setTemplateTypeId(String templateTypeId) {
		this.templateTypeId = templateTypeId;
	}

	public String getSectionId() {
		return sectionId;
	}

	public void setSectionId(String sectionId) {
		this.sectionId = sectionId;
	}

	public Integer getApplicationID() {
		return applicationID;
	}

	public void setApplicationID(Integer applicationID) {
		this.applicationID = applicationID;
	}

	public Integer getLanguageID() {
		return languageID;
	}

	public void setLanguageID(Integer languageID) {
		this.languageID = languageID;
	}
}
