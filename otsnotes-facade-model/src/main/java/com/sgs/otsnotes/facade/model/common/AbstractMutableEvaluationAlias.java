package com.sgs.otsnotes.facade.model.common;

import java.io.Serializable;

/**
 * 用于构造EvaluationAlias + PP_Notes
 * <AUTHOR>
 * @date 2020/9/27 19:02
 */
public abstract class AbstractMutableEvaluationAlias implements Serializable {
    private Long ppArtifactRelId;
    private String testLineInstanceId;
    private String evaluationAlias;
    private String ppNotes;

    public Long getPpArtifactRelId() {
        return ppArtifactRelId;
    }

    public void setPpArtifactRelId(Long ppArtifactRelId) {
        this.ppArtifactRelId = ppArtifactRelId;
    }

    public String getTestLineInstanceId() {
        return testLineInstanceId;
    }

    public void setTestLineInstanceId(String testLineInstanceId) {
        this.testLineInstanceId = testLineInstanceId;
    }

    public String getEvaluationAlias() {
        return evaluationAlias;
    }

    public void setEvaluationAlias(String evaluationAlias) {
        this.evaluationAlias = evaluationAlias;
    }

    public String getPpNotes() {
        return ppNotes;
    }

    public void setPpNotes(String ppNotes) {
        this.ppNotes = ppNotes;
    }
}
