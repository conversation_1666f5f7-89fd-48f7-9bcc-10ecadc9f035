package com.sgs.otsnotes.facade.model.gpn.subreport.req;

import com.sgs.framework.core.base.BaseRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @title: ReviseSubReportReq
 * @projectName otsnotes-service
 * @description: TODO
 * @date 2022/5/1310:46
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ReviseSubReportReq extends BaseRequest {
    private static final long serialVersionUID = 1092297870097433942L;
    private String subReportNo;
    //subContractNo
    private String objectNo;
    // By SubReport界面；By Amend Report Auto
    private String caller;
    private String orderNo;
}
