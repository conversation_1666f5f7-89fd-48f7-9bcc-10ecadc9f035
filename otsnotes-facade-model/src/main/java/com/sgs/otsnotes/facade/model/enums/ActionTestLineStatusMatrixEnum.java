package com.sgs.otsnotes.facade.model.enums;

/**
 * TL STATUS的操作控制
 * <AUTHOR>
 * @date 2020/11/16 17:07
 */
public enum ActionTestLineStatusMatrixEnum {
    ASSIGN_SAMPLE(1, "assign sample", new TestLineStatus[]{TestLineStatus.Typing,TestLineStatus.DR }),
    UPDATE_STANDARD(2, "Update Standard", new TestLineStatus[]{TestLineStatus.Typing,TestLineStatus.DR }),
    UPDATE_CONDITION(3, "Update Condition", new TestLineStatus[]{TestLineStatus.Typing,TestLineStatus.DR }),
    CONFIRM_CONDITION(4, "Confirm Condition", new TestLineStatus[]{TestLineStatus.Typing,TestLineStatus.DR }),
    DELETE_TL(5, "Del TL", new TestLineStatus[]{TestLineStatus.Typing,TestLineStatus.NC,TestLineStatus.DR }),
    CANCEL_TL(6, "Cancel TL", new TestLineStatus[]{TestLineStatus.Typing,TestLineStatus.NC,TestLineStatus.DR }),
    NC_TL(7, "NC TL", new TestLineStatus[]{TestLineStatus.Typing }),
    COPY_TEST(8, "Copy Test", new TestLineStatus[]{TestLineStatus.Typing}),
    DEL_TEST(9, "Del Test", new TestLineStatus[]{TestLineStatus.Typing}),
    CHANGE(10, "Change", new TestLineStatus[]{TestLineStatus.Entered,TestLineStatus.Submit,TestLineStatus.Completed}),

    ;

    private Integer action;
    private String name;
    private TestLineStatus[] allowTestLineStatus;

    ActionTestLineStatusMatrixEnum(Integer action,String name, TestLineStatus[] allowTestLineStatus) {
        this.action = action;
        this.name = name;
        this.allowTestLineStatus = allowTestLineStatus;
    }

    public Integer getAction() {
        return action;
    }

    public String getName() {
        return name;
    }

    public TestLineStatus[] getAllowTestLineStatus() {
        return allowTestLineStatus;
    }
}
