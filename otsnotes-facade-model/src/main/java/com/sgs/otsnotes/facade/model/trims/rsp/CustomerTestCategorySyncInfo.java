package com.sgs.otsnotes.facade.model.trims.rsp;

import com.sgs.framework.core.base.BaseProductLine;

public class CustomerTestCategorySyncInfo extends BaseProductLine {
    /**
     *
     */
    private Integer customerTestCategoryId;
    /**
     *
     */
    private Integer customerAccountId;
    /**
     *
     */
    private String customerTestCategoryName;

    public Integer getCustomerTestCategoryId() {
        return customerTestCategoryId;
    }

    public void setCustomerTestCategoryId(Integer customerTestCategoryId) {
        this.customerTestCategoryId = customerTestCategoryId;
    }

    public Integer getCustomerAccountId() {
        return customerAccountId;
    }

    public void setCustomerAccountId(Integer customerAccountId) {
        this.customerAccountId = customerAccountId;
    }

    public String getCustomerTestCategoryName() {
        return customerTestCategoryName;
    }

    public void setCustomerTestCategoryName(String customerTestCategoryName) {
        this.customerTestCategoryName = customerTestCategoryName;
    }
}
