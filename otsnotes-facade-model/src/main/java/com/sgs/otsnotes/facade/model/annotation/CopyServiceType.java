package com.sgs.otsnotes.facade.model.annotation;

import com.sgs.otsnotes.facade.model.enums.OrderBizType;
import com.sgs.otsnotes.facade.model.enums.OrderCopyType;

import java.lang.annotation.*;

@Target({ElementType.FIELD, ElementType.METHOD, ElementType.TYPE, ElementType.PARAMETER})
@Retention(RetentionPolicy.RUNTIME)
@Inherited
@Documented
public @interface CopyServiceType {
    /**
     *
     * @return
     */
    OrderCopyType[] copyType();
    /**
     *
     * @return
     */
    OrderBizType bizType();
}
