package com.sgs.otsnotes.facade.model.enums;

import java.util.HashMap;
import java.util.Map;

public enum OrderStatus {
    New(1601, "New"),
    Testing(1602, "Testing"),
	Completed(1603,"Completed");

    private Integer status;
    private String message;

    OrderStatus(Integer status, String message) {
        this.status = status;
        this.message = message;
    }

    public String getMessage() {
        return message;
    }

    public Integer getStatus() {
        return status;
    }

    public static final Map<Integer, OrderStatus> maps = new HashMap<Integer, OrderStatus>() {
        private static final long serialVersionUID = -8986866330615001847L;
        {
            for (OrderStatus enu : OrderStatus.values()) {
                put(enu.getStatus(), enu);
            }
        }
    };

    public static OrderStatus getOrderStatus(Integer status) {
        if (status == null || !maps.containsKey(status.intValue())) {
            return null;
        }
        return maps.get(status.intValue());
    }

    /**
     *
     * @param status
     * @param orderStatus
     * @return
     */
    public static boolean check(Integer status, OrderStatus orderStatus) {
        if (status == null || !maps.containsKey(status.intValue())){
            return false;
        }
        return maps.get(status.intValue()) == orderStatus;
    }
}