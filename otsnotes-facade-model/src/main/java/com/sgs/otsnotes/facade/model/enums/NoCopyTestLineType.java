package com.sgs.otsnotes.facade.model.enums;

import java.util.HashMap;
import java.util.Map;

public enum NoCopyTestLineType {
    TestLine(0, "NO"),
    PpTestLine(1, "YES");

    private int status;
    private String code;

    NoCopyTestLineType(int status, String code) {
        this.status = status;
        this.code = code;
    }

    public String getCode() {
        return code;
    }

    public int getStatus() {
        return status;
    }

    public static final Map<Integer, NoCopyTestLineType> maps = new HashMap<Integer, NoCopyTestLineType>() {
        private static final long serialVersionUID = -8986866330615001847L;
        {
            for (NoCopyTestLineType enu : NoCopyTestLineType.values()) {
                put(enu.getStatus(), enu);
            }
        }
    };

    public static NoCopyTestLineType getStatus(Integer status) {
        if (status == null || !maps.containsKey(status.intValue())) {
            return null;
        }
        return maps.get(status.intValue());
    }

    /**
     *
     * @param status
     * @param testLineType
     * @return
     */
    public static boolean check(Integer status, NoCopyTestLineType testLineType) {
        if (status == null || !maps.containsKey(status.intValue())){
            return false;
        }
        return maps.get(status.intValue()) == testLineType;
    }

}
