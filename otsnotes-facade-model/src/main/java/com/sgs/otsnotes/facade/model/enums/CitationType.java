package com.sgs.otsnotes.facade.model.enums;

import java.util.HashMap;
import java.util.Map;

public enum CitationType {
    None(0, "None"),
    Method(1, TrimsCacheType.CitationMethodInfo,"Method"),
    Regulation(2, TrimsCacheType.CitationRegulationInfo,"Regulation"),
    Standard(3, TrimsCacheType.CitationStandardInfo,"Standard"),
    ProtocolPackage(4,"ProtocolPackage");

    private int type;
    private TrimsCacheType cacheType;
    private String message;

    CitationType(int type, String message) {
        this.type = type;
        this.message = message;
    }

    CitationType(int type, TrimsCacheType cacheType, String message) {
        this(type, message);
        this.cacheType = cacheType;
    }

    public int getType() {
        return type;
    }

    public TrimsCacheType getCacheType() {
        return cacheType;
    }

    public String getMessage() {
        return message;
    }

    static Map<Integer, CitationType> maps = new HashMap<>();

    static {
        for (CitationType type : CitationType.values()) {
            if (type.getType() <= 0){
                continue;
            }
            maps.put(type.getType(), type);
        }
    }

    public static CitationType findType(Integer type) {
        if (type == null || !maps.containsKey(type.intValue())){
            return null;
        }
        return maps.get(type);
    }

}
