package com.sgs.otsnotes.facade.model.enums;

import java.util.HashMap;
import java.util.Map;

public enum PpType {
    PP(0, "Pp"),
    CCPP(1, "CCPP");

    private final int type;
    private final String code;

    PpType(int type, String code) {
        this.type = type;
        this.code = code;
    }

    public int getType() {
        return type;
    }

    public String getCode() {
        return code;
    }

    public static final Map<Integer, PpType> maps = new HashMap<Integer, PpType>() {
        private static final long serialVersionUID = -8986866330615001847L;
        {
            for (PpType enu : PpType.values()) {
                put(enu.getType(), enu);
            }
        }
    };

    public static PpType findType(Integer type) {
        if (type == null || !maps.containsKey(type.intValue())) {
            return PpType.PP;
        }
        return maps.get(type.intValue());
    }

    public static boolean check(Integer type, PpType ppType) {
        if (type == null || ppType == null || !maps.containsKey(type.intValue())) {
            return false;
        }
        return maps.get(type.intValue()) == ppType;
    }
}
