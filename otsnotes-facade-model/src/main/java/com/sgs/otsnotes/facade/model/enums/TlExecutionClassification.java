package com.sgs.otsnotes.facade.model.enums;

import java.util.HashMap;
import java.util.Map;

public enum TlExecutionClassification {
    NeedToTest(1, "Need To Test"),
    InformationReviewOnly(2, "Information Review Only"),
    DisplayOnly(3, "Display Only");

    private Integer status;
    private String message;

    TlExecutionClassification(Integer status, String message) {
        this.status = status;
        this.message = message;
    }

    public String getMessage() {
        return message;
    }

    public Integer getStatus() {
        return status;
    }

    public static final Map<Integer, TlExecutionClassification> maps = new HashMap<Integer, TlExecutionClassification>() {
        private static final long serialVersionUID = -8986866330615001847L;
        {
            for (TlExecutionClassification enu : TlExecutionClassification.values()) {
                put(enu.getStatus(), enu);
            }
        }
    };

    public static TlExecutionClassification getStatus(Integer status) {
        if (status == null || !maps.containsKey(status.intValue())) {
            return null;
        }
        return maps.get(status);
    }

    public static String getMessage(Integer status) {
        if (status == null || !maps.containsKey(status.intValue())) {
            return null;
        }
        return maps.get(status).getMessage();
    }

    public static boolean check(Integer status, TlExecutionClassification reportStatus) {
        if (status == null || !maps.containsKey(status.intValue())) {
            return false;
        }
        return maps.get(status) == reportStatus;
    }
}
