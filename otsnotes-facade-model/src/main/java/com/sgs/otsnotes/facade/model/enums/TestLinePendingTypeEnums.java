package com.sgs.otsnotes.facade.model.enums;

/**
 * @ClassName TestLinePendingTypeEnums
 * @Description testLinePending 自定义注解，对应type 枚举
 * <AUTHOR>
 * @Date 1/14/2021
 */
public enum TestLinePendingTypeEnums {
//    TL_ID(1),
////    PP_TL_REL_ID(2),
//    TL_ID_List(3),
//    PP_TL_REL_ID_List(4),

    NONE(0, "None"),
    TestLineInstanceId(1, "testLineInstanceId"),
    ConfirmCondition(2, "testLineIds.testLineInstanceId"),
    TestLineStandardQuery(3, "ppTestLineRels.testLineInstanceId"),
    SaveStandTestLineId(4, "standards.testLineInstanceId"),
    AssignSamplePpTlId(5, "ppTestLineRelIds"),
    PpTestLineRelId(6, "ppTestLineRels.ppTestLineRelId"),
    ;

    private Integer type;
    private String filedName;
    TestLinePendingTypeEnums(Integer type, String filedName){
        this.type = type;
        this.filedName = filedName;
    }

    public Integer getType() {
        return type;
    }

    public static boolean check(Integer type,TestLinePendingTypeEnums ...enums){
        if(type==null){
            return false;
        }
        for (TestLinePendingTypeEnums anEnum : enums) {
            if(anEnum.type.compareTo(type)==0){
                return true;
            }
        }
        return false;
    }
    public static TestLinePendingTypeEnums get(Integer type){
        if(type==null){
            return null;
        }
        TestLinePendingTypeEnums[] values = TestLinePendingTypeEnums.values();
        for (TestLinePendingTypeEnums value : values) {
            if(value.type.compareTo(type)==0){
                return value;
            }
        }
        return null;
    }
}
