package com.sgs.otsnotes.facade.model.enums;

import java.util.HashMap;
import java.util.Map;

public enum ClientSpecifiedType {
    Trims(0,"Trims"),
    ManualInput(1,"ManualInput");

    private Integer type;
    private String name;

    ClientSpecifiedType(int type, String name) {
        this.type = type;
        this.name = name;
    }

    public Integer getType() {
        return type;
    }

    public String getName() {
        return name;
    }

    public static final Map<Integer, ClientSpecifiedType> maps = new HashMap<Integer, ClientSpecifiedType>() {
        private static final long serialVersionUID = -8986866330615001847L;
        {
            for (ClientSpecifiedType specifiedType : ClientSpecifiedType.values()) {
                put(specifiedType.getType(), specifiedType);
            }
        }
    };

    public static ClientSpecifiedType findType(Integer type) {
        if (type == null || !maps.containsKey(type.intValue())) {
            return ClientSpecifiedType.Trims;
        }
        return maps.get(type);
    }

    public static boolean check(Integer type, ClientSpecifiedType specifiedType) {
        if (type == null || !maps.containsKey(type.intValue())){
            return false;
        }
        return maps.get(type.intValue()) == specifiedType;
    }

    /**
     *
     * @param type
     * @param specifiedType
     * @return
     */
    public static boolean check(Integer type, Integer specifiedType) {
        return check(type, ClientSpecifiedType.findType(specifiedType));
    }
}
