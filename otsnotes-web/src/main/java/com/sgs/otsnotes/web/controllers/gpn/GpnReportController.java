package com.sgs.otsnotes.web.controllers.gpn;

import com.beust.jcommander.internal.Lists;
import com.github.pagehelper.PageInfo;
import com.sgs.core.domain.UserInfo;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.core.base.CustomResult;
import com.sgs.framework.security.annotation.AccessRule;
import com.sgs.framework.security.context.SecurityContextHolder;
import com.sgs.framework.security.enums.PolicyActionType;
import com.sgs.framework.security.utils.SecurityUtil;
import com.sgs.framework.tool.utils.Func;
import com.sgs.framework.tool.utils.ProductLineContextHolder;
import com.sgs.grus.bizlog.common.BizLog;
import com.sgs.grus.bizlog.common.BizLogHelper;
import com.sgs.otsnotes.core.annotation.AccessPolicyRule;
import com.sgs.otsnotes.core.base.BaseController;
import com.sgs.otsnotes.core.constants.BizLogConstant;
import com.sgs.otsnotes.core.constants.Constants;
import com.sgs.otsnotes.core.enums.PolicyType;
import com.sgs.otsnotes.core.workbook.ExportUtils;
import com.sgs.otsnotes.dbstorages.mybatis.extmapper.ReportMapper;
import com.sgs.otsnotes.dbstorages.mybatis.model.ReportFilePO;
import com.sgs.otsnotes.domain.service.ReportReworkService;
import com.sgs.otsnotes.domain.service.gpn.report.IGpnReportService;
import com.sgs.otsnotes.facade.model.dto.EmailModelDTO;
import com.sgs.otsnotes.facade.model.dto.GpnOrderReportDTO;
import com.sgs.otsnotes.facade.model.dto.GpnOrderReportDetailDTO;
import com.sgs.otsnotes.facade.model.dto.ReportDTO;
import com.sgs.otsnotes.facade.model.enums.ReportApproveType;
import com.sgs.otsnotes.facade.model.enums.ReportStatus;
import com.sgs.otsnotes.facade.model.gpn.report.req.ReNewCheckReq;
import com.sgs.otsnotes.facade.model.req.RejectReportReq;
import com.sgs.otsnotes.facade.model.req.*;
import com.sgs.otsnotes.facade.model.req.gpn.*;
import com.sgs.otsnotes.facade.model.req.report.*;
import com.sgs.otsnotes.facade.model.req.subReport.ReturnSubContractReportReq;
import com.sgs.otsnotes.facade.model.rsp.ReportReworkApproverExportRsp;
import com.sgs.otsnotes.facade.model.rsp.SubReportRelReportRsp;
import com.sgs.otsnotes.facade.model.rsp.report.BatchCheckGenerateReportRsp;
import com.sgs.otsnotes.facade.model.rsp.report.ReportTestLineRsp;
import com.sgs.otsnotes.facade.model.rsp.report.UploadPdfRsp;
import com.sgs.otsnotes.integration.OrderClient;
import com.sgs.preorder.facade.model.dto.order.OrderInfoDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 *
 */
@Api(value = "gpn report管理", tags = "gpn report管理")
@RestController
@RequestMapping(value = "/report")
@AllArgsConstructor
@Slf4j
public class GpnReportController extends BaseController {

	private final IGpnReportService reportService;
	private final ReportMapper reportMapper;
	private final ReportReworkService reportReworkService;
	private final OrderClient orderClient;
	/**
	 * 搜索分页
	 * @param request
	 * @return
	 */
	@PostMapping("/page")
	@ApiOperation(value = "report list page", notes = "report list page")
	public BaseResponse<PageInfo<GpnOrderReportDTO>> queryReportPage(@RequestBody ReportQueryPageRequest request,
																	 @RequestParam Integer pageNo,
																	 @RequestParam Integer pageSize) {
		return reportService.queryReportPage(request,pageNo,pageSize);
	}

	/**
	 * report列表获取review、confirm数量
	 * @param request
	 * @return
	 */
	@PostMapping("/get/todo/quantity")
	@ApiOperation(value = "report list page", notes = "report list page")
	@AccessRule
	public BaseResponse<GpnOrderReportDTO> getToDoQuantity(@RequestBody ReportQueryPageRequest request) {
		return reportService.getToDoQuantity(request);
	}



	/**
	 * 搜索 order detail
	 * @return
	 */
	@PostMapping("/detail")
	@ApiOperation(value = "report detail page", notes = "report detail page")
	public BaseResponse<List<GpnOrderReportDetailDTO>> queryReportDetail(@RequestParam("orderNo") String orderNo) {
		return reportService.queryReportByOrderNo(orderNo);
	}

	/**
	 * 搜索 order detail
	 * @return
	 */
	@PostMapping("/getReportById")
	@ApiOperation(value = "report detail page", notes = "report detail page")
	public BaseResponse<GpnOrderReportDetailDTO> getReportById(@RequestParam("reportId") String reportId,@RequestParam("reportType") int reportType) {
		return reportService.getReportById(reportId);
	}


	@PostMapping("/generate")
	@ApiOperation(value = " generate report  page", notes = "generate report  page")
	@AccessRule(isDistLock = true,policyType = com.sgs.framework.security.enums.PolicyType.Report, policyActionType = PolicyActionType.Report_Generate)
	public BaseResponse generateReport(@RequestBody ReportForGenerateRequest request) {
		return reportService.generateReport(request);
	}

	@PostMapping("/checkGenerateTestResult")
	public BaseResponse checkGenerateTestResult(@RequestBody ReportForGenerateRequest request) {
		return reportService.checkGenerateTestResult(request);
	}

	@PostMapping("/checkBatchGenerateTestResult")
	public BaseResponse checkBatchGenerateTestResult(@RequestBody ReportForGenerateRequest request) {
		return reportService.checkBatchGenerateTestResult(request);
	}

	@PostMapping("/generateTestResult")
	@ApiOperation(value = " Generate Test Result", notes = "Generate Test Result")
	@AccessRule(isDistLock = true,policyType = com.sgs.framework.security.enums.PolicyType.Report, policyActionType = PolicyActionType.Report_Generate_Test_Result)
	public BaseResponse generateTestResult(@RequestBody ReportForGenerateRequest request) {
		return reportService.generateTestResult(request);
	}

	@PostMapping("/batchGenerateTestResult")
	@ApiOperation(value = " Generate Test Result", notes = "Generate Test Result")
	public BaseResponse batchGenerateTestResult(@RequestBody BatchGenerateTestResultRequest request) {
		if(Func.isEmpty(request.getGenerateTestResultReqs())){
			return BaseResponse.newFailInstance("Params is null");
		}
		return reportService.batchGenerateTestResult(request);
	}


	@PostMapping("/batchDownloadTestResult")
	@ApiOperation(value = "Batch Download Test Result", notes = "Batch Download Test Result")
	public BaseResponse batchDownloadTestResult(@RequestBody BatchGenerateTestResultRequest request,HttpServletResponse response) {
		if(Func.isEmpty(request.getGenerateTestResultReqs())){
			return BaseResponse.newFailInstance("Params is null");
		}
		return reportService.batchDownloadTestResult(request,response);
	}


	@PostMapping("/checkGenerateReport")
	@ApiOperation(value = " generate report  page", notes = "generate report  page")
	public BaseResponse checkGenerateReport(@RequestBody ReportForGenerateRequest request) {
		return reportService.checkGenerateReport(request);
	}

//	@PostMapping("/generateDraftPdf")
//	@ApiOperation(value = " generate draft  page", notes = "generate draft  page")
//	public BaseResponse generateDraftPdf(@RequestBody SaveReportRequest request) {
//		return reportService.generateDraftPdf(request);
//	}

	@PostMapping("/getQrcode")
	@ApiOperation(value = " get qrcode page", notes = " get qrcode  page")
	@AccessRule
	public BaseResponse<String> getQrcode(@RequestBody GpnGetQrcodeReq request) {
		return reportService.getQrcode(request);
	}

	@PostMapping("/getRstsQrcode")
	@ApiOperation(value = "get rsts qrcode page", notes = "get rsts qrcode page")
	public BaseResponse<String> getRstsQrcode(@RequestBody GpnGetQrcodeReq request) {
		return reportService.getRstsQrcode(request);
	}

	@PostMapping("/getReportFile")
	@ApiOperation(value = " get qrcode page", notes = " get qrcode  page")
	public BaseResponse<List<ReportFilePO>> getReportFile(@RequestBody GpnGetFileRequest request) {
		return reportService.getReportFile(request);
	}
	@PostMapping("/getReportFileById")
	@ApiOperation(value = " getReportFileById", notes = " getReportFileById")
	public BaseResponse<List<ReportFilePO>> getReportFileById(@RequestBody GpnGetFileRequest request) {

		ReportDTO byReportNo = reportMapper.getByReportNo(request.getReportNo());
		request.setReportId(byReportNo.getId());
		return reportService.getReportFile(request);
	}

	@PostMapping("/host-approve")
	@AccessRule(isDistLock = true,policyType = com.sgs.framework.security.enums.PolicyType.Report,statuses = {"203"},policyActionType = PolicyActionType.Report_Review)
	public BaseResponse approveReport(@RequestBody ReportIdReq reportIdReq) {
		return reportService.hostApprove(reportIdReq);
	}

	@PostMapping("/approve")
	@AccessRule(isDistLock = true,policyType = com.sgs.framework.security.enums.PolicyType.Report ,policyActionType = PolicyActionType.Report_Approve)
	public BaseResponse approveReport(@RequestBody SaveReportRequest request) {
		request.setApproverByIsNullUseCurrUser(true);
		return reportService.approveReport(request);
	}

	@PostMapping("/checkApproveReport")
	public BaseResponse checkApproveReport(@RequestBody SaveReportRequest request) {
		return reportService.checkApproveReport(request);
	}


	@PostMapping("/save")
	public BaseResponse saveReport(@RequestBody SaveReportRequest request) {
		return BaseResponse.newSuccessInstance(reportService.saveReport(Lists.newArrayList(request)));
	}

	@PostMapping("/saveReportConclusion")
	public BaseResponse saveReportConclusion(@RequestBody GpnSaveReportConclusionRequest request) {
		reportService.saveOrUpdateConclusion(request.getReportId(),request.getReportNo(),request.getConclusionId(),null);
		return BaseResponse.newSuccessInstance(true);
	}

	@PostMapping("/getDeliverPermission")
	public BaseResponse getDeliverPermission(@RequestParam("reportId") String reportId) {
		return BaseResponse.newSuccessInstance(reportService.deliverPermission(reportId,null));
	}

	@PostMapping("/getEmailModel")
	public BaseResponse<EmailModelDTO> getEmailModel(@RequestBody DeliverReportEmailRequest request) {
		return reportService.getEmailModel(request);
	}

	@PostMapping("/afterSendEmail")
	public BaseResponse afterSendEmail(@RequestBody DeliverReportEmailRequest request) {
		return reportService.afterSendEmail(request);
	}

	/**
	 * upload 上传方法， upload 原有组件，
	 * report 先不处理业务，业务处理在 afterUploadWord 方法
	 * @param request
	 * @return
	 */
	@PostMapping("/upload/save")
	public BaseResponse saveSubReport(@RequestBody ReportUploadFileReq request) {
		return BaseResponse.newSuccessInstance(true);
	}

	@PostMapping("/reject")
	@AccessRule
	public BaseResponse rejectReport(@RequestBody RejectReportReq request) {
		return reportService.rejectReport(request);
	}

	@PostMapping("/delete")
	@AccessRule(isDistLock = true,policyType = com.sgs.framework.security.enums.PolicyType.Report)
	public BaseResponse delete(@RequestBody ReportIdRequest request) {
		if(Func.isEmpty(request.getReportId()))
		{
			BaseResponse.newFailInstance("ReportId is null.");
		}
		return reportService.delete(request);
	}


	@PostMapping("/cancel")
	@AccessRule(isDistLock = true,policyType = com.sgs.framework.security.enums.PolicyType.Report)
	public BaseResponse cancel(@RequestBody ReportIdRequest request) {
		if(Func.isEmpty(request.getReportId()))
		{
			BaseResponse.newFailInstance("ReportId is null.");
		}
		return reportService.cancel(request);
	}

	@RequestMapping(value = "/uploadTlTemplate", method = RequestMethod.POST)
	public BaseResponse uploadTlTemplate(@RequestBody UploadTLTemplateFileRequest request) {
		return reportService.uploadTlTemplate(request);
	}

	@RequestMapping(value = "/afterUploadWord", method = RequestMethod.POST)
	public BaseResponse afterUploadWord(@RequestBody UploadReportFileRequest request) {
		return reportService.afterUploadWord(request);
	}

	@RequestMapping(value = "/getReportIdByReportNo", method = RequestMethod.POST)
	public BaseResponse getReportIdByReportNo(@RequestBody ReportNoReq reportNoReq) {
		return reportService.getReportIdByReportNo(reportNoReq);
	}

	@RequestMapping(value = "/downloadFileByClouldId", method = RequestMethod.POST)
	public BaseResponse downloadFileByClouldId(@RequestBody GpnDownloadFrameworkFileRequest request) {
		return reportService.downloadFileByClouldId(request);
	}
	@RequestMapping(value = "/reportReworkSubmit", method = RequestMethod.POST)
	@AccessRule(isDistLock = true,policyType = com.sgs.framework.security.enums.PolicyType.Report ,policyActionType = PolicyActionType.REPORT_REVISE_APPLY)
	public BaseResponse reportReworkSubmit( ReportReworkSubmitReq reqParams,@RequestParam("reworkFileList") MultipartFile[] reworkFiles){
		//ProductLineContextHolder.setProductLineCode("SL");
		BaseResponse response1 = reportService.reportReworkSubmit(reqParams,reworkFiles);
		//BaseResponse response = BaseResponse.newSuccessInstance(ProductLineServiceHolder.getProductLineService(IGpnReportService.class).reportReworkSubmit(reqParams));
		return response1;
	}

	@RequestMapping(value = "/queryRelReportList", method = RequestMethod.POST)
	public BaseResponse<SubReportRelReportRsp> queryRelReportList(@RequestBody ReportIdRequest request){
		return reportService.queryRelReportList(request.getReportNo(),request.getOrderNo());
	}

	@RequestMapping(value = "/searchReportReworkApplyRecordList", method = RequestMethod.POST)
	public BaseResponse searchReportReworkApplyRecordList(@RequestBody ReportReworkSearchReq reqParams){
		//ProductLineContextHolder.setProductLineCode("SL");
		BaseResponse response1 = reportService.searchReportReworkApplyRecordList(reqParams);
		return response1;
	}

	/**
	 * 查询reportRework数量
	 * @param reqParams
	 * @return
	 */
	@RequestMapping(value = "/report/rework/count", method = RequestMethod.POST)
	public BaseResponse getReportReworkCount(@RequestBody ReportReworkSearchReq reqParams){
		return reportService.getReportReworkCount(reqParams);
	}

	@RequestMapping(value = "/reportReworkApproval", method = RequestMethod.POST)
	public BaseResponse reportReworkApproval(@RequestBody ReportReworkApprovalReq reqParams){
		//ProductLineContextHolder.setProductLineCode("SL");
		BaseResponse response1 = reportService.reportReworkApproval(reqParams);
		return response1;
	}
	@RequestMapping(value = "/reworkReport", method = RequestMethod.POST)
	//@AccessRule(needToken = false)
	public BaseResponse reworkReport(@RequestBody ReportReworkApprovalReq reqParams){
		//ProductLineContextHolder.setProductLineCode("SL");
		return reportService.amendReportApproval(reqParams);
	}
	@RequestMapping(value = "/getReworkPage", method = RequestMethod.GET)
	public BaseResponse getReworkPage( @RequestBody ReportIdReq reportIdReq){
		return null;
	}

	@RequestMapping("/reportReworkApproverExportList")
	@ApiOperation(value = "导出 order parcel", notes = "导出 order parcel")
	public void exportParcel(HttpServletResponse response, String startDate, String endDate, String sgsToken,String productLineCode) {
		try {
			ReportReworkApproverExportReq reqParams = new ReportReworkApproverExportReq();
			ProductLineContextHolder.setProductLineCode(productLineCode);
			reqParams.setToken(sgsToken);
			reqParams.setStartTime(DateUtils.parseDate(startDate, "yyyy-MM-dd"));
			Date endTime = DateUtils.parseDate(endDate, "yyyy-MM-dd");
			endTime = DateUtils.addDays(endTime,1);
			endTime = DateUtils.addSeconds(endTime,-1);
			reqParams.setEndTime(endTime);

			CustomResult<List<ReportReworkApproverExportRsp>> result = reportReworkService.reportReworkApproverExportList(reqParams);
			ExportUtils.export(result.getData(),"classpath:export_template/reportRework.xml");
		}catch (Exception ex){
			ex.printStackTrace();
		}
	}

	/**
	 * pend unpend
	 * @param request
	 * @return
	 */
	@BizLog(bizType = BizLogConstant.REPORT_OPERATION_HISTORY, operType = "Pendind/Unpend")
	@RequestMapping(value = "/afterPendAction", method = RequestMethod.POST)
	public BaseResponse afterPendAction(@RequestBody GpnPendActionRequest request) {

		if(1==request.getOperationType())
		{
			BizLogHelper.setValue(request.getOrderNo(),"Pending. Reason:"+request.getOperationInfo()+",Remark:"+request.getRemark());
		}else if(2==request.getOperationType()){
			BizLogHelper.setValue(request.getOrderNo(),"Unpend. Remark:"+request.getRemark());

		}
		OrderInfoDto orderInfoDto = orderClient.getOrderInfoByOrderNo(request.getOrderNo());
		if (Func.isNotEmpty(orderInfoDto)){
			BizLogHelper.setLabCode(orderInfoDto.getBUCode(), orderInfoDto.getLocationCode());
		}
		return BaseResponse.newSuccessInstance(true);
	}

	@ResponseBody
	@AccessPolicyRule(reportStatus = { ReportStatus.Draft }, policyType = PolicyType.Report, isInclude = true)
	@RequestMapping(value = "/uploadPdf", method = RequestMethod.POST)
	@BizLog(bizType = BizLogConstant.REPORT_OPERATION_HISTORY, operType = "UploadPdf")
	@AccessRule(isDistLock = true,policyType = com.sgs.framework.security.enums.PolicyType.Report)
	public BaseResponse<UploadPdfRsp> uploadPdf(@RequestBody UploadPdfReq uploadPdfDTO) {
		return reportService.uploadPdf(uploadPdfDTO);
	}

	//在线预览使用，因格式不支持，暂时注释
	/*@GetMapping("/pdfSubStream/{cloudID}")
	public void getSubPDFStream(@PathVariable(name = "cloudID") String cloudID,HttpServletResponse response){
		UserInfo user = SecurityUtil.getUser();
		if(user==null){
			return;
		}
		String pdfUrl = frameWorkClient.downloadByCloudID(Constants.GPO_SYSTEM_ID,cloudID,"2");
		if(StringUtils.isNotEmpty(pdfUrl)){
			response.setContentType("application/pdf");
			response.setHeader("Access-Control-Allow-Headers","Accept-Ranges");
			//response.setHeader("Content-Disposition", "attachment;fileName=" + UUID.randomUUID().toString()+".pdf");
			InputStream in = null;
			OutputStream outputStream =null;
			try{
				URL url = new URL(pdfUrl);
				HttpURLConnection conn = (HttpURLConnection)url.openConnection();
				in=conn.getInputStream();
				outputStream = response.getOutputStream();
				//in = (InputStream) pdfStream;
				int len = 0;
				byte[] buffer = new byte[1024];
				while((len=in.read(buffer))>0){
					outputStream.write(buffer,0,len);
				}
				outputStream.flush();
			}catch (Exception e){
				e.printStackTrace();
			}finally {
				if(in!=null){
					try {
						in.close();
					} catch (IOException e) {
						e.printStackTrace();
					}
				}
				if(outputStream!=null){
					try {
						outputStream.close();
					} catch (IOException e) {
						e.printStackTrace();
					}
				}
			}
		}
	}*/
	@GetMapping("/pdfStream/{orderNo}")
	public void getPDFStream(@PathVariable(name = "orderNo") String orderNo,HttpServletResponse response){
		if(StringUtils.isBlank(orderNo)){
			return;
		}
		UserInfo user = SecurityUtil.getUser();
		if(user==null){
			return;
		}
		log.info("orderNo:{} user:{} preview pdf",orderNo,user.getRegionAccount());
		String pdfUrl = this.reportService.getPDFUrl(orderNo);
		if(StringUtils.isNotEmpty(pdfUrl)){
			response.setContentType("application/pdf");
			response.setHeader("Access-Control-Allow-Headers","Accept-Ranges");
			//response.setHeader("Content-Disposition", "attachment;fileName=" + UUID.randomUUID().toString()+".pdf");
			InputStream in = null;
			OutputStream outputStream =null;
			try{
				URL url = new URL(pdfUrl);
				HttpURLConnection conn = (HttpURLConnection)url.openConnection();
				in=conn.getInputStream();
				outputStream = response.getOutputStream();
				//in = (InputStream) pdfStream;
				int len = 0;
				byte[] buffer = new byte[1024];
				while((len=in.read(buffer))>0){
					outputStream.write(buffer,0,len);
				}
				outputStream.flush();
			}catch (Exception e){
				log.info("orderNo:{} user:{} preview pdf fail",orderNo,user.getRegionAccount());
				e.printStackTrace();
			}finally {
				if(in!=null){
					try {
						in.close();
					} catch (IOException e) {
						e.printStackTrace();
					}
				}
				if(outputStream!=null){
					try {
						outputStream.close();
					} catch (IOException e) {
						e.printStackTrace();
					}
				}
			}
		}
	}

	@PostMapping("/batchRegistration")
	@ApiOperation(value = " batch generate report", notes = "batch generate report")
	@AccessRule(isDistLock = true,policyType = com.sgs.framework.security.enums.PolicyType.Order)
	public BaseResponse batchRegistration(@RequestBody ReportBatchRegistrationReq request) {
		BaseResponse result = new BaseResponse<>();
		if(Func.isBlank(request.getOrderNo())){
			result.setStatus(500);
			result.setMessage("OrderNo is null");
			return result;
		}
		if(request.getActionType()==null||request.getActionType().intValue()==0){
			result.setStatus(500);
			result.setMessage("ActionType is null");
			return result;
		}
		if(request.getQty()!=null){
			if(request.getQty().intValue()<1||request.getQty().intValue()>=100){
				result.setStatus(500);
				result.setMessage("Qty Cannot be greater than 100 or less than 1");
				return result;
			}
		}
		request.setOrderStatusCheck(true);
		return reportService.batchRegistration(request);
	}

	/**
	 * 注册报告之前校验必须Assign Matrix
	 * @param request
	 * @return
	 */
	@PostMapping("/batchRegistrationNew")
	@ApiOperation(value = " batch generate report", notes = "batch generate report")
	@AccessRule(isDistLock = true,policyType = com.sgs.framework.security.enums.PolicyType.Order)
	public BaseResponse batchRegistrationNew(@RequestBody ReportBatchRegistrationReq request) {
		BaseResponse result = new BaseResponse<>();
		if(Func.isBlank(request.getOrderNo())){
			result.setStatus(500);
			result.setMessage("OrderNo is null");
			return result;
		}
		if(request.getActionType()==null||request.getActionType().intValue()==0){
			result.setStatus(500);
			result.setMessage("ActionType is null");
			return result;
		}

		return reportService.batchRegistrationNew(request);
	}


	@PostMapping("/matrix/list")
	@ApiOperation(value = "matrix list", notes = "matrix list")
	public BaseResponse matrixList(@RequestBody ReportMatrixListReq request) {
		BaseResponse result = new BaseResponse<>();
		if(Func.isBlank(request.getReportNo()) && Func.isBlank(request.getOrderNo())){
			result.setStatus(500);
			result.setMessage("ReportNo is null");
			return result;
		}

		return reportService.matrixList(request);
	}
	@PostMapping("/reportSeqList")
	@ApiOperation(value = " reportSeqList ", notes = "reportSeqList")
	public BaseResponse reportSeqList(@RequestBody ReportSeqListReq request) {
		return reportService.reportSeqList(request);
	}
	@PostMapping("/confirmReportSeq")
	@ApiOperation(value = "confirmReportSeq", notes = "confirmReportSeq")
	public BaseResponse confirmReportSeq(@RequestBody List<ReportTestLineRsp> request) {
		return reportService.confirmReportSeq(request);
	}

	@PostMapping("/matrix/assign")
	@ApiOperation(value = "assign matrix", notes = "assign matrix")
	public BaseResponse assignMatrix(@RequestBody ReportAssignMatrixReq request) {
		BaseResponse result = new BaseResponse<>();
		if(Func.isBlank(request.getReportNo())){
			result.setStatus(500);
			result.setMessage("ReportNo is null");
			return result;
		}
		if(Func.isEmpty(request.getReportAssignTestLineList()) && Func.isEmpty(request.getReportAssignPPTestLineList())){
			result.setStatus(500);
			result.setMessage("AssignTestLineList is Empty");
			return result;
		}
		String reportTestLineLevel = request.getReportTestLineLevel();
		List<ReportAssignTestLineReq> reportAssignTestLineList = request.getReportAssignTestLineList();
		List<ReportAssignTestLineReq> reportPPTestLineList = request.getReportAssignPPTestLineList();
		List<ReportAssignTestLineReq> reportAssignTestLineNew = new ArrayList<>();
		//处理成TL+Sample
		if("PP".equals(reportTestLineLevel)){
			for(ReportAssignTestLineReq reportTestLine : reportPPTestLineList){
				if(Func.isEmpty(reportTestLine.getTestLineId())){
					//PP添加
					String ppNo = reportTestLine.getPpNo();
					List<ReportAssignTestLineReq> testLine = reportAssignTestLineList.stream().filter(e -> Func.equalsSafe(e.getPpNo(),ppNo)).collect(Collectors.toList());
					if(Func.isNotEmpty(testLine)){
						for(ReportAssignTestLineReq req : testLine){
							req.setTestSampleList(reportTestLine.getTestSampleList());
							reportAssignTestLineNew.add(req);
						}
					}
				} else {
					reportAssignTestLineNew.add(reportTestLine);
				}
			}
		} else{
			reportAssignTestLineNew.addAll(reportAssignTestLineList);
		}
		request.setReportAssignTestLineList(reportAssignTestLineNew);
		return reportService.assignMatrix(request);
	}

	@PostMapping("/matrix/confirm")
	@ApiOperation(value = "confirm matrix", notes = "confirm matrix")
	public BaseResponse confirmMatrix(@RequestBody ReportConfirmMatrixReq request) {
		BaseResponse result = new BaseResponse<>();
		if(Func.isBlank(request.getOrderNo())){
			result.setStatus(500);
			result.setMessage("OrderNo is null");
			return result;
		}
		return reportService.confirmMatrix(request);
	}
	@PostMapping("/reportConfirm")
	@AccessRule(isDistLock = true,policyType = com.sgs.framework.security.enums.PolicyType.Report ,policyActionType = PolicyActionType.Report_Confirm)
	public BaseResponse reportConfirm(@RequestBody ReportConfirmReq request) {
		request.setSendEmail(false);
		return reportService.reportConfirm(request,"Report to Cust. Confirm ");
	}



	/**
	 * 邮件拒绝report
	 * @param request
	 * @return
	 */
	@PostMapping("email/report/approve")
	@AccessRule(needToken = false,needProductLine = false, isDistLock = true,policyType = com.sgs.framework.security.enums.PolicyType.Report,policyActionType = PolicyActionType.Report_Approve)
	public BaseResponse emailReportApprove(ReportIdRequest request, @RequestParam("fileList") MultipartFile[] files) {
		String id = request.getReportId();
		String productLineCode = request.getProductLineCode();
		String fromEmail = request.getFromEmail();
		String labCode = request.getLabCode();
		if(Func.isNotEmpty(fromEmail) && fromEmail.length()>50){
			fromEmail = StringUtils.substring(fromEmail,0,50);
			request.setFromEmail(fromEmail);
		}
		if(Func.isEmpty(fromEmail)){
			fromEmail = "Email Operation";
		}
		if(ReportApproveType.check(request.getReportApproveType(), ReportApproveType.Review)){
			if(Constants.WORKFLOW.APPROVE.equals(request.getAction())){
				return reportService.emailReportReview(id,productLineCode,fromEmail,labCode);
			}else if(Constants.WORKFLOW.REJECT.equals(request.getAction())){
				return reportService.emailReportRejectReport(request,files);
			}
		}
		if(ReportApproveType.check(request.getReportApproveType(), ReportApproveType.Confirm)){
			if(Constants.WORKFLOW.APPROVE.equals(request.getAction())){
				return reportService.emailReportConfirm(id,productLineCode,fromEmail);
			}else if(Constants.WORKFLOW.REJECT.equals(request.getAction())){
				return reportService.emailReportRejectReport(request,files);
			}else if(Constants.WORKFLOW.DELAY.equals(request.getAction())){
				return reportService.delayReportNotify(request);
			}
		}
		return BaseResponse.newSuccessInstance(true);
	}

	@RequestMapping("/cancelledReportToRsts")
	@ApiOperation(value = "cancelledReportToRsts", notes = "cancelledReportToRsts")
	public BaseResponse cancelledReportToRsts(@RequestBody ReworkReportInfoReq request) {
		BaseResponse result = new BaseResponse<>();
		if(Func.isBlank(request.getProductLineCode())){
			result.setStatus(500);
			result.setMessage("ProductLineCode is null");
			return result;
		}
		ProductLineContextHolder.setProductLineCode(request.getProductLineCode());

		return reportService.cancelledReportToRsts(request);
	}

	/**
	 * 修复数据接口
	 * @return
	 */
	@RequestMapping("/get/cancelledReportToRsts/data/repair")
	public BaseResponse cancelledReportToRstsDataRepair() {
		ProductLineContextHolder.setProductLineCode("MR");

		return reportService.cancelledReportToRstsDataRepair();
	}

	@PostMapping("/deliverApprove")
	@AccessRule(isDistLock = true,policyType = com.sgs.framework.security.enums.PolicyType.Report)
	public BaseResponse deliverApprove(@RequestBody ReportIdRequest request) {
		if(Func.isEmpty(request.getReportId()))
		{
			BaseResponse.newFailInstance("ReportId is null.");
		}
		return reportService.deliverApprove(request);
	}

	@PostMapping("/finalReport/checkIsDelivered")
	public BaseResponse checkIsDeliverForFinal(@RequestParam("reportId") String reportId,@RequestParam("orderNo") String orderNo) {
		return BaseResponse.newSuccessInstance(reportService.checkIsDeliverForFinal(reportId,orderNo));
	}

	@PostMapping("/draftReport/checkIsDelivered")
	public BaseResponse checkIsDeliverForDraft(@RequestParam("reportId") String reportId,@RequestParam("orderNo") String orderNo) {
		return BaseResponse.newSuccessInstance(reportService.checkIsDeliverForDraft(reportId,orderNo));
	}

	@PostMapping("/renew")
	@ApiOperation(value = "report  renew", notes = "report  renew")
	@AccessRule(isDistLock = true,policyType = com.sgs.framework.security.enums.PolicyType.Report ,policyActionType = PolicyActionType.Report_RENEW)
	public BaseResponse renew(@RequestBody RenewReq request) {
		return reportService.renew(request);
	}

	/**
	 * renew前校验接口
	 * @param reportId
	 * @return
	 */
	@RequestMapping(value = "/renew/check/{reportId}",method = RequestMethod.GET)
	@ApiOperation(value = "renew前校验接口",notes = "renew前校验接口")
	public BaseResponse renewCoverPageCheck(@PathVariable String reportId){
		return reportService.renewCoverPageCheck(reportId);
	}

	/**
	 * renew前校验接口
	 * @param reNewCheckReq
	 * @return
	 */
	@RequestMapping(value = "/renew/check",method = RequestMethod.POST)
	@ApiOperation(value = "renew前校验接口",notes = "renew前校验接口")
	public BaseResponse renewCheck(@RequestBody ReNewCheckReq reNewCheckReq){
		return reportService.renewCheck(reNewCheckReq);
	}
	/**
	 * cancel前校验接口
	 * @param req
	 * @return
	 */
	@RequestMapping(value = "/cancel/check",method = RequestMethod.POST)
	@ApiOperation(value = "cancelReport前校验接口",notes = "renew前校验接口")
	public BaseResponse cancelCheck(@RequestBody ReportIdReq req){
		return reportService.cancelCheck(req);
	}


	/**
	 * renewCoverPage提交
	 * @param reportId
	 * @return
	 */
	@RequestMapping(value = "/renew/cover/page",method = RequestMethod.GET)
	@ApiOperation(value = "renewCoverPage",notes = "renewCoverPage")
	@AccessRule(isDistLock = true,policyType = com.sgs.framework.security.enums.PolicyType.Report ,policyActionType = PolicyActionType.Report_RENEW)
	public BaseResponse renewCoverPage(@RequestParam("reportId") String reportId){
		return reportService.renewCoverPage(reportId);
	}

	/**
	 * renewSampleInfo提交a
	 * @param reportRenewSampleReq
	 * @return
	 */
	@RequestMapping(value = "/renew/sample/Info",method = RequestMethod.POST)
	@ApiOperation(value = "renewSampleInfo",notes = "renewSampleInfo")
	@AccessRule(isDistLock = true,policyType = com.sgs.framework.security.enums.PolicyType.Report ,policyActionType = PolicyActionType.Report_RENEW)
	public BaseResponse renewSampleInfo(@RequestBody ReportRenewSampleReq reportRenewSampleReq){
		return reportService.renewSampleInfo(reportRenewSampleReq);
	}

	/**
	 * Renew Cover Page & Test Result提交
	 * @param reportId
	 * @return
	 */
	@RequestMapping(value = "/renew/test/result",method = RequestMethod.GET)
	@ApiOperation(value = "renewCoverPage",notes = "renewCoverPage")
	@AccessRule(isDistLock = true,policyType = com.sgs.framework.security.enums.PolicyType.Report ,policyActionType = PolicyActionType.Report_RENEW)
	public BaseResponse renewTestResult(@RequestParam("reportId") String reportId){
		return reportService.renewTestResult(reportId);
	}
//	@RequestMapping(value = "/aaa",method = RequestMethod.GET)
//	public BaseResponse aaa(){
////		String aaa =  reportService.deliverySoftCopy("655faff7-9566-4cf4-a2a1-775f5ab528e9","MR","emily-lp_guo","GZ MR");
////		String aaa =  reportService.deliverySoftCopy("7b9eeb09-2fd2-4781-abd0-c075be129cef","HL","Star_Li","SH HL");
////		String aaa = reportService.deliverySendEmail("c27526f5-41f8-49a2-97fe-a55171c4bbc7","HL");
////		String ccc = reportService.deliverySendEmail("a78d5776-b470-4abe-8662-8229aecb73b4","MR");
//		BaseResponse ccc = iGpnSubReportService.deliverySubReport("5656eb08-7a5d-4852-847d-0879f96cf947");
//		BaseResponse baseResponse = new BaseResponse();
//		return baseResponse;
//	}

	/**
	　　* @description: TypeFinished
	　　* @param reportIdList
	　　* @return BaseResponse
	　　* @throws
	　　* <AUTHOR>
	　　* @date 2021/11/17 19:13
	　　*/
	@RequestMapping(value = "/typeFinished",method = RequestMethod.POST)
	@ApiOperation(value = "typeFinished",notes = "Type Finished")
	public BaseResponse typeFinished(@RequestBody List<String> reportIdList){
		log.info("reportIdList:{}",reportIdList);
		TypeFinishedReq typeFinishedReq = new TypeFinishedReq();
		typeFinishedReq.setReportIdList(reportIdList);
		typeFinishedReq.setToken(SecurityContextHolder.getSgsToken());
		return reportService.typeFinished(typeFinishedReq);
	}


	/**
	 * 全量更新certificateName
	 * @return
	 */
	@RequestMapping(value = "/update/certificateName",method = RequestMethod.GET)
	public BaseResponse updateCertificate(){
		return reportService.updateCertificate();
	}

	/**
	 * 批量生成报告之前获取模板
	 * @param batchGenerateReq
	 * @return
	 */
	@RequestMapping(value = "/batch/generate/template",method = RequestMethod.POST)
	public BaseResponse batchGenerateTemplate(@RequestBody BatchGenerateReq batchGenerateReq){
		return reportService.batchGenerateTemplate(batchGenerateReq);
	}

	/**
	 * 批量生成报告之前校验
	 * @param batchGenerateReq
	 * @return
	 */
	@RequestMapping(value = "batch/generate/check",method = RequestMethod.POST)
	public BaseResponse<BatchCheckGenerateReportRsp> batchGenerateCheck(@RequestBody BatchGenerateReq batchGenerateReq){
		return reportService.batchGenerateCheck(batchGenerateReq);
	}

	/**
	 * 批量生成报告
	 * @param request
	 * @return
	 */
	@RequestMapping(value = "batch/generate",method = RequestMethod.POST)
	public BaseResponse batchGenerate(@RequestBody ReportIdRequest request){
		return reportService.batchGenerate(request);
	}

	/**
	 * 批量生成报告下载校验
	 * @param batchGenerateReq
	 * @return
	 */
	@RequestMapping(value = "batch/generate/download/check",method = RequestMethod.POST)
	public BaseResponse batchGenerateDownloadCheck(@RequestBody BatchGenerateReq batchGenerateReq){
		return reportService.batchGenerateDownloadCheck(batchGenerateReq);
	}

	/**
	 * 批量生成报告下载
	 * @param request
	 * @param response
	 * @return
	 */
	@RequestMapping(value = "batch/generate/download",method = RequestMethod.GET)
	@ResponseBody
	public void batchGenerateDownload(ReportForBatchGenerateReq request, HttpServletResponse response){
		 reportService.batchGenerateDownload(request,response);
	}
	/**1
	 * 处理trackingMR的历史问题数据
	 * @return
	 */
	@RequestMapping(value = "dealMRProdUpdateTrackingOrderInfo",method = RequestMethod.POST)
	public @ResponseBody BaseResponse dealMRProdUpdateTrackingOrderInfo(@RequestBody Map<String,Object> req){
		reportService.dealMRProdUpdateTrackingOrderInfo(req);
		return BaseResponse.newSuccessInstance(true);
	}

	/**
	 * 查询bu下report workflow
	 * @return
	 */
	@RequestMapping(value = "/get/work/flow",method = RequestMethod.GET)
	public @ResponseBody BaseResponse getWorkFlowByBu(){
		return reportService.getWorkFlowByBu(null,null);
	}


	/**
	 *外部系统报告回写
	 */
	@PostMapping(value = "/returnSubContractReport")
	public BaseResponse returnSubContractReport(@RequestBody ReturnSubContractReportReq returnSubContractReportReq) {
		return reportService.returnSubContractReport(returnSubContractReportReq);
	}


	@PostMapping(value = "/auto/save/template")
	public BaseResponse autoSaveTemplate(@RequestBody AutoSaveTemplateReq autoSaveTemplateReq) {
		return reportService.autoSaveTemplate(autoSaveTemplateReq);
	}


	/**
	 * 根据reportNo获取report matrix信息
	 * @param reportNo
	 * @return
	 */
	@RequestMapping(value = "/get/report/matrix/info/{reportNo}",method = RequestMethod.GET)
	public BaseResponse getReportMatrixByReportNo(@PathVariable String reportNo){
		return reportService.getReportMatrixByReportNo(reportNo);
	}
	/**
	 * 根据reportNo获取report matrix信息
	 * @param
	 * @return
	 */
	@RequestMapping(value = "/get/config",method = RequestMethod.GET)
	public BaseResponse getReportConfig(){
		return reportService.getReportConfig();
	}

	/**
	 *
	 * @param req
	 * @return
	 */
	@PostMapping(value = "/printReviseForm")
	public BaseResponse printReportReviseForm(@RequestBody ReviseReportPrintReq req) {
		return BaseResponse.newSuccessInstance(reportReworkService.printReportReviseForm(req));
	}

	/**
	 * 搜索Repair分页
	 * @param request
	 * @return
	 */
	@PostMapping("/pageRepair")
	@ApiOperation(value = "report list page", notes = "report list page")
	public BaseResponse<PageInfo<GpnOrderReportDTO>> queryRepairReportPage(@RequestBody ReportQueryPageRequest request,@RequestParam Integer pageNo, @RequestParam Integer pageSize) {
		return reportService.queryRepairReportPage(request,pageNo,pageSize);
	}
	@GetMapping("auto-confirm-draft-report/{productLineCode}")
	public BaseResponse autoConfirmDraftReport(@PathVariable String productLineCode) {
		return reportService.autoConfirmDraftReport(productLineCode);
	}
	@GetMapping("auto-notify-draft-report/{productLineCode}")
	public BaseResponse autoNotifyDraftReport(@PathVariable String productLineCode) {
		return reportService.autoNotifyDraftReport(productLineCode);
	}

	@PostMapping(value = "/getLastReject")
	public BaseResponse getLastReject(@RequestBody RejectReportSearchReq req) {
		return reportService.getLastReject(req);
	}

	@PostMapping(value = "/renew/confirm")
	@AccessRule
	public BaseResponse renewConfirm(ReportRenewConfirmReq req,
									 @RequestParam("files") MultipartFile[] files){
		return reportService.renewConfirm(req,files);
	}

	@GetMapping(value = "/query-reject-reason/{reportId}")
	@AccessRule
	public BaseResponse queryRejectReason(@PathVariable String reportId){
		return reportService.queryRejectReason(reportId);
	}
}
