package com.sgs.otsnotes.facade.impl;

import com.google.common.collect.Lists;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.core.base.CustomResult;
import com.sgs.framework.tool.utils.Func;
import com.sgs.otsnotes.core.util.NumberUtil;
import com.sgs.otsnotes.dbstorages.mybatis.extmapper.SubContractExtMapper;
import com.sgs.otsnotes.dbstorages.mybatis.extmapper.SubContractRequirementContactsExtMapper;
import com.sgs.otsnotes.dbstorages.mybatis.extmapper.SubcontractRequirementMapper;
import com.sgs.otsnotes.dbstorages.mybatis.model.SubcontractRequirementContactsPO;
import com.sgs.otsnotes.facade.SubContractRequirementFacade;
import com.sgs.otsnotes.facade.model.info.subcontract.SubContractInfo;
import com.sgs.otsnotes.facade.model.info.subcontract.SubcontractRequirementContactsInfo;
import com.sgs.otsnotes.facade.model.info.subcontract.SubcontractRequirementInfo;
import com.sgs.otsnotes.facade.model.req.GPOSubContractReq;
import com.sgs.otsnotes.facade.model.req.starlims.SubContractContactDTO;
import com.sgs.otsnotes.integration.CustomerClient;
import com.sgs.otsnotes.integration.OrderPersonClient;
import com.sgs.preorder.facade.OrderFacade;
import com.sgs.preorder.facade.model.dto.order.OrderAllDTO;
import com.sgs.preorder.facade.model.enums.ContactsType;
import com.sgs.preorder.facade.model.enums.CustomerType;
import com.sgs.preorder.facade.model.enums.OrderPersonType;
import com.sgs.preorder.facade.model.req.OrderIdReq;
import com.sgs.preorder.facade.model.req.order.OrderPersonInfoReq;
import com.sgs.preorder.facade.model.rsp.customer.CustomerSimplifyInfoRsp;
import com.sgs.preorder.facade.model.rsp.order.OrderPersonInfoRsp;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

@Component("SubContractRequirementFacade")
public class SubContractRequirementFacadeImpl implements SubContractRequirementFacade {

    @Autowired
    private SubcontractRequirementMapper subcontractRequirementMapper;
    @Autowired
    private SubContractRequirementContactsExtMapper subContractRequirementContactsExtMapper;
    @Autowired
    private CustomerClient customerClient;
    @Autowired
    private OrderPersonClient orderPersonClient;
    @Autowired
    private OrderFacade orderFacade;
    @Autowired
    private SubContractExtMapper subContractExtMapper;


    @Override
    public BaseResponse<SubcontractRequirementInfo> getSubcontractRequirementInfo(GPOSubContractReq req) {
        SubcontractRequirementInfo subcontractRequirementInfo=null;
        if(Func.isNotEmpty(req.getSubContractNo())){
            subcontractRequirementInfo = subcontractRequirementMapper.getSubcontractRequirementBySubcontractNo(req.getSubContractNo());
        }else if(Func.isNotEmpty(req.getOrderNo())){
            subcontractRequirementInfo = subcontractRequirementMapper.getSubcontractRequirementByOrderNo(req.getOrderNo());

        }
        if (subcontractRequirementInfo != null && subcontractRequirementInfo.getSubContractID() != null) {
            subcontractRequirementInfo.setSubcontractRequirementContactsInfos(subcontractRequirementMapper.getSubcontractRequirementContactsBySubcontractId(subcontractRequirementInfo.getSubContractID()));
        }
        return BaseResponse.newInstance(subcontractRequirementInfo);
    }

    @Override
    public BaseResponse<SubcontractRequirementContactsInfo> getSubcontractRequirementContactsInfo(GPOSubContractReq req) {
        return BaseResponse.newInstance(subcontractRequirementMapper.getSubcontractRequirementContactsBySubcontractId(req.getSubContractId()));
    }

    @Override
    public BaseResponse<List<SubContractContactDTO>> getSubcontractContactsList(GPOSubContractReq req) {
        BaseResponse<List<SubContractContactDTO>> response = new BaseResponse<>();
        // 查询 Subcontract SoftCopy勾选项
        SubContractInfo subContractInfo = subContractExtMapper.getSubContractInfo(req.getSubContractNo());
        if(Func.isEmpty(subContractInfo)){
            return response;
        }
        SubcontractRequirementContactsPO subReqContacts = subContractRequirementContactsExtMapper.getSubRequirementContactsByOrderId(subContractInfo.getId(), ContactsType.SoftCopy.getType());
        if (subReqContacts == null){
            return response;
        }
        // 查询订单CS SALES信息
        OrderIdReq orderIdReq = new OrderIdReq();
        orderIdReq.setOrderNo(subContractInfo.getOrderNo());
        orderIdReq.setProductLineCode(req.getProductLineCode());
        BaseResponse<OrderAllDTO> orderRes = orderFacade.getOrderForPe(orderIdReq);
        if(Func.isEmpty(orderRes) || Func.isEmpty(orderRes.getData())){
            return response;
        }
        OrderAllDTO orderAllDTO = orderRes.getData();
        // 记录联系人信息
        List<SubContractContactDTO> subContracts = Lists.newArrayList();
        // Others
        SubContractContactDTO subContract = new SubContractContactDTO();
        String deliverOthers = subReqContacts.getDeliver_others();
        if (StringUtils.isNotBlank(deliverOthers)){
            subContract.setContactName(deliverOthers);
            subContract.setContactEmail(deliverOthers);
            subContract.setContactRole("others");
            subContracts.add(subContract);
        }
        String deliverTo = subReqContacts.getDeliver_to();
        if(Func.isNotEmpty(deliverTo)){
            for (String deliver: deliverTo.split(",")) {
                CustomerType customerType = CustomerType.findStatus(NumberUtil.toInt(deliver));
                if (customerType == null){
                    continue;
                }
                subContract = new SubContractContactDTO();
                switch (customerType){
                    case Applicant:
                    case Payer:
                    case Buyer:
                    case Agent:
                    case Supplier:
                    case Manufacture:
                        CustomResult<CustomerSimplifyInfoRsp> rspResult = customerClient.getCustomerSimplifyInfo(subContractInfo.getOrderNo(), customerType);
                        CustomerSimplifyInfoRsp customer = rspResult.getData();
                        if (customer == null){
                            continue;
                        }
                        subContract.setContactName(customer.getContactPersonName());
                        subContract.setContactEmail(customer.getContactPersonEmail());
                        subContract.setContactRole(customerType.getCode());
                        subContracts.add(subContract);
                        break;
                    case SUBCONTRACTFROM:
                    case CS: //
                        subContract.setContactName(orderAllDTO.getcSName());
                        subContract.setContactEmail(orderAllDTO.getcSEmail());
                        subContract.setContactRole(customerType.getCode());
                        subContracts.add(subContract);
                        continue;
                    case SALES:
                            OrderPersonInfoReq reqObject = new OrderPersonInfoReq();
                            reqObject.setOrderId(orderAllDTO.getOrderId());
                            reqObject.setPersonType(OrderPersonType.SALES.getCode());
                            CustomResult<OrderPersonInfoRsp> personResult = orderPersonClient.getPersonInfoByOrderId(reqObject);
                            OrderPersonInfoRsp person = personResult.getData();
                            if (person == null){
                                continue;
                            }
                            subContract.setContactName(person.getRegionAccount());
                            subContract.setContactEmail(person.getEmail());
                            subContract.setContactRole(customerType.getCode());
                            subContracts.add(subContract);
                            continue;
                }
            }
        }
        response.setData(subContracts);
        return response;
    }
}
