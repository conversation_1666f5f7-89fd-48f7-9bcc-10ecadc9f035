# 模块化Web项目架构设计方案

## 1. 整体架构层次

```
Page Layer (页面层)
    ↓
Component Layer (组件层)
    ↓
Function Layer (功能层)
    ↓
Capability Layer (能力层)
    ↓
Module Layer (模块层)
    ↓
Service Domain Layer (服务域层)
```

## 2. 核心注解设计

### 2.1 模块标识注解
```java
@Target(ElementType.TYPE)
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface ModuleTag {
    String value();                    // 模块名称，如 "OrderModule"
    String description() default "";   // 模块描述
    String domain() default "";        // 所属域
}
```

### 2.2 能力标识注解
```java
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface Capability {
    String name();                     // 能力名称
    String description() default "";   // 能力描述
    String[] tags() default {};        // 标签
    Class<? extends Rule>[] rules() default {}; // 执行规则
}
```

### 2.3 功能标识注解
```java
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface Function {
    String name();                     // 功能名称
    String description() default "";   // 功能描述
    String capability();               // 对应的能力
}
```

### 2.4 规则注解
```java
@Target(ElementType.TYPE)
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface RuleDefinition {
    String name();
    String description() default "";
    int priority() default 0;
}
```

## 3. 代码实现示例

### 3.1 订单模块业务服务
```java
@Service
@ModuleTag(value = "OrderModule", description = "订单模块", domain = "Order")
public class OrderBizService {
    
    @Autowired
    private OrderDomainService orderDomainService;
    
    @Capability(
        name = "query", 
        description = "查询订单信息包含日志",
        rules = {OrderIdNotEmptyRule.class}
    )
    public OrderInfo queryOrderInfo(String orderId) {
        // 业务逻辑
        return orderDomainService.queryOrderInfo(orderId);
    }
    
    @Capability(
        name = "save", 
        description = "保存订单信息包含日志",
        rules = {OrderStatusValidRule.class}
    )
    public void saveOrderInfo(OrderInfo orderInfo) {
        // 业务逻辑
        orderDomainService.saveOrderInfo(orderInfo);
        orderDomainService.saveOrderLogInfo(orderInfo);
    }
}
```

### 3.2 域服务
```java
@Service
public class OrderDomainService {
    
    @Function(
        name = "queryOrderInfo", 
        description = "查询订单信息",
        capability = "query"
    )
    public OrderInfo queryOrderInfo(String orderId) {
        // 具体实现
        return null;
    }
    
    @Function(
        name = "queryOrderLogInfo", 
        description = "查询订单日志信息",
        capability = "query"
    )
    public List<OrderLog> queryOrderLogInfo(String orderId) {
        // 具体实现
        return null;
    }
    
    @Function(
        name = "saveOrderInfo", 
        description = "保存订单信息",
        capability = "save"
    )
    public void saveOrderInfo(OrderInfo orderInfo) {
        // 具体实现
    }
    
    @Function(
        name = "saveOrderLogInfo", 
        description = "保存订单日志信息",
        capability = "save"
    )
    public void saveOrderLogInfo(OrderInfo orderInfo) {
        // 具体实现
    }
}
```

### 3.3 规则定义
```java
@RuleDefinition(name = "OrderIdNotEmpty", description = "订单ID不为空")
public class OrderIdNotEmptyRule implements Rule {
    @Override
    public boolean evaluate(Object... params) {
        if (params.length > 0 && params[0] instanceof String) {
            return StringUtils.isNotEmpty((String) params[0]);
        }
        return false;
    }
}

@RuleDefinition(name = "OrderStatusValid", description = "订单状态=2")
public class OrderStatusValidRule implements Rule {
    @Override
    public boolean evaluate(Object... params) {
        if (params.length > 0 && params[0] instanceof OrderInfo) {
            OrderInfo order = (OrderInfo) params[0];
            return order.getStatus() == 2;
        }
        return false;
    }
}
```

## 4. 元数据收集器

### 4.1 注解扫描器
```java
@Component
public class ArchitectureMetadataCollector implements ApplicationContextAware {
    
    private ApplicationContext applicationContext;
    
    @PostConstruct
    public void collectMetadata() {
        Map<String, Object> services = applicationContext.getBeansWithAnnotation(ModuleTag.class);
        
        for (Object service : services.values()) {
            Class<?> serviceClass = service.getClass();
            ModuleTag moduleTag = serviceClass.getAnnotation(ModuleTag.class);
            
            // 收集模块信息
            ModuleMetadata moduleMetadata = new ModuleMetadata();
            moduleMetadata.setModuleName(moduleTag.value());
            moduleMetadata.setDescription(moduleTag.description());
            moduleMetadata.setDomain(moduleTag.domain());
            
            // 收集能力信息
            Method[] methods = serviceClass.getDeclaredMethods();
            for (Method method : methods) {
                if (method.isAnnotationPresent(Capability.class)) {
                    Capability capability = method.getAnnotation(Capability.class);
                    // 收集能力元数据
                }
            }
        }
    }
}
```

## 5. 查询和文档生成

### 5.1 元数据查询服务
```java
@Service
public class ArchitectureQueryService {
    
    public List<ModuleMetadata> findModulesByTag(String tag) {
        // 根据标签查询模块
        return null;
    }
    
    public List<CapabilityMetadata> findCapabilitiesByModule(String moduleName) {
        // 根据模块查询能力
        return null;
    }
    
    public List<FunctionMetadata> findFunctionsByCapability(String capabilityName) {
        // 根据能力查询功能
        return null;
    }
}
```

### 5.2 文档生成器
```java
@Component
public class ArchitectureDocumentGenerator {
    
    public void generateModuleDocument(String moduleName) {
        // 生成模块文档
    }
    
    public void generateCapabilityDocument(String capabilityName) {
        // 生成能力文档
    }
}
```

## 6. 技术实现要点

1. **注解处理器**: 使用Spring的反射机制扫描注解
2. **元数据存储**: 可以存储在内存、数据库或配置文件中
3. **规则引擎**: 实现Rule接口，支持动态规则验证
4. **文档生成**: 基于模板引擎生成Markdown或HTML文档
5. **查询接口**: 提供RESTful API供前端查询

## 7. 扩展功能

1. **依赖关系图**: 可视化模块间依赖关系
2. **性能监控**: 基于注解添加性能监控
3. **版本管理**: 支持模块版本控制
4. **测试覆盖**: 自动生成测试用例模板

这个方案可以有效地实现你的需求，通过注解标记实现模块化管理和快速查询。