<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sgs.otsnotes.dbstorages.mybatis.mapper.ReportDeliverHistoryInfoMapper" >
  <resultMap id="BaseResultMap" type="com.sgs.otsnotes.dbstorages.mybatis.model.ReportDeliverHistoryInfoPO" >
    <id column="id" property="id" jdbcType="VARCHAR" />
    <result column="report_id" property="reportId" jdbcType="VARCHAR" />
    <result column="hardcopy_delivered_date" property="hardcopyDeliveredDate" jdbcType="TIMESTAMP" />
    <result column="hardcopy_delivered_by" property="hardcopyDeliveredBy" jdbcType="VARCHAR" />
    <result column="softcopy_delivered_by" property="softcopyDeliveredBy" jdbcType="VARCHAR" />
    <result column="softcopy_delivered_date" property="softcopyDeliveredDate" jdbcType="TIMESTAMP" />
    <result column="delivery_type" property="deliveryType" jdbcType="VARCHAR" />
    <result column="active_indicator" property="activeIndicator" jdbcType="BIT" />
    <result column="created_by" property="createdBy" jdbcType="VARCHAR" />
    <result column="created_date" property="createdDate" jdbcType="TIMESTAMP" />
    <result column="modified_by" property="modifiedBy" jdbcType="VARCHAR" />
    <result column="modified_date" property="modifiedDate" jdbcType="TIMESTAMP" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    id, report_id, hardcopy_delivered_date, hardcopy_delivered_by, softcopy_delivered_by, 
    softcopy_delivered_date, delivery_type, active_indicator, created_by, created_date, 
    modified_by, modified_date
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.ReportDeliverHistoryInfoExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    <include refid="Base_Column_List" />
    from tb_report_delivery_history
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.String" >
    select 
    <include refid="Base_Column_List" />
    from tb_report_delivery_history
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String" >
    delete from tb_report_delivery_history
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.ReportDeliverHistoryInfoExample" >
    delete from tb_report_delivery_history
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.ReportDeliverHistoryInfoPO" >
    insert into tb_report_delivery_history (id, report_id, hardcopy_delivered_date, 
      hardcopy_delivered_by, softcopy_delivered_by, 
      softcopy_delivered_date, delivery_type, 
      active_indicator, created_by, created_date, 
      modified_by, modified_date)
    values (#{id,jdbcType=VARCHAR}, #{reportId,jdbcType=VARCHAR}, #{hardcopyDeliveredDate,jdbcType=TIMESTAMP}, 
      #{hardcopyDeliveredBy,jdbcType=VARCHAR}, #{softcopyDeliveredBy,jdbcType=VARCHAR}, 
      #{softcopyDeliveredDate,jdbcType=TIMESTAMP}, #{deliveryType,jdbcType=VARCHAR}, 
      #{activeIndicator,jdbcType=BIT}, #{createdBy,jdbcType=VARCHAR}, #{createdDate,jdbcType=TIMESTAMP}, 
      #{modifiedBy,jdbcType=VARCHAR}, #{modifiedDate,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.ReportDeliverHistoryInfoPO" >
    insert into tb_report_delivery_history
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="reportId != null" >
        report_id,
      </if>
      <if test="hardcopyDeliveredDate != null" >
        hardcopy_delivered_date,
      </if>
      <if test="hardcopyDeliveredBy != null" >
        hardcopy_delivered_by,
      </if>
      <if test="softcopyDeliveredBy != null" >
        softcopy_delivered_by,
      </if>
      <if test="softcopyDeliveredDate != null" >
        softcopy_delivered_date,
      </if>
      <if test="deliveryType != null" >
        delivery_type,
      </if>
      <if test="activeIndicator != null" >
        active_indicator,
      </if>
      <if test="createdBy != null" >
        created_by,
      </if>
      <if test="createdDate != null" >
        created_date,
      </if>
      <if test="modifiedBy != null" >
        modified_by,
      </if>
      <if test="modifiedDate != null" >
        modified_date,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="reportId != null" >
        #{reportId,jdbcType=VARCHAR},
      </if>
      <if test="hardcopyDeliveredDate != null" >
        #{hardcopyDeliveredDate,jdbcType=TIMESTAMP},
      </if>
      <if test="hardcopyDeliveredBy != null" >
        #{hardcopyDeliveredBy,jdbcType=VARCHAR},
      </if>
      <if test="softcopyDeliveredBy != null" >
        #{softcopyDeliveredBy,jdbcType=VARCHAR},
      </if>
      <if test="softcopyDeliveredDate != null" >
        #{softcopyDeliveredDate,jdbcType=TIMESTAMP},
      </if>
      <if test="deliveryType != null" >
        #{deliveryType,jdbcType=VARCHAR},
      </if>
      <if test="activeIndicator != null" >
        #{activeIndicator,jdbcType=BIT},
      </if>
      <if test="createdBy != null" >
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdDate != null" >
        #{createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="modifiedBy != null" >
        #{modifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="modifiedDate != null" >
        #{modifiedDate,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.ReportDeliverHistoryInfoExample" resultType="java.lang.Integer" >
    select count(*) from tb_report_delivery_history
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update tb_report_delivery_history
    <set >
      <if test="record.id != null" >
        id = #{record.id,jdbcType=VARCHAR},
      </if>
      <if test="record.reportId != null" >
        report_id = #{record.reportId,jdbcType=VARCHAR},
      </if>
      <if test="record.hardcopyDeliveredDate != null" >
        hardcopy_delivered_date = #{record.hardcopyDeliveredDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.hardcopyDeliveredBy != null" >
        hardcopy_delivered_by = #{record.hardcopyDeliveredBy,jdbcType=VARCHAR},
      </if>
      <if test="record.softcopyDeliveredBy != null" >
        softcopy_delivered_by = #{record.softcopyDeliveredBy,jdbcType=VARCHAR},
      </if>
      <if test="record.softcopyDeliveredDate != null" >
        softcopy_delivered_date = #{record.softcopyDeliveredDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.deliveryType != null" >
        delivery_type = #{record.deliveryType,jdbcType=VARCHAR},
      </if>
      <if test="record.activeIndicator != null" >
        active_indicator = #{record.activeIndicator,jdbcType=BIT},
      </if>
      <if test="record.createdBy != null" >
        created_by = #{record.createdBy,jdbcType=VARCHAR},
      </if>
      <if test="record.createdDate != null" >
        created_date = #{record.createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.modifiedBy != null" >
        modified_by = #{record.modifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="record.modifiedDate != null" >
        modified_date = #{record.modifiedDate,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    update tb_report_delivery_history
    set id = #{record.id,jdbcType=VARCHAR},
      report_id = #{record.reportId,jdbcType=VARCHAR},
      hardcopy_delivered_date = #{record.hardcopyDeliveredDate,jdbcType=TIMESTAMP},
      hardcopy_delivered_by = #{record.hardcopyDeliveredBy,jdbcType=VARCHAR},
      softcopy_delivered_by = #{record.softcopyDeliveredBy,jdbcType=VARCHAR},
      softcopy_delivered_date = #{record.softcopyDeliveredDate,jdbcType=TIMESTAMP},
      delivery_type = #{record.deliveryType,jdbcType=VARCHAR},
      active_indicator = #{record.activeIndicator,jdbcType=BIT},
      created_by = #{record.createdBy,jdbcType=VARCHAR},
      created_date = #{record.createdDate,jdbcType=TIMESTAMP},
      modified_by = #{record.modifiedBy,jdbcType=VARCHAR},
      modified_date = #{record.modifiedDate,jdbcType=TIMESTAMP}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.ReportDeliverHistoryInfoPO" >
    update tb_report_delivery_history
    <set >
      <if test="reportId != null" >
        report_id = #{reportId,jdbcType=VARCHAR},
      </if>
      <if test="hardcopyDeliveredDate != null" >
        hardcopy_delivered_date = #{hardcopyDeliveredDate,jdbcType=TIMESTAMP},
      </if>
      <if test="hardcopyDeliveredBy != null" >
        hardcopy_delivered_by = #{hardcopyDeliveredBy,jdbcType=VARCHAR},
      </if>
      <if test="softcopyDeliveredBy != null" >
        softcopy_delivered_by = #{softcopyDeliveredBy,jdbcType=VARCHAR},
      </if>
      <if test="softcopyDeliveredDate != null" >
        softcopy_delivered_date = #{softcopyDeliveredDate,jdbcType=TIMESTAMP},
      </if>
      <if test="deliveryType != null" >
        delivery_type = #{deliveryType,jdbcType=VARCHAR},
      </if>
      <if test="activeIndicator != null" >
        active_indicator = #{activeIndicator,jdbcType=BIT},
      </if>
      <if test="createdBy != null" >
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdDate != null" >
        created_date = #{createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="modifiedBy != null" >
        modified_by = #{modifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="modifiedDate != null" >
        modified_date = #{modifiedDate,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.ReportDeliverHistoryInfoPO" >
    update tb_report_delivery_history
    set report_id = #{reportId,jdbcType=VARCHAR},
      hardcopy_delivered_date = #{hardcopyDeliveredDate,jdbcType=TIMESTAMP},
      hardcopy_delivered_by = #{hardcopyDeliveredBy,jdbcType=VARCHAR},
      softcopy_delivered_by = #{softcopyDeliveredBy,jdbcType=VARCHAR},
      softcopy_delivered_date = #{softcopyDeliveredDate,jdbcType=TIMESTAMP},
      delivery_type = #{deliveryType,jdbcType=VARCHAR},
      active_indicator = #{activeIndicator,jdbcType=BIT},
      created_by = #{createdBy,jdbcType=VARCHAR},
      created_date = #{createdDate,jdbcType=TIMESTAMP},
      modified_by = #{modifiedBy,jdbcType=VARCHAR},
      modified_date = #{modifiedDate,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=VARCHAR}
  </update>
</mapper>