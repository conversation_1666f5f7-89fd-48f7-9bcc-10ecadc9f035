<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sgs.otsnotes.dbstorages.mybatis.mapper.PPArtifactRelInfoMapper" >
  <resultMap id="BaseResultMap" type="com.sgs.otsnotes.dbstorages.mybatis.model.PPArtifactRelInfoPO" >
    <id column="Id" property="id" jdbcType="BIGINT" />
    <result column="PpVersionId" property="ppVersionId" jdbcType="INTEGER" />
    <result column="ArtifactId" property="artifactId" jdbcType="BIGINT" />
    <result column="ArtifactType" property="artifactType" jdbcType="INTEGER" />
    <result column="ArtifactVersionId" property="artifactVersionId" jdbcType="INTEGER" />
    <result column="SectionBaseId" property="sectionBaseId" jdbcType="BIGINT" />
    <result column="CitationVersionId" property="citationVersionId" jdbcType="INTEGER" />
    <result column="CitationType" property="citationType" jdbcType="INTEGER" />
    <result column="EvaluationAlias" property="evaluationAlias" jdbcType="VARCHAR" />
    <result column="CitationName" property="citationName" jdbcType="VARCHAR" />
    <result column="TestLineSeq" property="testLineSeq" jdbcType="INTEGER" />
    <result column="TlExecutionClassificationCode" property="tlExecutionClassificationCode" jdbcType="INTEGER" />
    <result column="BizVersionId" property="bizVersionId" jdbcType="CHAR" />
    <result column="RelStatus" property="relStatus" jdbcType="INTEGER" />
    <result column="CreatedDate" property="createdDate" jdbcType="TIMESTAMP" />
    <result column="ModifiedDate" property="modifiedDate" jdbcType="TIMESTAMP" />
  </resultMap>
  <resultMap id="ResultMapWithBLOBs" type="com.sgs.otsnotes.dbstorages.mybatis.model.PPArtifactRelInfoWithBLOBs" extends="BaseResultMap" >
    <result column="PpNotesAlias" property="ppNotesAlias" jdbcType="LONGVARCHAR" />
    <result column="SpecificNotes" property="specificNotes" jdbcType="LONGVARCHAR" />
    <result column="ReportReferenceNoteAlias" property="reportReferenceNoteAlias" jdbcType="LONGVARCHAR" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    Id, PpVersionId, ArtifactId, ArtifactType, ArtifactVersionId, SectionBaseId, CitationVersionId, 
    CitationType, EvaluationAlias, CitationName, TestLineSeq, TlExecutionClassificationCode, 
    BizVersionId, RelStatus, CreatedDate, ModifiedDate
  </sql>
  <sql id="Blob_Column_List" >
    PpNotesAlias, SpecificNotes, ReportReferenceNoteAlias
  </sql>
  <select id="selectByExampleWithBLOBs" resultMap="ResultMapWithBLOBs" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.PPArtifactRelInfoExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from tre_trims_pp_artifact_relationship
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.PPArtifactRelInfoExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    <include refid="Base_Column_List" />
    from tre_trims_pp_artifact_relationship
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="ResultMapWithBLOBs" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from tre_trims_pp_artifact_relationship
    where Id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from tre_trims_pp_artifact_relationship
    where Id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.PPArtifactRelInfoExample" >
    delete from tre_trims_pp_artifact_relationship
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.PPArtifactRelInfoWithBLOBs" >
    insert into tre_trims_pp_artifact_relationship (Id, PpVersionId, ArtifactId, 
      ArtifactType, ArtifactVersionId, SectionBaseId, 
      CitationVersionId, CitationType, EvaluationAlias, 
      CitationName, TestLineSeq, TlExecutionClassificationCode, 
      BizVersionId, RelStatus, CreatedDate, 
      ModifiedDate, PpNotesAlias, SpecificNotes, 
      ReportReferenceNoteAlias)
    values (#{id,jdbcType=BIGINT}, #{ppVersionId,jdbcType=INTEGER}, #{artifactId,jdbcType=BIGINT}, 
      #{artifactType,jdbcType=INTEGER}, #{artifactVersionId,jdbcType=INTEGER}, #{sectionBaseId,jdbcType=BIGINT}, 
      #{citationVersionId,jdbcType=INTEGER}, #{citationType,jdbcType=INTEGER}, #{evaluationAlias,jdbcType=VARCHAR}, 
      #{citationName,jdbcType=VARCHAR}, #{testLineSeq,jdbcType=INTEGER}, #{tlExecutionClassificationCode,jdbcType=INTEGER}, 
      #{bizVersionId,jdbcType=CHAR}, #{relStatus,jdbcType=INTEGER}, #{createdDate,jdbcType=TIMESTAMP}, 
      #{modifiedDate,jdbcType=TIMESTAMP}, #{ppNotesAlias,jdbcType=LONGVARCHAR}, #{specificNotes,jdbcType=LONGVARCHAR}, 
      #{reportReferenceNoteAlias,jdbcType=LONGVARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.PPArtifactRelInfoWithBLOBs" >
    insert into tre_trims_pp_artifact_relationship
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        Id,
      </if>
      <if test="ppVersionId != null" >
        PpVersionId,
      </if>
      <if test="artifactId != null" >
        ArtifactId,
      </if>
      <if test="artifactType != null" >
        ArtifactType,
      </if>
      <if test="artifactVersionId != null" >
        ArtifactVersionId,
      </if>
      <if test="sectionBaseId != null" >
        SectionBaseId,
      </if>
      <if test="citationVersionId != null" >
        CitationVersionId,
      </if>
      <if test="citationType != null" >
        CitationType,
      </if>
      <if test="evaluationAlias != null" >
        EvaluationAlias,
      </if>
      <if test="citationName != null" >
        CitationName,
      </if>
      <if test="testLineSeq != null" >
        TestLineSeq,
      </if>
      <if test="tlExecutionClassificationCode != null" >
        TlExecutionClassificationCode,
      </if>
      <if test="bizVersionId != null" >
        BizVersionId,
      </if>
      <if test="relStatus != null" >
        RelStatus,
      </if>
      <if test="createdDate != null" >
        CreatedDate,
      </if>
      <if test="modifiedDate != null" >
        ModifiedDate,
      </if>
      <if test="ppNotesAlias != null" >
        PpNotesAlias,
      </if>
      <if test="specificNotes != null" >
        SpecificNotes,
      </if>
      <if test="reportReferenceNoteAlias != null" >
        ReportReferenceNoteAlias,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="ppVersionId != null" >
        #{ppVersionId,jdbcType=INTEGER},
      </if>
      <if test="artifactId != null" >
        #{artifactId,jdbcType=BIGINT},
      </if>
      <if test="artifactType != null" >
        #{artifactType,jdbcType=INTEGER},
      </if>
      <if test="artifactVersionId != null" >
        #{artifactVersionId,jdbcType=INTEGER},
      </if>
      <if test="sectionBaseId != null" >
        #{sectionBaseId,jdbcType=BIGINT},
      </if>
      <if test="citationVersionId != null" >
        #{citationVersionId,jdbcType=INTEGER},
      </if>
      <if test="citationType != null" >
        #{citationType,jdbcType=INTEGER},
      </if>
      <if test="evaluationAlias != null" >
        #{evaluationAlias,jdbcType=VARCHAR},
      </if>
      <if test="citationName != null" >
        #{citationName,jdbcType=VARCHAR},
      </if>
      <if test="testLineSeq != null" >
        #{testLineSeq,jdbcType=INTEGER},
      </if>
      <if test="tlExecutionClassificationCode != null" >
        #{tlExecutionClassificationCode,jdbcType=INTEGER},
      </if>
      <if test="bizVersionId != null" >
        #{bizVersionId,jdbcType=CHAR},
      </if>
      <if test="relStatus != null" >
        #{relStatus,jdbcType=INTEGER},
      </if>
      <if test="createdDate != null" >
        #{createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="modifiedDate != null" >
        #{modifiedDate,jdbcType=TIMESTAMP},
      </if>
      <if test="ppNotesAlias != null" >
        #{ppNotesAlias,jdbcType=LONGVARCHAR},
      </if>
      <if test="specificNotes != null" >
        #{specificNotes,jdbcType=LONGVARCHAR},
      </if>
      <if test="reportReferenceNoteAlias != null" >
        #{reportReferenceNoteAlias,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.PPArtifactRelInfoExample" resultType="java.lang.Integer" >
    select count(*) from tre_trims_pp_artifact_relationship
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update tre_trims_pp_artifact_relationship
    <set >
      <if test="record.id != null" >
        Id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.ppVersionId != null" >
        PpVersionId = #{record.ppVersionId,jdbcType=INTEGER},
      </if>
      <if test="record.artifactId != null" >
        ArtifactId = #{record.artifactId,jdbcType=BIGINT},
      </if>
      <if test="record.artifactType != null" >
        ArtifactType = #{record.artifactType,jdbcType=INTEGER},
      </if>
      <if test="record.artifactVersionId != null" >
        ArtifactVersionId = #{record.artifactVersionId,jdbcType=INTEGER},
      </if>
      <if test="record.sectionBaseId != null" >
        SectionBaseId = #{record.sectionBaseId,jdbcType=BIGINT},
      </if>
      <if test="record.citationVersionId != null" >
        CitationVersionId = #{record.citationVersionId,jdbcType=INTEGER},
      </if>
      <if test="record.citationType != null" >
        CitationType = #{record.citationType,jdbcType=INTEGER},
      </if>
      <if test="record.evaluationAlias != null" >
        EvaluationAlias = #{record.evaluationAlias,jdbcType=VARCHAR},
      </if>
      <if test="record.citationName != null" >
        CitationName = #{record.citationName,jdbcType=VARCHAR},
      </if>
      <if test="record.testLineSeq != null" >
        TestLineSeq = #{record.testLineSeq,jdbcType=INTEGER},
      </if>
      <if test="record.tlExecutionClassificationCode != null" >
        TlExecutionClassificationCode = #{record.tlExecutionClassificationCode,jdbcType=INTEGER},
      </if>
      <if test="record.bizVersionId != null" >
        BizVersionId = #{record.bizVersionId,jdbcType=CHAR},
      </if>
      <if test="record.relStatus != null" >
        RelStatus = #{record.relStatus,jdbcType=INTEGER},
      </if>
      <if test="record.createdDate != null" >
        CreatedDate = #{record.createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.modifiedDate != null" >
        ModifiedDate = #{record.modifiedDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.ppNotesAlias != null" >
        PpNotesAlias = #{record.ppNotesAlias,jdbcType=LONGVARCHAR},
      </if>
      <if test="record.specificNotes != null" >
        SpecificNotes = #{record.specificNotes,jdbcType=LONGVARCHAR},
      </if>
      <if test="record.reportReferenceNoteAlias != null" >
        ReportReferenceNoteAlias = #{record.reportReferenceNoteAlias,jdbcType=LONGVARCHAR},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map" >
    update tre_trims_pp_artifact_relationship
    set Id = #{record.id,jdbcType=BIGINT},
      PpVersionId = #{record.ppVersionId,jdbcType=INTEGER},
      ArtifactId = #{record.artifactId,jdbcType=BIGINT},
      ArtifactType = #{record.artifactType,jdbcType=INTEGER},
      ArtifactVersionId = #{record.artifactVersionId,jdbcType=INTEGER},
      SectionBaseId = #{record.sectionBaseId,jdbcType=BIGINT},
      CitationVersionId = #{record.citationVersionId,jdbcType=INTEGER},
      CitationType = #{record.citationType,jdbcType=INTEGER},
      EvaluationAlias = #{record.evaluationAlias,jdbcType=VARCHAR},
      CitationName = #{record.citationName,jdbcType=VARCHAR},
      TestLineSeq = #{record.testLineSeq,jdbcType=INTEGER},
      TlExecutionClassificationCode = #{record.tlExecutionClassificationCode,jdbcType=INTEGER},
      BizVersionId = #{record.bizVersionId,jdbcType=CHAR},
      RelStatus = #{record.relStatus,jdbcType=INTEGER},
      CreatedDate = #{record.createdDate,jdbcType=TIMESTAMP},
      ModifiedDate = #{record.modifiedDate,jdbcType=TIMESTAMP},
      PpNotesAlias = #{record.ppNotesAlias,jdbcType=LONGVARCHAR},
      SpecificNotes = #{record.specificNotes,jdbcType=LONGVARCHAR},
      ReportReferenceNoteAlias = #{record.reportReferenceNoteAlias,jdbcType=LONGVARCHAR}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    update tre_trims_pp_artifact_relationship
    set Id = #{record.id,jdbcType=BIGINT},
      PpVersionId = #{record.ppVersionId,jdbcType=INTEGER},
      ArtifactId = #{record.artifactId,jdbcType=BIGINT},
      ArtifactType = #{record.artifactType,jdbcType=INTEGER},
      ArtifactVersionId = #{record.artifactVersionId,jdbcType=INTEGER},
      SectionBaseId = #{record.sectionBaseId,jdbcType=BIGINT},
      CitationVersionId = #{record.citationVersionId,jdbcType=INTEGER},
      CitationType = #{record.citationType,jdbcType=INTEGER},
      EvaluationAlias = #{record.evaluationAlias,jdbcType=VARCHAR},
      CitationName = #{record.citationName,jdbcType=VARCHAR},
      TestLineSeq = #{record.testLineSeq,jdbcType=INTEGER},
      TlExecutionClassificationCode = #{record.tlExecutionClassificationCode,jdbcType=INTEGER},
      BizVersionId = #{record.bizVersionId,jdbcType=CHAR},
      RelStatus = #{record.relStatus,jdbcType=INTEGER},
      CreatedDate = #{record.createdDate,jdbcType=TIMESTAMP},
      ModifiedDate = #{record.modifiedDate,jdbcType=TIMESTAMP}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.PPArtifactRelInfoWithBLOBs" >
    update tre_trims_pp_artifact_relationship
    <set >
      <if test="ppVersionId != null" >
        PpVersionId = #{ppVersionId,jdbcType=INTEGER},
      </if>
      <if test="artifactId != null" >
        ArtifactId = #{artifactId,jdbcType=BIGINT},
      </if>
      <if test="artifactType != null" >
        ArtifactType = #{artifactType,jdbcType=INTEGER},
      </if>
      <if test="artifactVersionId != null" >
        ArtifactVersionId = #{artifactVersionId,jdbcType=INTEGER},
      </if>
      <if test="sectionBaseId != null" >
        SectionBaseId = #{sectionBaseId,jdbcType=BIGINT},
      </if>
      <if test="citationVersionId != null" >
        CitationVersionId = #{citationVersionId,jdbcType=INTEGER},
      </if>
      <if test="citationType != null" >
        CitationType = #{citationType,jdbcType=INTEGER},
      </if>
      <if test="evaluationAlias != null" >
        EvaluationAlias = #{evaluationAlias,jdbcType=VARCHAR},
      </if>
      <if test="citationName != null" >
        CitationName = #{citationName,jdbcType=VARCHAR},
      </if>
      <if test="testLineSeq != null" >
        TestLineSeq = #{testLineSeq,jdbcType=INTEGER},
      </if>
      <if test="tlExecutionClassificationCode != null" >
        TlExecutionClassificationCode = #{tlExecutionClassificationCode,jdbcType=INTEGER},
      </if>
      <if test="bizVersionId != null" >
        BizVersionId = #{bizVersionId,jdbcType=CHAR},
      </if>
      <if test="relStatus != null" >
        RelStatus = #{relStatus,jdbcType=INTEGER},
      </if>
      <if test="createdDate != null" >
        CreatedDate = #{createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="modifiedDate != null" >
        ModifiedDate = #{modifiedDate,jdbcType=TIMESTAMP},
      </if>
      <if test="ppNotesAlias != null" >
        PpNotesAlias = #{ppNotesAlias,jdbcType=LONGVARCHAR},
      </if>
      <if test="specificNotes != null" >
        SpecificNotes = #{specificNotes,jdbcType=LONGVARCHAR},
      </if>
      <if test="reportReferenceNoteAlias != null" >
        ReportReferenceNoteAlias = #{reportReferenceNoteAlias,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where Id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.PPArtifactRelInfoWithBLOBs" >
    update tre_trims_pp_artifact_relationship
    set PpVersionId = #{ppVersionId,jdbcType=INTEGER},
      ArtifactId = #{artifactId,jdbcType=BIGINT},
      ArtifactType = #{artifactType,jdbcType=INTEGER},
      ArtifactVersionId = #{artifactVersionId,jdbcType=INTEGER},
      SectionBaseId = #{sectionBaseId,jdbcType=BIGINT},
      CitationVersionId = #{citationVersionId,jdbcType=INTEGER},
      CitationType = #{citationType,jdbcType=INTEGER},
      EvaluationAlias = #{evaluationAlias,jdbcType=VARCHAR},
      CitationName = #{citationName,jdbcType=VARCHAR},
      TestLineSeq = #{testLineSeq,jdbcType=INTEGER},
      TlExecutionClassificationCode = #{tlExecutionClassificationCode,jdbcType=INTEGER},
      BizVersionId = #{bizVersionId,jdbcType=CHAR},
      RelStatus = #{relStatus,jdbcType=INTEGER},
      CreatedDate = #{createdDate,jdbcType=TIMESTAMP},
      ModifiedDate = #{modifiedDate,jdbcType=TIMESTAMP},
      PpNotesAlias = #{ppNotesAlias,jdbcType=LONGVARCHAR},
      SpecificNotes = #{specificNotes,jdbcType=LONGVARCHAR},
      ReportReferenceNoteAlias = #{reportReferenceNoteAlias,jdbcType=LONGVARCHAR}
    where Id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.PPArtifactRelInfoPO" >
    update tre_trims_pp_artifact_relationship
    set PpVersionId = #{ppVersionId,jdbcType=INTEGER},
      ArtifactId = #{artifactId,jdbcType=BIGINT},
      ArtifactType = #{artifactType,jdbcType=INTEGER},
      ArtifactVersionId = #{artifactVersionId,jdbcType=INTEGER},
      SectionBaseId = #{sectionBaseId,jdbcType=BIGINT},
      CitationVersionId = #{citationVersionId,jdbcType=INTEGER},
      CitationType = #{citationType,jdbcType=INTEGER},
      EvaluationAlias = #{evaluationAlias,jdbcType=VARCHAR},
      CitationName = #{citationName,jdbcType=VARCHAR},
      TestLineSeq = #{testLineSeq,jdbcType=INTEGER},
      TlExecutionClassificationCode = #{tlExecutionClassificationCode,jdbcType=INTEGER},
      BizVersionId = #{bizVersionId,jdbcType=CHAR},
      RelStatus = #{relStatus,jdbcType=INTEGER},
      CreatedDate = #{createdDate,jdbcType=TIMESTAMP},
      ModifiedDate = #{modifiedDate,jdbcType=TIMESTAMP}
    where Id = #{id,jdbcType=BIGINT}
  </update>
</mapper>