<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sgs.otsnotes.dbstorages.mybatis.extmapper.TestLineMapper" >
    <resultMap id="BaseResultMap" type="com.sgs.otsnotes.dbstorages.mybatis.model.TestLineInstancePO" >
        <id column="ID" property="ID" jdbcType="VARCHAR" />
        <result column="GeneralOrderInstanceID" property="generalOrderInstanceID" jdbcType="VARCHAR" />
        <result column="TestLineVersionID" property="testLineVersionID" jdbcType="INTEGER" />
        <result column="TestLineID" property="testLineID" jdbcType="INTEGER" />
        <result column="TestLineStatus" property="testLineStatus" jdbcType="INTEGER" />
        <result column="ConditionStatus" property="conditionStatus" jdbcType="INTEGER" />
        <result column="TestLineSeq" property="testLineSeq" jdbcType="INTEGER" />
        <result column="OrdertestLineRemark" property="ordertestLineRemark" jdbcType="VARCHAR" />
        <result column="ActiveIndicator" property="activeIndicator" jdbcType="BIT" />
        <result column="modified" property="modified" jdbcType="BIT" />
        <result column="FileID" property="fileID" jdbcType="VARCHAR" />
        <result column="DataEntryCloudID" property="dataEntryCloudID" jdbcType="VARCHAR" />
        <result column="CreatedDate" property="createdDate" jdbcType="TIMESTAMP" />
        <result column="CreatedBy" property="createdBy" jdbcType="VARCHAR" />
        <result column="ModifiedDate" property="modifiedDate" jdbcType="TIMESTAMP" />
        <result column="ModifiedBy" property="modifiedBy" jdbcType="VARCHAR" />
        <result column="ValidateDate" property="validateDate" jdbcType="TIMESTAMP" />
        <result column="ValidateBy" property="validateBy" jdbcType="VARCHAR" />
        <result column="ReportSeq" property="reportSeq" jdbcType="INTEGER" />
        <result column="CalculateConclusionFlag" property="calculateConclusionFlag" jdbcType="INTEGER" />
        <result column="ProductLineAbbr" property="productLineAbbr" jdbcType="VARCHAR" />
        <result column="TestLineType" property="testLineType" jdbcType="INTEGER" />
        <result column="OrderSeq" property="orderSeq" jdbcType="INTEGER" />
        <result column="testLineBaseId" property="testLineBaseId" jdbcType="BIGINT" />
        <result column="citationBaseId" property="citationBaseId" jdbcType="BIGINT" />
        <result column="labSectionBaseId" property="labSectionBaseId" jdbcType="BIGINT" />
        <result column="citationId" property="citationId" jdbcType="BIGINT" />
        <result column="citationVersionId" property="citationVersionId" jdbcType="BIGINT" />
        <result column="pendingFlag" property="pendingFlag" jdbcType="BIT" />
        <result column="SampleSegegrationWIID" property="sampleSegegrationWIID" jdbcType="INTEGER" />
        <result column="SampleSegegrationWIText" property="sampleSegegrationWIText" jdbcType="VARCHAR" />
        <result column="LabTeamCode" property="labTeamCode" jdbcType="VARCHAR" />
<!--
        <result column="mainTestLineId" property="mainTestLineId" jdbcType="INTEGER" />
-->
        <result column="StyleVersionId" property="styleVersionId" jdbcType="INTEGER" />
        <result column="TestItemNo" property="testItemNo" jdbcType="VARCHAR" />
        <result column="TestDueDate" property="testDueDate" jdbcType="VARCHAR" />
        <result column="Engineer" property="engineer" jdbcType="VARCHAR" />
        <result column="DocumentReviewFlag" property="documentReviewFlag" jdbcType="VARCHAR" />
<!--
        <result column="wiForCsText" property="wiForCsText" jdbcType="VARCHAR" />
-->
        <result column="ClientStandard" property="clientStandard" jdbcType="VARCHAR" />
        <result column="CustomerTestLineName" property="customerTestLineName" jdbcType="VARCHAR"></result>
        <result column="CustomerTestLineNameCN" property="customerTestLineNameCN" jdbcType="VARCHAR"></result>
        <result column="ExternalTestlineInstanceId" property="externalTestlineInstanceId" jdbcType="VARCHAR"></result>
        <result column="citationName" property="citationName" jdbcType="VARCHAR"></result>
        <result column="citationNameCN" property="citationNameCN" jdbcType="VARCHAR"></result>
    </resultMap>

    <resultMap id="BaseResultMapNew" type="com.sgs.otsnotes.dbstorages.mybatis.extmodel.masterlist.TlinfoListData" >
        <id column="ID" property="ID" jdbcType="VARCHAR" />
        <result column="GeneralOrderInstanceID" property="generalOrderInstanceID" jdbcType="VARCHAR" />
        <result column="TestLineVersionID" property="testLineVersionID" jdbcType="INTEGER" />
        <result column="TestLineID" property="testLineID" jdbcType="INTEGER" />
        <result column="TestLineEvaluation" property="testLineEvaluation" jdbcType="VARCHAR" />
        <result column="CitationTypeID" property="citationTypeID" jdbcType="INTEGER" />
        <result column="EvaluationAlias" property="evaluationAlias" jdbcType="VARCHAR" />
        <result column="StandardID" property="standardID" jdbcType="INTEGER" />
        <result column="StandardVersionID" property="standardVersionID" jdbcType="INTEGER" />
        <result column="StandardName" property="standardName" jdbcType="VARCHAR" />
        <result column="StandardSectionID" property="standardSectionID" jdbcType="INTEGER" />
        <result column="StandardSectionName" property="standardSectionName" jdbcType="VARCHAR" />
        <result column="AutoConclusion" property="autoConclusion" jdbcType="BIT" />
        <result column="TestLineStatus" property="testLineStatus" jdbcType="INTEGER" />
        <result column="ConditionStatus" property="conditionStatus" jdbcType="INTEGER" />
        <result column="RecommendedFibreLabel" property="recommendedFibreLabel" jdbcType="VARCHAR" />
        <result column="TestLineSeq" property="testLineSeq" jdbcType="INTEGER" />
        <result column="CuttingRequest" property="cuttingRequest" jdbcType="VARCHAR" />
        <result column="OrdertestLineRemark" property="ordertestLineRemark" jdbcType="VARCHAR" />
        <result column="ActiveIndicator" property="activeIndicator" jdbcType="BIT" />
        <result column="modified" property="modified" jdbcType="BIT" />
        <result column="FileID" property="fileID" jdbcType="VARCHAR" />
        <result column="DataEntryCloudID" property="dataEntryCloudID" jdbcType="VARCHAR" />
        <result column="CreatedDate" property="createdDate" jdbcType="TIMESTAMP" />
        <result column="CreatedBy" property="createdBy" jdbcType="VARCHAR" />
        <result column="ModifiedDate" property="modifiedDate" jdbcType="TIMESTAMP" />
        <result column="ModifiedBy" property="modifiedBy" jdbcType="VARCHAR" />
        <result column="ValidateDate" property="validateDate" jdbcType="TIMESTAMP" />
        <result column="ValidateBy" property="validateBy" jdbcType="VARCHAR" />
        <result column="ReportSeq" property="reportSeq" jdbcType="INTEGER" />
        <result column="TestLineAlias" property="testLineAlias" jdbcType="VARCHAR" />
        <result column="CalculateConclusionFlag" property="calculateConclusionFlag" jdbcType="INTEGER" />
        <result column="ProductLineAbbr" property="productLineAbbr" jdbcType="VARCHAR" />
        <result column="TestLineType" property="testLineType" jdbcType="INTEGER" />
        <result column="OrderSeq" property="orderSeq" jdbcType="INTEGER" />
        <result column="testLineBaseId" property="testLineBaseId" jdbcType="BIGINT" />
        <result column="citationBaseId" property="citationBaseId" jdbcType="BIGINT" />
        <result column="labSectionBaseId" property="labSectionBaseId" jdbcType="BIGINT" />
        <result column="citationId" property="citationId" jdbcType="BIGINT" />
        <result column="citationVersionId" property="citationVersionId" jdbcType="BIGINT" />
        <result column="pendingFlag" property="pendingFlag" jdbcType="BIT" />
        <result column="SampleSegegrationWIID" property="sampleSegegrationWIID" jdbcType="INTEGER" />
        <result column="SampleSegegrationWIText" property="sampleSegegrationWIText" jdbcType="VARCHAR" />
        <result column="LabTeamCode" property="labTeamCode" jdbcType="VARCHAR" />
        <result column="TestItemNo" property="testItemNo" jdbcType="VARCHAR" />
        <result column="TestDueDate" property="testDueDate" jdbcType="VARCHAR" />
        <result column="Engineer" property="engineer" jdbcType="VARCHAR" />
        <result column="wiForCsText" property="wiForCsText" jdbcType="VARCHAR" />

    </resultMap>

    <resultMap id="RequirmentTestLineResultMap" type="com.sgs.otsnotes.facade.model.info.limitgroup.RequirmentTestLineInfo" >
        <id column="TestLineInstanceId" property="testLineInstanceId" jdbcType="VARCHAR" />
        <result column="TestLineId" property="testLineId" jdbcType="INTEGER" />
        <result column="EvaluationAlias" property="testLineName" jdbcType="VARCHAR" />
        <result column="TestLineVersionId" property="testLineVersionId" jdbcType="INTEGER" />
        <result column="StandardVersionId" property="standardVersionId" jdbcType="INTEGER" />
        <result column="ProductLineAbbr" property="productLineAbbr" jdbcType="VARCHAR" />
        <result column="LabSectionName" property="labSectionName" jdbcType="VARCHAR" />

        <collection property="matrixs"
                    ofType="com.sgs.otsnotes.facade.model.info.limitgroup.TestLineMatrixInfo"
                    javaType="java.util.List"
                    column="TestLineInstanceId"
                    select="getRequirmentTestLineMatrixList" />
    </resultMap>

    <resultMap id="editTestLineResultMap" type="com.sgs.otsnotes.facade.model.rsp.testLine.TestLineEditDetailRsp">
        <result column="testLineInstanceId" property="testLineInstanceId" jdbcType="VARCHAR"></result>
        <result column="wiForCsText" property="wiForCsText" jdbcType="VARCHAR"></result>
        <result column="wiForCsTextEN" property="wiForCsTextEN" jdbcType="VARCHAR"></result>
        <result column="ordertestLineRemark" property="ordertestLineRemark" jdbcType="VARCHAR"></result>
        <result column="sampleSegegrationWIID" property="sampleSegegrationWIID" jdbcType="INTEGER"></result>
        <result column="sampleSegegrationWIText" property="sampleSegegrationWIText" jdbcType="VARCHAR"></result>
        <result column="labId" property="labId" jdbcType="INTEGER"></result>
        <result column="testLineId" property="testLineId" jdbcType="INTEGER"></result>
        <result column="testLineStatus" property="testLineStatus" jdbcType="INTEGER"></result>
        <result column="testLineVersionID" property="testLineVersionID" jdbcType="INTEGER"></result>
        <result column="standardVersionId" property="standardVersionId" jdbcType="INTEGER"></result>
        <result column="labTeamCode" property="labTeamCode" jdbcType="VARCHAR"></result>
        <result column="orderInstanceId" property="orderInstanceId" jdbcType="VARCHAR"></result>
        <result column="orderNo" property="orderNo" jdbcType="VARCHAR"></result>
        <result column="ppNotes" property="ppNotes" jdbcType="VARCHAR"></result>
        <result column="clientStandard" property="clientStandard" jdbcType="VARCHAR"></result>
        <result column="testLineType" property="testLineType" jdbcType="INTEGER"></result>
        <result column="customerTestLineName" property="customerTestLineName" jdbcType="VARCHAR"></result>
        <result column="customerTestLineNameCN" property="customerTestLineNameCN" jdbcType="VARCHAR"></result>
        <result column="editCitationName" property="editCitationName" jdbcType="VARCHAR"></result>
        <result column="editCitationNameCN" property="editCitationNameCN" jdbcType="VARCHAR"></result>
        <result column="engineer" property="engineer" jdbcType="VARCHAR"></result>
        <collection property="analyteIds" ofType="java.lang.Integer" >
            <result column="analyteID"></result>
        </collection>
        <collection property="ppVersionIds" ofType="java.lang.Integer" >
            <result column="ppVersionId"></result>
        </collection>
        <collection property="aftifactIds" ofType="java.lang.Integer" >
            <result column="aid"></result>
        </collection>
    </resultMap>

    <resultMap id="TestLineSimplifyMap" type="com.sgs.otsnotes.facade.model.dto.TestLineSimplifyDTO" >
        <result column="OrderId" property="orderId" jdbcType="VARCHAR" />
        <result column="TestLineInstanceId" property="testLineInstanceId" jdbcType="VARCHAR" />
        <result column="TestLineVersionId" property="testLineVersionId" jdbcType="INTEGER" />
        <result column="TestLineId" property="testLineId" jdbcType="INTEGER" />
        <result column="EvaluationName" property="evaluationName" jdbcType="VARCHAR" />
        <result column="CitationVersionId" property="citationVersionId" jdbcType="INTEGER" />
        <result column="EvaluationAlias" property="evaluationAlias" jdbcType="VARCHAR" />
        <result column="CitationName" property="citationName" jdbcType="VARCHAR" />
        <result column="TestLineStatus" property="testLineStatus" jdbcType="INTEGER" />
        <result column="ConditionStatus" property="conditionStatus" jdbcType="INTEGER" />
        <result column="TestLineRemark" property="testLineRemark" jdbcType="VARCHAR" />
        <result column="CitationSectionName" property="citationSectionName" jdbcType="VARCHAR" />
        <result column="ProductLineAbbr" property="productLineAbbr" jdbcType="VARCHAR" />
        <result column="LabSectionName" property="labSectionName" jdbcType="VARCHAR" />
        <result column="TestLineType" property="testLineType" jdbcType="VARCHAR" />

        <result column="TestLineBaseId" property="testLineBaseId" jdbcType="BIGINT" />
        <result column="CitationBaseId" property="citationBaseId" jdbcType="BIGINT" />
        <result column="LabSectionBaseId" property="labSectionBaseId" jdbcType="BIGINT" />

        <collection property="artifactIds" ofType="java.lang.Long">
            <result column="ArtifactId"/>
        </collection>

        <collection property="ppArtifactRelIds" ofType="java.lang.Long">
            <result column="PpArtifactRelId"/>
        </collection>

        <collection property="langBaseIds" ofType="java.lang.Long">
            <result column="LangBaseId"/>
        </collection>
        <collection property="ppArtifactLangBaseIds" ofType="java.lang.Long">
            <result column="PpArtifactLang"/>
        </collection>

    </resultMap>


    <sql id="Base_Column_List" >
        ID,
        GeneralOrderInstanceID,
        OrderNo,
        LabId,
        TestLineVersionID,
        TestLineID,
        TestLineStatus,
        ConditionStatus,
        TestLineSeq,
        OrdertestLineRemark,
        ActiveIndicator,
        modified,
        FileID,
        DataEntryCloudID,
        CreatedDate,
        CreatedBy,
        ModifiedDate,
        ModifiedBy,
        ValidateDate,
        ValidateBy,
        ReportSeq,
        CalculateConclusionFlag,
        ProductLineAbbr,
        TestLineType,
        testLineBaseId,
        citationBaseId,
        labSectionBaseId,
        citationId,
        citationVersionId,
        pendingFlag,
        sampleSegegrationWIID,
        sampleSegegrationWIText,
        labTeamCode,
        StyleVersionId,
        testItemNo,
        orderSeq,
        CustomerTestLineName,
        CustomerTestLineNameCN,
        ClientStandard,
        ExternalTestlineInstanceId,
        CitationName,
        Engineer,
        TestDueDate,
        DocumentReviewFlag,
        TestStartDate
    </sql>

    <sql id="Multiple_Language_Test_Line_Base_Column_List" >
        ID,
        TestLineInstanceID,
        LanguageId,
        EvaluationAlias,
        StandardName,
        TestLineEvaluation,
        CreatedDate,
        CreatedBy,
        ModifiedDate,
        ModifiedBy,
        StandardSectionName,
        CitationName
    </sql>



    <sql id="getTLInstanceDetails_Where_Clause">
        <where>
            <if test="queryTestLinePage != null and queryTestLinePage == true">
                and oi.operation_mode != 8
            </if>
            <if test="labCode != null and labCode != ''" >
                and goi.labCode = #{labCode}
            </if>
            <if test="orderNo != null and orderNo != ''" >
                and goi.OrderNo = #{orderNo}
            </if>
            <if test="orderId != null and orderId != ''" >
                and goi.ID = #{orderId}
            </if>
            <if test="reportNo != null and reportNo != ''" >
                and tr.ReportNo = #{reportNo}
            </if>
            <if test="orderNos != null and orderNos.size() > 0" >
                and `goi`.`OrderNo` in
                <foreach collection="orderNos" open="(" close=")" separator="," item="item">
                    #{item}
                </foreach>
            </if>
            <if test="reportId != null and reportId != ''" >
                and trmr.ReportID = #{reportId} AND ttm.`ID` IS NOT NULL
            </if>
            <if test="testLineInstanceIds != null and testLineInstanceIds.size() > 0">
                and tli.id IN
                <foreach collection="testLineInstanceIds" item="testLineInstanceId" open="(" close=")" separator=",">
                    #{testLineInstanceId}
                </foreach>
            </if>
            <if test="testLineIdList != null and testLineIdList.size() > 0">
                and tli.TestLineId IN
                <foreach collection="testLineIdList" item="testLineId" open="(" close=")" separator=",">
                    #{testLineId}
                </foreach>
            </if>
            <if test="citationIdList != null and citationIdList.size() > 0">
                and tli.citationId IN
                <foreach collection="citationIdList" item="citationId" open="(" close=")" separator=",">
                    #{citationId}
                </foreach>
            </if>
            <if test="citationVersionIdList != null and citationVersionIdList.size() > 0">
                and tli.citationVersionId IN
                <foreach collection="citationVersionIdList" item="citationVersionId" open="(" close=")" separator=",">
                    #{citationVersionId}
                </foreach>
            </if>
            <if test="labSectionId !=null and labSectionId!='' ">
                and labsectionbase.LabSectionId =#{labSectionId}
            </if>
            <if test="engineer != null and engineer != ''" >
                and `tli`.`Engineer` like CONCAT('%',trim(#{engineer}),'%')
            </if>
            <if test="testItemNo != null and  testItemNo != '' ">
                and `tli`.`TestItemNo` = #{testItemNo}
            </if>
            <if test="testItemNos != null and testItemNos.size() > 0" >
                and `tli`.`TestItemNo` in
                <foreach collection="testItemNos" open="(" close=")" separator="," item="item">
                    #{item}
                </foreach>
            </if>
            <if test="labTeamCode != null and labTeamCode != ''" >
                and  `tli`.`LabTeamCode` like CONCAT('%',trim(#{labTeamCode}),'%')
            </if>
            <if test="testLineStatus != null and testLineStatus != ''" >
                <choose>
                    <when test='testLineStatus == 799 '>
                        and `tli`.`PendingFlag` is true
                    </when>
                    <otherwise>
                        and `tli`.`TestLineStatus` = #{testLineStatus}   and `tli`.`PendingFlag` is false
                    </otherwise>
                </choose>
            </if>
            <if test="createdDateFrom != null and createdDateFrom != ''" >
                and `tli`.`CreatedDate` between  #{createdDateFrom} and #{createdDateTo}
            </if>
            <if test="testStartDateFrom != null and testStartDateFrom != ''" >
                and `tli`.`TestStartDate` between  #{testStartDateFrom} and #{testStartDateTo}
            </if>
            <if test="testEndDateFrom != null and testEndDateFrom != ''" >
                and `tli`.`TestEndDate` between  #{testEndDateFrom} and #{testEndDateTo}
            </if>
            <if test="testDueDateFrom != null and testDueDateFrom != ''" >
                and `tli`.`TestDueDate` between  #{testDueDateFrom} and #{testDueDateTo}
            </if>
            <if test="jobId !=null and jobId!='' ">
                and `jobrel`.`JobID`=#{jobId}
            </if>
            <if test="jobNo != null and jobNo != ''" >
                and `tj`.`JobNo` = #{jobNo}
            </if>
            <if test="jobNos != null and jobNos.size() > 0" >
                and `tj`.`JobNo` in
                <foreach collection="jobNos" open="(" close=")" separator="," item="item">
                    #{item}
                </foreach>
            </if>

            <if test="subcontractId != null and subcontractId != '' ">
                and subcontract.id = #{subcontractId}
            </if>
            <if test="subcontractIds != null and subcontractIds.size() > 0" >
                and subcontract.id in
                <foreach collection="subcontractIds" open="(" close=")" separator="," item="item">
                    #{item}
                </foreach>
            </if>
            <if test="testLineStatusList != null and testLineStatusList.size() > 0" >
                <choose>
                    <when test="testLineStatusList.contains('799')">
                        and (
                        tli.TestLineStatus in
                        <foreach collection="testLineStatusList" open="(" close=")" separator="," item="item">
                            #{item}
                        </foreach> or tli.PendingFlag is true
                        )
                    </when>
                    <otherwise>
                        and tli.TestLineStatus in
                        <foreach collection="testLineStatusList" open="(" close=")" separator="," item="item">
                            #{item}
                        </foreach>
                        and tli.PendingFlag is false
                    </otherwise>
                </choose>
            </if>
            <if test="orderStatusList != null and orderStatusList.size() > 0" >
                and oi.order_status in
                <foreach collection="orderStatusList" open="(" close=")" separator="," item="item">
                    #{item}
                </foreach>
            </if>
            <if test="subcontractNo != null and  subcontractNo != '' ">
                and subcontract.SubContractNo = #{subcontractNo}
            </if>
            <if test="subcontractLabCode != null and  subcontractLabCode != '' ">
                and subcontract.SubContractLabCode = #{subcontractLabCode}
            </if>

            <if test="filterOption == 1">
                and `tli`.`TestStartDate` IS TRUE
            </if>
            <!-- filter=2 表示 lab out date有值-->
            <if test="filterOption == 2">
                and `tli`.`TestEndDate` IS TRUE
            </if>
            <!-- filter=3 表示 today - TL due date) <1天 & 没有lab out date或lab in date值）)-->
            <if test="filterOption == 3">
                and (`tli`.`TestEndDate` IS NULL or `tli`.`TestStartDate` IS NULL ) and (TIMESTAMPDIFF(MINUTE,NOW(),tli.TestDueDate) &lt; 24*60)
            </if>
            <if test="(testItemNos == null or testItemNos.size() ==0 ) and (orderNos == null or orderNos.size() ==0 ) and (orderNo == null or orderNo == '') and (testItemNo == null or  testItemNo == '') and testLineStatus == null and cancelStatus != null">
                and `tli`.`TestLineStatus` != #{cancelStatus}
            </if>
        </where>
    </sql>

    <insert id="batchInsertForAddTl" parameterType="java.util.List">
        INSERT INTO tb_test_line_instance (
        <include refid="Base_Column_List" />
        )
        VALUES
        <foreach collection="testLines" item="testLine" separator=",">
            (
            #{testLine.ID}
            ,#{testLine.generalOrderInstanceID}
            ,#{testLine.orderNo}
            ,#{testLine.labId}
            ,#{testLine.testLineVersionID}
            ,#{testLine.testLineID}
            ,#{testLine.testLineStatus}
            ,#{testLine.conditionStatus}
            ,#{testLine.testLineSeq}
            ,#{testLine.ordertestLineRemark}
            ,#{testLine.activeIndicator}
            ,#{testLine.modified}
            ,#{testLine.fileID}
            ,#{testLine.dataEntryCloudID}
            ,#{testLine.createdDate}
            ,#{testLine.createdBy}
            ,#{testLine.modifiedDate}
            ,#{testLine.modifiedBy}
            ,#{testLine.validateDate}
            ,#{testLine.validateBy}
            ,#{testLine.reportSeq}
            ,#{testLine.calculateConclusionFlag}
            ,#{testLine.productLineAbbr}
            ,#{testLine.testLineType}
            ,#{testLine.testLineBaseId}
            ,#{testLine.citationBaseId}
            ,#{testLine.labSectionBaseId}
            ,#{testLine.citationId}
            ,#{testLine.citationVersionId}
            ,#{testLine.pendingFlag}
            ,#{testLine.sampleSegegrationWIID}
            ,#{testLine.sampleSegegrationWIText}
            ,#{testLine.labTeamCode}
            ,#{testLine.styleVersionId}
            ,#{testLine.testItemNo}
            ,#{testLine.orderSeq}
            ,#{testLine.customerTestLineName}
            ,#{testLine.customerTestLineNameCN}
            ,#{testLine.clientStandard}
            ,#{testLine.externalTestlineInstanceId}
            ,#{testLine.citationName}
            ,#{testLine.engineer}
            ,#{testLine.testDueDate}
            ,#{testLine.documentReviewFlag}
            ,#{testLine.testStartDate}
            )
        </foreach>
        ON DUPLICATE KEY UPDATE
        TestLineStatus = VALUES(testLineStatus),
        ModifiedDate = VALUES(modifiedDate),
        ModifiedBy = VALUES(modifiedBy)


    </insert>

    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO tb_test_line_instance (
        <include refid="Base_Column_List" />
        )
        VALUES
        <foreach collection="testLines" item="testLine" separator=",">
            (
            #{testLine.ID}
            ,#{testLine.generalOrderInstanceID}
            ,#{testLine.orderNo}
            ,#{testLine.labId}
            ,#{testLine.testLineVersionID}
            ,#{testLine.testLineID}
            ,#{testLine.testLineStatus}
            ,#{testLine.conditionStatus}
            ,#{testLine.testLineSeq}
            ,#{testLine.ordertestLineRemark}
            ,#{testLine.activeIndicator}
            ,#{testLine.modified}
            ,#{testLine.fileID}
            ,#{testLine.dataEntryCloudID}
            ,#{testLine.createdDate}
            ,#{testLine.createdBy}
            ,#{testLine.modifiedDate}
            ,#{testLine.modifiedBy}
            ,#{testLine.validateDate}
            ,#{testLine.validateBy}
            ,#{testLine.reportSeq}
            ,#{testLine.calculateConclusionFlag}
            ,#{testLine.productLineAbbr}
            ,#{testLine.testLineType}
            ,#{testLine.testLineBaseId}
            ,#{testLine.citationBaseId}
            ,#{testLine.labSectionBaseId}
            ,#{testLine.citationId}
            ,#{testLine.citationVersionId}
            ,#{testLine.pendingFlag}
            ,#{testLine.sampleSegegrationWIID}
            ,#{testLine.sampleSegegrationWIText}
            ,#{testLine.labTeamCode}
            ,#{testLine.styleVersionId}
            ,#{testLine.testItemNo}
            ,#{testLine.orderSeq}
            ,#{testLine.customerTestLineName}
            ,#{testLine.customerTestLineNameCN}
            ,#{testLine.clientStandard}
            ,#{testLine.externalTestlineInstanceId}
            ,#{testLine.citationName}
            ,#{testLine.engineer}
            ,#{testLine.testDueDate}
            ,#{testLine.documentReviewFlag},
             #{testLine.testStartDate}
            )
        </foreach>
        ON DUPLICATE KEY UPDATE
        TestLineVersionID = VALUES(testLineVersionID),
        TestLineID = VALUES(testLineID),
        TestLineStatus = VALUES(testLineStatus),
        ConditionStatus = VALUES(conditionStatus),
        TestLineSeq = VALUES(testLineSeq),
        OrdertestLineRemark = VALUES(ordertestLineRemark),
        modified = VALUES(modified),
        FileID = VALUES(fileID),
        DataEntryCloudID = VALUES(dataEntryCloudID),
        ModifiedDate = VALUES(modifiedDate),
        ModifiedBy = VALUES(modifiedBy),
        ValidateDate = VALUES(validateDate),
        ValidateBy = VALUES(validateBy),
        ReportSeq = VALUES(reportSeq),
        CalculateConclusionFlag = VALUES(calculateConclusionFlag),
        ProductLineAbbr = VALUES(productLineAbbr),
        TestLineType = VALUES(testLineType),
        TestLineBaseId = VALUES(testLineBaseId),
        CitationBaseId = VALUES(citationBaseId),
        LabSectionBaseId = VALUES(labSectionBaseId),
        CitationId = VALUES(citationId),
        CitationVersionId = VALUES(citationVersionId),
        PendingFlag = VALUES(pendingFlag),
        SampleSegegrationWIID = VALUES(sampleSegegrationWIID),
        SampleSegegrationWIText = VALUES(sampleSegegrationWIText),
        LabTeamCode = VALUES(labTeamCode),
        StyleVersionId = VALUES(styleVersionId),
        TestItemNo = VALUES(testItemNo),
        OrderSeq = VALUES(orderSeq),
        CustomerTestLineName = VALUES(customerTestLineName)
    </insert>

    <select id="getTestLineInstanceById" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM tb_test_line_instance
        where id = #{testLineInstanceId}
    </select>

    <select id="getTestLineListByOrderId" parameterType="com.sgs.otsnotes.facade.model.req.testLine.OrderTestLineReq" resultType="com.sgs.otsnotes.facade.model.rsp.testLine.OrderTestLineRsp">
        SELECT DISTINCT
            ppTLRel.id
                      ,tlRel.id as ppArtifactRelId
                      ,tl.Id AS testLineInstanceId
                      ,pp.id as ppId
                      ,pp.ppName
                      ,pp.PpVersionId
                      ,ppTLRel.SectionName
                      ,ppTLRel.RootPpBaseId AS rootPpBaseId
                      ,tl.testLineId
                      ,if(tlRel.id is null or trim(tlRel.id)='',if(tlc.id is null or trim(tlc.id)='','',tlc.EvaluationAlias),tlRel.EvaluationAlias) as evaluationAlias
                      ,if(tlc.CitationSectionName is null or trim(tlc.CitationSectionName)='',
                          tlc.citationName,CONCAT(tlc.citationName,',',tlc.CitationSectionName)) AS standardName
                      ,tl.testLineStatus
                      ,if(tlRel.id is null or trim(tlRel.id)='','',tlRel.specificNotes) as ppNotes
                      ,tlb.Id AS testLineBaseId,
            if(sb.SubContractLabCode is null or trim(sb.SubContractLabCode)='',if(lab.LabSectionName is null or trim(lab.LabSectionName)='','/',lab.LabSectionName),
               if(t_c.SubContractLab is null or trim(t_c.SubContractLab)='',CONCAT(if(lab.LabSectionName is null or trim(lab.LabSectionName)='','/',lab.LabSectionName),'||',sb.SubContractLabCode),
                  CONCAT(t_c.SubContractLab,'||',sb.SubContractLabCode))) as labSectionName
                      ,tlb.productLineAbbr
                      ,tlRel.testLineSeq
                      ,(CASE WHEN job.Id IS NULL THEN 0 ELSE 1 END) AS job,
            tl.OrdertestLineRemark as testLineRemark,
            tl.citationBaseId as citationBaseId
        FROM tb_test_line_instance tl
                 LEFT JOIN tb_trims_testline_baseinfo tlb ON tlb.id = tl.testLineBaseId
                 LEFT JOIN tb_trims_artifact_citation_relationship tlc ON tlc.id = tl.citationBaseId
                 LEFT JOIN tre_pp_test_line_relationship ppTLRel ON ppTLRel.TestLineInstanceID = tl.Id
                 LEFT JOIN tre_trims_pp_artifact_relationship tlRel ON tlRel.id = ppTLRel.PpArtifactRelId AND ppTLRel.PpArtifactRelId > 0
                 LEFT JOIN tb_trims_pp_baseinfo pp ON pp.id = ppTLRel.ppBaseId AND pp.PpVersionId = tlRel.PpVersionId
                 LEFT JOIN tb_trims_pp_section_baseinfo pps ON pps.id = tlRel.sectionBaseId
                 LEFT JOIN tb_trims_labsection_baseinfo lab ON lab.Id = tl.labSectionBaseId
                 LEFT JOIN tre_job_test_line_relationship job ON job.TestLineInstanceID = tl.Id
                 LEFT JOIN tb_sub_contract_lab t_c ON tl.ID = t_c.TestLineInstanceID
                 LEFT JOIN tb_sub_contract_test_line_mapping m ON tl.id = m.TestLineInstanceID
                 LEFT JOIN tb_sub_contract sb ON sb.id = m.SubContractID
        WHERE tl.GeneralOrderInstanceID = #{orderId}
    </select>

    <delete id="batchDelete" parameterType="java.util.List">
        DELETE tl,ml FROM tb_test_line_instance tl
        LEFT JOIN tb_test_line_instance_multiplelanguage ml ON tl.Id = ml.TestLineInstanceID
        WHERE tl.Id IN
        <foreach collection="delTestLineIds" item="delTestLineId" open="(" close=")" separator=",">
            #{delTestLineId}
        </foreach>
    </delete>

    <select id="getTestLineInstanceByJobNo" parameterType="java.lang.String" resultType="com.sgs.otsnotes.facade.model.dto.JobTestLineDto">
        SELECT tl.ID,tl.GeneralOrderInstanceID,j.OrderNo,tl.TestLineStatus
        FROM tb_test_line_instance tl
                 INNER JOIN tre_job_test_line_relationship jtl ON tl.ID = jtl.TestLineInstanceID
                 INNER JOIN tb_job j ON j.ID = jtl.JobID
        WHERE tl.TestLineID NOT IN (
            SELECT TestLineID FROM tb_test_line_data_entry_style WHERE ActiveIndicator = 1
        )
          AND j.JobNo = #{jobNo};
    </select>

    <select id="getTestLineStatusByOrderId" parameterType="java.lang.String" resultType="com.sgs.otsnotes.facade.model.rsp.testLine.TestLineStatusRsp">
        SELECT Id AS TestLineInstanceId
             ,TestLineId
             ,TestLineType
             ,TestLineStatus
        FROM tb_test_line_instance
        WHERE GeneralOrderInstanceID = #{orderId}
    </select>

    <select id="getTestLineByTestLineIds" resultType="com.sgs.otsnotes.facade.model.po.TestLineReportPo">
        SELECT O.ID AS OrderId
        ,L.Id
        ,L.TestLineId
        ,L.TestLineStatus AS TestLineStatus
        ,S.ActiveIndicator AS Active
        FROM tb_general_order_instance O
        INNER JOIN tb_test_line_instance L ON O.ID = L.GeneralOrderInstanceID
        AND L.TestLineId IN
        <foreach item="testLineId" index="index" collection="testLineIds" open="(" separator="," close=")">
            #{testLineId}
        </foreach>
        LEFT JOIN tb_test_line_data_entry_style S ON L.TestLineId = S.TestLineId
        WHERE O.OrderNo = #{orderNo}
    </select>

    <select id="getTestLineByOrderId" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM tb_test_line_instance
        WHERE GeneralOrderInstanceID = #{orderId}
    </select>

    <select id="queryJobCountByPpTlRelId" resultType="int" parameterType="java.util.List">
        SELECT
        count(distinct j.ID)
        FROM
        tre_job_test_line_relationship tjtlr
        JOIN tre_pp_test_line_relationship tp ON tjtlr.TestLineInstanceID = tp.TestLineInstanceID
        JOIN tb_job j on j.id = tjtlr.JobId
        <where>
            <choose>
                <when test="subOrderFlag != null and subOrderFlag == true">
                    j.JobStatus in (1103,1105,1106)
                </when>
                <otherwise>
                    j.JobStatus != 1104
                </otherwise>
            </choose>
            <choose>
                <when test="relationIds != null and relationIds.size()>0">
                    and tp.ID IN
                    <foreach collection="relationIds" item="item" open="(" close=")" separator=",">
                        #{item}
                    </foreach>
                </when>
                <otherwise>
                   and 1=2
                </otherwise>
            </choose>
        </where>
    </select>

    <select id="querySubcontractCountByPpTlRelId" resultType="int" parameterType="java.util.List">
        SELECT
        count(distinct sc.ID)
        FROM
        tb_sub_contract_test_line_mapping sctlm
        JOIN tre_pp_test_line_relationship tp ON sctlm.TestLineInstanceID = tp.TestLineInstanceID
        JOIN tb_sub_contract sc on sc.id = sctlm.SubContractID
        WHERE
        sc.Status != 3
        and tp.ID IN
        <foreach collection="list" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>
    <select id="queryReportCountByPpTlRelId" resultType="int" parameterType="java.util.List">
        SELECT
        count(distinct r.ID)
        FROM
        tre_pp_test_line_relationship tp
        join tb_test_matrix tm on tm.TestLineInstanceID = tp.TestLineInstanceID
        join tre_report_matrix_relationship rmrel on rmrel.TestMatrixID = tm.ID
        join tb_report r on r.ID = rmrel.ReportId
        WHERE
        r.ReportStatus not in (201,202)
        and tp.ID IN
        <foreach collection="list" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>
    <select id="queryReportCountByTestLineInstanceIdList" resultType="int" parameterType="java.util.List">
        SELECT
        count(distinct r.ID)
        FROM  tb_test_matrix tm
        join tre_report_matrix_relationship rmrel on rmrel.TestMatrixID = tm.ID
        join tb_report r on r.ID = rmrel.ReportId
        WHERE
        r.ReportStatus not in (201,202)
        and tm.TestLineInstanceID IN
        <foreach collection="testLineInstanceIdList" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>

    <select id="queryTestLineByRelId" resultType="com.sgs.otsnotes.dbstorages.mybatis.model.TestLineInstancePO" parameterType="java.util.List">
        SELECT
        tl.*
        FROM
        tb_test_line_instance tl join tre_pp_test_line_relationship pprel on tl.id = pprel.TestLineInstanceID
        WHERE
        pprel.ID in
        <foreach item="item" index="index" collection="list" open="("
                 separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="getTestLineByOrderNo" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT distinct
        l.ID,
        l.GeneralOrderInstanceID,
        l.TestLineVersionID,
        l.TestLineID,
        tlc.CitationType as CitationTypeID,
        l.TestLineStatus,
        l.ConditionStatus,
        l.TestLineSeq,
        l.OrdertestLineRemark,
        l.ActiveIndicator,
        l.modified,
        l.FileID,
        l.DataEntryCloudID,
        l.ReportSeq,
        l.CalculateConclusionFlag,
        l.ProductLineAbbr,
        l.TestLineType,
        l.TestLineBaseId,
        l.CitationBaseId,
        l.LabSectionBaseId,
        l.CitationId,
        l.CitationVersionId,
        l.SampleSegegrationWIID,
        l.SampleSegegrationWIText,
        l.pendingFlag,
        tlc.CitationName as StandardName,
        tlb.EvaluationName as EvaluationAlias,
        l.Engineer,
        l.TestDueDate,
        l.DocumentReviewFlag,
        ptr.ExtFields as extFields
        FROM tb_general_order_instance o
                 INNER JOIN tb_test_line_instance l ON o.ID = l.GeneralOrderInstanceID
                 LEFT JOIN tb_trims_testline_baseinfo tlb ON tlb.id = l.testLineBaseId
                 LEFT JOIN tb_trims_artifact_citation_relationship tlc ON tlc.id = l.citationBaseId AND tlc.ArtifactType = 0
                 LEFT JOIN tre_pp_test_line_relationship ptr ON l.id = ptr.TestLineInstanceID
                 LEFT JOIN tb_trims_pp_baseinfo pb ON ptr.PpBaseId = pb.id
                 LEFT JOIN tre_trims_pp_artifact_relationship par ON par.id = ptr.PpArtifactRelId
                 LEFT JOIN tb_trims_pp_section_baseinfo psb ON psb.id = par.SectionBaseId
        WHERE o.OrderNo = #{orderNo}
        ORDER BY ISNULL(l.OrderSeq),
                 l.OrderSeq,
                 ptr.Seq,
                 ISNULL(pb.PpNo),
                 pb.PpNo,
                 ISNULL(psb.SectionLevel),
                 psb.SectionLevel,
                 ISNULL(par.TestLineSeq),
                 par.TestLineSeq,
                 l.TestItemNo,
                 l.CreatedDate
    </select>

    <select id="getTestLineByOrderMatrix" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT DISTINCT l.ID,l.GeneralOrderInstanceID,l.TestLineVersionID,l.TestLineID,
                        tlc.CitationType as CitationTypeID,l.TestLineStatus,l.ConditionStatus,
                        l.TestLineSeq,l.OrdertestLineRemark,l.ActiveIndicator,l.modified,l.FileID
                ,l.DataEntryCloudID,l.ReportSeq,l.CalculateConclusionFlag,l.ProductLineAbbr,l.TestLineType
                ,l.TestLineBaseId,l.CitationBaseId,l.LabSectionBaseId,l.CitationId,l.CitationVersionId,l.SampleSegegrationWIID,
                        l.SampleSegegrationWIText,l.pendingFlag,
                        tlc.CitationName as StandardName,tlb.EvaluationName as EvaluationAlias,l.Engineer,l.TestDueDate,l.DocumentReviewFlag
        FROM tb_general_order_instance o
                 INNER JOIN tb_test_line_instance l ON o.ID = l.GeneralOrderInstanceID
                 LEFT JOIN tb_trims_testline_baseinfo tlb ON tlb.id = l.testLineBaseId
                 LEFT JOIN tb_trims_artifact_citation_relationship tlc ON tlc.id = l.citationBaseId AND tlc.ArtifactType = 0
                 LEFT JOIN tre_pp_test_line_relationship ptr ON l.id = ptr.TestLineInstanceID
                 LEFT JOIN tb_trims_pp_baseinfo pb ON ptr.PpBaseId = pb.id
                 LEFT JOIN tre_trims_pp_artifact_relationship par ON par.id = ptr.PpArtifactRelId
                 LEFT JOIN tb_trims_pp_section_baseinfo psb ON psb.id = par.SectionBaseId
        WHERE o.OrderNo = #{orderNo}
          AND EXISTS (
                SELECT
                    1
                FROM
                    tb_test_matrix tm
                WHERE
                    tm.TestLineInstanceID = l.ID
                  AND tm.ActiveIndicator = 1
            )
        ORDER BY ISNULL(l.OrderSeq),
                 l.OrderSeq,
                 ptr.Seq,
                 ISNULL(pb.PpNo),
                 pb.PpNo,
                 ISNULL(psb.SectionLevel),
                 psb.SectionLevel,
                 ISNULL(par.TestLineSeq),
                 par.TestLineSeq,
                 l.TestItemNo,
                 l.CreatedDate
    </select>

    <select id="getTestLineReportByOrderNo" parameterType="java.lang.String" resultType="com.sgs.otsnotes.facade.model.rsp.testLine.TestLineReport2Rsp">

        select tl.TestLineID,tr.ReportNo,tr.ReportStatus
        from tb_test_line_instance tl
                 inner join tb_general_order_instance tgoi on tl.GeneralOrderInstanceID = tgoi.ID
                 left join tb_test_matrix matrix on matrix.TestLineInstanceID = tl.ID
            and tl.GeneralOrderInstanceID = matrix.GeneralOrderInstanceID
                 left join tre_report_matrix_relationship trmr on matrix.id = trmr.TestMatrixID
                 left join tb_report tr on trmr.ReportID = tr.ID
        where tgoi.OrderNo = #{orderNo}
    </select>
    <select id="getTestLineSeqByOrderNo" parameterType="java.lang.String" resultType="com.sgs.otsnotes.facade.model.dto.TestLineInstanceDTO">
        SELECT DISTINCT l.ID AS ID,
                        l.GeneralOrderInstanceID AS generalOrderInstanceID,
                        l.TestLineVersionID AS testLineVersionID,
                        l.TestLineID AS testLineID,
                        l.TestLineEvaluation AS testLineEvaluation,
                        tlc.CitationType AS citationTypeID,
                        l.TestLineStatus AS testLineStatus,
                        l.ConditionStatus AS conditionStatus,
                        l.TestLineSeq AS testLineSeq,
                        l.OrdertestLineRemark AS ordertestLineRemark,
                        l.OrderSeq AS orderSeq,
                        tlc.CitationName as testStandardName,
                        tlc.CitationSectionName as CitationSectionName,
                        tlb.EvaluationName AS evaluationAlias,
                        l.PendingFlag pendingFlag,
                        l.TestLineType AS testLineType,
                        l.CustomerTestLineName AS customerTestLineName
        FROM tb_general_order_instance o
                 INNER JOIN tb_test_line_instance l ON o.ID = l.GeneralOrderInstanceID
                 LEFT JOIN tb_trims_testline_baseinfo tlb ON tlb.id = l.testLineBaseId
                 LEFT JOIN tb_trims_artifact_citation_relationship tlc ON tlc.id = l.citationBaseId
                 LEFT JOIN tre_pp_test_line_relationship ptr ON l.id = ptr.TestLineInstanceID
                 LEFT JOIN tb_trims_pp_baseinfo pb ON ptr.PpBaseId = pb.id
                 LEFT JOIN tre_trims_pp_artifact_relationship par ON par.id = ptr.PpArtifactRelId
                 LEFT JOIN tb_trims_pp_section_baseinfo psb ON psb.id = par.SectionBaseId
        WHERE o.OrderNo = #{orderNo}
        order by ISNULL(l.OrderSeq),
                 l.OrderSeq,
                 ISNULL(pb.PpNo),
                 pb.PpNo,
                 ISNULL(psb.SectionLevel),
                 psb.SectionLevel,
                 ISNULL(par.TestLineSeq),
                 par.TestLineSeq,
                 l.TestItemNo,
                 l.CreatedDate
    </select>

    <select id="getTestLineStatusByOrderNo" parameterType="java.lang.String" resultType="com.sgs.otsnotes.facade.model.dto.TestLineStatusDTO">
        SELECT l.ID testlineInstanceID,l.TestLineID testLineID, l.TestLineStatus testLineStatus,
               l.TestLineSeq testLineSeq,l.ReportSeq reportSeq,l.PendingFlag pendingFlag
        FROM tb_general_order_instance o
                 LEFT JOIN tb_test_line_instance l ON o.ID = l.GeneralOrderInstanceID
        WHERE o.OrderNo = #{orderNo}
    </select>

    <select id="checkTestLineComplete" parameterType="java.lang.String" resultType="com.sgs.otsnotes.facade.model.dto.TestLineStatusDTO">
        SELECT
            l.ID testlineInstanceID,
            l.TestLineID testLineID,
            l.TestLineStatus testLineStatus,
            l.TestLineSeq testLineSeq,
            l.ReportSeq reportSeq
        FROM
            tb_test_line_instance l
                INNER JOIN tb_general_order_instance o ON o.ID = l.GeneralOrderInstanceID
        WHERE
            o.OrderNo = #{orderNo}
          AND l.TestLineStatus NOT IN (703, 706, 707)
            LIMIT 1
    </select>

    <select id="getTestLineByIds" parameterType="java.util.List" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM tb_test_line_instance
        WHERE Id IN
        <foreach item="testLineId" collection="testLineIds" open="(" separator="," close=")">
            #{testLineId}
        </foreach>
    </select>

    <update id="batchUpdateTestlineInstanceFileId" parameterType="java.util.List">
        <foreach item="testLine" index="index" collection="testLines" open="" close="" separator=";">
            UPDATE tb_test_line_instance
            <set>
                FileID = #{testLine.fileID},
                TestLineStatus = #{testLine.testLineStatus}
            </set>
            WHERE id = #{testLine.testLineInstanceId}
        </foreach>
    </update>

    <update id="batchUpdateTestLineStatus" parameterType="java.util.List">
        <foreach item="testLine" collection="testLines" separator=";">
            UPDATE tb_test_line_instance
            SET TestLineStatus = #{testLine.testLineStatus},
            ModifiedDate = #{testLine.modifiedDate},
            <if test="testLine.testEndDate != null">
                TestEndDate = IFNULL(TestEndDate,SYSDATE()),
            </if>
            <if test="testLine.engineer != null and testLine.engineer != ''">
                Engineer = IFNULL(Engineer,#{testLine.engineer}),
            </if>
            ModifiedBy = #{testLine.modifiedBy}
            WHERE Id = #{testLine.ID}
        </foreach>
    </update>

    <update id="batchUpdateTestLineDueDate" parameterType="java.util.List">
        <foreach item="testLine" collection="testLines" separator=";">
            UPDATE tb_test_line_instance
            SET TestDueDate = #{testLine.testDueDate},
            ModifiedDate = #{testLine.modifiedDate},
            ModifiedBy = #{testLine.modifiedBy}
            WHERE Id = #{testLine.ID}
        </foreach>
    </update>

    <update id="batchUpdateTestLineStatus2">
        <foreach item="testLine" collection="testLines" separator=";">
            UPDATE tb_test_line_instance
                SET TestLineStatus = #{testLine.testLineStatus},
                <choose>
                    <!--ReTest TestLine-->
                    <when test="moduleType == 4">
                        ValidateDate = null,
                        ValidateBy = null,
                    </when>
                    <!--NC TestLine、Update Remark TestLine-->
                    <when test="moduleType == 11 or moduleType == 12">
                        OrdertestLineRemark = #{testLine.testLineRemark},
                    </when>
                    <!--Cancel TestLine、Cancel Order、Cancel Cancel Matrix-->
                    <when test="moduleType == 13 or moduleType == 23 or moduleType == 29">
                        ActiveIndicator = #{testLine.activeIndicator},
                    </when>
                    <!--SubContract-->
                    <when test="moduleType == 14">
                        TestLineType = (IFNULL(TestLineType, 0) | #{testLine.testLineType}),
                    </when>
                    <!--Cancel Order for Subcontract Parent -->
                    <when test="moduleType == 17">
                        TestLineType = (IFNULL(TestLineType, 0) ^ #{testLine.testLineType}),
                    </when>
                    <!--Copy Report、Upload TestLine Report-->
                    <when test="moduleType == 18 or moduleType == 19 or moduleType == 25">
                        FileID = #{testLine.fileId},
                    </when>
                    <otherwise>

                    </otherwise>
                </choose>
                ModifiedBy = #{testLine.regionAccount},
                ModifiedDate = #{testLine.updateTime}
            WHERE Id = #{testLine.testLineInstanceId}
            <!--SubContract、SubContractSync、NewSubContract-->
            <if test="(moduleType == 14 or moduleType == 15 or moduleType == 16 or moduleType == 26) and testLine.oldTestLineStatus > 0">
                AND TestLineStatus = #{testLine.oldTestLineStatus}
            </if>
        </foreach>
    </update>

    <update id="batchUpdateTestLineValidate" parameterType="java.util.List">
        <foreach item="testLine" collection="testLines" separator=";">
            UPDATE tb_test_line_instance
            SET ValidateDate = #{testLine.validateDate},
            ValidateBy = #{testLine.validateBy}
            WHERE Id = #{testLine.ID}
        </foreach>
    </update>

    <update id="updateTestLineType">
        UPDATE tb_test_line_instance
        SET TestLineType = #{testLineType},
        ModifiedDate = #{modifiedDate},
        ModifiedBy = #{modifiedBy}
        <where>
            <choose>
                <when test="testLineInstanceId != null and testLineInstanceId != ''">
                    AND id= #{testLineInstanceId}
                </when>
                <otherwise>
                    AND 1!=1
                </otherwise>
            </choose>
        </where>;
    </update>

    <select id="getRequirmentTestLineInfoList" resultMap="RequirmentTestLineResultMap" parameterType="java.lang.String">
        SELECT
            tl.ID AS TestLineInstanceId
             ,tl.TestLineId
             ,acrel.EvaluationAlias
             ,tlb.TestLineVersionId
             ,acrel.CitationVersionId AS StandardVersionId
             ,tlb.ProductLineAbbr
             ,lab.LabSectionName
        FROM tb_test_line_instance tl
                 INNER JOIN tb_trims_testline_baseinfo tlb ON tlb.Id = tl.TestLineBaseId
                 INNER JOIN tb_trims_artifact_citation_relationship acrel ON acrel.Id = tl.CitationBaseId
                 LEFT JOIN tb_trims_labsection_baseinfo lab ON lab.Id = tl.LabSectionBaseId
        WHERE tl.GeneralOrderInstanceID = #{orderId}
          AND tl.TestLineStatus != 708
        AND IFNULL(tl.ActiveIndicator, 0) > 0
    </select>

    <select id="getPpSampleTestLineVersionIdsNew" resultType="java.lang.Integer" parameterType="com.sgs.otsnotes.facade.model.info.limitgroup.OrderPpSampleInfo">
        SELECT DISTINCT
            tlb.TestLineVersionId
        FROM tb_test_line_instance tl
                 INNER JOIN tb_trims_testline_baseinfo tlb ON tlb.Id = tl.TestLineBaseId
                 INNER JOIN tre_pp_test_line_relationship pptl ON pptl.TestLineInstanceID = tl.Id
                 INNER JOIN tre_pp_sample_relationship pps ON pps.PPTLRelID = pptl.Id
                 INNER JOIN tb_general_order_instance o ON o.Id = pptl.GeneralOrderInstanceID
        WHERE o.OrderNo = #{orderNo}
          AND pptl.PpBaseId = #{ppBaseId}
          AND pps.TestSampleId = #{sampleId}
    </select>

    <select id="getRequirmentTestLineMatrixList" resultType="com.sgs.otsnotes.facade.model.info.limitgroup.TestLineMatrixInfo" parameterType="java.lang.String">
        SELECT
            tm.Id AS MatrixId
             ,ts.Id AS SampleId
             ,ts.SampleNo
             ,ts.Description
             ,ts.SampleSeq
        FROM tb_test_matrix tm
                 INNER JOIN tb_test_sample ts ON ts.Id = tm.TestSampleID
        WHERE tm.TestLineInstanceID = #{testLineInstanceId}
          AND ts.ActiveIndicator = 1
          AND ts.Applicable = 0
        ORDER BY ts.SampleSeq
    </select>

    <select id="getEmTestLineInfoList" resultType="com.sgs.otsnotes.facade.model.dto.EmTestLineDTO">
        SELECT LI.TestLineID
--             ,LI.StandardName
             ,SI.LabSectionName
            /*,SB.SubContractNo
            ,SB.SubContractLabName*/
        FROM tb_general_order_instance GOI
                 INNER JOIN tb_test_line_instance LI ON GOI.ID = LI.GeneralOrderInstanceID AND LI.ActiveIndicator = 1
                 LEFT JOIN tb_lab_section_instance SI ON SI.TestLineInstanceID = LI.ID AND SI.GeneralOrderInstanceID = LI.GeneralOrderInstanceID
            /*LEFT JOIN tb_sub_contract_test_line_mapping M ON LI.ID = M.TestLineInstanceID
            LEFT JOIN tb_sub_contract SB ON SB.id = M.SubContractID*/
        WHERE GOI.OrderNo = #{orderNo}
    </select>

    <select id="getTestLineInstanceByJobNoForEm" parameterType="java.lang.String" resultType="com.sgs.otsnotes.facade.model.dto.JobTestLineForEmDto">
        SELECT tl.ID,tl.GeneralOrderInstanceID,j.OrderNo,j.JobNo,tl.TestLineStatus,tl.ProductLineAbbr
        FROM tb_test_line_instance tl
        INNER JOIN tre_job_test_line_relationship jtl ON tl.ID = jtl.TestLineInstanceID
        INNER JOIN tb_job j ON j.ID = jtl.JobID
        WHERE j.JobNo IN
        <foreach item="jobNo" index="index" collection="jobNos" open="(" separator="," close=")">
            #{jobNo,jdbcType=VARCHAR}
        </foreach>
    </select>



    <select id="getTestLineStatus" resultType="com.sgs.otsnotes.dbstorages.mybatis.model.TestLineInstancePO">
        SELECT Id
        ,TestLineStatus
        FROM tb_test_line_instance
        WHERE Id IN
        <foreach item="testLineInstanceId" index="index" collection="testLineInstanceIds" open="(" separator="," close=")">
            #{testLineInstanceId}
        </foreach>
    </select>

    <select id="getTestLineRemark" resultType="java.lang.String" parameterType="java.lang.String">
        SELECT
            OrdertestLineRemark
        FROM tb_test_line_instance
        WHERE Id = #{testLineInstanceId}
    </select>

    <select id="getTestLineBreakDownInfoList"
            parameterType="java.lang.String"
            resultType="com.sgs.otsnotes.facade.model.info.TestLineBreakDownInfo">
        SELECT DISTINCT
            rel.Id AS PPId,
            IFNULL( rel.PpArtifactRelId, 0 ) AS PpArtifactRelId
            ,pp.PpVersionID
                      ,pp.PpNo
                      ,pp.PpName AS PPClientRefNo
                      ,rel.aid
                      ,rel.sectionName
                      ,tl.Id AS TestLineInstanceId
                      ,tl.TestLineID
                      ,tl.TestLineVersionId
                      ,tl.testLineType
                      ,tl.TestLineStatus
                      ,tl.CitationBaseId as citationBaseId
                      ,IF(pa.id IS NULL OR TRIM(pa.id)='',IF(acrel.id IS NULL OR TRIM(acrel.id)='','',acrel.EvaluationAlias),pa.EvaluationAlias) AS testLineName
                      ,ttl.ProductLineAbbr
                      ,acrel.CitationVersionId AS TestStandardVersionId
                      ,acrel.CitationId AS TestStandardId
                      ,acrel.CitationName AS TestStandardName
                      ,acrel.CitationType as TestStandardType
                      ,sc.TestLineInstanceId AS subContractTestLineId
                      ,ppTlRel.TlExecutionClassificationCode
                      ,case when tl.testLineType=4 or tl.testLineType &amp;4 > 0 then 1 else 0 end as csPP
                      ,(CASE WHEN sc.SubContractLabName IS NULL THEN lab.LabSectionName ELSE sc.SubContractLabName END) AS LabSectionName
        FROM tb_general_order_instance o
                 INNER JOIN tb_test_line_instance tl ON tl.GeneralOrderInstanceID = o.Id
                 INNER JOIN tb_trims_testline_baseinfo ttl ON ttl.Id = tl.TestLineBaseId
                 INNER JOIN tre_pp_test_line_relationship rel ON rel.GeneralOrderInstanceID = tl.GeneralOrderInstanceID AND rel.TestLineInstanceID = tl.Id
                 LEFT JOIN tb_trims_pp_baseinfo pp ON pp.Id = rel.PpBaseId
                 LEFT JOIN tb_trims_labsection_baseinfo lab ON lab.Id = tl.LabSectionBaseId
                 LEFT JOIN tb_trims_artifact_citation_relationship acrel ON acrel.id=tl.CitationBaseId
                 LEFT JOIN tre_trims_pp_artifact_relationship pa ON rel.PpArtifactRelId = pa.id
                 LEFT JOIN (
            SELECT DISTINCT
                rel.TestLineInstanceID
                          ,c.SubContractLabName
            FROM tb_sub_contract c
                     INNER JOIN tb_sub_contract_test_line_mapping rel ON rel.SubContractID = c.Id
            WHERE c.OrderNo = #{orderNo}
        ) sc ON tl.Id = sc.TestLineInstanceID
                 LEFT JOIN tre_trims_pp_artifact_relationship ppTlRel on ppTlRel.id = rel.PpArtifactRelId
        WHERE o.OrderNo = #{orderNo}
    </select>

    <update id="updateBatchStandard" parameterType="java.util.List">
        <foreach collection="testLines" item="testLine" separator=";">
            UPDATE tb_test_line_instance
            <set>
                CitationBaseId = #{testLine.citationBaseId},
                CitationId = #{testLine.citationId},
                CitationVersionId = #{testLine.citationVersionId},
                ClientStandard = #{testLine.clientStandard},
                ModifiedDate=#{testLine.modifiedDate},
                ModifiedBy=#{testLine.modifiedBy},
                CitationName=#{testLine.citationName}
            </set>
            WHERE id = #{testLine.ID}
        </foreach>
    </update>
    <update id="updateBatchStandardMultilanguage" parameterType="java.util.List">
        <foreach collection="testLines" item="testLine" separator=";">
            UPDATE tb_test_line_instance_multiplelanguage
            <set>
                ModifiedDate = #{testLine.modifiedDate},
                ModifiedBy=#{testLine.modifiedBy},
                CitationName=#{testLine.citationName}
            </set>
            WHERE id = #{testLine.ID}
        </foreach>
    </update>
    <insert id="batchSaveMultiLanguageTestLine">
        INSERT INTO tb_test_line_instance_multiplelanguage (
        <include refid="Multiple_Language_Test_Line_Base_Column_List" />
        )
        VALUES
        <foreach collection="testLines" item="testLine" separator=",">
            (
            #{testLine.ID}
            ,#{testLine.testLineInstanceID}
            ,#{testLine.languageId}
            ,#{testLine.evaluationAlias}
            ,#{testLine.standardName}
            ,#{testLine.testLineEvaluation}
            ,#{testLine.createdDate}
            ,#{testLine.createdBy}
            ,#{testLine.modifiedDate}
            ,#{testLine.modifiedBy}
            ,#{testLine.standardSectionName}
            ,#{testLine.citationName}
            )
        </foreach>
        ON DUPLICATE KEY UPDATE
        TestLineInstanceID = VALUES(testLineInstanceID),
        LanguageId = VALUES(languageId),
        EvaluationAlias = VALUES(evaluationAlias),
        StandardName = VALUES(standardName),
        TestLineEvaluation = VALUES(testLineEvaluation),
        CreatedDate = VALUES(createdDate),
        CreatedBy = VALUES(createdBy),
        ModifiedDate = VALUES(modifiedDate),
        ModifiedBy = VALUES(modifiedBy),
        StandardSectionName = VALUES(standardSectionName),
        CitationName = VALUES(citationName)
    </insert>
    <update id="batchUpdateTlLabSectionBaseId" parameterType="java.util.List">
        <foreach collection="labSections" item="item" >
            update tb_test_line_instance
            set LabSectionBaseId = #{item.labSectionID}
            where ID = #{item.testLineInstanceID};
        </foreach>
    </update>

    <update id="updateBatchConditionStatus" parameterType="java.util.List">
        <foreach collection="testLines" item="testLine" separator=";">
            UPDATE tb_test_line_instance
            <set>
                ConditionStatus = #{testLine.conditionStatus},
                ModifiedDate=#{testLine.modifiedDate},
                ModifiedBy=#{testLine.modifiedBy}
            </set>
            WHERE id = #{testLine.ID}
        </foreach>
    </update>

    <select id="getSubContractTestLineStatus" resultType="com.sgs.otsnotes.dbstorages.mybatis.model.TestLineInstancePO">
        SELECT
            tl.ID,
            tl.TestLineType,
            tl.TestLineStatus
        from tb_sub_contract sc
                 INNER JOIN tb_sub_contract_test_line_mapping sctl ON sctl.SubContractID = sc.Id
                 INNER JOIN tb_test_line_instance tl ON tl.Id = sctl.TestLineInstanceID
        WHERE sc.OrderNo = #{oldOrderNo}
          AND sc.SubContractNo = #{subContractNo}
    </select>

    <select id="getSubContractInfoByTestLineId" resultType="com.sgs.otsnotes.dbstorages.mybatis.model.SubContractTestLineMappingPO">
        SELECT
            ID,
            SubContractID,
            TestLineInstanceID
        from tb_sub_contract_test_line_mapping
        WHERE TestLineInstanceID = #{testLineInstanceId}
        limit 1
    </select>

    <select id="getTestLineIdByInstId" parameterType="java.lang.String" resultType="java.lang.Integer">
        SELECT TestLineID from tb_test_line_instance where ID=#{testLineInstanceId}
    </select>

    <resultMap id="TestLineResultMap" type="com.sgs.otsnotes.dbstorages.mybatis.extmodel.testline.TestLineBaseInfo" >
        <id column="Id" property="id" jdbcType="BIGINT" />
        <result column="TestItemId" property="testItemId" jdbcType="INTEGER" />
        <result column="TestItemName" property="testItemName" jdbcType="VARCHAR" />
        <result column="TestLineId" property="testLineId" jdbcType="INTEGER" />
        <result column="TestLineVersionId" property="testLineVersionId" jdbcType="INTEGER" />
        <result column="EvaluationName" property="evaluationName" jdbcType="VARCHAR" />
        <result column="ProductLineId" property="productLineId" jdbcType="INTEGER" />
        <result column="ProductLineAbbr" property="productLineAbbr" jdbcType="VARCHAR" />
        <result column="MethodDesc" property="methodDesc" jdbcType="VARCHAR" />
        <result column="ReplicationNo" property="replicationNo" jdbcType="INTEGER" />
        <result column="TestLineStatus" property="testLineStatus" jdbcType="INTEGER" />

        <collection property="matrixs"
                    ofType="com.sgs.otsnotes.facade.model.info.limitgroup.TestLineMatrixInfo"
                    javaType="java.util.List"
                    column="TestLineInstanceId"
                    select="getRequirmentTestLineMatrixList" />
    </resultMap>

    <select id="getTestLineInfo" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT * from tb_test_line_instance where ID = #{testLineInstanceId}
    </select>

    <update id="updateTestlineInstanceRemark" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.TestLineInstancePO">
        UPDATE tb_test_line_instance
        set
            TestLineStatus = #{testLineStatus},
            ModifiedDate=#{modifiedDate},
            ModifiedBy=#{modifiedBy},
            OrdertestLineRemark=#{ordertestLineRemark}
        WHERE id = #{ID}
    </update>

    <select id="getTestLineConfirmMatrixInfoByOrderNo" resultType="com.sgs.otsnotes.dbstorages.mybatis.extmodel.TestLineConfirmMatrixInfo" parameterType="java.lang.String">
        SELECT distinct tl.ID
                      ,tl.TestItemNo
                      ,tl.TestLineBaseId
                      ,tl.ActiveIndicator
                      ,tl.GeneralOrderInstanceID
                      ,tsb.TestLineVersionId
                      ,tsb.TestLineID
                      ,citation.CitationVersionId as StandardVersionID
                      ,citation.CitationName as StandardName
                      ,tl.TestLineStatus
                      ,IFNULL(tl.PendingFlag, 0) AS PendingFlag
                      ,tl.ConditionStatus
                      ,tl.TestLineType
                      ,tl.ProductLineAbbr AS bu
                      ,tsb.ProductLineAbbr
                      ,trims_lab_base.LabSectionName
                      ,tl.CitationId
                      ,tl.CitationName
                      ,tptlr.PpBaseId
        FROM tb_test_line_instance tl
                 LEFT JOIN tb_trims_labsection_baseinfo trims_lab_base on tl.LabSectionBaseId=trims_lab_base.ID  AND trims_lab_base.LabSectionStatus=1
                 LEFT JOIN tre_pp_test_line_relationship tptlr on tl.id = tptlr.TestLineInstanceID
                 JOIN tb_trims_artifact_citation_relationship citation on citation.ID=tl.CitationBaseId
                 JOIN tb_trims_testline_baseinfo tsb on tl.TestLineBaseId=tsb.ID
                 JOIN tb_general_order_instance tgo ON tl.GeneralOrderInstanceID = tgo.id
        WHERE   tgo.orderno = #{orderNo} AND tl.TestLineStatus != 706 and tl.TestLineStatus != 707 And tl.TestLineStatus!= 708
    </select>


    <select id="getUsedTestLineConfirmMatrixInfoByOrderNo" resultType="com.sgs.otsnotes.dbstorages.mybatis.extmodel.TestLineConfirmMatrixInfo" parameterType="java.lang.String">
        SELECT DISTINCT
            tl.ID
                      ,tl.ActiveIndicator
                      ,tl.GeneralOrderInstanceID
                      ,tsb.TestLineVersionId
                      ,tsb.TestLineID
                      ,tl.TestLineBaseId
                      ,citation.CitationVersionId AS StandardVersionID
                      ,citation.CitationName AS StandardName
                      ,tl.TestLineStatus
                      ,IFNULL(tl.PendingFlag, 0) AS PendingFlag
                      ,tl.ConditionStatus
                      ,tsb.ProductLineAbbr
                      ,trims_lab_base.LabSectionName
        FROM tb_test_line_instance tl
                 LEFT JOIN tb_trims_labsection_baseinfo trims_lab_base on tl.LabSectionBaseId=trims_lab_base.ID  and trims_lab_base.LabSectionStatus=1
                 JOIN tb_trims_artifact_citation_relationship citation on citation.ID=tl.CitationBaseId
                 JOIN tb_trims_testline_baseinfo tsb on tl.TestLineBaseId=tsb.ID
                 JOIN tb_general_order_instance tgo ON tl.GeneralOrderInstanceID = tgo.id
                 JOIN tb_test_matrix tm ON tl.id = tm.TestLineInstanceID
        WHERE   tgo.orderno = #{orderNo}
          AND tl.TestLineStatus != 706 and tl.TestLineStatus != 707 And tl.TestLineStatus!= 708
    </select>

    <sql id="selectTestLine">
        select distinct tli.ID as ID,tsb.TestLineVersionID ,tsb.EvaluationName
        from tb_test_line_instance tli
                 left join tb_trims_testline_baseinfo tsb on tli.TestLineBaseId=tsb.ID
                 left join  tb_trims_labsection_baseinfo lsi ON tli.LabSectionBaseId = lsi.ID
                 left join tb_general_order_instance goi on goi.ID=tli.GeneralOrderInstanceID
                 left join tre_pp_test_line_relationship pptlr on pptlr.GeneralOrderInstanceID=goi.ID and tli.ID=pptlr.TestLineInstanceID
                 left Join   tre_trims_pp_artifact_relationship pptl_trim on pptlr.PpArtifactRelId=pptl_trim.id AND pptlr.PpArtifactRelId > 0
                 left JOIN   tb_trims_pp_baseinfo pp on pp.id = pptlr.PpBaseId AND pp.PpVersionId = pptl_trim.PpVersionId
        where goi.OrderNo=#{orderNo}
    </sql>
    <select id="getTlTestLineByOrderNo" resultType="com.sgs.otsnotes.dbstorages.mybatis.extmodel.TestLineConfirmMatrixInfo">
        <include refid="selectTestLine" />
        order by lsi.LabSectionSeq is null ,lsi.LabSectionSeq asc;
    </select>

    <select id="getPPTestLineByOrderNo" resultType="com.sgs.otsnotes.dbstorages.mybatis.extmodel.TestLineConfirmMatrixInfo">
        <include refid="selectTestLine" />
        order by pp.PPVersionID is null,lsi.LabSectionSeq is null,SectionLevel
        asc,pptl_trim.TestLineSeq asc,lsi.LabSectionSeq asc,SectionLevel
        asc,pptl_trim.TestLineSeq asc
    </select>

    <update id="updateTestlineSepecialSeq" parameterType="java.util.List">
        <foreach collection="list" item="item" index="index" open=""
                 close="" separator=";">
            update tb_test_line_instance
            <set>
                ReportSeq = #{item.reportSeq}
            </set>
            where ID=#{item.ID}
        </foreach>
    </update>

    <sql id="selectBaseTestLine">
        SELECT distinct tl.ID
                      ,tl.GeneralOrderInstanceID
                      ,tl.pendingFlag
                      ,tlb.TestLineVersionID
                      ,tlb.TestLineID
                      ,tlc.CitationVersionId as StandardVersionID
                      ,tlc.CitationName as StandardName
                      ,tlc.CitationID AS StandardID
                      ,tlc.CitationSectionID as StandardSectionID
                      ,tlc.CitationSectionName as StandardSectionName
                      ,tl.TestLineStatus
                      ,tl.ConditionStatus
                      ,tl.CitationBaseId as citationBaseId
                      ,tl.testLineBaseId
                      ,tl.testItemNo
                      ,tlc.EvaluationAlias
                      ,tl.citationName as citationName
                      ,tl.DocumentReviewFlag as documentReviewFlag
                      ,tliml.citationName as citationNameCN
        FROM tb_test_line_instance tl
            LEFT JOIN tb_test_line_instance_multiplelanguage tliml on tliml.TestLineInstanceID = tl.ID
                 LEFT JOIN tb_trims_testline_baseinfo tlb ON tlb.id = tl.testLineBaseId
                 LEFT JOIN tb_trims_artifact_citation_relationship tlc ON tlc.id = tl.citationBaseId
    </sql>

    <select id="getBaseTestLineById" parameterType="java.lang.String" resultMap="BaseResultMap">
        <include refid="selectBaseTestLine" />
        WHERE tl.Id =  #{testLineInstanceID}
        limit 1
    </select>

    <select id="getBaseTestLineByIds" parameterType="java.util.List" resultMap="BaseResultMap">
        <include refid="selectBaseTestLine" />
        WHERE tl.Id IN
        <foreach item="testLineId" collection="testLineIds" open="(" separator="," close=")">
            #{testLineId}
        </foreach>
    </select>

    <update id="cancelTestLine" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.TestLineInstancePO">
        UPDATE tb_test_line_instance
        SET testLineSeq = #{testLineSeq},
            ModifiedDate = #{modifiedDate},
            ModifiedBy = #{modifiedBy},
            ActiveIndicator=0,
            TestLineStatus=706
        WHERE Id = #{ID}
    </update>

    <select id="queryPretreatmentForMasterList" parameterType="java.lang.String"
            resultType="com.sgs.otsnotes.dbstorages.mybatis.extmodel.masterlist.PretreatmentDO">
        select tl.id,ttcr.CitationName standardName,sa.sampleNo,tl.testlineid ,
               otcg.combinedconditionDescription from tb_test_line_instance tl
                                                          LEFT JOIN tb_trims_artifact_citation_relationship ttcr
                                                                    ON tl.TestLineVersionId = ttcr.ArtifactVersionId AND tl.CitationBaseId = ttcr.Id
                                                          left
                                                              join tb_test_matrix ma on tl.id = ma.testlineinstanceid
                                                          left join tb_test_sample sa on sa.id = ma.testsampleid
                                                          left join tb_test_condition_group otcg on ma.TestConditionGroupID =
                                                                                                    otcg.id
                                                          left join tb_general_order_instance goi on goi.id =
                                                                                                     tl.GeneralOrderInstanceID
        where goi.orderno= #{orderNo}  and tl.ActiveIndicator = 1
          AND <![CDATA[ (tl.TestLineType & 1) = 1]]>
        order by sa.sampleseq
    </select>

    <select id="queryTestLineByReportId" resultMap="BaseResultMap"
            parameterType="java.lang.String">
        SELECT DISTINCT
            tl.*
        FROM tb_test_line_instance tl
                 JOIN tb_test_matrix tm ON tl.ID = tm.TestLineInstanceID
                 JOIN tre_report_matrix_relationship tmr ON tmr.TestMatrixID = tm.ID
                 JOIN tb_report r ON r.ID = tmr.ReportID
        WHERE r.id = #{reportId}
          AND tl.testLineStatus != 707
    </select>

    <select id="queryReportByTestLine" resultType="com.sgs.otsnotes.facade.model.dto.ReportDTO"
            parameterType="java.lang.String">
        SELECT DISTINCT
            r.ID id,
            r.ReportNo reportNo,
            r.ReportStatus reportStatus
        FROM
            tb_test_line_instance tl
                JOIN tb_test_matrix tm ON tl.ID = tm.TestLineInstanceID
                JOIN tre_report_matrix_relationship tmr ON tmr.TestMatrixID = tm.ID
                JOIN tb_report r ON r.ID = tmr.ReportID
        WHERE
            tl.id = #{testLineInsId}
    </select>

    <delete id="batchDeleteExt" parameterType="com.sgs.otsnotes.facade.model.info.testline.DelPPTestLineRelInfo">
            DELETE FROM tb_conclusion
            <where>
                <choose>
                    <when test="testLineInstanceIds != null and testLineInstanceIds.size() > 0">
                        AND TestLineInstanceID in
                        <foreach item="testLineInstanceId" collection="testLineInstanceIds" open="(" separator="," close=")">
                            #{testLineInstanceId}
                        </foreach>
                    </when>
                    <otherwise>
                        AND 1!=1
                    </otherwise>
                </choose>
            </where>;

            DELETE FROM tb_sub_contract_lab
            <where>
                <choose>
                    <when test="testLineInstanceIds != null and testLineInstanceIds.size() > 0">
                        AND TestLineInstanceID in
                        <foreach item="testLineInstanceId" collection="testLineInstanceIds" open="(" separator="," close=")">
                            #{testLineInstanceId}
                        </foreach>
                    </when>
                    <otherwise>
                        AND 1!=1
                    </otherwise>
                </choose>
            </where>;


            <!--DELETE ppCondRelMult FROM tre_pp_condition_relationship_multiplelanguage ppCondRelMult
            JOIN tre_pp_condition_relationship ppConRel ON ppCondRelMult.PpConditionRelationshipID = ppConRel.ID
            JOIN tre_pp_test_line_relationship ppTestLineRel ON ppConRel.PPTestLineRelID=ppTestLineRel.ID
            JOIN tb_test_line_instance t_l ON t_l.ID=ppTestLineRel.TestLineInstanceID
            WHERE  t_l.ID=#{testLineInstanceId};

            DELETE ppConRel FROM tre_pp_condition_relationship ppConRel
            JOIN tre_pp_test_line_relationship ppTestLineRel ON ppConRel.PPTestLineRelID=ppTestLineRel.ID
            JOIN tb_test_line_instance t_l ON t_l.ID=ppTestLineRel.TestLineInstanceID
            WHERE  t_l.ID=#{testLineInstanceId};-->

            DELETE ppSampleRel FROM tre_pp_sample_relationship  ppSampleRel
            JOIN tre_pp_test_line_relationship ppTestLineRel ON ppSampleRel.PPTLRelID=ppTestLineRel.ID
            JOIN tb_test_line_instance t_l ON t_l.ID=ppTestLineRel.TestLineInstanceID
            <where>
                <choose>
                    <when test="testLineInstanceIds != null and testLineInstanceIds.size() > 0">
                        AND t_l.ID in
                        <foreach item="testLineInstanceId" collection="testLineInstanceIds" open="(" separator="," close=")">
                            #{testLineInstanceId}
                        </foreach>
                    </when>
                    <otherwise>
                        AND 1!=1
                    </otherwise>
                </choose>
            </where>;

            <!--DELETE pp_tl_rel_mult FROM tre_pp_test_line_relationship_multiplelanguage pp_tl_rel_mult
            JOIN tre_pp_test_line_relationship pp_tl_rel ON pp_tl_rel_mult.PpTestLineRelationshipID = pp_tl_rel.ID
            WHERE pp_tl_rel.TestLineInstanceID=#{testLineInstanceId};-->

            DELETE FROM tre_pp_test_line_relationship
            <where>
                <choose>
                    <when test="testLineInstanceIds != null and testLineInstanceIds.size() > 0">
                        AND tre_pp_test_line_relationship.TestLineInstanceID in
                        <foreach item="testLineInstanceId" collection="testLineInstanceIds" open="(" separator="," close=")">
                            #{testLineInstanceId}
                        </foreach>
                    </when>
                    <otherwise>
                        AND 1!=1
                    </otherwise>
                </choose>
            </where>;

            DELETE FROM tre_job_test_line_relationship
            <where>
                <choose>
                    <when test="testLineInstanceIds != null and testLineInstanceIds.size() > 0">
                        AND tre_job_test_line_relationship.TestLineInstanceID in
                        <foreach item="testLineInstanceId" collection="testLineInstanceIds" open="(" separator="," close=")">
                            #{testLineInstanceId}
                        </foreach>
                    </when>
                    <otherwise>
                        AND 1!=1
                    </otherwise>
                </choose>
            </where>;

            <!--DELETE FROM tb_lab_section_instance WHERE
            tb_lab_section_instance.TestLineInstanceID=#{testLineInstanceId};-->

            DELETE FROM tb_analyte_instance
            <where>
                <choose>
                    <when test="testLineInstanceIds != null and testLineInstanceIds.size() > 0">
                        AND tb_analyte_instance.TestLineInstanceID in
                        <foreach item="testLineInstanceId" collection="testLineInstanceIds" open="(" separator="," close=")">
                            #{testLineInstanceId}
                        </foreach>
                    </when>
                    <otherwise>
                        AND 1!=1
                    </otherwise>
                </choose>
            </where>;

            DELETE test_cond_group_mult FROM tb_test_condition_group_multiplelanguage test_cond_group_mult
            JOIN tb_test_condition_group test_cond_group ON test_cond_group_mult.TestConditionGroupID = test_cond_group.ID
            <where>
                <choose>
                    <when test="testLineInstanceIds != null and testLineInstanceIds.size() > 0">
                        AND test_cond_group.TestLineInstanceID in
                        <foreach item="testLineInstanceId" collection="testLineInstanceIds" open="(" separator="," close=")">
                            #{testLineInstanceId}
                        </foreach>
                    </when>
                    <otherwise>
                        AND 1!=1
                    </otherwise>
                </choose>
            </where>;

            DELETE FROM tb_test_condition_group
            <where>
                <choose>
                    <when test="testLineInstanceIds != null and testLineInstanceIds.size() > 0">
                        AND tb_test_condition_group.TestLineInstanceID in
                        <foreach item="testLineInstanceId" collection="testLineInstanceIds" open="(" separator="," close=")">
                            #{testLineInstanceId}
                        </foreach>
                    </when>
                    <otherwise>
                        AND 1!=1
                    </otherwise>
                </choose>
            </where>;

            DELETE test_cond_inst_mult FROM tb_test_condition_instance_multiplelanguage test_cond_inst_mult
            JOIN tb_test_condition_instance test_cond_inst ON test_cond_inst_mult.TestConditionInstanceID=test_cond_inst.ID
            <where>
                <choose>
                    <when test="testLineInstanceIds != null and testLineInstanceIds.size() > 0">
                        AND test_cond_inst.TestLineInstanceID in
                        <foreach item="testLineInstanceId" collection="testLineInstanceIds" open="(" separator="," close=")">
                            #{testLineInstanceId}
                        </foreach>
                    </when>
                    <otherwise>
                        AND 1!=1
                    </otherwise>
                </choose>
            </where>;

            DELETE FROM tb_test_condition_instance
            <where>
                <choose>
                    <when test="testLineInstanceIds != null and testLineInstanceIds.size() > 0">
                        AND tb_test_condition_instance.TestLineInstanceID in
                        <foreach item="testLineInstanceId" collection="testLineInstanceIds" open="(" separator="," close=")">
                            #{testLineInstanceId}
                        </foreach>
                    </when>
                    <otherwise>
                        AND 1!=1
                    </otherwise>
                </choose>
            </where>;

            DELETE lim_inst_mult FROM tb_limit_instance_multiplelanguage lim_inst_mult
            JOIN tb_limit_instance lim_inst ON lim_inst_mult.LimitInstanceID = lim_inst.ID
            <where>
                <choose>
                    <when test="testLineInstanceIds != null and testLineInstanceIds.size() > 0">
                        AND lim_inst.TestLineInstanceID in
                        <foreach item="testLineInstanceId" collection="testLineInstanceIds" open="(" separator="," close=")">
                            #{testLineInstanceId}
                        </foreach>
                    </when>
                    <otherwise>
                        AND 1!=1
                    </otherwise>
                </choose>
            </where>;

            DELETE FROM tb_limit_instance
            <where>
                <choose>
                    <when test="testLineInstanceIds != null and testLineInstanceIds.size() > 0">
                        AND tb_limit_instance.TestLineInstanceID in
                        <foreach item="testLineInstanceId" collection="testLineInstanceIds" open="(" separator="," close=")">
                            #{testLineInstanceId}
                        </foreach>
                    </when>
                    <otherwise>
                        AND 1!=1
                    </otherwise>
                </choose>
            </where>;

            DELETE FROM tb_test_data
            <where>
                <choose>
                    <when test="testLineInstanceIds != null and testLineInstanceIds.size() > 0">
                        AND tb_test_data.TestLineInstanceID in
                        <foreach item="testLineInstanceId" collection="testLineInstanceIds" open="(" separator="," close=")">
                            #{testLineInstanceId}
                        </foreach>
                    </when>
                    <otherwise>
                        AND 1!=1
                    </otherwise>
                </choose>
            </where>;

            DELETE FROM tb_test_specimen
            <where>
                <choose>
                    <when test="testLineInstanceIds != null and testLineInstanceIds.size() > 0">
                        AND tb_test_specimen.TestLineInstanceID in
                        <foreach item="testLineInstanceId" collection="testLineInstanceIds" open="(" separator="," close=")">
                            #{testLineInstanceId}
                        </foreach>
                    </when>
                    <otherwise>
                        AND 1!=1
                    </otherwise>
                </choose>
            </where>;

            DELETE test_line_inst_mult FROM tb_test_line_instance_multiplelanguage test_line_inst_mult
            JOIN tb_test_line_instance test_line_inst ON test_line_inst_mult.TestLineInstanceID = test_line_inst.ID
            <where>
                <choose>
                    <when test="testLineInstanceIds != null and testLineInstanceIds.size() > 0">
                        AND test_line_inst.ID in
                        <foreach item="testLineInstanceId" collection="testLineInstanceIds" open="(" separator="," close=")">
                            #{testLineInstanceId}
                        </foreach>
                    </when>
                    <otherwise>
                        AND 1!=1
                    </otherwise>
                </choose>
            </where>;

            DELETE FROM tb_sub_contract_test_line_mapping
            <where>
                <choose>
                    <when test="testLineInstanceIds != null and testLineInstanceIds.size() > 0">
                        AND TestLineInstanceID in
                        <foreach item="testLineInstanceId" collection="testLineInstanceIds" open="(" separator="," close=")">
                            #{testLineInstanceId}
                        </foreach>
                    </when>
                    <otherwise>
                        AND 1!=1
                    </otherwise>
                </choose>
            </where>;

            DELETE FROM tb_test_line_instance
            <where>
                <choose>
                    <when test="testLineInstanceIds != null and testLineInstanceIds.size() > 0">
                        AND ID in
                        <foreach item="testLineInstanceId" collection="testLineInstanceIds" open="(" separator="," close=")">
                            #{testLineInstanceId}
                        </foreach>
                    </when>
                    <otherwise>
                        AND 1!=1
                    </otherwise>
                </choose>
            </where>;

            DELETE from tb_testline_pretreatment_relationship
            <where>
                <choose>
                    <when test="testLineInstanceIds != null and testLineInstanceIds.size() > 0">
                        AND PretreatmentTestLineInstanceId in
                        <foreach item="testLineInstanceId" collection="testLineInstanceIds" open="(" separator="," close=")">
                            #{testLineInstanceId}
                        </foreach>
                    </when>
                    <otherwise>
                        AND 1!=1
                    </otherwise>
                </choose>
            </where> ;


            DELETE ocrel FROM tre_order_citation_relationship ocrel
            LEFT JOIN tre_pp_test_line_relationship rel
            ON rel.GeneralOrderInstanceID = ocrel.OrderId AND rel.PpBaseId = ocrel.PpBaseId AND rel.PpBaseId != 0
                where ocrel.OrderId =  #{orderId}
                AND rel.Id IS NULL ;

        <!--DELETE pp FROM tb_pp_instance pp
        LEFT JOIN tre_pp_test_line_relationship rel
        ON rel.GeneralOrderInstanceID = pp.GeneralOrderInstanceID AND rel.PPInstanceID = pp.ID
        WHERE pp.GeneralOrderInstanceID = #{orderId}
        AND rel.Id IS NULL;-->

        DELETE job FROM tb_job job
        LEFT JOIN tre_job_test_line_relationship rel ON rel.JobID = job.ID
        WHERE job.GeneralOrderInstanceID = #{orderId}
        AND rel.Id IS NULL;

        DELETE sc FROM tb_sub_contract sc
        LEFT JOIN tb_sub_contract_test_line_mapping rel ON rel.SubContractID = sc.ID
        inner join tb_general_order_instance goi on goi.OrderNo = sc.OrderNo
        WHERE goi.id = #{orderId}
        AND rel.Id IS NULL;

        delete rmrel from tb_report r
        inner join tre_report_matrix_relationship rmrel on rmrel.ReportId = r.ID
        inner join tb_test_matrix m on m.ID = rmrel.TestMatrixID
        <where>
            <choose>
                <when test="testLineInstanceIds != null and testLineInstanceIds.size() > 0">
                    AND m.TestLineInstanceID IN
                    <foreach item="testLineInstanceId" collection="testLineInstanceIds" open="(" separator="," close=")">
                        #{testLineInstanceId}
                    </foreach>
                </when>
                <otherwise>
                    AND 1!=1
                </otherwise>
            </choose>
        </where>;

    </delete>

    <delete id="batchDeleteAndForeign" parameterType="com.sgs.otsnotes.facade.model.info.testline.DelPPTestLineRelInfo">

            DELETE FROM tb_sub_contract_lab WHERE TestLineInstanceID  in
        <foreach item="testLineInstanceId" collection="testLineInstanceIds" open="(" separator="," close=")">
            #{testLineInstanceId}
        </foreach>;

            DELETE ppSampleRel FROM tre_pp_sample_relationship  ppSampleRel
            JOIN tre_pp_test_line_relationship ppTestLineRel ON ppSampleRel.PPTLRelID=ppTestLineRel.ID
            JOIN tb_test_line_instance t_l ON t_l.ID=ppTestLineRel.TestLineInstanceID
            WHERE  t_l.ID  in
        <foreach item="testLineInstanceId" collection="testLineInstanceIds" open="(" separator="," close=")">
            #{testLineInstanceId}
        </foreach>;

            DELETE FROM tre_pp_test_line_relationship WHERE tre_pp_test_line_relationship.TestLineInstanceID  in
        <foreach item="testLineInstanceId" collection="testLineInstanceIds" open="(" separator="," close=")">
            #{testLineInstanceId}
        </foreach>;

            DELETE FROM tre_job_test_line_relationship WHERE tre_job_test_line_relationship.TestLineInstanceID  in
        <foreach item="testLineInstanceId" collection="testLineInstanceIds" open="(" separator="," close=")">
            #{testLineInstanceId}
        </foreach>;

            DELETE FROM tb_analyte_instance WHERE tb_analyte_instance.TestLineInstanceID  in
        <foreach item="testLineInstanceId" collection="testLineInstanceIds" open="(" separator="," close=")">
            #{testLineInstanceId}
        </foreach>;

            DELETE FROM tb_test_specimen WHERE tb_test_specimen.TestLineInstanceID  in
        <foreach item="testLineInstanceId" collection="testLineInstanceIds" open="(" separator="," close=")">
            #{testLineInstanceId}
        </foreach>;

            DELETE FROM tb_test_line_instance WHERE ID in
            <foreach item="testLineInstanceId" collection="testLineInstanceIds" open="(" separator="," close=")">
                #{testLineInstanceId}
            </foreach>;

            DELETE from tb_testline_pretreatment_relationship where PretreatmentTestLineInstanceId in
            <foreach item="testLineInstanceId" collection="testLineInstanceIds" open="(" separator="," close=")">
                #{testLineInstanceId}
            </foreach>;

        DELETE ocrel FROM tre_order_citation_relationship ocrel
        LEFT JOIN tre_pp_test_line_relationship rel
        ON rel.GeneralOrderInstanceID = ocrel.OrderId AND rel.PpBaseId = ocrel.PpBaseId AND rel.PpBaseId != 0
        WHERE ocrel.OrderId =  #{orderId}
        AND rel.Id IS NULL;

        DELETE job FROM tb_job job
        LEFT JOIN tre_job_test_line_relationship rel ON rel.JobID = job.ID
        WHERE job.GeneralOrderInstanceID = #{orderId}
        AND rel.Id IS NULL;
    </delete>

    <update id="updateModifiedById" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.TestLineInstancePO">
        update tb_test_line_instance
        set modified=#{modified},
            ModifiedDate = #{modifiedDate},
            ModifiedBy = #{modifiedBy}
        where id=#{ID}
    </update>

    <select id="getTestLineByOrderIdNew"
            resultType="com.sgs.otsnotes.dbstorages.mybatis.model.TestLineInstancePO">
        SELECT
            tl.ID,
            tl.GeneralOrderInstanceID,
            baseTl.TestLineVersionId,
            -- TestLineVersionID,
            -- TestLineID,
            baseTl.TestLineId,
            -- ??
            -- CitationTypeID,
            tcrel.CitationType AS CitationTypeID,
            -- EvaluationAlias,
            tcrel.EvaluationAlias,
            -- StandardID,
            tcrel.CitationId AS StandardID,
            -- StandardVersionID,
            tcrel.CitationVersionId AS StandardVersionID,
            -- StandardName,
            tcrel.CitationName AS StandardName,
            -- StandardSectionID,
            tcrel.CitationSectionId AS StandardSectionID,
            -- StandardSectionName,
            tcrel.CitationSectionName AS StandardSectionName,
            tl.TestLineStatus,
            tl.ConditionStatus,
            tl.TestLineSeq,
            tl.TestLineType,
            tl.OrdertestLineRemark,
            tl.ActiveIndicator,
            tl.modified,
            tl.FileID,
            tl.DataEntryCloudID,
            tl.CreatedDate,
            tl.CreatedBy,
            tl.ModifiedDate,
            tl.ModifiedBy,
            tl.ValidateDate,
            tl.ValidateBy,
            tl.ReportSeq,
            tl.CalculateConclusionFlag,
            tl.ProductLineAbbr,
            tl.testLineBaseId,
            tl.citationBaseId,
            tl.labSectionBaseId,
            tl.citationId,
            tl.testItemNo
        FROM
            tb_test_line_instance tl
                LEFT JOIN tb_trims_testline_baseinfo baseTl ON tl.TestLineBaseId = baseTl.id
                LEFT JOIN tb_trims_labsection_baseinfo baseLab ON tl.LabSectionBaseId = baseLab.Id
                LEFT JOIN tb_trims_artifact_citation_relationship tcrel ON tl.CitationBaseId = tcrel.Id
        WHERE GeneralOrderInstanceID = #{orderId}
    </select>

    <select id="getBaseIdByOrderId" parameterType="java.lang.String"
            resultMap="BaseResultMap">
        SELECT
            tl.ID,
            tl.GeneralOrderInstanceID,
-- 		baseTl.TestLineVersionId,
-- 		baseTl.TestLineId,
-- 		tcrel.EvaluationAlias AS TestLineEvaluation,
-- 		tcrel.CitationType AS CitationTypeID,
-- 		tcrel.EvaluationAlias,
-- 		tcrel.CitationId AS CitationId,
-- 		tcrel.CitationVersionId AS CitationVersionId,
-- 		tcrel.CitationName AS CitationName,
-- 		tcrel.CitationSectionId AS CitationSectionId,
-- 		tcrel.CitationSectionName AS CitationSectionName,
            tl.TestLineStatus,
            tl.ConditionStatus,
            tl.TestLineSeq,
            tl.TestLineType,
            tl.OrdertestLineRemark,
            tl.ActiveIndicator,
            tl.modified,
            tl.FileID,
            tl.DataEntryCloudID,
            tl.CreatedDate,
            tl.CreatedBy,
            tl.ModifiedDate,
            tl.ModifiedBy,
            tl.ValidateDate,
            tl.ValidateBy,
            tl.ReportSeq,
            tl.CalculateConclusionFlag,
            tl.testLineBaseId,
            tl.citationBaseId,
            tl.labSectionBaseId,
            tl.citationId,
            tl.SampleSegegrationWIID,
            tl.SampleSegegrationWIText,
            tl.LabTeamCode,
            tl.PendingFlag,
            tl.Engineer,
            tl.TestStartDate,
            tl.TestEndDate,
            tl.OrderSeq,
            tl.StyleVersionId,
            tl.CustomerTestLineName,
            tl.CustomerTestLineNameCN,
            tl.ClientStandard,
            tl.ExternalTestlineInstanceId,
            tl.CitationName,
            tl.DocumentReviewFlag
        FROM
            tb_test_line_instance tl
        WHERE tl.GeneralOrderInstanceID = #{orderId}

    </select>

    <select id="getTestLineList"
            parameterType="com.sgs.otsnotes.facade.model.rsp.testLine.TestLineListReq"
            resultType="com.sgs.otsnotes.facade.model.req.testLine.TestLineListRsp">
        SELECT tl.ID                     testLineInstanceID,
        tl.GeneralOrderInstanceID orderId,
        tl.TestItemNo as testItemNo,
        tttb.TestLineId     as  testLineId,
        orderinstance.OrderNo     as  orderNo,
        ttacr.EvaluationAlias as  testItem,
        ttacr.CitationName    AS  testStandard,
        tl.TestLineStatus     as  testLineStatus,
        job.JobNo             as  jobNo,
        job.LabSectionId      as  labSectionId,
        tl.TestStartDate      as  testStartDate,
        tl.Engineer           as  engineer,
        tl.TestEndDate        as  testEndDate,
        tl.TestDueDate        as  testDueDate,
        ppb.PpNo as ppNo,
        ppb.InternalDocumentRefNo as internalRefNo,
        tttl.EvaluationName as  evaluationNameCn,
        tl.LabTeamCode as labTeamCode,
        cil.CitationName as citationName
        FROM tb_job  job
        inner join tre_job_test_line_relationship jobship on job.ID = jobship.JobID
        left JOIN tb_trims_artifact_citation_relationship acr ON tli.CitationBaseId = acr.id
        left join tb_trims_artifact_citation_language acrl on acrl.CitationBaseId = tli.CitationBaseId
        LEFT JOIN tre_trims_pp_artifact_relationship par ON par.Id = ptlr.PpArtifactRelId
        LEFT JOIN tre_trims_pp_artifact_language parl ON parl.PpArtifactRelId = par.id
        WHERE job.JobNo is not null and tl.TestLineStatus != 706
        <if test="orderNo != null and  orderNo != '' ">
            and orderinstance.orderNo = #{orderNo}
        </if>
        <if test="testItem != null and  testItem != '' ">
            and (tttl.EvaluationName like CONCAT('%',trim(#{testItem}),'%') or ttacr.EvaluationAlias like CONCAT('%',trim(#{testItem}),'%'))
        </if>
        <if test="testStandard != null and  testStandard != '' ">
            and (ttacr.CitationName like CONCAT('%',trim(#{testStandard}),'%') or cil.CitationName like CONCAT('%',trim(#{testStandard}),'%'))
        </if>
        <if test="testItemNo != null and  testItemNo != '' ">
            and tl.TestItemNo = #{testItemNo}
        </if>
        <if test="testItemArray != null and testItemArray.size() > 0" >
            and tl.TestItemNo in
            <foreach collection="testItemArray" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        </if>
        <if test="labCode!=null and labCode!=''">
            and tl.ProductLineAbbr=#{labCode}
        </if>
        <if test="labTeamCode != null and labTeamCode != ''" >
            and  tl.LabTeamCode like CONCAT('%',trim(#{labTeamCode}),'%')
        </if>
        <if test="testStartDateFrom != null and testStartDateFrom != ''" >
            and tl.TestStartDate between  #{testStartDateFrom} and #{testStartDateTo}
        </if>
        <if test="createdDateFrom != null and createdDateFrom != ''" >
            and tl.CreatedDate between  #{createdDateFrom} and #{createdDateTo}
        </if>
        <if test="testEndDateFrom != null and testEndDateFrom != ''" >
            and tl.TestEndDate between  #{testEndDateFrom} and #{testEndDateTo}
        </if>
        <if test="testDueDateFrom != null and testDueDateFrom != ''" >
            and tl.TestDueDate between  #{testDueDateFrom} and #{testDueDateTo}
        </if>
        <if test="jobNo != null and jobNo != ''" >
            and job.JobNo = #{jobNo}
        </if>
        <if test="labSectionId != null and labSectionId != ''" >
            and job.LabSectionId = #{labSectionId}
        </if>
        <if test="testLineStatus != null and testLineStatus != ''" >
            and tl.TestLineStatus = #{testLineStatus}
        </if>
        <if test="engineer != null and engineer != ''" >
            and tl.Engineer  like CONCAT('%',trim(#{engineer}),'%')
        </if>
        <if test="subContractNo != null and subContractNo != ''" >
            and sb.SubContractNo = #{subContractNo}
        </if>
        <!-- 根据filter的值动态生成SQL -->
        <!-- filter=1 表示 Lab in date有值-->
        <if test="filterOption == 1">
            and job.LabInDate IS TRUE
        </if>
        <!-- filter=2 表示 lab out date有值-->
        <if test="filterOption == 2">
            and job.LabOutDate IS TRUE
        </if>
        <!-- filter=3 表示 today - TL due date) <1天 & 没有lab out date或lab in date值）)-->
        <if test="filterOption == 3">
            and (job.LabInDate IS NULL or job.LabOutDate IS NULL ) and (TIMESTAMPDIFF(MINUTE,tl.TestDueDate,NOW()) &lt; 24*60)
        </if>
        order by orderinstance.OrderNo desc,tl.TestItemNo asc
    </select>

    <select id="getTestLineByReportId"
            resultType="com.sgs.otsnotes.dbstorages.mybatis.model.TestLineInstancePO"
            parameterType="java.util.List">
        SELECT DISTINCT
            tl.ID,
            tl.ActiveIndicator,
            tl.CreatedBy,
            tl.CreatedDate,
            tl.ModifiedBy,
            tl.ModifiedDate,
            tl.GeneralOrderInstanceID,
            tl.TestLineVersionID,
            tl.TestLineID,
--         tl.CitationTypeID,
            tl.StandardVersionID,
--         tl.StandardName,
--         tl.AutoConclusion,
            tl.TestLineStatus,
            tl.ConditionStatus,
            tl.TestLineSeq,
            tl.TestLineType,
            tl.OrdertestLineRemark,
            tl.FileID
        FROM
            tb_test_line_instance tl
                JOIN tb_test_matrix tm ON tl.ID = tm.TestLineInstanceID
                JOIN tre_report_matrix_relationship trmr ON tm.ID = trmr.TestMatrixID
                JOIN tb_report re ON re.id = trmr.ReportID
        where re.id = #{reportId}
          AND <![CDATA[ (tl.TestLineType & 1) != 1]]>
		  and tl.TestLineStatus != 706 and tl.TestLineStatus != 707
    </select>

    <select id="getSampleListByTestLine"
            resultType="com.sgs.otsnotes.facade.model.dto.ConclusionSampleDTO"
            parameterType="map">
        select SampleNo,tts.id
        from tb_test_matrix matrix left join tb_test_sample tts on matrix.TestSampleID = tts.ID
        where matrix.TestLineInstanceID = #{testLineId}
    </select>

    <select id="getConclusionSampleList"
            resultType="com.sgs.otsnotes.facade.model.rsp.ConclusionSampleListRsp"
            parameterType="String">
        select DISTINCT
            ppsrel.ID,
            con.TestLineID conTestLineID,
            tl.TestLineID,
            tm.ID mid,
            tl.calculateConclusionFlag,
            tl.ID TestLineInstanceId,
            con.GeneralOrderInstanceID,
            conlist.Description,
            sample.SampleNo,
            sample.SampleType,
            sample.id SampleID,
            con.PpSampleRelID,
            con.ConclusionID concludionId,
            con.id concludionInsId,
            con.ConclusionRemark conclusionRemark,
            con.ConclusionSettingID conclusionSettingID,
            cf.ConclusionDescription,
            ttcr.EvaluationAlias,
            lab.LabSectionName,
            ifnull(lab.LabSectionSeq,999999999) LabSectionSeq,
            sb.SubContractLabName,
            tm.MatrixGroupId
        from tb_test_line_instance tl
                 INNER JOIN tre_pp_test_line_relationship pptlrel ON pptlrel.TestLineInstanceID = tl.ID
                 LEFT JOIN tre_pp_sample_relationship ppsrel ON ppsrel.PPTLRelID = pptlrel.Id
                 LEFT JOIN tb_trims_labsection_baseinfo lab ON lab.Id = tl.labSectionBaseId
                 LEFT JOIN tb_trims_artifact_citation_relationship ttcr ON  tl.CitationBaseId = ttcr.Id
                 LEFT JOIN tb_sub_contract_test_line_mapping m ON tl.id = m.TestLineInstanceID
                 LEFT JOIN tb_sub_contract sb ON sb.id = m.SubContractID
                 LEFT join tb_test_matrix tm on tl.ID = tm.TestLineInstanceID
                 LEFT join tre_report_matrix_relationship trmr on tm.ID = trmr.TestMatrixID
                 LEFT join tb_report re on re.id = trmr.ReportID
                 LEFT join tb_test_sample sample on sample.id = tm.TestSampleID
                 LEFT join tb_conclusion con on con.ObjectID = tm.ID and con.ReportID = re.id and con.TestLineID = tl.TestLineID
                 LEFT JOIN tb_customer_conclusion_reference cf on cf.id =con.ConclusionSettingID
                 left join tb_conclusion_list conlist on conlist.ID = con.ConclusionID
        where re.id = #{reportId}
          AND <![CDATA[ (tl.TestLineType & 1) != 1]]>
          and tl.TestLineStatus != 707
          and tl.TestLineStatus != 706
          and tm.ActiveIndicator = 1
        order by ifnull(lab.LabSectionSeq,999999999),lab.LabSectionName,sb.SubContractLabName,ttcr.EvaluationAlias,con.TestLineID
    </select>

    <select id="getConclusionSampleListBySubcontract"
            resultType="com.sgs.otsnotes.facade.model.rsp.ConclusionSampleListRsp"
            parameterType="java.util.List">
        SELECT DISTINCT
            ppsrel.ID,
            con.TestLineID conTestLineID,
            tl.TestLineID,
            tm.ID mid,
            tl.calculateConclusionFlag,
            tl.ID TestLineInstanceId,
            con.GeneralOrderInstanceID,
            conlist.Description,
            sample.SampleNo,
            sample.SampleType,
            sample.id SampleID,
            con.PpSampleRelID,
            con.ConclusionID concludionId,
            con.id concludionInsId,
            con.ConclusionRemark conclusionRemark,
            con.ConclusionSettingID conclusionSettingID,
            cf.ConclusionDescription,
            ttcr.EvaluationAlias,
            lab.LabSectionName,
            ifnull(
                    lab.LabSectionSeq,
                    999999999
                ) LabSectionSeq,
            sb.SubContractLabName,
            tm.MatrixGroupId
        FROM
            tb_test_line_instance tl
                INNER JOIN tre_pp_test_line_relationship pptlrel ON pptlrel.TestLineInstanceID = tl.ID
                LEFT JOIN tre_pp_sample_relationship ppsrel ON ppsrel.PPTLRelID = pptlrel.Id
                LEFT JOIN tb_trims_labsection_baseinfo lab ON lab.Id = tl.labSectionBaseId
                LEFT JOIN tb_trims_artifact_citation_relationship ttcr ON tl.CitationBaseId = ttcr.Id
                LEFT JOIN tb_sub_contract_test_line_mapping m ON tl.id = m.TestLineInstanceID
                LEFT JOIN tb_sub_contract sb ON sb.id = m.SubContractID
                LEFT JOIN tb_test_matrix tm ON tl.ID = tm.TestLineInstanceID
                LEFT JOIN tb_test_sample sample ON sample.id = tm.TestSampleID
                LEFT JOIN tb_conclusion con ON con.ObjectID = tm.ID
                AND con.TestLineID = tl.TestLineID
                LEFT JOIN tb_customer_conclusion_reference cf ON cf.id = con.ConclusionSettingID
                LEFT JOIN tb_conclusion_list conlist ON conlist.ID = con.ConclusionID
        WHERE
                tl.id  IN (
                SELECT
                    tl.ID
                FROM
                    tb_sub_contract s
                        INNER JOIN tb_sub_contract_test_line_mapping sm ON s.ID = sm.SubContractID
                        LEFT JOIN tb_test_line_instance tl ON tl.ID = sm.TestLineInstanceID
                WHERE
                    s.`Status` != 3
          AND s.SubContractNo IN
                <foreach collection="subContractNos" open="(" close=")" separator="," item="item">
                    #{item}
                </foreach>
            )
          AND <![CDATA[ (tl.TestLineType & 1) != 1]]>
          and tl.TestLineStatus != 707
          and tl.TestLineStatus != 706
          and tm.ActiveIndicator = 1
        ORDER BY
            ifnull(
            lab.LabSectionSeq,
            999999999
            ),
            lab.LabSectionName,
            sb.SubContractLabName,
            ttcr.EvaluationAlias,
            con.TestLineID
    </select>


    <select id="getConclusionSampleList2"
            resultType="com.sgs.otsnotes.facade.model.rsp.ConclusionSampleListRsp"
            parameterType="String">
        select
            IFNULL(pp.PpNo,0) PpNo,
            IFNULL(pp.Id ,0) PpId,
            pp.ppName,
            a.* from (
                         select DISTINCT
                             con.TestLineID conTestLineID,
                             tl.TestLineID,
                             tl.ID TestLineInstanceID,
                             con.GeneralOrderInstanceID,
                             sample.SampleNo,
                             sample.id SampleID,
                             con.PpSampleRelID,
                             cf.ConclusionDescription,
                             ttcr.EvaluationAlias,
                             lab.LabSectionName,
                             lab.LabSectionSeq

                         from tb_test_line_instance tl
                                  LEFT JOIN tb_trims_labsection_baseinfo lab ON lab.Id = tl.labSectionBaseId
                                  LEFT JOIN tb_trims_artifact_citation_relationship ttcr ON  tl.CitationBaseId = ttcr.Id
                                  LEFT JOIN tb_sub_contract_test_line_mapping m ON tl.id = m.TestLineInstanceID
                                  LEFT JOIN tb_sub_contract sb ON sb.id = m.SubContractID
                                  LEFT join tb_test_matrix tm on tl.ID = tm.TestLineInstanceID
                                  LEFT join tre_report_matrix_relationship trmr on tm.ID = trmr.TestMatrixID
                                  LEFT join tb_report re on re.id = trmr.ReportID
                                  LEFT join tb_test_sample sample on sample.id = tm.TestSampleID and sample.id = tm.TestSampleID
                                  LEFT join tb_conclusion con on con.ObjectID = tm.ID  and con.TestLineID = tl.TestLineID

                                  LEFT JOIN tb_customer_conclusion_reference cf on cf.id =con.ConclusionSettingID

                         where re.id = #{reportId}
                           AND <![CDATA[ (tl.TestLineType & 1) != 1]]>
                  and tl.TestLineStatus != 707
                         order by lab.LabSectionSeq,lab.LabSectionName,sb.SubContractLabName,ttcr.EvaluationAlias,con.TestLineID
                     )a
                         left join tre_pp_test_line_relationship ship on ship.TestLineInstanceID = a.TestLineInstanceID and a.GeneralOrderInstanceID = ship.GeneralOrderInstanceID
                         left join tb_trims_pp_baseinfo pp on pp.Id = ship.PpBaseId  where a.conTestLineID is not null
    </select>

    <select id="getTestLineInstanceIds" resultType="java.lang.String" parameterType="java.lang.String">
        SELECT Id FROM tb_test_line_instance
        WHERE GeneralOrderInstanceID = #{orderId}
    </select>

    <!-- batchUpdate -->
    <update id="batchUpdateConclusionFlag" parameterType="com.sgs.otsnotes.facade.model.info.conclusion.CalculateConclusionFlagInfo">
        <foreach collection="testLineInstanceIds" item="testLineInstanceId" separator=";">
            UPDATE tb_test_line_instance
            SET CalculateConclusionFlag = #{testLineInstanceId.conclusionFlag}
            WHERE Id = #{testLineInstanceId.testLineInstanceId}
            AND TestLineStatus != 707
        </foreach>
    </update>

    <select id="getConfirmMatrixGemoTestLine" parameterType="com.sgs.otsnotes.facade.model.req.testLine.GemoReq" resultType="com.sgs.otsnotes.facade.model.rsp.testLine.GemoRsp">
        SELECT DISTINCT ttb.testLineVersionID as testLineVersionID,tli.TestLineID as testLineID,
        ttb.EvaluationName as evaluationAlias,goi.OrderNo as orderNo,
        goi.id as orderId,tli.id as testLineInstanceId
        FROM tb_test_line_instance tli
        inner join tb_trims_testline_baseinfo ttb on tli.TestLineBaseId=ttb.ID
        inner join tb_general_order_instance goi on tli.GeneralOrderInstanceID=goi.ID
        WHERE  goi.CustomerCode=#{customerCode} and tli.ActiveIndicator=1
        AND goi.ActiveIndicator=1
        <if test="orderNo != null and orderNo != '' ">
            and	goi.OrderNo = #{orderNo}
        </if>
        and DATE_FORMAT(DATE_ADD(goi.ConfirmMatrixDate, INTERVAL 1 DAY)   ,'%Y%m%d')=#{time}
    </select>

    <select id="getTestConditionName" resultType="com.sgs.otsnotes.facade.model.dto.TlConditionDTO" parameterType="map">
        SELECT DISTINCT ttcb.TestConditionName, ttcb.TestConditionId, ttcb.TestConditionDesc, cond_type_info.TestConditionTypeName
        FROM tb_trims_pp_testline_condition_relationship ttptcr
                 JOIN tb_trims_testline_condition_relationship tttcr
                      ON tttcr.TestLineVersionId = ttptcr.TestLineVersionId
                          AND tttcr.ConditionTypeBaseId = ttptcr.ConditionTypeBaseId
                          AND tttcr.ConditionBaseId = ttptcr.ConditionBaseId AND tttcr.RelStatus = 1
                 JOIN tb_trims_condition_baseinfo ttcb on tttcr.ConditionBaseId = ttcb.id
                 JOIN tb_trims_conditiontype_baseinfo cond_type_info ON tttcr.ConditionTypeBaseId = cond_type_info.ID
        WHERE ttptcr.PpVersionId = #{ppVersionId}
          AND tttcr.TestLineVersionId = #{testLineVersionId}
    </select>

    <select id="getPPTestLineRelList" resultType="com.sgs.otsnotes.facade.model.info.MatrixTestLineRelInfo">
        SELECT DISTINCT
            pptl.Id AS PpTestLineRelId,
            pptl.TestLineInstanceId,
            IFNULL(tm.MatrixGroupId, 0) AS MatrixGroupId
        FROM
            tb_general_order_instance o
                INNER JOIN tre_pp_test_line_relationship pptl ON pptl.GeneralOrderInstanceID = o.Id
                INNER JOIN tb_test_line_instance tl ON tl.GeneralOrderInstanceID = pptl.GeneralOrderInstanceID
                AND tl.Id = pptl.TestLineInstanceID
                LEFT JOIN tb_test_matrix tm ON tm.TestLineInstanceID = tl.Id
        WHERE
            o.OrderNo = #{orderNo}
    </select>
    <select id="getTestLineByOrderIdAndStatus" resultType="com.sgs.otsnotes.dbstorages.mybatis.model.TestLineInstancePO" parameterType="com.sgs.otsnotes.facade.model.req.testLine.TestLineStatusReq">
        SELECT
            tl.*
        FROM tb_test_line_instance tl
                 INNER JOIN tb_general_order_instance o ON o.Id = tl.GeneralOrderInstanceID AND o.OrderNo = #{orderNo}
        WHERE tl.TestLineStatus = #{testLineStatus}
          AND tl.ActiveIndicator = 1
    </select>

    <update id="cancelTestLineByGeneralOrderId"
            parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.TestLineInstancePO">
        UPDATE
            tb_test_line_instance
        SET
            ActiveIndicator=#{activeIndicator},
            TestLineStatus=#{testLineStatus},
            ModifiedDate=#{modifiedDate},
            ModifiedBy=#{modifiedBy}
        WHERE
            GeneralOrderInstanceID=#{generalOrderInstanceID}
    </update>

    <update id="batchUpdateTestLineStatusAndTestLineType">
        <foreach item="testLine" collection="list" separator=";">
            UPDATE tb_test_line_instance
            SET TestLineStatus = #{testLine.testLineStatus},
            TestLineType = #{testLine.testLineType},
            ModifiedDate = #{testLine.modifiedDate},
            ModifiedBy = #{testLine.modifiedBy}
            WHERE Id = #{testLine.ID}
        </foreach>
    </update>

    <update id="updateTestLineInstanceDetail">
        UPDATE tb_test_line_instance
        SET OrdertestLineRemark = #{ordertestLineRemark},SampleSegegrationWIID=#{sampleSegegrationWIID},SampleSegegrationWIText=#{sampleSegegrationWIText}
          ,LabSectionBaseId = #{labSectionBaseId},LabTeamCode = #{labTeamCode},Engineer = #{engineer}, ModifiedBy = #{modifiedBy}, ModifiedDate = #{modifiedDate},ClientStandard = #{clientStandard},CustomerTestLineName=#{customerTestLineName},CustomerTestLineNameCN=#{customerTestLineNameCN}
        WHERE id = #{ID}
    </update>

    <update id="updateTestLineReportSeq">
        UPDATE tb_test_line_instance SET ReportSeq = #{reportSeq},ModifiedBy = #{modifiedBy}, ModifiedDate = #{modifiedDate}
        WHERE ID = #{testLineInstanceId}
    </update>

    <resultMap id="NewTestLineResultMap" type="com.sgs.otsnotes.facade.model.info.testline.NewTestLineInfo" >
        <id column="TestLineInstanceId" property="testLineInstanceId" jdbcType="BIGINT" />
        <result column="TestLineBaseId" property="testLineBaseId" jdbcType="BIGINT" />
        <result column="TestLineId" property="testLineId" jdbcType="INTEGER" />
        <result column="TestLineVersionId" property="testLineVersionId" jdbcType="INTEGER" />
        <result column="ArtifactType" property="artifactType" jdbcType="INTEGER" />

        <result column="OldTestLineBaseId" property="oldTestLineBaseId" jdbcType="BIGINT" />
        <result column="OldTestLineVersionId" property="oldTestLineVersionId" jdbcType="INTEGER" />
        <result column="OldCitationBaseId" property="oldCitationBaseId" jdbcType="BIGINT" />
        <result column="CitationId" property="citationId" jdbcType="INTEGER" />
        <result column="OldCitationVersionId" property="oldCitationVersionId" jdbcType="INTEGER" />
        <result column="OldCitationSectionId" property="oldCitationSectionId" jdbcType="INTEGER" />
        <result column="CitationType" property="citationType" jdbcType="INTEGER" />
        <result column="TestLineStatus" property="testLineStatus" jdbcType="INTEGER" />
        <result column="ConditionStatus" property="conditionStatus" jdbcType="INTEGER" />
        <result column="TestLineType" property="testLineType" jdbcType="INTEGER" />
        <result column="OrderTestLineType" property="orderTestLineType" jdbcType="INTEGER" />
        <result column="OldLabSectionBaseId" property="oldLabSectionBaseId" jdbcType="BIGINT" />
        <result column="TestLineRemark" property="testLineRemark" jdbcType="VARCHAR" />
        <result column="ProductLineAbbr" property="productLineAbbr" jdbcType="VARCHAR" />
        <result column="SampleSegegrationWIID" property="sampleSegegrationWIID" jdbcType="BIGINT" />
        <result column="SampleSegegrationWIText" property="sampleSegegrationWIText" jdbcType="VARCHAR" />
        <result column="LabTeamCode" property="labTeamCode" jdbcType="VARCHAR" />
        <result column="TestItemNo" property="testItemNo" jdbcType="VARCHAR" />
        <result column="ClientStandard" property="clientStandard" jdbcType="VARCHAR" />
        <result column="CustomerTestLineName" property="customerTestLineName" jdbcType="VARCHAR" />
        <result column="CustomerTestLineNameCN" property="customerTestLineNameCN" jdbcType="VARCHAR" />
        <result column="CitationName" property="citationName" jdbcType="VARCHAR" />
        <result column="Engineer" property="engineer" jdbcType="VARCHAR" />
        <result column="DocumentReviewFlag" property="documentReviewFlag" jdbcType="VARCHAR" />

        <result column="LangBaseId" property="langBaseId" jdbcType="BIGINT" />
        <result column="LanguageId" property="languageId" jdbcType="INTEGER" />

        <!--<association property="labSectionBaseId"
                     javaType="java.lang.Long"
                     column="{testLineVersionId=TestLineVersionId, labId=LabId}"
                     select="com.sgs.otsnotes.dbstorages.mybatis.extmapper.LabSectionMapper.getTestLineLabSectionBaseId">
        </association>-->

        <collection  property="citations"
                     ofType="com.sgs.otsnotes.facade.model.info.citation.NewArtifactCitationRelInfo"
                     javaType="java.util.List"
                     column="{testLineVersionId=TestLineVersionId, artifactType=ArtifactType, citationId=CitationId, citationVersionId=OldCitationVersionId, citationType=CitationType}"
                     select="com.sgs.otsnotes.dbstorages.mybatis.extmapper.ArtifactCitationRelMapper.getArtifactCitationRelInfoList" />

        <!--<collection property="ppTestLines"
                    ofType="com.sgs.otsnotes.facade.model.info.testline.NewPpTestLineInfo"
                    javaType="java.util.List"
                    column="TestLineInstanceId"
                    select="com.sgs.otsnotes.dbstorages.mybatis.extmapper.PPTestLineRelMapper.getNewPpTestLineInfoList" />-->
    </resultMap>

    <resultMap id="TestLineSimplifyResultMap" type="com.sgs.otsnotes.facade.model.info.testline.TestLineSimplifyInfo" >
        <id column="Id" property="testLineInstanceId" jdbcType="VARCHAR" />
        <result column="TestLineId" property="testLineId" jdbcType="INTEGER" />
        <result column="TestLineVersionId" property="testLineVersionId" jdbcType="INTEGER" />

        <association property="ppTestLine"
                     javaType="java.lang.String"
                     column="{testLineInstanceId=Id}"
                     select="getPpTestLineLangInfo">
        </association>

        <association property="testLine"
                     javaType="java.lang.Long"
                     column="CitationBaseId"
                     select="getTestLineLangInfo">
        </association>
    </resultMap>

    <select id="getNewTestLineInfoList" resultMap="NewTestLineResultMap">
        SELECT DISTINCT
            tl.Id AS TestLineInstanceId
                      ,tlb.Id AS TestLineBaseId
                      ,tlb.TestLineId
                      ,tlb.TestLineVersionId
                      ,tlb.ArtifactType

                      ,tl.TestLineBaseId AS OldTestLineBaseId
                      ,tl.TestLineVersionId AS OldTestLineVersionId
                      ,tl.CitationBaseId AS OldCitationBaseId
                      ,tl.CitationId
                      ,acrel.CitationVersionId AS OldCitationVersionId
                      ,acrel.CitationSectionId AS OldCitationSectionId
                      ,IFNULL(acrel.CitationType, 0) AS CitationType
                      ,tl.LabSectionBaseId AS OldLabSectionBaseId
                      ,tl.TestLineStatus
                      ,tl.ConditionStatus
                      ,tlb.TestLineType
                      ,tl.TestItemNo
                      ,tl.SampleSegegrationWIID
                      ,tl.SampleSegegrationWIText
                      ,tl.LabTeamCode
                      ,tl.orderSeq
                      ,tl.OrderTestLineRemark AS TestLineRemark
                      ,tl.ClientStandard AS ClientStandard
                      ,tl.CustomerTestLineName
                      ,tl.CustomerTestLineNameCN
                      ,tlb.ProductLineAbbr
                      ,lang.LangId AS LangBaseId
                      ,lang.LanguageId
                      ,tl.CitationName
                      ,tl.Engineer AS engineer
                      ,tl.DocumentReviewFlag
                      ,tl.TestLineType AS OrderTestLineType
        FROM tb_test_line_instance tl
                 INNER JOIN tb_trims_testline_baseinfo tlb ON tlb.TestLineId = tl.TestLineId AND tlb.TestLineStatus = 1
                 LEFT JOIN tb_trims_testline_language lang ON lang.TestLineVersionId = tlb.TestLineVersionId AND lang.LangStatus = 1
                 LEFT JOIN tb_trims_artifact_citation_relationship acrel ON acrel.Id = tl.CitationBaseId
        WHERE tl.GeneralOrderInstanceID = #{orderId}
        order By tl.TestItemNo
    </select>

    <select id="getTestLineSimplifyInfoList" resultMap="TestLineSimplifyResultMap">
        SELECT tl.Id
             ,tl.TestLineId
             ,tl.TestLineVersionId
             ,tl.CitationBaseId
        FROM tb_general_order_instance o
                 INNER JOIN tb_test_line_instance tl ON tl.GeneralOrderInstanceID = o.Id
        WHERE o.OrderNo = #{orderNo}
    </select>

    <select id="getTestLineNameZH" parameterType="java.lang.String" resultType="com.sgs.otsnotes.facade.model.rsp.GetTestLinePPNameZHRsp">
        SELECT DISTINCT t1.TestLineID, t2.EvaluationName
        FROM tb_test_line_instance t1
                 LEFT JOIN tb_trims_testline_language t2 ON t1.testLineBaseId = t2.testLineBaseId
                 LEFT JOIN tb_general_order_instance t3 ON t3.ID = t1.GeneralOrderInstanceID
        WHERE t3.OrderNo = #{orderNo}
        ORDER BY t2.LangId DESC
    </select>

    <select id="getPpTestLineLangInfo" resultType="com.sgs.otsnotes.facade.model.info.testline.TestLineLangInfo">
        SELECT rel.EvaluationAlias
             ,lang.EvaluationAlias AS EvaluationAliasCN
        FROM tre_pp_test_line_relationship pptl
                 INNER JOIN tre_trims_pp_artifact_relationship rel ON rel.Id = pptl.PpArtifactRelId
                 LEFT JOIN tre_trims_pp_artifact_language lang ON lang.PpArtifactRelId = rel.Id
        WHERE pptl.TestLineInstanceID = #{testLineInstanceId}
          AND pptl.PpArtifactRelId > 0
        ORDER BY pptl.CreatedDate ASC
            LIMIT 1;
    </select>

    <select id="getTestLineLangInfo" resultType="com.sgs.otsnotes.facade.model.info.testline.TestLineLangInfo">
        SELECT rel.EvaluationAlias
             ,lang.EvaluationAlias AS EvaluationAliasCN
        FROM tb_trims_artifact_citation_relationship rel
                 LEFT JOIN tb_trims_artifact_citation_language lang ON lang.CitationBaseId = rel.Id
        WHERE rel.Id = #{citationBaseId}
            LIMIT 1;
    </select>

    <select id="getEditTestLineInfoById"
            resultMap="editTestLineResultMap">
        select
            tl.id as testLineInstanceId,
            tl.TestLineVersionID,
            tl.testLineId,
            tl.testLineStatus,
            tl.OrdertestLineRemark,
            tl.SampleSegegrationWIID,
            tl.SampleSegegrationWIText,
            tl.LabSectionBaseId,
            tl.Engineer as engineer,
--             tw.WorkInstructionText as wiForCsText,
--             twLan.WorkInstructionText as wiForCsTextEN,
            tg.OrderLaboratoryID as labId,
            tg.orderNo,
            tg.id as orderInstanceId,
            an.AnalyteBaseId,
            an.AnalyteID,
            tl.LabTeamCode,
            pp.ppVersionId,
            ptrel.aid,
            tl.CitationVersionId as standardVersionId,
            tl.ClientStandard as clientStandard,
            tl.TestLineType as testLineType,
            tl.CustomerTestLineName as customerTestLineName,
            tl.CustomerTestLineNameCN as customerTestLineNameCN,
            tl.CitationName as editCitationName,
            tliml.CitationName as editCitationNameCN
        from tb_test_line_instance tl
                 left join tre_pp_test_line_relationship ptrel on tl.id = ptrel.TestLineInstanceID
                 left join tb_trims_pp_baseinfo pp on ptrel.PpBaseId = pp.id
                 left join tb_analyte_instance an on an.TestLineInstanceID=tl.id
                 join tb_general_order_instance tg on tl.GeneralOrderInstanceID=tg.id
                 left join tb_test_line_instance_multiplelanguage tliml on tliml.TestLineInstanceID = tl.id
        where tl.id = #{testLineInstanceId}
        ORDER BY ptrel.CreatedDate
    </select>

    <update id="updatePendingFlagByIds" parameterType="com.sgs.otsnotes.facade.model.req.testLine.UpdateTestLinePendingFlagReq">
        update tb_test_line_instance set pendingFlag = #{pendingFlag}
        where id in
        <foreach collection="testLineInstanceIds" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </update>

    <select id="getTestLinePendingFlag"
            resultType="com.sgs.otsnotes.facade.model.rsp.testLine.TestLinePendingFlagRsp">
        select id,pendingFlag from tb_test_line_instance
        where id in
        <foreach collection="list" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>

    </select>
    <select id="getTestLinePendingByPPTLRelIds"
            resultType="com.sgs.otsnotes.facade.model.rsp.testLine.TestLinePendingFlagRsp">
        select distinct a.id,a.pendingFlag from tb_test_line_instance a
        join tre_pp_test_line_relationship b on a.id = b.testLineInstanceId
        where b.id in
        <foreach collection="list" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </select>
    <select id="selectTestLineStatusByIds"
            resultType="com.sgs.otsnotes.dbstorages.mybatis.model.TestLineInstancePO">
        select id,TestLineID,TestLineStatus,pendingFlag,generalOrderInstanceID,testItemNo
        from tb_test_line_instance
        where id in
        <foreach collection="list" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>

    </select>

    <update id="updateTestLineStatusBySubContractId">
        UPDATE tb_test_line_instance AS A
            JOIN tb_sub_contract_test_line_mapping AS B ON A.ID = B.TestLineInstanceID
            SET A.TestLineStatus = #{testLineStatus}
        WHERE B.SubContractID = #{subContractId}
    </update>

    <select id="queryByParams" resultType="com.sgs.otsnotes.dbstorages.mybatis.model.TestLineInstancePO"
            parameterType="java.util.Map">
        SELECT
        <include refid="Base_Column_List" />
        FROM
        tb_test_line_instance t_l
        WHERE 1 = 1
        <if
                test="generalOrderInstanceID != null and  generalOrderInstanceID != '' ">
            and t_l.GeneralOrderInstanceID=#{generalOrderInstanceID}
        </if>
        <if test="id != null and  id != '' ">
            and t_l.ID=#{id}
        </if>
        <if test="testLineStatus != null and  testLineStatus != '' ">
            and t_l.TestLineStatus!=#{testLineStatus}
        </if>
        <if test="orderTestLineIds != null and  orderTestLineIds.size() > 0 ">
            and t_l.ID in
            <foreach collection="orderTestLineIds" item="item" open="("
                     separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <update id="batchUpdateTestlineInstance" parameterType="java.util.List">
        <foreach item="testLine" index="index" collection="testLines" open="" close="" separator=";">
            UPDATE tb_test_line_instance
            <set>
                SampleSegegrationWIID = #{testLine.sampleSegegrationWIID},
                SampleSegegrationWIText = #{testLine.sampleSegegrationWIText}
            </set>
            WHERE id = #{testLine.testLineInstanceId}
        </foreach>
    </update>

    <!-- batchUpdate -->
    <update id="batchUpdate" parameterType="java.util.List">
        <foreach collection="list" item="item" separator=";">
            UPDATE tb_test_line_instance
            SET
            ActiveIndicator=#{item.activeIndicator},ModifiedBy=#{item.modifiedBy},ModifiedDate=Now(),
            GeneralOrderInstanceID=#{item.generalOrderInstanceID},TestLineVersionID=#{item.testLineVersionID},TestLineID=#{item.testLineID},
            TestLineStatus=#{item.testLineStatus},ConditionStatus=#{item.conditionStatus},
            TestLineSeq=#{item.testLineSeq},TestLineType=#{item.testLineType},
            OrdertestLineRemark=#{item.ordertestLineRemark},FileID=#{item.fileID},
            DataEntryCloudID=#{item.dataEntryCloudID},CalculateConclusionFlag=#{item.calculateConclusionFlag},
            ProductLineAbbr=#{item.productLineAbbr},ReportSeq=#{item.reportSeq}
            WHERE ID=#{item.ID}
        </foreach>
    </update>

    <!--本地化 根据LG 查询PA时，需要用到testItemVersionId-->
    <select id="getPpSampleTestLineVersionIds" resultType="java.lang.Integer" parameterType="com.sgs.otsnotes.facade.model.info.limitgroup.OrderPpSampleInfo">
        SELECT DISTINCT
        tl.TestLineVersionId
        FROM tb_test_line_instance tl
        INNER JOIN tre_pp_test_line_relationship pptl ON pptl.TestLineInstanceID = tl.Id
        INNER JOIN tre_pp_sample_relationship pps ON pps.PPTLRelID = pptl.Id
        INNER JOIN tb_general_order_instance o ON o.Id = pptl.GeneralOrderInstanceID
        WHERE o.OrderNo = #{orderNo}
        AND pps.TestSampleId = #{sampleId}
        <if test="ppBaseId != null and ppBaseId > 0">
            AND pptl.PpBaseId = #{ppBaseId}
        </if>
        <if test="testLineVersionIds != null and testLineVersionIds.size() > 0">
            AND tl.TestLineVersionId in
            <foreach collection="testLineVersionIds" item="testLineVersionId" open="(" close=")" separator=",">
                #{testLineVersionId}
            </foreach>
        </if>
    </select>

    <select id="getTestLineForRequirment"
            resultType="com.sgs.otsnotes.facade.model.dto.TestLineRequirmentListDTO">
        select A.ID as TestLineInstanceId,
               A.TestLineID,
               B.EvaluationAlias,
               B.CitationName,
               GROUP_CONCAT(DISTINCT D.SampleNo order by D.SampleSeq) AS SampleNos
        from tb_test_line_instance  A
                 LEFT JOIN tb_trims_artifact_citation_relationship B
                           ON A.CitationBaseId=B.Id
                 LEFT JOIN tb_test_matrix C
                           ON A.ID=C.TestLineInstanceID
                 LEFT JOIN tb_test_sample D
                           ON C.TestSampleID=D.ID
        where A.GeneralOrderInstanceID=#{orderID}
          and A.TestLineStatus not in(706,707,708)
        GROUP BY A.ID,
                 A.TestLineID,
                 B.EvaluationAlias,
                 B.CitationName
        ORDER BY A.TestLineSeq
    </select>

    <select id="getTestLimits" resultType="com.sgs.otsnotes.facade.model.dto.TestLineLimitsDTO">
        SELECT DISTINCT B.Id as ppId,
                        B.PpNo as ppNo ,
                        C.TestConditionID as  testConditionId,
                        C.TestConditionName as testConditionName,
                        C.TestConditionSeq as testConditionSeq
        FROM tre_pp_test_line_relationship A
                 LEFT JOIN tb_trims_pp_baseinfo B
                           ON A.PpBaseId=B.Id
                 LEFT JOIN tb_test_condition_instance C
                           ON A.TestLineInstanceID=C.TestLineInstanceID
        WHERE A.TestLineInstanceID=#{testLineInstanceID}
        ORDER BY B.PpNo,C.TestConditionSeq
    </select>


    <resultMap id="BaseTestLineSampleResultMap" type="com.sgs.otsnotes.facade.model.dto.TestLineSampleDTO">
        <id column="TestMatrixId" property="testMatrixID" />
        <result column="SampleId" property="sampleId"/>
        <result column="SampleNo" property="sampleNo" />

        <collection property="ppBaseIds" ofType="java.lang.Long" >
            <result column="PpBaseId"/>
        </collection>
    </resultMap>

    <select id="getTestLineSample" resultMap="BaseTestLineSampleResultMap">
        SELECT DISTINCT
            tm.Id AS TestMatrixId
                      ,IFNULL(pptl.PpBaseId, 0) AS PpBaseId
                      ,ts.Id AS SampleId
                      ,ts.SampleNo AS SampleNo
        FROM tb_test_matrix tm
                 INNER JOIN tre_pp_test_line_relationship pptl ON pptl.TestLineInstanceId = tm.TestLineInstanceId
                 INNER JOIN tre_pp_sample_relationship pps ON pps.TestSampleID = tm.TestSampleId AND pps.PPTLRelID = pptl.Id
                 INNER JOIN tb_test_sample ts ON ts.Id = pps.TestSampleId AND ts.ActiveIndicator = 1 AND ts.Applicable = 0
        WHERE tm.TestLineInstanceID = #{testLineInstanceID}
          AND tm.ActiveIndicator = 1
        ORDER BY ts.SampleSeq;
    </select>

    <select id="getTestLineById" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT ID, GeneralOrderInstanceID, TestLineID,TestLineStatus, TestLineType, ProductLineAbbr
        FROM tb_test_line_instance
        where id = #{testLineInstanceId}
    </select>

    <select id="getTestLineListByOrderNo" resultMap="TestLineSimplifyMap">
        SELECT DISTINCT
        goi.Id AS OrderId,

        tli.ID AS TestLineInstanceId,
        tli.CitationVersionId,
        tli.TestLineVersionId,
        tli.TestLineId,
        tli.TestLineStatus,
        tli.ConditionStatus,
        tli.OrdertestLineRemark AS TestLineRemark,
        tli.TestLineType,

        tli.TestLineBaseId,
        tli.CitationBaseId,
        tli.LabSectionBaseId,
        <!--tl.EvaluationName,
        tl.ProductLineAbbr,-->

        <!--
        tlcRel.EvaluationAlias,
        tlcRel.CitationName,
        tlcRel.CitationSectionName,-->

        pptlrel.PpArtifactRelId,
        pptlrel.aid AS ArtifactId,

        lang.LangBaseId,
        langPpArtiFact.LangBaseId AS PpArtifactLang
        FROM tb_general_order_instance goi
        INNER JOIN tb_test_line_instance tli ON tli.GeneralOrderInstanceID = goi.Id
        INNER JOIN tre_pp_test_line_relationship pptlrel ON pptlrel.TestLineInstanceID = tli.Id
        LEFT JOIN tre_order_language_relationship lang ON lang.OrderId = tli.GeneralOrderInstanceID
        AND lang.ObjectBaseId = tli.CitationBaseId AND lang.LangType = 3
        LEFT JOIN tre_order_language_relationship langPpArtiFact ON langPpArtiFact.OrderId = tli.GeneralOrderInstanceID
        AND langPpArtiFact.ObjectBaseId = tli.CitationBaseId AND langPpArtiFact.LangType = 9
        WHERE goi.OrderNo = #{orderNo}
        ORDER BY tli.TestLineId ASC, pptlrel.CreatedDate ASC;
    </select>

    <select id="getTestLineInfoById" resultMap="TestLineSimplifyMap">
        SELECT DISTINCT
            tli.ID AS TestLineInstanceId,
            tli.CitationVersionId,
            tli.TestLineVersionId,
            tli.TestLineId,
            tli.TestLineStatus,
            tli.ConditionStatus,
            tli.OrdertestLineRemark AS TestLineRemark,
            tli.TestLineType,

            tli.TestLineBaseId,
            tli.CitationBaseId,
            tli.LabSectionBaseId,

            pptlrel.aid AS ArtifactId,
            0 AS PpArtifactRelId,
            0 AS LangBaseId,
            0 AS PpArtifactLang
        FROM tb_test_line_instance tli
                 INNER JOIN tre_pp_test_line_relationship pptlrel ON pptlrel.TestLineInstanceID = tli.Id
        WHERE tli.Id = #{testLineInstanceId}
        order By pptlrel.CreatedDate ASC
    </select>

    <select id="querySubTLStatusByCurrentTLId" resultType="com.sgs.otsnotes.facade.model.dto.CheckSubTLDTO">
        select
            g.orderNo,
            tl.TestLineType as testLineType,
            subTl.TestLineStatus as testLineStatus,
            sub.DataLock as dataLock
        from tb_sub_contract_test_line_mapping m
                 join tb_test_line_instance tl on m.TestLineInstanceID=tl.id
                 join tb_sub_contract sub on m.SubContractID=sub.id
                 join tb_subcontract_external_relationship sj on sj.SubContractNo = sub.SubContractNo
                 join tb_general_order_instance g on sj.ExternalNo = g.OrderNo
                 join tb_test_line_instance subTl on g.id = subTl.GeneralOrderInstanceID and subTl.TestLineID=tl.TestLineID
        where tl.id=#{testLineInstanceId}

    </select>

    <select id="getTestLineClaimInfo" resultType="com.sgs.otsnotes.facade.model.info.testline.TestLineClaimInfo">
        SELECT DISTINCT
        --         tab.TestAnalyteId AS AnalyteId,
        tl.ID AS TestLineInstanceID
        ,tab.TestAnalyteDesc
        ,tl.TestLineVersionID
        ,tl.TestLineType
        ,sa.SampleNo
        ,ma.TestSampleID
        ,sc.id AS sampleClaimId
        ,sc.ClaimType
        ,sc.ClaimValue
        ,sc.ClaimUnit
        ,IFNULL(sc.Unitid, 0) AS Unitid
        FROM tb_test_line_instance tl
        LEFT JOIN tb_test_matrix ma ON ma.TestLineInstanceID = tl.ID
        LEFT JOIN tb_test_sample sa ON sa.ID = ma.TestSampleID
        LEFT JOIN tb_trims_testline_analyte_relationship ttar ON ttar.TestLineVersionId = tl.TestLineVersionId AND ttar.Status = 1
        LEFT JOIN tb_trims_analyte_baseinfo tab ON tab.TestAnalyteId = ttar.TestAnalyteId AND tab.Status = 1
        LEFT JOIN tb_sample_claim_relationship sc ON ma.TestSampleID = sc.SampleId  and sc.ClaimType = tab.TestAnalyteDesc
        -- AND tab.TestAnalyteId = sc.AnalyteId
        INNER JOIN tre_trims_testline_work_instruction_relationship wi ON wi.TestLineVersionId = tl.TestLineVersionID AND wi.RelStatus = 1 AND wi.CategoryId = 15
        WHERE tl.ID IN
        <foreach item="testLineId" collection="testLineIds" open="(" separator="," close=")">
            #{testLineId}
        </foreach>
        ORDER BY tab.TestAnalyteDesc,sa.SampleSeq
    </select>

    <select id="getPageList" resultType="com.sgs.otsnotes.facade.model.dto.TestLinePageListDTO">
        SELECT DISTINCT
        tli.ID as testLineInstanceID,
        tli.GeneralOrderInstanceID as orderId,
        tli.TestLineVersionID as testLineVersionID,
        tli.OrdertestLineRemark as ordertestLineRemark,
        tli.TestItemNo as testItemNo,
        tli.TestLineId     as  testLineId,
        tli.CustomerTestLineName as customerTestLineName,
        tli.CustomerTestLineNameCN as customerTestLineNameCN,
        tli.TestLineType as testLineType,
        goi.OrderNo     as  orderNo,
        ttb.EvaluationName as  testItem,
        CONCAT(tl_cita.CitationName,IF(tl_cita.CitationSectionName IS NULL,'',CONCAT(',',tl_cita.CitationSectionName))) AS testStandard,
        tl_cita.CitationSectionName    as  standardSectionName,
        tli.TestLineStatus     as  testLineStatus,
        tj.JobNo             as  jobNo,
        tj.LabSectionId      as  labSectionId,
        tli.TestStartDate      as  testStartDate,
        tli.Engineer           as  engineer,
        tli.TestEndDate        as  testEndDate,
        tli.TestDueDate        as  testDueDate,
        ttb.EvaluationName as  evaluationNameCn,
        tli.LabTeamCode as labTeamCode,
        tli.CitationBaseId as citationBaseId,
        tlb.LabSectionName as labSection,
        sc.SubContractLabName as subContractName,
        sc.SubContractNo as subContractNo,
        tli.productLineAbbr as productLineAbbr,
        tli.SampleSegegrationWIID as sampleSegegrationWIID,
        tli.SampleSegegrationWIText as sampleSegegrationWIText,
        wi_for_cs.id as wiForCsBaseId,
        wi_for_cs.WorkInstructionText as wiForCsText,
        par.SpecificNotes as PPNotes,
        par.ReportReferenceNoteAlias as PPInternalNo,
        sc.ID as subcontractId,
        IF(sc.ID is null and jtl.ID is null,false ,true) as flag
        FROM
        tb_general_order_instance goi
        LEFT JOIN tb_test_line_instance tli
        ON goi.id = tli.GeneralOrderInstanceID
        LEFT JOIN tb_trims_testline_baseinfo ttb ON tli.TestLineBaseId = ttb.id
        LEFT JOIN tb_trims_artifact_citation_relationship tl_cita ON tl_cita.ID = tli.CitationBaseId
        LEFT JOIN tre_pp_test_line_relationship ptlr ON tli.id = ptlr.testLineInstanceId
        LEFT JOIN tre_trims_pp_artifact_relationship par ON ptlr.PpArtifactrelid = par.id
        LEFT JOIN tb_trims_labsection_baseinfo tlb ON tlb.Id= tli.LabSectionBaseId
        LEFT JOIN tre_job_test_line_relationship jtlr ON jtlr.TestLineInstanceID=tli.id
        LEFT JOIN tb_job tj ON jtlr.JobID=tj.id
        LEFT JOIN tb_sub_contract_test_line_mapping sctl ON sctl.TestLineInstanceID=tli.id
        LEFT JOIN tb_sub_contract sc ON sctl.SubContractID=sc.id
        LEFT JOIN tre_trims_testline_work_instruction_relationship wi_for_cs ON tli.TestLineVersionId = wi_for_cs.TestLineVersionId AND wi_for_cs.CategoryId=15 AND wi_for_cs.RelStatus=1
        LEFT JOIN tre_job_test_line_relationship jtl ON jtl.TestLineInstanceID = tli.ID
        <if test="requestType == 3">
            LEFT JOIN tb_test_matrix tmm ON tmm.TestLineInstanceID = tli.id
        </if>

        WHERE 1=1
        <if test="requestType == 1">
            and tj.JobNo is not null and tli.TestLineStatus != 706
        </if>
        <if test="requestType == 1 and orderNo != null and  orderNo != '' ">
            and goi.orderNo = #{orderNo}
        </if>
        <if test="requestType == 1 and testItem != null and  testItem != '' ">
            and ttb.EvaluationName like CONCAT('%',trim(#{testItem}),'%')
        </if>
        <if test="requestType == 1 and testStandard != null and  testStandard != '' ">
            and tl_cita.CitationName like CONCAT('%',trim(#{testStandard}),'%')
        </if>
        <if test="requestType == 1 and testItemNo != null and  testItemNo != '' ">
            and tli.TestItemNo = #{testItemNo}
        </if>
        <if test="requestType == 1 and testItemArray != null and testItemArray.size() > 0" >
            and tli.TestItemNo in
            <foreach collection="testItemArray" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        </if>
        <if test="requestType == 1 and productLineCode!=null and productLineCode!=''">
            and tli.ProductLineAbbr=#{productLineCode}
        </if>
        <if test="requestType == 1 and labCode!=null and labCode!=''">
            and goi.LabCode=#{labCode}
        </if>
        <if test="requestType == 1 and labTeamCode != null and labTeamCode != ''" >
            and  tli.LabTeamCode like CONCAT('%',trim(#{labTeamCode}),'%')
        </if>
        <if test="requestType == 1 and testStartDateFrom != null and testStartDateFrom != ''" >
            and tli.TestStartDate between  #{testStartDateFrom} and #{testStartDateTo}
        </if>
        <if test="requestType == 1 and createdDateFrom != null and createdDateFrom != ''" >
            and tli.CreatedDate between  #{createdDateFrom} and #{createdDateTo}
        </if>
        <if test="requestType == 1 and testEndDateFrom != null and testEndDateFrom != ''" >
            and tli.TestEndDate between  #{testEndDateFrom} and #{testEndDateTo}
        </if>
        <if test="requestType == 1 and testDueDateFrom != null and testDueDateFrom != ''" >
            and tli.TestDueDate between  #{testDueDateFrom} and #{testDueDateTo}
        </if>
        <if test="requestType == 1 and jobNo != null and jobNo != ''" >
            and tj.JobNo = #{jobNo}
        </if>
        <if test="requestType == 1 and labSectionId != null and labSectionId != ''" >
            and tj.LabSectionId = #{labSectionId}
        </if>
        <if test="requestType == 1 and testLineStatus != null and testLineStatus != ''" >
            and tli.TestLineStatus = #{testLineStatus}
        </if>
        <if test="requestType == 1 and engineer != null and engineer != ''" >
            and tli.Engineer  like CONCAT('%',trim(#{engineer}),'%')
        </if>
        <if test="requestType == 1 and subContractNo != null and subContractNo != ''" >
            and sc.SubContractNo = #{subContractNo}
        </if>
        <!-- 根据filter的值动态生成SQL -->
        <!-- filter=1 表示 Lab in date有值-->
        <if test="requestType == 1 and filterOption == 1">
            and tj.LabInDate IS TRUE
        </if>
        <!-- filter=2 表示 lab out date有值-->
        <if test="requestType == 1 and filterOption == 2">
            and tj.LabOutDate IS TRUE
        </if>
        <!-- filter=3 表示 today - TL due date) <1天 & 没有lab out date或lab in date值）)-->
        <if test="requestType == 1 and filterOption == 3">
            and (tj.LabInDate IS NULL or tj.LabOutDate IS NULL ) and (TIMESTAMPDIFF(MINUTE,tli.TestDueDate,NOW()) &lt; 24*60)
        </if>
        <if test="requestType == 1">
            order by goi.OrderNo desc,tli.TestItemNo asc
        </if>

        <if test="requestType == 2">
            and goi.OrderNo = #{orderNo}
            AND tli.TestLineStatus != 706
            AND tli.TestLineStatus != 707
            AND tli.TestLineStatus != 708
            and tli.PendingFlag is not true
        </if>
        <if test="requestType == 2 and allflag != 1">
            AND sc.ID IS NULL
            AND jtl.ID IS NULL
        </if>
        <if test="requestType == 2">
            ORDER BY
            tlb.LabSectionName,
            .tliTestLineID
        </if>
        <if test="requestType == 3 and SubContractID != null and SubContractID != ''">
            sctl.SubContractID = #{id}
            ORDER BY
            tli.TestLineID,
            tmm.MatrixGroupId
        </if>

    </select>

    <select id="getTestLineByPage" resultType="com.sgs.otsnotes.facade.model.dto.TestLinePageListDTO">
        SELECT DISTINCT
        tli.ID AS testLineInstanceID,
        tli.GeneralOrderInstanceID AS orderId,
        tli.TestLineVersionID AS testLineVersionID,
        tli.OrdertestLineRemark AS ordertestLineRemark,
        tli.TestItemNo AS testItemNo,
        tli.TestLineId AS testLineId,
        tli.CustomerTestLineName AS customerTestLineName,
        tli.CustomerTestLineNameCN AS customerTestLineNameCN,
        tli.TestLineType AS testLineType,
        goi.OrderNo AS orderNo,
        ttb.EvaluationName AS testItem,
        CONCAT(tl_cita.CitationName,IF(tl_cita.CitationSectionName IS NULL,'',CONCAT(',',tl_cita.CitationSectionName))) AS testStandard,
        tl_cita.CitationSectionName AS standardSectionName,
        tli.TestLineStatus AS testLineStatus,
        tj.JobNo AS jobNo,
        tj.LabSectionId AS labSectionId,
        tli.TestStartDate AS testStartDate,
        tli.Engineer AS engineer,
        tli.TestEndDate AS testEndDate,
        tli.TestDueDate AS testDueDate,
        ttb.EvaluationName AS evaluationNameCn,
        tli.LabTeamCode AS labTeamCode,
        tli.CitationBaseId AS citationBaseId,
        sc.SubContractLabName AS subContractName,
        sc.SubContractNo AS subContractNo,
        tli.productLineAbbr AS productLineAbbr,
        tli.SampleSegegrationWIID AS sampleSegegrationWIID,
        tli.SampleSegegrationWIText AS sampleSegegrationWIText,
        par.SpecificNotes AS PPNotes,
        par.ReportReferenceNoteAlias AS PPInternalNo,
        sc.ID AS subcontractId
        FROM
        tb_general_order_instance goi
        LEFT JOIN tb_test_line_instance tli
        ON goi.id = tli.GeneralOrderInstanceID
        LEFT JOIN tb_trims_testline_baseinfo ttb
        ON tli.TestLineBaseId = ttb.id
        LEFT JOIN tb_trims_artifact_citation_relationship tl_cita
        ON tl_cita.ID = tli.CitationBaseId
        LEFT JOIN tre_pp_test_line_relationship ptlr
        ON tli.id = ptlr.testLineInstanceId
        LEFT JOIN tre_trims_pp_artifact_relationship par
        ON ptlr.PpArtifactrelid = par.id
        LEFT JOIN tre_job_test_line_relationship jtlr
        ON jtlr.TestLineInstanceID = tli.id
        LEFT JOIN tb_job tj
        ON jtlr.JobID = tj.id
        LEFT JOIN tb_sub_contract_test_line_mapping sctl
        ON sctl.TestLineInstanceID = tli.id
        LEFT JOIN tb_sub_contract sc
        ON sctl.SubContractID = sc.id
        WHERE 1=1
        and tj.JobNo is not null and tli.TestLineStatus != 706
        <if test="orderNo != null and  orderNo != '' ">
            and goi.orderNo = #{orderNo}
        </if>
        <if test="testItem != null and  testItem != '' ">
            and ttb.EvaluationName like CONCAT('%',trim(#{testItem}),'%')
        </if>
        <if test="testStandard != null and  testStandard != '' ">
            and tl_cita.CitationName like CONCAT('%',trim(#{testStandard}),'%')
        </if>
        <if test="testItemNo != null and  testItemNo != '' ">
            and tli.TestItemNo = #{testItemNo}
        </if>
        <if test="testItemArray != null and testItemArray.size() > 0" >
            and tli.TestItemNo in
            <foreach collection="testItemArray" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        </if>
        <if test="productLineCode!=null and productLineCode!=''">
            and tli.ProductLineAbbr=#{productLineCode}
        </if>
        <if test="labCode!=null and labCode!=''">
            and goi.LabCode=#{labCode}
        </if>
        <if test="labTeamCode != null and labTeamCode != ''" >
            and  tli.LabTeamCode like CONCAT('%',trim(#{labTeamCode}),'%')
        </if>
        <if test="testStartDateFrom != null and testStartDateFrom != ''" >
            and tli.TestStartDate between  #{testStartDateFrom} and #{testStartDateTo}
        </if>
        <if test="createdDateFrom != null and createdDateFrom != ''" >
            and tli.CreatedDate between  #{createdDateFrom} and #{createdDateTo}
        </if>
        <if test="testEndDateFrom != null and testEndDateFrom != ''" >
            and tli.TestEndDate between  #{testEndDateFrom} and #{testEndDateTo}
        </if>
        <if test="testDueDateFrom != null and testDueDateFrom != ''" >
            and tli.TestDueDate between  #{testDueDateFrom} and #{testDueDateTo}
        </if>
        <if test="jobNo != null and jobNo != ''" >
            and tj.JobNo = #{jobNo}
        </if>
        <if test="labSectionId != null and labSectionId != ''" >
            and tj.LabSectionId = #{labSectionId}
        </if>
        <if test="testLineStatus != null and testLineStatus != ''" >
            and tli.TestLineStatus = #{testLineStatus}
        </if>
        <if test="engineer != null and engineer != ''" >
            and tli.Engineer  like CONCAT('%',trim(#{engineer}),'%')
        </if>
        <if test="subContractNo != null and subContractNo != ''" >
            and sc.SubContractNo = #{subContractNo}
        </if>
        <!-- 根据filter的值动态生成SQL -->
        <!-- filter=1 表示 Lab in date有值-->
        <if test="filterOption == 1">
            and tj.LabInDate IS TRUE
        </if>
        <!-- filter=2 表示 lab out date有值-->
        <if test="filterOption == 2">
            and tj.LabOutDate IS TRUE
        </if>
        <!-- filter=3 表示 today - TL due date) <1天 & 没有lab out date或lab in date值）)-->
        <if test="filterOption == 3">
            and (tj.LabInDate IS NULL or tj.LabOutDate IS NULL ) and (TIMESTAMPDIFF(MINUTE,tli.TestDueDate,NOW()) &lt; 24*60)
        </if>
        order by goi.OrderNo desc,tli.TestItemNo asc
    </select>
    <select id="getReportSummaryInfo" resultType="com.sgs.otsnotes.facade.model.info.ReportSummaryInfo">
        SELECT
            tr.OrderNo orderNo,
            tr.ReportNo reportNo ,
            tm.TestSampleID TestSampleId,
            ts.SampleNo sampleNo,
            tm.TestLineInstanceID testLineInstanceId,
            tb.EvaluationName evaluationName,
            tli.LabTeamCode labTeamCode,
            tli.TestDueDate testDueDate,
            tli.OrdertestLineRemark orderTestLineRemark,
            tli.TestLineType testLineType,
            sub.SubContractLabCode subContractLabCode,
            sub.SubContractExpectDueDate subContractExpectDueDate,
            tli.CustomerTestLineName customerTestLineName,
            tli.CustomerTestLineNameCN customerTestLineNameCN
        FROM
            tb_report tr
                LEFT JOIN tre_report_matrix_relationship rm ON tr.id = rm.ReportID
                LEFT JOIN tb_test_matrix tm ON rm.TestMatrixID = tm.id
                LEFT JOIN tb_test_line_instance tli ON tm.TestLineInstanceID = tli.id
                LEFT JOIN tb_trims_testline_baseinfo tb ON tli.TestLineBaseId = tb.id
                LEFT JOIN tb_test_sample ts ON tm.TestSampleID = ts.id
                LEFT JOIN tb_report re ON re.ReportNo = tr.ReportNo
                LEFT JOIN tb_sub_contract_test_line_mapping sub_map on sub_map.TestLineInstanceID = tli.ID
                LEFT JOIN tb_sub_contract sub on sub.ID = sub_map.SubContractID
        WHERE
            tr.OrderNo = #{orderNo}
          AND tli.TestLineStatus != 706
          AND tm.ActiveIndicator = 1
    </select>
    <select id="getTestLineJobSubcontractStatusByOrderID"
            resultType="com.sgs.otsnotes.facade.model.dto.TestLineJobSubcontractDTO">
        SELECT
        DISTINCT
        tl.id as testLineInstanceId,
        tl.TestLineID,
        tl.TestLineStatus,
        job.JobNo,
        job.JobStatus,
        sub.SubContractNo,
        sub.Status as subStatus,
        ac.CitationName as standardName
        FROM
        tb_test_line_instance tl
        JOIN tb_trims_artifact_citation_relationship ac  ON ac.id = tl.CitationBaseId
        LEFT JOIN tre_job_test_line_relationship jt ON jt.TestLineInstanceID = tl.id
        LEFT JOIN tb_job job ON jt.JobID = job.id
        LEFT JOIN tb_sub_contract_test_line_mapping st ON st.TestLineInstanceID = tl.id
        LEFT JOIN tb_sub_contract sub ON st.SubContractID = sub.id
        WHERE
        tl.GeneralOrderInstanceID = #{orderId}
        and tl.testLineId in
        <foreach collection="testLineIds" item="tlId" open="(" close=")" separator=",">
            #{tlId}
        </foreach>

    </select>

    <resultMap id="queryCitationResultMap" type="com.sgs.otsnotes.facade.model.dto.trimslocaldata.QueryCitationNameDTO">
        <result column="testLineInstanceId" property="testLineInstanceId"></result>
        <result column="ppTestLineRelId" property="ppTestLineRelId"></result>
        <result column="rootPpBaseId" property="rootPpBaseId"></result>
        <result column="tlCitationBaseId" property="citationBaseId"></result>
        <result column="ppArtifactRelId" property="ppArtifactRelId"></result>

        <collection column="ppCitationBaseIds" property="ppCitationBaseIds" ofType="java.lang.Long">
            <result column="ppCitationBaseId"></result>
        </collection>
        <collection property="lanDtos" ofType="com.sgs.otsnotes.facade.model.dto.trimslocaldata.QueryCitationNameLanItemDTO">
            <result column="LangBaseId" property="langBaseId"></result>
            <result column="LangType" property="langType"></result>
            <result column="ObjectBaseId" property="objectBaseId"></result>
        </collection>
    </resultMap>

    <update id="updateTestLineDueDateBatch" parameterType="java.util.List">
        <foreach collection="testLineList" item="item" index="index" open="" close="" separator=";">
            UPDATE tb_test_line_instance tl
            SET tl.TestDueDate = #{item.testDueDate},
                tl.ModifiedDate = #{item.modifiedDate},
                tl.ModifiedBy = #{item.modifiedBy}
            WHERE
            tl.ID = #{item.ID}
            AND tl.TestLineStatus NOT IN (706, 703)
        </foreach>
    </update>

    <select id="queryCitationParamDataByOrderId" resultMap="queryCitationResultMap">
        SELECT
            tl.id AS testLineInstanceId,
            rel.id AS ppTestLineRelId,
            tl.CitationBaseId AS tlCitationBaseId,
            rel.PpArtifactRelId AS ppArtifactRelId,
            rel.RootPpBaseId AS rootPpBaseId,
            oc.CitationBaseId AS ppCitationBaseId,
            lan.LangBaseId,
            lan.LangType,
            lan.ObjectBaseId
        FROM
            tb_test_line_instance tl
                JOIN tre_pp_test_line_relationship rel ON tl.id = rel.TestLineInstanceID
                LEFT JOIN tre_order_citation_relationship oc ON oc.OrderId = tl.GeneralOrderInstanceID
                AND oc.PpBaseId = rel.PpBaseId
                LEFT JOIN tre_order_language_relationship lan ON lan.OrderId = tl.GeneralOrderInstanceID
                AND lan.LangType IN ( 3, 9, 11 )
        WHERE
            tl.GeneralOrderInstanceID = #{orderId}
    </select>

    <select id="getSubcontractTestLineInfo" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT
            tl.Id,
            tl.testLineID,
            tl.testLineStatus,
            tl.ActiveIndicator,
            tl.TestLineType
        FROM tb_sub_contract_test_line_mapping sctl
                 LEFT JOIN tb_test_line_instance tl ON tl.ID = sctl.TestLineInstanceID
        WHERE SubContractID = #{subcontractId}
    </select>

    <!--GPO Begin-->

    <resultMap id="GPOBaseResultMap" type="com.sgs.otsnotes.facade.model.gpn.testline.info.TLInstanceBaseInfo" >

        <id column="ID" property="id" jdbcType="VARCHAR" />
        <result column="GeneralOrderInstanceID" property="generalOrderInstanceId" jdbcType="VARCHAR" />
        <result column="orderNo" property="orderNo" jdbcType="VARCHAR" />
        <result column="labCode" property="labCode" jdbcType="VARCHAR" />
        <result column="TestItemNo" property="testItemNo" jdbcType="VARCHAR" />
        <result column="TestLineType" property="testLineType" jdbcType="INTEGER" />
        <result column="testLineBaseID" property="testLineBaseId" jdbcType="INTEGER" />
        <result column="TestLineVersionID" property="testLineVersionId" jdbcType="INTEGER" />
        <result column="TestLineID" property="testLineId" jdbcType="INTEGER" />
        <result column="CustomerTestLineName" property="customerTestLineName" jdbcType="VARCHAR" />
        <result column="CustomerTestLineNameCN" property="customerTestLineNameCN" jdbcType="VARCHAR" />

        <result column="ProductLineCode" property="productLineCode" jdbcType="VARCHAR" />
        <result column="labSectionBaseId" property="labSectionBaseId" jdbcType="INTEGER" />
        <result column="LabTeamCode" property="labTeamCode" jdbcType="VARCHAR" />
        <result column="Engineer" property="engineer" jdbcType="VARCHAR" />

        <result column="TestLineSeq" property="testLineSeq" jdbcType="INTEGER" />
        <result column="ReportSeq" property="reportSeq" jdbcType="INTEGER" />
        <result column="OrderSeq" property="orderSeq" jdbcType="INTEGER" />

        <result column="OrdertestLineRemark" property="remark" jdbcType="VARCHAR" />

        <result column="ValidateDate" property="validateDate" jdbcType="TIMESTAMP" />
        <result column="ValidateBy" property="validateBy" jdbcType="VARCHAR" />

        <result column="PendingFlag" property="pendingFlag" jdbcType="INTEGER" />
        <result column="AutoConclusion" property="autoConclusion" jdbcType="BIT" />
        <result column="CalculateConclusionFlag" property="calculateConclusionFlag" jdbcType="INTEGER" />

        <result column="TestStartDate" property="testStartDate" jdbcType="VARCHAR" />
        <result column="TestEndDate" property="testEndDate" jdbcType="VARCHAR" />
        <result column="TestDueDate" property="testDueDate" jdbcType="VARCHAR" />

        <result column="ConditionStatus" property="conditionStatus" jdbcType="INTEGER" />
        <result column="TestLineStatus" property="testLineStatus" jdbcType="INTEGER" />
        <result column="DocumentReviewFlag" property="documentReviewFlag" jdbcType="INTEGER" />


        <result column="ActiveIndicator" property="activeIndicator" jdbcType="BIT" />
        <result column="CreatedDate" property="createdDate" jdbcType="TIMESTAMP" />
        <result column="CreatedBy" property="createdBy" jdbcType="VARCHAR" />
        <result column="ModifiedDate" property="modifiedDate" jdbcType="TIMESTAMP" />
        <result column="ModifiedBy" property="modifiedBy" jdbcType="VARCHAR" />
        <result column="ClientStandard" property="clientStandard" jdbcType="VARCHAR" />
        <result column="ClientStandard" property="clientStandard" jdbcType="VARCHAR" />
        <result column="PpTlRelId" property="ppTlRelId" jdbcType="VARCHAR" />




        <association property="citation" javaType="com.sgs.otsnotes.facade.model.gpn.citation.info.CitationInfo">
            <result column="CitationTypeID" property="citationTypeId" jdbcType="INTEGER" />
            <result column="CitationBaseID" property="citationBaseId" jdbcType="INTEGER" />
            <result column="CitationVersionId" property="citationVersionId" jdbcType="INTEGER" />
            <result column="CitationId" property="citationId" jdbcType="INTEGER" />
            <result column="editCitationName" property="editCitationName" jdbcType="VARCHAR" />
            <collection property="languages" column="TestLineInstanceID"
                        javaType="List" ofType="com.sgs.otsnotes.facade.model.gpn.citation.info.CitationLanguageInfo">
                <result column="languageId" property="languageId" jdbcType="INTEGER" />
                <result column="editCitationNameCN" property="editCitationName" jdbcType="VARCHAR" />
            </collection>
        </association>



        <collection property="ppInstanceBases" column="ArtifactId"
                    javaType="List" ofType="com.sgs.otsnotes.facade.model.gpn.pp.info.PPInstanceBaseInfo">
            <result column="RootPpBaseID" property="rootPpBaseId" jdbcType="INTEGER" />
            <result column="PpBaseID" property="ppBaseId" jdbcType="INTEGER" />
            <result column="PpArtifactRelID" property="ppArtifactRelId" jdbcType="INTEGER" />
            <result column="ppName" property="ppName" jdbcType="VARCHAR" />
            <result column="ArtifactId" property="artifactId" jdbcType="INTEGER" />
            <result column="PPNotes" property="ppNotes" jdbcType="VARCHAR" />
            <result column="TestLineInstanceID" property="testLineInstanceID" jdbcType="VARCHAR" />
            <result column="Seq" property="seq" jdbcType="INTEGER" />
        </collection>

    </resultMap>

    <resultMap id="GPODetailResultMap" extends="GPOBaseResultMap" type="com.sgs.otsnotes.facade.model.gpn.testline.info.TLInstanceDetailInfo" >

        <!-- 一对一关联：WI For Sample -->
        <association property="wiForSample" column="WIForSampleId" javaType="com.sgs.otsnotes.facade.model.gpn.wi.info.WorkingInstructionInfo">
            <id column="WIForSampleId" property="workingInstructionId" />
            <result column="WIForSampleText" property="workingInstructionText" />
        </association>

        <association property="jobSimplify" column="jobId" javaType="com.sgs.otsnotes.facade.model.gpn.job.info.JobSimplifyInfo">
            <id column="jobId" property="id" />
            <result column="jobNo" property="jobNo" />
            <result column="labInDate" property="labInDate" />
            <result column="labOutDate" property="labOutDate" />
        </association>

        <association property="subcontractSimplify" column="subcontractId" javaType="com.sgs.otsnotes.facade.model.gpn.subcontract.info.SubcontractSimplifyInfo">
            <id column="subcontractId" property="id" />
            <result column="subcontractNo" property="subcontractNo" />
            <result column="subcontractLabCode" property="subcontractLabCode" />
            <result column="subcontractExpectDueDate" property="subcontractExpectDueDate" />
        </association>

       <!-- <collection property="tlAnalyteInstances" column="ArtifactId"
                    javaType="List" ofType="com.sgs.otsnotes.facade.model.gpn.analyte.info.AnalyteInstanceInfo">
            <result column="AnalyteBaseId" property="analyteBaseId" jdbcType="INTEGER" />
            <result column="AnalyteID" property="analyteId" jdbcType="INTEGER" />
            <result column="TestAnalyteName" property="testAnalyteName" jdbcType="VARCHAR" />
            <result column="TestAnalyteSeq" property="testAnalyteSeq" jdbcType="INTEGER" />
            <collection property="reportUnits" column="ReportUnitId"
                        javaType="List" ofType="com.sgs.otsnotes.facade.model.gpn.unit.info.ReportUnitInfo">
                <result column="ReportUnit" property="reportUnit" jdbcType="VARCHAR" />
            </collection>
        </collection>-->

    </resultMap>

    <sql id="gpnBaseColumnList" >

        `goi`.`ID` AS `orderId`,`goi`.`OrderNo` AS `orderNo`,`goi`.`LabCode` AS `labCode`,
        tli.ID,tli.GeneralOrderInstanceID,

        tli.TestItemNo,tli.TestLineType,tli.TestLineBaseID,tli.TestLineVersionID,tli.TestLineID,tli.CustomerTestLineName,tli.CustomerTestLineNameCN,

        tli.CitationTypeID,tli.CitationBaseID,tli.CitationVersionID,tli.CitationId,

        tli.TestStartDate,tli.TestEndDate,tli.TestDueDate,

        tli.ProductLineAbbr,tli.LabSectionBaseID,tli.LabTeamCode,tli.Engineer,

        tli.SampleSegegrationWIID,tli.SampleSegegrationWIText,tli.modified,

        tli.TestLineSeq,tli.ReportSeq,tli.OrderSeq,

        tli.PendingFlag,tli.AutoConclusion,tli.CalculateConclusionFlag,

        tli.ValidateBy,tli.ValidateDate,

        tli.OrdertestLineRemark,

        tli.ConditionStatus,tli.TestLineStatus,

        tli.CreatedBy,tli.CreatedDate,tli.ModifiedBy,tli.ModifiedDate,

        ptlr.ID as PpTlRelId,ptlr.PpArtifactRelID,ptlr.RootPpBaseID,ptlr.PpBaseID,ptlr.PPNotes,ptlr.aid as ArtifactId,ptlr.TestLineInstanceID,ptlr.Seq,

        tli.ClientStandard,tli.CitationName as editCitationName,tli.DocumentReviewFlag
    </sql>

    <select id="getTLInstanceBases" resultMap="GPOBaseResultMap">
        SELECT
        <include refid="gpnBaseColumnList"/>
        FROM
        tb_general_order_instance goi
        LEFT JOIN tb_test_line_instance tli on goi.id = tli.GeneralOrderInstanceID
        LEFT JOIN tre_pp_test_line_relationship ptlr ON tli.id = ptlr.TestLineInstanceID and ptlr.PpBaseId > 0
        <where>
            <if test="orderNo != null and orderNo != ''" >
                and goi.OrderNo = #{orderNo}
            </if>
            <if test="orderId != null and orderId != ''" >
                and goi.ID = #{orderId}
            </if>
            <if test="testLineInstanceIds != null and testLineInstanceIds.size() > 0" >
                and tli.`ID` in
                <foreach collection="testLineInstanceIds" open="(" close=")" separator="," item="item">
                    #{item}
                </foreach>
            </if>
        </where>
        ORDER BY
        goi.OrderNo desc, ISNULL(tli.OrderSeq), tli.OrderSeq, ptlr.seq, tli.TestItemNo ,ptlr.CreatedDate
    </select>

    <select id="getTestLineIdsByPpTestLineRelIds" resultType="java.lang.String">
        SELECT DISTINCT
        TestLineInstanceID
        FROM tre_pp_test_line_relationship WHERE Id in
        <foreach collection="ppTlIds" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>

    <select id="getTLInstanceDetails" resultMap="GPODetailResultMap">
        SELECT distinct
        <include refid="gpnBaseColumnList"/>
        ,tlml.LanguageId as languageId,tlml.CitationName as editCitationNameCN,tli.SampleSegegrationWIID WIForSampleId,tli.SampleSegegrationWIText WIForSampleText,
        `jobrel`.`JobID` AS `jobId`,`jobrel`.`ID` AS `jobRelId`,`tj`.`JobNo` AS `jobNo`,`tj`.`LabInDate` AS `labInDate`,`tj`.`LabOutDate` AS `labOutDate`,
        `subcontract`.`ID` AS `subcontractId`,`subcontract`.`subcontractNo` AS `subcontractNo`,`subcontract`.`subcontractLabCode` AS `subcontractLabCode`,`subcontract`.`subcontractExpectDueDate` AS `subcontractExpectDueDate`

        FROM
        tb_general_order_instance goi
        inner join tb_order_index oi on oi.order_no = goi.orderNo
        INNER JOIN tb_test_line_instance tli on goi.id = tli.GeneralOrderInstanceID
        LEFT JOIN tb_test_line_instance_multiplelanguage tlml on tlml.TestLineInstanceID = tli.id
        LEFT JOIN tre_pp_test_line_relationship ptlr ON tli.id = ptlr.TestLineInstanceID
        LEFT JOIN tb_trims_pp_baseinfo ttpb ON ptlr.PpBaseId = ttpb.id
        LEFT JOIN tre_trims_pp_artifact_relationship ttpar ON ttpar.id = ptlr.PpArtifactRelId
        LEFT JOIN tb_trims_pp_section_baseinfo ttpsb ON ttpsb.id = ttpar.SectionBaseId
        LEFT JOIN tre_job_test_line_relationship jobrel ON jobrel.TestLineInstanceID = tli.id
        LEFT JOIN tb_job tj ON jobrel.JobID = tj.ID	AND tj.JobStatus != 1104
        LEFT JOIN tb_sub_contract_test_line_mapping sub_tl_map ON sub_tl_map.TestLineInstanceID = tli.ID
        LEFT JOIN tb_sub_contract subcontract ON sub_tl_map.SubContractID = subcontract.id
--         LEFT JOIN tb_analyte_instance ai on tli.id = ai.TestLineInstanceID
        <if test="(reportNo != null and reportNo != '')||(reportId != null and reportId != '')" >
            LEFT JOIN tb_report tr on goi.OrderNo = tr.OrderNo
            LEFT JOIN tre_report_matrix_relationship trmr on tr.id = trmr.ReportID
            LEFT JOIN tb_test_matrix ttm ON ttm.ID = trmr.TestMatrixID AND ttm.`TestLineInstanceID` = tli.`ID` AND ttm.ActiveIndicator = 1

        </if>
        LEFT JOIN tb_trims_labsection_baseinfo labsectionbase ON tli.LabSectionBaseId = labsectionbase.id
        <include refid="getTLInstanceDetails_Where_Clause" />
        ORDER BY
        goi.OrderNo desc,
        ISNULL(tli.OrderSeq),
        tli.OrderSeq,
        ptlr.Seq,
        ISNULL(ttpb.PpNo),
        ttpb.PpNo,
        ISNULL(ttpsb.SectionLevel),
        ttpsb.SectionLevel,
        tli.TestItemNo,
        ptlr.CreatedDate
    </select>

    <select id="getOrderTestLineRelForFirstPpArtifactRel" resultType="com.sgs.otsnotes.facade.model.dto.testline.OrderTestLineRelDTO" parameterType="com.sgs.otsnotes.facade.model.req.testLine.OrderTestLineRelReq">
        SELECT DISTINCT
        goi.id as orderId,
        goi.orderNo,
        tl.id AS testLineInstanceId,
        tl.testLineBaseId,
        tl.testLineId,
        tl.testLineVersionId,
        tl.citationBaseId,
        pptlrel.PpArtifactRelId,
        GROUP_CONCAT(pptlrel.PpArtifactRelId SEPARATOR ',') originalPpArtifactRelId,
        ppbase.Id AS ppBaseId ,
        ppbase.ppVersionId ,
        ppbase.ppNo
        FROM
        tb_general_order_instance goi
        INNER JOIN tre_pp_test_line_relationship pptlrel ON goi.id = pptlrel.GeneralOrderInstanceID
        INNER JOIN tb_test_line_instance tl ON pptlrel.TestLineInstanceID = tl.ID
        LEFT JOIN tb_trims_pp_baseinfo ppbase ON  ppbase.Id = pptlrel.PpBaseId
        <where>
            <if test="orderNo != null and  orderNo != '' ">
                and goi.orderNo = #{orderNo}
            </if>
            <if test="testLineInstanceIds != null and testLineInstanceIds.size() > 0" >
                and `tl`.`id` in
                <foreach collection="testLineInstanceIds" open="(" close=")" separator="," item="item">
                    #{item}
                </foreach>
            </if>
        </where>
        GROUP BY goi.OrderNo,tl.id,tl.CitationBaseId
        order by goi.OrderNo desc,tl.OrderSeq,tl.TestItemNo desc,pptlrel.CreatedDate
    </select>


    <select id="getPpTestLineJob" resultType="com.sgs.otsnotes.facade.model.dto.PPTestLineJobDto">
        SELECT DISTINCT
        map.subOrderId,
        pptl.ID AS ppTestLineRelID,
        tl.ID AS testLineInstanceId,
        tl.TestLineStatus,
        jobtlrel.ID AS testLineJobId,
        con.Id AS testConditionId,
        cg.id AS testConditionGroupId,
        ma.id AS matrixId,
        marel.id AS reportMatrixRelId,
        tl.PendingFlag as pendingFlag
        FROM
        tre_order_subcontract_relationship_mapping map
        LEFT JOIN tb_test_line_instance tl ON map.subRelId = tl.ID
        LEFT JOIN tre_pp_test_line_relationship pptl ON tl.ID = pptl.TestLineInstanceID
        LEFT JOIN tre_job_test_line_relationship jobtlrel ON tl.id = jobtlrel.TestLineInstanceID
        LEFT JOIN tb_test_condition_instance con ON tl.id = con.TestLineInstanceID
        LEFT JOIN tb_test_condition_group cg ON cg.TestLineInstanceID = tl.id
        LEFT JOIN tb_test_matrix ma ON ma.TestLineInstanceID = tl.id
        LEFT JOIN tre_report_matrix_relationship marel ON marel.TestMatrixID = ma.id
        WHERE
        map.originalOrderId = #{orderId}
        AND map.originalRelId IN
        <foreach collection="testLineInstanceIds" item="id" separator="," open="(" close=")">
            #{id}
        </foreach>
        <if test="exeOrderId != null and exeOrderId != ''" >
            AND  map.subOrderId = #{exeOrderId}
        </if>
    </select>


    <select id="queryTrimsTestLineListByName"
            resultType="com.sgs.otsnotes.facade.model.gpn.testline.rsp.TestItemRsp">
        SELECT DISTINCT
            tli.TestLineID as testLineId,
            acr.EvaluationAlias citationTestItem,
            acrl.EvaluationAlias citationTestItemCn,
            par.EvaluationAlias ppTestItem,
            parl.EvaluationAlias PPTestItemCn
        FROM
            tb_general_order_instance goi
                INNER JOIN tb_test_line_instance tli ON goi.id = tli.GeneralOrderInstanceID
                left JOIN tre_pp_test_line_relationship ptlr ON goi.id = ptlr.GeneralOrderInstanceID
                AND tli.id = ptlr.TestLineInstanceID
                left JOIN tb_trims_artifact_citation_relationship acr ON tli.CitationBaseId = acr.id
                left join tb_trims_artifact_citation_language acrl on acrl.CitationBaseId = tli.CitationBaseId
                LEFT JOIN tre_trims_pp_artifact_relationship par ON par.Id = ptlr.PpArtifactRelId
                LEFT JOIN tre_trims_pp_artifact_language parl ON parl.PpArtifactRelId = par.id
        WHERE
            goi.LabCode = #{labCode}
            AND (acr.EvaluationAlias like CONCAT('%',#{evaluationAlias},'%')
           OR acrl.EvaluationAlias like CONCAT('%',#{evaluationAlias},'%')
           OR par.EvaluationAlias like CONCAT('%',#{evaluationAlias},'%')
           OR parl.EvaluationAlias like CONCAT('%',#{evaluationAlias},'%'))
            LIMIT #{limit};
    </select>

    <select id="getLabSectionNameByTlIds" resultType="com.sgs.otsnotes.facade.model.req.EquipmentTestLineReq">
        SELECT
            tli.TestLineVersionID testLineVersionId,
            labsectionbase.LabSectionName labSection
        FROM
            tb_test_line_instance tli
                LEFT JOIN tb_trims_labsection_baseinfo labsectionbase ON tli.LabSectionBaseId = labsectionbase.id
        WHERE tli.GeneralOrderInstanceID = #{orderInstanceID}
        <if test="testLineInstanceIds != null and testLineInstanceIds.size() > 0" >
            AND tli.ID IN
            <foreach collection="testLineInstanceIds" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        </if>
    </select>
    <!--GPO End-->


    <select id="queryTestLineInstanceForMatrix" resultType="com.sgs.otsnotes.facade.model.dto.testline.MatrixTestlineInstanceDTO">
        SELECT
        oo.OrderNo as orderNo,
        ti.TestLineVersionID as testLineVersionId,
        ti.CitationVersionId as citationVersionId,
        pptlrel.aid as aid,
        pptlrel.ID as ppTlRelId
        FROM
        tre_pp_test_line_relationship pptlrel
        INNER JOIN tb_test_line_instance ti ON pptlrel.TestLineInstanceID = ti.ID
        INNER JOIN tb_general_order_instance oo ON oo.ID = ti.GeneralOrderInstanceID
        WHERE
        ti.TestLineStatus!=706 and
        oo.OrderNo IN
        <foreach item="item" index="index" collection="orderNoList" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="getWIPOinfo" resultType="com.sgs.otsnotes.dbstorages.mybatis.model.WIPO">
        SELECT
        tli.id as testLineInstanceId,
        tli.CitationBaseId,tli.GeneralOrderInstanceID, tli.TestLineVersionID, tli.CitationId as tlCitationId, ptlr.PpBaseId,ptlr.RootPpBaseId AS rootPpBaseId ,
        ocr.CitationBaseId as ppCitationBaseId
        FROM tb_test_line_instance tli LEFT JOIN tre_pp_test_line_relationship ptlr ON tli.id = ptlr.TestLineInstanceID
            LEFT JOIN tre_order_citation_relationship ocr ON ptlr.PpBaseId = ocr.PpBaseId AND tli.GeneralOrderInstanceID = ocr.OrderId
        where
        <choose>
            <when test="testLineInstanceIdList != null and testLineInstanceIdList.size() > 0">
                tli.id in
                <foreach item="item" index="index" collection="testLineInstanceIdList" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                1=2
            </otherwise>
        </choose>
    </select>


    <select id="getTestLineLanguages" resultType="com.sgs.otsnotes.dbstorages.mybatis.model.TestLineInstanceMultipleLanguageInfoPO">
            select
        <include refid="Multiple_Language_Test_Line_Base_Column_List" />
        from tb_test_line_instance_multiplelanguage
        where
        <choose>
            <when test="testLineInstanceIdList != null and testLineInstanceIdList.size() > 0">
                TestLineInstanceID in
                <foreach item="item" index="index" collection="testLineInstanceIdList" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                1=2
            </otherwise>
        </choose>

    </select>


    <select id="queryTestLineLabSectionRel" resultType="com.sgs.otsnotes.facade.model.dto.TestLineLabSectionRelDTO" >
        select test_line_instance_id as testLineInstanceId,lab_section_base_id as labSectionBaseId,lab_section_id as labSectionId
        from tb_testline_labsection_relationship
        where
        <choose>
            <when test="testLineInstanceIdList != null and testLineInstanceIdList.size() > 0">
                test_line_instance_id in
                <foreach item="item" index="index" collection="testLineInstanceIdList" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                1=2
            </otherwise>
        </choose>

    </select>

    <select id="getPpTestLineInfoByPpTestLineIds" resultType="com.sgs.otsnotes.facade.model.info.testline.PpTestLineInfo">
        SELECT DISTINCT
        tl.GeneralOrderInstanceID AS orderId,
        tr.ID AS PPTestLineRelID,
        tl.ID AS testLineInstanceId,
        tl.TestLineID,
        tl.TestLineVersionID AS testLineVersionId,
        tl.CitationId,
        tl.CitationVersionId,
        tl.CitationBaseId,
        tr.PpBaseId,
        tr.PpArtifactRelId,
        tr.aid,
        tl.TestLineStatus
        FROM
        tb_test_line_instance tl
        INNER JOIN tre_pp_test_line_relationship tr ON tr.TestLineInstanceID = tl.ID
        WHERE
        tl.GeneralOrderInstanceID = #{orderId}
        <if test="ppTestLineInstanceIds != null and ppTestLineInstanceIds.size() > 0">
            AND tr.Id IN
            <foreach collection="ppTestLineInstanceIds" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
    </select>

</mapper>