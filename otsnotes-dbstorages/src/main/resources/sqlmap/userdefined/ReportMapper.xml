<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sgs.otsnotes.dbstorages.mybatis.extmapper.ReportMapper" >
    <resultMap id="BaseResultMap" type="com.sgs.otsnotes.dbstorages.mybatis.model.ReportInfoPO" >
        <id column="ID" property="ID" jdbcType="VARCHAR" />
        <result column="OrderNo" property="orderNo" jdbcType="VARCHAR" />
        <result column="ReportNo" property="reportNo" jdbcType="VARCHAR" />
        <result column="ActualReportNo" property="actualReportNo" jdbcType="VARCHAR" />
        <result column="RootReportNo" property="rootReportNo" jdbcType="VARCHAR" />
        <result column="ParentReportNo" property="parentReportNo" jdbcType="VARCHAR" />
        <result column="LabId" property="labId" jdbcType="INTEGER" />
        <result column="LabCode" property="labCode" jdbcType="VARCHAR" />
        <result column="ReportDueDate" property="reportDueDate" jdbcType="TIMESTAMP" />
        <result column="ReportStatus" property="reportStatus" jdbcType="INTEGER" />
        <result column="CoverPageTemplateID" property="coverPageTemplateID" jdbcType="VARCHAR" />
        <result column="CoverPageTemplateName" property="coverPageTemplateName" jdbcType="VARCHAR" />
        <result column="CoverPageTemplatePath" property="coverPageTemplatePath" jdbcType="VARCHAR" />
        <result column="CoverPageTemplateNewMappingFlag" property="coverPageTemplateNewMappingFlag" jdbcType="BIT" />
        <result column="TemplateID" property="templateID" jdbcType="VARCHAR" />
        <result column="ReportTypeID" property="reportTypeID" jdbcType="VARCHAR" />
        <result column="RequestID" property="requestID" jdbcType="VARCHAR" />
        <result column="RequestSentDate" property="requestSentDate" jdbcType="TIMESTAMP" />
        <result column="RequestFinishedDate" property="requestFinishedDate" jdbcType="TIMESTAMP" />
        <result column="ApproverBy" property="approverBy" jdbcType="VARCHAR" />
        <result column="Approver" property="approver" jdbcType="VARCHAR" />
        <result column="ApproverDate" property="approverDate" jdbcType="TIMESTAMP" />
        <result column="CustomerCode" property="customerCode" jdbcType="VARCHAR" />
        <result column="CustomerGroupCode" property="customerGroupCode" jdbcType="VARCHAR" />
        <result column="AmendRemark" property="amendRemark" jdbcType="VARCHAR" />
        <result column="ActiveIndicator" property="activeIndicator" jdbcType="BIT" />
        <result column="LogoAliyunID" property="logoAliyunID" jdbcType="VARCHAR" />
        <result column="CreatedBy" property="createdBy" jdbcType="VARCHAR" />
        <result column="CreatedDate" property="createdDate" jdbcType="TIMESTAMP" />
        <result column="ModifiedBy" property="modifiedBy" jdbcType="VARCHAR" />
        <result column="ModifiedDate" property="modifiedDate" jdbcType="TIMESTAMP" />
        <result column="ReportFlag" property="reportFlag" jdbcType="INTEGER" />
        <result column="NeedReview" property="needReview" jdbcType="INTEGER" />
        <result column="WorkFlow" property="workFlow" jdbcType="INTEGER" />
        <result column="EditorBy" property="editorBy" jdbcType="VARCHAR" />
        <result column="Editor" property="editor" jdbcType="VARCHAR" />
        <result column="ReviewerBy" property="reviewerBy" jdbcType="VARCHAR" />
        <result column="Reviewer" property="reviewer" jdbcType="VARCHAR" />
        <result column="SoftcopyDeliveryDate" property="softcopyDeliveryDate" jdbcType="TIMESTAMP" />
        <result column="NeedToAmendFlag" property="needToAmendFlag" jdbcType="INTEGER" />
        <result column="ExternalReportNo" property="externalReportNo" jdbcType="VARCHAR" />
        <result column="SignatureLanguage" property="signatureLanguage" jdbcType="VARCHAR" />
        <result column="SealFlag" property="sealFlag" jdbcType="INTEGER" />
        <result column="CertificateId" property="certificateId" jdbcType="VARCHAR" />
        <result column="CertificateName" property="certificateName" jdbcType="VARCHAR" />
        <result column="CountryOfDestination" property="countryOfDestination" jdbcType="VARCHAR" />
        <result column="ReportVersion" property="reportVersion" jdbcType="INTEGER"/>
        <result column="EntryMode" property="entryMode" jdbcType="VARCHAR"/>
    </resultMap>

    <resultMap id="reportAndSubReportMap" type="com.sgs.otsnotes.facade.model.dto.GpnOrderReportDTO" extends="BaseResultMap">
        <result column="SubReportNo" property="subReportNo" jdbcType="VARCHAR" />
    </resultMap>

    <resultMap id="parentReportMap" type="com.sgs.otsnotes.facade.model.rsp.report.ParentReportRsp">
        <result column="byReportNo" property="byReportNo" jdbcType="VARCHAR" />
        <association property="parentReport" javaType="com.sgs.otsnotes.facade.model.rsp.report.ReportRsp">
            <id column="ID" property="ID" jdbcType="VARCHAR" />
            <result column="OrderNo" property="orderNo" jdbcType="VARCHAR" />
            <result column="ReportNo" property="reportNo" jdbcType="VARCHAR" />
            <result column="ParentReportNo" property="parentReportNo" jdbcType="VARCHAR" />
            <result column="ReportDueDate" property="reportDueDate" jdbcType="TIMESTAMP" />
            <result column="ReportStatus" property="reportStatus" jdbcType="INTEGER" />
            <result column="CoverPageTemplateID" property="coverPageTemplateID" jdbcType="VARCHAR" />
            <result column="CoverPageTemplateName" property="coverPageTemplateName" jdbcType="VARCHAR" />
            <result column="CoverPageTemplatePath" property="coverPageTemplatePath" jdbcType="VARCHAR" />
            <result column="CoverPageTemplateNewMappingFlag" property="coverPageTemplateNewMappingFlag" jdbcType="BIT" />
            <result column="TemplateID" property="templateID" jdbcType="VARCHAR" />
            <result column="ReportTypeID" property="reportTypeID" jdbcType="VARCHAR" />
            <result column="RequestID" property="requestID" jdbcType="VARCHAR" />
            <result column="RequestSentDate" property="requestSentDate" jdbcType="TIMESTAMP" />
            <result column="RequestFinishedDate" property="requestFinishedDate" jdbcType="TIMESTAMP" />
            <result column="ApproverBy" property="approverBy" jdbcType="VARCHAR" />
            <result column="Approver" property="approver" jdbcType="VARCHAR" />
            <result column="ApproverDate" property="approverDate" jdbcType="TIMESTAMP" />
            <result column="CustomerCode" property="customerCode" jdbcType="VARCHAR" />
            <result column="CustomerGroupCode" property="customerGroupCode" jdbcType="VARCHAR" />
            <result column="AmendRemark" property="amendRemark" jdbcType="VARCHAR" />
            <result column="ActiveIndicator" property="activeIndicator" jdbcType="BIT" />
            <result column="LogoAliyunID" property="logoAliyunID" jdbcType="VARCHAR" />
            <result column="CreatedBy" property="createdBy" jdbcType="VARCHAR" />
            <result column="CreatedDate" property="createdDate" jdbcType="TIMESTAMP" />
            <result column="ModifiedBy" property="modifiedBy" jdbcType="VARCHAR" />
            <result column="ModifiedDate" property="modifiedDate" jdbcType="TIMESTAMP" />
            <result column="ReportFlag" property="reportFlag" jdbcType="INTEGER" />
            <result column="NeedReview" property="needReview" jdbcType="INTEGER" />
        </association>
    </resultMap>
    <sql id="Base_Column_List" >
        ID, OrderNo, ReportNo, ParentReportNo, ReportDueDate, ReportStatus, CoverPageTemplateID,
        CoverPageTemplateName, CoverPageTemplatePath, CoverPageTemplateNewMappingFlag, TemplateID,
        ReportTypeID, RequestID, RequestSentDate, RequestFinishedDate, ApproverBy, Approver,
        ApproverDate, CustomerCode, CustomerGroupCode, CertificateId, CertificateFileCloudKey, CertificateName,
        AmendRemark, ActiveIndicator, CreatedBy, CreatedDate, ModifiedBy, ModifiedDate, RecalculationFlag,
        ConclusionMd5,LogoAliyunID
    </sql>

    <sql id="Report_Column_List">
		ID,OrderNo,ReportNo,ParentReportNo,ReportDueDate,ReportStatus,TemplateID,CoverPageTemplateID,CertificateId,CertificateFileCloudKey,CertificateName,
		CoverPageTemplateName,CoverPageTemplatePath,ReportTypeID,RequestID,RequestSentDate,RequestFinishedDate,ApproverBy,Approver,ApproverDate,CustomerCode,
		CustomerGroupCode,AmendRemark,ActiveIndicator,CreatedBy,CreatedDate,ModifiedBy,ModifiedDate,CoverPageTemplateNewMappingFlag,RecalculationFlag,ConclusionMd5,SubReportReviseFlag,SoftcopyDeliveryDate,
        ActualReportNo,RootReportNo,SignatureLanguage,CountryOfDestination,ReportVersion,LabCode,ReportFlag
	</sql>

    <sql id="ReportType_Column_List" >
        ID, Description, `Position`, AmendRemarkRule, ActiveIndicator, languageId,CreatedBy, CreatedDate, ModifiedBy, ModifiedDate
    </sql>

    <insert id="saveReportInfo" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.ReportInfoPO" >
        insert into tb_report (
            ID, OrderNo, ReportNo, ParentReportNo, LabId,LabCode,ReportDueDate, ReportStatus, CoverPageTemplateID,
            CoverPageTemplateName, CoverPageTemplatePath, CoverPageTemplateNewMappingFlag, TemplateID,
            ReportTypeID, RequestID, RequestSentDate, RequestFinishedDate, ApproverBy, Approver,
            ApproverDate, CustomerCode, CustomerGroupCode, CertificateId, CertificateFileCloudKey, CertificateName,
            AmendRemark, ActiveIndicator, CreatedBy, CreatedDate, ModifiedBy, ModifiedDate, RecalculationFlag,
            ConclusionMd5,LogoAliyunID,ActualReportNo,RootReportNo,OperationType,ExternalReportNo,SealFlag,CountryOfDestination,
            TestMatrixMergeMode,ReportVersion,TestResultStatus, ReviewerBy, Reviewer,EditorBy,Editor,EntryMode
        )
        values (
            #{ID,jdbcType=VARCHAR}, #{orderNo,jdbcType=VARCHAR}, #{reportNo,jdbcType=VARCHAR},
            #{parentReportNo,jdbcType=VARCHAR},#{labId,jdbcType=INTEGER},#{labCode,jdbcType=VARCHAR},#{reportDueDate,jdbcType=TIMESTAMP}, #{reportStatus,jdbcType=INTEGER},
            #{coverPageTemplateID,jdbcType=VARCHAR}, #{coverPageTemplateName,jdbcType=VARCHAR},
            #{coverPageTemplatePath,jdbcType=VARCHAR}, #{coverPageTemplateNewMappingFlag,jdbcType=BIT},
            #{templateID,jdbcType=VARCHAR}, #{reportTypeID,jdbcType=VARCHAR}, #{requestID,jdbcType=VARCHAR},
            #{requestSentDate,jdbcType=TIMESTAMP}, #{requestFinishedDate,jdbcType=TIMESTAMP},
            #{approverBy,jdbcType=VARCHAR}, #{approver,jdbcType=VARCHAR}, #{approverDate,jdbcType=TIMESTAMP},
            #{customerCode,jdbcType=VARCHAR}, #{customerGroupCode,jdbcType=VARCHAR}, #{certificateId,jdbcType=VARCHAR}, #{certificateFileCloudKey,jdbcType=VARCHAR},
            #{certificateName,jdbcType=VARCHAR}, #{amendRemark,jdbcType=VARCHAR}, #{activeIndicator,jdbcType=BIT},
            #{createdBy,jdbcType=VARCHAR}, #{createdDate,jdbcType=TIMESTAMP}, #{modifiedBy,jdbcType=VARCHAR},
            #{modifiedDate,jdbcType=TIMESTAMP}, #{recalculationFlag,jdbcType=INTEGER}, #{conclusionMd5,jdbcType=VARCHAR},
            #{logoAliyunID,jdbcType=VARCHAR},#{actualReportNo,jdbcType=VARCHAR},#{rootReportNo,jdbcType=VARCHAR},#{operationType,jdbcType=INTEGER}, #{externalReportNo,jdbcType=VARCHAR},#{sealFlag,jdbcType=INTEGER},
            #{countryOfDestination,jdbcType=VARCHAR},#{testMatrixMergeMode,jdbcType=VARCHAR},#{reportVersion,jdbcType=INTEGER},#{testResultStatus,jdbcType=VARCHAR},
            #{reviewerBy,jdbcType=VARCHAR},#{reviewer,jdbcType=VARCHAR},#{editorBy,jdbcType=VARCHAR},#{editor,jdbcType=VARCHAR},#{entryMode,jdbcType=VARCHAR}
        )
        ON DUPLICATE KEY UPDATE
            ReportDueDate = VALUES(reportDueDate),
            CustomerCode = VALUES(customerCode),
            CustomerGroupCode = VALUES(customerGroupCode),
            ModifiedBy = VALUES(modifiedBy),
            ModifiedDate = VALUES(modifiedDate)
    </insert>

    <update id="updateReportStatus" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.ReportInfoPO">
        UPDATE tb_report
        SET ReportStatus = #{reportStatus},
            ModifiedDate = #{modifiedDate},
            ModifiedBy = #{modifiedBy}
        WHERE ReportNo = #{reportNo}
    </update>

    <update id="updateReportStatusInfo" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.ReportInfoPO">
        UPDATE tb_report
        SET ReportNo = #{reportNo},
            ReportStatus = #{reportStatus},
            ModifiedDate = #{modifiedDate},
            ModifiedBy = #{modifiedBy}
        WHERE ID = #{ID}
    </update>




    <update id="updateReportRecalculationFlag" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.ReportInfoPO">
        UPDATE tb_report
        SET RecalculationFlag = (CASE WHEN RecalculationFlag = 1 THEN #{recalculationFlag} ELSE RecalculationFlag END),
            ModifiedDate = #{modifiedDate},
            ModifiedBy = #{modifiedBy}
        WHERE ReportNo = #{reportNo}
    </update>

    <update id="updateApproverReportStatus" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.ReportInfoPO">
        UPDATE tb_report
        <set>
            ReportStatus = #{reportStatus},
            ApproverBy = #{approverBy},
            Approver = #{approver},
            ApproverDate = #{approverDate},
            ModifiedBy = #{modifiedBy},
            ModifiedDate = #{modifiedDate}
        </set>
        WHERE ReportNo = #{reportNo}
    </update>

    <select id="getReportInfoByReportNo" resultMap="BaseResultMap">
        SELECT * FROM tb_report WHERE ReportNo = #{reportNo} LIMIT 1
    </select>
    <select id="getReportInfoByParentReportNo" resultMap="BaseResultMap">
        SELECT * FROM tb_report WHERE ParentReportNo = #{parentReportNo} LIMIT 1
    </select>

    <select id="getReportInfoByReportId" resultMap="BaseResultMap">
        SELECT * FROM tb_report WHERE Id = #{reportId} LIMIT 1
    </select>


    <select id="getReportTypeByDesc" resultType="com.sgs.otsnotes.dbstorages.mybatis.model.ReportTypePO" parameterType="java.lang.String">
        SELECT
          <include refid="ReportType_Column_List" />
        FROM tb_report_type
        WHERE Description=#{desc}
        order by languageId asc
        LIMIT 1
    </select>

    <select id="getReportTypeByDescAndLanguage" resultType="com.sgs.otsnotes.dbstorages.mybatis.model.ReportTypePO">
        SELECT
        <include refid="ReportType_Column_List" />
        FROM tb_report_type
        WHERE Description=#{desc} and LanguageID = #{languageId}
        order by languageId asc
        LIMIT 1
    </select>

    <select id="getReportByOrderNo" resultMap="BaseResultMap">
        SELECT * FROM tb_report
          WHERE OrderNo = #{orderNo}
        ORDER BY CreatedDate DESC
        LIMIT 1
    </select>

    <select id="queryReportByOrderNo" resultType="com.sgs.otsnotes.facade.model.dto.GpnQuotationReportDTO">
        SELECT  report.ReportNo reportNo, report.ID reportId, report.OrderNo orderNo,report.ReportStatus reportStatus,
                report.ReportDueDate reportDueDate,report.ReportFlag reportFlag
        FROM tb_report report
        WHERE OrderNo = #{orderNo}
        ORDER BY  ReportNo
    </select>

    <select id="getReportStatusByReportId" resultType="java.lang.Integer" parameterType="java.lang.String">
        SELECT ReportStatus FROM tb_report
        WHERE Id = #{reportId}
        LIMIT 1
    </select>


    <select id="getReportInfoByOrderNo" resultType="com.sgs.otsnotes.facade.model.dto.EmReportDTO">
        SELECT ReportNo,ReportStatus FROM tb_report WHERE OrderNo = #{orderNo} AND ReportStatus NOT IN (202,205) ORDER BY CreatedDate DESC LIMIT 1
    </select>

    <insert id="batchSaveReportMatrixRelInfo" parameterType="java.util.List">
        INSERT INTO tre_report_matrix_relationship
        (
            ID, ReportID, TestMatrixID,
            CreatedBy, CreatedDate,
            ModifiedBy,ModifiedDate
        )
        VALUES
        <foreach collection="rels" index="index" item="rel" separator=",">
        (
            #{rel.ID,jdbcType=VARCHAR}, #{rel.reportID,jdbcType=VARCHAR},#{rel.testMatrixID,jdbcType=VARCHAR},
            #{rel.createdBy,jdbcType=VARCHAR}, #{rel.createdDate,jdbcType=TIMESTAMP}, #{rel.modifiedBy,jdbcType=VARCHAR},
            #{rel.modifiedDate,jdbcType=TIMESTAMP}
        )
        </foreach>
        ON DUPLICATE KEY UPDATE
            ReportID = VALUES(reportID),
            TestMatrixID = VALUES(testMatrixID),
            ModifiedBy = VALUES(modifiedBy),
            ModifiedDate = VALUES(modifiedDate)
    </insert>


    <delete id="deleteReport">
        delete from tb_report where id = #{reportId};
        delete from tb_report_file where ReportID = #{reportId};
        delete from tre_report_matrix_relationship where ReportID = #{reportId};
        delete from tb_conclusion where ReportID = #{reportId};
    </delete>

    <update id="updateRecalculationFlagByReportId"  parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.ReportInfoPO">
        update tb_report set recalculationFlag = #{recalculationFlag},  ModifiedBy = #{modifiedBy},
            ModifiedDate = #{modifiedDate} where id = #{ID}
    </update>

    <update id="deleteMd5ByReportId" parameterType="string">
		update tb_report set ConclusionMd5 = null where id = #{reportId}
	</update>

    <update id="updateConclusionMd5" parameterType="com.sgs.otsnotes.facade.model.info.conclusion.ConclusionMd5Info" >
        UPDATE tb_report
        SET ConclusionMd5 = #{conclusionMd5},
            recalculationFlag = 1,
            ModifiedDate =#{modifiedDate},
            ModifiedBy = #{userName}
        WHERE id = #{reportId}
    </update>

    <update id="cancelReportByOrderNo"
            parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.ReportInfoPO">
        update tb_report
        set
        ModifiedBy=#{modifiedBy},
        ModifiedDate=#{modifiedDate},
        ActiveIndicator=0,
        ReportStatus=202
        where orderNo =#{orderNo}
    </update>


    <select id="getGenerateReportByReportId" parameterType="java.lang.String"
            resultType="com.sgs.otsnotes.facade.model.dto.GpnGenerateReportDTO">
        SELECT
        ord.ID orderId,
        ord.OrderNo orderNo,
        ord.OrderStatus orderStatus,
        report.ReportStatus reportStatus,
        report.ReportNo reportNo,
        report.ID reportId,
        report.Remark as remark,
        report.ApproverBy approvedBy,
        report.ApproverDate approvedDate,
        report.ReportDueDate reportedDueDate,
        report.ReportFlag reportFlag,
        '' as conclusion,
        report.AwbNo as awbNo,
        report.CreatedDate createdDate,
        report.CertificateId as certificateId,
        report.CertificateFileCloudKey as accreditationLogoPath,
        report.ExternalReportNo as externalReportNo,
        deliveryHis.softcopy_delivered_date as softCopyDeliveredDate,
        deliveryHis.softcopy_delivered_by as softcopyDeliveredBy,
        deliveryHis.hardcopy_delivered_date as hardCopyDeliveredDate,
        deliveryHis.hardcopy_delivered_by as hardCopyDeliveredBy,
        templateCn.template_setting_id as otsTemplateCnId,
        templateEn.template_setting_id as otsTemplateEnId,
        templateCn.qrcode_path as qrcodeCnPath,
        templateEn.qrcode_path as qrcodeEnPath,
        templateEn.template_type_id as otsTemplateTypeId,
        delay.id as delayId,
        delay.delay_type as delayType,
        delay.delay_type_code as delayTypeCode,
        delay.delay_subtype_code as delaySubtypeCode,
        delay.delay_reason as delayReason
        FROM
        tb_report report
        LEFT JOIN tb_general_order_instance ord ON report.OrderNo = ord.OrderNo
        left join tb_report_delivery_history deliveryHis on report.ID = deliveryHis.report_id and deliveryHis.delivery_type='Report'
        left join tb_report_template templateEn on report.ID = templateEn.report_id and templateEn.language_id in (1,3)
        left join tb_report_template templateCn on report.ID = templateCn.report_id and templateCn.language_id =2
        left join tb_delay delay on delay.object_id = report.ID and delay.delay_type = 'Report'
        where report.id  = #{reportId} limit 1
    </select>

    <select id="queryReportDetail" parameterType="java.lang.String"
            resultType="com.sgs.otsnotes.facade.model.dto.GpnOrderReportDetailDTO">
            SELECT
                ord.ID orderId,
                ord.OrderNo orderNo,
                ord.OrderStatus orderStatus,
                report.CoverPageTemplateName coverPageTemplateName,
                report.CoverPageTemplatePath coverPageTemplatePath,
                report.ReportStatus reportStatus,
                report.ReportNo reportNo,
                report.ID reportId,
                report.Remark as remark,
                report.ApproverBy approvedBy,
                report.ApproverDate approvedDate,
                report.ReportDueDate reportedDueDate,
                report.AwbNo as awbNo,
                report.CreatedDate createdDate,
                report.WorkFlow workFlow,
                report CertificateId as certificateId,
                report.CertificateFileCloudKey as accreditationLogoPath,
                deliveryHis.softcopy_delivered_date as softCopyDeliveredDate,
                deliveryHis.softcopy_delivered_by as softcopyDeliveredBy,
                deliveryHis.hardcopy_delivered_date as hardCopyDeliveredDate,
                deliveryHis.hardcopy_delivered_by as hardCopyDeliveredBy,
                templateCn.template_setting_id as otsTemplateCnId,
                templateEn.template_setting_id as otsTemplateEnId,
                templateCn.qrcode_path as qrcodeCnPath,
                templateEn.qrcode_path as qrcodeEnPath,
                templateEn.template_type_id as otsTemplateTypeId,
                delay.id as delayId,
                delay.delay_type as delayType,
                delay.delay_type_code as delayTypeCode,
                delay.delay_subtype_code as delaySubtypeCode,
                delay.delay_reason as delayReason,
                conclusion.ConclusionID as  conclusionId,
                conclusionList.Description as conclusion,
                report.ReportFlag as reportFlag
                FROM
                tb_report report
                LEFT JOIN tb_general_order_instance ord ON report.OrderNo = ord.OrderNo
                left join tb_report_delivery_history deliveryHis on report.ID = deliveryHis.report_id and deliveryHis.delivery_type='Report'
                left join tb_report_template templateEn on report.ID = templateEn.report_id and templateEn.language_id  in (1,3)
                left join tb_report_template templateCn on report.ID = templateCn.report_id and templateCn.language_id =2
                left join tb_delay delay on delay.object_id = report.ID and delay.delay_type = 'Report'
                left join tb_conclusion conclusion on conclusion.ReportID = report.id and conclusion.ConclusionLevelID = 603
                left join tb_conclusion_list conclusionList on conclusionList.ID = conclusion.ConclusionID
                where 1 = 1
                    <if test="reportNo != null and reportNo != ''">
                       and (ord.OrderNo = #{orderNo} or report.ReportNo like concat(#{reportNo},'%'))
                    </if>
                    <if test="reportNo == null or reportNo == ''">
                      AND  ord.OrderNo = #{orderNo}
                    </if>
            GROUP BY report.ID
             order by  report.ReportNo
    </select>

    <select id="queryReportDetailByReportId" parameterType="java.lang.String"
            resultType="com.sgs.otsnotes.facade.model.dto.GpnOrderReportDetailDTO">
        SELECT
        ord.ID orderId,
        ord.OrderNo orderNo,
        ord.OrderStatus orderStatus,
        ord.LabCode labCode,
        tgo.BUID buId,
        report.CoverPageTemplateName coverPageTemplateName,
        report.CoverPageTemplatePath coverPageTemplatePath,
        report.ReportStatus reportStatus,
--         report.ApproveStatus AS approveStatus,
        report.ReportNo reportNo,
        report.ID reportId,
        report.WorkFlow workFlow,
        report.Remark as remark,
        report.ApproverBy approvedBy,
        report.Approver approver,
        report.EditorBy editorBy,
        report.Editor editor,
        report.ReviewerBy reviewerBy,
        report.Reviewer reviewer,
        report.ApproverDate approvedDate,
        report.ReportDueDate reportedDueDate,
        report.AwbNo as awbNo,
        report.CreatedDate createdDate,
        report.CertificateFileCloudKey as accreditationLogoPath,
        report.CertificateId as certificateId,
        report.reportFlag,
        report.WorkFlow workFlow,
        report.CertificateName certificateName,
        report.OperationType operationType,
        report.ParentReportNo parentReportNo,
        report.ExternalReportNo referenceReportNo,
        report.SignatureLanguage signatureLanguage,
        report.SealFlag reportSealFlag,
        report.CountryOfDestination as countryOfDestination,
        report.actualReportNo as actualReportNo,
        report.rootReportNo as rootReportNo,
        report.testingType as testingType,
        report.photoRemark as photoRemark,
        report.TemplateID as templateId,
        report.EntryMode as entryMode,
        report.ExternalReportNo as referenceReportNo,
        re.lab_section_id as labSectionIds,
        re.notes as reportNotes,
        deliveryHis.softcopy_delivered_date as softCopyDeliveredDate,
        deliveryHis.softcopy_delivered_by as softcopyDeliveredBy,
        deliveryHis.hardcopy_delivered_date as hardCopyDeliveredDate,
        deliveryHis.hardcopy_delivered_by as hardCopyDeliveredBy,
        templateCn.template_setting_id as otsTemplateCnId,
        templateEn.template_setting_id as otsTemplateEnId,
        templateCn.qrcode_path as qrcodeCnPath,
        templateEn.qrcode_path as qrcodeEnPath,
        templateEn.template_type_id as otsTemplateTypeId,
        delay.id as delayId,
        delay.delay_type as delayType,
        delay.delay_type_code as delayTypeCode,
        delay.delay_subtype_code as delaySubtypeCode,
        delay.delay_reason as delayReason,
        delay.delay_caused_by as delayCausedBy,
        delay.delay_responsible_person as delayResponsiblePerson,
        conclusion.ConclusionID as  conclusionId,
        conclusion.ConclusionSettingID as  conclusionSettingID,
        conclusionList.Description as conclusion,
        subcontractFrom.CustomerID as subcontractFromLabCode
        FROM
        tb_report report
        LEFT JOIN tb_general_order_instance ord ON report.OrderNo = ord.OrderNo
        LEFT JOIN tb_report_ext re ON re.report_id = report.id
        LEFT JOIN gpo.tb_general_order tgo ON tgo.OrderNo = ord.OrderNo
        left join tb_report_delivery_history deliveryHis on report.ID = deliveryHis.report_id and deliveryHis.delivery_type='Report'
        left join tb_report_template templateEn on report.ID = templateEn.report_id and templateEn.language_id  in (1,3)
        left join tb_report_template templateCn on report.ID = templateCn.report_id and templateCn.language_id =2
        left join tb_delay delay on delay.object_id = report.ID and delay.delay_type = 'Report'
        left join tb_conclusion conclusion on conclusion.ReportID = report.id and conclusion.ConclusionLevelID = 603
        left join tb_conclusion_list conclusionList on conclusionList.ID = conclusion.ConclusionID
        LEFT JOIN gpo.tb_customer_instance subcontractFrom ON subcontractFrom.GeneralOrderID = tgo.id
            AND subcontractFrom.CustomerUsage = 'subcontractFrom'
        where report.ID = #{reportId} limit 1
    </select>

    <sql id="ReportQueryPage_Column_List" >
        tr.ID AS reportId,
        tr.AwbNo AS awbNo,
        tr.ReportNo reportNo,
        tr.ReportStatus AS reportStatus,
        tr.ApproverBy AS approvedBy,
        tr.ApproverDate AS approvedDate,
        tr.ReportDueDate AS reportedDueDate,
        tr.CreatedDate AS createdDate,
        tr.WorkFlow AS workFlow,
--         tr.NeedReview AS needReview,
        tr.SoftcopyDeliveryDate softCopyDeliveredDate,
        tr.SubReportReviseFlag as subReportReviseFlag,
        tr.NeedToAmendFlag as needToAmendFlag,
        tr.CountryOfDestination as countryOfDestination,
        goi.id AS orderId,
        goi.OrderNo AS orderNo,
        tgo.OrderStatus AS orderStatus,
        goi.LabCode AS labCode,

        applicant.CustomerNameEn AS applicantEnName,
        applicant.CustomerNameCN AS applicantCnName,
        buyer.CustomerNameEn AS buyerEnName,
        buyer.CustomerNameCN AS buyerCnName,
        applicant.BossNumber AS applicantBossNumber,
        buyer.BossNumber AS buyerBossNumber,
        subcontractFrom.CustomerID as subcontractFromLabCode,
        tso.CSName AS csName,
        tso.customerRefNo as customerRefNo,
        goi.SuffixNum AS suffixNum,
        goi.TechnicalSupporter AS technicalSupporter,
        tr.actualReportNo as actualReportNo,
        tr.rootReportNo as rootReportNo
    </sql>


    <select id="queryReportList" parameterType="com.sgs.otsnotes.facade.model.req.report.ReportQueryPageRequest"
            resultType="com.sgs.otsnotes.facade.model.dto.GpnOrderReportDTO">
        SELECT
            re0.*,
            tcl.OverallDescription as conclusion,
            tcc.ConclusionSettingID conclusionSettingId,
            draftDeliver.softcopy_delivered_date as draftDeliverDate
--             rdh.softcopy_delivered_date as softCopyDeliveredDate,
--             rdh.hardcopy_delivered_date as hardCopyDeliveredDate
        FROM
        (
            (
                SELECT
                'owner' fromDB,
                tr.reportFlag as reportType,
                tr.reportFlag,
                goi.OrderNo AS trueOrderNo,
                tsr.SubReportNo AS subReportNo,
--                 tr.reportStatus AS approveStatus,
                '' AS subContractStatus,
                '' AS subContractLabCode,
                '' AS subContractLabName,
                <include refid="ReportQueryPage_Column_List" />
                FROM tb_general_order_instance goi
                LEFT JOIN gpo.tb_general_order tgo ON tgo.OrderNo = goi.OrderNo
                LEFT JOIN gpo.tb_customer_instance applicant ON tgo.`ID` = applicant.`GeneralOrderID` AND applicant.`CustomerUsage` = 'Applicant'
                LEFT JOIN gpo.tb_customer_instance buyer ON tgo.`ID` = buyer.`GeneralOrderID` AND buyer.`CustomerUsage` = 'Buyer'
                LEFT JOIN gpo.tb_customer_instance subcontractFrom ON tgo.`ID` = subcontractFrom.`GeneralOrderID` AND subcontractFrom.`CustomerUsage` = 'subcontractFrom'
                LEFT JOIN gpo.tb_sl_order tso ON tso.GeneralOrderID = tgo.`ID`
                LEFT JOIN gpo.tb_test_request requirement ON requirement.GeneralOrderID = tgo.id
                LEFT JOIN tb_sub_contract tsc ON goi.OrderNo = tsc.OrderNo
                INNER JOIN tb_report tr ON goi.OrderNo = tr.OrderNo
                LEFT JOIN tb_sub_report tsr ON tr.ReportNo = tsr.ReportNo
                WHERE 1=1
                <if test="labCode != null and labCode != ''">
                    AND goi.LabCode = #{labCode}
                </if>
                <if test="reportStatus != null and reportStatus != ''">
                    AND tr.reportStatus = #{reportStatus}
                </if>
                <if test="reportNoList != null and reportNoList.size() != 0 ">
                    AND tr.reportNo IN
                    <foreach item="item" index="index" collection="reportNoList" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="reportIdList != null and reportIdList.size() != 0 ">
                    AND tr.id IN
                    <foreach item="item" index="index" collection="reportIdList" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="reportNo != null and reportNo != ''">
                    AND tr.reportNo LIKE CONCAT('%',#{reportNo},'%')
                </if>
                <if test="orderId != null and orderId != ''">
                    AND goi.id = #{orderId}
                </if>
            )
            UNION
            (
                SELECT
                'subcontract' fromDB,
                2 as reportType,
                2 as reportFlag,
                tss.ExternalNo AS trueOrderNo,
                tr.ReportNo AS subReportNo,
--                 tr.ApproveStatus AS approveStatus,
                tsc.Status AS subContractStatus,
                tsc.SubContractLabCode AS subContractLabCode,
                tsc.SubContractLabName AS subContractLabName,
                <include refid="ReportQueryPage_Column_List" />
                FROM tb_general_order_instance goi
                LEFT JOIN tb_sub_contract tsc ON goi.OrderNo = tsc.OrderNo
                INNER JOIN tb_subcontract_external_relationship tss ON tsc.SubContractNo = tss.SubContractNo
                INNER JOIN tb_report tr ON tss.ExternalNo = tr.OrderNo
                LEFT JOIN gpo.tb_general_order tgo ON tgo.OrderNo = goi.OrderNo
                LEFT JOIN gpo.tb_customer_instance applicant ON tgo.`ID` = applicant.`GeneralOrderID` AND applicant.`CustomerUsage` = 'Applicant'
                LEFT JOIN gpo.tb_customer_instance buyer ON tgo.`ID` = buyer.`GeneralOrderID` AND buyer.`CustomerUsage` = 'Buyer'
                LEFT JOIN gpo.tb_customer_instance subcontractFrom ON tgo.`ID` = subcontractFrom.`GeneralOrderID` AND subcontractFrom.`CustomerUsage` = 'subcontractFrom'
                LEFT JOIN gpo.tb_sl_order tso ON tso.GeneralOrderID = tgo.`ID`
                LEFT JOIN gpo.tb_test_request requirement ON requirement.GeneralOrderID = tgo.id
                WHERE tr.ReportStatus != 205
                AND (
                    requirement.ReportRequirement = 2
                    OR (
                        (
                        requirement.ReportRequirement IS NULL
                        OR requirement.ReportRequirement = 1
                        )
                        AND tr.ReportStatus != 208
                    )
                )
                <choose>
                    <when  test="reportStatus != null and reportStatus != ''">
                        AND  tr.ReportStatus = #{reportStatus}
                    </when>
                    <otherwise>
                        AND tr.ReportStatus is not null
                    </otherwise>
                </choose>
                <if test="labCode != null and labCode != ''">
                    AND goi.LabCode = #{labCode}
                </if>
            <if test="reportNoList != null and reportNoList.size() != 0 ">
                AND tr.reportNo IN
                <foreach item="item" index="index" collection="reportNoList" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="reportIdList != null and reportIdList.size() != 0 ">
                AND tr.id IN
                <foreach item="item" index="index" collection="reportIdList" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="reportNo != null and reportNo != ''">
                AND tr.reportNo LIKE CONCAT('%',#{reportNo},'%')
            </if>
            <if test="orderId != null and orderId != ''">
                AND goi.id = #{orderId}
            </if>
        )
        ) re0
--         LEFT JOIN tb_order_cross_lab_rel oclr ON oclr.orderNo = re0.orderNo
        LEFT JOIN tb_conclusion tcc ON tcc.ReportID = re0.reportId and tcc.ConclusionLevelID = 603
        LEFT JOIN tb_customer_conclusion_reference tcl ON tcl.ID = tcc.ConclusionSettingID
--         LEFT JOIN tb_report_delivery_history rdh ON rdh.report_id = re0.reportId and rdh.delivery_type = 'Report'
        LEFT JOIN tb_report_delivery_history draftDeliver ON draftDeliver.report_id = re0.reportId and draftDeliver.delivery_type = 'DraftReport'
        WHERE 1 = 1
            <if test="labCode != null and labCode != ''">
                AND re0.labCode = #{labCode}
            </if>

<!--        <if test="blockTop == '' or blockTop == null">-->
<!--            AND (re0.labCode = #{labCode} OR (oclr.toLab = #{labCode} AND oclr.execType = 1))-->
<!--        </if>-->
<!--        <if test="blockTop == '1'">-->
<!--            AND re0.labCode = #{labCode} AND oclr.id IS NULL-->
<!--        </if>-->
        <choose>
            <when test="orderNoList != null and orderNoList.size() != 0">
                AND  re0.orderNo  IN (
                <foreach collection="orderNoList" item="item" index="index" separator =",">
                    #{item}
                </foreach >
                )
            </when>
            <when test="orderNo != null and orderNo != ''">
                AND ( re0.orderNo = #{orderNo} or re0.SuffixNum = #{orderNo})
            </when>
            <otherwise>
            </otherwise>
        </choose>

        <if test='deliverDraft!= null and deliverDraft != ""'>
            <choose>
                <when test='deliverDraft == "Y".toString()'>
                    AND draftDeliver.id is not null
                </when>
                <when test='deliverDraft == "N".toString()'>
                    AND draftDeliver.id is  null
                </when>
                <otherwise>
                </otherwise>
            </choose>
        </if>

        <if test="applicant != null">
            <choose>
                <when test="applicant.customerNo != null and applicant.customerNo != '' ">
                    AND re0.applicantBossNumber = #{applicant.customerNo}
                </when>
                <when test="applicant.customerName != null and applicant.customerName != '' ">
                    AND (re0.applicantEnName LIKE CONCAT('%',#{applicant.customerName},'%') OR re0.applicantCnName LIKE CONCAT('%',#{applicant.customerName},'%'))
                </when>
            </choose>
        </if>


        <if test="draftReportRequired != null and draftReportRequired != ''">
            AND requirement.DraftReportRequired = #{draftReportRequired}
        </if>
        <if test="buyer != null">
            <choose>
                <when test="buyer.customerNo != null and buyer.customerNo != '' ">
                    AND re0.buyerBossNumber = #{buyer.customerNo}
                </when>
                <when test="buyer.customerName != null and buyer.customerName != '' ">
                    AND (re0.buyerEnName LIKE CONCAT('%',#{buyer.customerName},'%') OR re0.buyerCnName LIKE CONCAT('%',#{buyer.customerName},'%'))
                </when>
            </choose>
        </if>

        <if test="orderStatus != null and orderStatus>0">
            AND re0.orderStatus = #{orderStatus}
        </if>

        <if test="subcontractFromLabCode != null and subcontractFromLabCode!= ''">
            AND re0.subcontractFromLabCode = #{subcontractFromLabCode}
        </if>

        <if test="customerRefNo != null and customerRefNo!= ''">
            AND re0.customerRefNo = #{customerRefNo}
        </if>
        <if test="approvedStartDate != null and approvedStartDate != ''">
            AND re0.approvedDate <![CDATA[  >=   ]]> #{approvedStartDate}
        </if>

        <if test="approvedEndDate != null and approvedEndDate != ''">
            AND re0.approvedDate <![CDATA[ <=  ]]> #{approvedEndDate}
        </if>

        <if test="createdStartDate != null and createdStartDate != ''">
            AND re0.createdDate <![CDATA[  >=   ]]> #{createdStartDate}
        </if>

        <if test="createdEndDate != null and createdEndDate != ''">
            AND re0.createdDate <![CDATA[ <=  ]]> #{createdEndDate}
        </if>

        <if test="expectedStartDueDate != null and expectedStartDueDate != ''">
            AND re0.reportedDueDate <![CDATA[  >=   ]]> #{expectedStartDueDate}
        </if>

        <if test="expectedEndDueDate != null and expectedEndDueDate != ''">
            AND re0.reportedDueDate <![CDATA[ <=  ]]> #{expectedEndDueDate}
        </if>


        <if test="draftDeliverStartDate != null and draftDeliverStartDate != ''">
            AND  draftDeliver.softcopy_delivered_date <![CDATA[  >=   ]]> #{draftDeliverStartDate}
        </if>

        <if test="draftDeliverEndDate != null and draftDeliverEndDate != ''">
            AND  draftDeliver.softcopy_delivered_date <![CDATA[ <=  ]]> #{draftDeliverEndDate}
        </if>

        <if test="readyForDeliver != null and readyForDeliver != ''">
            AND (re0.subContractStatus != 3 or re0.reportStatus = 203 or re0.reportStatus = 208)
        </if>
        <if test="subContractToLab != null and subContractToLab != ''">
            AND (re0.subContractLabCode = #{subContractToLab} OR re0.subContractLabName LIKE CONCAT('%',#{subContractToLab},'%'))
        </if>
        <if test="subContractLabCodeList != null and subContractLabCodeList.size() != 0 ">
            AND re0.subContractLabCode IN
            <foreach item="item" index="index" collection="subContractLabCodeList" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="reportType != null and reportType != '' and reportType > 0">
            AND re0.reportType = #{reportType}
        </if>
        <if test="responsibleCs != null and responsibleCs != ''">
            AND re0.csName LIKE CONCAT('%',#{responsibleCs},'%')
        </if>
        <if test="ts != null and ts != ''">
            AND re0.technicalSupporter LIKE CONCAT('%',#{ts},'%')
        </if>
        group by re0.reportId order by re0.orderNo desc,re0.reportNo asc
    </select>

    <sql id="queryReportPageColumnsNew">
        'owner' fromDB,
        tr.reportFlag as reportType,
        tr.reportFlag,
        goi.OrderNo AS trueOrderNo,
        oi.parent_order_no AS parentOrderNo,
        tsr.SubReportNo AS subReportNo,
        '' AS subContractStatus,
        '' AS subContractLabCode,
        '' AS subContractLabName,
        oi.to_test_flag as toTestFlag,
        tcl.Description as conclusion,
        tcc.ConclusionSettingID as conclusionSettingID,
        tcc.ConclusionID as  conclusionId,
        tr.ID AS reportId,
        tr.AwbNo AS awbNo,
        tr.ReportNo reportNo,
        tr.ReportStatus AS reportStatus,
        tr.ApproverBy AS approvedBy,
        tr.EditorBy AS editorBy,
        tr.ReviewerBy AS reviewerBy,
        tr.CertificateFileCloudKey as accreditationLogoPath,
        tr.CertificateId as certificateId,
        tr.CertificateName as certificateName,
        tr.ExternalReportNo as referenceReportNo,
        tr.CoverPageTemplateName as coverPageTemplateName,
        tr.CoverPageTemplatePath as coverPageTemplatePath,
        tr.ApproverDate AS approvedDate,
        tr.ReportDueDate AS reportedDueDate,
        tr.CreatedDate AS createdDate,
        tr.CreatedBy AS createdBy,
        tr.PhotoRemark as photoRemark,
        tr.WorkFlow AS workFlow,
        tr.SoftcopyDeliveryDate softCopyDeliveredDate,
        tr.SubReportReviseFlag as subReportReviseFlag,
        tr.SignatureLanguage as signatureLanguage,
        tr.CountryOfDestination as countryOfDestination,
        tr.Remark as remark,
        tr.TestingType as testingType,
        tr.testResultStatus as testResultStatus,
        IFNULL(tr.ReportTestLineLevel, 'TL') AS reportTestLineLevel,
        tr.EntryMode as entryMode,
        goi.id AS orderId,
        goi.OrderNo AS orderNo,
        oi.order_status AS orderStatus,
        goi.LabCode AS labCode,
        oi.applicant_name AS applicantName,
        oi.buyer_name AS buyerName,
        oi.applicant_no AS applicantBossNumber,
        oi.buyer_no AS buyerBossNumber,
        subcontractFrom.CustomerID as subcontractFromLabCode,
        oi.cs AS csName,
        oi.customer_reference_no as customerRefNo,
        oi.order_type orderType,
        goi.SuffixNum AS suffixNum,
        goi.TechnicalSupporter AS technicalSupporter,
        oi.operation_mode AS operationMode,
        oi.service_type as serviceLevel,
        concat_ws(',',trfRel.ExternalOrderNo) as subContractOrderNo,
        tr.actualReportNo as actualReportNo,
        tr.rootReportNo as rootReportNo,
        re.lab_section_id as labSectionId,
        IFNULL(re.test_completed_flag,0) as testCompletedFlag,
        draftDeliver.softcopy_delivered_date as draftDeliverDate
        <if test="needToAmendFlag != null and needToAmendFlag != ''">
            ,record.ApplyId as applyId,
            record.ReviseType as reviseType,
            record.Reason as reviseContent,
            record.ApplyTime as applyTime,
            record.ApplyBy as applyBy
        </if>
        <if test='reportToDoListType!=null and reportToDoListType != "" and reportToDoListType == "3".toString()'>
            ,'' AS subReportReview,
            null as hardCopyDeliveredDate
        </if>
        <if test='reportToDoListType!=null and reportToDoListType != "" and reportToDoListType == "2".toString()'>
            ,re.subcontract_tl_status as subcontractSection
        </if>
        <if test='reportToDoListType != null and reportToDoListType != "" and reportToDoListType == "5".toString()'>
            ,oolConfirm.operator as reportConfirmedBy
            ,oolConfirm.operate_date as reportConfirmedDate
            ,oolApprove.operator as reportApprovedBy
        </if>
    </sql>




    <sql id="queryReportPageTablesNew">
        tb_report tr
        LEFT JOIN tb_report_ext re on re.report_id = tr.id
        INNER JOIN tb_order_index oi on oi.order_no = tr.OrderNo
        INNER JOIN tb_general_order_instance goi on goi.OrderNo = tr.OrderNo
        LEFT JOIN gpo.tb_order_trf_relationship trfRel ON trfRel.OrderId = oi.order_id
        LEFT JOIN gpo.tb_customer_instance subcontractFrom ON subcontractFrom.GeneralOrderID = oi.order_id
        AND subcontractFrom.CustomerUsage = 'subcontractFrom'
        LEFT JOIN tb_sub_contract tsc ON goi.OrderNo = tsc.OrderNo
        LEFT JOIN tb_sub_report tsr ON tr.ReportNo = tsr.ReportNo
        LEFT JOIN tb_conclusion tcc ON tcc.ReportID = tr.ID
        AND tcc.ConclusionLevelID = 603
        LEFT JOIN tb_conclusion_list tcl ON tcl.ID = tcc.ConclusionID
        LEFT JOIN tb_report_delivery_history draftDeliver ON draftDeliver.report_id = tr.ID AND draftDeliver.delivery_type = 'DraftReport'
        <if test="(certificateTypeList != null and certificateTypeList.size() >0) or (certificateNo != null and certificateNo != '')">
            left join tb_report_certificate trc on trc.report_id = tr.id and trc.status = 1
        </if>
        <if test="needToAmendFlag != null and needToAmendFlag != ''">
            INNER JOIN tb_report_rework_apply_records record ON tr.ID = record.ReportID
            AND record.ApplyStatus = 4
        </if>
        <if test='pendingFlag != null and pendingFlag != ""'>
            LEFT JOIN gpn.tb_status_log slog on slog.reportId = tr.id
        </if>
        <if test="reportToDoListType!=null and reportToDoListType != '' and (reportToDoListType == '6'.toString() or reportToDoListType == '2'.toString() or reportToDoListType == '8'.toString())">
            LEFT JOIN tb_report_file rf on rf.ReportID = tr.id AND rf.ReportFileType =  1505
        </if>
        <if test="reportToDoListType!=null and reportToDoListType != '' and (reportToDoListType == '5'.toString())">
            LEFT JOIN tb_object_operation_log oolConfirm on oolConfirm.object_no = tr.ReportNo and oolConfirm.object = 'Report' and oolConfirm.action = 'confirm'
            LEFT JOIN tb_object_operation_log oolApprove on oolApprove.object_no = tr.ReportNo and oolApprove.object = 'Report' and oolApprove.action = 'approve'
        </if>
    </sql>




    <sql id="queryReportPageWhereNew">
        <where>
            <if test="shareLab">
                AND (MATCH (oi.host_lab_id,oi.top_lab_id) AGAINST (CONCAT('lab',#{labId}) IN BOOLEAN Mode))
            </if>
            <if test="!shareLab">
                AND tr.labId = #{labId}
            </if>
            <if test='topsToLabId != null and topsToLabId != ""'>
                AND oi.top_lab_id = CONCAT('lab',#{topsToLabId})
            </if>
            <if test='topsFromLabId != null and topsFromLabId != ""'>
                AND oi.host_lab_id = CONCAT('lab',#{topsFromLabId})
            </if>
            <if test="createdStartDate != null and createdStartDate != ''">
                AND tr.CreatedDate <![CDATA[  >=   ]]> #{createdStartDate}
            </if>
            <if test="createdEndDate != null and createdEndDate != ''">
                AND tr.CreatedDate <![CDATA[ <=  ]]> #{createdEndDate}
            </if>
            <choose>
                <when test="orderNoList != null and orderNoList.size() != 0">
                    AND  oi.order_no  IN (
                    <foreach collection="orderNoList" item="item" index="index" separator =",">
                        #{item}
                    </foreach >
                    )
                </when>
                <when test="orderNo != null and orderNo != ''">
                    AND oi.order_no = #{orderNo}
                </when>
                <otherwise>
                </otherwise>
            </choose>
            <if test='hasReportRemark != null and hasReportRemark != "" and hasReportRemark == "Y".toString()'>
                AND (tr.Remark IS NOT NULL AND tr.Remark != '')
            </if>
            <if test='hasReportRemark != null and hasReportRemark != "" and hasReportRemark == "N".toString()'>
                AND (tr.Remark IS NULL OR tr.Remark = '')
            </if>

            <if test="labSectionIdList != null and labSectionIdList.size() >0">
                <choose>
                    <when test="labSectionExactQueryFlag != null and labSectionExactQueryFlag == true and labSectionIds != null and labSectionIds != ''">
                        AND re.lab_section_id = #{labSectionIds}
                    </when>
                    <otherwise>
                        AND re.lab_section_id IN
                        <foreach item="item" index="index" collection="labSectionIdList" open="(" separator="," close=")">
                            #{item}
                        </foreach>
                    </otherwise>
                </choose>

            </if>
            <if test="certificateTypeList != null and certificateTypeList.size() >0">
                and trc.certificate_type in
                <foreach item="item" index="index" collection="certificateTypeList" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="testingType != null and testingType.size() >0">
                and tr.testingType in
                <foreach item="item" index="index" collection="testingType" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test='certificateNo != null and certificateNo != ""'>
                AND trc.certificate_no like CONCAT('%',#{certificateNo},'%')
            </if>
            <if test='pendingFlag != null and pendingFlag != "" and pendingFlag == "Y".toString()'>
                AND slog.Id IS NOT NULL
            </if>
            <if test='testCompletedFlag != null and testCompletedFlag != ""'>
                <choose>
                    <when test='testCompletedFlag == "Y".toString()'>
                        AND re.test_completed_flag = 1
                    </when>
                    <when test='testCompletedFlag == "N".toString()'>
                        AND IFNULL(re.test_completed_flag,0) = 0
                    </when>
                </choose>
            </if>
            <if test='pendingFlag != null and pendingFlag != "" and pendingFlag == "N".toString()'>
                AND slog.Id IS NULL
            </if>
            <if test="accreditationList != null and accreditationList.size() != 0 ">
                AND tr.certificateId IN
                <foreach item="item" index="index" collection="accreditationList" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="testResultStatus != null and testResultStatus!=''">
                and tr.testResultStatus = #{testResultStatus}
            </if>
            <if test='readyToDelivery != null and readyToDelivery != "" and readyToDelivery == "Y".toString()'>
                AND (oi.enable_delivery_flag > 0 or oi.order_type in('IDB' ,'IDN'))
            </if>
            <if test='readyToDelivery != null and readyToDelivery != "" and readyToDelivery == "N".toString()'>
                AND (oi.enable_delivery_flag = 0 AND oi.order_type != 'IDB' AND oi.order_type != 'IDN')
            </if>
            <if test='reportToDoListType!=null and reportToDoListType != "" and reportToDoListType == "3".toString()'>
                AND  ((tr.ReportStatus = 204 AND <![CDATA[ (tr.WorkFlow & 2) > 0]]> AND <![CDATA[ (tr.WorkFlow & 1) <= 0]]>) OR (tr.ReportStatus = 211 AND <![CDATA[ (tr.WorkFlow & 2) > 0]]>))
            </if>
            <if test='reportToDoListType!=null and reportToDoListType != "" and reportToDoListType == "4".toString()'>
                AND tr.ReportStatus = 203
                AND EXISTS (
                SELECT ID FROM tb_report_file trf WHERE trf.ReportFileType = 1504 AND trf.ReportID = tr.ID  AND oi.report_file_type = 1
                union
                SELECT ID FROM tb_report_file trf WHERE trf.ReportFileType = 1501 AND trf.ReportID = tr.ID  AND oi.report_file_type != 1
                )
            </if>
            <if test='reportToDoListType!=null and reportToDoListType != "" and reportToDoListType == "5".toString()'>
                AND (oi.enable_delivery_flag > 0 OR oi.order_type in ('IDB' ,'IDN'))
                AND (tr.reportStatus = 210 OR ((oi.need_draft_flag = 0 OR <![CDATA[ (tr.WorkFlow & 8) <= 0]]>) AND tr.reportStatus = 203))
            </if>
            <choose>
                <when test="queryTodoCount!= null and queryTodoCount ">
                    <if test='reportToDoListType!=null and reportToDoListType != "" and reportToDoListType != "4".toString() and reportToDoListType != "5".toString() and reportToDoListType != "7".toString() '>
                        <if test="reportStatusList != null and reportStatusList.size() != 0 ">
                            AND tr.reportStatus IN
                            <foreach item="item" index="index" collection="reportStatusList" open="(" separator="," close=")">
                                #{item}
                            </foreach>
                        </if>
                    </if>
                </when>
                <otherwise>
                    <if test="orderType != null and orderType != ''">
                        AND oi.order_type = #{orderType}
                    </if>

                    <if test="subContractFromList != null and subContractFromList.size() != 0 ">
                        AND subcontractFrom.CustomerID IN
                        <foreach item="item" index="index" collection="subContractFromList" open="(" separator="," close=")">
                            #{item}
                        </foreach>
                    </if>
                    <if test="reportType != null and reportType != ''">
                        AND tr.reportFlag = #{reportType}
                    </if>
                    <if test="reportStatusList != null and reportStatusList.size() != 0 ">
                        AND tr.reportStatus IN
                        <foreach item="item" index="index" collection="reportStatusList" open="(" separator="," close=")">
                            #{item}
                        </foreach>
                    </if>
                    <if test='deliverDraft!= null and deliverDraft != ""'>
                        <choose>
                            <when test='deliverDraft == "Y".toString()'>
                                AND draftDeliver.id is not null
                            </when>
                            <when test='deliverDraft == "N".toString()'>
                                AND draftDeliver.id is  null
                            </when>
                            <otherwise>
                            </otherwise>
                        </choose>
                    </if>

                    <choose>
                        <when test="applicant != null and applicant.customerNo != null and applicant.customerNo != '' ">
                            AND oi.applicant_no = #{applicant.customerNo}
                        </when>
                        <when test="applicant != null and applicant.customerName != null and applicant.customerName != '' ">
                            AND oi.applicant_name like concat( '%', #{applicant.customerName}, '%' )
                        </when>
                    </choose>
                    <choose>
                        <when test="buyer != null and buyer.customerNo != null and buyer.customerNo != '' ">
                            AND oi.buyer_no = #{buyer.customerNo}
                        </when>
                        <when test="buyer != null and buyer.customerName != null and buyer.customerName != '' ">
                            AND oi.buyer_name like concat( '%', #{buyer.customerName}, '%' )
                        </when>
                    </choose>
                    <if test="subcontractFromLabCode != null and subcontractFromLabCode!= ''">
                        AND subcontractFrom.CustomerId = #{subcontractFromLabCode}
                    </if>
                    <if test="customerRefNo != null and customerRefNo!= ''">
                        AND (oi.customer_reference_no = #{customerRefNo} or oi.parent_order_no = #{customerRefNo})
                    </if>
                    <if test="reportNo != null and reportNo != ''">
                        AND tr.ActualReportNo = #{reportNo}
                    </if>
                    <if test='crossLabSection != null and crossLabSection != "" and crossLabSection == "Y".toString()'>
                        AND locate(',',re.lab_section_id) > 0
                    </if>
                    <if test='crossLabSection != null and crossLabSection != "" and crossLabSection == "N".toString()'>
                        AND locate(',',re.lab_section_id) = 0
                    </if>
                    <if test="subcontractOrderNo != null and subcontractOrderNo != '' " >
                        and trfRel.ExternalOrderNo = #{subcontractOrderNo}
                    </if>
                    <if test="orderId != null and orderId != ''">
                        AND oi.order_id = #{orderId}
                    </if>
                    <if test="draftReportRequired != null and draftReportRequired != ''">
                        AND oi.need_draft_flag = #{draftReportRequired}
                    </if>
                    <if test="reportNoList != null and reportNoList.size() != 0 ">
                        AND tr.ActualReportNo IN
                        <foreach item="item" index="index" collection="reportNoList" open="(" separator="," close=")">
                            #{item}
                        </foreach>
                    </if>
                    <if test="orderStatusList != null and orderStatusList.size() != 0 ">
                        AND oi.order_status IN
                        <foreach item="item" index="index" collection="orderStatusList" open="(" separator="," close=")">
                            #{item}
                        </foreach>
                    </if>
                    <if test="orderStatus != null and orderStatus>0">
                        AND oi.order_status = #{orderStatus}
                    </if>
                    <if test="approvedStartDate != null and approvedStartDate != ''">
                        AND tr.ApproverDate <![CDATA[  >=   ]]> #{approvedStartDate}
                    </if>
                    <if test="approvedEndDate != null and approvedEndDate != ''">
                        AND tr.ApproverDate <![CDATA[ <=  ]]> #{approvedEndDate}
                    </if>
                    <if test="expectedStartDueDate != null and expectedStartDueDate != ''">
                        AND tr.ReportDueDate <![CDATA[  >=   ]]> #{expectedStartDueDate}
                    </if>
                    <if test="expectedEndDueDate != null and expectedEndDueDate != ''">
                        AND tr.ReportDueDate <![CDATA[ <=  ]]> #{expectedEndDueDate}
                    </if>
                    <if test="draftDeliverStartDate != null and draftDeliverStartDate != ''">
                        AND draftDeliver.softcopy_delivered_date <![CDATA[  >=   ]]> #{draftDeliverStartDate}
                    </if>

                    <if test="draftDeliverEndDate != null and draftDeliverEndDate != ''">
                        AND draftDeliver.softcopy_delivered_date <![CDATA[ <=  ]]> #{draftDeliverEndDate}
                    </if>
                    <if test="reportConfirmedStartDate != null and reportConfirmedStartDate != ''">
                        AND oolConfirm.operate_date <![CDATA[  >=   ]]> #{reportConfirmedStartDate}
                    </if>
                    <if test="reportConfirmedEndDate != null and reportConfirmedEndDate != ''">
                        AND oolConfirm.operate_date <![CDATA[ <=  ]]> #{reportConfirmedEndDate}
                    </if>
                    <if test="responsibleCs != null and responsibleCs != ''">
                        AND oi.cs = #{responsibleCs}
                    </if>
                    <if test="ts != null and ts != ''">
                        AND oi.ts = #{ts}
                    </if>
                    <if test="subContractLabCodeList != null and subContractLabCodeList.size() != 0 ">
                        AND tsc.SubContractLabCode IN
                        <foreach item="item" index="index" collection="subContractLabCodeList" open="(" separator="," close=")">
                            #{item}
                        </foreach>
                    </if>
                    <if test="(orderNo == null or orderNo == '') and (reportStatusList == null or reportStatusList.size() == 0) and (reportNo == null or reportNo == '') and (queryFlag == null or queryFlag == '') and (orderNos == null or orderNos == '') and cancelStatus != null">
                        AND tr.reportStatus != #{cancelStatus}
                    </if>
                    <if test="hasApproved!= null and hasApproved != ''">
                        <choose>
                            <when test='hasApproved == "Y".toString()'>
                                and EXISTS (
                                select objectNo from tb_status tst where tst.objectNo = tr.ReportNo and tst.ObjectType = 4 and tst.OldStatus = 203
                                )
                            </when>
                            <when test='hasApproved == "N".toString()'>
                                and not EXISTS (
                                select objectNo from tb_status tst where tst.objectNo = tr.ReportNo and tst.ObjectType = 4 and tst.OldStatus = 203
                                )
                            </when>
                            <otherwise>

                            </otherwise>
                        </choose>
                    </if>
                </otherwise>
            </choose>
            <if test='reportToDoListType!=null and reportToDoListType != "" and reportToDoListType == "2".toString() and subcontractSection != null and subcontractSection != "" and subcontractSection == "N".toString()'>
                and re.subcontract_tl_status = 0
            </if>
            <if test='reportToDoListType!=null and reportToDoListType != "" and reportToDoListType == "2".toString() and subcontractSection != null and subcontractSection != "" and subcontractSection == "Y".toString()'>
                and re.subcontract_tl_status = 1
            </if>
            <if test='certificateId!=null and certificateId != ""'>
                and tr.CertificateId=#{certificateId}
            </if>
            <if test='sampleInfo!=null and sampleInfo != ""'>
                and oi.product like CONCAT('%',#{sampleInfo},'%')
            </if>
            <if test='photoRemark!=null and photoRemark != ""'>
                and tr.PhotoRemark like CONCAT('%',#{photoRemark},'%')
            </if>
            <if test='createdBy!=null and createdBy != ""'>
                and tr.CreatedBy=#{createdBy}
            </if>
        </where>
    </sql>


    <sql id="querySubReportPageColumnsNew">
        'subcontract' fromDB,
        2 AS reportType,
        2 AS reportFlag,
        toi.order_no AS trueOrderNo,
        toi.parent_order_no AS parentOrderNo,
        tr.ReportNo AS subReportNo,
        tsc.STATUS AS subContractStatus,
        tsc.SubContractLabCode AS subContractLabCode,
        tsc.SubContractLabName AS subContractLabName,
        toi.to_test_flag as toTestFlag,
        tcl.Description AS conclusion,
        tcc.ConclusionSettingID conclusionSettingID,
        tcc.ConclusionID as  conclusionId,
        tr.ID AS reportId,
        tr.AwbNo AS awbNo,
        tr.ReportNo reportNo,
        tr.ReportStatus AS reportStatus,
        tr.ApproverBy AS approvedBy,
        tr.EditorBy AS editorBy,
        tr.ReviewerBy AS reviewerBy,
        tr.CertificateFileCloudKey as accreditationLogoPath,
        tr.CertificateId as certificateId,
        tr.CertificateName as certificateName,
        tr.ExternalReportNo as referenceReportNo,
        tr.CoverPageTemplateName as coverPageTemplateName,
        tr.CoverPageTemplatePath as coverPageTemplatePath,
        tr.ApproverDate AS approvedDate,
        tr.ReportDueDate AS reportedDueDate,
        tr.CreatedDate AS createdDate,
        tr.CreatedBy AS createdBy,
        tr.PhotoRemark as photoRemark,
        tr.WorkFlow AS workFlow,
        rdh.softcopy_delivered_date AS softCopyDeliveredDate,
        tr.SubReportReviseFlag AS subReportReviseFlag,
        tr.SignatureLanguage as signatureLanguage,
        tr.CountryOfDestination AS countryOfDestination,
        tr.Remark as remark,
        tr.TestingType as testingType,
        tr.testResultStatus as testResultStatus,
        IFNULL(tr.ReportTestLineLevel, 'TL') AS reportTestLineLevel,
        tr.EntryMode as entryMode,
        goi.id AS orderId,
        goi.OrderNo AS orderNo,
        toi.order_status AS orderStatus,
        goi.LabCode AS labCode,
        toi.applicant_name as applicantName,
        toi.applicant_name as buyerName,
        goi.CustomerCode AS applicantBossNumber,
        goi.ApplicantCustomerCode AS buyerBossNumber,
        subcontractFrom.CustomerID as subcontractFromLabCode,
        toi.cs AS csName,
        toi.customer_reference_no as customerRefNo,
        toi.order_type orderType,
        goi.SuffixNum AS suffixNum,
        goi.TechnicalSupporter AS technicalSupporter,
        toi.operation_mode AS operationMode,
        toi.service_type as serviceLevel,
        concat_ws(',',trfRel.ExternalOrderNo) as subContractOrderNo,
        tr.ActualReportNo,
        tr.RootReportNo,
        re.lab_section_id as labSectionId,
        IFNULL(re.test_completed_flag,0) as testCompletedFlag,
        draftDeliver.softcopy_delivered_date as draftDeliverDate
        <if test='reportToDoListType!=null and reportToDoListType != "" and reportToDoListType == "3".toString()'>
            ,top.RegionAccount AS subReportReview,
            rdh.hardcopy_delivered_date as hardCopyDeliveredDate
        </if>
    </sql>

    <sql id="querySubReportPageTablesNew">
        tb_order_index toi
        INNER JOIN tb_report tr ON toi.order_no = tr.OrderNo
        LEFT JOIN tb_report_ext re on re.report_id = tr.id
        LEFT JOIN tb_general_order_instance tgoi ON tgoi.OrderNo = tr.OrderNo
        LEFT JOIN gpo.tb_order_person top ON top.GeneralOrderID = toi.order_id AND top.PersonType = 'subReportReviewer'
        LEFT JOIN tb_subcontract_external_relationship tss ON tr.OrderNo = tss.ExternalNo
        LEFT JOIN tb_sub_contract tsc ON tsc.SubContractNo = tss.SubContractNo
        LEFT JOIN tb_general_order_instance goi ON goi.OrderNo = tsc.OrderNo
        INNER JOIN gpo.`tb_customer_instance` subcontractFrom ON toi.order_id = subcontractFrom.`GeneralOrderID` AND subcontractFrom.CustomerUsage = 'subcontractFrom'
        LEFT JOIN tb_conclusion tcc ON tcc.ReportID = tr.ID AND tcc.ConclusionLevelID = 603
        LEFT JOIN tb_conclusion_list tcl ON tcl.ID = tcc.ConclusionID
        LEFT JOIN tb_report_delivery_history rdh ON rdh.report_id = tr.ID AND rdh.delivery_type = 'Report'
        LEFT JOIN tb_report_delivery_history draftDeliver ON draftDeliver.report_id = tr.ID AND draftDeliver.delivery_type = 'DraftReport'
        LEFT JOIN gpo.tb_order_trf_relationship trfRel ON trfRel.OrderId = toi.order_id
        left join tb_order_index toi2 on toi2.order_no  = toi.parent_order_no
        <if test="(certificateTypeList != null and certificateTypeList.size() >0) or (certificateNo != null and certificateNo != '')">
            left join tb_report_certificate trc on trc.report_id = tr.id and trc.status = 1
        </if>
        <if test='pendingFlag != null and pendingFlag != ""'>
            LEFT JOIN gpn.tb_status_log slog on slog.reportId = tr.id
        </if>

        <if test="reportToDoListType!=null and reportToDoListType != '' and (reportToDoListType == '6'.toString() or reportToDoListType == '2'.toString() or reportToDoListType == '8'.toString())">
            LEFT JOIN tb_report_file rf on rf.ReportID = tr.id AND rf.ReportFileType =  1505
        </if>
    </sql>

   <sql id="querySubReportPageWhereNew">
        <where>
            toi.subcontract_lab_id = CONCAT('lab',#{labId})
            <if test="createdStartDate != null and createdStartDate != ''">
                AND tr.CreatedDate <![CDATA[  >=   ]]> #{createdStartDate}
            </if>

            <if test="createdEndDate != null and createdEndDate != ''">
                AND tr.CreatedDate <![CDATA[ <=  ]]> #{createdEndDate}
            </if>
            <if test='hasReportRemark != null and hasReportRemark != "" and hasReportRemark == "Y".toString()'>
                AND (tr.Remark IS NOT NULL AND tr.Remark != '')
            </if>
            <if test='hasReportRemark != null and hasReportRemark != "" and hasReportRemark == "N".toString()'>
                AND (tr.Remark IS NULL OR tr.Remark = '')
            </if>
            <if test='pendingFlag != null and pendingFlag != "" and pendingFlag == "Y".toString()'>
                AND slog.Id IS NOT NULL
            </if>
            <if test='pendingFlag != null and pendingFlag != "" and pendingFlag == "N".toString()'>
                AND slog.Id IS NULL
            </if>
            <if test="accreditationList != null and accreditationList.size() != 0 ">
                AND tr.certificateId IN
                <foreach item="item" index="index" collection="accreditationList" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="testingType != null and testingType.size() >0">
                and tr.testingType in
                <foreach item="item" index="index" collection="testingType" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="testResultStatus != null and testResultStatus!=''">
                and tr.testResultStatus = #{testResultStatus}
            </if>
            <if test='testCompletedFlag != null and testCompletedFlag != ""'>
                <choose>
                    <when test='testCompletedFlag == "Y".toString()'>
                        AND re.test_completed_flag = 1
                    </when>
                    <when test='testCompletedFlag == "N".toString()'>
                        AND IFNULL(re.test_completed_flag,0) = 0
                    </when>
                </choose>
            </if>
                <choose>
                <when test='reportToDoListType!=null and reportToDoListType != "" and reportToDoListType == "4".toString()'>
                    And tr.ReportStatus = 203
                    and tss.subContractType in (1,2,4,8,16,32)
                </when>
                <when test='reportToDoListType!=null and reportToDoListType != "" and reportToDoListType == "3".toString()'>
                    AND subcontractFrom.`CustomerID` = #{labCode}
                    AND  tr.ReportStatus = 203
                    AND <![CDATA[ (tr.WorkFlow & 4) > 0]]>
                </when>
                <otherwise>
                    AND subcontractFrom.`CustomerID` = #{labCode}
                </otherwise>
            </choose>
            <if test='readyToDelivery != null and readyToDelivery ==  "Y".toString()'>
                AND (toi.enable_delivery_flag > 0 or toi.order_type in ('IDB','IDN'))
            </if>
            <if test='readyToDelivery != null and readyToDelivery ==  "N".toString()'>
                AND (toi.enable_delivery_flag = 0 AND toi.order_type != 'IDB' AND toi.order_type != 'IDN')
            </if>
            <if test="certificateTypeList != null and certificateTypeList.size() >0">
                and trc.certificate_type in
                <foreach item="item" index="index" collection="certificateTypeList" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test='certificateNo != null and certificateNo != ""'>
                AND trc.certificate_no like CONCAT('%',#{certificateNo},'%')
            </if>

            <choose>
                <when test="queryTodoCount!= null and queryTodoCount ">

                </when>
                <otherwise>
                    <choose>
                        <when test="orderNoList != null and orderNoList.size() != 0">
                            AND  toi.parent_order_no  IN (
                            <foreach collection="orderNoList" item="item" index="index" separator =",">
                                #{item}
                            </foreach >
                            )
                        </when>
                        <when test="orderNo != null and orderNo != ''">
                            AND toi.parent_order_no = #{orderNo}
                        </when>
                        <otherwise>
                        </otherwise>
                    </choose>
                    <if test="customerRefNo != null and customerRefNo!= ''">
                        AND (toi.customer_reference_no = #{customerRefNo} or toi.parent_order_no = #{customerRefNo})
                    </if>
                    <choose>
                        <when test="applicant != null and applicant.customerNo != null and applicant.customerNo != '' ">
                            AND toi2.applicant_no = #{applicant.customerNo}
                        </when>
                        <when test="applicant != null and applicant.customerName != null and applicant.customerName != '' ">
                            AND toi2.applicant_name like concat('%',#{applicant.customerName},'%')
                        </when>
                    </choose>
                    <choose>
                        <when test="buyer != null and buyer.customerNo != null and buyer.customerNo != '' ">
                            AND toi2.buyer_no = #{buyer.customerNo}
                        </when>
                        <when test="buyer != null and buyer.customerName != null and buyer.customerName != '' ">
                            AND toi2.buyer_name like concat('%',#{buyer.customerName},'%')
                        </when>
                    </choose>
                    <if test="reportNo != null and reportNo != ''">
                        AND tr.ActualReportNo = #{reportNo}
                    </if>
                    <if test="subcontractOrderNo != null and subcontractOrderNo != '' " >
                        and trfRel.ExternalOrderNo = #{subcontractOrderNo}
                    </if>
                    <if test="orderId != null and orderId != ''">
                        AND goi.id = #{orderId}
                    </if>
                    <if test="draftReportRequired != null and draftReportRequired != ''">
                        AND toi.need_draft_flag = #{draftReportRequired}
                    </if>
                    <if test="reportNoList != null and reportNoList.size() != 0 ">
                        AND tr.ActualReportNo IN
                        <foreach item="item" index="index" collection="reportNoList" open="(" separator="," close=")">
                            #{item}
                        </foreach>
                    </if>
                    <if test="orderStatusList != null and orderStatusList.size() != 0 ">
                        AND toi.order_status IN
                        <foreach item="item" index="index" collection="orderStatusList" open="(" separator="," close=")">
                            #{item}
                        </foreach>
                    </if>




                    <if test="orderStatus != null and orderStatus>0">
                        AND toi.order_status = #{orderStatus}
                    </if>

                    <if test="approvedStartDate != null and approvedStartDate != ''">
                        AND tr.ApproverDate <![CDATA[  >=   ]]> #{approvedStartDate}
                    </if>

                    <if test="approvedEndDate != null and approvedEndDate != ''">
                        AND tr.ApproverDate <![CDATA[ <=  ]]> #{approvedEndDate}
                    </if>

                    <if test="expectedStartDueDate != null and expectedStartDueDate != ''">
                        AND tr.ReportDueDate <![CDATA[  >=   ]]> #{expectedStartDueDate}
                    </if>

                    <if test="expectedEndDueDate != null and expectedEndDueDate != ''">
                        AND tr.ReportDueDate <![CDATA[ <=  ]]> #{expectedEndDueDate}
                    </if>
                    <if test="labSectionIds != null and labSectionIds != ''">
                        AND LOCATE(re.lab_section_id,#{labSectionIds}) > 0
                    </if>
                    <if test="readyForDeliver != null and readyForDeliver != ''">
                        AND (tsc.STATUS != 3 or tr.ReportStatus = 203 or tr.ReportStatus = 208)
                    </if>
                    <if test="subContractToLab != null and subContractToLab != ''">
                        AND (tsc.SubContractLabCode = #{subContractToLab})
                    </if>
                    <if test="subContractLabCodeList != null and subContractLabCodeList.size() != 0 ">
                        AND tsc.SubContractLabCode IN
                        <foreach item="item" index="index" collection="subContractLabCodeList" open="(" separator="," close=")">
                            #{item}
                        </foreach>
                    </if>
                    <if test="responsibleCs != null and responsibleCs != ''">
                        AND toi.cs = #{responsibleCs}
                    </if>
                    <if test="ts != null and ts != ''">
                        AND toi.ts = #{ts}
                    </if>

                    <if test='deliverDraft!= null and deliverDraft != ""'>
                        <choose>
                            <when test='deliverDraft == "Y".toString()'>
                                AND draftDeliver.id is not null
                            </when>
                            <when test='deliverDraft == "N".toString()'>
                                AND draftDeliver.id is  null
                            </when>
                            <otherwise>
                            </otherwise>
                        </choose>
                    </if>
                    <if test="hasApproved">
                        and EXISTS (
                        select objectNo from tb_status tst where tst.objectNo = tr.ReportNo and tst.ObjectType = 4 and tst.OldStatus = 203
                        )
                    </if>
                </otherwise>
            </choose>
            <if test='certificateId!=null and certificateId != ""'>
                and tr.CertificateId=#{certificateId}
            </if>
            <if test='sampleInfo!=null and sampleInfo != ""'>
                and toi.product like CONCAT('%',#{sampleInfo},'%')
            </if>
            <if test='photoRemark!=null and photoRemark != ""'>
                and tr.PhotoRemark like CONCAT('%',#{photoRemark},'%')
            </if>
            <if test='createdBy!=null and createdBy != ""'>
                and tr.CreatedBy=#{createdBy}
            </if>
        </where>
    </sql>

    <sql id="queryReportCountSqlNew">
        select count(1) from (
        select distinct
        id
        <if test="needToAmendFlag != null and needToAmendFlag != ''">
            ,recordId
        </if>
        from(
        select DISTINCT tr.id
        <if test="needToAmendFlag != null and needToAmendFlag != ''">
            ,record.ApplyId as recordId
        </if>
        <if test="reportType != null and reportType != '' and reportType > 0">
            ,tr.reportFlag as reportType
        </if>
        from
        <include refid="queryReportPageTablesNew"></include>
        <include refid="queryReportPageWhereNew"></include>
        <if test='reportToDoListType!=null and reportToDoListType != "" and (reportToDoListType == "3".toString())'>
            union
            select DISTINCT tr.id
            <if test="reportType != null and reportType != '' and reportType > 0">
                ,2 as reportType
            </if>
            from
            <include refid="querySubReportPageTablesNew"></include>
            <include refid="querySubReportPageWhereNew"></include>
        </if>
        ) TEMP
        <where>
            <if test="readyForDeliver != null and readyForDeliver != ''">
                AND (TEMP.subContractStatus != 3 or TEMP.reportStatus = 203 or TEMP.reportStatus = 208)
            </if>
            <if test="reportType != null and reportType != '' and reportType > 0">
                AND TEMP.reportType = #{reportType}
            </if>
        </where>
        ) table_count
    </sql>


    <select id="queryReportPageNew_COUNT" resultType="java.lang.Long">
        <include refid="queryReportCountSqlNew"></include>
    </select>

    <select id="queryReportPageQuantity" resultType="java.lang.Long">
        SELECT count(1) FROM (
            SELECT distinct id
            <if test="needToAmendFlag != null and needToAmendFlag != ''">
                ,recordId
            </if>
            FROM(
                SELECT distinct tr.id
                <if test="needToAmendFlag != null and needToAmendFlag != ''">
                    ,record.ApplyId as recordId
                </if>
                FROM tb_report tr
                INNER JOIN tb_order_index oi ON oi.order_no = tr.OrderNo
                <if test="needToAmendFlag != null and needToAmendFlag != ''">
                    INNER JOIN tb_report_rework_apply_records record ON tr.ID = record.ReportID
                    AND record.ApplyStatus = 4
                </if>
                <where>
                    <if test="!shareLab">
                         tr.labId = #{labId}
                    </if>
                    <if test='reportToDoListType!=null and reportToDoListType != "" and reportToDoListType == "3".toString()'>
                        AND  ((tr.ReportStatus = 204 AND <![CDATA[ (tr.WorkFlow & 2) > 0]]> AND <![CDATA[ (tr.WorkFlow & 1) <= 0]]>) OR (tr.ReportStatus = 211 AND <![CDATA[ (tr.WorkFlow & 2) > 0]]>))
                    </if>
                    <if test='reportToDoListType!=null and reportToDoListType != "" and reportToDoListType == "4".toString()'>
                        AND tr.ReportStatus = 203
                        AND EXISTS (
                        SELECT ID FROM tb_report_file trf WHERE trf.ReportFileType = 1504 AND trf.ReportID = tr.ID  AND oi.report_file_type = 1
                        union
                        SELECT ID FROM tb_report_file trf WHERE trf.ReportFileType = 1501 AND trf.ReportID = tr.ID  AND oi.report_file_type != 1
                        )
                        <if test="draftReportRequired != null and draftReportRequired != ''">
                            AND oi.need_draft_flag = #{draftReportRequired}
                        </if>
                    </if>
                    <if test='reportToDoListType!=null and reportToDoListType != "" and reportToDoListType == "5".toString()'>
                        AND (oi.enable_delivery_flag > 0 OR oi.order_type in ('IDB' ,'IDN'))
                        AND (tr.reportStatus = 210 OR ((oi.need_draft_flag = 0 OR <![CDATA[ (tr.WorkFlow & 8) <= 0]]>) AND tr.reportStatus = 203))
                    </if>
                    <if test='reportToDoListType!=null and reportToDoListType != "" and reportToDoListType == "8".toString()'>
                        <if test="typingFinishedFlag != null and typingFinishedFlag == 1">
                            AND ((tr.reportStatus = 212 AND <![CDATA[ (tr.WorkFlow & 2) > 0 ]]>)
                                OR (tr.reportStatus = 211 AND <![CDATA[ (tr.WorkFlow & 1) > 0 ]]> AND <![CDATA[ (tr.WorkFlow & 2) <= 0 ]]> ))
                        </if>
                        <if test="typingFinishedFlag != null and typingFinishedFlag != 1">
                            <if test="reportStatusList != null and reportStatusList.size() != 0 ">
                                AND tr.reportStatus IN
                                <foreach item="item" index="index" collection="reportStatusList" open="(" separator="," close=")">
                                    #{item}
                                </foreach>
                            </if>
                        </if>
                    </if>
                    <if test="queryTodoCount!= null and queryTodoCount ">
                        <if test='reportToDoListType!=null and reportToDoListType != "" and reportToDoListType != "4".toString() and reportToDoListType != "5".toString() and reportToDoListType != "7".toString()  and reportToDoListType != "8".toString()'>
                            <if test="reportStatusList != null and reportStatusList.size() != 0 ">
                                AND tr.reportStatus IN
                                <foreach item="item" index="index" collection="reportStatusList" open="(" separator="," close=")">
                                    #{item}
                                </foreach>
                            </if>
                        </if>
                    </if>
                </where>

            <if test='reportToDoListType!=null and reportToDoListType != "" and (reportToDoListType == "3".toString())'>
                union
                SELECT DISTINCT tr.id
                FROM tb_order_index toi
                INNER JOIN tb_report tr ON toi.order_no = tr.OrderNo
                INNER JOIN gpo.`tb_customer_instance` subcontractFrom ON toi.order_id = subcontractFrom.`GeneralOrderID` AND subcontractFrom.CustomerUsage = 'subcontractFrom'
                <if test='reportToDoListType!=null and reportToDoListType != "" and reportToDoListType == "4".toString()'>
                    LEFT JOIN tb_subcontract_external_relationship tss ON tr.OrderNo = tss.ExternalNo
                </if>
                <where>
                    toi.subcontract_lab_id = CONCAT('lab',#{labId})
                    <if test='reportToDoListType!=null and reportToDoListType != "" and reportToDoListType == "4".toString()'>
                        AND tr.ReportStatus = 203
                        AND tss.subContractType in (1,2,4,8,16,32)
                        <if test="draftReportRequired != null and draftReportRequired != ''">
                            AND toi.need_draft_flag = #{draftReportRequired}
                        </if>
                    </if>
                    <if test='reportToDoListType!=null and reportToDoListType != "" and reportToDoListType == "3".toString()'>
                        AND subcontractFrom.`CustomerID` = #{labCode}
                        AND  tr.ReportStatus = 203
                        AND <![CDATA[ (tr.WorkFlow & 4) > 0]]>
                    </if>
                </where>
            </if>
            ) TEMP
        ) table_count
    </select>

    <select id="queryReportPageNew" parameterType="com.sgs.otsnotes.facade.model.req.report.ReportQueryPageRequest" resultType="com.sgs.otsnotes.facade.model.dto.GpnOrderReportDTO">
        select
        temp.*
        from(
            select
            <include refid="queryReportPageColumnsNew"></include>
            from
            <include refid="queryReportPageTablesNew"></include>
            <include refid="queryReportPageWhereNew"></include>
            group by tr.id
        <if test='reportToDoListType!=null and reportToDoListType != "" and (reportToDoListType == "3".toString())'>
            union
            select
                <include refid="querySubReportPageColumnsNew"></include>
                from
                <include refid="querySubReportPageTablesNew"></include>
                <include refid="querySubReportPageWhereNew"></include>
                group by tr.id
        </if>
        ) TEMP
        <where>
            <if test="readyForDeliver != null and readyForDeliver != ''">
                AND (TEMP.subContractStatus != 3 or TEMP.reportStatus = 203 or TEMP.reportStatus = 208)
            </if>
            <if test="reportType != null and reportType != '' and reportType > 0">
                AND TEMP.reportType = #{reportType}
            </if>
        </where>
        order by TEMP.OrderNo desc,TEMP.ReportNo asc
    </select>


    <select id="getOriginalReportByOrderNo" resultType="com.sgs.otsnotes.dbstorages.mybatis.model.ReportInfoPO" parameterType="java.lang.String">
		SELECT
			re.*
		FROM
			tb_report re
		JOIN tb_report_type rt ON re.ReportTypeID = rt.ID
		WHERE
			OrderNo = #{orderNo}
	</select>

    <select id="getByReportNo" resultType="com.sgs.otsnotes.facade.model.dto.ReportDTO" parameterType="java.lang.String">
        SELECT
        <include refid="Report_Column_List" />
        FROM
        tb_report
        WHERE ReportNo = #{reportNo}
        limit 1
    </select>

    <select id="getByReportNos" resultType="com.sgs.otsnotes.facade.model.dto.ReportDTO">
        SELECT
        <include refid="Report_Column_List" />
        FROM
        tb_report
        <where>
            <choose>
                <when test="reportNoList != null and reportNoList.size() != 0">
                    ReportNo IN (
                    <foreach collection="reportNoList" item="item" index="index" separator =",">
                        #{item}
                    </foreach >
                    )
                </when>
                <otherwise>
                    1!=1
                </otherwise>
            </choose>
        </where>

    </select>

    <resultMap type="com.sgs.otsnotes.facade.model.rsp.report.GetTestLineConclusionRsp" id="testLineConclusion">
        <id column="ReportNo" property="reportNo"/>
        <id column="reportID" property="reportId"/>
        <result column="CloudID" property="reportFilePath"/>
        <collection column="ReportNo" property="testLineList" ofType="com.sgs.otsnotes.facade.model.rsp.report.ReportTestLineConclusionDTO">
            <result column="TestLineID" property="testLineID"/>
            <result column="TestLineEvaluation" property="testItem"/>
        </collection>
    </resultMap>

    <select id="quertTestLineConclusion" parameterType="java.lang.String" resultMap="testLineConclusion">
			SELECT a.reportID,a.reportNo,a.cloudID,b.TestLineID,b.EvaluationAlias AS TestLineEvaluation
			FROM
				(
					SELECT DISTINCT tr.ID AS reportID,tr.ReportNo,trf.CloudID FROM tb_report tr
					LEFT JOIN tb_report_file trf ON tr.id = trf.ReportID
					AND trf.ReportFileType = 1502
					WHERE tr.id = #{reportId}
				) AS a
			LEFT JOIN (
				SELECT DISTINCT tr.ReportNo,tl.TestLineID,art_cit_rel.EvaluationAlias FROM tb_report tr
				JOIN tb_report_file trf ON tr.id = trf.ReportID
				JOIN tre_report_matrix_relationship trm ON trm.ReportID = tr.ID
				JOIN tb_conclusion tb_con ON tb_con.ReportID = tr.id
				JOIN tb_test_line_instance tl ON tl.GeneralOrderInstanceID = tb_con.GeneralOrderInstanceID AND tl.ID = tb_con.ObjectID
        JOIN tb_trims_artifact_citation_relationship art_cit_rel ON tl.CitationBaseId = art_cit_rel.Id
				WHERE tr.id = #{reportId} AND tb_con.ConclusionID = '87659d05-9aad-11e7-b1df-00163e0a4f4a' AND tb_con.ConclusionLevelID = 602
			) b ON a.ReportNo = b.ReportNo;

	</select>

    <select id="getReportTestLineConclusions" parameterType="java.util.Map" resultType="com.sgs.otsnotes.facade.model.rsp.report.ReportTestLineConclusionDTO">
        SELECT conc.*, art_cit_rel.EvaluationAlias AS testItem,conclist.Description AS testLineConclusion,tl.CreatedDate AS testLineCreatedDate
        FROM tb_conclusion conc
        JOIN tb_test_line_instance tl ON conc.TestLineInstanceID = tl.ID
        JOIN tb_conclusion_list conclist ON conc.ConclusionID = conclist.ID
        JOIN tb_trims_artifact_citation_relationship art_cit_rel on tl.CitationBaseId = art_cit_rel.Id
        WHERE conc.ReportID = #{reportId}
        <if test="conclusionLevelID != null">
            AND conc.ConclusionLevelID= #{conclusionLevelID}
        </if>
    </select>

    <select id="getReportTestLineEvaluationAlias" parameterType="java.util.Map" resultType="com.sgs.otsnotes.facade.model.rsp.report.ReportTestLineConclusionDTO">
        SELECT tl.ID AS testLineInstanceID, art_cit_rel.EvaluationAlias AS testItem, tl.CreatedDate AS testLineCreatedDate
        FROM tb_test_line_instance tl
            JOIN tb_trims_artifact_citation_relationship art_cit_rel on tl.CitationBaseId = art_cit_rel.Id
        WHERE tl.ID IN
        <foreach collection="testLineInstanceIds" item="testLineInstanceId" open="(" close=")" separator=",">
            #{testLineInstanceId}
        </foreach>
    </select>


    <select id="getReportByTestLineInstanceID" resultMap="BaseResultMap" parameterType="java.lang.String">
		select re.ID,re.OrderNo,re.ReportNo,re.ParentReportNo,re.LabId,re.LabCode,re.ReportDueDate,re.ReportStatus,re.TemplateID,re.CoverPageTemplateID,re.CoverPageTemplateName,re.CoverPageTemplatePath,
		        re.ReportTypeID,re.RequestID,re.RequestSentDate,re.RequestFinishedDate,re.Approver,re.ApproverDate,re.CustomerCode,
		        re.CustomerGroupCode,re.AmendRemark,re.ActiveIndicator,re.CreatedBy,re.CreatedDate,re.ModifiedBy,re.ModifiedDate
		from tb_report re
		join tb_general_order_instance goi on re.orderNo = goi.orderNo
		join tb_test_line_instance tl on goi.id = tl.GeneralOrderInstanceID
		where tl.id = #{testLineInstanceId}
		ORDER BY re.ReportNo LIMIT 0 ,1
	</select>
    <select id="getAllReportByTestLineInstanceID" resultMap="BaseResultMap" parameterType="com.sgs.otsnotes.facade.model.info.GetReportListByTlIdInfo">
		select distinct re.ID,re.OrderNo,re.ReportNo,re.ParentReportNo,re.LabId,re.LabCode,re.ReportDueDate,re.ReportStatus,re.TemplateID,re.CoverPageTemplateID,re.CoverPageTemplateName,re.CoverPageTemplatePath,
               re.ReportTypeID,re.RequestID,re.RequestSentDate,re.RequestFinishedDate,re.Approver,re.ApproverDate,re.CustomerCode,
               re.CustomerGroupCode,re.AmendRemark,re.ActiveIndicator,re.CreatedBy,re.CreatedDate,re.ModifiedBy,re.ModifiedDate
        from tb_report re left join tre_report_matrix_relationship trmr on re.ID = trmr.ReportID
        left join tb_test_matrix ttm on ttm.ID = trmr.TestMatrixID
        where ttm.TestLineInstanceID = #{testLineInsId}
        and re.orderNo = #{orderNo}
        and ttm.ActiveIndicator = 1
	</select>
    <select id="getReportListByTestLineInstanceID" resultMap="BaseResultMap" parameterType="com.sgs.otsnotes.facade.model.info.GetReportListByTlIdInfo">
        select distinct re.ID,re.OrderNo,re.ReportNo,re.ParentReportNo,re.LabId,re.LabCode,re.ReportDueDate,re.ReportStatus,re.TemplateID,re.CoverPageTemplateID,re.CoverPageTemplateName,re.CoverPageTemplatePath,
                        re.ReportTypeID,re.RequestID,re.RequestSentDate,re.RequestFinishedDate,re.Approver,re.ApproverDate,re.CustomerCode,
                        re.CustomerGroupCode,re.AmendRemark,re.ActiveIndicator,re.CreatedBy,re.CreatedDate,re.ModifiedBy,re.ModifiedDate
        from tb_report re left join tre_report_matrix_relationship trmr on re.ID = trmr.ReportID
                          left join tb_test_matrix ttm on ttm.ID = trmr.TestMatrixID
        where re.orderNo = #{orderNo}
          and ttm.ActiveIndicator = 1
        <if test="testLineIdList != null and testLineIdList.size() != 0 ">
            AND ttm.TestLineInstanceID in
            <foreach item="item" index="index" collection="testLineIdList" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="getReportMatrixByTestLineInstanceID" resultType="com.sgs.otsnotes.facade.model.output.info.ReportMatrixInfo"
            parameterType="com.sgs.otsnotes.facade.model.info.GetReportListByTlIdInfo">
        select distinct
        re.ID reportId,
        ttm.TestLineInstanceID testLineInstanceId,
        ttm.ID testMatrixId
        from tb_report re left join tre_report_matrix_relationship trmr on re.ID = trmr.ReportID
        left join tb_test_matrix ttm on ttm.ID = trmr.TestMatrixID
        where re.orderNo = #{orderNo}
        and ttm.ActiveIndicator = 1
        <if test="testLineIdList != null and testLineIdList.size() != 0 ">
            AND ttm.TestLineInstanceID in
            <foreach item="item" index="index" collection="testLineIdList" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <update id="updateReportByID" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.ReportInfoPO">
        UPDATE
        tb_report
        SET
        ModifiedBy=#{modifiedBy},ModifiedDate=#{modifiedDate}
<!--        <if test="reportDueDate != null  " >-->
<!--            ,ReportDueDate = #{reportDueDate}-->
<!--        </if>-->
        <if test="customerCode != null and  customerCode != '' " >
            ,CustomerCode = #{customerCode}
        </if>
        <if test="customerGroupCode != null and  customerGroupCode != '' " >
            ,CustomerGroupCode = #{customerGroupCode}
        </if>
        <if test="amendRemark != null and  amendRemark != '' " >
            ,AmendRemark = #{amendRemark}
        </if>
        WHERE id=#{ID}
    </update>
    <select id="getReportListByOrderNo" resultMap="BaseResultMap">
         SELECT * FROM tb_report
          WHERE OrderNo = #{orderNo}
        ORDER BY CreatedDate DESC
    </select>

    <select id="getReportByReportNo" resultMap="BaseResultMap">
         SELECT * FROM tb_report
        <where>
            <choose>
                <when test="reportNoList != null and reportNoList.size() != 0 ">
                    AND ReportNo in
                    <foreach item="item" index="index" collection="reportNoList" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </when>
                <otherwise>
                    1!=1
                </otherwise>
            </choose>
        </where>
        ORDER BY CreatedDate DESC
    </select>

    <select id="getFirstReportByOrderNo" resultMap="BaseResultMap">
        SELECT * FROM tb_report
        WHERE OrderNo = #{orderNo}
        ORDER BY CreatedDate
        LIMIT 1
    </select>

    <!-- 2018年5月2日13:05:18 by whale 修复reportNo号码重复问题 修改sql 将原始的 子查询删除（因为没有使用到，关联的字段台前也并没有被使用） 更改为 双表关联-->
    <!-- 2018年6月26日14:21:02 modified by vincent 增加order的active的输出 -->
    <select id="getReportList"
            resultType="com.sgs.otsnotes.facade.model.info.report.GetReportInfo"
            parameterType="com.sgs.otsnotes.facade.model.info.report.GetReportListInfo">
        SELECT distinct
        t_order.CustomerName,
        t_order.ActiveIndicator,
        T_R.*,
        t_rf.FileStatus
        FROM
        TB_Report T_R
        LEFT JOIN
        tb_general_order_instance t_order
        ON
        t_order.OrderNo = T_R.OrderNo
--         LEFT JOIN tb_order_cross_lab_rel oclr on oclr.orderNo = t_order.OrderNo
        LEFT JOIN tb_report_file t_rf ON T_R.ID = t_rf.reportID
        AND t_rf.ReportFileType = 1504
        WHERE  1=1
<!--        <if test="blockTop == ''">-->
<!--            AND (t_order.LabCode = #{labCode} or (oclr.toLab = #{labCode} and oclr.execType = 1))-->
<!--        </if>-->
<!--        <if test="blockTop == '1'">-->
<!--            AND t_order.LabCode = #{labCode} and oclr.id is null-->
<!--        </if>-->
        <if test="reportNo != null and  reportNo != '' " >
            and T_R.ReportNo = #{reportNo}
        </if>
        <if test="status != null and  status != '' " >
            and T_R.reportStatus=#{status}
        </if>
        <if test="orderNo != null and orderNo != '' " >
            and t_order.OrderNo = #{orderNo,jdbcType=VARCHAR}
        </if>
        <if test="buyer != null and  buyer != '' " >
            and t_order.CustomerName like concat('%',#{buyer},'%')
        </if>
        <if test="approverBy != null and  approverBy != '' " >
            and T_R.approverBy like concat('%',#{approverBy},'%')
        </if>
        <if test="reportDueDateStart != null and reportDueDateStart != '' " >
            and T_R.ReportDueDate>=#{reportDueDateStart}
        </if>
        <if test="reportDueDateEnd != null and reportDueDateEnd != '' " >
            and T_R.ReportDueDate &lt;=#{reportDueDateEnd}
        </if>
        <if test="approverDateStart != null and approverDateStart != '' " >
            and T_R.ApproverDate>=#{approverDateStart}
        </if>
        <if test="approverDateEnd != null and approverDateEnd != '' " >
            and T_R.ApproverDate &lt;=#{approverDateEnd}
        </if>
        <if test="requestID != null and requestID != '' " >
            and T_R.RequestID=#{requestID}
        </if>
        <choose>
            <when test="fuzzySearch">
                ORDER BY t_order.OrderNo DESC
            </when>
            <otherwise>
                ORDER BY T_R.CreatedDate DESC
            </otherwise>
        </choose>
    </select>

    <update id="updateReportDetailById" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.ReportInfoPO">
        UPDATE
            tb_report
        SET
            TemplateID=#{templateID}
          ,ApproverBy=#{approverBy}
          ,Approver=#{approver}
          ,ApproverDate=#{approverDate}
          ,CoverPageTemplateID=#{coverPageTemplateID}
          ,CoverPageTemplateName=#{coverPageTemplateName}
          ,CoverPageTemplatePath=#{coverPageTemplatePath}
          ,CoverPageTemplateNewMappingFlag=#{coverPageTemplateNewMappingFlag}
          ,CertificateId=#{certificateId}
          ,CertificateFileCloudKey=#{certificateFileCloudKey}
          ,CertificateName=#{certificateName}
          ,WatermarkCode=#{watermarkCode}
          ,LogoAliyunID=#{logoAliyunID}
          ,AmendReportType=#{amendReportType}
          ,ModifiedDate=#{modifiedDate}
          ,ModifiedBy=#{modifiedBy}
        WHERE ID=#{ID}
    </update>


    <insert id="batchSaveReportRegistration" parameterType="java.util.List">
        INSERT INTO tb_report
        (
        ID,LabId,LabCode, OrderNo, ReportNo,ReportStatus,ActiveIndicator,DeliverReportFormat,CoverPageTemplateName,CoverPageTemplatePath,
        CreatedBy, CreatedDate,ReportDueDate,ParentReportNo,ActualReportNo,RootReportNo,OperationType,ExternalReportNo,SealFlag,CountryOfDestination,CertificateId,CertificateFileCloudKey,CertificateName, ReportVersion
        ,TestResultStatus,ReviewerBy,Reviewer)
        VALUES
        <foreach collection="list" index="index" item="rel" separator=",">
            (
            #{rel.ID},#{rel.labId},#{rel.labCode}, #{rel.orderNo},#{rel.reportNo},#{rel.reportStatus},
            #{rel.activeIndicator},#{rel.deliverReportFormat},#{rel.coverPageTemplateName},#{rel.coverPageTemplatePath},#{rel.createdBy}, #{rel.createdDate},
             #{rel.reportDueDate},#{rel.parentReportNo},#{rel.actualReportNo},#{rel.rootReportNo},#{rel.operationType},#{rel.externalReportNo},#{rel.sealFlag},
            #{rel.countryOfDestination},#{rel.certificateId},#{rel.certificateFileCloudKey},#{rel.certificateName},#{rel.reportVersion},#{rel.testResultStatus},
            #{rel.reviewerBy},#{rel.reviewer})
        </foreach>

    </insert>

    <select id="queryReworkReportInfo" parameterType="com.sgs.otsnotes.facade.model.req.report.ReworkReportInfoReq" resultMap="BaseResultMap">
        SELECT * FROM tb_report report
        WHERE 1=1 AND report.ReportStatus IN (205,207,202)
        <if test="reportNo != null and reportNo != ''">
            AND report.ReportNo like CONCAT('%',#{reportNo},'%')
        </if>
        <if test="reportNoList != null and reportNoList.size() != 0 ">
            AND report.ReportNo in
            <foreach item="item" index="index" collection="reportNoList" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="intervalDay==0 ">
            AND DATE_FORMAT(report.LastModifiedTimestamp,'%Y-%m-%d') = DATE_FORMAT(date_sub(curdate(),interval #{intervalDay} day),'%Y-%m-%d')
        </if>
        <if test="intervalDay!=0 ">
            AND DATE_FORMAT(report.LastModifiedTimestamp,'%Y-%m-%d') >= DATE_FORMAT(date_sub(curdate(),interval #{intervalDay} day),'%Y-%m-%d')
        </if>
        ORDER BY report.LastModifiedTimestamp
    </select>

    <select id="checkIsSubReport" resultType="java.lang.Integer">
        SELECT COUNT(1)
        FROM tb_general_order_instance goi
                 LEFT JOIN tb_report tr ON goi.OrderNo = tr.OrderNo
        WHERE tr.`ReportNo` = #{reportNo}
          AND goi.`LabCode` = #{labCode}
    </select>
    <select id="checkIsSubReportByReportNo" resultType="java.lang.Integer">
        SELECT COUNT(1)
        FROM tb_general_order_instance goi
                 LEFT JOIN tb_sub_contract tsc
                           ON goi.OrderNo = tsc.OrderNo
                 INNER JOIN tb_subcontract_external_relationship tss
                            ON tsc.SubContractNo = tss.SubContractNo
                 INNER JOIN tb_report tr
                            ON tss.ExternalNo = tr.OrderNo
        WHERE tr.`ReportNo` = #{reportNo}
    </select>
    <select id="checkSubContractByReport" resultType="java.lang.Integer">
            SELECT
                COUNT(1)
            FROM
                tb_report tr
                LEFT JOIN tre_report_matrix_relationship rm ON tr.id = rm.ReportID
                LEFT JOIN tb_test_matrix tm ON rm.TestMatrixID = tm.id
                LEFT JOIN tb_test_line_instance tli ON tm.TestLineInstanceID = tli.id
            WHERE
                tr.ReportNo = #{reportNo}
                AND tli.TestLineType &amp; #{testLineType} = 0
    </select>

    <select id="getReportInfoByOrderNos" resultType="com.sgs.otsnotes.facade.model.dto.GpnOrderReportDTO">
        SELECT distinct
        t.ID as "reportId",
        t.OrderNo as "orderNo",
        t.ReportNo as "reportNo",
        t.ActualReportNo as "actualReportNo",
        t.RootReportNo as "rootReportNo",
        t.ParentReportNo as "parentReportNo",
        t.ReportStatus as "reportStatus",
        t.CreatedDate as "createdDate",
        t.ReportDueDate as "reportedDueDate",
        t.ReportFlag as "reportFlag",
        t.ApproverDate approvedDate,
        t.SoftcopyDeliveryDate softCopyDeliveredDate,
        t.ExternalReportNo referenceReportNo,
        t.CountryOfDestination countryOfDestination
        FROM tb_report t
        <where>
            <choose>
                <when test="orderNoList != null and orderNoList.size()!=0">
                    and t.OrderNo in
                    <foreach collection="orderNoList" item="item" open="(" close=")"
                             separator=",">
                        #{item}
                    </foreach>
                </when>
                <otherwise>
                    and 1!=1
                </otherwise>
            </choose>
        </where>
        ORDER BY t.CreatedDate DESC
    </select>


    <select id="getTestLineListByReport" resultType="com.sgs.otsnotes.dbstorages.mybatis.model.TestLineInstancePO">
        SELECT
            DISTINCT
            tl.StandardName standardName,
            tl.TestDueDate testDueDate,
            tl.ID,
            tl.TestItemNo testItemNo,
            tl.TestLineID testLineID,
            tl.TestLineStatus testLineStatus,
            tl.PendingFlag pendingFlag
        FROM
            tre_report_matrix_relationship rmr
                LEFT JOIN tb_test_matrix tm ON rmr.TestMatrixID = tm.ID
                LEFT JOIN tb_test_line_instance tl on tl.ID = tm.TestLineInstanceID
        where rmr.ReportID = #{reportId}
            and tl.TestLineStatus != 706
    </select>

    <update id="updateReportDueDateBatch" parameterType="java.util.List">
        <foreach collection="reportList" item="item" index="index" open="" close="" separator=";">
            UPDATE tb_report r
            SET r.ReportDueDate = #{item.reportDueDate},
                r.ModifiedBy = #{item.modifiedBy},
                r.ModifiedDate = #{item.modifiedDate}
            WHERE
            r.ID = #{item.ID}
            AND r.ReportStatus NOT IN (202,208)
        </foreach>
    </update>

    <select id="getRelReportListByReportMatrix" resultMap="BaseResultMap">
        SELECT
            DISTINCT rel_report.*
        FROM
            tb_report tr
                LEFT JOIN tre_report_matrix_relationship rmr ON tr.id = rmr.ReportID
                LEFT JOIN tb_test_matrix tm ON rmr.TestMatrixID = tm.ID
                LEFT JOIN tb_test_sample ts on tm.TestSampleID = ts.ID

                LEFT JOIN tb_test_matrix ttm ON ts.ID = ttm.TestSampleID
                LEFT JOIN tre_report_matrix_relationship rel_matrix on ttm.ID = rel_matrix.TestMatrixID
                LEFT JOIN tb_report rel_report on rel_report.id = rel_matrix.ReportID
        WHERE
            tr.id =#{reportId}
            and rel_report.ReportStatus not in (202,205,207)
    </select>

<!--    <update id="updateReportDueDateBatch" parameterType="java.util.List">-->
<!--        update tb_report-->
<!--        <trim prefix="set" suffixOverrides=",">-->
<!--            <foreach collection="reportList" item="item" index="index">-->
<!--                <if test="item.reportNo!=null">-->
<!--                    when ReportNo = #{item.reportNo}-->
<!--                    then ReportDueDate #{item.reportDueDate}-->
<!--                </if>-->
<!--            </foreach>-->
<!--        </trim>-->
<!--        where ReportNo in-->
<!--        <foreach collection="reportList" item="item" index="index" separator="," open="(" close=")">-->
<!--            #{item.reportNo}-->
<!--        </foreach>-->
<!--    </update>-->

    <select id="selectForUpdateCertificate" resultType="com.sgs.otsnotes.facade.model.dto.ReportDTO">
        select tr.ID id,tr.CertificateId certificateId,tr.CertificateFileCloudKey certificateFileCloudKey,tr.CertificateName certificateName,tgoi.LabCode labCode
        from tb_report tr
        inner join tb_general_order_instance tgoi on tr.OrderNo = tgoi.OrderNo
        where tr.OrderNo is not null and tgoi.OrderNo is not null
        and tgoi.LabCode is not null and tr.CertificateFileCloudKey is not null and tr.CertificateFileCloudKey != ''
        and (tr.CertificateName is null or tr.CertificateName = '')
    </select>

    <update id="updateCertificate">
        update tb_report
        set CertificateName = #{certificateName}
        where ID = #{id}
    </update>

    <select id="selectDealMRTrackingData" resultType="java.util.Map">
        SELECT DISTINCT
        gnr.OrderNo,
        rdh.softcopy_delivered_by,
        max( rdh.softcopy_delivered_date ) AS softcopy_delivered_date
        FROM
        gpn.tb_report gnr
                INNER JOIN gpn.tb_report_delivery_history rdh ON rdh.report_id = gnr.id
        WHERE

        <if test="orderNoList != null and orderNoList.size() != 0 ">
             gnr.OrderNo in
            <foreach item="item" index="index" collection="orderNoList" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        GROUP BY
        gnr.OrderNo
    </select>
    <select id="selectTrackingOrder" resultType="java.util.Map" parameterType="java.util.Map">
        select o.CompletedDate,
        o.BUCode as BUCode,
        o.orderno as OrderNo from gpo.tb_general_order o where
        <if test="req.completedStartDate != null and req.completedStartDate != ''">
            o.CompletedDate <![CDATA[ >  ]]> #{req.completedStartDate}
        </if>
        <if test="req.completedEndDate != null and req.completedEndDate != ''">
            and o.CompletedDate <![CDATA[ <=  ]]> #{req.completedEndDate}
        </if>
       and o.BuCode = 'HL'
       and o.orderStatus in (5,10)
        <if test="req.excludeOrderNoList != null and req.excludeOrderNoList.size() != 0 ">
            and o.OrderNo not in
            <foreach item="item" index="index" collection="req.excludeOrderNoList" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="queryReportNoListByReportNo"
            resultType="com.sgs.otsnotes.facade.model.rsp.report.ReportNoSearchRsp" parameterType="com.sgs.otsnotes.facade.model.req.report.ReportNoSearchReq">
        SELECT DISTINCT
        r.ReportNo,
        r.reportFlag,
        r.ActualReportNo as actualReportNo,
        r.ActualReportNo as externalReportNo
        FROM
        tb_order_index toi
        INNER JOIN tb_report r ON toi.order_no = r.OrderNo
        <where>
            <foreach collection="reportNoList" item="item" open="(" separator="or" close=")">
                r.ActualReportNo LIKE concat( '%', #{item}, '%' )
            </foreach>
            AND MATCH (toi.host_lab_id,toi.top_lab_id) AGAINST (CONCAT('lab',#{labId}) IN BOOLEAN Mode)
        </where>
        order by r.CreatedDate desc
        limit 50
    </select>

    <select id="getParentReportByReportNo" resultMap="parentReportMap">
        SELECT
            report.reportNo as byReportNo,
            parentReport.*
        FROM
            tb_report report
                INNER JOIN tb_report parentReport ON report.ParentReportNo = parentReport.ReportNo
        WHERE
            report.reportNo in
        <foreach collection="reportNoList" item="reportNo" open="(" close=")" separator=",">
            #{reportNo}
        </foreach>
    </select>

    <select id="getReportBySubReportID" resultType="com.sgs.otsnotes.facade.model.dto.report.ReportSubReportDTO">
        SELECT distinct
            sr.id as subReportId,
            sr.SubReportNo as subReportNo,
            sr.`status` as subReportStatus,
            sr.ObjectNo as subcontractNo,
            sr.Filename as subReportFileName,
            sr.ReportFileType as subReportFileType,
            sr.LanguageId as languageId,
            r.id as reportId,
            r.ReportNo as reportNo,
            r.ReportFlag as reportFlag,
            r.ReportStatus as reportStatus,
            r.OrderNo as orderNo,
            r.ActualReportNo as actualReportNo
        FROM tb_sub_report sr
                 inner join tre_report_sub_report_relationship srrel on sr.id = srrel.sub_report_id
                 inner join tb_report r on r.id = srrel.report_id

        where sr.id = #{subReportId}
    </select>

    <select id="getReportBySubReportNo" resultType="com.sgs.otsnotes.facade.model.dto.report.ReportSubReportDTO">
        SELECT distinct
            sr.id as subReportId,
            sr.SubReportNo as subReportNo,
            sr.`status` as subReportStatus,
            sr.ObjectNo as subcontractNo,
            sr.Filename as subReportFileName,
            sr.ReportFileType as subReportFileType,
            sr.LanguageId as languageId,
            r.id as reportId,
            r.ReportNo as reportNo,
            r.ReportFlag as reportFlag,
            r.ReportStatus as reportStatus,
            r.OrderNo as orderNo,
            r.ActualReportNo as actualReportNo
        FROM tb_sub_report sr
                 left join tre_report_sub_report_relationship srrel on sr.id = srrel.sub_report_id
                 left join tb_report r on r.id = srrel.report_id

        where sr.SubReportNo = #{subReportNo}
    </select>
    <select id="getReportBySubReportNoForPdf" resultType="com.sgs.otsnotes.facade.model.dto.report.ReportSubReportDTO">
        SELECT
            sr.id as subReportId,
            sr.SubReportNo as subReportNo,
            sr.`status` as subReportStatus,
            sr.ObjectNo as subcontractNo,
            sr.Filename as subReportFileName,
            sr.ReportFileType as subReportFileType,
            sr.LanguageId as languageId,
            r.id as reportId,
            r.ReportNo as reportNo,
            r.ReportFlag as reportFlag,
            r.ReportStatus as reportStatus,
            r.OrderNo as orderNo,
            r.ActualReportNo as actualReportNo
        FROM tb_sub_report sr
                 left join tb_report r on r.ReportNo = sr.ReportNo
        where sr.SubReportNo = #{subReportNo} and sr.ReportNo is not null
            limit 1
    </select>
    <select id="getReportBySubReport" resultType="com.sgs.otsnotes.facade.model.dto.report.ReportSubReportDTO">
        SELECT distinct
            sr.id as subReportId,
            sr.SubReportNo as subReportNo,
            sr.`status` as subReportStatus,
            sr.ObjectNo as subcontractNo,
            sr.Filename as subReportFileName,
            sr.ReportFileType as subReportFileType,
            sr.LanguageId as languageId,
            r.id as reportId,
            r.ReportNo as reportNo,
            r.ReportFlag as reportFlag,
            r.ReportStatus as reportStatus,
            r.OrderNo as orderNo
        FROM tb_sub_report sr
        INNER JOIN tb_general_order_instance goi ON goi.id = sr.GeneralOrderInstanceID
                 left join tre_report_sub_report_relationship srrel on sr.id = srrel.sub_report_id
                 left join tb_report r on r.id = srrel.report_id
        where sr.SubReportNo = #{subReportNo}
        AND goi.orderNo = #{orderNo}
        and sr.status not in (205,202)
        <if test="objectNo != null and objectNo != ''">
            AND sr.ObjectNo = #{objectNo}
        </if>
    </select>

    <select id="getJobTLValidateUserByReportId" resultType="java.lang.String">
        SELECT
            tl.`ValidateBy`
        FROM
            tb_job tj
                INNER JOIN tre_job_test_line_relationship tjtr ON tj.`ID` = tjtr.`JobID`
                INNER JOIN tb_test_matrix ttm ON ttm.`TestLineInstanceID` = tjtr.`TestLineInstanceID`
                INNER JOIN tre_report_matrix_relationship trmr ON trmr.`TestMatrixID` = ttm.`ID`
                INNER JOIN tb_test_line_instance tl ON tl.id = tjtr.TestLineInstanceID
        WHERE trmr.`ReportID` = #{reportId}
          AND tl.`TestEndDate` IS NOT NULL
          AND tl.`ValidateBy` IS NOT NULL
        ORDER BY tl.`TestEndDate` DESC
            LIMIT 1
    </select>

    <select id="queryReportByTl" resultType="java.lang.String">
        SELECT distinct
            rmr.ReportID
        FROM
            tre_report_matrix_relationship rmr
                INNER JOIN tb_test_matrix tm ON rmr.TestMatrixID = tm.ID
        WHERE
            tm.TestLineInstanceID = #{testLineInstanceID}
    </select>

    <update id="updateReportForRevise">
        update tb_report
        set ReportStatus=#{reportStatus},
            ModifiedBy=#{modifiedBy},
            ModifiedDate=#{modifiedDate},
            SubReportReviseFlag=#{subReportReviseFlag},
            ApproverBy=#{approverBy},
            Approver=#{approver},
            ApproverDate=#{approverDate}
        where ReportNo = #{reportNo}
    </update>

    <update id="updateReportReviseFlag">
        update tb_report
        set ModifiedDate=#{modifiedDate},
            SubReportReviseFlag=#{subReportReviseFlag}
        where ReportNo = #{reportNo}
    </update>

    <update id="updateReportNeedToAmendFlag">
        update tb_report
        set NeedToAmendFlag= #{needToAmendFlag}
        where ReportNo = #{reportNo}
        <if test="needToAmendFlag != null and needToAmendFlag == 1">
            AND ReportStatus = 208
        </if>
    </update>


    <select id="getNeedConfirmSubReportByReportId" resultType="java.lang.String">
        SELECT DISTINCT
            sr.`SubReportNo`
        FROM
            tre_report_sub_report_relationship res
                LEFT JOIN tb_sub_report sr
                          ON res.`sub_report_id` = sr.`ID`
        WHERE res.`report_id` = #{reportId}
          AND sr.`SubReportNo` IS NOT NULL
          AND  EXISTS
            (SELECT DISTINCT
                 tsr.`SubReportNo`
             FROM
                 tb_sub_report tsr
                     LEFT JOIN tre_report_sub_report_relationship ship
                               ON tsr.`ID` = ship.`sub_report_id`
                     LEFT JOIN tb_report tr
                               ON ship.`report_id` = tr.`ID`
             WHERE tsr.`SubReportNo` = sr.`SubReportNo`
               AND tr.`ReportStatus` = 203)
    </select>

    <update id="updateActualReportNo">
        update tb_report
        set ActualReportNo = #{actualReportNo}
        where ReportNo = #{reportNo}
    </update>
    <select id="getStatusRecord" resultType="com.sgs.otsnotes.dbstorages.mybatis.model.StatusInfoPO">
        SELECT
            *
        FROM
            tb_status
        WHERE
            OperationType = 1
          AND ObjectNo = #{reportNo}
        ORDER BY
            CreatedDate DESC
            LIMIT 1
    </select>

    <select id="getMaxReportDueDate" resultType="com.sgs.otsnotes.dbstorages.mybatis.model.ReportInfoPO">
        SELECT
            ID,
            OrderNo,
            ReportNo,
            ReportDueDate
        FROM
            tb_report
        WHERE
            OrderNo = #{orderNo}
          AND ReportDueDate IS NOT NULL
          AND ReportStatus NOT IN (202)
        ORDER BY
            ReportDueDate DESC
            LIMIT 1
    </select>

    <select id="queryReportCertificate" resultType="com.sgs.framework.model.report.report.v2.ReportCertificateBO">
        select
        certificate_no as certificateNo,
        certificate_type as certificateType,
        expire_date as expireDate,
        status as status,
        to_customer_flag as toCustomerFlag,
        deliver_to_customer_flag as deliverToCustomerFlag,
        remark as remark
        from tb_report_certificate
        <where>
            <choose>
                <when test="reportId != null and reportId != ''">
                    report_id = #{reportId}
                </when>
                <otherwise>
                    1!=1
                </otherwise>
            </choose>
        </where>
        order by created_date desc
    </select>

    <select id="queryReportCertificates" resultType="com.sgs.otsnotes.dbstorages.mybatis.model.ReportCertificatePO">
        select
        report_id as reportId,
        certificate_no as certificateNo,
        certificate_type as certificateType,
        status as status
        from tb_report_certificate
        <where>
            <choose>
                <when test="reportIds != null and reportIds.size() > 0">
                    report_id in
                    <foreach collection="reportIds" item="id" separator="," open="(" close=")">
                        #{id}
                    </foreach>
                </when>
                <otherwise>
                    1!=1
                </otherwise>
            </choose>
        </where>
        order by created_date desc
    </select>

    <update id="updateReportTestLineLevelByNo">
        update tb_report set ReportTestLineLevel = #{level} where ReportNo = #{reportNo}
    </update>

    <select id="queryOrderIndex" resultType="com.sgs.otsnotes.facade.model.dto.GpnOrderReportDTO">
        select
            toi.applicant_name applicantName ,
            toi.buyer_name buyerName
        from
            tb_order_index toi
        where
            order_no = #{orderNo}
            limit 1
    </select>


    <select id="queryReportToDoList" resultType="com.sgs.otsnotes.facade.model.dto.ReportToDoDTO">
        select
        trtd.*
        from
        tb_report_to_do trtd
        where
        (status is null)
        <if test="req.reportNoList != null and req.reportNoList.size() > 0">
            and trtd.reportNo in
            <foreach item="reportNo" collection="req.reportNoList" open="(" separator="," close=")">
                #{reportNo}
            </foreach>
        </if>
    </select>
    <select id="queryQrCodeToDoList" resultType="com.sgs.otsnotes.facade.model.dto.ReportToDoDTO">
        select
            trtd.*
        from
            tb_report_to_do trtd
        order by trtd.id desc
            limit 0,2575;
    </select>

    <select id="queryReportToConfirmList" resultType="com.sgs.otsnotes.facade.model.dto.ReportToDoDTO">
        select
        trtd.*
        from
        tb_report_to_do trtd
        where
        (status = 800)
        <if test="req.reportNoList != null and req.reportNoList.size() > 0">
            and trtd.reportNo in
            <foreach item="reportNo" collection="req.reportNoList" open="(" separator="," close=")">
                #{reportNo}
            </foreach>
        </if>
    </select>

    <select id="querySendReportList" resultType="com.sgs.otsnotes.facade.model.dto.ReportToDoDTO">
        select
        trtd.*
        from
        tb_report_to_do trtd
        where
        status = 300
        <if test="req.reportNoList != null and req.reportNoList.size() > 0">
            and trtd.reportNo in
            <foreach item="reportNo" collection="req.reportNoList" open="(" separator="," close=")">
                #{reportNo}
            </foreach>
        </if>
    </select>

    <select id="queryReportFileMapping" resultType="com.sgs.otsnotes.facade.model.dto.ReportFileMappingDTO" parameterType="com.sgs.otsnotes.facade.model.req.RepairReportReq">
        select
            *
        from
            tb_report_file_mapping trfm
        where
            cloudId = #{oldCloudId}
          and reportNo = #{reportNo}
    </select>

    <update id="updateReportTodo" parameterType="com.sgs.otsnotes.facade.model.dto.ReportToDoDTO">
        update
            tb_report_to_do
        set
            message = #{message},
            status = #{status}
        where
            id = #{id}
    </update>
    <update id="updateNewReportTodo" parameterType="com.sgs.otsnotes.facade.model.dto.ReportToDoDTO">
        update
            tb_report_to_do
        set
            newReportNo = #{newReportNo}
        where
            id = #{id}
    </update>

    <select id="queryReportPageRepair" parameterType="com.sgs.otsnotes.facade.model.req.report.ReportQueryPageRequest" resultType="com.sgs.otsnotes.facade.model.dto.GpnOrderReportDTO">
        select
        temp.*
        from(
        select
        'owner' fromDB,
        tr.reportFlag as reportType,
        tr.reportFlag,
        goi.OrderNo AS trueOrderNo,
        oi.parent_order_no AS parentOrderNo,
        tsr.SubReportNo AS subReportNo,
        '' AS subContractStatus,
        '' AS subContractLabCode,
        '' AS subContractLabName,
        oi.to_test_flag as toTestFlag,
        tcl.Description as conclusion,
        tcc.ConclusionSettingID as conclusionSettingID,
        tcc.ConclusionID as  conclusionId,
        tr.ID AS reportId,
        tr.AwbNo AS awbNo,
        tr.ReportNo reportNo,
        tr.ReportStatus AS reportStatus,
        tr.ApproverBy AS approvedBy,
        tr.EditorBy AS editorBy,
        tr.ReviewerBy AS reviewerBy,
        tr.CertificateFileCloudKey as accreditationLogoPath,
        tr.CertificateId as certificateId,
        tr.CertificateName as certificateName,
        tr.ExternalReportNo as referenceReportNo,
        tr.CoverPageTemplateName as coverPageTemplateName,
        tr.CoverPageTemplatePath as coverPageTemplatePath,
        tr.ApproverDate AS approvedDate,
        tr.ReportDueDate AS reportedDueDate,
        tr.CreatedDate AS createdDate,
        tr.WorkFlow AS workFlow,
        tr.SoftcopyDeliveryDate softCopyDeliveredDate,
        tr.SubReportReviseFlag as subReportReviseFlag,
        tr.SignatureLanguage as signatureLanguage,
        tr.CountryOfDestination as countryOfDestination,
        tr.Remark as remark,
        tr.testResultStatus as testResultStatus,
        IFNULL(tr.ReportTestLineLevel, 'TL') AS reportTestLineLevel,
        goi.id AS orderId,
        goi.OrderNo AS orderNo,
        oi.order_status AS orderStatus,
        goi.LabCode AS labCode,
        oi.applicant_name AS applicantName,
        oi.buyer_name AS buyerName,
        oi.applicant_no AS applicantBossNumber,
        oi.buyer_no AS buyerBossNumber,
        subcontractFrom.CustomerID as subcontractFromLabCode,
        oi.cs AS csName,
        oi.customer_reference_no as customerRefNo,
        oi.order_type orderType,
        goi.SuffixNum AS suffixNum,
        goi.TechnicalSupporter AS technicalSupporter,
        oi.operation_mode AS operationMode,
        oi.service_type as serviceLevel,
        concat_ws(',',trfRel.ExternalOrderNo) as subContractOrderNo,
        tr.actualReportNo as actualReportNo,
        tr.rootReportNo as rootReportNo,
        re.lab_section_id as labSectionId,
        IFNULL(re.test_completed_flag,0) as testCompletedFlag,
        draftDeliver.softcopy_delivered_date as draftDeliverDate
        from
        tb_report_to_do trtd
        LEFT JOIN tb_report tr on trtd.newReportNo = tr.reportNo
        LEFT JOIN tb_report_ext re on re.report_id = tr.id
        INNER JOIN tb_order_index oi on oi.order_no = tr.OrderNo
        INNER JOIN tb_general_order_instance goi on goi.OrderNo = tr.OrderNo
        LEFT JOIN gpo.tb_order_trf_relationship trfRel ON trfRel.OrderId = oi.order_id
        LEFT JOIN gpo.tb_customer_instance subcontractFrom ON subcontractFrom.GeneralOrderID = oi.order_id
        AND subcontractFrom.CustomerUsage = 'subcontractFrom'
        LEFT JOIN tb_sub_contract tsc ON goi.OrderNo = tsc.OrderNo
        LEFT JOIN tb_sub_report tsr ON tr.ReportNo = tsr.ReportNo
        LEFT JOIN tb_conclusion tcc ON tcc.ReportID = tr.ID
        AND tcc.ConclusionLevelID = 603
        LEFT JOIN tb_conclusion_list tcl ON tcl.ID = tcc.ConclusionID
        LEFT JOIN tb_report_delivery_history draftDeliver ON draftDeliver.report_id = tr.ID AND draftDeliver.delivery_type = 'DraftReport'
        <where>
            trtd.status = 200
            <if test="shareLab">
                AND (MATCH (oi.host_lab_id,oi.top_lab_id) AGAINST (CONCAT('lab',#{labId}) IN BOOLEAN Mode))
            </if>
            <if test="!shareLab">
                AND tr.labId = #{labId}
            </if>
            <if test="orderNo != null and orderNo != ''">
                AND oi.order_no = #{orderNo}
            </if>
            <if test="reportStatusList != null and reportStatusList.size() != 0 ">
                AND tr.reportStatus IN
                <foreach item="item" index="index" collection="reportStatusList" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="reportNo != null and reportNo != ''">
                AND tr.ActualReportNo = #{reportNo}
            </if>
        </where>
        group by tr.id
        ) TEMP
        order by TEMP.OrderNo desc,TEMP.ReportNo asc
    </select>

    <update id="updateReportTodoByNewReportNo" parameterType="com.sgs.otsnotes.facade.model.dto.ReportToDoDTO">
        update
            tb_report_to_do
        set
            message = #{message},
            status = #{status}
        where
            newReportNo = #{newReportNo}
    </update>

    <!--查询tb_report_delivery_history表中，delivery_type='DraftReport'的， 按report_id分组，取每个Report的softcopy_delivered_date最大的一条 将softcopy_delivered_date转成日期（yyyy-MM-dd）与当前日期相差15天的有效数据-->







    <select id="queryDraftReportDeliveryHistory" resultType="com.sgs.otsnotes.facade.model.dto.report.ReportDeliveryHistoryDTO">
        select
        trdh.report_id as reportId,
        tr.OrderNo as orderNo,
        tr.ReportNo as reportNo,
        tr.ActualReportNo as actualReportNo,
        tr.ReportStatus as reportStatus
        from
        tb_report_delivery_history trdh
        inner join tb_report tr on
        trdh.report_id = tr.id
        inner join tb_order_index toi on
        tr.OrderNo = toi.order_no
        inner join tb_auto_confirm_config tacc on tacc.product_line_id  = toi.bu_id
        <where>
            <if test="buCode!= null and buCode!= ''">
                and tacc.product_line_code = #{buCode}
            </if>
            and trdh.delivery_type = 'DraftReport'
            and trdh.softcopy_delivered_date is not null
            and toi.bu_id = tacc.product_line_id
            and tr.ReportStatus = 203
            and tr.ReportFlag in (1,2)
            and trdh.softcopy_delivered_date >'2025-03-02'
            and DATEDIFF(CURDATE(), DATE_FORMAT(trdh.softcopy_delivered_date, '%Y-%m-%d')) > tacc.days
        </where>

    </select>

    <select id="queryDraftReportNotify" resultType="com.sgs.otsnotes.facade.model.dto.report.ReportNotifyDTO">
        select
        trnl.report_id as reportId,
        tr.OrderNo as orderNo,
        tr.ReportNo as reportNo,
        tr.ActualReportNo as actualReportNo,
        tr.ReportStatus as reportStatus
        from
        tb_report_notification_log trnl
        inner join tb_report tr on
        trnl.report_id = tr.id
        inner join tb_order_index toi on
        tr.OrderNo = toi.order_no
        <where>
        tr.ReportStatus = 203
        and tr.ReportFlag in (1, 2)
        <if test="buCode != null and buCode != ''">
            and trnl.bu_code =  #{buCode}
        </if>
        and trnl.next_notification_date = CURDATE()
        </where>
    </select>

    <insert id="saveOrUpdateReportNotify" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.ReportNotifyLogInfoPO">
        INSERT INTO tb_report_notification_log
        (
        id, report_id, next_notification_date, created_date, created_by, modified_date, modified_by,
        active_indicator, bu_code,params
        )
        values (#{id,jdbcType=VARCHAR}, #{reportId,jdbcType=VARCHAR}, #{nextNotificationDate,jdbcType=DATE},
        #{createdDate,jdbcType=TIMESTAMP}, #{createdBy,jdbcType=VARCHAR}, #{modifiedDate,jdbcType=TIMESTAMP},
        #{modifiedBy,jdbcType=VARCHAR}, #{activeIndicator,jdbcType=INTEGER}, #{buCode,jdbcType=VARCHAR},
        #{params,jdbcType=LONGVARCHAR})
        ON DUPLICATE KEY UPDATE
        report_id=VALUES(report_id)
        ,next_notification_date=VALUES(next_notification_date)
        ,params=VALUES(params)
        ,modified_by=VALUES(modified_by)
        ,modified_date=VALUES(modified_date)
    </insert>

    <select id="queryLabSectionAndSubLabByReportNoList" resultType="com.sgs.otsnotes.facade.model.rsp.report.ReportLabSectionSubLabDTO">
        select
        distinct
            tr.reportNo,
            ttm.TestLineInstanceID,
            ttlr.lab_section_id labSectionId,
            tsc.SubContractLabCode
        from tb_report tr
        left join tre_report_matrix_relationship trmr on trmr.ReportID = tr.ID
        left join tb_test_matrix ttm on trmr.TestMatrixID = ttm.ID
        left join tb_testline_labsection_relationship ttlr on ttlr.test_line_instance_id = ttm.TestLineInstanceID
        left join tb_sub_contract_test_line_mapping tsctlm on tsctlm.TestLineInstanceID = ttm.TestLineInstanceID
        left join tb_sub_contract tsc on tsctlm.SubContractID = tsc.ID
        <where>
            <choose>
                <when test="reportNoList != null and reportNoList.size() != 0">
                    tr.ReportNo IN (
                    <foreach collection="reportNoList" item="item" index="index" separator =",">
                        #{item}
                    </foreach >
                    )
                </when>
                <otherwise>
                    1!=1
                </otherwise>
            </choose>
        </where>
    </select>

    <select id="selectReportByPpBaseIds" resultType="com.sgs.otsnotes.dbstorages.mybatis.model.ReportInfoPO">
        select distinct tr.ID,tr.reportNo
        from tb_report tr
        inner join tre_report_matrix_relationship trmr on trmr.ReportID = tr.ID
        inner join tb_test_matrix ttm on ttm.ID = trmr.TestMatrixID
        inner join tre_pp_test_line_relationship tptlr on tptlr.TestLineInstanceID  = ttm.TestLineInstanceID
        <where>
            <choose>
                <when test="orderNo != null and orderNo != ''">
                    tr.OrderNo = #{orderNo} and tr.ReportStatus not in (202,205,207)
                    <if test="ppBaseIds != null and ppBaseIds.size()>0">
                        and tptlr.PpBaseId in (
                        <foreach collection="ppBaseIds" item="item" index="index" separator =",">
                            #{item}
                        </foreach >
                        )
                    </if>
                </when>
                <otherwise>
                    1!=1
                </otherwise>
            </choose>
        </where>
    </select>

    <select id="queryExecuteReportList" resultType="com.sgs.otsnotes.facade.model.dto.report.ExecuteReportDTO">
        SELECT
        mreport.ID AS mainReportId,
        mreport.OrderNo AS mainOrderNo,
        mreport.ReportNo AS mainReportNo,
        mreport.ActualReportNo AS mainActualReportNo,
        mreport.ReportStatus AS mainReportStatus,
        tr2.ID AS executeReportId,
        tr2.OrderNo AS executeOrderNo,
        tr2.ReportNo AS executeReportNo,
        tr2.ActualReportNo AS executeActualReportNo,
        tr2.ReportStatus AS executeReportStatus,
        tr2.ExternalReportNo AS executeReferenceReportNo
        FROM tb_report mreport
        LEFT JOIN tb_report tr2 ON tr2.ExternalReportNo = mreport.ReportNo
        <where>
            <choose>
                <when test="(executeOrderNo!= null and executeOrderNo!= '') or (executeReportNoList != null and executeReportNoList.size()>0)">
                    mreport.OrderNo IN (
                    SELECT tsc.orderNo
                    FROM gpn.tb_report tr
                    INNER JOIN tb_subcontract_external_relationship tser ON tser.ExternalNo = tr.OrderNo
                    INNER JOIN tb_sub_contract tsc ON tsc.subContractNo = tser.subContractNo
                    <where>
                        <if test="executeOrderNo!= null and executeOrderNo!= ''">
                            and tr.OrderNo = #{executeOrderNo}
                        </if>
                        <if test="executeReportNoList != null and executeReportNoList.size()>0">
                            tr.ReportNo IN (
                            <foreach collection="executeReportNoList" item="item" index="index" separator =",">
                                #{item}
                            </foreach >
                            )
                        </if>
                    </where>
                    )
                </when>
            </choose>
        </where>
        <where>
            <if test="mainReportNoList != null and mainReportNoList.size()>0">
                mreport.ReportNo IN (
                <foreach collection="mainReportNoList" item="item" index="index" separator =",">
                    #{item}
                </foreach >
                )
            </if>
            <if test="mainOrderNo!= null and mainOrderNo!= ''">
                and mreport.OrderNo = #{mainOrderNo}
            </if>
        </where>
        order by mreport.ReportNo
    </select>

    <select id="queryReportBySubContracts" resultMap="BaseResultMap">
        select
        tr.*
        from
        tb_sub_contract tsc
        inner join tb_sub_contract_test_line_mapping tsctlm on
        tsc.ID = tsctlm.SubContractID
        inner join tb_test_matrix ttm on
        ttm.TestLineInstanceID = tsctlm.TestLineInstanceID
        inner join tre_report_matrix_relationship trmr on
        trmr.TestMatrixID = ttm.ID
        inner join tb_report tr on
        tr.ID = trmr.ReportID
        <where>
            <choose>
                <when  test="subcontractNoList != null and subcontractNoList.size()>0">
                    AND tsc.SubContractNo IN
                    <foreach item="item" index="index" collection="subcontractNoList" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </when>
                <otherwise>
                    and 1!=1
                </otherwise>
            </choose>
        </where>
    </select>
     <select id="queryRepairSubReport" resultType="java.util.HashMap">
         select distinct
         tsr2.ReportRequirement ,
         tr.id as reportId,
         tr.ReportVersion as reportVersion,
         tgo.BUCode as sourceProductLineCode,
         tgo2.BUCode as targetProductLineCode,
         tsr.id as subReportId,
         tsr.reportNo as referenceReportNo,
         tss.SubContractNo as subContractNo,
         2 as reportType,
         2 as reportFlag,
         toi.order_no as trueOrderNo,
         toi.parent_order_no as parentOrderNo,
         tr.ReportNo as subReportNo,
         tsc.SubContractLabCode as subContractLabCode,
         toi.to_test_flag as toTestFlag,
         tr.ID as reportId,
         tr.ReportNo reportNo,
         tr.ReportStatus as reportStatus,
         tr.ApproverBy as approvedBy,
         tr.ExternalReportNo as referenceReportNo,
         tr.ApproverDate as approvedDate,
         tr.WorkFlow as workFlow,
         goi.id as orderId,
         goi.OrderNo as orderNo,
         toi.order_status as orderStatus,
         goi.LabCode as labCode,
         goi.CustomerCode as applicantBossNumber,
         subcontractFrom.CustomerID as subcontractFromLabCode,
         tr.ActualReportNo,
         draftDeliver.softcopy_delivered_date as draftDeliverDate
         from
         tb_order_index toi
         inner join tb_report tr on
         toi.order_no = tr.OrderNo
         left join tb_report_ext re on
         re.report_id = tr.id
         left join tb_general_order_instance tgoi on
         tgoi.OrderNo = tr.OrderNo
         left join tb_subcontract_external_relationship tss on
         tr.OrderNo = tss.ExternalNo
         left join tb_sub_contract tsc on
         tsc.SubContractNo = tss.SubContractNo
         left join tb_general_order_instance goi on
         goi.OrderNo = tsc.OrderNo
         inner join gpo.`tb_customer_instance` subcontractFrom on
         toi.order_id = subcontractFrom.`GeneralOrderID`
         and subcontractFrom.CustomerUsage = 'subcontractFrom'
         left join tb_report_delivery_history draftDeliver on
         draftDeliver.report_id = tr.ID
         and draftDeliver.delivery_type = 'DraftReport'
         left join tb_order_index toi2 on
         toi2.order_no = toi.parent_order_no
         inner join tb_report_file draftFile on
         draftFile.ReportNo = tr.ReportNo
         and draftFile.ReportFileType = 1504
         left join tb_sub_report tsr on
         tsr.SubReportNo = tr.reportNo
         and tsr.ObjectNo = tss.SubContractNo
         inner join gpo.tb_general_order tgo on
         tgo.OrderNo = goi.OrderNo
         inner join gpo.tb_general_order tgo2 on
         tgo2.orderNo = toi.order_no
         left join tb_subcontract_requirement tsr2 on tsr2.SubContractNo  = tss.SubContractNo
         where
         tr.ReportStatus = 203
         <if test="labId != null and labId > 0">
             AND toi.host_lab_id = CONCAT('lab',#{labId})
         </if>
         and tss.subContractType in (1, 2, 4, 8, 16, 32)
         and (toi.enable_delivery_flag > 0
         or toi.order_type in ('IDB', 'IDN'))
         and toi.need_draft_flag = '1'
         and tsr2.ReportRequirement = 1
         and tsr.reportNo is null
         and (tgo.buCode not in ('IND-PL', 'AUTO'))
         and exists (
         select
         objectNo
         from
         tb_status tst
         where
         tst.objectNo = tr.ReportNo
         and tst.ObjectType = 4
         and tst.OldStatus = 203 )
         group by
         tr.id order by tr.reportNo;
     </select>

    <select id="querySubReportIdByReportId" resultType="java.lang.String">
        select
            tr.id
        from
            tre_report_sub_report_relationship trsrr
                inner join tb_sub_report tsr on trsrr.sub_report_id = tsr.id
                inner join tb_report tr on tr.ReportNo = tsr.SubReportNo
        where trsrr.report_id = #{reportId} limit 1
    </select>
</mapper>
