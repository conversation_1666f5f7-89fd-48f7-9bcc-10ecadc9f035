<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sgs.otsnotes.dbstorages.mybatis.extmapper.PPTestLineRelMapper" >
  <resultMap id="BaseResultMap" type="com.sgs.otsnotes.dbstorages.mybatis.model.PPTestLineRelationshipInfoPO" >
    <id column="ID" property="ID" jdbcType="VARCHAR" />
    <result column="PpBaseId" property="ppBaseId" jdbcType="INTEGER" />
    <result column="GeneralOrderInstanceID" property="generalOrderInstanceID" jdbcType="VARCHAR" />
    <result column="TestLineInstanceID" property="testLineInstanceID" jdbcType="VARCHAR" />
    <result column="PpArtifactRelId" property="ppArtifactRelId" jdbcType="INTEGER" />
    <result column="PPInstanceID" property="PPInstanceID" jdbcType="VARCHAR" />
    <result column="SectionID" property="sectionID" jdbcType="INTEGER" />
    <result column="SectionName" property="sectionName" jdbcType="VARCHAR" />
    <result column="SectionLevel" property="sectionLevel" jdbcType="VARCHAR" />
    <result column="PPNotes" property="PPNotes" jdbcType="VARCHAR" />
    <result column="CreatedDate" property="createdDate" jdbcType="TIMESTAMP" />
    <result column="CreatedBy" property="createdBy" jdbcType="VARCHAR" />
    <result column="ModifiedDate" property="modifiedDate" jdbcType="TIMESTAMP" />
    <result column="ModifiedBy" property="modifiedBy" jdbcType="VARCHAR" />
    <result column="aid" property="aid" jdbcType="BIGINT" />
    <result column="RootPpBaseId" property="rootPpBaseId" jdbcType="BIGINT" />
    <result column="SubPpRelSeq" property="subPpRelSeq" jdbcType="BIGINT" />
    <result column="Seq" property="seq" jdbcType="BIGINT" />
    <result column="ExtFields" property="extFields" jdbcType="VARCHAR" />
    <result column="ConstructionId" property="constructionId" jdbcType="VARCHAR" />
  </resultMap>

  <sql id="Base_Column_List" >
    ID, GeneralOrderInstanceID, TestLineInstanceID, PpArtifactRelId,PpBaseId, PPInstanceID, SectionID, SectionName,
    SectionLevel, PPNotes, CreatedDate, CreatedBy, ModifiedDate, ModifiedBy, aid,quotationTestlineInstanceID, RootPpBaseId, SubPpRelSeq,Seq,ExtFields,ConstructionId
  </sql>

    <resultMap id="sectionTestLineDTOMap" type="com.sgs.otsnotes.facade.model.info.SectionTestLineInfo">
        <id property="sectionID" column="SectionID" />
        <result property="sectionName" column="SectionName" />
        <collection property="testLineInfo" resultMap="testLineInstancePOMap"></collection>
    </resultMap>

    <resultMap id="testLineInstancePOMap" type="com.sgs.otsnotes.facade.model.info.TestLineInfo">
        <result property="ID" column="ID" />
        <result property="testLineID" column="testLineID" />
        <result property="testLineEvaluation" column="testLineEvaluation" />
        <result property="evaluationAlias" column="evaluationAlias" />
        <result property="generalOrderInstanceID" column="generalOrderInstanceID" />
    </resultMap>

    <resultMap id="ReferSampleMatrixInfoMap" type="com.sgs.otsnotes.facade.model.info.testline.ReferSampleMatrixInfo">
        <result column="GeneralOrderInstanceID" property="orderId"/>
        <result column="PpBaseId" property="ppBaseId"/>
        <result column="PpNo" property="ppNo"/>
        <result column="PpSampleRelId" property="ppSampleRelId"/>
        <result column="TestMatrixId" property="testMatrixId"/>
        <result column="MatrixGroupId" property="matrixGroupId"/>
        <result column="TestLineInstanceId" property="testLineInstanceId"/>
        <result column="TestLineId" property="testLineId"/>
        <result column="TestSampleId" property="testSampleId"/>
        <result column="SampleNo" property="sampleNo"/>
        <result column="CitationId" property="citationId"/>

        <collection property="testConditions" ofType="com.sgs.otsnotes.facade.model.info.condition.TestConditionMatrixInfo" >
            <result column="TestConditionTypeId" property="testConditionTypeId"/>
            <result column="TestConditionId" property="testConditionId"/>
        </collection>

        <collection property="analytes" ofType="com.sgs.otsnotes.facade.model.info.testline.TestLineAnalyteInfo" >
            <result column="AnalyteInstanceId" property="analyteInstanceId"/>
            <result column="AnalyteId" property="analyteId"/>
            <result column="ReportUnit" property="reportUnit"/>
        </collection>
    </resultMap>


    <select id="getPPTestLineRelListByOrderId" resultMap="BaseResultMap" parameterType="java.lang.String">
    select
      <include refid="Base_Column_List" />
    from tre_pp_test_line_relationship
    where GeneralOrderInstanceID = #{orderId}
  </select>

    <select id="getPpTestLineRelInfoByTestLineId" resultType="com.sgs.otsnotes.dbstorages.mybatis.model.PPTestLineRelationshipInfoPO">
        SELECT
            <include refid="Base_Column_List" />
        FROM tre_pp_test_line_relationship
        WHERE TestLineInstanceID= #{testLineInstanceId}
    </select>


  <insert id="batchInsert" parameterType="java.util.List">
    INSERT INTO tre_pp_test_line_relationship (
        <include refid="Base_Column_List" />
    )
    VALUES
    <foreach collection="rels" item="rel" separator=",">
    (
        #{rel.ID,jdbcType=VARCHAR}
        ,#{rel.generalOrderInstanceID,jdbcType=VARCHAR}
        ,#{rel.testLineInstanceID,jdbcType=VARCHAR}
        ,#{rel.ppArtifactRelId,jdbcType=VARCHAR}
        ,#{rel.ppBaseId,jdbcType=VARCHAR}
        ,#{rel.PPInstanceID,jdbcType=VARCHAR}
        ,#{rel.sectionID,jdbcType=INTEGER}
        ,#{rel.sectionName,jdbcType=VARCHAR}
        ,#{rel.sectionLevel,jdbcType=VARCHAR}
        ,#{rel.PPNotes,jdbcType=VARCHAR}
        ,#{rel.createdDate,jdbcType=TIMESTAMP}
        ,#{rel.createdBy,jdbcType=VARCHAR}
        ,#{rel.modifiedDate,jdbcType=TIMESTAMP}
        ,#{rel.modifiedBy,jdbcType=VARCHAR}
        ,#{rel.aid,jdbcType=BIGINT}
        ,#{rel.quotationTestlineInstanceID,jdbcType=VARCHAR}
        ,#{rel.rootPpBaseId,jdbcType=BIGINT}
        ,#{rel.subPpRelSeq,jdbcType=BIGINT}
        ,#{rel.seq,jdbcType=BIGINT}
        ,#{rel.extFields,jdbcType=LONGVARCHAR}
        ,#{rel.constructionId,jdbcType=VARCHAR}
    )
    </foreach>
    ON DUPLICATE KEY UPDATE
        TestLineInstanceID = VALUES(testLineInstanceID),
        PpArtifactRelId = VALUES(ppArtifactRelId),
        PpBaseId = VALUES(ppBaseId),
        PPInstanceID = VALUES(PPInstanceID),
        SectionID = VALUES(sectionID),
        SectionName = VALUES(sectionName),
        SectionLevel = VALUES(sectionLevel),
        PPNotes = VALUES(PPNotes),
        ModifiedDate = VALUES(modifiedDate),
        ModifiedBy = VALUES(modifiedBy),
        aid = VALUES(aid),
        RootPpBaseId = VALUES(rootPpBaseId),
        Seq = VALUES(seq),
        ExtFields = VALUES(extFields)
  </insert>

    <select id="getPPSectionSampleTLByReportId" resultType="com.sgs.otsnotes.facade.model.info.PPSectionSampleTestLineInfo" parameterType="java.lang.String">
        SELECT DISTINCT
            ship.PpBaseId AS ppId,
            pp.ppNo,
            pps.SectionId,
            tm.Id AS MatrixId,
            tm.TestSampleId,
            tl.ID AS TestLineId,
            rel.Id AS ppSampleRelId
        FROM tre_pp_test_line_relationship ship
        INNER JOIN tb_general_order_instance goi ON goi.id = ship.GeneralOrderInstanceID and ship.GeneralOrderInstanceID=goi.id
        INNER JOIN tb_report tr ON tr.orderNo = goi.OrderNo
        INNER JOIN tb_test_line_instance tl ON tl.ID = ship.TestLineInstanceID
        INNER JOIN tb_test_matrix tm ON tl.id = tm.TestLineInstanceID AND tm.ActiveIndicator = 1
        INNER JOIN tre_pp_sample_relationship rel ON rel.TestSampleID = tm.TestSampleID AND ship.ID = rel.PPTLRelID
        LEFT JOIN tre_trims_pp_artifact_relationship ppship on ppship.Id = ship.PpArtifactRelId AND ship.PpArtifactRelId > 0
        LEFT JOIN tb_trims_pp_baseinfo pp ON pp.Id = ship.PpBaseId AND pp.PpVersionId = ppship.PpVersionId
        LEFT JOIN tb_trims_pp_section_baseinfo pps ON pps.PpVersionId = pp.PpVersionId AND pps.ID = ppship.SectionBaseId
        WHERE tr.id = #{reportId}
          AND tl.TestLineStatus != 706
          AND tl.TestLineStatus != 707
          AND <![CDATA[ (tl.TestLineType & 1) != 1]]>
    </select>

    <select id="getPPTestLineRelCitationInfoByOrderId"
            parameterType="list"
            resultType="com.sgs.otsnotes.facade.model.info.testline.PpTestLineRelCitationInfo">
        SELECT DISTINCT
            o.orderNo,
            tl.testLineID,
            tl.CitationBaseId,
            tl.CitationVersionId,
            tl.TestLineStatus,
            tl.TestLineVersionId,
            tl.TestLineType,
            IFNULL( tr.PpArtifactRelId, 0 ) AS PpArtifactRelId
        FROM
            tre_pp_test_line_relationship tr
        INNER JOIN tb_test_line_instance tl ON tr.GeneralOrderInstanceID = tl.GeneralOrderInstanceID
            AND tr.TestLineInstanceID = tl.ID
        left join tb_general_order_instance o ON o.Id = tl.GeneralOrderInstanceID
        WHERE
        tr.GeneralOrderInstanceID in
        <foreach collection="orderIds" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        and tl.TestLineStatus in(701,705,702,707,708)
    </select>

    <select id="getPPTestLineRelListByIds"
            parameterType="list"
            resultType="com.sgs.otsnotes.dbstorages.mybatis.model.PPTestLineRelationshipInfoPO">
        SELECT
            b.id,
            b.PpArtifactRelId,
            b.TestLineInstanceID
        FROM
            tre_pp_test_line_relationship a
        JOIN tre_pp_test_line_relationship b ON a.TestLineInstanceID = b.TestLineInstanceID
        <where>
            a.id in
            <foreach collection="list" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </where>


    </select>

    <select id="getReferSampleMatrixInfoList" resultMap="ReferSampleMatrixInfoMap">
        SELECT DISTINCT
        pptl.GeneralOrderInstanceID
        ,pptl.PpBaseId
        ,pptl.TestLineInstanceId
        ,tm.Id AS TestMatrixId
        ,tm.MatrixGroupId
        ,tl.TestLineId
        ,pps.Id AS PpSampleRelId
        ,pps.TestSampleId
        ,sa.SampleNo
        ,tl.CitationId
        ,tc.TestConditionTypeId
        ,tc.TestConditionId
        ,ai.Id AS AnalyteInstanceId
        ,ai.AnalyteId
        ,ai.ReportUnit
        FROM tre_pp_sample_relationship pps
        INNER JOIN tre_pp_test_line_relationship pptl ON pptl.Id = pps.PPTLRelID
        INNER JOIN tb_test_line_instance tl ON tl.Id = pptl.TestLineInstanceId
        INNER JOIN tb_test_matrix tm ON tm.TestSampleId = pps.TestSampleId AND tm.TestLineInstanceId = pptl.TestLineInstanceId -- AND tm.MatrixGroupId = 0
        INNER JOIN tb_test_sample sa on sa.ID = tm.TestSampleId
        LEFT JOIN tb_test_condition_instance tc ON tc.TestSampleId = tm.TestSampleId AND tc.TestMatrixId = tm.Id AND tc.ActiveIndicator = 1
        LEFT JOIN tb_analyte_instance ai ON ai.TestLineInstanceID = tl.Id
        WHERE pps.TestSampleId IN
        <foreach collection="sampleIds" separator="," open="(" close=")" item="sampleId" index="index">
            #{sampleId}
        </foreach>
    </select>
    <select id="getPPInfoByPPTLRelIds"
            parameterType="list"
            resultType="com.sgs.otsnotes.facade.model.rsp.assignsample.SamplePPSRsp">

        SELECT
            rel.id as ppTlRelId,
            pp.id as ppId,
            pp.ppName as ppName
        FROM
            tre_pp_test_line_relationship rel
        left JOIN tre_trims_pp_artifact_relationship trimsrel ON rel.PpArtifactRelId = trimsrel.id AND rel.PpArtifactRelId > 0
        left JOIN tb_trims_pp_baseinfo pp ON pp.id = rel.ppBaseId AND pp.PpVersionId = trimsrel.PpVersionId
        <where>
            rel.id in
            <foreach collection="list" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </where>

    </select>
    <select id="getByIds" parameterType="list" resultType="com.sgs.otsnotes.dbstorages.mybatis.model.PPTestLineRelationshipInfoPO">
        select
        <include refid="Base_Column_List"/>
        from tre_pp_test_line_relationship
        <where>
            id in
            <foreach collection="list" separator="," open="(" close=")" item="item">
                #{item}
            </foreach>
        </where>
    </select>

    <delete id="batchDelete" parameterType="java.util.List">
        DELETE FROM tre_pp_test_line_relationship
         WHERE ID IN
        <foreach collection="list" item="delTestLineId" open="(" close=")" separator=",">
            #{delTestLineId}
        </foreach>
    </delete>
    <select id="getPPTestLineRelList" parameterType="java.lang.String" resultMap="BaseResultMap" >
        select distinct
            rel.*
        from tre_pp_test_line_relationship rel
        INNER JOIN tb_general_order_instance o ON o.Id = rel.GeneralOrderInstanceID
        where o.OrderNo = #{orderNo}
    </select>

    <delete id="deletePPTestLineRelationship" parameterType="com.sgs.otsnotes.facade.model.info.testline.DelPPTestLineRelInfo">
        <if test="testLineInstanceIds != null and testLineInstanceIds.size() != 0" >
            DELETE al FROM tb_analyte_instance_multiplelanguage al
            INNER JOIN tb_analyte_instance ai ON ai.Id = al.AnalyteInstanceID
            WHERE ai.TestLineInstanceID IN
            <foreach collection="testLineInstanceIds" item="testLineInstanceId" open="(" close=")" separator=",">
                #{testLineInstanceId}
            </foreach>;

            DELETE FROM tb_testline_labsection_relationship
            WHERE test_line_instance_id in
            <foreach collection="testLineInstanceIds" item="testLineInstanceId" open="(" close=")" separator=",">
                #{testLineInstanceId}
            </foreach>;
        </if>

        DELETE ppl FROM tb_pp_instance_multiplelanguage ppl
        INNER JOIN tb_pp_instance pp ON pp.Id = ppl.PPInstanceId
        INNER JOIN tre_pp_test_line_relationship pptl ON pptl.PPInstanceID = pp.Id AND pptl.GeneralOrderInstanceID = pp.GeneralOrderInstanceID
        WHERE pptl.Id IN
        <foreach collection="ppTestLineRelIds" item="ppTestLineRelId" open="(" close=")" separator=",">
            #{ppTestLineRelId}
        </foreach>;

        DELETE ppcrel FROM tre_pp_condition_relationship_multiplelanguage ppcrel
        INNER JOIN tre_pp_condition_relationship pcr ON pcr.Id = ppcrel.PpConditionRelationshipID
        INNER JOIN tre_pp_test_line_relationship ptlr ON ptlr.Id = pcr.PPTestLineRelID
        WHERE ptlr.Id IN
        <foreach collection="ppTestLineRelIds" item="ppTestLineRelId" open="(" close=")" separator=",">
            #{ppTestLineRelId}
        </foreach>;

        DELETE FROM tre_pp_condition_relationship
        WHERE PPTestLineRelID IN
        <foreach collection="ppTestLineRelIds" item="ppTestLineRelId" open="(" close=")" separator=",">
            #{ppTestLineRelId}
        </foreach>;

        DELETE FROM tre_pp_sample_relationship
        WHERE PPTLRelID IN
        <foreach collection="ppTestLineRelIds" item="ppTestLineRelId" open="(" close=")" separator=",">
            #{ppTestLineRelId}
        </foreach>;

        DELETE FROM tre_pp_test_line_relationship
        WHERE ID IN
        <foreach collection="ppTestLineRelIds" item="ppTestLineRelId" open="(" close=")" separator=",">
            #{ppTestLineRelId}
        </foreach>;

        DELETE FROM tre_pp_test_line_relationship_multiplelanguage
        WHERE PpTestLineRelationshipID IN
        <foreach collection="ppTestLineRelIds" item="ppTestLineRelId" open="(" close=")" separator=",">
            #{ppTestLineRelId}
        </foreach>;
    </delete>


    <select id="getPPBaseIdList" resultType="com.sgs.otsnotes.facade.model.info.limitgroup.RequirmentPPInfo">
        SELECT DISTINCT
            PPInstanceId
            ,PpBaseId
        FROM tre_pp_test_line_relationship
        WHERE GeneralOrderInstanceID = #{orderId}
    </select>

  <select id="queryPPTestLineListByOrderId" resultType="com.sgs.otsnotes.facade.model.dto.MatrixTableDTO">
SELECT * FROM (
        SELECT DISTINCT tlbase.TestLineVersionId AS testLineVersionId,
            ppbase.PpVersionId AS ppVersionId,
          pptlrel.id AS ppTlRelId,
          ppbase.id AS ppBaseId,
          ppbase.PpName AS ppName,
          pa.specificNotes AS ppNotes,
          pptlrel.SectionName,
          tlbase.TestLineId AS testLineId,
          IF(pa.id IS NULL OR TRIM(pa.id)='',IF(citationrel.id IS NULL OR TRIM(citationrel.id)='','',citationrel.EvaluationAlias),pa.EvaluationAlias) AS testItem,
          CONCAT(citationrel.CitationName,IF(citationrel.CitationSectionName IS NULL,'',CONCAT(',',citationrel.CitationSectionName))) AS testStandard,
          citationrel.CitationSectionName as CitationSectionName,
            subcontract.SubContractLabCode as subcontractLabCode,
          CONCAT(IFNULL(labsectionbase.LabSectionName,''),IF(subcontract.SubContractLabCode IS NULL,'',CONCAT('||',subcontract.SubContractLabCode))) AS labSection,
          tl.id AS testLineInstanceId,
          tl.ProductLineAbbr AS bu,
          tl.TestLineStatus AS testLineStatus,
          tl.OrdertestLineRemark AS remark,
          0 AS matrixGroupId,
          tl.TestLineBaseId AS testLineBaseId,
          tl.CitationBaseId AS citationBaseId,
          tl.CitationId AS citationId,
          tl.CitationVersionId as citationVersionId,
          tl.LabSectionBaseId AS labSectionBaseId,
          pptlrel.PpArtifactRelId AS ppTlRelBaseId,
          pptlrel.GeneralOrderInstanceID AS orderId,
          pptlrel.aid AS aId,

          subcontract.id as subContractId,
          tl.ConditionStatus AS conditionStatus,
          tl.SampleSegegrationWIID AS sampleSegegrationWIID,
          tl.SampleSegegrationWIText AS sampleSegegrationWIText,
          tl.labTeamCode,
          tl.reportSeq,
          tl.PendingFlag,
           tl.OrderSeq,
           ppbase.PpNo,
           psb.SectionLevel,
           pa.TestLineSeq,
           tl.TestItemNo,
           tl.CreatedDate,
           tl.TestLineType AS testLineType,
           tl.CustomerTestLineName AS customerTestLineName,
           tl.CustomerTestLineNameCN AS customerTestLineNameCN,
           pptlrel.Seq,
           tl.ClientStandard as clientStandard,
           ocr.CitationBaseId AS ppCitationBaseId,
           tl.DocumentReviewFlag as documentReviewFlag

      FROM
          tre_pp_test_line_relationship pptlrel
          LEFT JOIN tre_trims_pp_artifact_relationship pa ON pptlrel.PpArtifactRelId = pa.id
          LEFT JOIN tb_trims_pp_baseinfo ppbase ON pa.PpVersionId = ppbase.PpVersionId AND ppbase.Id = pptlrel.PpBaseId
          JOIN tb_test_line_instance tl ON pptlrel.TestLineInstanceID = tl.ID
          LEFT JOIN tre_order_citation_relationship ocr ON pptlrel.PpBaseId = ocr.PpBaseId
          AND tl.GeneralOrderInstanceID = ocr.OrderId

      JOIN tb_trims_testline_baseinfo tlbase ON tl.TestLineBaseId = tlbase.Id
          LEFT JOIN tb_trims_labsection_baseinfo labsectionbase ON tl.LabSectionBaseId = labsectionbase.id
          LEFT JOIN tb_trims_artifact_citation_relationship citationrel ON tl.CitationBaseId = citationrel.id
          LEFT JOIN tb_sub_contract_test_line_mapping sub_tl_map ON sub_tl_map.TestLineInstanceID = tl.ID
          LEFT JOIN tb_sub_contract subcontract ON sub_tl_map.SubContractID = subcontract.id
          LEFT JOIN tb_trims_pp_section_baseinfo psb ON psb.id = pa.SectionBaseId
      WHERE
          pptlrel.GeneralOrderInstanceID = #{orderId}
      <if test="NewJobFlag != null and NewJobFlag != '' and NewJobFlag == true" >
          AND sub_tl_map.ID IS NULL
          AND labsectionbase.LabSectionName  IS NOT NULL AND labsectionbase.LabSectionName != ''
          AND tl.TestLineStatus NOT IN (706,707)
      </if>
      <if test="copyTlFlag != null and copyTlFlag != '' and copyTlFlag == true" >
          AND tl.TestLineStatus NOT IN (706)
      </if>
      <if test="testLineInstanceIds != null and testLineInstanceIds.size() != 0" >
          AND tl.ID IN
          <foreach collection="testLineInstanceIds" item="testLineInstanceId" open="(" close=")" separator=",">
              #{testLineInstanceId}
          </foreach>
      </if>
      UNION ALL
      SELECT DISTINCT tlbase.TestLineVersionId AS testLineVersionId,
        NULL AS ppVersionId,
        NULL AS ppTlRelId,
        NULL AS ppBaseId,
        NULL AS ppName,
        NULL AS ppNotes,
        NULL AS SectionName,
        tlbase.TestLineId AS testLineId,
        citationrel.EvaluationAlias AS testItem,
        citationrel.CitationName AS testStandard,
        citationrel.CitationSectionName as CitationSectionName,
      subcontract.SubContractLabCode as subcontractLabCode,
      CONCAT(IFNULL(labsectionbase.LabSectionName,''),IF(subcontract.SubContractLabCode IS NULL,'',CONCAT('||',subcontract.SubContractLabCode))) AS labSection,
        tl.id AS testLineInstanceId,
        tl.ProductLineAbbr AS bu,
        tl.TestLineStatus AS testLineStatus,
        tl.OrdertestLineRemark AS remark,
        tm.MatrixGroupId AS matrixGroupId,
        tl.TestLineBaseId AS testLineBaseId,
        tl.CitationBaseId AS citationBaseId,
        tl.CitationId AS citationId,
        tl.CitationVersionId as citationVersionId,
        tl.LabSectionBaseId AS labSectionBaseId,
        NULL AS ppTlRelBaseId,
        NULL AS orderId,
        NULL AS aId,
        subcontract.id as subContractId,
        tl.ConditionStatus AS conditionStatus,
        tl.SampleSegegrationWIID AS sampleSegegrationWIID,
        tl.SampleSegegrationWIText AS sampleSegegrationWIText,
        tl.labTeamCode,
        tl.reportSeq,
        tl.PendingFlag,
          tl.OrderSeq,
          pb.PpNo,
          psb.SectionLevel,
          par.TestLineSeq,
          tl.TestItemNo,
          tl.CreatedDate,
          tl.TestLineType AS testLineType,
          tl.CustomerTestLineName AS customerTestLineName,
          tl.CustomerTestLineNameCN AS customerTestLineNameCN,
          ptr.Seq,
          tl.ClientStandard as clientStandard,
          ocr.CitationBaseId AS ppCitationBaseId,
          tl.DocumentReviewFlag as documentReviewFlag

      FROM tb_test_matrix tm
        JOIN tb_test_line_instance tl ON tm.TestLineInstanceID = tl.ID
        JOIN tb_trims_testline_baseinfo tlbase ON tl.TestLineBaseId = tlbase.Id
        LEFT JOIN tb_trims_labsection_baseinfo labsectionbase ON tl.LabSectionBaseId = labsectionbase.id
        LEFT JOIN tb_trims_artifact_citation_relationship citationrel ON tl.CitationBaseId = citationrel.id
        LEFT JOIN tb_sub_contract_test_line_mapping sub_tl_map ON sub_tl_map.TestLineInstanceID = tl.ID
        LEFT JOIN tb_sub_contract subcontract ON sub_tl_map.SubContractID = subcontract.id
        LEFT JOIN tre_pp_test_line_relationship ptr ON tl.id = ptr.TestLineInstanceID
        LEFT JOIN tb_trims_pp_baseinfo pb ON ptr.PpBaseId = pb.id
        LEFT JOIN tre_trims_pp_artifact_relationship par ON par.id = ptr.PpArtifactRelId
        LEFT JOIN tb_trims_pp_section_baseinfo psb ON psb.id = par.SectionBaseId
        LEFT JOIN tre_order_citation_relationship ocr ON ptr.PpBaseId = ocr.PpBaseId
        AND tl.GeneralOrderInstanceID = ocr.OrderId
      WHERE tm.GeneralOrderInstanceID = #{orderId} and tm.MatrixGroupId > 0
      <if test="NewJobFlag != null and NewJobFlag != '' and NewJobFlag == true" >
          AND sub_tl_map.ID IS NULL
          AND labsectionbase.LabSectionName  IS NOT NULL AND labsectionbase.LabSectionName != ''
          AND tl.TestLineStatus NOT IN (706,707)
      </if>
      <if test="copyTlFlag != null and copyTlFlag != '' and copyTlFlag == true" >
          AND tl.TestLineStatus NOT IN (706)
          AND tm.MatrixGroupId = 0
      </if>
      <if test="testLineInstanceIds != null and testLineInstanceIds.size() != 0" >
          AND tl.ID IN
          <foreach collection="testLineInstanceIds" item="testLineInstanceId" open="(" close=")" separator=",">
              #{testLineInstanceId}
          </foreach>
      </if>
      ) AS T
ORDER BY ISNULL(OrderSeq),
         OrderSeq,
         Seq,
         ISNULL(PpNo),
         PpNo,
         ISNULL(SectionLevel),
         SectionLevel,
         ISNULL(TestLineSeq),
         TestLineSeq,
         TestItemNo,
         CreatedDate
  </select>


  <select id="listPpEvaluationAliasByTestLineIds" resultType="com.sgs.otsnotes.facade.model.dto.PPTestLineEvaluationAliasDTO">
    SELECT pp_tl_rel.TestLineInstanceID,pp_tl_rel.PpArtifactRelId,pp_tl_rel.PpBaseId,pp_art_rel.EvaluationAlias,pp_tl_rel.CreatedDate,pp_art_rel.SpecificNotes AS PPNotes
FROM  tre_pp_test_line_relationship pp_tl_rel
JOIN tre_trims_pp_artifact_relationship pp_art_rel ON pp_tl_rel.PpArtifactRelId = pp_art_rel.Id AND pp_tl_rel.PpArtifactRelId > 0
WHERE pp_tl_rel.TestLineInstanceID IN
    <foreach collection="testLineInstanceIds" separator="," open="(" close=")" item="id" index="index">
      #{id}
    </foreach>
  </select>

    <select id="listPpEvaluationAliasMultiLanguageByTestLineIds" resultType="com.sgs.otsnotes.facade.model.dto.PPTestLineEvaluationAliasDTO">
        SELECT DISTINCT pp_tl_rel.TestLineInstanceID
            ,pp_tl_rel.PpArtifactRelId
            ,pp_tl_rel.PpBaseId
            ,pp_art_lang.EvaluationAlias
            ,pp_tl_rel.CreatedDate
            ,pp_art_lang.SpecificNotes as PPNotes
        FROM  tre_pp_test_line_relationship AS pp_tl_rel
        JOIN tre_order_language_relationship AS order_lang_rel
        ON order_lang_rel.OrderId = pp_tl_rel.GeneralOrderInstanceID
        AND order_lang_rel.ObjectBaseId = pp_tl_rel.PpArtifactRelId
        AND order_lang_rel.LangType=9
        AND order_lang_rel.LanguageId=#{languageId}
        JOIN tre_trims_pp_artifact_language AS pp_art_lang ON order_lang_rel.LangBaseId = pp_art_lang.LangId AND pp_art_lang.LanguageId = #{languageId}
        WHERE pp_tl_rel.TestLineInstanceID IN
        <foreach collection="testLineInstanceIds" separator="," open="(" close=")" item="id" index="index">
            #{id}
        </foreach>
    </select>

  <select id="listPPBaseInfoByTestLineInstanceId" resultType="com.sgs.otsnotes.dbstorages.mybatis.model.PPBaseInfoPO">
    SELECT pp_base.* FROM tre_pp_test_line_relationship pp_tl_rel
JOIN tb_trims_pp_baseinfo pp_base ON pp_tl_rel.PpBaseId = pp_base.Id
WHERE pp_tl_rel.TestLineInstanceID= #{testLineInstanceId}
  </select>

    <select id="getNewPpTestLineInfoList" resultType="com.sgs.otsnotes.facade.model.info.testline.NewPpTestLineInfo">
        SELECT DISTINCT
            tlrel.TestLineInstanceId
            ,tlrel.Id AS PpTestLineRelId
            ,oldpp.PpNo AS OldPpNo
            ,newpp.Id AS PpBaseId
            ,newpp.PpVersionId
            ,oldpp.PpVersionId AS OldPpVersionId
            ,pparel.ArtifactVersionId AS OldTestLineVersionId
            ,tlrel.RootPpBaseId
            ,tlrel.PpBaseId AS OldPpBaseId
            ,pparel.ArtifactId
            ,tlrel.SectionId AS OldSectionId
            ,tlrel.Seq AS seq
            ,tlrel.aid AS aid
            ,pps.SectionId
            ,pps.SectionLevel
            ,lang.LangId AS LangBaseId
            ,lang.LanguageId
        FROM tre_pp_test_line_relationship tlrel
        LEFT JOIN tre_trims_pp_artifact_relationship pparel ON pparel.Id = tlrel.PpArtifactRelId AND tlrel.PpArtifactRelId > 0
        LEFT JOIN tb_trims_pp_baseinfo oldpp ON oldpp.PpVersionId = pparel.PpVersionId AND oldpp.Id = tlrel.PpBaseId
        LEFT JOIN tb_trims_pp_baseinfo newpp ON newpp.PpNo = oldpp.PpNo AND newpp.PpStatus = 1
        LEFT JOIN tb_trims_pp_section_baseinfo pps ON pps.Id = pparel.SectionBaseId AND pparel.SectionBaseId > 0
        LEFT JOIN tre_trims_pp_artifact_language lang ON lang.PpArtifactRelId = tlrel.PpArtifactRelId AND lang.LangStatus=1
        WHERE tlrel.GeneralOrderInstanceID = #{orderId}
    </select>

    <resultMap id="sectionTestLineSampleDTOMap" type="com.sgs.otsnotes.facade.model.info.SectionTestLineSampleInfo">
        <id property="sectionID" column="SectionID" />
        <result property="sectionName" column="SectionName" />
        <collection property="testLineSampleList" resultMap="testLineInstanceMap"></collection>
    </resultMap>

    <resultMap id="testLineInstanceMap" type="com.sgs.otsnotes.facade.model.info.TestLineSampleInfo">
        <result property="testLineInstanceID" column="testLineInstanceID" />
        <result property="testLineID" column="testLineID" />
        <result property="testLineEvaluation" column="testLineEvaluation" />
        <result property="evaluationAlias" column="evaluationAlias" />
        <result property="generalOrderInstanceID" column="generalOrderInstanceID" />
        <result property="ppTestLineRelID" column="ppTestLineRelID" />
        <collection property="samplePOs" resultMap="samplePOsMap"></collection>
    </resultMap>
    <resultMap id="samplePOsMap" type="com.sgs.otsnotes.facade.model.info.SampleInfo">
        <result property="id" column="TestSampleID" />
        <result property="ppSameleRelID" column="ppSameleRelID" />
        <result property="sampleID" column="TestSampleID"/>
    </resultMap>

    <update id="batchUpdateConstructionId" parameterType="list">
        <foreach collection="updateList" item="item" separator=";">
            UPDATE
            tre_pp_test_line_relationship
            SET
            ModifiedBy=#{item.modifiedBy},
            ModifiedDate=#{item.modifiedDate},
            ConstructionId=#{item.constructionId},
            RootPpBaseId=#{item.rootPpBaseId}
            WHERE ID=#{item.ID}
        </foreach>
    </update>

</mapper>