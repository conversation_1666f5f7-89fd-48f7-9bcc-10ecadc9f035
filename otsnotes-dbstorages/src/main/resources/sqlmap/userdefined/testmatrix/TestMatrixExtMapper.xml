<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sgs.otsnotes.dbstorages.mybatis.extmapper.testmatrix.TestMatrixExtMapper" >
    <resultMap id="BaseResultMap" type="com.sgs.otsnotes.dbstorages.mybatis.model.TestMatrixPO" >
        <id column="ID" property="ID" jdbcType="VARCHAR" />
        <result column="GeneralOrderInstanceID" property="generalOrderInstanceID" jdbcType="VARCHAR" />
        <result column="TestLineInstanceID" property="testLineInstanceID" jdbcType="VARCHAR" />
        <result column="TestSampleID" property="testSampleID" jdbcType="VARCHAR" />
        <result column="TestConditionGroupID" property="testConditionGroupID" jdbcType="VARCHAR" />
        <result column="ConditionID" property="conditionID" jdbcType="INTEGER" />
        <result column="ConditionName" property="conditionName" jdbcType="VARCHAR" />
        <result column="MatrixGroupId" property="matrixGroupId" jdbcType="INTEGER" />
        <result column="MatrixStatus" property="matrixStatus" jdbcType="INTEGER" />
        <result column="ActiveIndicator" property="activeIndicator" jdbcType="BIT" />
        <result column="CreatedDate" property="createdDate" jdbcType="TIMESTAMP" />
        <result column="CreatedBy" property="createdBy" jdbcType="VARCHAR" />
        <result column="ModifiedDate" property="modifiedDate" jdbcType="TIMESTAMP" />
        <result column="ModifiedBy" property="modifiedBy" jdbcType="VARCHAR" />
        <result column="ExternalMatrixInstanceId" property="externalMatrixInstanceId" jdbcType="VARCHAR" />
    </resultMap>

    <select id="selectOrderTestMatrixes" resultType="com.sgs.otsnotes.facade.model.testmatrix.info.OrderTestMatrixInfo">
        SELECT
            goi.ID as orderId,
            goi.OrderNo as orderNo,
            tm.id as matrixId,
            tm.matrixNo as matrixNo,
            tm.ActiveIndicator activeIndicator,
            tm.MatrixStatus matrixStatus,
            tli.id as testLineInstanceId,
            tli.TestLineBaseId testLineBaseId,
            tli.TestLineID as testLineId,
            tli.TestItemNo as testItemNo,
            tli.CitationBaseId as citationBaseId,
            tli.LabSectionBaseId as labSectionBaseId,
            ptlr.PpBaseId as ppBaseId,
            ptlr.PpArtifactRelId as ppArtifactRelId,
            tli.CitationId as citationId,
            tli.TestLineStatus testLineStatus,
            tli.OrderSeq as orderSeq,
            tli.ReportSeq as reportSeq,
            ts.ID as sampleId,
            ts.SampleNo as sampleNo,
            ts.SampleSeq as sampleSeq
        FROM
            tb_general_order_instance goi
                INNER JOIN tb_test_matrix tm ON goi.ID = tm.GeneralOrderInstanceID
                INNER JOIN tb_test_line_instance tli ON tm.TestLineInstanceID = tli.ID
                INNER JOIN tre_pp_test_line_relationship ptlr on ptlr.TestLineInstanceID = tli.id
                INNER JOIN tb_test_sample ts ON tm.TestSampleID = ts.id
        <where>
            <if test="orderNo != null and orderNo != '' and orderId != null and orderId != ''">
                And 1!=1
            </if>
            <if test="orderNo != null and orderNo != ''">
                AND goi.OrderNo = #{orderNo}
            </if>
            <if test="orderId != null and orderId != ''">
                AND goi.ID = #{orderId}
            </if>
            <if test="testLineInstanceIdList != null and testLineInstanceIdList.size()>0">
                AND tli.ID IN
                <foreach item="item" index="index" collection="testLineInstanceIdList" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <choose>
                <when test="filterActive == 'Yes'">
                    AND tm.ActiveIndicator = 1
                </when>
                <when test="filterActive == 'No'">
                    AND tm.ActiveIndicator = 0
                </when>
            </choose>
        </where>
        order by tli.OrderSeq,ts.SampleSeq
    </select>

    <select id="selectReportTestMatrixes" resultType="com.sgs.otsnotes.facade.model.testmatrix.info.ReportTestMatrixInfo">
        SELECT distinct
            r.id as reportId,
            r.reportNo as reportNo,
            goi.ID as orderId,
            goi.OrderNo as orderNo,
            tm.id as matrixId,
            tm.matrixNo as matrixNo,
            tm.ActiveIndicator activeIndicator,
            tm.MatrixStatus matrixStatus,
            tli.id as testLineInstanceId,
            tli.TestLineBaseId testLineBaseId,
            tli.TestLineID as testLineId,
            tli.TestItemNo as testItemNo,
            tli.CitationBaseId as citationBaseId,
            tli.LabSectionBaseId as labSectionBaseId,
            ptlr.PpBaseId as ppBaseId,
            ptlr.PpArtifactRelId as ppArtifactRelId,
            tli.CitationId as citationId,
            tli.TestLineStatus testLineStatus,
            tli.PendingFlag pendingFlag,
            tli.OrderSeq as orderSeq,
            tli.ReportSeq as reportSeq,
            ts.ID as sampleId,
            ts.SampleNo as sampleNo,
            ts.SampleSeq as sampleSeq
        FROM
            tb_report r
                INNER JOIN tb_general_order_instance goi on r.OrderNo = goi.OrderNo
                INNER JOIN tb_test_matrix tm ON goi.ID = tm.GeneralOrderInstanceID
                INNER JOIN tre_report_matrix_relationship rmr ON tm.id = rmr.TestMatrixID and rmr.ReportID = r.id
                INNER JOIN tb_test_line_instance tli ON tm.TestLineInstanceID = tli.ID
                INNER JOIN tre_pp_test_line_relationship ptlr on ptlr.TestLineInstanceID = tli.id
                INNER JOIN tb_test_sample ts ON tm.TestSampleID = ts.id
        <where>
            <if test="reportId != null and reportId != '' and reportNo != null and reportNo != ''">
                And 1!=1
            </if>
            <if test="reportNo != null and reportNo != ''">
                AND r.ReportNo = #{reportNo}
            </if>
            <if test="reportId != null and reportId != ''">
                r.id = #{reportId}
            </if>
            <choose>
                <when test="filterActive == 'Yes'">
                    AND tm.ActiveIndicator = 1
                </when>
                <when test="filterActive == 'No'">
                    AND tm.ActiveIndicator = 0
                </when>
            </choose>
        </where>
        order by goi.OrderNo desc,tli.OrderSeq,ptlr.seq, `tli`.`TestItemNo`

    </select>

    <select id="selectByTestMatrixIds" resultType="com.sgs.otsnotes.dbstorages.mybatis.model.TestMatrixExtPO">
        SELECT
        id,
        test_matrix_id as testMatrixId,
        data_entry_mode as dataEntryMode,
        display_in_report as displayInReport,
        show_test_line_remark as showTestLineRemark,
        test_line_remark as testLineRemark,
        show_test_line_fail_remark as showTestLineFailRemark,
        test_line_fail_remark as testLineFailRemark,
        reporting_test_line_list_type as reportingTestLineListType,
        protocol_template as protocolTemplate,
        active_indicator as activeIndicator,
        created_date createdDate,
        created_by as createdBy,
        modified_date as modifiedDate,
        modified_by as modifiedBy,
        last_modified_timestamp as lastModifiedTimestamp,
        ppnotes_display_in_report as ppnotesDisplayInReport,
        application_factor_id as applicationFactorId
        FROM
        tb_test_matrix_ext
        <where>
            <choose>
                <when test="list != null and list.size() > 0">
                    AND test_matrix_id IN
                    <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </when>
                <otherwise>
                    AND 1!=1
                </otherwise>
            </choose>
        </where>
    </select>

    <insert id="insertBatch" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.TestMatrixExtPO">
        insert into tb_test_matrix_ext (test_matrix_id,application_factor_id,active_indicator) values
        <foreach collection="list" separator="," item="item" >
            ( #{item.testMatrixId},#{item.applicationFactorId},1 )
        </foreach>
    </insert>

    <update id="updateAFByTestMatrixId" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.TestMatrixExtPO">
        <foreach collection="list" item="item" index="index" separator=";" open="" close="">
            update tb_test_matrix_ext
            <set>
                application_factor_id = #{item.applicationFactorId},
                modified_date = now()
            </set>
                where test_matrix_id = #{item.testMatrixId}
        </foreach>
    </update>

    <select id="selectByTestLineAndSampleNo" resultMap="BaseResultMap">
        SELECT
            m.*
        FROM
            tb_test_matrix m
                INNER JOIN tb_test_sample ts ON ts.id = m.TestSampleID
        <where>
                <choose>
                    <when test="testLineInstanceIdList != null and testLineInstanceIdList.size() > 0">
                        AND m.TestLineInstanceID IN
                        <foreach item="item" index="index" collection="testLineInstanceIdList" open="(" separator="," close=")">
                            #{item}
                        </foreach>
                    </when>
                    <otherwise>
                        AND 1!=1
                    </otherwise>
                </choose>
            <choose>
                <when test="sampleNo!= null and sampleNo != ''">
                    AND ts.SampleNo = #{sampleNo}
                </when>
                <otherwise>
                    AND 1!=1
                </otherwise>
            </choose>
        </where>
    </select>

    <insert id="insertBatchTestMatrixExt" parameterType="com.sgs.otsnotes.dbstorages.mybatis.model.TestMatrixExtPO">
        insert into tb_test_matrix_ext
        (test_matrix_id,data_entry_mode,display_in_report,
        show_test_line_remark,test_line_remark,show_test_line_fail_remark,test_line_fail_remark,
        reporting_test_line_list_type,protocol_template,active_indicator,
        created_date,created_by,modified_date,modified_by,
        ppnotes_display_in_report,application_factor_id) values
        <foreach collection="list" separator="," item="item" >
            ( #{item.testMatrixId,jdbcType=VARCHAR},
            #{item.dataEntryMode,jdbcType=INTEGER},
            #{item.displayInReport,jdbcType=BIT},
            #{item.showTestLineRemark,jdbcType=BIT},
            #{item.testLineRemark,jdbcType=VARCHAR},
            #{item.showTestLineFailRemark,jdbcType=BIT},
            #{item.testLineFailRemark,jdbcType=VARCHAR},
            #{item.reportingTestLineListType,jdbcType=INTEGER},
            #{item.protocolTemplate,jdbcType=VARCHAR},
            #{item.activeIndicator,jdbcType=BIT},
            #{item.createdDate,jdbcType=TIMESTAMP},
            #{item.createdBy,jdbcType=VARCHAR},
            #{item.modifiedDate,jdbcType=TIMESTAMP},
            #{item.modifiedBy,jdbcType=VARCHAR},
            #{item.ppnotesDisplayInReport,jdbcType=BIT},
            #{item.applicationFactorId,jdbcType=VARCHAR})
        </foreach>
    </insert>
</mapper>