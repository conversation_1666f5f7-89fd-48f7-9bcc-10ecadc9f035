<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sgs.otsnotes.dbstorages.mybatis.extmapper.TestSampleMapper" >
    <resultMap id="BaseResultMap" type="com.sgs.otsnotes.dbstorages.mybatis.model.TestSampleInfoPO" >
        <id column="ID" property="ID" jdbcType="VARCHAR" />
        <result column="SampleParentID" property="sampleParentID" jdbcType="VARCHAR" />
        <result column="SampleNo" property="sampleNo" jdbcType="VARCHAR" />
        <result column="SampleType" property="sampleType" jdbcType="INTEGER" />
        <result column="SampleSeq" property="sampleSeq" jdbcType="INTEGER" />
        <result column="OrderNo" property="orderNo" jdbcType="VARCHAR" />
        <result column="Category" property="category" jdbcType="VARCHAR" />
        <result column="Description" property="description" jdbcType="VARCHAR" />
        <result column="Composition" property="composition" jdbcType="VARCHAR" />
        <result column="Color" property="color" jdbcType="VARCHAR" />
        <result column="SampleDescforReport" property="sampleDescforReport" jdbcType="VARCHAR" />
        <result column="SampleRemark" property="sampleRemark" jdbcType="VARCHAR" />
        <result column="EndUse" property="endUse" jdbcType="VARCHAR" />
        <result column="Material" property="material" jdbcType="VARCHAR" />
        <result column="OtherSampleInfo" property="otherSampleInfo" jdbcType="VARCHAR" />
        <result column="Applicable" property="applicable" jdbcType="BIT" />
        <result column="ReferDataType" property="referDataType" jdbcType="VARCHAR" />
        <result column="ActiveIndicator" property="activeIndicator" jdbcType="BIT" />
        <result column="CreatedDate" property="createdDate" jdbcType="TIMESTAMP" />
        <result column="CreatedBy" property="createdBy" jdbcType="VARCHAR" />
        <result column="ModifiedDate" property="modifiedDate" jdbcType="TIMESTAMP" />
        <result column="ModifiedBy" property="modifiedBy" jdbcType="VARCHAR" />
        <result column="Version" property="version" jdbcType="INTEGER" />
    </resultMap>

    <resultMap id="BaseSampleResultMap" type="com.sgs.otsnotes.facade.model.info.TestSampleInfo">
        <id column="Id" property="sampleId" />
        <result column="SampleParentId" property="sampleParentId"/>
        <result column="SampleType" property="sampleType" />
        <result column="SampleNo" property="sampleNo" jdbcType="VARCHAR" />
        <result column="Color" property="color" jdbcType="VARCHAR" />
        <result column="Description" property="description" jdbcType="VARCHAR" />
        <collection property="sampleGroupIds" ofType="java.lang.String" >
            <result column="SampleGroupId"/>
        </collection>
    </resultMap>

    <resultMap id="PPTLSampleResultMap" extends="BaseResultMap" type="com.sgs.otsnotes.facade.model.dto.PPTLSampleInfoDTO">
        <result column="PPTLRelID" property="ppTlRelId" jdbcType="VARCHAR" />
    </resultMap>

    <resultMap id="ConclusionSampleResultMap" type="com.sgs.otsnotes.facade.model.rsp.conclusion.ConclusionSampleInfo" extends="BaseSampleResultMap">
    </resultMap>

    <resultMap id="TestSampleSimplifyInfoResultMap" type="com.sgs.otsnotes.facade.model.rsp.sample.TestSampleSimplifyInfo" extends="BaseSampleResultMap">
        <result column="ActiveIndicator" property="activeIndicator" jdbcType="VARCHAR" />
    </resultMap>

    <sql id="Sample_Column_List">
		ID, SampleParentID, SampleNo, SampleType, SampleSeq, OrderNo, Category, Description, Composition, Color,
		SampleDescforReport, SampleRemark, EndUse, Material, OtherSampleInfo, ActiveIndicator,
		CreatedBy, CreatedDate, ModifiedBy, ModifiedDate,Applicable,ReferDataType
	</sql>


    <select id="getSamplesByOrderNoAndSampleNos" resultMap="BaseResultMap">
        select * from tb_test_sample
        <where>
            OrderNo = #{orderNo}
            and SampleNo IN
            <foreach collection="sampleNos" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </where>
        order by SampleSeq asc
    </select>

    <select id="getSamplesByOrderNos" resultMap="BaseResultMap">
        select * from tb_test_sample
        <where>
            <choose>
                <when test="orderNos!= null and orderNos.size()!= 0">
                    OrderNo in
                    <foreach collection="orderNos" item="item" open="(" close=")" separator=",">
                        #{item}
                    </foreach>
                </when>
                <otherwise>
                    1!=1
                </otherwise>
            </choose>
        </where>
        order by SampleSeq asc
    </select>

    <select id="getSampleByOrderNo" resultMap="BaseResultMap">
        select * from tb_test_sample
        <where>
            OrderNo = #{orderNo}
        </where>
        order by SampleSeq asc
    </select>

    <select id="getSampleByMatrixReportNo" resultType="com.sgs.otsnotes.dbstorages.mybatis.model.TestSampleInfoPO">
        SELECT DISTINCT
            ts.*
        FROM tb_test_sample ts
        INNER JOIN tb_test_matrix tm ON tm.TestSampleID = ts.ID
        INNER JOIN tre_report_matrix_relationship rmr ON rmr.TestMatrixID = tm.ID
        WHERE rmr.ReportID = #{reportId}
    </select>


    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO tb_test_sample (
        ID, SampleParentID, SampleNo,
        SampleType, SampleSeq, OrderNo,
        Category, Description, Composition,
        Color, SampleDescforReport, SampleRemark,
        EndUse, Material, OtherSampleInfo,
        Applicable, ReferDataType, ActiveIndicator,
        CreatedDate, CreatedBy, ModifiedDate,
        ModifiedBy
        )
        VALUES
        <foreach collection="samples" index="index" item="sample" open="" close="" separator=",">
            (
            #{sample.ID,jdbcType=VARCHAR}, #{sample.sampleParentID,jdbcType=VARCHAR}, #{sample.sampleNo,jdbcType=VARCHAR},
            #{sample.sampleType,jdbcType=INTEGER}, #{sample.sampleSeq,jdbcType=INTEGER}, #{sample.orderNo,jdbcType=VARCHAR},
            #{sample.category,jdbcType=VARCHAR}, #{sample.description,jdbcType=VARCHAR}, #{sample.composition,jdbcType=VARCHAR},
            #{sample.color,jdbcType=VARCHAR}, #{sample.sampleDescforReport,jdbcType=VARCHAR}, #{sample.sampleRemark,jdbcType=VARCHAR},
            #{sample.endUse,jdbcType=VARCHAR}, #{sample.material,jdbcType=VARCHAR}, #{sample.otherSampleInfo,jdbcType=VARCHAR},
            #{sample.applicable,jdbcType=BIT}, #{sample.referDataType,jdbcType=VARCHAR}, #{sample.activeIndicator,jdbcType=BIT},
            #{sample.createdDate,jdbcType=TIMESTAMP}, #{sample.createdBy,jdbcType=VARCHAR}, #{sample.modifiedDate,jdbcType=TIMESTAMP},
            #{sample.modifiedBy,jdbcType=VARCHAR}
            )
        </foreach>
        ON DUPLICATE KEY UPDATE
        SampleParentID = VALUES(sampleParentID),
        SampleNo = VALUES(sampleNo),
        SampleType = VALUES(sampleType),
        SampleSeq = VALUES(sampleSeq),
        Category = VALUES(category),
        Description = VALUES(description),
        Color = VALUES(color),
        SampleRemark = VALUES(sampleRemark),
        EndUse = VALUES(endUse),
        Material = VALUES(material),
        OtherSampleInfo = VALUES(otherSampleInfo),
        Applicable = VALUES(applicable),
        ActiveIndicator = VALUES(activeIndicator),
        ModifiedDate = VALUES(modifiedDate),
        ModifiedBy = VALUES(modifiedBy)
    </insert>

    <delete id="batchDelete">
        DELETE FROM tb_test_condition_instance WHERE TestSampleID IN
        <foreach collection="sampleIds" item="sampleId" open="(" close=")" separator=",">
            #{sampleId}
        </foreach>;

        DELETE rel FROM tre_pp_sample_relationship rel
        INNER JOIN tb_test_sample ts ON ts.Id = rel.TestSampleID
        WHERE ts.ID IN
        <foreach collection="sampleIds" item="sampleId" open="(" close=")" separator=",">
            #{sampleId}
        </foreach>;

        DELETE FROM tb_test_sample_group WHERE SampleId IN
        <foreach collection="sampleIds" item="sampleId" open="(" close=")" separator=",">
            #{sampleId}
        </foreach>;

        DELETE FROM tb_test_sample WHERE Id IN
        <foreach collection="sampleIds" item="sampleId" open="(" close=")" separator=",">
            #{sampleId}
        </foreach>;
    </delete>

    <select id="getTestSampleList" resultMap="BaseSampleResultMap" parameterType="java.lang.String">
        SELECT DISTINCT
            s.Id,
            s.SampleParentId,
            s.SampleType,
			s.SampleNo,
			s.Color,
			s.Description,
            g.SampleGroupId
        FROM tb_test_sample s
        LEFT JOIN tb_test_sample_group g ON g.SampleId = s.Id
        WHERE s.OrderNo = #{orderNo}
    </select>

    <select id="getTestSampleIdList" resultMap="BaseResultMap">
        SELECT *
        FROM tb_test_sample
        WHERE Id IN
        <foreach collection="sampleIds" item="sampleId" open="(" close=")" separator=",">
            #{sampleId}
        </foreach>
    </select>

    <select id="queryAllSample" parameterType="java.lang.String"  resultType="com.sgs.otsnotes.dbstorages.mybatis.extmodel.AssignSampleInfo">
        select
        ID, SampleParentID, SampleNo, SampleType, SampleSeq, OrderNo, Category, Description, Composition, Color,
		SampleDescforReport, SampleRemark, EndUse, Material, OtherSampleInfo, ActiveIndicator,
		CreatedBy, CreatedDate, ModifiedBy, ModifiedDate,Applicable,ReferDataType
		FROM
		tb_test_sample
		WHERE OrderNo = #{orderNo} And ActiveIndicator=1 and Applicable = 0
		order by sampleSeq asc
    </select>


    <select id="getSampleInfoByMatrixNo"  resultMap="BaseResultMap">
        SELECT
        ts.*
        FROM
        tb_test_matrix tm
        LEFT JOIN tb_test_sample ts ON ts.id = tm.TestSampleID
        <where>
            tm.MatrixNo = #{matrixNo}
        </where>
        ORDER BY SampleSeq ASC
    </select>

    <select id="queryAllTestSample" parameterType="java.lang.String"  resultType="com.sgs.otsnotes.dbstorages.mybatis.model.TestSampleInfoPO">
        select
        ID, SampleParentID, SampleNo, SampleType, SampleSeq, OrderNo, Category, Description, Composition, Color,
		SampleDescforReport, SampleRemark, EndUse, Material, OtherSampleInfo, ActiveIndicator,
		CreatedBy, CreatedDate, ModifiedBy, ModifiedDate,Applicable,ReferDataType
		FROM
		tb_test_sample
		WHERE OrderNo = #{orderNo}
		order by sampleSeq asc
    </select>
    <!--新增或更新，当主键id存在的时候就更新数据-->
    <insert id="saveOrUpdateSamplesBatch" parameterType="java.util.List">
        INSERT INTO tb_test_sample
        (ID, SampleParentID, SampleNo, SampleType, SampleSeq, OrderNo, Description, Composition, Color,
        SampleDescforReport, SampleRemark, EndUse, OtherSampleInfo, ActiveIndicator,
        CreatedBy, CreatedDate, ModifiedBy, ModifiedDate, Category, Material, Applicable) values
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.ID}, #{item.sampleParentID}, #{item.sampleNo}, #{item.sampleType},
            #{item.sampleSeq}, #{item.orderNo}, #{item.description}, #{item.composition},
            #{item.color}, #{item.sampleDescforReport}, #{item.sampleRemark}, #{item.endUse},
            #{item.otherSampleInfo}, #{item.activeIndicator},
            #{item.createdBy}, #{item.createdDate}, #{item.modifiedBy}, #{item.modifiedDate},#{item.category}, #{item.material},#{item.applicable})
        </foreach>
        ON DUPLICATE KEY UPDATE
        SampleNo=VALUES(SampleNo),SampleSeq=VALUES(SampleSeq),Description=VALUES(Description), Composition=VALUES(Composition),
        Color=VALUES(Color), SampleRemark=VALUES(SampleRemark), EndUse=VALUES(EndUse),ModifiedBy=VALUES(ModifiedBy),ModifiedDate=VALUES(ModifiedDate),
        OtherSampleInfo=VALUES(OtherSampleInfo),Material=VALUES(Material),Applicable=VALUES(Applicable)
    </insert>

    <select id="getSampleInfoListByOrderNo" resultMap="TestSampleSimplifyInfoResultMap" parameterType="java.lang.String">
        SELECT DISTINCT
            s.Id,
            s.SampleNo,
            s.SampleParentId,
            s.Category,
            s.SampleType,
            s.SampleSeq,
            g.SampleGroupId,
            s.ActiveIndicator
        FROM tb_test_sample s
        LEFT JOIN tb_test_sample_group g ON g.SampleId = s.Id
        WHERE s.OrderNo = #{orderNo}
    </select>

    <select id="getSampleInfoListByTl" resultMap="TestSampleSimplifyInfoResultMap">
        SELECT DISTINCT
            s.Id,
            s.SampleNo,
            s.SampleParentId,
            s.Category,
            s.SampleType,
            s.SampleSeq,
            g.SampleGroupId,
            s.ActiveIndicator
        FROM
            tb_test_sample s
                LEFT JOIN tb_test_sample_group g ON g.SampleId = s.Id
        WHERE
            s.OrderNo = #{orderNo}
          AND s.ID IN (
            SELECT
                tm.TestSampleID
            FROM
                tb_test_matrix tm
            WHERE
                tm.TestLineInstanceID IN
                <foreach collection="testLineIds" open="(" close=")" separator="," item="item">
                    #{item}
                </foreach>
              and tm.ActiveIndicator = 1
        )
    </select>


    <select id="getConclusionSampleByOrderNo" resultMap="ConclusionSampleResultMap" parameterType="java.lang.String">
        SELECT DISTINCT
            s.Id,
            s.SampleNo,
            s.SampleParentId,
            s.Category,
            s.SampleType,
            s.SampleSeq,
            g.SampleGroupId
        FROM tb_test_sample s
        LEFT JOIN tb_test_sample_group g ON g.SampleId = s.Id
        WHERE s.OrderNo = #{orderNo}
    </select>

    <select id="querySampleByReportIdGroupByMatrixGroupId" resultType="com.sgs.otsnotes.facade.model.rsp.SampleListRsp" parameterType="java.lang.String">

		SELECT DISTINCT
			sa.ID,
			sa.SampleParentID,
			sa.SampleNo,
			sa.SampleType,
			sa.SampleSeq,
			sa.OrderNo,
			sa.Description,
			sa.Composition,
			sa.Color,
			sa.SampleDescforReport,
			sa.SampleRemark,
			sa.EndUse,
			sa.OtherSampleInfo,
			sa.ActiveIndicator,
			sa.CreatedBy,
			sa.CreatedDate,
			sa.ModifiedBy,
			sa.ModifiedDate,
			tm.MatrixGroupId
		FROM
			tb_test_sample sa
		JOIN tb_test_matrix tm ON sa.ID = tm.TestSampleID
		JOIN tre_report_matrix_relationship trmr ON tm.ID = trmr.TestMatrixID
		JOIN tb_report re ON re.id = trmr.ReportID
		WHERE
			re.id = #{reportId}
		ORDER BY
			sa.sampleSeq,tm.MatrixGroupId
	</select>


    <select id="querySampleBySubcontractNos" resultType="com.sgs.otsnotes.facade.model.rsp.SampleListRsp" parameterType="java.util.List">
        SELECT DISTINCT
            sa.ID,
            sa.SampleParentID,
            sa.SampleNo,
            sa.SampleType,
            sa.SampleSeq,
            sa.OrderNo,
            sa.Description,
            sa.Composition,
            sa.Color,
            sa.SampleDescforReport,
            sa.SampleRemark,
            sa.EndUse,
            sa.OtherSampleInfo,
            sa.ActiveIndicator,
            sa.CreatedBy,
            sa.CreatedDate,
            sa.ModifiedBy,
            sa.ModifiedDate,
            tm.MatrixGroupId
        FROM
            tb_test_sample sa
                JOIN tb_test_matrix tm ON sa.ID = tm.TestSampleID
                JOIN tb_test_line_instance tl ON tl.ID = tm.TestLineInstanceID
        WHERE
            tl.ID IN (
                SELECT
                tl.ID
                FROM
                tb_sub_contract s
                INNER JOIN tb_sub_contract_test_line_mapping sm ON s.ID = sm.SubContractID
                LEFT JOIN tb_test_line_instance tl ON tl.ID = sm.TestLineInstanceID
                WHERE
                s.`Status` != 3
                AND s.SubContractNo IN
                <foreach collection="subContractNos" open="(" close=")" separator="," item="item">
                    #{item}
                </foreach>
            )
        ORDER BY
            sa.sampleSeq,
            tm.MatrixGroupId
    </select>

    <update id="batchUpdateSampleNo" parameterType="java.util.List">
        <foreach collection="list" item="item" index="index" open="" close="" separator=";">
            UPDATE tb_test_sample
            SET SampleNo = #{item.sampleNo},
            Description = #{item.description},
            ModifiedBy = #{item.modifiedBy},
            ModifiedDate = now()
            WHERE ID = #{item.ID}
        </foreach>
    </update>

    <select id="queryOriginalSampleByReportId" resultType="com.sgs.otsnotes.facade.model.dto.ConclusionSampleDTO" parameterType="java.lang.String">
		SELECT DISTINCT
			sa.ID,
			sa.SampleParentID,
			sa.SampleNo,
			sa.SampleType,
			sa.SampleSeq,
			sa.OrderNo,
			sa.Description,
			sa.Composition,
			sa.Color,
			sa.SampleDescforReport,
			sa.SampleRemark,
			sa.EndUse,
			sa.OtherSampleInfo,
			sa.ActiveIndicator,
			sa.CreatedBy,
			sa.CreatedDate,
			sa.ModifiedBy,
			sa.ModifiedDate
		FROM
			tb_test_sample sa
		JOIN tb_report re ON re.OrderNo = sa.OrderNo
		WHERE
			re.id = #{reportId}
		AND sa.sampleType = 101
		ORDER BY
			sa.sampleSeq
	</select>

    <select id="querySampleByReportId" resultType="com.sgs.otsnotes.facade.model.dto.ConclusionSampleDTO" parameterType="java.lang.String">
		SELECT DISTINCT
			sa.ID,
			sa.SampleParentID,
			sa.SampleNo,
			sa.SampleType,
			sa.SampleSeq,
			sa.OrderNo,
			sa.Description,
			sa.Composition,
			sa.Color,
			sa.SampleDescforReport,
			sa.SampleRemark,
			sa.EndUse,
			sa.OtherSampleInfo,
			sa.ActiveIndicator,
			sa.CreatedBy,
			sa.CreatedDate,
			sa.ModifiedBy,
			sa.ModifiedDate
		FROM
			tb_test_sample sa
		JOIN tb_report re ON re.OrderNo = sa.OrderNo
		WHERE
			re.id = #{reportId}
		AND (sa.sampleType = 101 or sa.sampleType = 104 or sa.sampleType = 102 or sa.sampleType = 103)
		ORDER BY
			sa.sampleSeq
	</select>

    <select id="getMixSampleByTestLineInstanceID" resultType="com.sgs.otsnotes.facade.model.dto.ConclusionSampleDTO" parameterType="java.lang.String">
		select DISTINCT
		sa.ID, sa.SampleParentID, sa.SampleNo, sa.SampleType, sa.SampleSeq, sa.OrderNo, sa.Description, sa.Composition, sa.Color,
		sa.SampleDescforReport, sa.SampleRemark, sa.EndUse, sa.OtherSampleInfo, sa.ActiveIndicator,
		sa.CreatedBy, sa.CreatedDate, sa.ModifiedBy, sa.ModifiedDate
		from tb_test_sample sa
		join tb_test_matrix tm on sa.ID = tm.TestSampleID
		where tm.testlineInstanceID = #{testLineInstanceID}
		and sa.sampleType = 104 and sa.ActiveIndicator=1 ORDER BY sampleSeq
	</select>

    <select id="getSampleByMixSampleID" resultType="com.sgs.otsnotes.facade.model.dto.ConclusionSampleDTO" parameterType="java.lang.String">
		select DISTINCT
		sa.ID, sa.SampleParentID, sa.SampleNo, sa.SampleType, sa.SampleSeq, sa.OrderNo, sa.Description, sa.Composition, sa.Color,
		sa.SampleDescforReport, sa.SampleRemark, sa.EndUse, sa.OtherSampleInfo, sa.ActiveIndicator,
		sa.CreatedBy, sa.CreatedDate, sa.ModifiedBy, sa.ModifiedDate
		from tb_test_sample sa
		join tb_test_sample_group sg on sa.id = sg.sampleGroupId
		where sg.sampleID = #{sampleID} and sa.activeIndicator = 1 ORDER BY sampleseq
	</select>

    <select id="getParentSampleBySampleID" resultType="com.sgs.otsnotes.facade.model.dto.ConclusionSampleDTO" parameterType="java.lang.String">

        SELECT
        <include refid="Sample_Column_List" />
        FROM
        tb_test_sample
        WHERE ID = #{sampleParentID}
    </select>

    <!-- getByOrder -->
    <select id="getPPSampleRelsOrder" resultType="com.sgs.otsnotes.dbstorages.mybatis.model.PPSampleRelationshipInfoPO" parameterType="java.lang.String">
		SELECT t_pr.* FROM tre_pp_sample_relationship t_pr
		JOIN tre_pp_test_line_relationship t_lr ON t_pr.PPTLRelID=t_lr.ID
		WHERE t_lr.GeneralOrderInstanceID=#{generalOrderInstanceID};
	</select>

    <select id="queryAllChemSample" resultType="com.sgs.otsnotes.dbstorages.mybatis.model.TestSampleInfoPO" parameterType="java.lang.String">
		SELECT
		  *
		FROM
		 tb_test_sample
		 WHERE OrderNo = #{orderNo} and category = 'C' and Applicable = 0 order by sampleSeq asc
	</select>

    <select id="queryAllPhyChildSample" resultType="com.sgs.otsnotes.dbstorages.mybatis.model.TestSampleInfoPO" parameterType="java.lang.String">
		SELECT
		 *
		FROM
		tb_test_sample
		WHERE OrderNo = #{orderNo} and category = 'P' and sampleType = 102 order by sampleSeq asc
	</select>

    <select id="querySampleMatrixInfo"
            resultType="com.sgs.otsnotes.facade.model.dto.sample.TestSampleMatrixDTO">
        SELECT DISTINCT
            ts.*,
            tm.MatrixNo as matrixNo,
            tm.TestLineInstanceID as testLineInstanceId
        FROM
            tb_test_sample ts
                LEFT JOIN tb_test_matrix tm ON tm.TestSampleID = ts.ID
        <where>
            <choose>
                <when test="orderNoList!= null and orderNoList.size()>0">
                    and ts.OrderNo in
                    <foreach collection="orderNoList" item="item" open="(" close=")" separator=",">
                        #{item}
                    </foreach>
                </when>
                <when test="matrixNoList!= null and matrixNoList.size()>0">
                    and tm.MatrixNo in
                    <foreach collection="matrixNoList" item="item" open="(" close=")" separator=",">
                        #{item}
                    </foreach>
                </when>
                <otherwise>
                    and 1!=1
                </otherwise>
            </choose>
        </where>
        order by sampleSeq asc
    </select>


    <select id="querySampleByOrderNoAndType" resultType="com.sgs.otsnotes.dbstorages.mybatis.model.TestSampleInfoPO" parameterType="java.lang.String">
        SELECT
        *
        FROM
        tb_test_sample
        <where>
            1=1
            <if test="sampleType != null ">
                and SampleType = #{sampleType}
            </if>
            <if test="orderNo != null and orderNo != '' ">
                and OrderNo = #{orderNo}
            </if>
        </where>
        order by SampleSeq asc
    </select>

    <insert id="saveOrUpdateSamples" parameterType="java.util.List">
        INSERT INTO tb_test_sample
        (ID, SampleParentID, SampleNo, SampleType, SampleSeq, OrderNo, Description, Composition, Color,
        SampleDescforReport, SampleRemark, EndUse, OtherSampleInfo, ActiveIndicator,
        CreatedBy, CreatedDate, ModifiedBy, ModifiedDate, Category, Material, Applicable) values
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.id}, #{item.sampleParentID}, #{item.sampleNo}, #{item.sampleType},
            #{item.sampleSeq}, #{item.orderNo}, #{item.description}, #{item.composition},
            #{item.color}, #{item.sampleDescforReport}, #{item.sampleRemark}, #{item.endUse},
            #{item.otherSampleInfo}, #{item.activeIndicator},
            #{item.createdBy}, now(), #{item.modifiedBy}, now(),#{item.category}, #{item.material},#{item.applicable})
        </foreach>
        ON DUPLICATE KEY UPDATE
        SampleNo=VALUES(SampleNo),SampleSeq=VALUES(SampleSeq),Description=VALUES(Description), Composition=VALUES(Composition),
        Color=VALUES(Color), SampleRemark=VALUES(SampleRemark), EndUse=VALUES(EndUse),ModifiedBy=VALUES(ModifiedBy),ModifiedDate=now(),
        OtherSampleInfo=VALUES(OtherSampleInfo),Material=VALUES(Material),Applicable=VALUES(Applicable)
    </insert>

    <select id="querySampleGroupByOrderNo" resultType="com.sgs.otsnotes.dbstorages.mybatis.model.TestSampleGroupPO" parameterType="java.lang.String">
		SELECT tsg.* from tb_test_sample_group tsg join tb_test_sample ts on tsg.SampleID = ts.ID
		where (ts.SampleType = 104 or ts.SampleType = 105) and ts.OrderNo = #{orderNo}
	</select>

    <update id="batchUpdateSampleGroupStatus" parameterType="java.util.List">
        <foreach collection="list" item="item" index="index" open="" close="" separator=";">
            UPDATE
            tb_test_sample_group
            SET
            ModifiedBy=#{item.modifiedBy},ModifiedDate=now(),ActiveIndicator=#{item.activeIndicator}
            WHERE id=#{item.id}
        </foreach>
    </update>

    <select id="querySampleByTestLineId" resultType="com.sgs.otsnotes.facade.model.rsp.MatrixSampleRsp" parameterType="java.lang.String">
		SELECT DISTINCT
			sa.ID as sampleId,
			sa.SampleNo,
			tli.id as testlineInstanceId,
            tli.testlineId as testlineId,
            tm.id as matrixId,
			re.id as reportId,
            tm.GeneralOrderInstanceID
		FROM
			tb_test_sample sa
		JOIN tb_test_matrix tm ON sa.ID = tm.TestSampleID
		JOIN tb_test_line_instance tli on tli.id=tm.TestLineInstanceID
		JOIN tre_report_matrix_relationship trmr ON tm.ID = trmr.TestMatrixID
		JOIN tb_report re ON re.id = trmr.ReportID
		WHERE
			tm.testLineInstanceID = #{testLineInstanceID}
		ORDER BY
			sa.sampleSeq,tm.MatrixGroupId
	</select>


    <select id="getExclusiveMixSampleList" resultType="com.sgs.otsnotes.facade.model.dto.ConclusionSampleDTO" parameterType="java.lang.String">
		SELECT

		sa.ID, sa.SampleParentID, sa.SampleNo, sa.SampleType, sa.SampleSeq, sa.OrderNo, sa.Description, sa.Composition, sa.Color,
		sa.SampleDescforReport, sa.SampleRemark, sa.EndUse, sa.OtherSampleInfo, sa.ActiveIndicator,
		sa.CreatedBy, sa.CreatedDate, sa.ModifiedBy, sa.ModifiedDate

		FROM tb_test_sample sa join tb_test_matrix ma on sa.id = ma.testsampleID
		where sa.SampleType != 104 and ma.testLineInstanceId = #{testLineInstanceID} and ma.ActiveIndicator = 1
		and sa.ActiveIndicator = 1 order by sa.SampleSeq


	</select>

    <select id="queryMixSampleByIds" resultType="com.sgs.otsnotes.dbstorages.mybatis.model.TestSampleInfoPO" parameterType="java.lang.String">
        SELECT
        sa.*
        FROM
        tb_test_sample sa
        JOIN tb_test_sample_group sg ON sa.ID = sg.SampleID
        WHERE
        sg.SampleGroupID IN
        <foreach collection="list" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        and sa.activeIndicator = 1
    </select>

    <select id="querySampleByMixSampleIds" resultType="com.sgs.otsnotes.facade.model.dto.ConclusionSampleDTO" parameterType="list">
        SELECT DISTINCT
        sa.*
        FROM
        tb_test_sample sa
        JOIN tb_test_sample_group tg ON tg.SampleGroupID = sa.ID
        JOIN tb_test_matrix tm on tm.TestSampleID = tg.SampleID
        WHERE
        tm.ID in
        <foreach collection="list" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        ORDER BY
        SampleSeq;
    </select>
</mapper>