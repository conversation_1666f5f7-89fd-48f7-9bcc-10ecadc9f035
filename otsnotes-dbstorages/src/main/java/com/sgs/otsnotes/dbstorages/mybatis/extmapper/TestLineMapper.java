package com.sgs.otsnotes.dbstorages.mybatis.extmapper;

import com.sgs.otsnotes.dbstorages.mybatis.config.TrimsShuntLabMethod;
import com.sgs.otsnotes.dbstorages.mybatis.extmodel.TestLineConfirmMatrixInfo;
import com.sgs.otsnotes.dbstorages.mybatis.extmodel.UpdateTestlineInstanceFileIdReq;
import com.sgs.otsnotes.dbstorages.mybatis.extmodel.masterlist.PretreatmentDO;
import com.sgs.otsnotes.dbstorages.mybatis.model.*;
import com.sgs.otsnotes.facade.model.dto.*;
import com.sgs.otsnotes.facade.model.dto.testline.MatrixTestlineInstanceDTO;
import com.sgs.otsnotes.facade.model.dto.testline.OrderTestLineRelDTO;
import com.sgs.otsnotes.facade.model.dto.trimslocaldata.QueryCitationNameDTO;
import com.sgs.otsnotes.facade.model.gpn.testline.info.TLInstanceBaseInfo;
import com.sgs.otsnotes.facade.model.gpn.testline.info.TLInstanceDetailInfo;
import com.sgs.otsnotes.facade.model.gpn.testline.req.TLInstanceBaseReq;
import com.sgs.otsnotes.facade.model.gpn.testline.req.TLInstanceDetailReq;
import com.sgs.otsnotes.facade.model.gpn.testline.rsp.TestItemRsp;
import com.sgs.otsnotes.facade.model.info.MatrixTestLineRelInfo;
import com.sgs.otsnotes.facade.model.info.ReportSummaryInfo;
import com.sgs.otsnotes.facade.model.info.SubContractTestLineInfo;
import com.sgs.otsnotes.facade.model.info.TestLineBreakDownInfo;
import com.sgs.otsnotes.facade.model.info.conclusion.CalculateConclusionFlagInfo;
import com.sgs.otsnotes.facade.model.info.limitgroup.OrderPpSampleInfo;
import com.sgs.otsnotes.facade.model.info.limitgroup.RequirmentTestLineInfo;
import com.sgs.otsnotes.facade.model.info.testline.*;
import com.sgs.otsnotes.facade.model.po.TestLineReportPo;
import com.sgs.otsnotes.facade.model.req.EquipmentTestLineReq;
import com.sgs.otsnotes.facade.model.req.SaveTestLineInstanceReq;
import com.sgs.otsnotes.facade.model.req.TestLineListSearchReq;
import com.sgs.otsnotes.facade.model.req.TestLineReportParamReq;
import com.sgs.otsnotes.facade.model.req.testLine.*;
import com.sgs.otsnotes.facade.model.rsp.ConclusionSampleListRsp;
import com.sgs.otsnotes.facade.model.rsp.GetTestLinePPNameZHRsp;
import com.sgs.otsnotes.facade.model.rsp.testLine.*;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 *
 */
public interface TestLineMapper {
    /**
     *
     * @param orderNo
     * @return9
     */
    List<TestLineBreakDownInfo> getTestLineBreakDownInfoList(@Param("orderNo") String orderNo);

    List<TestLineStatusDTO> getTestLineStatusByOrderNo(@Param("orderNo") String orderNo);

    TestLineStatusDTO checkTestLineComplete(@Param("orderNo") String orderNo);

    /**
     *
     * @param orderId
     * @return
     */
    @TrimsShuntLabMethod(methodName = "getTestLineByOrderIdNew")
    List<TestLineInstancePO> getTestLineByOrderId(@Param("orderId") String orderId);

    /**
     *
     * @param orderId
     * @return
     */
    List<TestLineInstancePO> getBaseIdByOrderId(@Param("orderId") String orderId);

    /**
     *
     * @param orderId
     * @return
     */
    List<TestLineInstancePO> getTestLineByOrderIdNew(@Param("orderId") String orderId);

    List<TestLineListRsp> getTestLineList( TestLineListReq orderId);

    /**
     *
     * @param orderId
     * @return
     */
    List<String> getTestLineInstanceIds(@Param("orderId") String orderId);

    /**
     *
     * @param orderId
     * @return
     */
    List<OrderTestLineRsp> getTestLineListByOrderId(@Param("orderId") String orderId);

    /**
     *
     * @param orderNo
     * @return
     */
    List<TestLineInstancePO> getTestLineByOrderNo(@Param("orderNo") String orderNo);
    List<TestLineInstancePO> getTestLineByOrderMatrix(@Param("orderNo") String orderNo);
    List<TestLineInstanceDTO> getTestLineSeqByOrderNo(@Param("orderNo") String orderNo);
    List<TestLineReport2Rsp> getTestLineReportByOrderNo(@Param("orderNo") String orderNo);


    /**
     *
     * @param testLineInstanceId
     * @return
     */
    TestLineInstancePO getTestLineInstanceById(String testLineInstanceId);

    /**
     *
     * @param delTestLineIds
     * @return
     */
    int batchDelete(@Param("delTestLineIds")List<String> delTestLineIds);

    /**
     *
     * @param testLineIds
     * @return
     */
    List<TestLineInstancePO> getTestLineByIds(@Param("testLineIds")List<String> testLineIds);

    /**
     * 根据jobNo找对应的TestLine
     * @param jobNo
     * @return
     */
    List<JobTestLineDto> getTestLineInstanceByJobNo(String jobNo);

    /**
     *
     * @param reqParams
     * @return
     */
    List<TestLineReportPo> getTestLineByTestLineIds(TestLineReportParamReq reqParams);

    /**
     *
     * @param testLines
     * @return
     */
    int batchInsert(@Param("testLines")List<TestLineInstancePO> testLines);

    /**
     *
     * @param testLines
     * @return
     */
    int batchUpdateTestlineInstanceFileId(@Param("testLines")List<UpdateTestlineInstanceFileIdReq> testLines);

    /**
     *
     * @param testLines
     * @return
     */
    int batchUpdateTestLineStatus(@Param("testLines")List<TestLineInstancePO> testLines);

    /**
     *
     * @param moduleType
     * @param testLines
     * @return
     */
    int batchUpdateTestLineStatus2(@Param("moduleType")Integer moduleType, @Param("testLines")List<TestLineStatusInfo> testLines);

    int batchUpdateTestLineDueDate(@Param("testLines")List<TestLineInstancePO> testLines);

    /**
     *
     * @param testLines
     * @return
     */
    int batchUpdateTestLineValidate(@Param("testLines")List<TestLineInstancePO> testLines);

    int updateTestLineType(TestLineTypeUpdateReq testLineTypeUpdateReq);

    /**
     *
     * @param orderId
     * @return
     */
    List<RequirmentTestLineInfo> getRequirmentTestLineInfoList(String orderId);

    /**
     *
     * @param ppSample
     * @return
     */
    Set<Integer> getPpSampleTestLineVersionIds(OrderPpSampleInfo ppSample);

    /**
     *
     * @param ppSample
     * @return
     */
    Set<Integer> getPpSampleTestLineVersionIdsNew(OrderPpSampleInfo ppSample);

    /**
     *
     * @param orderNo
     * @return
     */
    List<EmTestLineDTO> getEmTestLineInfoList(@Param("orderNo") String orderNo);

    /**
     *
     * @param jobNos
     * @return
     */
    List<JobTestLineForEmDto> getTestLineInstanceByJobNoForEm(@Param("jobNos")List<String> jobNos);

    /**
     *
     * @param testLines
     * @return
     */
    int updateBatchConditionStatus(@Param("testLines")List<TestLineInstancePO> testLines);



     List<TestLineInstancePO> getSubContractTestLineStatus(SubContractTestLineInfo reqObject);

    SubContractTestLineMappingPO getSubContractInfoByTestLineId(@Param("testLineInstanceId") String testLineInstanceId);

    /**
     * by testLineInstanceId get testlineid
     * @param testLineInstanceId
     * @return
     */
    Integer getTestLineIdByInstId(@Param("testLineInstanceId") String testLineInstanceId);

    String getTestLineRemark(@Param("testLineInstanceId") String testLineInstanceId);

    List<TestLineInstancePO> getTestLineStatus(@Param("testLineInstanceIds")Set<String> testLineInstanceIds);

    TestLineInstancePO getTestLineInfo(@Param("testLineInstanceId") String testLineInstanceId);

    int updateTestlineInstanceRemark(TestLineInstancePO testLineInstancePO);

    List<TestLineConfirmMatrixInfo> getTestLineConfirmMatrixInfoByOrderNo(@Param("orderNo")String orderNo);

    List<TestLineConfirmMatrixInfo> getUsedTestLineConfirmMatrixInfoByOrderNo(@Param("orderNo")String orderNo);

    /**
     *
     * @param orderNo
     * @return
     */
    List<TestLineSimplifyDTO> getTestLineListByOrderNo(String orderNo);

    /**
     *
     * @param testLineInstanceId
     * @return
     */
    TestLineSimplifyDTO getTestLineInfoById(@Param("testLineInstanceId") String testLineInstanceId);

    /**
     *
     * @param testLines
     * @return
     */
    int batchInsertForAddTl(@Param("testLines")List<TestLineInstancePO> testLines);

    int queryJobCountByPpTlRelId(@Param("relationIds")List<String> relationIds,@Param("subOrderFlag")boolean subOrderFlag);
    int querySubcontractCountByPpTlRelId(List<String> relationIDs);
    int queryReportCountByPpTlRelId(List<String> relationIDs);
    int queryReportCountByTestLineInstanceIdList(@Param("testLineInstanceIdList")List<String> testLineInstanceIdList);

    List<TestLineInstancePO> queryTestLineByRelId(List<String> relationIDs);

    List<TestLineConfirmMatrixInfo> getTlTestLineByOrderNo(@Param("orderNo")String orderNo);

    List<TestLineConfirmMatrixInfo> getPPTestLineByOrderNo(@Param("orderNo")String orderNo);

    int updateTestlineSepecialSeq(List<TestLineConfirmMatrixInfo> testLineInstancePOs);

    int updateModifiedById(TestLineInstancePO testLineInstancePO);

    List<TestLineInstancePO>getBaseTestLineByIds(@Param("testLineIds")List<String >testLineIds);

    int  cancelTestLine(TestLineInstancePO testLine);

    int updateBatchStandard(@Param("testLines")List<TestLineInstancePO> testLines);
    int updateBatchStandardMultilanguage(@Param("testLines")List<TestLineInstanceMultipleLanguageInfoPO> testLineInstanceMultipleLanguageInfoPOS);
    int batchSaveMultiLanguageTestLine(@Param("testLines") List<TestLineInstanceMultipleLanguageInfoPO> testLineInstanceMultipleLanguageInfoPOS);
    int batchUpdateTlLabSectionBaseId(@Param("labSections")List<LabSectionInfoPO> labSections);

    List<PretreatmentDO> queryPretreatmentForMasterList(@Param("orderNo")String orderNo);

    /**
     *
     * @param reqObject
     * @return
     */
    int batchDeleteExt(DelPPTestLineRelInfo reqObject);

    List<TestLineInstancePO> queryTestLineByReportId(@Param("reportId") String reportId);

    List<ReportDTO> queryReportByTestLine(@Param("testLineInsId") String testLineInsId);


   TestLineInstancePO getBaseTestLineById(@Param("testLineInstanceID")String testLineInstanceID);

    List<TestLineInstancePO> getTestLineByReportId(@Param("reportId")String reportId);

    List<ConclusionSampleDTO> getSampleListByTestLine(Map<String,String> map);

    List<TlConditionDTO> getTestConditionName(Map<String,Object> map);

    List<ConclusionSampleListRsp> getConclusionSampleList(@Param("reportId")String reportId);

    List<ConclusionSampleListRsp> getConclusionSampleListBySubcontract(@Param("subContractNos")List<String> subContractNos);

    int batchUpdateConclusionFlag(CalculateConclusionFlagInfo conclusion);

    List<GemoRsp> getConfirmMatrixGemoTestLine(GemoReq gemoReq);

    int cancelTestLineByGeneralOrderId(TestLineInstancePO testLineInstancePO);

    List<MatrixTestLineRelInfo> getPPTestLineRelList(String orderNo);

    List<TestLineInstancePO> getTestLineByOrderIdAndStatus(TestLineStatusReq testLineStatusReq);

    void updateTestLineStatusBySubContractId(@Param("subContractId") String subContractId,@Param("testLineStatus")Integer testLineStatus);

    List<TestLineInstancePO> queryByParams(Map<String, Object> params);

    /**
     *
     * @param testLines
     * @return
     */
    int batchUpdateTestlineInstance(@Param("testLines")List<SaveTestLineInstanceReq> testLines);

    void batchUpdate(List<TestLineInstancePO> testLineUpdateList);

    int batchUpdateTestLineStatusAndTestLineType(List<TestLineInstancePO> list);

    /**
     *
     * @param orderId
     * @return
     */
    List<NewTestLineInfo> getNewTestLineInfoList(@Param("orderId")String orderId);

    /**
     *
     * @param orderNo
     * @return
     */
    List<GetTestLinePPNameZHRsp> getTestLineNameZH(@Param("orderNo")String orderNo);

    /**
     *
     * @param orderNo
     * @return
     */
    List<TestLineSimplifyInfo> getTestLineSimplifyInfoList(@Param("orderNo")String orderNo);

    List<TestLineRequirmentListDTO> getTestLineForRequirment(@Param("orderID")String orderID);

    List<TestLineLimitsDTO> getTestLimits(@Param("testLineInstanceID")String testLineInstanceID);

    List<TestLineSampleDTO> getTestLineSample(@Param("testLineInstanceID")String testLineInstanceID);
    /**
     *
     * @param testLineInstanceId
     * @return
     */
    TestLineInstancePO getTestLineById(String testLineInstanceId);

    /**
     * 更新TL部分信息，用于弹窗口编辑
     * @param testLineInstancePO testLineInstancePO
     * @return 受影响的行数
     */
    int updateTestLineInstanceDetail(TestLineInstancePO testLineInstancePO);

    /**
     * 单独更新TL的ReportSeq
     * @param updateTestLineReportSeq
     * @return
     */
    int updateTestLineReportSeq(UpdateTestLineReportSeq updateTestLineReportSeq);

    TestLineEditDetailRsp getEditTestLineInfoById(String testLineInstanceId);

    int updatePendingFlagByIds(UpdateTestLinePendingFlagReq req);

    List<TestLinePendingFlagRsp> getTestLinePendingFlag(List<String> tlIds);

    List<TestLinePendingFlagRsp> getTestLinePendingByPPTLRelIds(List<String> pptlRelIds);

    List<TestLineInstancePO> selectTestLineStatusByIds(List<String> testLineInstanceIds);

    CheckSubTLDTO querySubTLStatusByCurrentTLId(String testLineInstanceId);

    /**
     *
     * @param testLineInstanceIds
     * @return
     */
    List<TestLineClaimInfo> getTestLineClaimInfo(@Param("testLineIds")List<String> testLineInstanceIds);

    /**
     * 分页查询testLine列表
     * @param testLineListSearchReq
     * @return
     */
    List<TestLinePageListDTO> getPageList(TestLineListSearchReq testLineListSearchReq);

    /**
     * 分页查询testLine列表
     * @param testLineListSearchReq
     * @return
     */
    List<TestLinePageListDTO> getTestLineByPage(TestLineListSearchReq testLineListSearchReq);

    /**
     * Master List report summary 信息查询
     */
    List<ReportSummaryInfo> getReportSummaryInfo(@Param("orderNo")String orderNo);

    List<TestLineJobSubcontractDTO> getTestLineJobSubcontractStatusByOrderID(TestLineJobSubcontractReqDTO req);

    int updateTestLineDueDateBatch(@Param("testLineList") List<TestLineInstancePO> testLineList);

    List<QueryCitationNameDTO> queryCitationParamDataByOrderId(@Param("orderId") String orderId);


    /**
     *
     * @param subcontractId
     * @return
     */
    List<TestLineInstancePO> getSubcontractTestLineInfo(String subcontractId);

    //////////////////////////////////////////////// GPO Begin ///////////////////////////////////////////////////////

    /**
     * 基于订单、报告、TL InstanceIDs 查询TL Instance 基础信息
     *
     * @param tlInstanceBaseReq
     * @return
     */
    List<TLInstanceBaseInfo> getTLInstanceBases(TLInstanceBaseReq tlInstanceBaseReq);

    /**
     * 基于订单、报告、TL InstanceIDs 查询TL Instance 详细信息
     *
     * @param tlInstanceDetailReq
     * @return
     */
    List<TLInstanceDetailInfo> getTLInstanceDetails(TLInstanceDetailReq tlInstanceDetailReq);

    List<OrderTestLineRelDTO> getOrderTestLineRelForFirstPpArtifactRel(OrderTestLineRelReq orderTestLineRelReq);

    List<String> getTestLineIdsByPpTestLineRelIds(@Param("ppTlIds") List<String> ppTlIds);

    List<PPTestLineJobDto> getPpTestLineJob(@Param("orderId") String orderId, @Param("testLineInstanceIds") List<String> testLineInstanceIds,@Param("exeOrderId") String exeOrderId);

    List<TestItemRsp> queryTrimsTestLineListByName(@Param("evaluationAlias") String evaluationAlias, @Param("limit") int limit,@Param("labCode") String labCode);

    List<EquipmentTestLineReq> getLabSectionNameByTlIds(@Param("testLineInstanceIds") List<String> testLineInstanceIds,@Param("orderInstanceID") String orderInstanceID);
    List<MatrixTestlineInstanceDTO> queryTestLineInstanceForMatrix(@Param("orderNoList") List<String> orderNoList);
    //////////////////////////////////////////////// GPO End /////////////////////////////////////////////////////////

    List<WIPO> getWIPOinfo(@Param("testLineInstanceIdList") List<String> testLineInstanceIdList);

    List<TestLineInstanceMultipleLanguageInfoPO> getTestLineLanguages(@Param("testLineInstanceIdList") List<String> testLineInstanceIdList);

    List<TestLineLabSectionRelDTO> queryTestLineLabSectionRel(@Param("testLineInstanceIdList") List<String> testLineInstanceIdList);

    List<PpTestLineInfo> getPpTestLineInfoByPpTestLineIds(@Param("orderId") String orderId, @Param("ppTestLineInstanceIds") List<String> ppTestLineInstanceIds);

}
