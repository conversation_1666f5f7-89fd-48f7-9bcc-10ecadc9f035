package com.sgs.otsnotes.dbstorages.mybatis.model;

import java.util.Date;

public class ReportTemplateInfoPO {
    /**
     * id VARCHAR(50) 必填<br>
     * 
     */
    private String id;

    /**
     * report_id VARCHAR(50) 必填<br>
     * 
     */
    private String reportId;

    /**
     * qrcode_path VARCHAR(500)<br>
     * 
     */
    private String qrcodePath;

    /**
     * template_setting_id INTEGER(10)<br>
     * 
     */
    private Integer templateSettingId;

    /**
     * language_id INTEGER(10)<br>
     * 
     */
    private Integer languageId;

    /**
     * template_type_id INTEGER(10)<br>
     * 
     */
    private Integer templateTypeId;

    /**
     * active_indicator BIT 必填<br>
     * 0: inactive, 1: active
     */
    private Boolean activeIndicator;

    /**
     * created_by VARCHAR(50)<br>
     * 
     */
    private String createdBy;

    /**
     * created_date TIMESTAMP(19)<br>
     * 
     */
    private Date createdDate;

    /**
     * modified_by VARCHAR(50)<br>
     * 
     */
    private String modifiedBy;

    /**
     * modified_date TIMESTAMP(19)<br>
     * 
     */
    private Date modifiedDate;

    /**
     * template_name VARCHAR(100)<br>
     * 
     */
    private String templateName;

    /**
     * container INTEGER(10)<br>
     * 
     */
    private Integer container;

    /**
     * container_template_filepath VARCHAR(500)<br>
     * 
     */
    private String containerTemplateFilepath;

    /**
     * data_object VARCHAR(100)<br>
     * 
     */
    private String dataObject;

    /**
     * template_config VARCHAR(1500)<br>
     * 
     */
    private String templateConfig;

    /**
     * protocol_structure_flag
     *
     */
    private Integer protocolStructureFlag;

    /**
     * id VARCHAR(50) 必填<br>
     * 获得 
     */
    public String getId() {
        return id;
    }

    /**
     * id VARCHAR(50) 必填<br>
     * 设置 
     */
    public void setId(String id) {
        this.id = id == null ? null : id.trim();
    }

    /**
     * report_id VARCHAR(50) 必填<br>
     * 获得 
     */
    public String getReportId() {
        return reportId;
    }

    /**
     * report_id VARCHAR(50) 必填<br>
     * 设置 
     */
    public void setReportId(String reportId) {
        this.reportId = reportId == null ? null : reportId.trim();
    }

    /**
     * qrcode_path VARCHAR(500)<br>
     * 获得 
     */
    public String getQrcodePath() {
        return qrcodePath;
    }

    /**
     * qrcode_path VARCHAR(500)<br>
     * 设置 
     */
    public void setQrcodePath(String qrcodePath) {
        this.qrcodePath = qrcodePath == null ? null : qrcodePath.trim();
    }

    /**
     * template_setting_id INTEGER(10)<br>
     * 获得 
     */
    public Integer getTemplateSettingId() {
        return templateSettingId;
    }

    /**
     * template_setting_id INTEGER(10)<br>
     * 设置 
     */
    public void setTemplateSettingId(Integer templateSettingId) {
        this.templateSettingId = templateSettingId;
    }

    /**
     * language_id INTEGER(10)<br>
     * 获得 
     */
    public Integer getLanguageId() {
        return languageId;
    }

    /**
     * language_id INTEGER(10)<br>
     * 设置 
     */
    public void setLanguageId(Integer languageId) {
        this.languageId = languageId;
    }

    /**
     * template_type_id INTEGER(10)<br>
     * 获得 
     */
    public Integer getTemplateTypeId() {
        return templateTypeId;
    }

    /**
     * template_type_id INTEGER(10)<br>
     * 设置 
     */
    public void setTemplateTypeId(Integer templateTypeId) {
        this.templateTypeId = templateTypeId;
    }

    /**
     * active_indicator BIT 必填<br>
     * 获得 0: inactive, 1: active
     */
    public Boolean getActiveIndicator() {
        return activeIndicator;
    }

    /**
     * active_indicator BIT 必填<br>
     * 设置 0: inactive, 1: active
     */
    public void setActiveIndicator(Boolean activeIndicator) {
        this.activeIndicator = activeIndicator;
    }

    /**
     * created_by VARCHAR(50)<br>
     * 获得 
     */
    public String getCreatedBy() {
        return createdBy;
    }

    /**
     * created_by VARCHAR(50)<br>
     * 设置 
     */
    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy == null ? null : createdBy.trim();
    }

    /**
     * created_date TIMESTAMP(19)<br>
     * 获得 
     */
    public Date getCreatedDate() {
        return createdDate;
    }

    /**
     * created_date TIMESTAMP(19)<br>
     * 设置 
     */
    public void setCreatedDate(Date createdDate) {
        this.createdDate = createdDate;
    }

    /**
     * modified_by VARCHAR(50)<br>
     * 获得 
     */
    public String getModifiedBy() {
        return modifiedBy;
    }

    /**
     * modified_by VARCHAR(50)<br>
     * 设置 
     */
    public void setModifiedBy(String modifiedBy) {
        this.modifiedBy = modifiedBy == null ? null : modifiedBy.trim();
    }

    /**
     * modified_date TIMESTAMP(19)<br>
     * 获得 
     */
    public Date getModifiedDate() {
        return modifiedDate;
    }

    /**
     * modified_date TIMESTAMP(19)<br>
     * 设置 
     */
    public void setModifiedDate(Date modifiedDate) {
        this.modifiedDate = modifiedDate;
    }

    /**
     * template_name VARCHAR(100)<br>
     * 获得 
     */
    public String getTemplateName() {
        return templateName;
    }

    /**
     * template_name VARCHAR(100)<br>
     * 设置 
     */
    public void setTemplateName(String templateName) {
        this.templateName = templateName == null ? null : templateName.trim();
    }

    /**
     * container INTEGER(10)<br>
     * 获得 
     */
    public Integer getContainer() {
        return container;
    }

    /**
     * container INTEGER(10)<br>
     * 设置 
     */
    public void setContainer(Integer container) {
        this.container = container;
    }

    /**
     * container_template_filepath VARCHAR(500)<br>
     * 获得 
     */
    public String getContainerTemplateFilepath() {
        return containerTemplateFilepath;
    }

    /**
     * container_template_filepath VARCHAR(500)<br>
     * 设置 
     */
    public void setContainerTemplateFilepath(String containerTemplateFilepath) {
        this.containerTemplateFilepath = containerTemplateFilepath == null ? null : containerTemplateFilepath.trim();
    }

    /**
     * data_object VARCHAR(100)<br>
     * 获得 
     */
    public String getDataObject() {
        return dataObject;
    }

    /**
     * data_object VARCHAR(100)<br>
     * 设置 
     */
    public void setDataObject(String dataObject) {
        this.dataObject = dataObject == null ? null : dataObject.trim();
    }

    /**
     * template_config VARCHAR(1500)<br>
     * 获得 
     */
    public String getTemplateConfig() {
        return templateConfig;
    }

    /**
     * template_config VARCHAR(1500)<br>
     * 设置 
     */
    public void setTemplateConfig(String templateConfig) {
        this.templateConfig = templateConfig == null ? null : templateConfig.trim();
    }

    public Integer getProtocolStructureFlag() {
        return protocolStructureFlag;
    }

    public void setProtocolStructureFlag(Integer protocolStructureFlag) {
        this.protocolStructureFlag = protocolStructureFlag;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", reportId=").append(reportId);
        sb.append(", qrcodePath=").append(qrcodePath);
        sb.append(", templateSettingId=").append(templateSettingId);
        sb.append(", languageId=").append(languageId);
        sb.append(", templateTypeId=").append(templateTypeId);
        sb.append(", activeIndicator=").append(activeIndicator);
        sb.append(", createdBy=").append(createdBy);
        sb.append(", createdDate=").append(createdDate);
        sb.append(", modifiedBy=").append(modifiedBy);
        sb.append(", modifiedDate=").append(modifiedDate);
        sb.append(", templateName=").append(templateName);
        sb.append(", container=").append(container);
        sb.append(", containerTemplateFilepath=").append(containerTemplateFilepath);
        sb.append(", dataObject=").append(dataObject);
        sb.append(", templateConfig=").append(templateConfig);
        sb.append("]");
        return sb.toString();
    }
}