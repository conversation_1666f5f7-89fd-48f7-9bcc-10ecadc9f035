package com.sgs.otsnotes.dbstorages.mybatis.model;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class PrelimResultInfoExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public PrelimResultInfoExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(String value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(String value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(String value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(String value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(String value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(String value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLike(String value) {
            addCriterion("id like", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotLike(String value) {
            addCriterion("id not like", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<String> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<String> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(String value1, String value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(String value1, String value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andBuIdIsNull() {
            addCriterion("bu_id is null");
            return (Criteria) this;
        }

        public Criteria andBuIdIsNotNull() {
            addCriterion("bu_id is not null");
            return (Criteria) this;
        }

        public Criteria andBuIdEqualTo(Integer value) {
            addCriterion("bu_id =", value, "buId");
            return (Criteria) this;
        }

        public Criteria andBuIdNotEqualTo(Integer value) {
            addCriterion("bu_id <>", value, "buId");
            return (Criteria) this;
        }

        public Criteria andBuIdGreaterThan(Integer value) {
            addCriterion("bu_id >", value, "buId");
            return (Criteria) this;
        }

        public Criteria andBuIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("bu_id >=", value, "buId");
            return (Criteria) this;
        }

        public Criteria andBuIdLessThan(Integer value) {
            addCriterion("bu_id <", value, "buId");
            return (Criteria) this;
        }

        public Criteria andBuIdLessThanOrEqualTo(Integer value) {
            addCriterion("bu_id <=", value, "buId");
            return (Criteria) this;
        }

        public Criteria andBuIdIn(List<Integer> values) {
            addCriterion("bu_id in", values, "buId");
            return (Criteria) this;
        }

        public Criteria andBuIdNotIn(List<Integer> values) {
            addCriterion("bu_id not in", values, "buId");
            return (Criteria) this;
        }

        public Criteria andBuIdBetween(Integer value1, Integer value2) {
            addCriterion("bu_id between", value1, value2, "buId");
            return (Criteria) this;
        }

        public Criteria andBuIdNotBetween(Integer value1, Integer value2) {
            addCriterion("bu_id not between", value1, value2, "buId");
            return (Criteria) this;
        }

        public Criteria andBuCodeIsNull() {
            addCriterion("bu_code is null");
            return (Criteria) this;
        }

        public Criteria andBuCodeIsNotNull() {
            addCriterion("bu_code is not null");
            return (Criteria) this;
        }

        public Criteria andBuCodeEqualTo(String value) {
            addCriterion("bu_code =", value, "buCode");
            return (Criteria) this;
        }

        public Criteria andBuCodeNotEqualTo(String value) {
            addCriterion("bu_code <>", value, "buCode");
            return (Criteria) this;
        }

        public Criteria andBuCodeGreaterThan(String value) {
            addCriterion("bu_code >", value, "buCode");
            return (Criteria) this;
        }

        public Criteria andBuCodeGreaterThanOrEqualTo(String value) {
            addCriterion("bu_code >=", value, "buCode");
            return (Criteria) this;
        }

        public Criteria andBuCodeLessThan(String value) {
            addCriterion("bu_code <", value, "buCode");
            return (Criteria) this;
        }

        public Criteria andBuCodeLessThanOrEqualTo(String value) {
            addCriterion("bu_code <=", value, "buCode");
            return (Criteria) this;
        }

        public Criteria andBuCodeLike(String value) {
            addCriterion("bu_code like", value, "buCode");
            return (Criteria) this;
        }

        public Criteria andBuCodeNotLike(String value) {
            addCriterion("bu_code not like", value, "buCode");
            return (Criteria) this;
        }

        public Criteria andBuCodeIn(List<String> values) {
            addCriterion("bu_code in", values, "buCode");
            return (Criteria) this;
        }

        public Criteria andBuCodeNotIn(List<String> values) {
            addCriterion("bu_code not in", values, "buCode");
            return (Criteria) this;
        }

        public Criteria andBuCodeBetween(String value1, String value2) {
            addCriterion("bu_code between", value1, value2, "buCode");
            return (Criteria) this;
        }

        public Criteria andBuCodeNotBetween(String value1, String value2) {
            addCriterion("bu_code not between", value1, value2, "buCode");
            return (Criteria) this;
        }

        public Criteria andLocationIdIsNull() {
            addCriterion("location_id is null");
            return (Criteria) this;
        }

        public Criteria andLocationIdIsNotNull() {
            addCriterion("location_id is not null");
            return (Criteria) this;
        }

        public Criteria andLocationIdEqualTo(Integer value) {
            addCriterion("location_id =", value, "locationId");
            return (Criteria) this;
        }

        public Criteria andLocationIdNotEqualTo(Integer value) {
            addCriterion("location_id <>", value, "locationId");
            return (Criteria) this;
        }

        public Criteria andLocationIdGreaterThan(Integer value) {
            addCriterion("location_id >", value, "locationId");
            return (Criteria) this;
        }

        public Criteria andLocationIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("location_id >=", value, "locationId");
            return (Criteria) this;
        }

        public Criteria andLocationIdLessThan(Integer value) {
            addCriterion("location_id <", value, "locationId");
            return (Criteria) this;
        }

        public Criteria andLocationIdLessThanOrEqualTo(Integer value) {
            addCriterion("location_id <=", value, "locationId");
            return (Criteria) this;
        }

        public Criteria andLocationIdIn(List<Integer> values) {
            addCriterion("location_id in", values, "locationId");
            return (Criteria) this;
        }

        public Criteria andLocationIdNotIn(List<Integer> values) {
            addCriterion("location_id not in", values, "locationId");
            return (Criteria) this;
        }

        public Criteria andLocationIdBetween(Integer value1, Integer value2) {
            addCriterion("location_id between", value1, value2, "locationId");
            return (Criteria) this;
        }

        public Criteria andLocationIdNotBetween(Integer value1, Integer value2) {
            addCriterion("location_id not between", value1, value2, "locationId");
            return (Criteria) this;
        }

        public Criteria andLocationCodeIsNull() {
            addCriterion("location_code is null");
            return (Criteria) this;
        }

        public Criteria andLocationCodeIsNotNull() {
            addCriterion("location_code is not null");
            return (Criteria) this;
        }

        public Criteria andLocationCodeEqualTo(String value) {
            addCriterion("location_code =", value, "locationCode");
            return (Criteria) this;
        }

        public Criteria andLocationCodeNotEqualTo(String value) {
            addCriterion("location_code <>", value, "locationCode");
            return (Criteria) this;
        }

        public Criteria andLocationCodeGreaterThan(String value) {
            addCriterion("location_code >", value, "locationCode");
            return (Criteria) this;
        }

        public Criteria andLocationCodeGreaterThanOrEqualTo(String value) {
            addCriterion("location_code >=", value, "locationCode");
            return (Criteria) this;
        }

        public Criteria andLocationCodeLessThan(String value) {
            addCriterion("location_code <", value, "locationCode");
            return (Criteria) this;
        }

        public Criteria andLocationCodeLessThanOrEqualTo(String value) {
            addCriterion("location_code <=", value, "locationCode");
            return (Criteria) this;
        }

        public Criteria andLocationCodeLike(String value) {
            addCriterion("location_code like", value, "locationCode");
            return (Criteria) this;
        }

        public Criteria andLocationCodeNotLike(String value) {
            addCriterion("location_code not like", value, "locationCode");
            return (Criteria) this;
        }

        public Criteria andLocationCodeIn(List<String> values) {
            addCriterion("location_code in", values, "locationCode");
            return (Criteria) this;
        }

        public Criteria andLocationCodeNotIn(List<String> values) {
            addCriterion("location_code not in", values, "locationCode");
            return (Criteria) this;
        }

        public Criteria andLocationCodeBetween(String value1, String value2) {
            addCriterion("location_code between", value1, value2, "locationCode");
            return (Criteria) this;
        }

        public Criteria andLocationCodeNotBetween(String value1, String value2) {
            addCriterion("location_code not between", value1, value2, "locationCode");
            return (Criteria) this;
        }

        public Criteria andOrderNoIsNull() {
            addCriterion("order_no is null");
            return (Criteria) this;
        }

        public Criteria andOrderNoIsNotNull() {
            addCriterion("order_no is not null");
            return (Criteria) this;
        }

        public Criteria andOrderNoEqualTo(String value) {
            addCriterion("order_no =", value, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoNotEqualTo(String value) {
            addCriterion("order_no <>", value, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoGreaterThan(String value) {
            addCriterion("order_no >", value, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoGreaterThanOrEqualTo(String value) {
            addCriterion("order_no >=", value, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoLessThan(String value) {
            addCriterion("order_no <", value, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoLessThanOrEqualTo(String value) {
            addCriterion("order_no <=", value, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoLike(String value) {
            addCriterion("order_no like", value, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoNotLike(String value) {
            addCriterion("order_no not like", value, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoIn(List<String> values) {
            addCriterion("order_no in", values, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoNotIn(List<String> values) {
            addCriterion("order_no not in", values, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoBetween(String value1, String value2) {
            addCriterion("order_no between", value1, value2, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoNotBetween(String value1, String value2) {
            addCriterion("order_no not between", value1, value2, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderIdIsNull() {
            addCriterion("order_id is null");
            return (Criteria) this;
        }

        public Criteria andOrderIdIsNotNull() {
            addCriterion("order_id is not null");
            return (Criteria) this;
        }

        public Criteria andOrderIdEqualTo(String value) {
            addCriterion("order_id =", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdNotEqualTo(String value) {
            addCriterion("order_id <>", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdGreaterThan(String value) {
            addCriterion("order_id >", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdGreaterThanOrEqualTo(String value) {
            addCriterion("order_id >=", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdLessThan(String value) {
            addCriterion("order_id <", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdLessThanOrEqualTo(String value) {
            addCriterion("order_id <=", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdLike(String value) {
            addCriterion("order_id like", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdNotLike(String value) {
            addCriterion("order_id not like", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdIn(List<String> values) {
            addCriterion("order_id in", values, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdNotIn(List<String> values) {
            addCriterion("order_id not in", values, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdBetween(String value1, String value2) {
            addCriterion("order_id between", value1, value2, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdNotBetween(String value1, String value2) {
            addCriterion("order_id not between", value1, value2, "orderId");
            return (Criteria) this;
        }

        public Criteria andTemplateNameIsNull() {
            addCriterion("template_name is null");
            return (Criteria) this;
        }

        public Criteria andTemplateNameIsNotNull() {
            addCriterion("template_name is not null");
            return (Criteria) this;
        }

        public Criteria andTemplateNameEqualTo(String value) {
            addCriterion("template_name =", value, "templateName");
            return (Criteria) this;
        }

        public Criteria andTemplateNameNotEqualTo(String value) {
            addCriterion("template_name <>", value, "templateName");
            return (Criteria) this;
        }

        public Criteria andTemplateNameGreaterThan(String value) {
            addCriterion("template_name >", value, "templateName");
            return (Criteria) this;
        }

        public Criteria andTemplateNameGreaterThanOrEqualTo(String value) {
            addCriterion("template_name >=", value, "templateName");
            return (Criteria) this;
        }

        public Criteria andTemplateNameLessThan(String value) {
            addCriterion("template_name <", value, "templateName");
            return (Criteria) this;
        }

        public Criteria andTemplateNameLessThanOrEqualTo(String value) {
            addCriterion("template_name <=", value, "templateName");
            return (Criteria) this;
        }

        public Criteria andTemplateNameLike(String value) {
            addCriterion("template_name like", value, "templateName");
            return (Criteria) this;
        }

        public Criteria andTemplateNameNotLike(String value) {
            addCriterion("template_name not like", value, "templateName");
            return (Criteria) this;
        }

        public Criteria andTemplateNameIn(List<String> values) {
            addCriterion("template_name in", values, "templateName");
            return (Criteria) this;
        }

        public Criteria andTemplateNameNotIn(List<String> values) {
            addCriterion("template_name not in", values, "templateName");
            return (Criteria) this;
        }

        public Criteria andTemplateNameBetween(String value1, String value2) {
            addCriterion("template_name between", value1, value2, "templateName");
            return (Criteria) this;
        }

        public Criteria andTemplateNameNotBetween(String value1, String value2) {
            addCriterion("template_name not between", value1, value2, "templateName");
            return (Criteria) this;
        }

        public Criteria andTemplateJsonIsNull() {
            addCriterion("template_json is null");
            return (Criteria) this;
        }

        public Criteria andTemplateJsonIsNotNull() {
            addCriterion("template_json is not null");
            return (Criteria) this;
        }

        public Criteria andTemplateJsonEqualTo(Object value) {
            addCriterion("template_json =", value, "templateJson");
            return (Criteria) this;
        }

        public Criteria andTemplateJsonNotEqualTo(Object value) {
            addCriterion("template_json <>", value, "templateJson");
            return (Criteria) this;
        }

        public Criteria andTemplateJsonGreaterThan(Object value) {
            addCriterion("template_json >", value, "templateJson");
            return (Criteria) this;
        }

        public Criteria andTemplateJsonGreaterThanOrEqualTo(Object value) {
            addCriterion("template_json >=", value, "templateJson");
            return (Criteria) this;
        }

        public Criteria andTemplateJsonLessThan(Object value) {
            addCriterion("template_json <", value, "templateJson");
            return (Criteria) this;
        }

        public Criteria andTemplateJsonLessThanOrEqualTo(Object value) {
            addCriterion("template_json <=", value, "templateJson");
            return (Criteria) this;
        }

        public Criteria andTemplateJsonIn(List<Object> values) {
            addCriterion("template_json in", values, "templateJson");
            return (Criteria) this;
        }

        public Criteria andTemplateJsonNotIn(List<Object> values) {
            addCriterion("template_json not in", values, "templateJson");
            return (Criteria) this;
        }

        public Criteria andTemplateJsonBetween(Object value1, Object value2) {
            addCriterion("template_json between", value1, value2, "templateJson");
            return (Criteria) this;
        }

        public Criteria andTemplateJsonNotBetween(Object value1, Object value2) {
            addCriterion("template_json not between", value1, value2, "templateJson");
            return (Criteria) this;
        }

        public Criteria andPrelimResultNoIsNull() {
            addCriterion("prelim_result_no is null");
            return (Criteria) this;
        }

        public Criteria andPrelimResultNoIsNotNull() {
            addCriterion("prelim_result_no is not null");
            return (Criteria) this;
        }

        public Criteria andPrelimResultNoEqualTo(String value) {
            addCriterion("prelim_result_no =", value, "prelimResultNo");
            return (Criteria) this;
        }

        public Criteria andPrelimResultNoNotEqualTo(String value) {
            addCriterion("prelim_result_no <>", value, "prelimResultNo");
            return (Criteria) this;
        }

        public Criteria andPrelimResultNoGreaterThan(String value) {
            addCriterion("prelim_result_no >", value, "prelimResultNo");
            return (Criteria) this;
        }

        public Criteria andPrelimResultNoGreaterThanOrEqualTo(String value) {
            addCriterion("prelim_result_no >=", value, "prelimResultNo");
            return (Criteria) this;
        }

        public Criteria andPrelimResultNoLessThan(String value) {
            addCriterion("prelim_result_no <", value, "prelimResultNo");
            return (Criteria) this;
        }

        public Criteria andPrelimResultNoLessThanOrEqualTo(String value) {
            addCriterion("prelim_result_no <=", value, "prelimResultNo");
            return (Criteria) this;
        }

        public Criteria andPrelimResultNoLike(String value) {
            addCriterion("prelim_result_no like", value, "prelimResultNo");
            return (Criteria) this;
        }

        public Criteria andPrelimResultNoNotLike(String value) {
            addCriterion("prelim_result_no not like", value, "prelimResultNo");
            return (Criteria) this;
        }

        public Criteria andPrelimResultNoIn(List<String> values) {
            addCriterion("prelim_result_no in", values, "prelimResultNo");
            return (Criteria) this;
        }

        public Criteria andPrelimResultNoNotIn(List<String> values) {
            addCriterion("prelim_result_no not in", values, "prelimResultNo");
            return (Criteria) this;
        }

        public Criteria andPrelimResultNoBetween(String value1, String value2) {
            addCriterion("prelim_result_no between", value1, value2, "prelimResultNo");
            return (Criteria) this;
        }

        public Criteria andPrelimResultNoNotBetween(String value1, String value2) {
            addCriterion("prelim_result_no not between", value1, value2, "prelimResultNo");
            return (Criteria) this;
        }

        public Criteria andConclusionIsNull() {
            addCriterion("conclusion is null");
            return (Criteria) this;
        }

        public Criteria andConclusionIsNotNull() {
            addCriterion("conclusion is not null");
            return (Criteria) this;
        }

        public Criteria andConclusionEqualTo(String value) {
            addCriterion("conclusion =", value, "conclusion");
            return (Criteria) this;
        }

        public Criteria andConclusionNotEqualTo(String value) {
            addCriterion("conclusion <>", value, "conclusion");
            return (Criteria) this;
        }

        public Criteria andConclusionGreaterThan(String value) {
            addCriterion("conclusion >", value, "conclusion");
            return (Criteria) this;
        }

        public Criteria andConclusionGreaterThanOrEqualTo(String value) {
            addCriterion("conclusion >=", value, "conclusion");
            return (Criteria) this;
        }

        public Criteria andConclusionLessThan(String value) {
            addCriterion("conclusion <", value, "conclusion");
            return (Criteria) this;
        }

        public Criteria andConclusionLessThanOrEqualTo(String value) {
            addCriterion("conclusion <=", value, "conclusion");
            return (Criteria) this;
        }

        public Criteria andConclusionLike(String value) {
            addCriterion("conclusion like", value, "conclusion");
            return (Criteria) this;
        }

        public Criteria andConclusionNotLike(String value) {
            addCriterion("conclusion not like", value, "conclusion");
            return (Criteria) this;
        }

        public Criteria andConclusionIn(List<String> values) {
            addCriterion("conclusion in", values, "conclusion");
            return (Criteria) this;
        }

        public Criteria andConclusionNotIn(List<String> values) {
            addCriterion("conclusion not in", values, "conclusion");
            return (Criteria) this;
        }

        public Criteria andConclusionBetween(String value1, String value2) {
            addCriterion("conclusion between", value1, value2, "conclusion");
            return (Criteria) this;
        }

        public Criteria andConclusionNotBetween(String value1, String value2) {
            addCriterion("conclusion not between", value1, value2, "conclusion");
            return (Criteria) this;
        }

        public Criteria andCsNameIsNull() {
            addCriterion("cs_name is null");
            return (Criteria) this;
        }

        public Criteria andCsNameIsNotNull() {
            addCriterion("cs_name is not null");
            return (Criteria) this;
        }

        public Criteria andCsNameEqualTo(String value) {
            addCriterion("cs_name =", value, "csName");
            return (Criteria) this;
        }

        public Criteria andCsNameNotEqualTo(String value) {
            addCriterion("cs_name <>", value, "csName");
            return (Criteria) this;
        }

        public Criteria andCsNameGreaterThan(String value) {
            addCriterion("cs_name >", value, "csName");
            return (Criteria) this;
        }

        public Criteria andCsNameGreaterThanOrEqualTo(String value) {
            addCriterion("cs_name >=", value, "csName");
            return (Criteria) this;
        }

        public Criteria andCsNameLessThan(String value) {
            addCriterion("cs_name <", value, "csName");
            return (Criteria) this;
        }

        public Criteria andCsNameLessThanOrEqualTo(String value) {
            addCriterion("cs_name <=", value, "csName");
            return (Criteria) this;
        }

        public Criteria andCsNameLike(String value) {
            addCriterion("cs_name like", value, "csName");
            return (Criteria) this;
        }

        public Criteria andCsNameNotLike(String value) {
            addCriterion("cs_name not like", value, "csName");
            return (Criteria) this;
        }

        public Criteria andCsNameIn(List<String> values) {
            addCriterion("cs_name in", values, "csName");
            return (Criteria) this;
        }

        public Criteria andCsNameNotIn(List<String> values) {
            addCriterion("cs_name not in", values, "csName");
            return (Criteria) this;
        }

        public Criteria andCsNameBetween(String value1, String value2) {
            addCriterion("cs_name between", value1, value2, "csName");
            return (Criteria) this;
        }

        public Criteria andCsNameNotBetween(String value1, String value2) {
            addCriterion("cs_name not between", value1, value2, "csName");
            return (Criteria) this;
        }

        public Criteria andResponsibleEngineerIsNull() {
            addCriterion("responsible_engineer is null");
            return (Criteria) this;
        }

        public Criteria andResponsibleEngineerIsNotNull() {
            addCriterion("responsible_engineer is not null");
            return (Criteria) this;
        }

        public Criteria andResponsibleEngineerEqualTo(String value) {
            addCriterion("responsible_engineer =", value, "responsibleEngineer");
            return (Criteria) this;
        }

        public Criteria andResponsibleEngineerNotEqualTo(String value) {
            addCriterion("responsible_engineer <>", value, "responsibleEngineer");
            return (Criteria) this;
        }

        public Criteria andResponsibleEngineerGreaterThan(String value) {
            addCriterion("responsible_engineer >", value, "responsibleEngineer");
            return (Criteria) this;
        }

        public Criteria andResponsibleEngineerGreaterThanOrEqualTo(String value) {
            addCriterion("responsible_engineer >=", value, "responsibleEngineer");
            return (Criteria) this;
        }

        public Criteria andResponsibleEngineerLessThan(String value) {
            addCriterion("responsible_engineer <", value, "responsibleEngineer");
            return (Criteria) this;
        }

        public Criteria andResponsibleEngineerLessThanOrEqualTo(String value) {
            addCriterion("responsible_engineer <=", value, "responsibleEngineer");
            return (Criteria) this;
        }

        public Criteria andResponsibleEngineerLike(String value) {
            addCriterion("responsible_engineer like", value, "responsibleEngineer");
            return (Criteria) this;
        }

        public Criteria andResponsibleEngineerNotLike(String value) {
            addCriterion("responsible_engineer not like", value, "responsibleEngineer");
            return (Criteria) this;
        }

        public Criteria andResponsibleEngineerIn(List<String> values) {
            addCriterion("responsible_engineer in", values, "responsibleEngineer");
            return (Criteria) this;
        }

        public Criteria andResponsibleEngineerNotIn(List<String> values) {
            addCriterion("responsible_engineer not in", values, "responsibleEngineer");
            return (Criteria) this;
        }

        public Criteria andResponsibleEngineerBetween(String value1, String value2) {
            addCriterion("responsible_engineer between", value1, value2, "responsibleEngineer");
            return (Criteria) this;
        }

        public Criteria andResponsibleEngineerNotBetween(String value1, String value2) {
            addCriterion("responsible_engineer not between", value1, value2, "responsibleEngineer");
            return (Criteria) this;
        }

        public Criteria andPrelimResultStatusIsNull() {
            addCriterion("prelim_result_status is null");
            return (Criteria) this;
        }

        public Criteria andPrelimResultStatusIsNotNull() {
            addCriterion("prelim_result_status is not null");
            return (Criteria) this;
        }

        public Criteria andPrelimResultStatusEqualTo(Integer value) {
            addCriterion("prelim_result_status =", value, "prelimResultStatus");
            return (Criteria) this;
        }

        public Criteria andPrelimResultStatusNotEqualTo(Integer value) {
            addCriterion("prelim_result_status <>", value, "prelimResultStatus");
            return (Criteria) this;
        }

        public Criteria andPrelimResultStatusGreaterThan(Integer value) {
            addCriterion("prelim_result_status >", value, "prelimResultStatus");
            return (Criteria) this;
        }

        public Criteria andPrelimResultStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("prelim_result_status >=", value, "prelimResultStatus");
            return (Criteria) this;
        }

        public Criteria andPrelimResultStatusLessThan(Integer value) {
            addCriterion("prelim_result_status <", value, "prelimResultStatus");
            return (Criteria) this;
        }

        public Criteria andPrelimResultStatusLessThanOrEqualTo(Integer value) {
            addCriterion("prelim_result_status <=", value, "prelimResultStatus");
            return (Criteria) this;
        }

        public Criteria andPrelimResultStatusIn(List<Integer> values) {
            addCriterion("prelim_result_status in", values, "prelimResultStatus");
            return (Criteria) this;
        }

        public Criteria andPrelimResultStatusNotIn(List<Integer> values) {
            addCriterion("prelim_result_status not in", values, "prelimResultStatus");
            return (Criteria) this;
        }

        public Criteria andPrelimResultStatusBetween(Integer value1, Integer value2) {
            addCriterion("prelim_result_status between", value1, value2, "prelimResultStatus");
            return (Criteria) this;
        }

        public Criteria andPrelimResultStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("prelim_result_status not between", value1, value2, "prelimResultStatus");
            return (Criteria) this;
        }

        public Criteria andUploadByIsNull() {
            addCriterion("upload_by is null");
            return (Criteria) this;
        }

        public Criteria andUploadByIsNotNull() {
            addCriterion("upload_by is not null");
            return (Criteria) this;
        }

        public Criteria andUploadByEqualTo(String value) {
            addCriterion("upload_by =", value, "uploadBy");
            return (Criteria) this;
        }

        public Criteria andUploadByNotEqualTo(String value) {
            addCriterion("upload_by <>", value, "uploadBy");
            return (Criteria) this;
        }

        public Criteria andUploadByGreaterThan(String value) {
            addCriterion("upload_by >", value, "uploadBy");
            return (Criteria) this;
        }

        public Criteria andUploadByGreaterThanOrEqualTo(String value) {
            addCriterion("upload_by >=", value, "uploadBy");
            return (Criteria) this;
        }

        public Criteria andUploadByLessThan(String value) {
            addCriterion("upload_by <", value, "uploadBy");
            return (Criteria) this;
        }

        public Criteria andUploadByLessThanOrEqualTo(String value) {
            addCriterion("upload_by <=", value, "uploadBy");
            return (Criteria) this;
        }

        public Criteria andUploadByLike(String value) {
            addCriterion("upload_by like", value, "uploadBy");
            return (Criteria) this;
        }

        public Criteria andUploadByNotLike(String value) {
            addCriterion("upload_by not like", value, "uploadBy");
            return (Criteria) this;
        }

        public Criteria andUploadByIn(List<String> values) {
            addCriterion("upload_by in", values, "uploadBy");
            return (Criteria) this;
        }

        public Criteria andUploadByNotIn(List<String> values) {
            addCriterion("upload_by not in", values, "uploadBy");
            return (Criteria) this;
        }

        public Criteria andUploadByBetween(String value1, String value2) {
            addCriterion("upload_by between", value1, value2, "uploadBy");
            return (Criteria) this;
        }

        public Criteria andUploadByNotBetween(String value1, String value2) {
            addCriterion("upload_by not between", value1, value2, "uploadBy");
            return (Criteria) this;
        }

        public Criteria andUploadDateIsNull() {
            addCriterion("upload_date is null");
            return (Criteria) this;
        }

        public Criteria andUploadDateIsNotNull() {
            addCriterion("upload_date is not null");
            return (Criteria) this;
        }

        public Criteria andUploadDateEqualTo(Date value) {
            addCriterion("upload_date =", value, "uploadDate");
            return (Criteria) this;
        }

        public Criteria andUploadDateNotEqualTo(Date value) {
            addCriterion("upload_date <>", value, "uploadDate");
            return (Criteria) this;
        }

        public Criteria andUploadDateGreaterThan(Date value) {
            addCriterion("upload_date >", value, "uploadDate");
            return (Criteria) this;
        }

        public Criteria andUploadDateGreaterThanOrEqualTo(Date value) {
            addCriterion("upload_date >=", value, "uploadDate");
            return (Criteria) this;
        }

        public Criteria andUploadDateLessThan(Date value) {
            addCriterion("upload_date <", value, "uploadDate");
            return (Criteria) this;
        }

        public Criteria andUploadDateLessThanOrEqualTo(Date value) {
            addCriterion("upload_date <=", value, "uploadDate");
            return (Criteria) this;
        }

        public Criteria andUploadDateIn(List<Date> values) {
            addCriterion("upload_date in", values, "uploadDate");
            return (Criteria) this;
        }

        public Criteria andUploadDateNotIn(List<Date> values) {
            addCriterion("upload_date not in", values, "uploadDate");
            return (Criteria) this;
        }

        public Criteria andUploadDateBetween(Date value1, Date value2) {
            addCriterion("upload_date between", value1, value2, "uploadDate");
            return (Criteria) this;
        }

        public Criteria andUploadDateNotBetween(Date value1, Date value2) {
            addCriterion("upload_date not between", value1, value2, "uploadDate");
            return (Criteria) this;
        }

        public Criteria andGenerateByIsNull() {
            addCriterion("generate_by is null");
            return (Criteria) this;
        }

        public Criteria andGenerateByIsNotNull() {
            addCriterion("generate_by is not null");
            return (Criteria) this;
        }

        public Criteria andGenerateByEqualTo(String value) {
            addCriterion("generate_by =", value, "generateBy");
            return (Criteria) this;
        }

        public Criteria andGenerateByNotEqualTo(String value) {
            addCriterion("generate_by <>", value, "generateBy");
            return (Criteria) this;
        }

        public Criteria andGenerateByGreaterThan(String value) {
            addCriterion("generate_by >", value, "generateBy");
            return (Criteria) this;
        }

        public Criteria andGenerateByGreaterThanOrEqualTo(String value) {
            addCriterion("generate_by >=", value, "generateBy");
            return (Criteria) this;
        }

        public Criteria andGenerateByLessThan(String value) {
            addCriterion("generate_by <", value, "generateBy");
            return (Criteria) this;
        }

        public Criteria andGenerateByLessThanOrEqualTo(String value) {
            addCriterion("generate_by <=", value, "generateBy");
            return (Criteria) this;
        }

        public Criteria andGenerateByLike(String value) {
            addCriterion("generate_by like", value, "generateBy");
            return (Criteria) this;
        }

        public Criteria andGenerateByNotLike(String value) {
            addCriterion("generate_by not like", value, "generateBy");
            return (Criteria) this;
        }

        public Criteria andGenerateByIn(List<String> values) {
            addCriterion("generate_by in", values, "generateBy");
            return (Criteria) this;
        }

        public Criteria andGenerateByNotIn(List<String> values) {
            addCriterion("generate_by not in", values, "generateBy");
            return (Criteria) this;
        }

        public Criteria andGenerateByBetween(String value1, String value2) {
            addCriterion("generate_by between", value1, value2, "generateBy");
            return (Criteria) this;
        }

        public Criteria andGenerateByNotBetween(String value1, String value2) {
            addCriterion("generate_by not between", value1, value2, "generateBy");
            return (Criteria) this;
        }

        public Criteria andGenerateDateIsNull() {
            addCriterion("generate_date is null");
            return (Criteria) this;
        }

        public Criteria andGenerateDateIsNotNull() {
            addCriterion("generate_date is not null");
            return (Criteria) this;
        }

        public Criteria andGenerateDateEqualTo(Date value) {
            addCriterion("generate_date =", value, "generateDate");
            return (Criteria) this;
        }

        public Criteria andGenerateDateNotEqualTo(Date value) {
            addCriterion("generate_date <>", value, "generateDate");
            return (Criteria) this;
        }

        public Criteria andGenerateDateGreaterThan(Date value) {
            addCriterion("generate_date >", value, "generateDate");
            return (Criteria) this;
        }

        public Criteria andGenerateDateGreaterThanOrEqualTo(Date value) {
            addCriterion("generate_date >=", value, "generateDate");
            return (Criteria) this;
        }

        public Criteria andGenerateDateLessThan(Date value) {
            addCriterion("generate_date <", value, "generateDate");
            return (Criteria) this;
        }

        public Criteria andGenerateDateLessThanOrEqualTo(Date value) {
            addCriterion("generate_date <=", value, "generateDate");
            return (Criteria) this;
        }

        public Criteria andGenerateDateIn(List<Date> values) {
            addCriterion("generate_date in", values, "generateDate");
            return (Criteria) this;
        }

        public Criteria andGenerateDateNotIn(List<Date> values) {
            addCriterion("generate_date not in", values, "generateDate");
            return (Criteria) this;
        }

        public Criteria andGenerateDateBetween(Date value1, Date value2) {
            addCriterion("generate_date between", value1, value2, "generateDate");
            return (Criteria) this;
        }

        public Criteria andGenerateDateNotBetween(Date value1, Date value2) {
            addCriterion("generate_date not between", value1, value2, "generateDate");
            return (Criteria) this;
        }

        public Criteria andReviewByIsNull() {
            addCriterion("review_by is null");
            return (Criteria) this;
        }

        public Criteria andReviewByIsNotNull() {
            addCriterion("review_by is not null");
            return (Criteria) this;
        }

        public Criteria andReviewByEqualTo(String value) {
            addCriterion("review_by =", value, "reviewBy");
            return (Criteria) this;
        }

        public Criteria andReviewByNotEqualTo(String value) {
            addCriterion("review_by <>", value, "reviewBy");
            return (Criteria) this;
        }

        public Criteria andReviewByGreaterThan(String value) {
            addCriterion("review_by >", value, "reviewBy");
            return (Criteria) this;
        }

        public Criteria andReviewByGreaterThanOrEqualTo(String value) {
            addCriterion("review_by >=", value, "reviewBy");
            return (Criteria) this;
        }

        public Criteria andReviewByLessThan(String value) {
            addCriterion("review_by <", value, "reviewBy");
            return (Criteria) this;
        }

        public Criteria andReviewByLessThanOrEqualTo(String value) {
            addCriterion("review_by <=", value, "reviewBy");
            return (Criteria) this;
        }

        public Criteria andReviewByLike(String value) {
            addCriterion("review_by like", value, "reviewBy");
            return (Criteria) this;
        }

        public Criteria andReviewByNotLike(String value) {
            addCriterion("review_by not like", value, "reviewBy");
            return (Criteria) this;
        }

        public Criteria andReviewByIn(List<String> values) {
            addCriterion("review_by in", values, "reviewBy");
            return (Criteria) this;
        }

        public Criteria andReviewByNotIn(List<String> values) {
            addCriterion("review_by not in", values, "reviewBy");
            return (Criteria) this;
        }

        public Criteria andReviewByBetween(String value1, String value2) {
            addCriterion("review_by between", value1, value2, "reviewBy");
            return (Criteria) this;
        }

        public Criteria andReviewByNotBetween(String value1, String value2) {
            addCriterion("review_by not between", value1, value2, "reviewBy");
            return (Criteria) this;
        }

        public Criteria andReviewDateIsNull() {
            addCriterion("review_date is null");
            return (Criteria) this;
        }

        public Criteria andReviewDateIsNotNull() {
            addCriterion("review_date is not null");
            return (Criteria) this;
        }

        public Criteria andReviewDateEqualTo(Date value) {
            addCriterion("review_date =", value, "reviewDate");
            return (Criteria) this;
        }

        public Criteria andReviewDateNotEqualTo(Date value) {
            addCriterion("review_date <>", value, "reviewDate");
            return (Criteria) this;
        }

        public Criteria andReviewDateGreaterThan(Date value) {
            addCriterion("review_date >", value, "reviewDate");
            return (Criteria) this;
        }

        public Criteria andReviewDateGreaterThanOrEqualTo(Date value) {
            addCriterion("review_date >=", value, "reviewDate");
            return (Criteria) this;
        }

        public Criteria andReviewDateLessThan(Date value) {
            addCriterion("review_date <", value, "reviewDate");
            return (Criteria) this;
        }

        public Criteria andReviewDateLessThanOrEqualTo(Date value) {
            addCriterion("review_date <=", value, "reviewDate");
            return (Criteria) this;
        }

        public Criteria andReviewDateIn(List<Date> values) {
            addCriterion("review_date in", values, "reviewDate");
            return (Criteria) this;
        }

        public Criteria andReviewDateNotIn(List<Date> values) {
            addCriterion("review_date not in", values, "reviewDate");
            return (Criteria) this;
        }

        public Criteria andReviewDateBetween(Date value1, Date value2) {
            addCriterion("review_date between", value1, value2, "reviewDate");
            return (Criteria) this;
        }

        public Criteria andReviewDateNotBetween(Date value1, Date value2) {
            addCriterion("review_date not between", value1, value2, "reviewDate");
            return (Criteria) this;
        }

        public Criteria andDeliveredByIsNull() {
            addCriterion("delivered_by is null");
            return (Criteria) this;
        }

        public Criteria andDeliveredByIsNotNull() {
            addCriterion("delivered_by is not null");
            return (Criteria) this;
        }

        public Criteria andDeliveredByEqualTo(String value) {
            addCriterion("delivered_by =", value, "deliveredBy");
            return (Criteria) this;
        }

        public Criteria andDeliveredByNotEqualTo(String value) {
            addCriterion("delivered_by <>", value, "deliveredBy");
            return (Criteria) this;
        }

        public Criteria andDeliveredByGreaterThan(String value) {
            addCriterion("delivered_by >", value, "deliveredBy");
            return (Criteria) this;
        }

        public Criteria andDeliveredByGreaterThanOrEqualTo(String value) {
            addCriterion("delivered_by >=", value, "deliveredBy");
            return (Criteria) this;
        }

        public Criteria andDeliveredByLessThan(String value) {
            addCriterion("delivered_by <", value, "deliveredBy");
            return (Criteria) this;
        }

        public Criteria andDeliveredByLessThanOrEqualTo(String value) {
            addCriterion("delivered_by <=", value, "deliveredBy");
            return (Criteria) this;
        }

        public Criteria andDeliveredByLike(String value) {
            addCriterion("delivered_by like", value, "deliveredBy");
            return (Criteria) this;
        }

        public Criteria andDeliveredByNotLike(String value) {
            addCriterion("delivered_by not like", value, "deliveredBy");
            return (Criteria) this;
        }

        public Criteria andDeliveredByIn(List<String> values) {
            addCriterion("delivered_by in", values, "deliveredBy");
            return (Criteria) this;
        }

        public Criteria andDeliveredByNotIn(List<String> values) {
            addCriterion("delivered_by not in", values, "deliveredBy");
            return (Criteria) this;
        }

        public Criteria andDeliveredByBetween(String value1, String value2) {
            addCriterion("delivered_by between", value1, value2, "deliveredBy");
            return (Criteria) this;
        }

        public Criteria andDeliveredByNotBetween(String value1, String value2) {
            addCriterion("delivered_by not between", value1, value2, "deliveredBy");
            return (Criteria) this;
        }

        public Criteria andDeliveredDateIsNull() {
            addCriterion("delivered_date is null");
            return (Criteria) this;
        }

        public Criteria andDeliveredDateIsNotNull() {
            addCriterion("delivered_date is not null");
            return (Criteria) this;
        }

        public Criteria andDeliveredDateEqualTo(Date value) {
            addCriterion("delivered_date =", value, "deliveredDate");
            return (Criteria) this;
        }

        public Criteria andDeliveredDateNotEqualTo(Date value) {
            addCriterion("delivered_date <>", value, "deliveredDate");
            return (Criteria) this;
        }

        public Criteria andDeliveredDateGreaterThan(Date value) {
            addCriterion("delivered_date >", value, "deliveredDate");
            return (Criteria) this;
        }

        public Criteria andDeliveredDateGreaterThanOrEqualTo(Date value) {
            addCriterion("delivered_date >=", value, "deliveredDate");
            return (Criteria) this;
        }

        public Criteria andDeliveredDateLessThan(Date value) {
            addCriterion("delivered_date <", value, "deliveredDate");
            return (Criteria) this;
        }

        public Criteria andDeliveredDateLessThanOrEqualTo(Date value) {
            addCriterion("delivered_date <=", value, "deliveredDate");
            return (Criteria) this;
        }

        public Criteria andDeliveredDateIn(List<Date> values) {
            addCriterion("delivered_date in", values, "deliveredDate");
            return (Criteria) this;
        }

        public Criteria andDeliveredDateNotIn(List<Date> values) {
            addCriterion("delivered_date not in", values, "deliveredDate");
            return (Criteria) this;
        }

        public Criteria andDeliveredDateBetween(Date value1, Date value2) {
            addCriterion("delivered_date between", value1, value2, "deliveredDate");
            return (Criteria) this;
        }

        public Criteria andDeliveredDateNotBetween(Date value1, Date value2) {
            addCriterion("delivered_date not between", value1, value2, "deliveredDate");
            return (Criteria) this;
        }

        public Criteria andCancelFlagIsNull() {
            addCriterion("cancel_flag is null");
            return (Criteria) this;
        }

        public Criteria andCancelFlagIsNotNull() {
            addCriterion("cancel_flag is not null");
            return (Criteria) this;
        }

        public Criteria andCancelFlagEqualTo(Byte value) {
            addCriterion("cancel_flag =", value, "cancelFlag");
            return (Criteria) this;
        }

        public Criteria andCancelFlagNotEqualTo(Byte value) {
            addCriterion("cancel_flag <>", value, "cancelFlag");
            return (Criteria) this;
        }

        public Criteria andCancelFlagGreaterThan(Byte value) {
            addCriterion("cancel_flag >", value, "cancelFlag");
            return (Criteria) this;
        }

        public Criteria andCancelFlagGreaterThanOrEqualTo(Byte value) {
            addCriterion("cancel_flag >=", value, "cancelFlag");
            return (Criteria) this;
        }

        public Criteria andCancelFlagLessThan(Byte value) {
            addCriterion("cancel_flag <", value, "cancelFlag");
            return (Criteria) this;
        }

        public Criteria andCancelFlagLessThanOrEqualTo(Byte value) {
            addCriterion("cancel_flag <=", value, "cancelFlag");
            return (Criteria) this;
        }

        public Criteria andCancelFlagIn(List<Byte> values) {
            addCriterion("cancel_flag in", values, "cancelFlag");
            return (Criteria) this;
        }

        public Criteria andCancelFlagNotIn(List<Byte> values) {
            addCriterion("cancel_flag not in", values, "cancelFlag");
            return (Criteria) this;
        }

        public Criteria andCancelFlagBetween(Byte value1, Byte value2) {
            addCriterion("cancel_flag between", value1, value2, "cancelFlag");
            return (Criteria) this;
        }

        public Criteria andCancelFlagNotBetween(Byte value1, Byte value2) {
            addCriterion("cancel_flag not between", value1, value2, "cancelFlag");
            return (Criteria) this;
        }

        public Criteria andCancelByIsNull() {
            addCriterion("cancel_by is null");
            return (Criteria) this;
        }

        public Criteria andCancelByIsNotNull() {
            addCriterion("cancel_by is not null");
            return (Criteria) this;
        }

        public Criteria andCancelByEqualTo(String value) {
            addCriterion("cancel_by =", value, "cancelBy");
            return (Criteria) this;
        }

        public Criteria andCancelByNotEqualTo(String value) {
            addCriterion("cancel_by <>", value, "cancelBy");
            return (Criteria) this;
        }

        public Criteria andCancelByGreaterThan(String value) {
            addCriterion("cancel_by >", value, "cancelBy");
            return (Criteria) this;
        }

        public Criteria andCancelByGreaterThanOrEqualTo(String value) {
            addCriterion("cancel_by >=", value, "cancelBy");
            return (Criteria) this;
        }

        public Criteria andCancelByLessThan(String value) {
            addCriterion("cancel_by <", value, "cancelBy");
            return (Criteria) this;
        }

        public Criteria andCancelByLessThanOrEqualTo(String value) {
            addCriterion("cancel_by <=", value, "cancelBy");
            return (Criteria) this;
        }

        public Criteria andCancelByLike(String value) {
            addCriterion("cancel_by like", value, "cancelBy");
            return (Criteria) this;
        }

        public Criteria andCancelByNotLike(String value) {
            addCriterion("cancel_by not like", value, "cancelBy");
            return (Criteria) this;
        }

        public Criteria andCancelByIn(List<String> values) {
            addCriterion("cancel_by in", values, "cancelBy");
            return (Criteria) this;
        }

        public Criteria andCancelByNotIn(List<String> values) {
            addCriterion("cancel_by not in", values, "cancelBy");
            return (Criteria) this;
        }

        public Criteria andCancelByBetween(String value1, String value2) {
            addCriterion("cancel_by between", value1, value2, "cancelBy");
            return (Criteria) this;
        }

        public Criteria andCancelByNotBetween(String value1, String value2) {
            addCriterion("cancel_by not between", value1, value2, "cancelBy");
            return (Criteria) this;
        }

        public Criteria andCancelDateIsNull() {
            addCriterion("cancel_date is null");
            return (Criteria) this;
        }

        public Criteria andCancelDateIsNotNull() {
            addCriterion("cancel_date is not null");
            return (Criteria) this;
        }

        public Criteria andCancelDateEqualTo(Date value) {
            addCriterion("cancel_date =", value, "cancelDate");
            return (Criteria) this;
        }

        public Criteria andCancelDateNotEqualTo(Date value) {
            addCriterion("cancel_date <>", value, "cancelDate");
            return (Criteria) this;
        }

        public Criteria andCancelDateGreaterThan(Date value) {
            addCriterion("cancel_date >", value, "cancelDate");
            return (Criteria) this;
        }

        public Criteria andCancelDateGreaterThanOrEqualTo(Date value) {
            addCriterion("cancel_date >=", value, "cancelDate");
            return (Criteria) this;
        }

        public Criteria andCancelDateLessThan(Date value) {
            addCriterion("cancel_date <", value, "cancelDate");
            return (Criteria) this;
        }

        public Criteria andCancelDateLessThanOrEqualTo(Date value) {
            addCriterion("cancel_date <=", value, "cancelDate");
            return (Criteria) this;
        }

        public Criteria andCancelDateIn(List<Date> values) {
            addCriterion("cancel_date in", values, "cancelDate");
            return (Criteria) this;
        }

        public Criteria andCancelDateNotIn(List<Date> values) {
            addCriterion("cancel_date not in", values, "cancelDate");
            return (Criteria) this;
        }

        public Criteria andCancelDateBetween(Date value1, Date value2) {
            addCriterion("cancel_date between", value1, value2, "cancelDate");
            return (Criteria) this;
        }

        public Criteria andCancelDateNotBetween(Date value1, Date value2) {
            addCriterion("cancel_date not between", value1, value2, "cancelDate");
            return (Criteria) this;
        }

        public Criteria andRemarkIsNull() {
            addCriterion("remark is null");
            return (Criteria) this;
        }

        public Criteria andRemarkIsNotNull() {
            addCriterion("remark is not null");
            return (Criteria) this;
        }

        public Criteria andRemarkEqualTo(String value) {
            addCriterion("remark =", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotEqualTo(String value) {
            addCriterion("remark <>", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkGreaterThan(String value) {
            addCriterion("remark >", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkGreaterThanOrEqualTo(String value) {
            addCriterion("remark >=", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkLessThan(String value) {
            addCriterion("remark <", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkLessThanOrEqualTo(String value) {
            addCriterion("remark <=", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkLike(String value) {
            addCriterion("remark like", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotLike(String value) {
            addCriterion("remark not like", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkIn(List<String> values) {
            addCriterion("remark in", values, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotIn(List<String> values) {
            addCriterion("remark not in", values, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkBetween(String value1, String value2) {
            addCriterion("remark between", value1, value2, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotBetween(String value1, String value2) {
            addCriterion("remark not between", value1, value2, "remark");
            return (Criteria) this;
        }

        public Criteria andCreatedDateIsNull() {
            addCriterion("created_date is null");
            return (Criteria) this;
        }

        public Criteria andCreatedDateIsNotNull() {
            addCriterion("created_date is not null");
            return (Criteria) this;
        }

        public Criteria andCreatedDateEqualTo(Date value) {
            addCriterion("created_date =", value, "createdDate");
            return (Criteria) this;
        }

        public Criteria andCreatedDateNotEqualTo(Date value) {
            addCriterion("created_date <>", value, "createdDate");
            return (Criteria) this;
        }

        public Criteria andCreatedDateGreaterThan(Date value) {
            addCriterion("created_date >", value, "createdDate");
            return (Criteria) this;
        }

        public Criteria andCreatedDateGreaterThanOrEqualTo(Date value) {
            addCriterion("created_date >=", value, "createdDate");
            return (Criteria) this;
        }

        public Criteria andCreatedDateLessThan(Date value) {
            addCriterion("created_date <", value, "createdDate");
            return (Criteria) this;
        }

        public Criteria andCreatedDateLessThanOrEqualTo(Date value) {
            addCriterion("created_date <=", value, "createdDate");
            return (Criteria) this;
        }

        public Criteria andCreatedDateIn(List<Date> values) {
            addCriterion("created_date in", values, "createdDate");
            return (Criteria) this;
        }

        public Criteria andCreatedDateNotIn(List<Date> values) {
            addCriterion("created_date not in", values, "createdDate");
            return (Criteria) this;
        }

        public Criteria andCreatedDateBetween(Date value1, Date value2) {
            addCriterion("created_date between", value1, value2, "createdDate");
            return (Criteria) this;
        }

        public Criteria andCreatedDateNotBetween(Date value1, Date value2) {
            addCriterion("created_date not between", value1, value2, "createdDate");
            return (Criteria) this;
        }

        public Criteria andCreatedByIsNull() {
            addCriterion("created_by is null");
            return (Criteria) this;
        }

        public Criteria andCreatedByIsNotNull() {
            addCriterion("created_by is not null");
            return (Criteria) this;
        }

        public Criteria andCreatedByEqualTo(String value) {
            addCriterion("created_by =", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotEqualTo(String value) {
            addCriterion("created_by <>", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByGreaterThan(String value) {
            addCriterion("created_by >", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByGreaterThanOrEqualTo(String value) {
            addCriterion("created_by >=", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByLessThan(String value) {
            addCriterion("created_by <", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByLessThanOrEqualTo(String value) {
            addCriterion("created_by <=", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByLike(String value) {
            addCriterion("created_by like", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotLike(String value) {
            addCriterion("created_by not like", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByIn(List<String> values) {
            addCriterion("created_by in", values, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotIn(List<String> values) {
            addCriterion("created_by not in", values, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByBetween(String value1, String value2) {
            addCriterion("created_by between", value1, value2, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotBetween(String value1, String value2) {
            addCriterion("created_by not between", value1, value2, "createdBy");
            return (Criteria) this;
        }

        public Criteria andModifiedDateIsNull() {
            addCriterion("modified_date is null");
            return (Criteria) this;
        }

        public Criteria andModifiedDateIsNotNull() {
            addCriterion("modified_date is not null");
            return (Criteria) this;
        }

        public Criteria andModifiedDateEqualTo(Date value) {
            addCriterion("modified_date =", value, "modifiedDate");
            return (Criteria) this;
        }

        public Criteria andModifiedDateNotEqualTo(Date value) {
            addCriterion("modified_date <>", value, "modifiedDate");
            return (Criteria) this;
        }

        public Criteria andModifiedDateGreaterThan(Date value) {
            addCriterion("modified_date >", value, "modifiedDate");
            return (Criteria) this;
        }

        public Criteria andModifiedDateGreaterThanOrEqualTo(Date value) {
            addCriterion("modified_date >=", value, "modifiedDate");
            return (Criteria) this;
        }

        public Criteria andModifiedDateLessThan(Date value) {
            addCriterion("modified_date <", value, "modifiedDate");
            return (Criteria) this;
        }

        public Criteria andModifiedDateLessThanOrEqualTo(Date value) {
            addCriterion("modified_date <=", value, "modifiedDate");
            return (Criteria) this;
        }

        public Criteria andModifiedDateIn(List<Date> values) {
            addCriterion("modified_date in", values, "modifiedDate");
            return (Criteria) this;
        }

        public Criteria andModifiedDateNotIn(List<Date> values) {
            addCriterion("modified_date not in", values, "modifiedDate");
            return (Criteria) this;
        }

        public Criteria andModifiedDateBetween(Date value1, Date value2) {
            addCriterion("modified_date between", value1, value2, "modifiedDate");
            return (Criteria) this;
        }

        public Criteria andModifiedDateNotBetween(Date value1, Date value2) {
            addCriterion("modified_date not between", value1, value2, "modifiedDate");
            return (Criteria) this;
        }

        public Criteria andModifiedByIsNull() {
            addCriterion("modified_by is null");
            return (Criteria) this;
        }

        public Criteria andModifiedByIsNotNull() {
            addCriterion("modified_by is not null");
            return (Criteria) this;
        }

        public Criteria andModifiedByEqualTo(String value) {
            addCriterion("modified_by =", value, "modifiedBy");
            return (Criteria) this;
        }

        public Criteria andModifiedByNotEqualTo(String value) {
            addCriterion("modified_by <>", value, "modifiedBy");
            return (Criteria) this;
        }

        public Criteria andModifiedByGreaterThan(String value) {
            addCriterion("modified_by >", value, "modifiedBy");
            return (Criteria) this;
        }

        public Criteria andModifiedByGreaterThanOrEqualTo(String value) {
            addCriterion("modified_by >=", value, "modifiedBy");
            return (Criteria) this;
        }

        public Criteria andModifiedByLessThan(String value) {
            addCriterion("modified_by <", value, "modifiedBy");
            return (Criteria) this;
        }

        public Criteria andModifiedByLessThanOrEqualTo(String value) {
            addCriterion("modified_by <=", value, "modifiedBy");
            return (Criteria) this;
        }

        public Criteria andModifiedByLike(String value) {
            addCriterion("modified_by like", value, "modifiedBy");
            return (Criteria) this;
        }

        public Criteria andModifiedByNotLike(String value) {
            addCriterion("modified_by not like", value, "modifiedBy");
            return (Criteria) this;
        }

        public Criteria andModifiedByIn(List<String> values) {
            addCriterion("modified_by in", values, "modifiedBy");
            return (Criteria) this;
        }

        public Criteria andModifiedByNotIn(List<String> values) {
            addCriterion("modified_by not in", values, "modifiedBy");
            return (Criteria) this;
        }

        public Criteria andModifiedByBetween(String value1, String value2) {
            addCriterion("modified_by between", value1, value2, "modifiedBy");
            return (Criteria) this;
        }

        public Criteria andModifiedByNotBetween(String value1, String value2) {
            addCriterion("modified_by not between", value1, value2, "modifiedBy");
            return (Criteria) this;
        }

        public Criteria andActiveIndicatorIsNull() {
            addCriterion("active_indicator is null");
            return (Criteria) this;
        }

        public Criteria andActiveIndicatorIsNotNull() {
            addCriterion("active_indicator is not null");
            return (Criteria) this;
        }

        public Criteria andActiveIndicatorEqualTo(Integer value) {
            addCriterion("active_indicator =", value, "activeIndicator");
            return (Criteria) this;
        }

        public Criteria andActiveIndicatorNotEqualTo(Integer value) {
            addCriterion("active_indicator <>", value, "activeIndicator");
            return (Criteria) this;
        }

        public Criteria andActiveIndicatorGreaterThan(Integer value) {
            addCriterion("active_indicator >", value, "activeIndicator");
            return (Criteria) this;
        }

        public Criteria andActiveIndicatorGreaterThanOrEqualTo(Integer value) {
            addCriterion("active_indicator >=", value, "activeIndicator");
            return (Criteria) this;
        }

        public Criteria andActiveIndicatorLessThan(Integer value) {
            addCriterion("active_indicator <", value, "activeIndicator");
            return (Criteria) this;
        }

        public Criteria andActiveIndicatorLessThanOrEqualTo(Integer value) {
            addCriterion("active_indicator <=", value, "activeIndicator");
            return (Criteria) this;
        }

        public Criteria andActiveIndicatorIn(List<Integer> values) {
            addCriterion("active_indicator in", values, "activeIndicator");
            return (Criteria) this;
        }

        public Criteria andActiveIndicatorNotIn(List<Integer> values) {
            addCriterion("active_indicator not in", values, "activeIndicator");
            return (Criteria) this;
        }

        public Criteria andActiveIndicatorBetween(Integer value1, Integer value2) {
            addCriterion("active_indicator between", value1, value2, "activeIndicator");
            return (Criteria) this;
        }

        public Criteria andActiveIndicatorNotBetween(Integer value1, Integer value2) {
            addCriterion("active_indicator not between", value1, value2, "activeIndicator");
            return (Criteria) this;
        }


        public Criteria andReportIdIsNull() {
            addCriterion("report_id is null");
            return (Criteria) this;
        }

        public Criteria andReportIdIsNotNull() {
            addCriterion("report_id is not null");
            return (Criteria) this;
        }

        public Criteria andReportIdEqualTo(String value) {
            addCriterion("report_id =", value, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdNotEqualTo(String value) {
            addCriterion("report_id <>", value, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdGreaterThan(String value) {
            addCriterion("report_id >", value, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdGreaterThanOrEqualTo(String value) {
            addCriterion("report_id >=", value, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdLessThan(String value) {
            addCriterion("report_id <", value, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdLessThanOrEqualTo(String value) {
            addCriterion("report_id <=", value, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdLike(String value) {
            addCriterion("report_id like", value, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdNotLike(String value) {
            addCriterion("report_id not like", value, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdIn(List<String> values) {
            addCriterion("report_id in", values, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdNotIn(List<String> values) {
            addCriterion("report_id not in", values, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdBetween(String value1, String value2) {
            addCriterion("report_id between", value1, value2, "reportId");
            return (Criteria) this;
        }

        public Criteria andReportIdNotBetween(String value1, String value2) {
            addCriterion("report_id not between", value1, value2, "reportId");
            return (Criteria) this;
        }


        public Criteria andReportNoIsNull() {
            addCriterion("report_no is null");
            return (Criteria) this;
        }

        public Criteria andReportNoIsNotNull() {
            addCriterion("report_no is not null");
            return (Criteria) this;
        }

        public Criteria andReportNoEqualTo(String value) {
            addCriterion("report_no =", value, "reportNo");
            return (Criteria) this;
        }

        public Criteria andReportNoNotEqualTo(String value) {
            addCriterion("report_no <>", value, "reportNo");
            return (Criteria) this;
        }

        public Criteria andReportNoGreaterThan(String value) {
            addCriterion("report_no >", value, "reportNo");
            return (Criteria) this;
        }

        public Criteria andReportNoGreaterThanOrEqualTo(String value) {
            addCriterion("report_no >=", value, "reportNo");
            return (Criteria) this;
        }

        public Criteria andReportNoLessThan(String value) {
            addCriterion("report_no <", value, "reportNo");
            return (Criteria) this;
        }

        public Criteria andReportNoLessThanOrEqualTo(String value) {
            addCriterion("report_no <=", value, "reportNo");
            return (Criteria) this;
        }

        public Criteria andReportNoLike(String value) {
            addCriterion("report_no like", value, "reportNo");
            return (Criteria) this;
        }

        public Criteria andReportNoNotLike(String value) {
            addCriterion("report_no not like", value, "reportNo");
            return (Criteria) this;
        }

        public Criteria andReportNoIn(List<String> values) {
            addCriterion("report_no in", values, "reportNo");
            return (Criteria) this;
        }

        public Criteria andReportNoNotIn(List<String> values) {
            addCriterion("report_no not in", values, "reportNo");
            return (Criteria) this;
        }

        public Criteria andReportNoBetween(String value1, String value2) {
            addCriterion("report_no between", value1, value2, "reportNo");
            return (Criteria) this;
        }

        public Criteria andReportNoNotBetween(String value1, String value2) {
            addCriterion("report_no not between", value1, value2, "reportNo");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}