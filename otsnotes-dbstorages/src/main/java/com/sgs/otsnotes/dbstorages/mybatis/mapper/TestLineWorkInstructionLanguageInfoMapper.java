package com.sgs.otsnotes.dbstorages.mybatis.mapper;

import com.sgs.otsnotes.dbstorages.mybatis.model.TestLineWorkInstructionLanguageInfoExample;
import com.sgs.otsnotes.dbstorages.mybatis.model.TestLineWorkInstructionLanguageInfoPO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface TestLineWorkInstructionLanguageInfoMapper {
    int countByExample(TestLineWorkInstructionLanguageInfoExample example);

    int deleteByExample(TestLineWorkInstructionLanguageInfoExample example);

    int deleteByPrimaryKey(Long langId);

    int insert(TestLineWorkInstructionLanguageInfoPO record);

    int insertSelective(TestLineWorkInstructionLanguageInfoPO record);

    List<TestLineWorkInstructionLanguageInfoPO> selectByExample(TestLineWorkInstructionLanguageInfoExample example);

    TestLineWorkInstructionLanguageInfoPO selectByPrimaryKey(Long langId);

    int updateByExampleSelective(@Param("record") TestLineWorkInstructionLanguageInfoPO record, @Param("example") TestLineWorkInstructionLanguageInfoExample example);

    int updateByExample(@Param("record") TestLineWorkInstructionLanguageInfoPO record, @Param("example") TestLineWorkInstructionLanguageInfoExample example);

    int updateByPrimaryKeySelective(TestLineWorkInstructionLanguageInfoPO record);

    int updateByPrimaryKey(TestLineWorkInstructionLanguageInfoPO record);
}