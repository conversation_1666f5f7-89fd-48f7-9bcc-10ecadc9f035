package com.sgs.otsnotes.dbstorages.mybatis.model;

import java.util.Date;

public class OrderTestLineVMPO {
    /**
     * orderId VARCHAR(36) 必填<br>
     * ID,Primary key
     */
    private String orderId;

    /**
     * orderNo VARCHAR(50)<br>
     * 
     */
    private String orderNo;

    /**
     * labCode VARCHAR(50)<br>
     * labcode
     */
    private String labCode;

    /**
     * ppVersionId INTEGER(10) 默认值[0]<br>
     * 
     */
    private Integer ppVersionId;

    /**
     * ppBaseId BIGINT(19) 默认值[0]<br>
     * 
     */
    private Long ppBaseId;

    /**
     * PpNo INTEGER(10)<br>
     * 
     */
    private Integer ppNo;

    /**
     * ppName VARCHAR(512)<br>
     * 对应老数据表字段：PPClientRefNo
     */
    private String ppName;

    /**
     * ppTlRelId VARCHAR(36)<br>
     * ID,Primary key
     */
    private String ppTlRelId;

    /**
     * SectionName VARCHAR(200)<br>
     * 
     */
    private String sectionName;

    /**
     * testLineType INTEGER(10) 默认值[0]<br>
     * 0：普通【默认值】、1：Pretreatment、2：SubContract(Order)
     */
    private Integer testLineType;

    /**
     * testLineInstanceId VARCHAR(36) 必填<br>
     * ID,Primary key
     */
    private String testLineInstanceId;

    /**
     * TestItemNo VARCHAR(25)<br>
     * 
     */
    private String testItemNo;

    /**
     * productLineCode VARCHAR(100)<br>
     * 
     */
    private String productLineCode;

    /**
     * testLineBaseId BIGINT(19)<br>
     * 
     */
    private Long testLineBaseId;

    /**
     * testLineVersionId INTEGER(10) 默认值[0] 必填<br>
     * 
     */
    private Integer testLineVersionId;

    /**
     * testLineId INTEGER(10) 必填<br>
     * 
     */
    private Integer testLineId;

    /**
     * testItemCn VARCHAR(512)<br>
     * 
     */
    private String testItemCn;

    /**
     * customerTestLineName VARCHAR(500)<br>
     * 
     */
    private String customerTestLineName;

    /**
     * testStandard VARCHAR(512)<br>
     * 
     */
    private String testStandard;

    /**
     * citationSectionName VARCHAR(512)<br>
     * 
     */
    private String citationSectionName;

    /**
     * artifactVersionId INTEGER(10) 默认值[0]<br>
     * 0：tb_trims_testline_baseinfo.TestLineVersionId
1：tb_trims_pp_baseinfo.PpVersionId
     */
    private Integer artifactVersionId;

    /**
     * testStandardCn VARCHAR(512)<br>
     * 
     */
    private String testStandardCn;

    /**
     * citationSectionNameCn VARCHAR(512)<br>
     * 
     */
    private String citationSectionNameCn;

    /**
     * remark VARCHAR(4000)<br>
     * 
     */
    private String remark;

    /**
     * citationBaseId BIGINT(19)<br>
     * 
     */
    private Long citationBaseId;

    /**
     * citationId INTEGER(10)<br>
     * 
     */
    private Integer citationId;

    /**
     * citationVersionId INTEGER(10)<br>
     * 
     */
    private Integer citationVersionId;

    /**
     * conditionStatus INTEGER(10)<br>
     * 
     */
    private Integer conditionStatus;

    /**
     * labSectionBaseId BIGINT(19)<br>
     * 
     */
    private Long labSectionBaseId;

    /**
     * labsectionbase INTEGER(10)<br>
     * 
     */
    private Integer labsectionbase;

    /**
     * labTeamCode VARCHAR(100)<br>
     * LabTeamCode，CS 提供。length follow CS
     */
    private String labTeamCode;

    /**
     * labSectionName VARCHAR(512) 默认值[] 必填<br>
     * 
     */
    private String labSectionName;

    /**
     * subContractLabCode VARCHAR(30)<br>
     * 
     */
    private String subContractLabCode;

    /**
     * ppTlRelBaseId BIGINT(19) 默认值[0]<br>
     * 
     */
    private Long ppTlRelBaseId;

    /**
     * jobId VARCHAR(36)<br>
     * 
     */
    private String jobId;

    /**
     * jobNo VARCHAR(50)<br>
     * 
     */
    private String jobNo;

    /**
     * subContractID VARCHAR(36)<br>
     * 
     */
    private String subContractID;

    /**
     * subContractNo VARCHAR(50)<br>
     * 
     */
    private String subContractNo;

    /**
     * sampleSegegrationWIID BIGINT(19)<br>
     * 
     */
    private Long sampleSegegrationWIID;

    /**
     * sampleSegegrationWIText VARCHAR(4096)<br>
     * 
     */
    private String sampleSegegrationWIText;

    /**
     * reportSeq INTEGER(10)<br>
     * 
     */
    private Integer reportSeq;

    /**
     * orderSeq INTEGER(10)<br>
     * 
     */
    private Integer orderSeq;

    /**
     * sectionLevel INTEGER(10) 默认值[0]<br>
     * 
     */
    private Integer sectionLevel;

    /**
     * testLineSeq INTEGER(10) 默认值[0]<br>
     * 
     */
    private Integer testLineSeq;

    /**
     * pendingFlag BIT(1) 默认值[b'0']<br>
     * 0 unpending,1pending
     */
    private Boolean pendingFlag;

    /**
     * testLineStatus INTEGER(10)<br>
     * 
     */
    private Integer testLineStatus;

    /**
     * createdDate TIMESTAMP(19)<br>
     * CreatedDate
     */
    private Date createdDate;

    /**
     * testStartDate TIMESTAMP(19)<br>
     * 
     */
    private Date testStartDate;

    /**
     * testEndDate TIMESTAMP(19)<br>
     * 
     */
    private Date testEndDate;

    /**
     * testDueDate TIMESTAMP(19)<br>
     * 
     */
    private Date testDueDate;

    /**
     * engineer VARCHAR(50)<br>
     * 
     */
    private String engineer;

    /**
     * orderId VARCHAR(36) 必填<br>
     * 获得 ID,Primary key
     */
    public String getOrderId() {
        return orderId;
    }

    /**
     * orderId VARCHAR(36) 必填<br>
     * 设置 ID,Primary key
     */
    public void setOrderId(String orderId) {
        this.orderId = orderId == null ? null : orderId.trim();
    }

    /**
     * orderNo VARCHAR(50)<br>
     * 获得 
     */
    public String getOrderNo() {
        return orderNo;
    }

    /**
     * orderNo VARCHAR(50)<br>
     * 设置 
     */
    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo == null ? null : orderNo.trim();
    }

    /**
     * labCode VARCHAR(50)<br>
     * 获得 labcode
     */
    public String getLabCode() {
        return labCode;
    }

    /**
     * labCode VARCHAR(50)<br>
     * 设置 labcode
     */
    public void setLabCode(String labCode) {
        this.labCode = labCode == null ? null : labCode.trim();
    }

    /**
     * ppVersionId INTEGER(10) 默认值[0]<br>
     * 获得 
     */
    public Integer getPpVersionId() {
        return ppVersionId;
    }

    /**
     * ppVersionId INTEGER(10) 默认值[0]<br>
     * 设置 
     */
    public void setPpVersionId(Integer ppVersionId) {
        this.ppVersionId = ppVersionId;
    }

    /**
     * ppBaseId BIGINT(19) 默认值[0]<br>
     * 获得 
     */
    public Long getPpBaseId() {
        return ppBaseId;
    }

    /**
     * ppBaseId BIGINT(19) 默认值[0]<br>
     * 设置 
     */
    public void setPpBaseId(Long ppBaseId) {
        this.ppBaseId = ppBaseId;
    }

    /**
     * PpNo INTEGER(10)<br>
     * 获得 
     */
    public Integer getPpNo() {
        return ppNo;
    }

    /**
     * PpNo INTEGER(10)<br>
     * 设置 
     */
    public void setPpNo(Integer ppNo) {
        this.ppNo = ppNo;
    }

    /**
     * ppName VARCHAR(512)<br>
     * 获得 对应老数据表字段：PPClientRefNo
     */
    public String getPpName() {
        return ppName;
    }

    /**
     * ppName VARCHAR(512)<br>
     * 设置 对应老数据表字段：PPClientRefNo
     */
    public void setPpName(String ppName) {
        this.ppName = ppName == null ? null : ppName.trim();
    }

    /**
     * ppTlRelId VARCHAR(36)<br>
     * 获得 ID,Primary key
     */
    public String getPpTlRelId() {
        return ppTlRelId;
    }

    /**
     * ppTlRelId VARCHAR(36)<br>
     * 设置 ID,Primary key
     */
    public void setPpTlRelId(String ppTlRelId) {
        this.ppTlRelId = ppTlRelId == null ? null : ppTlRelId.trim();
    }

    /**
     * SectionName VARCHAR(200)<br>
     * 获得 
     */
    public String getSectionName() {
        return sectionName;
    }

    /**
     * SectionName VARCHAR(200)<br>
     * 设置 
     */
    public void setSectionName(String sectionName) {
        this.sectionName = sectionName == null ? null : sectionName.trim();
    }

    /**
     * testLineType INTEGER(10) 默认值[0]<br>
     * 获得 0：普通【默认值】、1：Pretreatment、2：SubContract(Order)
     */
    public Integer getTestLineType() {
        return testLineType;
    }

    /**
     * testLineType INTEGER(10) 默认值[0]<br>
     * 设置 0：普通【默认值】、1：Pretreatment、2：SubContract(Order)
     */
    public void setTestLineType(Integer testLineType) {
        this.testLineType = testLineType;
    }

    /**
     * testLineInstanceId VARCHAR(36) 必填<br>
     * 获得 ID,Primary key
     */
    public String getTestLineInstanceId() {
        return testLineInstanceId;
    }

    /**
     * testLineInstanceId VARCHAR(36) 必填<br>
     * 设置 ID,Primary key
     */
    public void setTestLineInstanceId(String testLineInstanceId) {
        this.testLineInstanceId = testLineInstanceId == null ? null : testLineInstanceId.trim();
    }

    /**
     * TestItemNo VARCHAR(25)<br>
     * 获得 
     */
    public String getTestItemNo() {
        return testItemNo;
    }

    /**
     * TestItemNo VARCHAR(25)<br>
     * 设置 
     */
    public void setTestItemNo(String testItemNo) {
        this.testItemNo = testItemNo == null ? null : testItemNo.trim();
    }

    /**
     * productLineCode VARCHAR(100)<br>
     * 获得 
     */
    public String getProductLineCode() {
        return productLineCode;
    }

    /**
     * productLineCode VARCHAR(100)<br>
     * 设置 
     */
    public void setProductLineCode(String productLineCode) {
        this.productLineCode = productLineCode == null ? null : productLineCode.trim();
    }

    /**
     * testLineBaseId BIGINT(19)<br>
     * 获得 
     */
    public Long getTestLineBaseId() {
        return testLineBaseId;
    }

    /**
     * testLineBaseId BIGINT(19)<br>
     * 设置 
     */
    public void setTestLineBaseId(Long testLineBaseId) {
        this.testLineBaseId = testLineBaseId;
    }

    /**
     * testLineVersionId INTEGER(10) 默认值[0] 必填<br>
     * 获得 
     */
    public Integer getTestLineVersionId() {
        return testLineVersionId;
    }

    /**
     * testLineVersionId INTEGER(10) 默认值[0] 必填<br>
     * 设置 
     */
    public void setTestLineVersionId(Integer testLineVersionId) {
        this.testLineVersionId = testLineVersionId;
    }

    /**
     * testLineId INTEGER(10) 必填<br>
     * 获得 
     */
    public Integer getTestLineId() {
        return testLineId;
    }

    /**
     * testLineId INTEGER(10) 必填<br>
     * 设置 
     */
    public void setTestLineId(Integer testLineId) {
        this.testLineId = testLineId;
    }

    /**
     * testItemCn VARCHAR(512)<br>
     * 获得 
     */
    public String getTestItemCn() {
        return testItemCn;
    }

    /**
     * testItemCn VARCHAR(512)<br>
     * 设置 
     */
    public void setTestItemCn(String testItemCn) {
        this.testItemCn = testItemCn == null ? null : testItemCn.trim();
    }

    /**
     * customerTestLineName VARCHAR(500)<br>
     * 获得 
     */
    public String getCustomerTestLineName() {
        return customerTestLineName;
    }

    /**
     * customerTestLineName VARCHAR(500)<br>
     * 设置 
     */
    public void setCustomerTestLineName(String customerTestLineName) {
        this.customerTestLineName = customerTestLineName == null ? null : customerTestLineName.trim();
    }

    /**
     * testStandard VARCHAR(512)<br>
     * 获得 
     */
    public String getTestStandard() {
        return testStandard;
    }

    /**
     * testStandard VARCHAR(512)<br>
     * 设置 
     */
    public void setTestStandard(String testStandard) {
        this.testStandard = testStandard == null ? null : testStandard.trim();
    }

    /**
     * citationSectionName VARCHAR(512)<br>
     * 获得 
     */
    public String getCitationSectionName() {
        return citationSectionName;
    }

    /**
     * citationSectionName VARCHAR(512)<br>
     * 设置 
     */
    public void setCitationSectionName(String citationSectionName) {
        this.citationSectionName = citationSectionName == null ? null : citationSectionName.trim();
    }

    /**
     * artifactVersionId INTEGER(10) 默认值[0]<br>
     * 获得 0：tb_trims_testline_baseinfo.TestLineVersionId
1：tb_trims_pp_baseinfo.PpVersionId
     */
    public Integer getArtifactVersionId() {
        return artifactVersionId;
    }

    /**
     * artifactVersionId INTEGER(10) 默认值[0]<br>
     * 设置 0：tb_trims_testline_baseinfo.TestLineVersionId
1：tb_trims_pp_baseinfo.PpVersionId
     */
    public void setArtifactVersionId(Integer artifactVersionId) {
        this.artifactVersionId = artifactVersionId;
    }

    /**
     * testStandardCn VARCHAR(512)<br>
     * 获得 
     */
    public String getTestStandardCn() {
        return testStandardCn;
    }

    /**
     * testStandardCn VARCHAR(512)<br>
     * 设置 
     */
    public void setTestStandardCn(String testStandardCn) {
        this.testStandardCn = testStandardCn == null ? null : testStandardCn.trim();
    }

    /**
     * citationSectionNameCn VARCHAR(512)<br>
     * 获得 
     */
    public String getCitationSectionNameCn() {
        return citationSectionNameCn;
    }

    /**
     * citationSectionNameCn VARCHAR(512)<br>
     * 设置 
     */
    public void setCitationSectionNameCn(String citationSectionNameCn) {
        this.citationSectionNameCn = citationSectionNameCn == null ? null : citationSectionNameCn.trim();
    }

    /**
     * remark VARCHAR(4000)<br>
     * 获得 
     */
    public String getRemark() {
        return remark;
    }

    /**
     * remark VARCHAR(4000)<br>
     * 设置 
     */
    public void setRemark(String remark) {
        this.remark = remark == null ? null : remark.trim();
    }

    /**
     * citationBaseId BIGINT(19)<br>
     * 获得 
     */
    public Long getCitationBaseId() {
        return citationBaseId;
    }

    /**
     * citationBaseId BIGINT(19)<br>
     * 设置 
     */
    public void setCitationBaseId(Long citationBaseId) {
        this.citationBaseId = citationBaseId;
    }

    /**
     * citationId INTEGER(10)<br>
     * 获得 
     */
    public Integer getCitationId() {
        return citationId;
    }

    /**
     * citationId INTEGER(10)<br>
     * 设置 
     */
    public void setCitationId(Integer citationId) {
        this.citationId = citationId;
    }

    /**
     * citationVersionId INTEGER(10)<br>
     * 获得 
     */
    public Integer getCitationVersionId() {
        return citationVersionId;
    }

    /**
     * citationVersionId INTEGER(10)<br>
     * 设置 
     */
    public void setCitationVersionId(Integer citationVersionId) {
        this.citationVersionId = citationVersionId;
    }

    /**
     * conditionStatus INTEGER(10)<br>
     * 获得 
     */
    public Integer getConditionStatus() {
        return conditionStatus;
    }

    /**
     * conditionStatus INTEGER(10)<br>
     * 设置 
     */
    public void setConditionStatus(Integer conditionStatus) {
        this.conditionStatus = conditionStatus;
    }

    /**
     * labSectionBaseId BIGINT(19)<br>
     * 获得 
     */
    public Long getLabSectionBaseId() {
        return labSectionBaseId;
    }

    /**
     * labSectionBaseId BIGINT(19)<br>
     * 设置 
     */
    public void setLabSectionBaseId(Long labSectionBaseId) {
        this.labSectionBaseId = labSectionBaseId;
    }

    /**
     * labsectionbase INTEGER(10)<br>
     * 获得 
     */
    public Integer getLabsectionbase() {
        return labsectionbase;
    }

    /**
     * labsectionbase INTEGER(10)<br>
     * 设置 
     */
    public void setLabsectionbase(Integer labsectionbase) {
        this.labsectionbase = labsectionbase;
    }

    /**
     * labTeamCode VARCHAR(100)<br>
     * 获得 LabTeamCode，CS 提供。length follow CS
     */
    public String getLabTeamCode() {
        return labTeamCode;
    }

    /**
     * labTeamCode VARCHAR(100)<br>
     * 设置 LabTeamCode，CS 提供。length follow CS
     */
    public void setLabTeamCode(String labTeamCode) {
        this.labTeamCode = labTeamCode == null ? null : labTeamCode.trim();
    }

    /**
     * labSectionName VARCHAR(512) 默认值[] 必填<br>
     * 获得 
     */
    public String getLabSectionName() {
        return labSectionName;
    }

    /**
     * labSectionName VARCHAR(512) 默认值[] 必填<br>
     * 设置 
     */
    public void setLabSectionName(String labSectionName) {
        this.labSectionName = labSectionName == null ? null : labSectionName.trim();
    }

    /**
     * subContractLabCode VARCHAR(30)<br>
     * 获得 
     */
    public String getSubContractLabCode() {
        return subContractLabCode;
    }

    /**
     * subContractLabCode VARCHAR(30)<br>
     * 设置 
     */
    public void setSubContractLabCode(String subContractLabCode) {
        this.subContractLabCode = subContractLabCode == null ? null : subContractLabCode.trim();
    }

    /**
     * ppTlRelBaseId BIGINT(19) 默认值[0]<br>
     * 获得 
     */
    public Long getPpTlRelBaseId() {
        return ppTlRelBaseId;
    }

    /**
     * ppTlRelBaseId BIGINT(19) 默认值[0]<br>
     * 设置 
     */
    public void setPpTlRelBaseId(Long ppTlRelBaseId) {
        this.ppTlRelBaseId = ppTlRelBaseId;
    }

    /**
     * jobId VARCHAR(36)<br>
     * 获得 
     */
    public String getJobId() {
        return jobId;
    }

    /**
     * jobId VARCHAR(36)<br>
     * 设置 
     */
    public void setJobId(String jobId) {
        this.jobId = jobId == null ? null : jobId.trim();
    }

    /**
     * jobNo VARCHAR(50)<br>
     * 获得 
     */
    public String getJobNo() {
        return jobNo;
    }

    /**
     * jobNo VARCHAR(50)<br>
     * 设置 
     */
    public void setJobNo(String jobNo) {
        this.jobNo = jobNo == null ? null : jobNo.trim();
    }

    /**
     * subContractID VARCHAR(36)<br>
     * 获得 
     */
    public String getSubContractID() {
        return subContractID;
    }

    /**
     * subContractID VARCHAR(36)<br>
     * 设置 
     */
    public void setSubContractID(String subContractID) {
        this.subContractID = subContractID == null ? null : subContractID.trim();
    }

    /**
     * subContractNo VARCHAR(50)<br>
     * 获得 
     */
    public String getSubContractNo() {
        return subContractNo;
    }

    /**
     * subContractNo VARCHAR(50)<br>
     * 设置 
     */
    public void setSubContractNo(String subContractNo) {
        this.subContractNo = subContractNo == null ? null : subContractNo.trim();
    }

    /**
     * sampleSegegrationWIID BIGINT(19)<br>
     * 获得 
     */
    public Long getSampleSegegrationWIID() {
        return sampleSegegrationWIID;
    }

    /**
     * sampleSegegrationWIID BIGINT(19)<br>
     * 设置 
     */
    public void setSampleSegegrationWIID(Long sampleSegegrationWIID) {
        this.sampleSegegrationWIID = sampleSegegrationWIID;
    }

    /**
     * sampleSegegrationWIText VARCHAR(4096)<br>
     * 获得 
     */
    public String getSampleSegegrationWIText() {
        return sampleSegegrationWIText;
    }

    /**
     * sampleSegegrationWIText VARCHAR(4096)<br>
     * 设置 
     */
    public void setSampleSegegrationWIText(String sampleSegegrationWIText) {
        this.sampleSegegrationWIText = sampleSegegrationWIText == null ? null : sampleSegegrationWIText.trim();
    }

    /**
     * reportSeq INTEGER(10)<br>
     * 获得 
     */
    public Integer getReportSeq() {
        return reportSeq;
    }

    /**
     * reportSeq INTEGER(10)<br>
     * 设置 
     */
    public void setReportSeq(Integer reportSeq) {
        this.reportSeq = reportSeq;
    }

    /**
     * orderSeq INTEGER(10)<br>
     * 获得 
     */
    public Integer getOrderSeq() {
        return orderSeq;
    }

    /**
     * orderSeq INTEGER(10)<br>
     * 设置 
     */
    public void setOrderSeq(Integer orderSeq) {
        this.orderSeq = orderSeq;
    }

    /**
     * sectionLevel INTEGER(10) 默认值[0]<br>
     * 获得 
     */
    public Integer getSectionLevel() {
        return sectionLevel;
    }

    /**
     * sectionLevel INTEGER(10) 默认值[0]<br>
     * 设置 
     */
    public void setSectionLevel(Integer sectionLevel) {
        this.sectionLevel = sectionLevel;
    }

    /**
     * testLineSeq INTEGER(10) 默认值[0]<br>
     * 获得 
     */
    public Integer getTestLineSeq() {
        return testLineSeq;
    }

    /**
     * testLineSeq INTEGER(10) 默认值[0]<br>
     * 设置 
     */
    public void setTestLineSeq(Integer testLineSeq) {
        this.testLineSeq = testLineSeq;
    }

    /**
     * pendingFlag BIT(1) 默认值[b'0']<br>
     * 获得 0 unpending,1pending
     */
    public Boolean getPendingFlag() {
        return pendingFlag;
    }

    /**
     * pendingFlag BIT(1) 默认值[b'0']<br>
     * 设置 0 unpending,1pending
     */
    public void setPendingFlag(Boolean pendingFlag) {
        this.pendingFlag = pendingFlag;
    }

    /**
     * testLineStatus INTEGER(10)<br>
     * 获得 
     */
    public Integer getTestLineStatus() {
        return testLineStatus;
    }

    /**
     * testLineStatus INTEGER(10)<br>
     * 设置 
     */
    public void setTestLineStatus(Integer testLineStatus) {
        this.testLineStatus = testLineStatus;
    }

    /**
     * createdDate TIMESTAMP(19)<br>
     * 获得 CreatedDate
     */
    public Date getCreatedDate() {
        return createdDate;
    }

    /**
     * createdDate TIMESTAMP(19)<br>
     * 设置 CreatedDate
     */
    public void setCreatedDate(Date createdDate) {
        this.createdDate = createdDate;
    }

    /**
     * testStartDate TIMESTAMP(19)<br>
     * 获得 
     */
    public Date getTestStartDate() {
        return testStartDate;
    }

    /**
     * testStartDate TIMESTAMP(19)<br>
     * 设置 
     */
    public void setTestStartDate(Date testStartDate) {
        this.testStartDate = testStartDate;
    }

    /**
     * testEndDate TIMESTAMP(19)<br>
     * 获得 
     */
    public Date getTestEndDate() {
        return testEndDate;
    }

    /**
     * testEndDate TIMESTAMP(19)<br>
     * 设置 
     */
    public void setTestEndDate(Date testEndDate) {
        this.testEndDate = testEndDate;
    }

    /**
     * testDueDate TIMESTAMP(19)<br>
     * 获得 
     */
    public Date getTestDueDate() {
        return testDueDate;
    }

    /**
     * testDueDate TIMESTAMP(19)<br>
     * 设置 
     */
    public void setTestDueDate(Date testDueDate) {
        this.testDueDate = testDueDate;
    }

    /**
     * engineer VARCHAR(50)<br>
     * 获得 
     */
    public String getEngineer() {
        return engineer;
    }

    /**
     * engineer VARCHAR(50)<br>
     * 设置 
     */
    public void setEngineer(String engineer) {
        this.engineer = engineer == null ? null : engineer.trim();
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", orderId=").append(orderId);
        sb.append(", orderNo=").append(orderNo);
        sb.append(", labCode=").append(labCode);
        sb.append(", ppVersionId=").append(ppVersionId);
        sb.append(", ppBaseId=").append(ppBaseId);
        sb.append(", ppNo=").append(ppNo);
        sb.append(", ppName=").append(ppName);
        sb.append(", ppTlRelId=").append(ppTlRelId);
        sb.append(", sectionName=").append(sectionName);
        sb.append(", testLineType=").append(testLineType);
        sb.append(", testLineInstanceId=").append(testLineInstanceId);
        sb.append(", testItemNo=").append(testItemNo);
        sb.append(", productLineCode=").append(productLineCode);
        sb.append(", testLineBaseId=").append(testLineBaseId);
        sb.append(", testLineVersionId=").append(testLineVersionId);
        sb.append(", testLineId=").append(testLineId);
        sb.append(", testItemCn=").append(testItemCn);
        sb.append(", customerTestLineName=").append(customerTestLineName);
        sb.append(", testStandard=").append(testStandard);
        sb.append(", citationSectionName=").append(citationSectionName);
        sb.append(", artifactVersionId=").append(artifactVersionId);
        sb.append(", testStandardCn=").append(testStandardCn);
        sb.append(", citationSectionNameCn=").append(citationSectionNameCn);
        sb.append(", remark=").append(remark);
        sb.append(", citationBaseId=").append(citationBaseId);
        sb.append(", citationId=").append(citationId);
        sb.append(", citationVersionId=").append(citationVersionId);
        sb.append(", conditionStatus=").append(conditionStatus);
        sb.append(", labSectionBaseId=").append(labSectionBaseId);
        sb.append(", labsectionbase=").append(labsectionbase);
        sb.append(", labTeamCode=").append(labTeamCode);
        sb.append(", labSectionName=").append(labSectionName);
        sb.append(", subContractLabCode=").append(subContractLabCode);
        sb.append(", ppTlRelBaseId=").append(ppTlRelBaseId);
        sb.append(", jobId=").append(jobId);
        sb.append(", jobNo=").append(jobNo);
        sb.append(", subContractID=").append(subContractID);
        sb.append(", subContractNo=").append(subContractNo);
        sb.append(", sampleSegegrationWIID=").append(sampleSegegrationWIID);
        sb.append(", sampleSegegrationWIText=").append(sampleSegegrationWIText);
        sb.append(", reportSeq=").append(reportSeq);
        sb.append(", orderSeq=").append(orderSeq);
        sb.append(", sectionLevel=").append(sectionLevel);
        sb.append(", testLineSeq=").append(testLineSeq);
        sb.append(", pendingFlag=").append(pendingFlag);
        sb.append(", testLineStatus=").append(testLineStatus);
        sb.append(", createdDate=").append(createdDate);
        sb.append(", testStartDate=").append(testStartDate);
        sb.append(", testEndDate=").append(testEndDate);
        sb.append(", testDueDate=").append(testDueDate);
        sb.append(", engineer=").append(engineer);
        sb.append("]");
        return sb.toString();
    }
}