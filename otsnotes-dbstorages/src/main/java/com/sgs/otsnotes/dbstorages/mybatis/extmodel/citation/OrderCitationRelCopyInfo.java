package com.sgs.otsnotes.dbstorages.mybatis.extmodel.citation;

import com.sgs.otsnotes.dbstorages.mybatis.model.OrderCitationRelInfoPO;
import com.sgs.otsnotes.dbstorages.mybatis.model.OrderLanguageRelInfoPO;

import java.util.List;
import java.util.Set;

public class OrderCitationRelCopyInfo {
    /**
     *
     */
    private String orderId;
    /**
     *
     */
    private List<OrderCitationRelInfoPO> citationRels;
    /**
     *
     */
    private List<OrderLanguageRelInfoPO> langRels;

    private Set<Long> needDelOrderCitationIds;

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public List<OrderCitationRelInfoPO> getCitationRels() {
        return citationRels;
    }

    public void setCitationRels(List<OrderCitationRelInfoPO> citationRels) {
        this.citationRels = citationRels;
    }

    public List<OrderLanguageRelInfoPO> getLangRels() {
        return langRels;
    }

    public void setLangRels(List<OrderLanguageRelInfoPO> langRels) {
        this.langRels = langRels;
    }

    public Set<Long> getNeedDelOrderCitationIds() {
        return needDelOrderCitationIds;
    }

    public void setNeedDelOrderCitationIds(Set<Long> needDelOrderCitationIds) {
        this.needDelOrderCitationIds = needDelOrderCitationIds;
    }
}
