package com.sgs.otsnotes.dbstorages.mybatis.model;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class ArtifactCitationRelInfoExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public ArtifactCitationRelInfoExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("Id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("Id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("Id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("Id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("Id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("Id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("Id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("Id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("Id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("Id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("Id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("Id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andArtifactTypeIsNull() {
            addCriterion("ArtifactType is null");
            return (Criteria) this;
        }

        public Criteria andArtifactTypeIsNotNull() {
            addCriterion("ArtifactType is not null");
            return (Criteria) this;
        }

        public Criteria andArtifactTypeEqualTo(Integer value) {
            addCriterion("ArtifactType =", value, "artifactType");
            return (Criteria) this;
        }

        public Criteria andArtifactTypeNotEqualTo(Integer value) {
            addCriterion("ArtifactType <>", value, "artifactType");
            return (Criteria) this;
        }

        public Criteria andArtifactTypeGreaterThan(Integer value) {
            addCriterion("ArtifactType >", value, "artifactType");
            return (Criteria) this;
        }

        public Criteria andArtifactTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("ArtifactType >=", value, "artifactType");
            return (Criteria) this;
        }

        public Criteria andArtifactTypeLessThan(Integer value) {
            addCriterion("ArtifactType <", value, "artifactType");
            return (Criteria) this;
        }

        public Criteria andArtifactTypeLessThanOrEqualTo(Integer value) {
            addCriterion("ArtifactType <=", value, "artifactType");
            return (Criteria) this;
        }

        public Criteria andArtifactTypeIn(List<Integer> values) {
            addCriterion("ArtifactType in", values, "artifactType");
            return (Criteria) this;
        }

        public Criteria andArtifactTypeNotIn(List<Integer> values) {
            addCriterion("ArtifactType not in", values, "artifactType");
            return (Criteria) this;
        }

        public Criteria andArtifactTypeBetween(Integer value1, Integer value2) {
            addCriterion("ArtifactType between", value1, value2, "artifactType");
            return (Criteria) this;
        }

        public Criteria andArtifactTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("ArtifactType not between", value1, value2, "artifactType");
            return (Criteria) this;
        }

        public Criteria andArtifactVersionIdIsNull() {
            addCriterion("ArtifactVersionId is null");
            return (Criteria) this;
        }

        public Criteria andArtifactVersionIdIsNotNull() {
            addCriterion("ArtifactVersionId is not null");
            return (Criteria) this;
        }

        public Criteria andArtifactVersionIdEqualTo(Integer value) {
            addCriterion("ArtifactVersionId =", value, "artifactVersionId");
            return (Criteria) this;
        }

        public Criteria andArtifactVersionIdNotEqualTo(Integer value) {
            addCriterion("ArtifactVersionId <>", value, "artifactVersionId");
            return (Criteria) this;
        }

        public Criteria andArtifactVersionIdGreaterThan(Integer value) {
            addCriterion("ArtifactVersionId >", value, "artifactVersionId");
            return (Criteria) this;
        }

        public Criteria andArtifactVersionIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("ArtifactVersionId >=", value, "artifactVersionId");
            return (Criteria) this;
        }

        public Criteria andArtifactVersionIdLessThan(Integer value) {
            addCriterion("ArtifactVersionId <", value, "artifactVersionId");
            return (Criteria) this;
        }

        public Criteria andArtifactVersionIdLessThanOrEqualTo(Integer value) {
            addCriterion("ArtifactVersionId <=", value, "artifactVersionId");
            return (Criteria) this;
        }

        public Criteria andArtifactVersionIdIn(List<Integer> values) {
            addCriterion("ArtifactVersionId in", values, "artifactVersionId");
            return (Criteria) this;
        }

        public Criteria andArtifactVersionIdNotIn(List<Integer> values) {
            addCriterion("ArtifactVersionId not in", values, "artifactVersionId");
            return (Criteria) this;
        }

        public Criteria andArtifactVersionIdBetween(Integer value1, Integer value2) {
            addCriterion("ArtifactVersionId between", value1, value2, "artifactVersionId");
            return (Criteria) this;
        }

        public Criteria andArtifactVersionIdNotBetween(Integer value1, Integer value2) {
            addCriterion("ArtifactVersionId not between", value1, value2, "artifactVersionId");
            return (Criteria) this;
        }

        public Criteria andCitationIdIsNull() {
            addCriterion("CitationId is null");
            return (Criteria) this;
        }

        public Criteria andCitationIdIsNotNull() {
            addCriterion("CitationId is not null");
            return (Criteria) this;
        }

        public Criteria andCitationIdEqualTo(Integer value) {
            addCriterion("CitationId =", value, "citationId");
            return (Criteria) this;
        }

        public Criteria andCitationIdNotEqualTo(Integer value) {
            addCriterion("CitationId <>", value, "citationId");
            return (Criteria) this;
        }

        public Criteria andCitationIdGreaterThan(Integer value) {
            addCriterion("CitationId >", value, "citationId");
            return (Criteria) this;
        }

        public Criteria andCitationIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("CitationId >=", value, "citationId");
            return (Criteria) this;
        }

        public Criteria andCitationIdLessThan(Integer value) {
            addCriterion("CitationId <", value, "citationId");
            return (Criteria) this;
        }

        public Criteria andCitationIdLessThanOrEqualTo(Integer value) {
            addCriterion("CitationId <=", value, "citationId");
            return (Criteria) this;
        }

        public Criteria andCitationIdIn(List<Integer> values) {
            addCriterion("CitationId in", values, "citationId");
            return (Criteria) this;
        }

        public Criteria andCitationIdNotIn(List<Integer> values) {
            addCriterion("CitationId not in", values, "citationId");
            return (Criteria) this;
        }

        public Criteria andCitationIdBetween(Integer value1, Integer value2) {
            addCriterion("CitationId between", value1, value2, "citationId");
            return (Criteria) this;
        }

        public Criteria andCitationIdNotBetween(Integer value1, Integer value2) {
            addCriterion("CitationId not between", value1, value2, "citationId");
            return (Criteria) this;
        }

        public Criteria andCitationVersionIdIsNull() {
            addCriterion("CitationVersionId is null");
            return (Criteria) this;
        }

        public Criteria andCitationVersionIdIsNotNull() {
            addCriterion("CitationVersionId is not null");
            return (Criteria) this;
        }

        public Criteria andCitationVersionIdEqualTo(Integer value) {
            addCriterion("CitationVersionId =", value, "citationVersionId");
            return (Criteria) this;
        }

        public Criteria andCitationVersionIdNotEqualTo(Integer value) {
            addCriterion("CitationVersionId <>", value, "citationVersionId");
            return (Criteria) this;
        }

        public Criteria andCitationVersionIdGreaterThan(Integer value) {
            addCriterion("CitationVersionId >", value, "citationVersionId");
            return (Criteria) this;
        }

        public Criteria andCitationVersionIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("CitationVersionId >=", value, "citationVersionId");
            return (Criteria) this;
        }

        public Criteria andCitationVersionIdLessThan(Integer value) {
            addCriterion("CitationVersionId <", value, "citationVersionId");
            return (Criteria) this;
        }

        public Criteria andCitationVersionIdLessThanOrEqualTo(Integer value) {
            addCriterion("CitationVersionId <=", value, "citationVersionId");
            return (Criteria) this;
        }

        public Criteria andCitationVersionIdIn(List<Integer> values) {
            addCriterion("CitationVersionId in", values, "citationVersionId");
            return (Criteria) this;
        }

        public Criteria andCitationVersionIdNotIn(List<Integer> values) {
            addCriterion("CitationVersionId not in", values, "citationVersionId");
            return (Criteria) this;
        }

        public Criteria andCitationVersionIdBetween(Integer value1, Integer value2) {
            addCriterion("CitationVersionId between", value1, value2, "citationVersionId");
            return (Criteria) this;
        }

        public Criteria andCitationVersionIdNotBetween(Integer value1, Integer value2) {
            addCriterion("CitationVersionId not between", value1, value2, "citationVersionId");
            return (Criteria) this;
        }

        public Criteria andCitationTypeIsNull() {
            addCriterion("CitationType is null");
            return (Criteria) this;
        }

        public Criteria andCitationTypeIsNotNull() {
            addCriterion("CitationType is not null");
            return (Criteria) this;
        }

        public Criteria andCitationTypeEqualTo(Integer value) {
            addCriterion("CitationType =", value, "citationType");
            return (Criteria) this;
        }

        public Criteria andCitationTypeNotEqualTo(Integer value) {
            addCriterion("CitationType <>", value, "citationType");
            return (Criteria) this;
        }

        public Criteria andCitationTypeGreaterThan(Integer value) {
            addCriterion("CitationType >", value, "citationType");
            return (Criteria) this;
        }

        public Criteria andCitationTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("CitationType >=", value, "citationType");
            return (Criteria) this;
        }

        public Criteria andCitationTypeLessThan(Integer value) {
            addCriterion("CitationType <", value, "citationType");
            return (Criteria) this;
        }

        public Criteria andCitationTypeLessThanOrEqualTo(Integer value) {
            addCriterion("CitationType <=", value, "citationType");
            return (Criteria) this;
        }

        public Criteria andCitationTypeIn(List<Integer> values) {
            addCriterion("CitationType in", values, "citationType");
            return (Criteria) this;
        }

        public Criteria andCitationTypeNotIn(List<Integer> values) {
            addCriterion("CitationType not in", values, "citationType");
            return (Criteria) this;
        }

        public Criteria andCitationTypeBetween(Integer value1, Integer value2) {
            addCriterion("CitationType between", value1, value2, "citationType");
            return (Criteria) this;
        }

        public Criteria andCitationTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("CitationType not between", value1, value2, "citationType");
            return (Criteria) this;
        }

        public Criteria andCitationNameIsNull() {
            addCriterion("CitationName is null");
            return (Criteria) this;
        }

        public Criteria andCitationNameIsNotNull() {
            addCriterion("CitationName is not null");
            return (Criteria) this;
        }

        public Criteria andCitationNameEqualTo(String value) {
            addCriterion("CitationName =", value, "citationName");
            return (Criteria) this;
        }

        public Criteria andCitationNameNotEqualTo(String value) {
            addCriterion("CitationName <>", value, "citationName");
            return (Criteria) this;
        }

        public Criteria andCitationNameGreaterThan(String value) {
            addCriterion("CitationName >", value, "citationName");
            return (Criteria) this;
        }

        public Criteria andCitationNameGreaterThanOrEqualTo(String value) {
            addCriterion("CitationName >=", value, "citationName");
            return (Criteria) this;
        }

        public Criteria andCitationNameLessThan(String value) {
            addCriterion("CitationName <", value, "citationName");
            return (Criteria) this;
        }

        public Criteria andCitationNameLessThanOrEqualTo(String value) {
            addCriterion("CitationName <=", value, "citationName");
            return (Criteria) this;
        }

        public Criteria andCitationNameLike(String value) {
            addCriterion("CitationName like", value, "citationName");
            return (Criteria) this;
        }

        public Criteria andCitationNameNotLike(String value) {
            addCriterion("CitationName not like", value, "citationName");
            return (Criteria) this;
        }

        public Criteria andCitationNameIn(List<String> values) {
            addCriterion("CitationName in", values, "citationName");
            return (Criteria) this;
        }

        public Criteria andCitationNameNotIn(List<String> values) {
            addCriterion("CitationName not in", values, "citationName");
            return (Criteria) this;
        }

        public Criteria andCitationNameBetween(String value1, String value2) {
            addCriterion("CitationName between", value1, value2, "citationName");
            return (Criteria) this;
        }

        public Criteria andCitationNameNotBetween(String value1, String value2) {
            addCriterion("CitationName not between", value1, value2, "citationName");
            return (Criteria) this;
        }

        public Criteria andCitationSectionIdIsNull() {
            addCriterion("CitationSectionId is null");
            return (Criteria) this;
        }

        public Criteria andCitationSectionIdIsNotNull() {
            addCriterion("CitationSectionId is not null");
            return (Criteria) this;
        }

        public Criteria andCitationSectionIdEqualTo(Integer value) {
            addCriterion("CitationSectionId =", value, "citationSectionId");
            return (Criteria) this;
        }

        public Criteria andCitationSectionIdNotEqualTo(Integer value) {
            addCriterion("CitationSectionId <>", value, "citationSectionId");
            return (Criteria) this;
        }

        public Criteria andCitationSectionIdGreaterThan(Integer value) {
            addCriterion("CitationSectionId >", value, "citationSectionId");
            return (Criteria) this;
        }

        public Criteria andCitationSectionIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("CitationSectionId >=", value, "citationSectionId");
            return (Criteria) this;
        }

        public Criteria andCitationSectionIdLessThan(Integer value) {
            addCriterion("CitationSectionId <", value, "citationSectionId");
            return (Criteria) this;
        }

        public Criteria andCitationSectionIdLessThanOrEqualTo(Integer value) {
            addCriterion("CitationSectionId <=", value, "citationSectionId");
            return (Criteria) this;
        }

        public Criteria andCitationSectionIdIn(List<Integer> values) {
            addCriterion("CitationSectionId in", values, "citationSectionId");
            return (Criteria) this;
        }

        public Criteria andCitationSectionIdNotIn(List<Integer> values) {
            addCriterion("CitationSectionId not in", values, "citationSectionId");
            return (Criteria) this;
        }

        public Criteria andCitationSectionIdBetween(Integer value1, Integer value2) {
            addCriterion("CitationSectionId between", value1, value2, "citationSectionId");
            return (Criteria) this;
        }

        public Criteria andCitationSectionIdNotBetween(Integer value1, Integer value2) {
            addCriterion("CitationSectionId not between", value1, value2, "citationSectionId");
            return (Criteria) this;
        }

        public Criteria andCitationSectionNameIsNull() {
            addCriterion("CitationSectionName is null");
            return (Criteria) this;
        }

        public Criteria andCitationSectionNameIsNotNull() {
            addCriterion("CitationSectionName is not null");
            return (Criteria) this;
        }

        public Criteria andCitationSectionNameEqualTo(String value) {
            addCriterion("CitationSectionName =", value, "citationSectionName");
            return (Criteria) this;
        }

        public Criteria andCitationSectionNameNotEqualTo(String value) {
            addCriterion("CitationSectionName <>", value, "citationSectionName");
            return (Criteria) this;
        }

        public Criteria andCitationSectionNameGreaterThan(String value) {
            addCriterion("CitationSectionName >", value, "citationSectionName");
            return (Criteria) this;
        }

        public Criteria andCitationSectionNameGreaterThanOrEqualTo(String value) {
            addCriterion("CitationSectionName >=", value, "citationSectionName");
            return (Criteria) this;
        }

        public Criteria andCitationSectionNameLessThan(String value) {
            addCriterion("CitationSectionName <", value, "citationSectionName");
            return (Criteria) this;
        }

        public Criteria andCitationSectionNameLessThanOrEqualTo(String value) {
            addCriterion("CitationSectionName <=", value, "citationSectionName");
            return (Criteria) this;
        }

        public Criteria andCitationSectionNameLike(String value) {
            addCriterion("CitationSectionName like", value, "citationSectionName");
            return (Criteria) this;
        }

        public Criteria andCitationSectionNameNotLike(String value) {
            addCriterion("CitationSectionName not like", value, "citationSectionName");
            return (Criteria) this;
        }

        public Criteria andCitationSectionNameIn(List<String> values) {
            addCriterion("CitationSectionName in", values, "citationSectionName");
            return (Criteria) this;
        }

        public Criteria andCitationSectionNameNotIn(List<String> values) {
            addCriterion("CitationSectionName not in", values, "citationSectionName");
            return (Criteria) this;
        }

        public Criteria andCitationSectionNameBetween(String value1, String value2) {
            addCriterion("CitationSectionName between", value1, value2, "citationSectionName");
            return (Criteria) this;
        }

        public Criteria andCitationSectionNameNotBetween(String value1, String value2) {
            addCriterion("CitationSectionName not between", value1, value2, "citationSectionName");
            return (Criteria) this;
        }

        public Criteria andEvaluationAliasIsNull() {
            addCriterion("EvaluationAlias is null");
            return (Criteria) this;
        }

        public Criteria andEvaluationAliasIsNotNull() {
            addCriterion("EvaluationAlias is not null");
            return (Criteria) this;
        }

        public Criteria andEvaluationAliasEqualTo(String value) {
            addCriterion("EvaluationAlias =", value, "evaluationAlias");
            return (Criteria) this;
        }

        public Criteria andEvaluationAliasNotEqualTo(String value) {
            addCriterion("EvaluationAlias <>", value, "evaluationAlias");
            return (Criteria) this;
        }

        public Criteria andEvaluationAliasGreaterThan(String value) {
            addCriterion("EvaluationAlias >", value, "evaluationAlias");
            return (Criteria) this;
        }

        public Criteria andEvaluationAliasGreaterThanOrEqualTo(String value) {
            addCriterion("EvaluationAlias >=", value, "evaluationAlias");
            return (Criteria) this;
        }

        public Criteria andEvaluationAliasLessThan(String value) {
            addCriterion("EvaluationAlias <", value, "evaluationAlias");
            return (Criteria) this;
        }

        public Criteria andEvaluationAliasLessThanOrEqualTo(String value) {
            addCriterion("EvaluationAlias <=", value, "evaluationAlias");
            return (Criteria) this;
        }

        public Criteria andEvaluationAliasLike(String value) {
            addCriterion("EvaluationAlias like", value, "evaluationAlias");
            return (Criteria) this;
        }

        public Criteria andEvaluationAliasNotLike(String value) {
            addCriterion("EvaluationAlias not like", value, "evaluationAlias");
            return (Criteria) this;
        }

        public Criteria andEvaluationAliasIn(List<String> values) {
            addCriterion("EvaluationAlias in", values, "evaluationAlias");
            return (Criteria) this;
        }

        public Criteria andEvaluationAliasNotIn(List<String> values) {
            addCriterion("EvaluationAlias not in", values, "evaluationAlias");
            return (Criteria) this;
        }

        public Criteria andEvaluationAliasBetween(String value1, String value2) {
            addCriterion("EvaluationAlias between", value1, value2, "evaluationAlias");
            return (Criteria) this;
        }

        public Criteria andEvaluationAliasNotBetween(String value1, String value2) {
            addCriterion("EvaluationAlias not between", value1, value2, "evaluationAlias");
            return (Criteria) this;
        }

        public Criteria andMethodDescIsNull() {
            addCriterion("MethodDesc is null");
            return (Criteria) this;
        }

        public Criteria andMethodDescIsNotNull() {
            addCriterion("MethodDesc is not null");
            return (Criteria) this;
        }

        public Criteria andMethodDescEqualTo(String value) {
            addCriterion("MethodDesc =", value, "methodDesc");
            return (Criteria) this;
        }

        public Criteria andMethodDescNotEqualTo(String value) {
            addCriterion("MethodDesc <>", value, "methodDesc");
            return (Criteria) this;
        }

        public Criteria andMethodDescGreaterThan(String value) {
            addCriterion("MethodDesc >", value, "methodDesc");
            return (Criteria) this;
        }

        public Criteria andMethodDescGreaterThanOrEqualTo(String value) {
            addCriterion("MethodDesc >=", value, "methodDesc");
            return (Criteria) this;
        }

        public Criteria andMethodDescLessThan(String value) {
            addCriterion("MethodDesc <", value, "methodDesc");
            return (Criteria) this;
        }

        public Criteria andMethodDescLessThanOrEqualTo(String value) {
            addCriterion("MethodDesc <=", value, "methodDesc");
            return (Criteria) this;
        }

        public Criteria andMethodDescLike(String value) {
            addCriterion("MethodDesc like", value, "methodDesc");
            return (Criteria) this;
        }

        public Criteria andMethodDescNotLike(String value) {
            addCriterion("MethodDesc not like", value, "methodDesc");
            return (Criteria) this;
        }

        public Criteria andMethodDescIn(List<String> values) {
            addCriterion("MethodDesc in", values, "methodDesc");
            return (Criteria) this;
        }

        public Criteria andMethodDescNotIn(List<String> values) {
            addCriterion("MethodDesc not in", values, "methodDesc");
            return (Criteria) this;
        }

        public Criteria andMethodDescBetween(String value1, String value2) {
            addCriterion("MethodDesc between", value1, value2, "methodDesc");
            return (Criteria) this;
        }

        public Criteria andMethodDescNotBetween(String value1, String value2) {
            addCriterion("MethodDesc not between", value1, value2, "methodDesc");
            return (Criteria) this;
        }

        public Criteria andBizVersionIdIsNull() {
            addCriterion("BizVersionId is null");
            return (Criteria) this;
        }

        public Criteria andBizVersionIdIsNotNull() {
            addCriterion("BizVersionId is not null");
            return (Criteria) this;
        }

        public Criteria andBizVersionIdEqualTo(String value) {
            addCriterion("BizVersionId =", value, "bizVersionId");
            return (Criteria) this;
        }

        public Criteria andBizVersionIdNotEqualTo(String value) {
            addCriterion("BizVersionId <>", value, "bizVersionId");
            return (Criteria) this;
        }

        public Criteria andBizVersionIdGreaterThan(String value) {
            addCriterion("BizVersionId >", value, "bizVersionId");
            return (Criteria) this;
        }

        public Criteria andBizVersionIdGreaterThanOrEqualTo(String value) {
            addCriterion("BizVersionId >=", value, "bizVersionId");
            return (Criteria) this;
        }

        public Criteria andBizVersionIdLessThan(String value) {
            addCriterion("BizVersionId <", value, "bizVersionId");
            return (Criteria) this;
        }

        public Criteria andBizVersionIdLessThanOrEqualTo(String value) {
            addCriterion("BizVersionId <=", value, "bizVersionId");
            return (Criteria) this;
        }

        public Criteria andBizVersionIdLike(String value) {
            addCriterion("BizVersionId like", value, "bizVersionId");
            return (Criteria) this;
        }

        public Criteria andBizVersionIdNotLike(String value) {
            addCriterion("BizVersionId not like", value, "bizVersionId");
            return (Criteria) this;
        }

        public Criteria andBizVersionIdIn(List<String> values) {
            addCriterion("BizVersionId in", values, "bizVersionId");
            return (Criteria) this;
        }

        public Criteria andBizVersionIdNotIn(List<String> values) {
            addCriterion("BizVersionId not in", values, "bizVersionId");
            return (Criteria) this;
        }

        public Criteria andBizVersionIdBetween(String value1, String value2) {
            addCriterion("BizVersionId between", value1, value2, "bizVersionId");
            return (Criteria) this;
        }

        public Criteria andBizVersionIdNotBetween(String value1, String value2) {
            addCriterion("BizVersionId not between", value1, value2, "bizVersionId");
            return (Criteria) this;
        }

        public Criteria andCitationStatusIsNull() {
            addCriterion("CitationStatus is null");
            return (Criteria) this;
        }

        public Criteria andCitationStatusIsNotNull() {
            addCriterion("CitationStatus is not null");
            return (Criteria) this;
        }

        public Criteria andCitationStatusEqualTo(Integer value) {
            addCriterion("CitationStatus =", value, "citationStatus");
            return (Criteria) this;
        }

        public Criteria andCitationStatusNotEqualTo(Integer value) {
            addCriterion("CitationStatus <>", value, "citationStatus");
            return (Criteria) this;
        }

        public Criteria andCitationStatusGreaterThan(Integer value) {
            addCriterion("CitationStatus >", value, "citationStatus");
            return (Criteria) this;
        }

        public Criteria andCitationStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("CitationStatus >=", value, "citationStatus");
            return (Criteria) this;
        }

        public Criteria andCitationStatusLessThan(Integer value) {
            addCriterion("CitationStatus <", value, "citationStatus");
            return (Criteria) this;
        }

        public Criteria andCitationStatusLessThanOrEqualTo(Integer value) {
            addCriterion("CitationStatus <=", value, "citationStatus");
            return (Criteria) this;
        }

        public Criteria andCitationStatusIn(List<Integer> values) {
            addCriterion("CitationStatus in", values, "citationStatus");
            return (Criteria) this;
        }

        public Criteria andCitationStatusNotIn(List<Integer> values) {
            addCriterion("CitationStatus not in", values, "citationStatus");
            return (Criteria) this;
        }

        public Criteria andCitationStatusBetween(Integer value1, Integer value2) {
            addCriterion("CitationStatus between", value1, value2, "citationStatus");
            return (Criteria) this;
        }

        public Criteria andCitationStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("CitationStatus not between", value1, value2, "citationStatus");
            return (Criteria) this;
        }

        public Criteria andCreatedDateIsNull() {
            addCriterion("CreatedDate is null");
            return (Criteria) this;
        }

        public Criteria andCreatedDateIsNotNull() {
            addCriterion("CreatedDate is not null");
            return (Criteria) this;
        }

        public Criteria andCreatedDateEqualTo(Date value) {
            addCriterion("CreatedDate =", value, "createdDate");
            return (Criteria) this;
        }

        public Criteria andCreatedDateNotEqualTo(Date value) {
            addCriterion("CreatedDate <>", value, "createdDate");
            return (Criteria) this;
        }

        public Criteria andCreatedDateGreaterThan(Date value) {
            addCriterion("CreatedDate >", value, "createdDate");
            return (Criteria) this;
        }

        public Criteria andCreatedDateGreaterThanOrEqualTo(Date value) {
            addCriterion("CreatedDate >=", value, "createdDate");
            return (Criteria) this;
        }

        public Criteria andCreatedDateLessThan(Date value) {
            addCriterion("CreatedDate <", value, "createdDate");
            return (Criteria) this;
        }

        public Criteria andCreatedDateLessThanOrEqualTo(Date value) {
            addCriterion("CreatedDate <=", value, "createdDate");
            return (Criteria) this;
        }

        public Criteria andCreatedDateIn(List<Date> values) {
            addCriterion("CreatedDate in", values, "createdDate");
            return (Criteria) this;
        }

        public Criteria andCreatedDateNotIn(List<Date> values) {
            addCriterion("CreatedDate not in", values, "createdDate");
            return (Criteria) this;
        }

        public Criteria andCreatedDateBetween(Date value1, Date value2) {
            addCriterion("CreatedDate between", value1, value2, "createdDate");
            return (Criteria) this;
        }

        public Criteria andCreatedDateNotBetween(Date value1, Date value2) {
            addCriterion("CreatedDate not between", value1, value2, "createdDate");
            return (Criteria) this;
        }

        public Criteria andModifiedDateIsNull() {
            addCriterion("ModifiedDate is null");
            return (Criteria) this;
        }

        public Criteria andModifiedDateIsNotNull() {
            addCriterion("ModifiedDate is not null");
            return (Criteria) this;
        }

        public Criteria andModifiedDateEqualTo(Long value) {
            addCriterion("ModifiedDate =", value, "modifiedDate");
            return (Criteria) this;
        }

        public Criteria andModifiedDateNotEqualTo(Long value) {
            addCriterion("ModifiedDate <>", value, "modifiedDate");
            return (Criteria) this;
        }

        public Criteria andModifiedDateGreaterThan(Long value) {
            addCriterion("ModifiedDate >", value, "modifiedDate");
            return (Criteria) this;
        }

        public Criteria andModifiedDateGreaterThanOrEqualTo(Long value) {
            addCriterion("ModifiedDate >=", value, "modifiedDate");
            return (Criteria) this;
        }

        public Criteria andModifiedDateLessThan(Long value) {
            addCriterion("ModifiedDate <", value, "modifiedDate");
            return (Criteria) this;
        }

        public Criteria andModifiedDateLessThanOrEqualTo(Long value) {
            addCriterion("ModifiedDate <=", value, "modifiedDate");
            return (Criteria) this;
        }

        public Criteria andModifiedDateIn(List<Long> values) {
            addCriterion("ModifiedDate in", values, "modifiedDate");
            return (Criteria) this;
        }

        public Criteria andModifiedDateNotIn(List<Long> values) {
            addCriterion("ModifiedDate not in", values, "modifiedDate");
            return (Criteria) this;
        }

        public Criteria andModifiedDateBetween(Long value1, Long value2) {
            addCriterion("ModifiedDate between", value1, value2, "modifiedDate");
            return (Criteria) this;
        }

        public Criteria andModifiedDateNotBetween(Long value1, Long value2) {
            addCriterion("ModifiedDate not between", value1, value2, "modifiedDate");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}