package com.sgs.otsnotes.dbstorages.mybatis.model;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class TestLineAnalyteRelInfoExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public TestLineAnalyteRelInfoExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("Id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("Id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("Id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("Id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("Id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("Id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("Id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("Id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("Id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("Id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("Id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("Id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andTestLineVersionIdIsNull() {
            addCriterion("TestLineVersionId is null");
            return (Criteria) this;
        }

        public Criteria andTestLineVersionIdIsNotNull() {
            addCriterion("TestLineVersionId is not null");
            return (Criteria) this;
        }

        public Criteria andTestLineVersionIdEqualTo(Integer value) {
            addCriterion("TestLineVersionId =", value, "testLineVersionId");
            return (Criteria) this;
        }

        public Criteria andTestLineVersionIdNotEqualTo(Integer value) {
            addCriterion("TestLineVersionId <>", value, "testLineVersionId");
            return (Criteria) this;
        }

        public Criteria andTestLineVersionIdGreaterThan(Integer value) {
            addCriterion("TestLineVersionId >", value, "testLineVersionId");
            return (Criteria) this;
        }

        public Criteria andTestLineVersionIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("TestLineVersionId >=", value, "testLineVersionId");
            return (Criteria) this;
        }

        public Criteria andTestLineVersionIdLessThan(Integer value) {
            addCriterion("TestLineVersionId <", value, "testLineVersionId");
            return (Criteria) this;
        }

        public Criteria andTestLineVersionIdLessThanOrEqualTo(Integer value) {
            addCriterion("TestLineVersionId <=", value, "testLineVersionId");
            return (Criteria) this;
        }

        public Criteria andTestLineVersionIdIn(List<Integer> values) {
            addCriterion("TestLineVersionId in", values, "testLineVersionId");
            return (Criteria) this;
        }

        public Criteria andTestLineVersionIdNotIn(List<Integer> values) {
            addCriterion("TestLineVersionId not in", values, "testLineVersionId");
            return (Criteria) this;
        }

        public Criteria andTestLineVersionIdBetween(Integer value1, Integer value2) {
            addCriterion("TestLineVersionId between", value1, value2, "testLineVersionId");
            return (Criteria) this;
        }

        public Criteria andTestLineVersionIdNotBetween(Integer value1, Integer value2) {
            addCriterion("TestLineVersionId not between", value1, value2, "testLineVersionId");
            return (Criteria) this;
        }

        public Criteria andTestAnalyteIdIsNull() {
            addCriterion("TestAnalyteId is null");
            return (Criteria) this;
        }

        public Criteria andTestAnalyteIdIsNotNull() {
            addCriterion("TestAnalyteId is not null");
            return (Criteria) this;
        }

        public Criteria andTestAnalyteIdEqualTo(Integer value) {
            addCriterion("TestAnalyteId =", value, "testAnalyteId");
            return (Criteria) this;
        }

        public Criteria andTestAnalyteIdNotEqualTo(Integer value) {
            addCriterion("TestAnalyteId <>", value, "testAnalyteId");
            return (Criteria) this;
        }

        public Criteria andTestAnalyteIdGreaterThan(Integer value) {
            addCriterion("TestAnalyteId >", value, "testAnalyteId");
            return (Criteria) this;
        }

        public Criteria andTestAnalyteIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("TestAnalyteId >=", value, "testAnalyteId");
            return (Criteria) this;
        }

        public Criteria andTestAnalyteIdLessThan(Integer value) {
            addCriterion("TestAnalyteId <", value, "testAnalyteId");
            return (Criteria) this;
        }

        public Criteria andTestAnalyteIdLessThanOrEqualTo(Integer value) {
            addCriterion("TestAnalyteId <=", value, "testAnalyteId");
            return (Criteria) this;
        }

        public Criteria andTestAnalyteIdIn(List<Integer> values) {
            addCriterion("TestAnalyteId in", values, "testAnalyteId");
            return (Criteria) this;
        }

        public Criteria andTestAnalyteIdNotIn(List<Integer> values) {
            addCriterion("TestAnalyteId not in", values, "testAnalyteId");
            return (Criteria) this;
        }

        public Criteria andTestAnalyteIdBetween(Integer value1, Integer value2) {
            addCriterion("TestAnalyteId between", value1, value2, "testAnalyteId");
            return (Criteria) this;
        }

        public Criteria andTestAnalyteIdNotBetween(Integer value1, Integer value2) {
            addCriterion("TestAnalyteId not between", value1, value2, "testAnalyteId");
            return (Criteria) this;
        }

        public Criteria andTestAnalyteSeqIsNull() {
            addCriterion("TestAnalyteSeq is null");
            return (Criteria) this;
        }

        public Criteria andTestAnalyteSeqIsNotNull() {
            addCriterion("TestAnalyteSeq is not null");
            return (Criteria) this;
        }

        public Criteria andTestAnalyteSeqEqualTo(Integer value) {
            addCriterion("TestAnalyteSeq =", value, "testAnalyteSeq");
            return (Criteria) this;
        }

        public Criteria andTestAnalyteSeqNotEqualTo(Integer value) {
            addCriterion("TestAnalyteSeq <>", value, "testAnalyteSeq");
            return (Criteria) this;
        }

        public Criteria andTestAnalyteSeqGreaterThan(Integer value) {
            addCriterion("TestAnalyteSeq >", value, "testAnalyteSeq");
            return (Criteria) this;
        }

        public Criteria andTestAnalyteSeqGreaterThanOrEqualTo(Integer value) {
            addCriterion("TestAnalyteSeq >=", value, "testAnalyteSeq");
            return (Criteria) this;
        }

        public Criteria andTestAnalyteSeqLessThan(Integer value) {
            addCriterion("TestAnalyteSeq <", value, "testAnalyteSeq");
            return (Criteria) this;
        }

        public Criteria andTestAnalyteSeqLessThanOrEqualTo(Integer value) {
            addCriterion("TestAnalyteSeq <=", value, "testAnalyteSeq");
            return (Criteria) this;
        }

        public Criteria andTestAnalyteSeqIn(List<Integer> values) {
            addCriterion("TestAnalyteSeq in", values, "testAnalyteSeq");
            return (Criteria) this;
        }

        public Criteria andTestAnalyteSeqNotIn(List<Integer> values) {
            addCriterion("TestAnalyteSeq not in", values, "testAnalyteSeq");
            return (Criteria) this;
        }

        public Criteria andTestAnalyteSeqBetween(Integer value1, Integer value2) {
            addCriterion("TestAnalyteSeq between", value1, value2, "testAnalyteSeq");
            return (Criteria) this;
        }

        public Criteria andTestAnalyteSeqNotBetween(Integer value1, Integer value2) {
            addCriterion("TestAnalyteSeq not between", value1, value2, "testAnalyteSeq");
            return (Criteria) this;
        }

        public Criteria andSelectionTypeIsNull() {
            addCriterion("SelectionType is null");
            return (Criteria) this;
        }

        public Criteria andSelectionTypeIsNotNull() {
            addCriterion("SelectionType is not null");
            return (Criteria) this;
        }

        public Criteria andSelectionTypeEqualTo(Integer value) {
            addCriterion("SelectionType =", value, "selectionType");
            return (Criteria) this;
        }

        public Criteria andSelectionTypeNotEqualTo(Integer value) {
            addCriterion("SelectionType <>", value, "selectionType");
            return (Criteria) this;
        }

        public Criteria andSelectionTypeGreaterThan(Integer value) {
            addCriterion("SelectionType >", value, "selectionType");
            return (Criteria) this;
        }

        public Criteria andSelectionTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("SelectionType >=", value, "selectionType");
            return (Criteria) this;
        }

        public Criteria andSelectionTypeLessThan(Integer value) {
            addCriterion("SelectionType <", value, "selectionType");
            return (Criteria) this;
        }

        public Criteria andSelectionTypeLessThanOrEqualTo(Integer value) {
            addCriterion("SelectionType <=", value, "selectionType");
            return (Criteria) this;
        }

        public Criteria andSelectionTypeIn(List<Integer> values) {
            addCriterion("SelectionType in", values, "selectionType");
            return (Criteria) this;
        }

        public Criteria andSelectionTypeNotIn(List<Integer> values) {
            addCriterion("SelectionType not in", values, "selectionType");
            return (Criteria) this;
        }

        public Criteria andSelectionTypeBetween(Integer value1, Integer value2) {
            addCriterion("SelectionType between", value1, value2, "selectionType");
            return (Criteria) this;
        }

        public Criteria andSelectionTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("SelectionType not between", value1, value2, "selectionType");
            return (Criteria) this;
        }

        public Criteria andDescriptionAliasIsNull() {
            addCriterion("DescriptionAlias is null");
            return (Criteria) this;
        }

        public Criteria andDescriptionAliasIsNotNull() {
            addCriterion("DescriptionAlias is not null");
            return (Criteria) this;
        }

        public Criteria andDescriptionAliasEqualTo(String value) {
            addCriterion("DescriptionAlias =", value, "descriptionAlias");
            return (Criteria) this;
        }

        public Criteria andDescriptionAliasNotEqualTo(String value) {
            addCriterion("DescriptionAlias <>", value, "descriptionAlias");
            return (Criteria) this;
        }

        public Criteria andDescriptionAliasGreaterThan(String value) {
            addCriterion("DescriptionAlias >", value, "descriptionAlias");
            return (Criteria) this;
        }

        public Criteria andDescriptionAliasGreaterThanOrEqualTo(String value) {
            addCriterion("DescriptionAlias >=", value, "descriptionAlias");
            return (Criteria) this;
        }

        public Criteria andDescriptionAliasLessThan(String value) {
            addCriterion("DescriptionAlias <", value, "descriptionAlias");
            return (Criteria) this;
        }

        public Criteria andDescriptionAliasLessThanOrEqualTo(String value) {
            addCriterion("DescriptionAlias <=", value, "descriptionAlias");
            return (Criteria) this;
        }

        public Criteria andDescriptionAliasLike(String value) {
            addCriterion("DescriptionAlias like", value, "descriptionAlias");
            return (Criteria) this;
        }

        public Criteria andDescriptionAliasNotLike(String value) {
            addCriterion("DescriptionAlias not like", value, "descriptionAlias");
            return (Criteria) this;
        }

        public Criteria andDescriptionAliasIn(List<String> values) {
            addCriterion("DescriptionAlias in", values, "descriptionAlias");
            return (Criteria) this;
        }

        public Criteria andDescriptionAliasNotIn(List<String> values) {
            addCriterion("DescriptionAlias not in", values, "descriptionAlias");
            return (Criteria) this;
        }

        public Criteria andDescriptionAliasBetween(String value1, String value2) {
            addCriterion("DescriptionAlias between", value1, value2, "descriptionAlias");
            return (Criteria) this;
        }

        public Criteria andDescriptionAliasNotBetween(String value1, String value2) {
            addCriterion("DescriptionAlias not between", value1, value2, "descriptionAlias");
            return (Criteria) this;
        }

        public Criteria andBizVersionIdIsNull() {
            addCriterion("BizVersionId is null");
            return (Criteria) this;
        }

        public Criteria andBizVersionIdIsNotNull() {
            addCriterion("BizVersionId is not null");
            return (Criteria) this;
        }

        public Criteria andBizVersionIdEqualTo(String value) {
            addCriterion("BizVersionId =", value, "bizVersionId");
            return (Criteria) this;
        }

        public Criteria andBizVersionIdNotEqualTo(String value) {
            addCriterion("BizVersionId <>", value, "bizVersionId");
            return (Criteria) this;
        }

        public Criteria andBizVersionIdGreaterThan(String value) {
            addCriterion("BizVersionId >", value, "bizVersionId");
            return (Criteria) this;
        }

        public Criteria andBizVersionIdGreaterThanOrEqualTo(String value) {
            addCriterion("BizVersionId >=", value, "bizVersionId");
            return (Criteria) this;
        }

        public Criteria andBizVersionIdLessThan(String value) {
            addCriterion("BizVersionId <", value, "bizVersionId");
            return (Criteria) this;
        }

        public Criteria andBizVersionIdLessThanOrEqualTo(String value) {
            addCriterion("BizVersionId <=", value, "bizVersionId");
            return (Criteria) this;
        }

        public Criteria andBizVersionIdLike(String value) {
            addCriterion("BizVersionId like", value, "bizVersionId");
            return (Criteria) this;
        }

        public Criteria andBizVersionIdNotLike(String value) {
            addCriterion("BizVersionId not like", value, "bizVersionId");
            return (Criteria) this;
        }

        public Criteria andBizVersionIdIn(List<String> values) {
            addCriterion("BizVersionId in", values, "bizVersionId");
            return (Criteria) this;
        }

        public Criteria andBizVersionIdNotIn(List<String> values) {
            addCriterion("BizVersionId not in", values, "bizVersionId");
            return (Criteria) this;
        }

        public Criteria andBizVersionIdBetween(String value1, String value2) {
            addCriterion("BizVersionId between", value1, value2, "bizVersionId");
            return (Criteria) this;
        }

        public Criteria andBizVersionIdNotBetween(String value1, String value2) {
            addCriterion("BizVersionId not between", value1, value2, "bizVersionId");
            return (Criteria) this;
        }

        public Criteria andStatusIsNull() {
            addCriterion("`Status` is null");
            return (Criteria) this;
        }

        public Criteria andStatusIsNotNull() {
            addCriterion("`Status` is not null");
            return (Criteria) this;
        }

        public Criteria andStatusEqualTo(Integer value) {
            addCriterion("`Status` =", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotEqualTo(Integer value) {
            addCriterion("`Status` <>", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThan(Integer value) {
            addCriterion("`Status` >", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("`Status` >=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThan(Integer value) {
            addCriterion("`Status` <", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThanOrEqualTo(Integer value) {
            addCriterion("`Status` <=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusIn(List<Integer> values) {
            addCriterion("`Status` in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotIn(List<Integer> values) {
            addCriterion("`Status` not in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusBetween(Integer value1, Integer value2) {
            addCriterion("`Status` between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("`Status` not between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andCreatedDateIsNull() {
            addCriterion("CreatedDate is null");
            return (Criteria) this;
        }

        public Criteria andCreatedDateIsNotNull() {
            addCriterion("CreatedDate is not null");
            return (Criteria) this;
        }

        public Criteria andCreatedDateEqualTo(Date value) {
            addCriterion("CreatedDate =", value, "createdDate");
            return (Criteria) this;
        }

        public Criteria andCreatedDateNotEqualTo(Date value) {
            addCriterion("CreatedDate <>", value, "createdDate");
            return (Criteria) this;
        }

        public Criteria andCreatedDateGreaterThan(Date value) {
            addCriterion("CreatedDate >", value, "createdDate");
            return (Criteria) this;
        }

        public Criteria andCreatedDateGreaterThanOrEqualTo(Date value) {
            addCriterion("CreatedDate >=", value, "createdDate");
            return (Criteria) this;
        }

        public Criteria andCreatedDateLessThan(Date value) {
            addCriterion("CreatedDate <", value, "createdDate");
            return (Criteria) this;
        }

        public Criteria andCreatedDateLessThanOrEqualTo(Date value) {
            addCriterion("CreatedDate <=", value, "createdDate");
            return (Criteria) this;
        }

        public Criteria andCreatedDateIn(List<Date> values) {
            addCriterion("CreatedDate in", values, "createdDate");
            return (Criteria) this;
        }

        public Criteria andCreatedDateNotIn(List<Date> values) {
            addCriterion("CreatedDate not in", values, "createdDate");
            return (Criteria) this;
        }

        public Criteria andCreatedDateBetween(Date value1, Date value2) {
            addCriterion("CreatedDate between", value1, value2, "createdDate");
            return (Criteria) this;
        }

        public Criteria andCreatedDateNotBetween(Date value1, Date value2) {
            addCriterion("CreatedDate not between", value1, value2, "createdDate");
            return (Criteria) this;
        }

        public Criteria andModifiedDateIsNull() {
            addCriterion("ModifiedDate is null");
            return (Criteria) this;
        }

        public Criteria andModifiedDateIsNotNull() {
            addCriterion("ModifiedDate is not null");
            return (Criteria) this;
        }

        public Criteria andModifiedDateEqualTo(Date value) {
            addCriterion("ModifiedDate =", value, "modifiedDate");
            return (Criteria) this;
        }

        public Criteria andModifiedDateNotEqualTo(Date value) {
            addCriterion("ModifiedDate <>", value, "modifiedDate");
            return (Criteria) this;
        }

        public Criteria andModifiedDateGreaterThan(Date value) {
            addCriterion("ModifiedDate >", value, "modifiedDate");
            return (Criteria) this;
        }

        public Criteria andModifiedDateGreaterThanOrEqualTo(Date value) {
            addCriterion("ModifiedDate >=", value, "modifiedDate");
            return (Criteria) this;
        }

        public Criteria andModifiedDateLessThan(Date value) {
            addCriterion("ModifiedDate <", value, "modifiedDate");
            return (Criteria) this;
        }

        public Criteria andModifiedDateLessThanOrEqualTo(Date value) {
            addCriterion("ModifiedDate <=", value, "modifiedDate");
            return (Criteria) this;
        }

        public Criteria andModifiedDateIn(List<Date> values) {
            addCriterion("ModifiedDate in", values, "modifiedDate");
            return (Criteria) this;
        }

        public Criteria andModifiedDateNotIn(List<Date> values) {
            addCriterion("ModifiedDate not in", values, "modifiedDate");
            return (Criteria) this;
        }

        public Criteria andModifiedDateBetween(Date value1, Date value2) {
            addCriterion("ModifiedDate between", value1, value2, "modifiedDate");
            return (Criteria) this;
        }

        public Criteria andModifiedDateNotBetween(Date value1, Date value2) {
            addCriterion("ModifiedDate not between", value1, value2, "modifiedDate");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}