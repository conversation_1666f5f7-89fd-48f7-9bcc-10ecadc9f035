package com.sgs.otsnotes.dbstorages.mybatis.extmodel.matrix;

import com.sgs.framework.core.base.BaseProductLine;
import com.sgs.otsnotes.dbstorages.mybatis.model.*;

import java.util.List;

public class SyncAzureTestMatrixInfo extends BaseProductLine {
    /**
     *
     */
    private List<String> delReportMatrixRelIds;
    /**
     *
     */
    private List<String> delConditionIds;
    /**
     *
     */
    private List<String> delConditionGroupIds;
    /**
     *
     */
    private List<String> delTestPositionIds;
    /**
     *
     */
    private List<String> delProductAttrIds;
    /**
     *
     */
    private List<String> delProductAttrLimitGroupIds;
    /**
     *
     */
    private List<LimitInstancePO> delTestDataIds;
    /**
     *
     */
    private List<String> delLimitIds;
    /**
     *
     */
    private List<String> delLimitGroupIds;
    /**
     *
     */
    private List<String> delConclusionPpSampleRelIds;
    /**
     *
     */
    private List<String> delConclusionIds;
    /**
     *
     */
    private List<String> delPpSampleRelIds;
    /**
     *
     */
    private List<String> delTestConditionGroupIds;
    /**
     *
     */
    private List<String> delTestMatrixIds;
    /**
     *
     */
    private List<PPSampleRelationshipInfoPO> ppSampleRels;
    /**
     *
     */
    private List<TestMatrixPO> testMatrixs;
    /**
     *
     */
    private List<ReportMatrixRelationShipInfoPO> reportMatrixRels;
    /**
     *
     */
    private List<TestLineInstancePO> conditionStatus;

    public List<String> getDelReportMatrixRelIds() {
        return delReportMatrixRelIds;
    }

    public void setDelReportMatrixRelIds(List<String> delReportMatrixRelIds) {
        this.delReportMatrixRelIds = delReportMatrixRelIds;
    }

    public List<String> getDelConditionIds() {
        return delConditionIds;
    }

    public void setDelConditionIds(List<String> delConditionIds) {
        this.delConditionIds = delConditionIds;
    }

    public List<String> getDelConditionGroupIds() {
        return delConditionGroupIds;
    }

    public void setDelConditionGroupIds(List<String> delConditionGroupIds) {
        this.delConditionGroupIds = delConditionGroupIds;
    }

    public List<String> getDelTestPositionIds() {
        return delTestPositionIds;
    }

    public void setDelTestPositionIds(List<String> delTestPositionIds) {
        this.delTestPositionIds = delTestPositionIds;
    }

    public List<String> getDelProductAttrIds() {
        return delProductAttrIds;
    }

    public void setDelProductAttrIds(List<String> delProductAttrIds) {
        this.delProductAttrIds = delProductAttrIds;
    }

    public List<String> getDelProductAttrLimitGroupIds() {
        return delProductAttrLimitGroupIds;
    }

    public void setDelProductAttrLimitGroupIds(List<String> delProductAttrLimitGroupIds) {
        this.delProductAttrLimitGroupIds = delProductAttrLimitGroupIds;
    }

    public List<LimitInstancePO> getDelTestDataIds() {
        return delTestDataIds;
    }

    public void setDelTestDataIds(List<LimitInstancePO> delTestDataIds) {
        this.delTestDataIds = delTestDataIds;
    }

    public List<String> getDelLimitIds() {
        return delLimitIds;
    }

    public void setDelLimitIds(List<String> delLimitIds) {
        this.delLimitIds = delLimitIds;
    }

    public List<String> getDelLimitGroupIds() {
        return delLimitGroupIds;
    }

    public void setDelLimitGroupIds(List<String> delLimitGroupIds) {
        this.delLimitGroupIds = delLimitGroupIds;
    }

    public List<String> getDelConclusionPpSampleRelIds() {
        return delConclusionPpSampleRelIds;
    }

    public void setDelConclusionPpSampleRelIds(List<String> delConclusionPpSampleRelIds) {
        this.delConclusionPpSampleRelIds = delConclusionPpSampleRelIds;
    }

    public List<String> getDelConclusionIds() {
        return delConclusionIds;
    }

    public void setDelConclusionIds(List<String> delConclusionIds) {
        this.delConclusionIds = delConclusionIds;
    }

    public List<String> getDelPpSampleRelIds() {
        return delPpSampleRelIds;
    }

    public void setDelPpSampleRelIds(List<String> delPpSampleRelIds) {
        this.delPpSampleRelIds = delPpSampleRelIds;
    }

    public List<String> getDelTestConditionGroupIds() {
        return delTestConditionGroupIds;
    }

    public void setDelTestConditionGroupIds(List<String> delTestConditionGroupIds) {
        this.delTestConditionGroupIds = delTestConditionGroupIds;
    }

    public List<String> getDelTestMatrixIds() {
        return delTestMatrixIds;
    }

    public void setDelTestMatrixIds(List<String> delTestMatrixIds) {
        this.delTestMatrixIds = delTestMatrixIds;
    }

    public List<PPSampleRelationshipInfoPO> getPpSampleRels() {
        return ppSampleRels;
    }

    public void setPpSampleRels(List<PPSampleRelationshipInfoPO> ppSampleRels) {
        this.ppSampleRels = ppSampleRels;
    }

    public List<TestMatrixPO> getTestMatrixs() {
        return testMatrixs;
    }

    public void setTestMatrixs(List<TestMatrixPO> testMatrixs) {
        this.testMatrixs = testMatrixs;
    }

    public List<ReportMatrixRelationShipInfoPO> getReportMatrixRels() {
        return reportMatrixRels;
    }

    public void setReportMatrixRels(List<ReportMatrixRelationShipInfoPO> reportMatrixRels) {
        this.reportMatrixRels = reportMatrixRels;
    }

    public List<TestLineInstancePO> getConditionStatus() {
        return conditionStatus;
    }

    public void setConditionStatus(List<TestLineInstancePO> conditionStatus) {
        this.conditionStatus = conditionStatus;
    }
}
