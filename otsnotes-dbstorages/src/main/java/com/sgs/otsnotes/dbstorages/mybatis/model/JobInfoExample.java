package com.sgs.otsnotes.dbstorages.mybatis.model;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class JobInfoExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public JobInfoExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIDIsNull() {
            addCriterion("ID is null");
            return (Criteria) this;
        }

        public Criteria andIDIsNotNull() {
            addCriterion("ID is not null");
            return (Criteria) this;
        }

        public Criteria andIDEqualTo(String value) {
            addCriterion("ID =", value, "ID");
            return (Criteria) this;
        }

        public Criteria andIDNotEqualTo(String value) {
            addCriterion("ID <>", value, "ID");
            return (Criteria) this;
        }

        public Criteria andIDGreaterThan(String value) {
            addCriterion("ID >", value, "ID");
            return (Criteria) this;
        }

        public Criteria andIDGreaterThanOrEqualTo(String value) {
            addCriterion("ID >=", value, "ID");
            return (Criteria) this;
        }

        public Criteria andIDLessThan(String value) {
            addCriterion("ID <", value, "ID");
            return (Criteria) this;
        }

        public Criteria andIDLessThanOrEqualTo(String value) {
            addCriterion("ID <=", value, "ID");
            return (Criteria) this;
        }

        public Criteria andIDLike(String value) {
            addCriterion("ID like", value, "ID");
            return (Criteria) this;
        }

        public Criteria andIDNotLike(String value) {
            addCriterion("ID not like", value, "ID");
            return (Criteria) this;
        }

        public Criteria andIDIn(List<String> values) {
            addCriterion("ID in", values, "ID");
            return (Criteria) this;
        }

        public Criteria andIDNotIn(List<String> values) {
            addCriterion("ID not in", values, "ID");
            return (Criteria) this;
        }

        public Criteria andIDBetween(String value1, String value2) {
            addCriterion("ID between", value1, value2, "ID");
            return (Criteria) this;
        }

        public Criteria andIDNotBetween(String value1, String value2) {
            addCriterion("ID not between", value1, value2, "ID");
            return (Criteria) this;
        }

        public Criteria andGeneralOrderInstanceIDIsNull() {
            addCriterion("GeneralOrderInstanceID is null");
            return (Criteria) this;
        }

        public Criteria andGeneralOrderInstanceIDIsNotNull() {
            addCriterion("GeneralOrderInstanceID is not null");
            return (Criteria) this;
        }

        public Criteria andGeneralOrderInstanceIDEqualTo(String value) {
            addCriterion("GeneralOrderInstanceID =", value, "generalOrderInstanceID");
            return (Criteria) this;
        }

        public Criteria andGeneralOrderInstanceIDNotEqualTo(String value) {
            addCriterion("GeneralOrderInstanceID <>", value, "generalOrderInstanceID");
            return (Criteria) this;
        }

        public Criteria andGeneralOrderInstanceIDGreaterThan(String value) {
            addCriterion("GeneralOrderInstanceID >", value, "generalOrderInstanceID");
            return (Criteria) this;
        }

        public Criteria andGeneralOrderInstanceIDGreaterThanOrEqualTo(String value) {
            addCriterion("GeneralOrderInstanceID >=", value, "generalOrderInstanceID");
            return (Criteria) this;
        }

        public Criteria andGeneralOrderInstanceIDLessThan(String value) {
            addCriterion("GeneralOrderInstanceID <", value, "generalOrderInstanceID");
            return (Criteria) this;
        }

        public Criteria andGeneralOrderInstanceIDLessThanOrEqualTo(String value) {
            addCriterion("GeneralOrderInstanceID <=", value, "generalOrderInstanceID");
            return (Criteria) this;
        }

        public Criteria andGeneralOrderInstanceIDLike(String value) {
            addCriterion("GeneralOrderInstanceID like", value, "generalOrderInstanceID");
            return (Criteria) this;
        }

        public Criteria andGeneralOrderInstanceIDNotLike(String value) {
            addCriterion("GeneralOrderInstanceID not like", value, "generalOrderInstanceID");
            return (Criteria) this;
        }

        public Criteria andGeneralOrderInstanceIDIn(List<String> values) {
            addCriterion("GeneralOrderInstanceID in", values, "generalOrderInstanceID");
            return (Criteria) this;
        }

        public Criteria andGeneralOrderInstanceIDNotIn(List<String> values) {
            addCriterion("GeneralOrderInstanceID not in", values, "generalOrderInstanceID");
            return (Criteria) this;
        }

        public Criteria andGeneralOrderInstanceIDBetween(String value1, String value2) {
            addCriterion("GeneralOrderInstanceID between", value1, value2, "generalOrderInstanceID");
            return (Criteria) this;
        }

        public Criteria andGeneralOrderInstanceIDNotBetween(String value1, String value2) {
            addCriterion("GeneralOrderInstanceID not between", value1, value2, "generalOrderInstanceID");
            return (Criteria) this;
        }

        public Criteria andLabSectionIdIsNull() {
            addCriterion("LabSectionId is null");
            return (Criteria) this;
        }

        public Criteria andLabSectionIdIsNotNull() {
            addCriterion("LabSectionId is not null");
            return (Criteria) this;
        }

        public Criteria andLabSectionIdEqualTo(Integer value) {
            addCriterion("LabSectionId =", value, "labSectionId");
            return (Criteria) this;
        }

        public Criteria andLabSectionIdNotEqualTo(Integer value) {
            addCriterion("LabSectionId <>", value, "labSectionId");
            return (Criteria) this;
        }

        public Criteria andLabSectionIdGreaterThan(Integer value) {
            addCriterion("LabSectionId >", value, "labSectionId");
            return (Criteria) this;
        }

        public Criteria andLabSectionIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("LabSectionId >=", value, "labSectionId");
            return (Criteria) this;
        }

        public Criteria andLabSectionIdLessThan(Integer value) {
            addCriterion("LabSectionId <", value, "labSectionId");
            return (Criteria) this;
        }

        public Criteria andLabSectionIdLessThanOrEqualTo(Integer value) {
            addCriterion("LabSectionId <=", value, "labSectionId");
            return (Criteria) this;
        }

        public Criteria andLabSectionIdIn(List<Integer> values) {
            addCriterion("LabSectionId in", values, "labSectionId");
            return (Criteria) this;
        }

        public Criteria andLabSectionIdNotIn(List<Integer> values) {
            addCriterion("LabSectionId not in", values, "labSectionId");
            return (Criteria) this;
        }

        public Criteria andLabSectionIdBetween(Integer value1, Integer value2) {
            addCriterion("LabSectionId between", value1, value2, "labSectionId");
            return (Criteria) this;
        }

        public Criteria andLabSectionIdNotBetween(Integer value1, Integer value2) {
            addCriterion("LabSectionId not between", value1, value2, "labSectionId");
            return (Criteria) this;
        }

        public Criteria andOrderNoIsNull() {
            addCriterion("OrderNo is null");
            return (Criteria) this;
        }

        public Criteria andOrderNoIsNotNull() {
            addCriterion("OrderNo is not null");
            return (Criteria) this;
        }

        public Criteria andOrderNoEqualTo(String value) {
            addCriterion("OrderNo =", value, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoNotEqualTo(String value) {
            addCriterion("OrderNo <>", value, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoGreaterThan(String value) {
            addCriterion("OrderNo >", value, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoGreaterThanOrEqualTo(String value) {
            addCriterion("OrderNo >=", value, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoLessThan(String value) {
            addCriterion("OrderNo <", value, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoLessThanOrEqualTo(String value) {
            addCriterion("OrderNo <=", value, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoLike(String value) {
            addCriterion("OrderNo like", value, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoNotLike(String value) {
            addCriterion("OrderNo not like", value, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoIn(List<String> values) {
            addCriterion("OrderNo in", values, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoNotIn(List<String> values) {
            addCriterion("OrderNo not in", values, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoBetween(String value1, String value2) {
            addCriterion("OrderNo between", value1, value2, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoNotBetween(String value1, String value2) {
            addCriterion("OrderNo not between", value1, value2, "orderNo");
            return (Criteria) this;
        }

        public Criteria andJobNoIsNull() {
            addCriterion("JobNo is null");
            return (Criteria) this;
        }

        public Criteria andJobNoIsNotNull() {
            addCriterion("JobNo is not null");
            return (Criteria) this;
        }

        public Criteria andJobNoEqualTo(String value) {
            addCriterion("JobNo =", value, "jobNo");
            return (Criteria) this;
        }

        public Criteria andJobNoNotEqualTo(String value) {
            addCriterion("JobNo <>", value, "jobNo");
            return (Criteria) this;
        }

        public Criteria andJobNoGreaterThan(String value) {
            addCriterion("JobNo >", value, "jobNo");
            return (Criteria) this;
        }

        public Criteria andJobNoGreaterThanOrEqualTo(String value) {
            addCriterion("JobNo >=", value, "jobNo");
            return (Criteria) this;
        }

        public Criteria andJobNoLessThan(String value) {
            addCriterion("JobNo <", value, "jobNo");
            return (Criteria) this;
        }

        public Criteria andJobNoLessThanOrEqualTo(String value) {
            addCriterion("JobNo <=", value, "jobNo");
            return (Criteria) this;
        }

        public Criteria andJobNoLike(String value) {
            addCriterion("JobNo like", value, "jobNo");
            return (Criteria) this;
        }

        public Criteria andJobNoNotLike(String value) {
            addCriterion("JobNo not like", value, "jobNo");
            return (Criteria) this;
        }

        public Criteria andJobNoIn(List<String> values) {
            addCriterion("JobNo in", values, "jobNo");
            return (Criteria) this;
        }

        public Criteria andJobNoNotIn(List<String> values) {
            addCriterion("JobNo not in", values, "jobNo");
            return (Criteria) this;
        }

        public Criteria andJobNoBetween(String value1, String value2) {
            addCriterion("JobNo between", value1, value2, "jobNo");
            return (Criteria) this;
        }

        public Criteria andJobNoNotBetween(String value1, String value2) {
            addCriterion("JobNo not between", value1, value2, "jobNo");
            return (Criteria) this;
        }

        public Criteria andJobStatusIsNull() {
            addCriterion("JobStatus is null");
            return (Criteria) this;
        }

        public Criteria andJobStatusIsNotNull() {
            addCriterion("JobStatus is not null");
            return (Criteria) this;
        }

        public Criteria andJobStatusEqualTo(Integer value) {
            addCriterion("JobStatus =", value, "jobStatus");
            return (Criteria) this;
        }

        public Criteria andJobStatusNotEqualTo(Integer value) {
            addCriterion("JobStatus <>", value, "jobStatus");
            return (Criteria) this;
        }

        public Criteria andJobStatusGreaterThan(Integer value) {
            addCriterion("JobStatus >", value, "jobStatus");
            return (Criteria) this;
        }

        public Criteria andJobStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("JobStatus >=", value, "jobStatus");
            return (Criteria) this;
        }

        public Criteria andJobStatusLessThan(Integer value) {
            addCriterion("JobStatus <", value, "jobStatus");
            return (Criteria) this;
        }

        public Criteria andJobStatusLessThanOrEqualTo(Integer value) {
            addCriterion("JobStatus <=", value, "jobStatus");
            return (Criteria) this;
        }

        public Criteria andJobStatusIn(List<Integer> values) {
            addCriterion("JobStatus in", values, "jobStatus");
            return (Criteria) this;
        }

        public Criteria andJobStatusNotIn(List<Integer> values) {
            addCriterion("JobStatus not in", values, "jobStatus");
            return (Criteria) this;
        }

        public Criteria andJobStatusBetween(Integer value1, Integer value2) {
            addCriterion("JobStatus between", value1, value2, "jobStatus");
            return (Criteria) this;
        }

        public Criteria andJobStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("JobStatus not between", value1, value2, "jobStatus");
            return (Criteria) this;
        }

        public Criteria andLabInDateIsNull() {
            addCriterion("LabInDate is null");
            return (Criteria) this;
        }

        public Criteria andLabInDateIsNotNull() {
            addCriterion("LabInDate is not null");
            return (Criteria) this;
        }

        public Criteria andLabInDateEqualTo(Date value) {
            addCriterion("LabInDate =", value, "labInDate");
            return (Criteria) this;
        }

        public Criteria andLabInDateNotEqualTo(Date value) {
            addCriterion("LabInDate <>", value, "labInDate");
            return (Criteria) this;
        }

        public Criteria andLabInDateGreaterThan(Date value) {
            addCriterion("LabInDate >", value, "labInDate");
            return (Criteria) this;
        }

        public Criteria andLabInDateGreaterThanOrEqualTo(Date value) {
            addCriterion("LabInDate >=", value, "labInDate");
            return (Criteria) this;
        }

        public Criteria andLabInDateLessThan(Date value) {
            addCriterion("LabInDate <", value, "labInDate");
            return (Criteria) this;
        }

        public Criteria andLabInDateLessThanOrEqualTo(Date value) {
            addCriterion("LabInDate <=", value, "labInDate");
            return (Criteria) this;
        }

        public Criteria andLabInDateIn(List<Date> values) {
            addCriterion("LabInDate in", values, "labInDate");
            return (Criteria) this;
        }

        public Criteria andLabInDateNotIn(List<Date> values) {
            addCriterion("LabInDate not in", values, "labInDate");
            return (Criteria) this;
        }

        public Criteria andLabInDateBetween(Date value1, Date value2) {
            addCriterion("LabInDate between", value1, value2, "labInDate");
            return (Criteria) this;
        }

        public Criteria andLabInDateNotBetween(Date value1, Date value2) {
            addCriterion("LabInDate not between", value1, value2, "labInDate");
            return (Criteria) this;
        }

        public Criteria andLabOutDateIsNull() {
            addCriterion("LabOutDate is null");
            return (Criteria) this;
        }

        public Criteria andLabOutDateIsNotNull() {
            addCriterion("LabOutDate is not null");
            return (Criteria) this;
        }

        public Criteria andLabOutDateEqualTo(Date value) {
            addCriterion("LabOutDate =", value, "labOutDate");
            return (Criteria) this;
        }

        public Criteria andLabOutDateNotEqualTo(Date value) {
            addCriterion("LabOutDate <>", value, "labOutDate");
            return (Criteria) this;
        }

        public Criteria andLabOutDateGreaterThan(Date value) {
            addCriterion("LabOutDate >", value, "labOutDate");
            return (Criteria) this;
        }

        public Criteria andLabOutDateGreaterThanOrEqualTo(Date value) {
            addCriterion("LabOutDate >=", value, "labOutDate");
            return (Criteria) this;
        }

        public Criteria andLabOutDateLessThan(Date value) {
            addCriterion("LabOutDate <", value, "labOutDate");
            return (Criteria) this;
        }

        public Criteria andLabOutDateLessThanOrEqualTo(Date value) {
            addCriterion("LabOutDate <=", value, "labOutDate");
            return (Criteria) this;
        }

        public Criteria andLabOutDateIn(List<Date> values) {
            addCriterion("LabOutDate in", values, "labOutDate");
            return (Criteria) this;
        }

        public Criteria andLabOutDateNotIn(List<Date> values) {
            addCriterion("LabOutDate not in", values, "labOutDate");
            return (Criteria) this;
        }

        public Criteria andLabOutDateBetween(Date value1, Date value2) {
            addCriterion("LabOutDate between", value1, value2, "labOutDate");
            return (Criteria) this;
        }

        public Criteria andLabOutDateNotBetween(Date value1, Date value2) {
            addCriterion("LabOutDate not between", value1, value2, "labOutDate");
            return (Criteria) this;
        }

        public Criteria andLabOutByIsNull() {
            addCriterion("LabOutBy is null");
            return (Criteria) this;
        }

        public Criteria andLabOutByIsNotNull() {
            addCriterion("LabOutBy is not null");
            return (Criteria) this;
        }

        public Criteria andLabOutByEqualTo(String value) {
            addCriterion("LabOutBy =", value, "labOutBy");
            return (Criteria) this;
        }

        public Criteria andLabOutByNotEqualTo(String value) {
            addCriterion("LabOutBy <>", value, "labOutBy");
            return (Criteria) this;
        }

        public Criteria andLabOutByGreaterThan(String value) {
            addCriterion("LabOutBy >", value, "labOutBy");
            return (Criteria) this;
        }

        public Criteria andLabOutByGreaterThanOrEqualTo(String value) {
            addCriterion("LabOutBy >=", value, "labOutBy");
            return (Criteria) this;
        }

        public Criteria andLabOutByLessThan(String value) {
            addCriterion("LabOutBy <", value, "labOutBy");
            return (Criteria) this;
        }

        public Criteria andLabOutByLessThanOrEqualTo(String value) {
            addCriterion("LabOutBy <=", value, "labOutBy");
            return (Criteria) this;
        }

        public Criteria andLabOutByLike(String value) {
            addCriterion("LabOutBy like", value, "labOutBy");
            return (Criteria) this;
        }

        public Criteria andLabOutByNotLike(String value) {
            addCriterion("LabOutBy not like", value, "labOutBy");
            return (Criteria) this;
        }

        public Criteria andLabOutByIn(List<String> values) {
            addCriterion("LabOutBy in", values, "labOutBy");
            return (Criteria) this;
        }

        public Criteria andLabOutByNotIn(List<String> values) {
            addCriterion("LabOutBy not in", values, "labOutBy");
            return (Criteria) this;
        }

        public Criteria andLabOutByBetween(String value1, String value2) {
            addCriterion("LabOutBy between", value1, value2, "labOutBy");
            return (Criteria) this;
        }

        public Criteria andLabOutByNotBetween(String value1, String value2) {
            addCriterion("LabOutBy not between", value1, value2, "labOutBy");
            return (Criteria) this;
        }

        public Criteria andRemarkIsNull() {
            addCriterion("Remark is null");
            return (Criteria) this;
        }

        public Criteria andRemarkIsNotNull() {
            addCriterion("Remark is not null");
            return (Criteria) this;
        }

        public Criteria andRemarkEqualTo(String value) {
            addCriterion("Remark =", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotEqualTo(String value) {
            addCriterion("Remark <>", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkGreaterThan(String value) {
            addCriterion("Remark >", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkGreaterThanOrEqualTo(String value) {
            addCriterion("Remark >=", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkLessThan(String value) {
            addCriterion("Remark <", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkLessThanOrEqualTo(String value) {
            addCriterion("Remark <=", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkLike(String value) {
            addCriterion("Remark like", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotLike(String value) {
            addCriterion("Remark not like", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkIn(List<String> values) {
            addCriterion("Remark in", values, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotIn(List<String> values) {
            addCriterion("Remark not in", values, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkBetween(String value1, String value2) {
            addCriterion("Remark between", value1, value2, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotBetween(String value1, String value2) {
            addCriterion("Remark not between", value1, value2, "remark");
            return (Criteria) this;
        }

        public Criteria andActiveIndicatorIsNull() {
            addCriterion("ActiveIndicator is null");
            return (Criteria) this;
        }

        public Criteria andActiveIndicatorIsNotNull() {
            addCriterion("ActiveIndicator is not null");
            return (Criteria) this;
        }

        public Criteria andActiveIndicatorEqualTo(Boolean value) {
            addCriterion("ActiveIndicator =", value, "activeIndicator");
            return (Criteria) this;
        }

        public Criteria andActiveIndicatorNotEqualTo(Boolean value) {
            addCriterion("ActiveIndicator <>", value, "activeIndicator");
            return (Criteria) this;
        }

        public Criteria andActiveIndicatorGreaterThan(Boolean value) {
            addCriterion("ActiveIndicator >", value, "activeIndicator");
            return (Criteria) this;
        }

        public Criteria andActiveIndicatorGreaterThanOrEqualTo(Boolean value) {
            addCriterion("ActiveIndicator >=", value, "activeIndicator");
            return (Criteria) this;
        }

        public Criteria andActiveIndicatorLessThan(Boolean value) {
            addCriterion("ActiveIndicator <", value, "activeIndicator");
            return (Criteria) this;
        }

        public Criteria andActiveIndicatorLessThanOrEqualTo(Boolean value) {
            addCriterion("ActiveIndicator <=", value, "activeIndicator");
            return (Criteria) this;
        }

        public Criteria andActiveIndicatorIn(List<Boolean> values) {
            addCriterion("ActiveIndicator in", values, "activeIndicator");
            return (Criteria) this;
        }

        public Criteria andActiveIndicatorNotIn(List<Boolean> values) {
            addCriterion("ActiveIndicator not in", values, "activeIndicator");
            return (Criteria) this;
        }

        public Criteria andActiveIndicatorBetween(Boolean value1, Boolean value2) {
            addCriterion("ActiveIndicator between", value1, value2, "activeIndicator");
            return (Criteria) this;
        }

        public Criteria andActiveIndicatorNotBetween(Boolean value1, Boolean value2) {
            addCriterion("ActiveIndicator not between", value1, value2, "activeIndicator");
            return (Criteria) this;
        }

        public Criteria andCreatedDateIsNull() {
            addCriterion("CreatedDate is null");
            return (Criteria) this;
        }

        public Criteria andCreatedDateIsNotNull() {
            addCriterion("CreatedDate is not null");
            return (Criteria) this;
        }

        public Criteria andCreatedDateEqualTo(Date value) {
            addCriterion("CreatedDate =", value, "createdDate");
            return (Criteria) this;
        }

        public Criteria andCreatedDateNotEqualTo(Date value) {
            addCriterion("CreatedDate <>", value, "createdDate");
            return (Criteria) this;
        }

        public Criteria andCreatedDateGreaterThan(Date value) {
            addCriterion("CreatedDate >", value, "createdDate");
            return (Criteria) this;
        }

        public Criteria andCreatedDateGreaterThanOrEqualTo(Date value) {
            addCriterion("CreatedDate >=", value, "createdDate");
            return (Criteria) this;
        }

        public Criteria andCreatedDateLessThan(Date value) {
            addCriterion("CreatedDate <", value, "createdDate");
            return (Criteria) this;
        }

        public Criteria andCreatedDateLessThanOrEqualTo(Date value) {
            addCriterion("CreatedDate <=", value, "createdDate");
            return (Criteria) this;
        }

        public Criteria andCreatedDateIn(List<Date> values) {
            addCriterion("CreatedDate in", values, "createdDate");
            return (Criteria) this;
        }

        public Criteria andCreatedDateNotIn(List<Date> values) {
            addCriterion("CreatedDate not in", values, "createdDate");
            return (Criteria) this;
        }

        public Criteria andCreatedDateBetween(Date value1, Date value2) {
            addCriterion("CreatedDate between", value1, value2, "createdDate");
            return (Criteria) this;
        }

        public Criteria andCreatedDateNotBetween(Date value1, Date value2) {
            addCriterion("CreatedDate not between", value1, value2, "createdDate");
            return (Criteria) this;
        }

        public Criteria andCreatedByIsNull() {
            addCriterion("CreatedBy is null");
            return (Criteria) this;
        }

        public Criteria andCreatedByIsNotNull() {
            addCriterion("CreatedBy is not null");
            return (Criteria) this;
        }

        public Criteria andCreatedByEqualTo(String value) {
            addCriterion("CreatedBy =", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotEqualTo(String value) {
            addCriterion("CreatedBy <>", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByGreaterThan(String value) {
            addCriterion("CreatedBy >", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByGreaterThanOrEqualTo(String value) {
            addCriterion("CreatedBy >=", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByLessThan(String value) {
            addCriterion("CreatedBy <", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByLessThanOrEqualTo(String value) {
            addCriterion("CreatedBy <=", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByLike(String value) {
            addCriterion("CreatedBy like", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotLike(String value) {
            addCriterion("CreatedBy not like", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByIn(List<String> values) {
            addCriterion("CreatedBy in", values, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotIn(List<String> values) {
            addCriterion("CreatedBy not in", values, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByBetween(String value1, String value2) {
            addCriterion("CreatedBy between", value1, value2, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotBetween(String value1, String value2) {
            addCriterion("CreatedBy not between", value1, value2, "createdBy");
            return (Criteria) this;
        }

        public Criteria andModifiedDateIsNull() {
            addCriterion("ModifiedDate is null");
            return (Criteria) this;
        }

        public Criteria andModifiedDateIsNotNull() {
            addCriterion("ModifiedDate is not null");
            return (Criteria) this;
        }

        public Criteria andModifiedDateEqualTo(Date value) {
            addCriterion("ModifiedDate =", value, "modifiedDate");
            return (Criteria) this;
        }

        public Criteria andModifiedDateNotEqualTo(Date value) {
            addCriterion("ModifiedDate <>", value, "modifiedDate");
            return (Criteria) this;
        }

        public Criteria andModifiedDateGreaterThan(Date value) {
            addCriterion("ModifiedDate >", value, "modifiedDate");
            return (Criteria) this;
        }

        public Criteria andModifiedDateGreaterThanOrEqualTo(Date value) {
            addCriterion("ModifiedDate >=", value, "modifiedDate");
            return (Criteria) this;
        }

        public Criteria andModifiedDateLessThan(Date value) {
            addCriterion("ModifiedDate <", value, "modifiedDate");
            return (Criteria) this;
        }

        public Criteria andModifiedDateLessThanOrEqualTo(Date value) {
            addCriterion("ModifiedDate <=", value, "modifiedDate");
            return (Criteria) this;
        }

        public Criteria andModifiedDateIn(List<Date> values) {
            addCriterion("ModifiedDate in", values, "modifiedDate");
            return (Criteria) this;
        }

        public Criteria andModifiedDateNotIn(List<Date> values) {
            addCriterion("ModifiedDate not in", values, "modifiedDate");
            return (Criteria) this;
        }

        public Criteria andModifiedDateBetween(Date value1, Date value2) {
            addCriterion("ModifiedDate between", value1, value2, "modifiedDate");
            return (Criteria) this;
        }

        public Criteria andModifiedDateNotBetween(Date value1, Date value2) {
            addCriterion("ModifiedDate not between", value1, value2, "modifiedDate");
            return (Criteria) this;
        }

        public Criteria andModifiedByIsNull() {
            addCriterion("ModifiedBy is null");
            return (Criteria) this;
        }

        public Criteria andModifiedByIsNotNull() {
            addCriterion("ModifiedBy is not null");
            return (Criteria) this;
        }

        public Criteria andModifiedByEqualTo(String value) {
            addCriterion("ModifiedBy =", value, "modifiedBy");
            return (Criteria) this;
        }

        public Criteria andModifiedByNotEqualTo(String value) {
            addCriterion("ModifiedBy <>", value, "modifiedBy");
            return (Criteria) this;
        }

        public Criteria andModifiedByGreaterThan(String value) {
            addCriterion("ModifiedBy >", value, "modifiedBy");
            return (Criteria) this;
        }

        public Criteria andModifiedByGreaterThanOrEqualTo(String value) {
            addCriterion("ModifiedBy >=", value, "modifiedBy");
            return (Criteria) this;
        }

        public Criteria andModifiedByLessThan(String value) {
            addCriterion("ModifiedBy <", value, "modifiedBy");
            return (Criteria) this;
        }

        public Criteria andModifiedByLessThanOrEqualTo(String value) {
            addCriterion("ModifiedBy <=", value, "modifiedBy");
            return (Criteria) this;
        }

        public Criteria andModifiedByLike(String value) {
            addCriterion("ModifiedBy like", value, "modifiedBy");
            return (Criteria) this;
        }

        public Criteria andModifiedByNotLike(String value) {
            addCriterion("ModifiedBy not like", value, "modifiedBy");
            return (Criteria) this;
        }

        public Criteria andModifiedByIn(List<String> values) {
            addCriterion("ModifiedBy in", values, "modifiedBy");
            return (Criteria) this;
        }

        public Criteria andModifiedByNotIn(List<String> values) {
            addCriterion("ModifiedBy not in", values, "modifiedBy");
            return (Criteria) this;
        }

        public Criteria andModifiedByBetween(String value1, String value2) {
            addCriterion("ModifiedBy between", value1, value2, "modifiedBy");
            return (Criteria) this;
        }

        public Criteria andModifiedByNotBetween(String value1, String value2) {
            addCriterion("ModifiedBy not between", value1, value2, "modifiedBy");
            return (Criteria) this;
        }

        public Criteria andJobOwnerIsNull() {
            addCriterion("JobOwner is null");
            return (Criteria) this;
        }

        public Criteria andJobOwnerIsNotNull() {
            addCriterion("JobOwner is not null");
            return (Criteria) this;
        }

        public Criteria andJobOwnerEqualTo(String value) {
            addCriterion("JobOwner =", value, "jobOwner");
            return (Criteria) this;
        }

        public Criteria andJobOwnerNotEqualTo(String value) {
            addCriterion("JobOwner <>", value, "jobOwner");
            return (Criteria) this;
        }

        public Criteria andJobOwnerGreaterThan(String value) {
            addCriterion("JobOwner >", value, "jobOwner");
            return (Criteria) this;
        }

        public Criteria andJobOwnerGreaterThanOrEqualTo(String value) {
            addCriterion("JobOwner >=", value, "jobOwner");
            return (Criteria) this;
        }

        public Criteria andJobOwnerLessThan(String value) {
            addCriterion("JobOwner <", value, "jobOwner");
            return (Criteria) this;
        }

        public Criteria andJobOwnerLessThanOrEqualTo(String value) {
            addCriterion("JobOwner <=", value, "jobOwner");
            return (Criteria) this;
        }

        public Criteria andJobOwnerLike(String value) {
            addCriterion("JobOwner like", value, "jobOwner");
            return (Criteria) this;
        }

        public Criteria andJobOwnerNotLike(String value) {
            addCriterion("JobOwner not like", value, "jobOwner");
            return (Criteria) this;
        }

        public Criteria andJobOwnerIn(List<String> values) {
            addCriterion("JobOwner in", values, "jobOwner");
            return (Criteria) this;
        }

        public Criteria andJobOwnerNotIn(List<String> values) {
            addCriterion("JobOwner not in", values, "jobOwner");
            return (Criteria) this;
        }

        public Criteria andJobOwnerBetween(String value1, String value2) {
            addCriterion("JobOwner between", value1, value2, "jobOwner");
            return (Criteria) this;
        }

        public Criteria andJobOwnerNotBetween(String value1, String value2) {
            addCriterion("JobOwner not between", value1, value2, "jobOwner");
            return (Criteria) this;
        }

        public Criteria andExpectedDueDateIsNull() {
            addCriterion("ExpectedDueDate is null");
            return (Criteria) this;
        }

        public Criteria andExpectedDueDateIsNotNull() {
            addCriterion("ExpectedDueDate is not null");
            return (Criteria) this;
        }

        public Criteria andExpectedDueDateEqualTo(Date value) {
            addCriterion("ExpectedDueDate =", value, "expectedDueDate");
            return (Criteria) this;
        }

        public Criteria andExpectedDueDateNotEqualTo(Date value) {
            addCriterion("ExpectedDueDate <>", value, "expectedDueDate");
            return (Criteria) this;
        }

        public Criteria andExpectedDueDateGreaterThan(Date value) {
            addCriterion("ExpectedDueDate >", value, "expectedDueDate");
            return (Criteria) this;
        }

        public Criteria andExpectedDueDateGreaterThanOrEqualTo(Date value) {
            addCriterion("ExpectedDueDate >=", value, "expectedDueDate");
            return (Criteria) this;
        }

        public Criteria andExpectedDueDateLessThan(Date value) {
            addCriterion("ExpectedDueDate <", value, "expectedDueDate");
            return (Criteria) this;
        }

        public Criteria andExpectedDueDateLessThanOrEqualTo(Date value) {
            addCriterion("ExpectedDueDate <=", value, "expectedDueDate");
            return (Criteria) this;
        }

        public Criteria andExpectedDueDateIn(List<Date> values) {
            addCriterion("ExpectedDueDate in", values, "expectedDueDate");
            return (Criteria) this;
        }

        public Criteria andExpectedDueDateNotIn(List<Date> values) {
            addCriterion("ExpectedDueDate not in", values, "expectedDueDate");
            return (Criteria) this;
        }

        public Criteria andExpectedDueDateBetween(Date value1, Date value2) {
            addCriterion("ExpectedDueDate between", value1, value2, "expectedDueDate");
            return (Criteria) this;
        }

        public Criteria andExpectedDueDateNotBetween(Date value1, Date value2) {
            addCriterion("ExpectedDueDate not between", value1, value2, "expectedDueDate");
            return (Criteria) this;
        }


        public Criteria andLabSectionBaseIdIsNull() {
            addCriterion("LabSectionBaseId is null");
            return (Criteria) this;
        }

        public Criteria andLabSectionBaseIdIsNotNull() {
            addCriterion("LabSectionBaseId is not null");
            return (Criteria) this;
        }

        public Criteria andLabSectionBaseIdEqualTo(Long value) {
            addCriterion("LabSectionBaseId =", value, "labSectionBaseId");
            return (Criteria) this;
        }

        public Criteria andLabSectionBaseIdNotEqualTo(Long value) {
            addCriterion("LabSectionBaseId <>", value, "labSectionBaseId");
            return (Criteria) this;
        }

        public Criteria andLabSectionBaseIdGreaterThan(Long value) {
            addCriterion("LabSectionBaseId >", value, "labSectionBaseId");
            return (Criteria) this;
        }

        public Criteria andLabSectionBaseIdGreaterThanOrEqualTo(Long value) {
            addCriterion("LabSectionBaseId >=", value, "labSectionBaseId");
            return (Criteria) this;
        }

        public Criteria andLabSectionBaseIdLessThan(Long value) {
            addCriterion("LabSectionBaseId <", value, "labSectionBaseId");
            return (Criteria) this;
        }

        public Criteria andLabSectionBaseIdLessThanOrEqualTo(Long value) {
            addCriterion("LabSectionBaseId <=", value, "labSectionBaseId");
            return (Criteria) this;
        }

        public Criteria andLabSectionBaseIdIn(List<Long> values) {
            addCriterion("LabSectionBaseId in", values, "labSectionBaseId");
            return (Criteria) this;
        }

        public Criteria andLabSectionBaseIdNotIn(List<Long> values) {
            addCriterion("LabSectionBaseId not in", values, "labSectionBaseId");
            return (Criteria) this;
        }

        public Criteria andLabSectionBaseIdBetween(Long value1, Long value2) {
            addCriterion("LabSectionBaseId between", value1, value2, "labSectionBaseId");
            return (Criteria) this;
        }

        public Criteria andLabSectionBaseIdNotBetween(Long value1, Long value2) {
            addCriterion("LabSectionBaseId not between", value1, value2, "labSectionBaseId");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}