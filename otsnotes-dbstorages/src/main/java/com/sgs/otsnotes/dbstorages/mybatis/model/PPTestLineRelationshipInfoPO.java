package com.sgs.otsnotes.dbstorages.mybatis.model;

import java.util.Date;

public class PPTestLineRelationshipInfoPO {
    /**
     * ID VARCHAR(36) 必填<br>
     * ID,Primary key
     */
    private String ID;

    /**
     * GeneralOrderInstanceID VARCHAR(36)<br>
     * 
     */
    private String generalOrderInstanceID;

    /**
     * PpArtifactRelId BIGINT(19) 默认值[0] 必填<br>
     * 
     */
    private Long ppArtifactRelId;

    /**
     * TestLineInstanceID VARCHAR(36)<br>
     * 
     */
    private String testLineInstanceID;

    /**
     * PPInstanceID VARCHAR(36)<br>
     * 
     */
    private String PPInstanceID;

    /**
     * SectionID INTEGER(10)<br>
     * 
     */
    private Integer sectionID;

    /**
     * SectionName VARCHAR(200)<br>
     * 
     */
    private String sectionName;

    /**
     * SectionLevel VARCHAR(200)<br>
     * 
     */
    private String sectionLevel;

    /**
     * PPNotes VARCHAR(4000)<br>
     * 
     */
    private String PPNotes;

    /**
     * CreatedDate TIMESTAMP(19)<br>
     * CreatedDate
     */
    private Date createdDate;

    /**
     * CreatedBy VARCHAR(50)<br>
     * CreatedBy
     */
    private String createdBy;

    /**
     * ModifiedDate TIMESTAMP(19)<br>
     * ModifiedDate
     */
    private Date modifiedDate;

    /**
     * ModifiedBy VARCHAR(50)<br>
     * ModifiedBy
     */
    private String modifiedBy;

    /**
     * aid BIGINT(19) 默认值[0]<br>
     * 
     */
    private Long aid;

    /**
     * QuotationTestlineInstanceID VARCHAR(50)<br>
     * 
     */
    private String quotationTestlineInstanceID;

    /**
     * TrimsPPTestLineRelId BIGINT(19) 默认值[0]<br>
     * 
     */
    private Long trimsPPTestLineRelId;

    /**
     * PpBaseId BIGINT(19) 默认值[0]<br>
     * 
     */
    private Long ppBaseId;

    /**
     * RootPpBaseId BIGINT(19) 默认值[0]<br>
     * 
     */
    private Long rootPpBaseId;

    /**
     * SubPpRelSeq INTEGER(10) 默认值[0]<br>
     * 对应tre_trims_pp_artifact_relationship.TestLineSeq,当Add PP时如果是SubPP TL时，需要将SubPP的Seq保存进去
     */
    private Integer subPpRelSeq;

    /**
     * LastModifiedTimestamp TIMESTAMP(23) 默认值[CURRENT_TIMESTAMP(3)]<br>
     * 
     */
    private Date lastModifiedTimestamp;

    /**
     * Seq BIGINT(19)<br>
     * 
     */
    private Long seq;

    /**
     * ExtFields LONGVARCHAR(65535)<br>
     * ExtFields
     */
    private String extFields;

    private String constructionId;

    /**
     * ID VARCHAR(36) 必填<br>
     * 获得 ID,Primary key
     */
    public String getID() {
        return ID;
    }

    /**
     * ID VARCHAR(36) 必填<br>
     * 设置 ID,Primary key
     */
    public void setID(String ID) {
        this.ID = ID == null ? null : ID.trim();
    }

    /**
     * GeneralOrderInstanceID VARCHAR(36)<br>
     * 获得 
     */
    public String getGeneralOrderInstanceID() {
        return generalOrderInstanceID;
    }

    /**
     * GeneralOrderInstanceID VARCHAR(36)<br>
     * 设置 
     */
    public void setGeneralOrderInstanceID(String generalOrderInstanceID) {
        this.generalOrderInstanceID = generalOrderInstanceID == null ? null : generalOrderInstanceID.trim();
    }

    /**
     * PpArtifactRelId BIGINT(19) 默认值[0] 必填<br>
     * 获得 
     */
    public Long getPpArtifactRelId() {
        return ppArtifactRelId;
    }

    /**
     * PpArtifactRelId BIGINT(19) 默认值[0] 必填<br>
     * 设置 
     */
    public void setPpArtifactRelId(Long ppArtifactRelId) {
        this.ppArtifactRelId = ppArtifactRelId;
    }

    /**
     * TestLineInstanceID VARCHAR(36)<br>
     * 获得 
     */
    public String getTestLineInstanceID() {
        return testLineInstanceID;
    }

    /**
     * TestLineInstanceID VARCHAR(36)<br>
     * 设置 
     */
    public void setTestLineInstanceID(String testLineInstanceID) {
        this.testLineInstanceID = testLineInstanceID == null ? null : testLineInstanceID.trim();
    }

    /**
     * PPInstanceID VARCHAR(36)<br>
     * 获得 
     */
    public String getPPInstanceID() {
        return PPInstanceID;
    }

    /**
     * PPInstanceID VARCHAR(36)<br>
     * 设置 
     */
    public void setPPInstanceID(String PPInstanceID) {
        this.PPInstanceID = PPInstanceID == null ? null : PPInstanceID.trim();
    }

    /**
     * SectionID INTEGER(10)<br>
     * 获得 
     */
    public Integer getSectionID() {
        return sectionID;
    }

    /**
     * SectionID INTEGER(10)<br>
     * 设置 
     */
    public void setSectionID(Integer sectionID) {
        this.sectionID = sectionID;
    }

    /**
     * SectionName VARCHAR(200)<br>
     * 获得 
     */
    public String getSectionName() {
        return sectionName;
    }

    /**
     * SectionName VARCHAR(200)<br>
     * 设置 
     */
    public void setSectionName(String sectionName) {
        this.sectionName = sectionName == null ? null : sectionName.trim();
    }

    /**
     * SectionLevel VARCHAR(200)<br>
     * 获得 
     */
    public String getSectionLevel() {
        return sectionLevel;
    }

    /**
     * SectionLevel VARCHAR(200)<br>
     * 设置 
     */
    public void setSectionLevel(String sectionLevel) {
        this.sectionLevel = sectionLevel == null ? null : sectionLevel.trim();
    }

    /**
     * PPNotes VARCHAR(4000)<br>
     * 获得 
     */
    public String getPPNotes() {
        return PPNotes;
    }

    /**
     * PPNotes VARCHAR(4000)<br>
     * 设置 
     */
    public void setPPNotes(String PPNotes) {
        this.PPNotes = PPNotes == null ? null : PPNotes.trim();
    }

    /**
     * CreatedDate TIMESTAMP(19)<br>
     * 获得 CreatedDate
     */
    public Date getCreatedDate() {
        return createdDate;
    }

    /**
     * CreatedDate TIMESTAMP(19)<br>
     * 设置 CreatedDate
     */
    public void setCreatedDate(Date createdDate) {
        this.createdDate = createdDate;
    }

    /**
     * CreatedBy VARCHAR(50)<br>
     * 获得 CreatedBy
     */
    public String getCreatedBy() {
        return createdBy;
    }

    /**
     * CreatedBy VARCHAR(50)<br>
     * 设置 CreatedBy
     */
    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy == null ? null : createdBy.trim();
    }

    /**
     * ModifiedDate TIMESTAMP(19)<br>
     * 获得 ModifiedDate
     */
    public Date getModifiedDate() {
        return modifiedDate;
    }

    /**
     * ModifiedDate TIMESTAMP(19)<br>
     * 设置 ModifiedDate
     */
    public void setModifiedDate(Date modifiedDate) {
        this.modifiedDate = modifiedDate;
    }

    /**
     * ModifiedBy VARCHAR(50)<br>
     * 获得 ModifiedBy
     */
    public String getModifiedBy() {
        return modifiedBy;
    }

    /**
     * ModifiedBy VARCHAR(50)<br>
     * 设置 ModifiedBy
     */
    public void setModifiedBy(String modifiedBy) {
        this.modifiedBy = modifiedBy == null ? null : modifiedBy.trim();
    }

    /**
     * aid BIGINT(19) 默认值[0]<br>
     * 获得 
     */
    public Long getAid() {
        return aid;
    }

    /**
     * aid BIGINT(19) 默认值[0]<br>
     * 设置 
     */
    public void setAid(Long aid) {
        this.aid = aid;
    }

    /**
     * QuotationTestlineInstanceID VARCHAR(50)<br>
     * 获得 
     */
    public String getQuotationTestlineInstanceID() {
        return quotationTestlineInstanceID;
    }

    /**
     * QuotationTestlineInstanceID VARCHAR(50)<br>
     * 设置 
     */
    public void setQuotationTestlineInstanceID(String quotationTestlineInstanceID) {
        this.quotationTestlineInstanceID = quotationTestlineInstanceID == null ? null : quotationTestlineInstanceID.trim();
    }

    /**
     * TrimsPPTestLineRelId BIGINT(19) 默认值[0]<br>
     * 获得 
     */
    public Long getTrimsPPTestLineRelId() {
        return trimsPPTestLineRelId;
    }

    /**
     * TrimsPPTestLineRelId BIGINT(19) 默认值[0]<br>
     * 设置 
     */
    public void setTrimsPPTestLineRelId(Long trimsPPTestLineRelId) {
        this.trimsPPTestLineRelId = trimsPPTestLineRelId;
    }

    /**
     * PpBaseId BIGINT(19) 默认值[0]<br>
     * 获得 
     */
    public Long getPpBaseId() {
        return ppBaseId;
    }

    /**
     * PpBaseId BIGINT(19) 默认值[0]<br>
     * 设置 
     */
    public void setPpBaseId(Long ppBaseId) {
        this.ppBaseId = ppBaseId;
    }

    /**
     * RootPpBaseId BIGINT(19) 默认值[0]<br>
     * 获得 
     */
    public Long getRootPpBaseId() {
        return rootPpBaseId;
    }

    /**
     * RootPpBaseId BIGINT(19) 默认值[0]<br>
     * 设置 
     */
    public void setRootPpBaseId(Long rootPpBaseId) {
        this.rootPpBaseId = rootPpBaseId;
    }

    /**
     * SubPpRelSeq INTEGER(10) 默认值[0]<br>
     * 获得 对应tre_trims_pp_artifact_relationship.TestLineSeq,当Add PP时如果是SubPP TL时，需要将SubPP的Seq保存进去
     */
    public Integer getSubPpRelSeq() {
        return subPpRelSeq;
    }

    /**
     * SubPpRelSeq INTEGER(10) 默认值[0]<br>
     * 设置 对应tre_trims_pp_artifact_relationship.TestLineSeq,当Add PP时如果是SubPP TL时，需要将SubPP的Seq保存进去
     */
    public void setSubPpRelSeq(Integer subPpRelSeq) {
        this.subPpRelSeq = subPpRelSeq;
    }

    /**
     * LastModifiedTimestamp TIMESTAMP(23) 默认值[CURRENT_TIMESTAMP(3)]<br>
     * 获得 
     */
    public Date getLastModifiedTimestamp() {
        return lastModifiedTimestamp;
    }

    /**
     * LastModifiedTimestamp TIMESTAMP(23) 默认值[CURRENT_TIMESTAMP(3)]<br>
     * 设置 
     */
    public void setLastModifiedTimestamp(Date lastModifiedTimestamp) {
        this.lastModifiedTimestamp = lastModifiedTimestamp;
    }

    /**
     * Seq BIGINT(19)<br>
     * 获得 
     */
    public Long getSeq() {
        return seq;
    }

    /**
     * Seq BIGINT(19)<br>
     * 设置 
     */
    public void setSeq(Long seq) {
        this.seq = seq;
    }

    /**
     * ExtFields LONGVARCHAR(65535)<br>
     * 获得 ExtFields
     */
    public String getExtFields() {
        return extFields;
    }

    /**
     * ExtFields LONGVARCHAR(65535)<br>
     * 设置 ExtFields
     */
    public void setExtFields(String extFields) {
        this.extFields = extFields == null ? null : extFields.trim();
    }


    public String getConstructionId() {
        return constructionId;
    }

    public void setConstructionId(String constructionId) {
        this.constructionId = constructionId;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", ID=").append(ID);
        sb.append(", generalOrderInstanceID=").append(generalOrderInstanceID);
        sb.append(", ppArtifactRelId=").append(ppArtifactRelId);
        sb.append(", testLineInstanceID=").append(testLineInstanceID);
        sb.append(", PPInstanceID=").append(PPInstanceID);
        sb.append(", sectionID=").append(sectionID);
        sb.append(", sectionName=").append(sectionName);
        sb.append(", sectionLevel=").append(sectionLevel);
        sb.append(", PPNotes=").append(PPNotes);
        sb.append(", createdDate=").append(createdDate);
        sb.append(", createdBy=").append(createdBy);
        sb.append(", modifiedDate=").append(modifiedDate);
        sb.append(", modifiedBy=").append(modifiedBy);
        sb.append(", aid=").append(aid);
        sb.append(", quotationTestlineInstanceID=").append(quotationTestlineInstanceID);
        sb.append(", trimsPPTestLineRelId=").append(trimsPPTestLineRelId);
        sb.append(", ppBaseId=").append(ppBaseId);
        sb.append(", rootPpBaseId=").append(rootPpBaseId);
        sb.append(", subPpRelSeq=").append(subPpRelSeq);
        sb.append(", lastModifiedTimestamp=").append(lastModifiedTimestamp);
        sb.append(", seq=").append(seq);
        sb.append(", extFields=").append(extFields);
        sb.append(", constructionId=").append(constructionId);
        sb.append("]");
        return sb.toString();
    }
}