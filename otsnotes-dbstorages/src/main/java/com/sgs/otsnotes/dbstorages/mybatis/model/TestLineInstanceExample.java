package com.sgs.otsnotes.dbstorages.mybatis.model;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class TestLineInstanceExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public TestLineInstanceExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIDIsNull() {
            addCriterion("ID is null");
            return (Criteria) this;
        }

        public Criteria andIDIsNotNull() {
            addCriterion("ID is not null");
            return (Criteria) this;
        }

        public Criteria andIDEqualTo(String value) {
            addCriterion("ID =", value, "ID");
            return (Criteria) this;
        }

        public Criteria andIDNotEqualTo(String value) {
            addCriterion("ID <>", value, "ID");
            return (Criteria) this;
        }

        public Criteria andIDGreaterThan(String value) {
            addCriterion("ID >", value, "ID");
            return (Criteria) this;
        }

        public Criteria andIDGreaterThanOrEqualTo(String value) {
            addCriterion("ID >=", value, "ID");
            return (Criteria) this;
        }

        public Criteria andIDLessThan(String value) {
            addCriterion("ID <", value, "ID");
            return (Criteria) this;
        }

        public Criteria andIDLessThanOrEqualTo(String value) {
            addCriterion("ID <=", value, "ID");
            return (Criteria) this;
        }

        public Criteria andIDLike(String value) {
            addCriterion("ID like", value, "ID");
            return (Criteria) this;
        }

        public Criteria andIDNotLike(String value) {
            addCriterion("ID not like", value, "ID");
            return (Criteria) this;
        }

        public Criteria andIDIn(List<String> values) {
            addCriterion("ID in", values, "ID");
            return (Criteria) this;
        }

        public Criteria andIDNotIn(List<String> values) {
            addCriterion("ID not in", values, "ID");
            return (Criteria) this;
        }

        public Criteria andIDBetween(String value1, String value2) {
            addCriterion("ID between", value1, value2, "ID");
            return (Criteria) this;
        }

        public Criteria andIDNotBetween(String value1, String value2) {
            addCriterion("ID not between", value1, value2, "ID");
            return (Criteria) this;
        }

        public Criteria andGeneralOrderInstanceIDIsNull() {
            addCriterion("GeneralOrderInstanceID is null");
            return (Criteria) this;
        }

        public Criteria andGeneralOrderInstanceIDIsNotNull() {
            addCriterion("GeneralOrderInstanceID is not null");
            return (Criteria) this;
        }

        public Criteria andGeneralOrderInstanceIDEqualTo(String value) {
            addCriterion("GeneralOrderInstanceID =", value, "generalOrderInstanceID");
            return (Criteria) this;
        }

        public Criteria andGeneralOrderInstanceIDNotEqualTo(String value) {
            addCriterion("GeneralOrderInstanceID <>", value, "generalOrderInstanceID");
            return (Criteria) this;
        }

        public Criteria andGeneralOrderInstanceIDGreaterThan(String value) {
            addCriterion("GeneralOrderInstanceID >", value, "generalOrderInstanceID");
            return (Criteria) this;
        }

        public Criteria andGeneralOrderInstanceIDGreaterThanOrEqualTo(String value) {
            addCriterion("GeneralOrderInstanceID >=", value, "generalOrderInstanceID");
            return (Criteria) this;
        }

        public Criteria andGeneralOrderInstanceIDLessThan(String value) {
            addCriterion("GeneralOrderInstanceID <", value, "generalOrderInstanceID");
            return (Criteria) this;
        }

        public Criteria andGeneralOrderInstanceIDLessThanOrEqualTo(String value) {
            addCriterion("GeneralOrderInstanceID <=", value, "generalOrderInstanceID");
            return (Criteria) this;
        }

        public Criteria andGeneralOrderInstanceIDLike(String value) {
            addCriterion("GeneralOrderInstanceID like", value, "generalOrderInstanceID");
            return (Criteria) this;
        }

        public Criteria andGeneralOrderInstanceIDNotLike(String value) {
            addCriterion("GeneralOrderInstanceID not like", value, "generalOrderInstanceID");
            return (Criteria) this;
        }

        public Criteria andGeneralOrderInstanceIDIn(List<String> values) {
            addCriterion("GeneralOrderInstanceID in", values, "generalOrderInstanceID");
            return (Criteria) this;
        }

        public Criteria andGeneralOrderInstanceIDNotIn(List<String> values) {
            addCriterion("GeneralOrderInstanceID not in", values, "generalOrderInstanceID");
            return (Criteria) this;
        }

        public Criteria andGeneralOrderInstanceIDBetween(String value1, String value2) {
            addCriterion("GeneralOrderInstanceID between", value1, value2, "generalOrderInstanceID");
            return (Criteria) this;
        }

        public Criteria andGeneralOrderInstanceIDNotBetween(String value1, String value2) {
            addCriterion("GeneralOrderInstanceID not between", value1, value2, "generalOrderInstanceID");
            return (Criteria) this;
        }

        public Criteria andTestLineVersionIDIsNull() {
            addCriterion("TestLineVersionID is null");
            return (Criteria) this;
        }

        public Criteria andTestLineVersionIDIsNotNull() {
            addCriterion("TestLineVersionID is not null");
            return (Criteria) this;
        }

        public Criteria andTestLineVersionIDEqualTo(Integer value) {
            addCriterion("TestLineVersionID =", value, "testLineVersionID");
            return (Criteria) this;
        }

        public Criteria andTestLineVersionIDNotEqualTo(Integer value) {
            addCriterion("TestLineVersionID <>", value, "testLineVersionID");
            return (Criteria) this;
        }

        public Criteria andTestLineVersionIDGreaterThan(Integer value) {
            addCriterion("TestLineVersionID >", value, "testLineVersionID");
            return (Criteria) this;
        }

        public Criteria andTestLineVersionIDGreaterThanOrEqualTo(Integer value) {
            addCriterion("TestLineVersionID >=", value, "testLineVersionID");
            return (Criteria) this;
        }

        public Criteria andTestLineVersionIDLessThan(Integer value) {
            addCriterion("TestLineVersionID <", value, "testLineVersionID");
            return (Criteria) this;
        }

        public Criteria andTestLineVersionIDLessThanOrEqualTo(Integer value) {
            addCriterion("TestLineVersionID <=", value, "testLineVersionID");
            return (Criteria) this;
        }

        public Criteria andTestLineVersionIDIn(List<Integer> values) {
            addCriterion("TestLineVersionID in", values, "testLineVersionID");
            return (Criteria) this;
        }

        public Criteria andTestLineVersionIDNotIn(List<Integer> values) {
            addCriterion("TestLineVersionID not in", values, "testLineVersionID");
            return (Criteria) this;
        }

        public Criteria andTestLineVersionIDBetween(Integer value1, Integer value2) {
            addCriterion("TestLineVersionID between", value1, value2, "testLineVersionID");
            return (Criteria) this;
        }

        public Criteria andTestLineVersionIDNotBetween(Integer value1, Integer value2) {
            addCriterion("TestLineVersionID not between", value1, value2, "testLineVersionID");
            return (Criteria) this;
        }

        public Criteria andTestLineIDIsNull() {
            addCriterion("TestLineID is null");
            return (Criteria) this;
        }

        public Criteria andTestLineIDIsNotNull() {
            addCriterion("TestLineID is not null");
            return (Criteria) this;
        }

        public Criteria andTestLineIDEqualTo(Integer value) {
            addCriterion("TestLineID =", value, "testLineID");
            return (Criteria) this;
        }

        public Criteria andTestLineIDNotEqualTo(Integer value) {
            addCriterion("TestLineID <>", value, "testLineID");
            return (Criteria) this;
        }

        public Criteria andTestLineIDGreaterThan(Integer value) {
            addCriterion("TestLineID >", value, "testLineID");
            return (Criteria) this;
        }

        public Criteria andTestLineIDGreaterThanOrEqualTo(Integer value) {
            addCriterion("TestLineID >=", value, "testLineID");
            return (Criteria) this;
        }

        public Criteria andTestLineIDLessThan(Integer value) {
            addCriterion("TestLineID <", value, "testLineID");
            return (Criteria) this;
        }

        public Criteria andTestLineIDLessThanOrEqualTo(Integer value) {
            addCriterion("TestLineID <=", value, "testLineID");
            return (Criteria) this;
        }

        public Criteria andTestLineIDIn(List<Integer> values) {
            addCriterion("TestLineID in", values, "testLineID");
            return (Criteria) this;
        }

        public Criteria andTestLineIDNotIn(List<Integer> values) {
            addCriterion("TestLineID not in", values, "testLineID");
            return (Criteria) this;
        }

        public Criteria andTestLineIDBetween(Integer value1, Integer value2) {
            addCriterion("TestLineID between", value1, value2, "testLineID");
            return (Criteria) this;
        }

        public Criteria andTestLineIDNotBetween(Integer value1, Integer value2) {
            addCriterion("TestLineID not between", value1, value2, "testLineID");
            return (Criteria) this;
        }

        public Criteria andTestLineEvaluationIsNull() {
            addCriterion("TestLineEvaluation is null");
            return (Criteria) this;
        }

        public Criteria andTestLineEvaluationIsNotNull() {
            addCriterion("TestLineEvaluation is not null");
            return (Criteria) this;
        }

        public Criteria andTestLineEvaluationEqualTo(String value) {
            addCriterion("TestLineEvaluation =", value, "testLineEvaluation");
            return (Criteria) this;
        }

        public Criteria andTestLineEvaluationNotEqualTo(String value) {
            addCriterion("TestLineEvaluation <>", value, "testLineEvaluation");
            return (Criteria) this;
        }

        public Criteria andTestLineEvaluationGreaterThan(String value) {
            addCriterion("TestLineEvaluation >", value, "testLineEvaluation");
            return (Criteria) this;
        }

        public Criteria andTestLineEvaluationGreaterThanOrEqualTo(String value) {
            addCriterion("TestLineEvaluation >=", value, "testLineEvaluation");
            return (Criteria) this;
        }

        public Criteria andTestLineEvaluationLessThan(String value) {
            addCriterion("TestLineEvaluation <", value, "testLineEvaluation");
            return (Criteria) this;
        }

        public Criteria andTestLineEvaluationLessThanOrEqualTo(String value) {
            addCriterion("TestLineEvaluation <=", value, "testLineEvaluation");
            return (Criteria) this;
        }

        public Criteria andTestLineEvaluationLike(String value) {
            addCriterion("TestLineEvaluation like", value, "testLineEvaluation");
            return (Criteria) this;
        }

        public Criteria andTestLineEvaluationNotLike(String value) {
            addCriterion("TestLineEvaluation not like", value, "testLineEvaluation");
            return (Criteria) this;
        }

        public Criteria andTestLineEvaluationIn(List<String> values) {
            addCriterion("TestLineEvaluation in", values, "testLineEvaluation");
            return (Criteria) this;
        }

        public Criteria andTestLineEvaluationNotIn(List<String> values) {
            addCriterion("TestLineEvaluation not in", values, "testLineEvaluation");
            return (Criteria) this;
        }

        public Criteria andTestLineEvaluationBetween(String value1, String value2) {
            addCriterion("TestLineEvaluation between", value1, value2, "testLineEvaluation");
            return (Criteria) this;
        }

        public Criteria andTestLineEvaluationNotBetween(String value1, String value2) {
            addCriterion("TestLineEvaluation not between", value1, value2, "testLineEvaluation");
            return (Criteria) this;
        }

        public Criteria andCitationTypeIDIsNull() {
            addCriterion("CitationTypeID is null");
            return (Criteria) this;
        }

        public Criteria andCitationTypeIDIsNotNull() {
            addCriterion("CitationTypeID is not null");
            return (Criteria) this;
        }

        public Criteria andCitationTypeIDEqualTo(Integer value) {
            addCriterion("CitationTypeID =", value, "citationTypeID");
            return (Criteria) this;
        }

        public Criteria andCitationTypeIDNotEqualTo(Integer value) {
            addCriterion("CitationTypeID <>", value, "citationTypeID");
            return (Criteria) this;
        }

        public Criteria andCitationTypeIDGreaterThan(Integer value) {
            addCriterion("CitationTypeID >", value, "citationTypeID");
            return (Criteria) this;
        }

        public Criteria andCitationTypeIDGreaterThanOrEqualTo(Integer value) {
            addCriterion("CitationTypeID >=", value, "citationTypeID");
            return (Criteria) this;
        }

        public Criteria andCitationTypeIDLessThan(Integer value) {
            addCriterion("CitationTypeID <", value, "citationTypeID");
            return (Criteria) this;
        }

        public Criteria andCitationTypeIDLessThanOrEqualTo(Integer value) {
            addCriterion("CitationTypeID <=", value, "citationTypeID");
            return (Criteria) this;
        }

        public Criteria andCitationTypeIDIn(List<Integer> values) {
            addCriterion("CitationTypeID in", values, "citationTypeID");
            return (Criteria) this;
        }

        public Criteria andCitationTypeIDNotIn(List<Integer> values) {
            addCriterion("CitationTypeID not in", values, "citationTypeID");
            return (Criteria) this;
        }

        public Criteria andCitationTypeIDBetween(Integer value1, Integer value2) {
            addCriterion("CitationTypeID between", value1, value2, "citationTypeID");
            return (Criteria) this;
        }

        public Criteria andCitationTypeIDNotBetween(Integer value1, Integer value2) {
            addCriterion("CitationTypeID not between", value1, value2, "citationTypeID");
            return (Criteria) this;
        }

        public Criteria andEvaluationAliasIsNull() {
            addCriterion("EvaluationAlias is null");
            return (Criteria) this;
        }

        public Criteria andEvaluationAliasIsNotNull() {
            addCriterion("EvaluationAlias is not null");
            return (Criteria) this;
        }

        public Criteria andEvaluationAliasEqualTo(String value) {
            addCriterion("EvaluationAlias =", value, "evaluationAlias");
            return (Criteria) this;
        }

        public Criteria andEvaluationAliasNotEqualTo(String value) {
            addCriterion("EvaluationAlias <>", value, "evaluationAlias");
            return (Criteria) this;
        }

        public Criteria andEvaluationAliasGreaterThan(String value) {
            addCriterion("EvaluationAlias >", value, "evaluationAlias");
            return (Criteria) this;
        }

        public Criteria andEvaluationAliasGreaterThanOrEqualTo(String value) {
            addCriterion("EvaluationAlias >=", value, "evaluationAlias");
            return (Criteria) this;
        }

        public Criteria andEvaluationAliasLessThan(String value) {
            addCriterion("EvaluationAlias <", value, "evaluationAlias");
            return (Criteria) this;
        }

        public Criteria andEvaluationAliasLessThanOrEqualTo(String value) {
            addCriterion("EvaluationAlias <=", value, "evaluationAlias");
            return (Criteria) this;
        }

        public Criteria andEvaluationAliasLike(String value) {
            addCriterion("EvaluationAlias like", value, "evaluationAlias");
            return (Criteria) this;
        }

        public Criteria andEvaluationAliasNotLike(String value) {
            addCriterion("EvaluationAlias not like", value, "evaluationAlias");
            return (Criteria) this;
        }

        public Criteria andEvaluationAliasIn(List<String> values) {
            addCriterion("EvaluationAlias in", values, "evaluationAlias");
            return (Criteria) this;
        }

        public Criteria andEvaluationAliasNotIn(List<String> values) {
            addCriterion("EvaluationAlias not in", values, "evaluationAlias");
            return (Criteria) this;
        }

        public Criteria andEvaluationAliasBetween(String value1, String value2) {
            addCriterion("EvaluationAlias between", value1, value2, "evaluationAlias");
            return (Criteria) this;
        }

        public Criteria andEvaluationAliasNotBetween(String value1, String value2) {
            addCriterion("EvaluationAlias not between", value1, value2, "evaluationAlias");
            return (Criteria) this;
        }

        public Criteria andStandardIDIsNull() {
            addCriterion("StandardID is null");
            return (Criteria) this;
        }

        public Criteria andStandardIDIsNotNull() {
            addCriterion("StandardID is not null");
            return (Criteria) this;
        }

        public Criteria andStandardIDEqualTo(Integer value) {
            addCriterion("StandardID =", value, "standardID");
            return (Criteria) this;
        }

        public Criteria andStandardIDNotEqualTo(Integer value) {
            addCriterion("StandardID <>", value, "standardID");
            return (Criteria) this;
        }

        public Criteria andStandardIDGreaterThan(Integer value) {
            addCriterion("StandardID >", value, "standardID");
            return (Criteria) this;
        }

        public Criteria andStandardIDGreaterThanOrEqualTo(Integer value) {
            addCriterion("StandardID >=", value, "standardID");
            return (Criteria) this;
        }

        public Criteria andStandardIDLessThan(Integer value) {
            addCriterion("StandardID <", value, "standardID");
            return (Criteria) this;
        }

        public Criteria andStandardIDLessThanOrEqualTo(Integer value) {
            addCriterion("StandardID <=", value, "standardID");
            return (Criteria) this;
        }

        public Criteria andStandardIDIn(List<Integer> values) {
            addCriterion("StandardID in", values, "standardID");
            return (Criteria) this;
        }

        public Criteria andStandardIDNotIn(List<Integer> values) {
            addCriterion("StandardID not in", values, "standardID");
            return (Criteria) this;
        }

        public Criteria andStandardIDBetween(Integer value1, Integer value2) {
            addCriterion("StandardID between", value1, value2, "standardID");
            return (Criteria) this;
        }

        public Criteria andStandardIDNotBetween(Integer value1, Integer value2) {
            addCriterion("StandardID not between", value1, value2, "standardID");
            return (Criteria) this;
        }

        public Criteria andStandardVersionIDIsNull() {
            addCriterion("StandardVersionID is null");
            return (Criteria) this;
        }

        public Criteria andStandardVersionIDIsNotNull() {
            addCriterion("StandardVersionID is not null");
            return (Criteria) this;
        }

        public Criteria andStandardVersionIDEqualTo(Integer value) {
            addCriterion("StandardVersionID =", value, "standardVersionID");
            return (Criteria) this;
        }

        public Criteria andStandardVersionIDNotEqualTo(Integer value) {
            addCriterion("StandardVersionID <>", value, "standardVersionID");
            return (Criteria) this;
        }

        public Criteria andStandardVersionIDGreaterThan(Integer value) {
            addCriterion("StandardVersionID >", value, "standardVersionID");
            return (Criteria) this;
        }

        public Criteria andStandardVersionIDGreaterThanOrEqualTo(Integer value) {
            addCriterion("StandardVersionID >=", value, "standardVersionID");
            return (Criteria) this;
        }

        public Criteria andStandardVersionIDLessThan(Integer value) {
            addCriterion("StandardVersionID <", value, "standardVersionID");
            return (Criteria) this;
        }

        public Criteria andStandardVersionIDLessThanOrEqualTo(Integer value) {
            addCriterion("StandardVersionID <=", value, "standardVersionID");
            return (Criteria) this;
        }

        public Criteria andStandardVersionIDIn(List<Integer> values) {
            addCriterion("StandardVersionID in", values, "standardVersionID");
            return (Criteria) this;
        }

        public Criteria andStandardVersionIDNotIn(List<Integer> values) {
            addCriterion("StandardVersionID not in", values, "standardVersionID");
            return (Criteria) this;
        }

        public Criteria andStandardVersionIDBetween(Integer value1, Integer value2) {
            addCriterion("StandardVersionID between", value1, value2, "standardVersionID");
            return (Criteria) this;
        }

        public Criteria andStandardVersionIDNotBetween(Integer value1, Integer value2) {
            addCriterion("StandardVersionID not between", value1, value2, "standardVersionID");
            return (Criteria) this;
        }

        public Criteria andStandardNameIsNull() {
            addCriterion("StandardName is null");
            return (Criteria) this;
        }

        public Criteria andStandardNameIsNotNull() {
            addCriterion("StandardName is not null");
            return (Criteria) this;
        }

        public Criteria andStandardNameEqualTo(String value) {
            addCriterion("StandardName =", value, "standardName");
            return (Criteria) this;
        }

        public Criteria andStandardNameNotEqualTo(String value) {
            addCriterion("StandardName <>", value, "standardName");
            return (Criteria) this;
        }

        public Criteria andStandardNameGreaterThan(String value) {
            addCriterion("StandardName >", value, "standardName");
            return (Criteria) this;
        }

        public Criteria andStandardNameGreaterThanOrEqualTo(String value) {
            addCriterion("StandardName >=", value, "standardName");
            return (Criteria) this;
        }

        public Criteria andStandardNameLessThan(String value) {
            addCriterion("StandardName <", value, "standardName");
            return (Criteria) this;
        }

        public Criteria andStandardNameLessThanOrEqualTo(String value) {
            addCriterion("StandardName <=", value, "standardName");
            return (Criteria) this;
        }

        public Criteria andStandardNameLike(String value) {
            addCriterion("StandardName like", value, "standardName");
            return (Criteria) this;
        }

        public Criteria andStandardNameNotLike(String value) {
            addCriterion("StandardName not like", value, "standardName");
            return (Criteria) this;
        }

        public Criteria andStandardNameIn(List<String> values) {
            addCriterion("StandardName in", values, "standardName");
            return (Criteria) this;
        }

        public Criteria andStandardNameNotIn(List<String> values) {
            addCriterion("StandardName not in", values, "standardName");
            return (Criteria) this;
        }

        public Criteria andStandardNameBetween(String value1, String value2) {
            addCriterion("StandardName between", value1, value2, "standardName");
            return (Criteria) this;
        }

        public Criteria andStandardNameNotBetween(String value1, String value2) {
            addCriterion("StandardName not between", value1, value2, "standardName");
            return (Criteria) this;
        }

        public Criteria andStandardSectionIDIsNull() {
            addCriterion("StandardSectionID is null");
            return (Criteria) this;
        }

        public Criteria andStandardSectionIDIsNotNull() {
            addCriterion("StandardSectionID is not null");
            return (Criteria) this;
        }

        public Criteria andStandardSectionIDEqualTo(Integer value) {
            addCriterion("StandardSectionID =", value, "standardSectionID");
            return (Criteria) this;
        }

        public Criteria andStandardSectionIDNotEqualTo(Integer value) {
            addCriterion("StandardSectionID <>", value, "standardSectionID");
            return (Criteria) this;
        }

        public Criteria andStandardSectionIDGreaterThan(Integer value) {
            addCriterion("StandardSectionID >", value, "standardSectionID");
            return (Criteria) this;
        }

        public Criteria andStandardSectionIDGreaterThanOrEqualTo(Integer value) {
            addCriterion("StandardSectionID >=", value, "standardSectionID");
            return (Criteria) this;
        }

        public Criteria andStandardSectionIDLessThan(Integer value) {
            addCriterion("StandardSectionID <", value, "standardSectionID");
            return (Criteria) this;
        }

        public Criteria andStandardSectionIDLessThanOrEqualTo(Integer value) {
            addCriterion("StandardSectionID <=", value, "standardSectionID");
            return (Criteria) this;
        }

        public Criteria andStandardSectionIDIn(List<Integer> values) {
            addCriterion("StandardSectionID in", values, "standardSectionID");
            return (Criteria) this;
        }

        public Criteria andStandardSectionIDNotIn(List<Integer> values) {
            addCriterion("StandardSectionID not in", values, "standardSectionID");
            return (Criteria) this;
        }

        public Criteria andStandardSectionIDBetween(Integer value1, Integer value2) {
            addCriterion("StandardSectionID between", value1, value2, "standardSectionID");
            return (Criteria) this;
        }

        public Criteria andStandardSectionIDNotBetween(Integer value1, Integer value2) {
            addCriterion("StandardSectionID not between", value1, value2, "standardSectionID");
            return (Criteria) this;
        }

        public Criteria andStandardSectionNameIsNull() {
            addCriterion("StandardSectionName is null");
            return (Criteria) this;
        }

        public Criteria andStandardSectionNameIsNotNull() {
            addCriterion("StandardSectionName is not null");
            return (Criteria) this;
        }

        public Criteria andStandardSectionNameEqualTo(String value) {
            addCriterion("StandardSectionName =", value, "standardSectionName");
            return (Criteria) this;
        }

        public Criteria andStandardSectionNameNotEqualTo(String value) {
            addCriterion("StandardSectionName <>", value, "standardSectionName");
            return (Criteria) this;
        }

        public Criteria andStandardSectionNameGreaterThan(String value) {
            addCriterion("StandardSectionName >", value, "standardSectionName");
            return (Criteria) this;
        }

        public Criteria andStandardSectionNameGreaterThanOrEqualTo(String value) {
            addCriterion("StandardSectionName >=", value, "standardSectionName");
            return (Criteria) this;
        }

        public Criteria andStandardSectionNameLessThan(String value) {
            addCriterion("StandardSectionName <", value, "standardSectionName");
            return (Criteria) this;
        }

        public Criteria andStandardSectionNameLessThanOrEqualTo(String value) {
            addCriterion("StandardSectionName <=", value, "standardSectionName");
            return (Criteria) this;
        }

        public Criteria andStandardSectionNameLike(String value) {
            addCriterion("StandardSectionName like", value, "standardSectionName");
            return (Criteria) this;
        }

        public Criteria andStandardSectionNameNotLike(String value) {
            addCriterion("StandardSectionName not like", value, "standardSectionName");
            return (Criteria) this;
        }

        public Criteria andStandardSectionNameIn(List<String> values) {
            addCriterion("StandardSectionName in", values, "standardSectionName");
            return (Criteria) this;
        }

        public Criteria andStandardSectionNameNotIn(List<String> values) {
            addCriterion("StandardSectionName not in", values, "standardSectionName");
            return (Criteria) this;
        }

        public Criteria andStandardSectionNameBetween(String value1, String value2) {
            addCriterion("StandardSectionName between", value1, value2, "standardSectionName");
            return (Criteria) this;
        }

        public Criteria andStandardSectionNameNotBetween(String value1, String value2) {
            addCriterion("StandardSectionName not between", value1, value2, "standardSectionName");
            return (Criteria) this;
        }

        public Criteria andAutoConclusionIsNull() {
            addCriterion("AutoConclusion is null");
            return (Criteria) this;
        }

        public Criteria andAutoConclusionIsNotNull() {
            addCriterion("AutoConclusion is not null");
            return (Criteria) this;
        }

        public Criteria andAutoConclusionEqualTo(Boolean value) {
            addCriterion("AutoConclusion =", value, "autoConclusion");
            return (Criteria) this;
        }

        public Criteria andAutoConclusionNotEqualTo(Boolean value) {
            addCriterion("AutoConclusion <>", value, "autoConclusion");
            return (Criteria) this;
        }

        public Criteria andAutoConclusionGreaterThan(Boolean value) {
            addCriterion("AutoConclusion >", value, "autoConclusion");
            return (Criteria) this;
        }

        public Criteria andAutoConclusionGreaterThanOrEqualTo(Boolean value) {
            addCriterion("AutoConclusion >=", value, "autoConclusion");
            return (Criteria) this;
        }

        public Criteria andAutoConclusionLessThan(Boolean value) {
            addCriterion("AutoConclusion <", value, "autoConclusion");
            return (Criteria) this;
        }

        public Criteria andAutoConclusionLessThanOrEqualTo(Boolean value) {
            addCriterion("AutoConclusion <=", value, "autoConclusion");
            return (Criteria) this;
        }

        public Criteria andAutoConclusionIn(List<Boolean> values) {
            addCriterion("AutoConclusion in", values, "autoConclusion");
            return (Criteria) this;
        }

        public Criteria andAutoConclusionNotIn(List<Boolean> values) {
            addCriterion("AutoConclusion not in", values, "autoConclusion");
            return (Criteria) this;
        }

        public Criteria andAutoConclusionBetween(Boolean value1, Boolean value2) {
            addCriterion("AutoConclusion between", value1, value2, "autoConclusion");
            return (Criteria) this;
        }

        public Criteria andAutoConclusionNotBetween(Boolean value1, Boolean value2) {
            addCriterion("AutoConclusion not between", value1, value2, "autoConclusion");
            return (Criteria) this;
        }

        public Criteria andTestLineStatusIsNull() {
            addCriterion("TestLineStatus is null");
            return (Criteria) this;
        }

        public Criteria andTestLineStatusIsNotNull() {
            addCriterion("TestLineStatus is not null");
            return (Criteria) this;
        }

        public Criteria andTestLineStatusEqualTo(Integer value) {
            addCriterion("TestLineStatus =", value, "testLineStatus");
            return (Criteria) this;
        }

        public Criteria andTestLineStatusNotEqualTo(Integer value) {
            addCriterion("TestLineStatus <>", value, "testLineStatus");
            return (Criteria) this;
        }

        public Criteria andTestLineStatusGreaterThan(Integer value) {
            addCriterion("TestLineStatus >", value, "testLineStatus");
            return (Criteria) this;
        }

        public Criteria andTestLineStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("TestLineStatus >=", value, "testLineStatus");
            return (Criteria) this;
        }

        public Criteria andTestLineStatusLessThan(Integer value) {
            addCriterion("TestLineStatus <", value, "testLineStatus");
            return (Criteria) this;
        }

        public Criteria andTestLineStatusLessThanOrEqualTo(Integer value) {
            addCriterion("TestLineStatus <=", value, "testLineStatus");
            return (Criteria) this;
        }

        public Criteria andTestLineStatusIn(List<Integer> values) {
            addCriterion("TestLineStatus in", values, "testLineStatus");
            return (Criteria) this;
        }

        public Criteria andTestLineStatusNotIn(List<Integer> values) {
            addCriterion("TestLineStatus not in", values, "testLineStatus");
            return (Criteria) this;
        }

        public Criteria andTestLineStatusBetween(Integer value1, Integer value2) {
            addCriterion("TestLineStatus between", value1, value2, "testLineStatus");
            return (Criteria) this;
        }

        public Criteria andTestLineStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("TestLineStatus not between", value1, value2, "testLineStatus");
            return (Criteria) this;
        }

        public Criteria andConditionStatusIsNull() {
            addCriterion("ConditionStatus is null");
            return (Criteria) this;
        }

        public Criteria andConditionStatusIsNotNull() {
            addCriterion("ConditionStatus is not null");
            return (Criteria) this;
        }

        public Criteria andConditionStatusEqualTo(Integer value) {
            addCriterion("ConditionStatus =", value, "conditionStatus");
            return (Criteria) this;
        }

        public Criteria andConditionStatusNotEqualTo(Integer value) {
            addCriterion("ConditionStatus <>", value, "conditionStatus");
            return (Criteria) this;
        }

        public Criteria andConditionStatusGreaterThan(Integer value) {
            addCriterion("ConditionStatus >", value, "conditionStatus");
            return (Criteria) this;
        }

        public Criteria andConditionStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("ConditionStatus >=", value, "conditionStatus");
            return (Criteria) this;
        }

        public Criteria andConditionStatusLessThan(Integer value) {
            addCriterion("ConditionStatus <", value, "conditionStatus");
            return (Criteria) this;
        }

        public Criteria andConditionStatusLessThanOrEqualTo(Integer value) {
            addCriterion("ConditionStatus <=", value, "conditionStatus");
            return (Criteria) this;
        }

        public Criteria andConditionStatusIn(List<Integer> values) {
            addCriterion("ConditionStatus in", values, "conditionStatus");
            return (Criteria) this;
        }

        public Criteria andConditionStatusNotIn(List<Integer> values) {
            addCriterion("ConditionStatus not in", values, "conditionStatus");
            return (Criteria) this;
        }

        public Criteria andConditionStatusBetween(Integer value1, Integer value2) {
            addCriterion("ConditionStatus between", value1, value2, "conditionStatus");
            return (Criteria) this;
        }

        public Criteria andConditionStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("ConditionStatus not between", value1, value2, "conditionStatus");
            return (Criteria) this;
        }

        public Criteria andRecommendedFibreLabelIsNull() {
            addCriterion("RecommendedFibreLabel is null");
            return (Criteria) this;
        }

        public Criteria andRecommendedFibreLabelIsNotNull() {
            addCriterion("RecommendedFibreLabel is not null");
            return (Criteria) this;
        }

        public Criteria andRecommendedFibreLabelEqualTo(String value) {
            addCriterion("RecommendedFibreLabel =", value, "recommendedFibreLabel");
            return (Criteria) this;
        }

        public Criteria andRecommendedFibreLabelNotEqualTo(String value) {
            addCriterion("RecommendedFibreLabel <>", value, "recommendedFibreLabel");
            return (Criteria) this;
        }

        public Criteria andRecommendedFibreLabelGreaterThan(String value) {
            addCriterion("RecommendedFibreLabel >", value, "recommendedFibreLabel");
            return (Criteria) this;
        }

        public Criteria andRecommendedFibreLabelGreaterThanOrEqualTo(String value) {
            addCriterion("RecommendedFibreLabel >=", value, "recommendedFibreLabel");
            return (Criteria) this;
        }

        public Criteria andRecommendedFibreLabelLessThan(String value) {
            addCriterion("RecommendedFibreLabel <", value, "recommendedFibreLabel");
            return (Criteria) this;
        }

        public Criteria andRecommendedFibreLabelLessThanOrEqualTo(String value) {
            addCriterion("RecommendedFibreLabel <=", value, "recommendedFibreLabel");
            return (Criteria) this;
        }

        public Criteria andRecommendedFibreLabelLike(String value) {
            addCriterion("RecommendedFibreLabel like", value, "recommendedFibreLabel");
            return (Criteria) this;
        }

        public Criteria andRecommendedFibreLabelNotLike(String value) {
            addCriterion("RecommendedFibreLabel not like", value, "recommendedFibreLabel");
            return (Criteria) this;
        }

        public Criteria andRecommendedFibreLabelIn(List<String> values) {
            addCriterion("RecommendedFibreLabel in", values, "recommendedFibreLabel");
            return (Criteria) this;
        }

        public Criteria andRecommendedFibreLabelNotIn(List<String> values) {
            addCriterion("RecommendedFibreLabel not in", values, "recommendedFibreLabel");
            return (Criteria) this;
        }

        public Criteria andRecommendedFibreLabelBetween(String value1, String value2) {
            addCriterion("RecommendedFibreLabel between", value1, value2, "recommendedFibreLabel");
            return (Criteria) this;
        }

        public Criteria andRecommendedFibreLabelNotBetween(String value1, String value2) {
            addCriterion("RecommendedFibreLabel not between", value1, value2, "recommendedFibreLabel");
            return (Criteria) this;
        }

        public Criteria andTestLineSeqIsNull() {
            addCriterion("TestLineSeq is null");
            return (Criteria) this;
        }

        public Criteria andTestLineSeqIsNotNull() {
            addCriterion("TestLineSeq is not null");
            return (Criteria) this;
        }

        public Criteria andTestLineSeqEqualTo(Integer value) {
            addCriterion("TestLineSeq =", value, "testLineSeq");
            return (Criteria) this;
        }

        public Criteria andTestLineSeqNotEqualTo(Integer value) {
            addCriterion("TestLineSeq <>", value, "testLineSeq");
            return (Criteria) this;
        }

        public Criteria andTestLineSeqGreaterThan(Integer value) {
            addCriterion("TestLineSeq >", value, "testLineSeq");
            return (Criteria) this;
        }

        public Criteria andTestLineSeqGreaterThanOrEqualTo(Integer value) {
            addCriterion("TestLineSeq >=", value, "testLineSeq");
            return (Criteria) this;
        }

        public Criteria andTestLineSeqLessThan(Integer value) {
            addCriterion("TestLineSeq <", value, "testLineSeq");
            return (Criteria) this;
        }

        public Criteria andTestLineSeqLessThanOrEqualTo(Integer value) {
            addCriterion("TestLineSeq <=", value, "testLineSeq");
            return (Criteria) this;
        }

        public Criteria andTestLineSeqIn(List<Integer> values) {
            addCriterion("TestLineSeq in", values, "testLineSeq");
            return (Criteria) this;
        }

        public Criteria andTestLineSeqNotIn(List<Integer> values) {
            addCriterion("TestLineSeq not in", values, "testLineSeq");
            return (Criteria) this;
        }

        public Criteria andTestLineSeqBetween(Integer value1, Integer value2) {
            addCriterion("TestLineSeq between", value1, value2, "testLineSeq");
            return (Criteria) this;
        }

        public Criteria andTestLineSeqNotBetween(Integer value1, Integer value2) {
            addCriterion("TestLineSeq not between", value1, value2, "testLineSeq");
            return (Criteria) this;
        }

        public Criteria andCuttingRequestIsNull() {
            addCriterion("CuttingRequest is null");
            return (Criteria) this;
        }

        public Criteria andCuttingRequestIsNotNull() {
            addCriterion("CuttingRequest is not null");
            return (Criteria) this;
        }

        public Criteria andCuttingRequestEqualTo(String value) {
            addCriterion("CuttingRequest =", value, "cuttingRequest");
            return (Criteria) this;
        }

        public Criteria andCuttingRequestNotEqualTo(String value) {
            addCriterion("CuttingRequest <>", value, "cuttingRequest");
            return (Criteria) this;
        }

        public Criteria andCuttingRequestGreaterThan(String value) {
            addCriterion("CuttingRequest >", value, "cuttingRequest");
            return (Criteria) this;
        }

        public Criteria andCuttingRequestGreaterThanOrEqualTo(String value) {
            addCriterion("CuttingRequest >=", value, "cuttingRequest");
            return (Criteria) this;
        }

        public Criteria andCuttingRequestLessThan(String value) {
            addCriterion("CuttingRequest <", value, "cuttingRequest");
            return (Criteria) this;
        }

        public Criteria andCuttingRequestLessThanOrEqualTo(String value) {
            addCriterion("CuttingRequest <=", value, "cuttingRequest");
            return (Criteria) this;
        }

        public Criteria andCuttingRequestLike(String value) {
            addCriterion("CuttingRequest like", value, "cuttingRequest");
            return (Criteria) this;
        }

        public Criteria andCuttingRequestNotLike(String value) {
            addCriterion("CuttingRequest not like", value, "cuttingRequest");
            return (Criteria) this;
        }

        public Criteria andCuttingRequestIn(List<String> values) {
            addCriterion("CuttingRequest in", values, "cuttingRequest");
            return (Criteria) this;
        }

        public Criteria andCuttingRequestNotIn(List<String> values) {
            addCriterion("CuttingRequest not in", values, "cuttingRequest");
            return (Criteria) this;
        }

        public Criteria andCuttingRequestBetween(String value1, String value2) {
            addCriterion("CuttingRequest between", value1, value2, "cuttingRequest");
            return (Criteria) this;
        }

        public Criteria andCuttingRequestNotBetween(String value1, String value2) {
            addCriterion("CuttingRequest not between", value1, value2, "cuttingRequest");
            return (Criteria) this;
        }

        public Criteria andIsPretreatmentIsNull() {
            addCriterion("IsPretreatment is null");
            return (Criteria) this;
        }

        public Criteria andIsPretreatmentIsNotNull() {
            addCriterion("IsPretreatment is not null");
            return (Criteria) this;
        }

        public Criteria andIsPretreatmentEqualTo(Boolean value) {
            addCriterion("IsPretreatment =", value, "isPretreatment");
            return (Criteria) this;
        }

        public Criteria andIsPretreatmentNotEqualTo(Boolean value) {
            addCriterion("IsPretreatment <>", value, "isPretreatment");
            return (Criteria) this;
        }

        public Criteria andIsPretreatmentGreaterThan(Boolean value) {
            addCriterion("IsPretreatment >", value, "isPretreatment");
            return (Criteria) this;
        }

        public Criteria andIsPretreatmentGreaterThanOrEqualTo(Boolean value) {
            addCriterion("IsPretreatment >=", value, "isPretreatment");
            return (Criteria) this;
        }

        public Criteria andIsPretreatmentLessThan(Boolean value) {
            addCriterion("IsPretreatment <", value, "isPretreatment");
            return (Criteria) this;
        }

        public Criteria andIsPretreatmentLessThanOrEqualTo(Boolean value) {
            addCriterion("IsPretreatment <=", value, "isPretreatment");
            return (Criteria) this;
        }

        public Criteria andIsPretreatmentIn(List<Boolean> values) {
            addCriterion("IsPretreatment in", values, "isPretreatment");
            return (Criteria) this;
        }

        public Criteria andIsPretreatmentNotIn(List<Boolean> values) {
            addCriterion("IsPretreatment not in", values, "isPretreatment");
            return (Criteria) this;
        }

        public Criteria andIsPretreatmentBetween(Boolean value1, Boolean value2) {
            addCriterion("IsPretreatment between", value1, value2, "isPretreatment");
            return (Criteria) this;
        }

        public Criteria andIsPretreatmentNotBetween(Boolean value1, Boolean value2) {
            addCriterion("IsPretreatment not between", value1, value2, "isPretreatment");
            return (Criteria) this;
        }

        public Criteria andOrdertestLineRemarkIsNull() {
            addCriterion("OrdertestLineRemark is null");
            return (Criteria) this;
        }

        public Criteria andOrdertestLineRemarkIsNotNull() {
            addCriterion("OrdertestLineRemark is not null");
            return (Criteria) this;
        }

        public Criteria andOrdertestLineRemarkEqualTo(String value) {
            addCriterion("OrdertestLineRemark =", value, "ordertestLineRemark");
            return (Criteria) this;
        }

        public Criteria andOrdertestLineRemarkNotEqualTo(String value) {
            addCriterion("OrdertestLineRemark <>", value, "ordertestLineRemark");
            return (Criteria) this;
        }

        public Criteria andOrdertestLineRemarkGreaterThan(String value) {
            addCriterion("OrdertestLineRemark >", value, "ordertestLineRemark");
            return (Criteria) this;
        }

        public Criteria andOrdertestLineRemarkGreaterThanOrEqualTo(String value) {
            addCriterion("OrdertestLineRemark >=", value, "ordertestLineRemark");
            return (Criteria) this;
        }

        public Criteria andOrdertestLineRemarkLessThan(String value) {
            addCriterion("OrdertestLineRemark <", value, "ordertestLineRemark");
            return (Criteria) this;
        }

        public Criteria andOrdertestLineRemarkLessThanOrEqualTo(String value) {
            addCriterion("OrdertestLineRemark <=", value, "ordertestLineRemark");
            return (Criteria) this;
        }

        public Criteria andOrdertestLineRemarkLike(String value) {
            addCriterion("OrdertestLineRemark like", value, "ordertestLineRemark");
            return (Criteria) this;
        }

        public Criteria andOrdertestLineRemarkNotLike(String value) {
            addCriterion("OrdertestLineRemark not like", value, "ordertestLineRemark");
            return (Criteria) this;
        }

        public Criteria andOrdertestLineRemarkIn(List<String> values) {
            addCriterion("OrdertestLineRemark in", values, "ordertestLineRemark");
            return (Criteria) this;
        }

        public Criteria andOrdertestLineRemarkNotIn(List<String> values) {
            addCriterion("OrdertestLineRemark not in", values, "ordertestLineRemark");
            return (Criteria) this;
        }

        public Criteria andOrdertestLineRemarkBetween(String value1, String value2) {
            addCriterion("OrdertestLineRemark between", value1, value2, "ordertestLineRemark");
            return (Criteria) this;
        }

        public Criteria andOrdertestLineRemarkNotBetween(String value1, String value2) {
            addCriterion("OrdertestLineRemark not between", value1, value2, "ordertestLineRemark");
            return (Criteria) this;
        }

        public Criteria andActiveIndicatorIsNull() {
            addCriterion("ActiveIndicator is null");
            return (Criteria) this;
        }

        public Criteria andActiveIndicatorIsNotNull() {
            addCriterion("ActiveIndicator is not null");
            return (Criteria) this;
        }

        public Criteria andActiveIndicatorEqualTo(Boolean value) {
            addCriterion("ActiveIndicator =", value, "activeIndicator");
            return (Criteria) this;
        }

        public Criteria andActiveIndicatorNotEqualTo(Boolean value) {
            addCriterion("ActiveIndicator <>", value, "activeIndicator");
            return (Criteria) this;
        }

        public Criteria andActiveIndicatorGreaterThan(Boolean value) {
            addCriterion("ActiveIndicator >", value, "activeIndicator");
            return (Criteria) this;
        }

        public Criteria andActiveIndicatorGreaterThanOrEqualTo(Boolean value) {
            addCriterion("ActiveIndicator >=", value, "activeIndicator");
            return (Criteria) this;
        }

        public Criteria andActiveIndicatorLessThan(Boolean value) {
            addCriterion("ActiveIndicator <", value, "activeIndicator");
            return (Criteria) this;
        }

        public Criteria andActiveIndicatorLessThanOrEqualTo(Boolean value) {
            addCriterion("ActiveIndicator <=", value, "activeIndicator");
            return (Criteria) this;
        }

        public Criteria andActiveIndicatorIn(List<Boolean> values) {
            addCriterion("ActiveIndicator in", values, "activeIndicator");
            return (Criteria) this;
        }

        public Criteria andActiveIndicatorNotIn(List<Boolean> values) {
            addCriterion("ActiveIndicator not in", values, "activeIndicator");
            return (Criteria) this;
        }

        public Criteria andActiveIndicatorBetween(Boolean value1, Boolean value2) {
            addCriterion("ActiveIndicator between", value1, value2, "activeIndicator");
            return (Criteria) this;
        }

        public Criteria andActiveIndicatorNotBetween(Boolean value1, Boolean value2) {
            addCriterion("ActiveIndicator not between", value1, value2, "activeIndicator");
            return (Criteria) this;
        }

        public Criteria andModifiedIsNull() {
            addCriterion("modified is null");
            return (Criteria) this;
        }

        public Criteria andModifiedIsNotNull() {
            addCriterion("modified is not null");
            return (Criteria) this;
        }

        public Criteria andModifiedEqualTo(Boolean value) {
            addCriterion("modified =", value, "modified");
            return (Criteria) this;
        }

        public Criteria andModifiedNotEqualTo(Boolean value) {
            addCriterion("modified <>", value, "modified");
            return (Criteria) this;
        }

        public Criteria andModifiedGreaterThan(Boolean value) {
            addCriterion("modified >", value, "modified");
            return (Criteria) this;
        }

        public Criteria andModifiedGreaterThanOrEqualTo(Boolean value) {
            addCriterion("modified >=", value, "modified");
            return (Criteria) this;
        }

        public Criteria andModifiedLessThan(Boolean value) {
            addCriterion("modified <", value, "modified");
            return (Criteria) this;
        }

        public Criteria andModifiedLessThanOrEqualTo(Boolean value) {
            addCriterion("modified <=", value, "modified");
            return (Criteria) this;
        }

        public Criteria andModifiedIn(List<Boolean> values) {
            addCriterion("modified in", values, "modified");
            return (Criteria) this;
        }

        public Criteria andModifiedNotIn(List<Boolean> values) {
            addCriterion("modified not in", values, "modified");
            return (Criteria) this;
        }

        public Criteria andModifiedBetween(Boolean value1, Boolean value2) {
            addCriterion("modified between", value1, value2, "modified");
            return (Criteria) this;
        }

        public Criteria andModifiedNotBetween(Boolean value1, Boolean value2) {
            addCriterion("modified not between", value1, value2, "modified");
            return (Criteria) this;
        }

        public Criteria andFileIDIsNull() {
            addCriterion("FileID is null");
            return (Criteria) this;
        }

        public Criteria andFileIDIsNotNull() {
            addCriterion("FileID is not null");
            return (Criteria) this;
        }

        public Criteria andFileIDEqualTo(String value) {
            addCriterion("FileID =", value, "fileID");
            return (Criteria) this;
        }

        public Criteria andFileIDNotEqualTo(String value) {
            addCriterion("FileID <>", value, "fileID");
            return (Criteria) this;
        }

        public Criteria andFileIDGreaterThan(String value) {
            addCriterion("FileID >", value, "fileID");
            return (Criteria) this;
        }

        public Criteria andFileIDGreaterThanOrEqualTo(String value) {
            addCriterion("FileID >=", value, "fileID");
            return (Criteria) this;
        }

        public Criteria andFileIDLessThan(String value) {
            addCriterion("FileID <", value, "fileID");
            return (Criteria) this;
        }

        public Criteria andFileIDLessThanOrEqualTo(String value) {
            addCriterion("FileID <=", value, "fileID");
            return (Criteria) this;
        }

        public Criteria andFileIDLike(String value) {
            addCriterion("FileID like", value, "fileID");
            return (Criteria) this;
        }

        public Criteria andFileIDNotLike(String value) {
            addCriterion("FileID not like", value, "fileID");
            return (Criteria) this;
        }

        public Criteria andFileIDIn(List<String> values) {
            addCriterion("FileID in", values, "fileID");
            return (Criteria) this;
        }

        public Criteria andFileIDNotIn(List<String> values) {
            addCriterion("FileID not in", values, "fileID");
            return (Criteria) this;
        }

        public Criteria andFileIDBetween(String value1, String value2) {
            addCriterion("FileID between", value1, value2, "fileID");
            return (Criteria) this;
        }

        public Criteria andFileIDNotBetween(String value1, String value2) {
            addCriterion("FileID not between", value1, value2, "fileID");
            return (Criteria) this;
        }

        public Criteria andDataEntryCloudIDIsNull() {
            addCriterion("DataEntryCloudID is null");
            return (Criteria) this;
        }

        public Criteria andDataEntryCloudIDIsNotNull() {
            addCriterion("DataEntryCloudID is not null");
            return (Criteria) this;
        }

        public Criteria andDataEntryCloudIDEqualTo(String value) {
            addCriterion("DataEntryCloudID =", value, "dataEntryCloudID");
            return (Criteria) this;
        }

        public Criteria andDataEntryCloudIDNotEqualTo(String value) {
            addCriterion("DataEntryCloudID <>", value, "dataEntryCloudID");
            return (Criteria) this;
        }

        public Criteria andDataEntryCloudIDGreaterThan(String value) {
            addCriterion("DataEntryCloudID >", value, "dataEntryCloudID");
            return (Criteria) this;
        }

        public Criteria andDataEntryCloudIDGreaterThanOrEqualTo(String value) {
            addCriterion("DataEntryCloudID >=", value, "dataEntryCloudID");
            return (Criteria) this;
        }

        public Criteria andDataEntryCloudIDLessThan(String value) {
            addCriterion("DataEntryCloudID <", value, "dataEntryCloudID");
            return (Criteria) this;
        }

        public Criteria andDataEntryCloudIDLessThanOrEqualTo(String value) {
            addCriterion("DataEntryCloudID <=", value, "dataEntryCloudID");
            return (Criteria) this;
        }

        public Criteria andDataEntryCloudIDLike(String value) {
            addCriterion("DataEntryCloudID like", value, "dataEntryCloudID");
            return (Criteria) this;
        }

        public Criteria andDataEntryCloudIDNotLike(String value) {
            addCriterion("DataEntryCloudID not like", value, "dataEntryCloudID");
            return (Criteria) this;
        }

        public Criteria andDataEntryCloudIDIn(List<String> values) {
            addCriterion("DataEntryCloudID in", values, "dataEntryCloudID");
            return (Criteria) this;
        }

        public Criteria andDataEntryCloudIDNotIn(List<String> values) {
            addCriterion("DataEntryCloudID not in", values, "dataEntryCloudID");
            return (Criteria) this;
        }

        public Criteria andDataEntryCloudIDBetween(String value1, String value2) {
            addCriterion("DataEntryCloudID between", value1, value2, "dataEntryCloudID");
            return (Criteria) this;
        }

        public Criteria andDataEntryCloudIDNotBetween(String value1, String value2) {
            addCriterion("DataEntryCloudID not between", value1, value2, "dataEntryCloudID");
            return (Criteria) this;
        }

        public Criteria andCreatedDateIsNull() {
            addCriterion("CreatedDate is null");
            return (Criteria) this;
        }

        public Criteria andCreatedDateIsNotNull() {
            addCriterion("CreatedDate is not null");
            return (Criteria) this;
        }

        public Criteria andCreatedDateEqualTo(Date value) {
            addCriterion("CreatedDate =", value, "createdDate");
            return (Criteria) this;
        }

        public Criteria andCreatedDateNotEqualTo(Date value) {
            addCriterion("CreatedDate <>", value, "createdDate");
            return (Criteria) this;
        }

        public Criteria andCreatedDateGreaterThan(Date value) {
            addCriterion("CreatedDate >", value, "createdDate");
            return (Criteria) this;
        }

        public Criteria andCreatedDateGreaterThanOrEqualTo(Date value) {
            addCriterion("CreatedDate >=", value, "createdDate");
            return (Criteria) this;
        }

        public Criteria andCreatedDateLessThan(Date value) {
            addCriterion("CreatedDate <", value, "createdDate");
            return (Criteria) this;
        }

        public Criteria andCreatedDateLessThanOrEqualTo(Date value) {
            addCriterion("CreatedDate <=", value, "createdDate");
            return (Criteria) this;
        }

        public Criteria andCreatedDateIn(List<Date> values) {
            addCriterion("CreatedDate in", values, "createdDate");
            return (Criteria) this;
        }

        public Criteria andCreatedDateNotIn(List<Date> values) {
            addCriterion("CreatedDate not in", values, "createdDate");
            return (Criteria) this;
        }

        public Criteria andCreatedDateBetween(Date value1, Date value2) {
            addCriterion("CreatedDate between", value1, value2, "createdDate");
            return (Criteria) this;
        }

        public Criteria andCreatedDateNotBetween(Date value1, Date value2) {
            addCriterion("CreatedDate not between", value1, value2, "createdDate");
            return (Criteria) this;
        }

        public Criteria andCreatedByIsNull() {
            addCriterion("CreatedBy is null");
            return (Criteria) this;
        }

        public Criteria andCreatedByIsNotNull() {
            addCriterion("CreatedBy is not null");
            return (Criteria) this;
        }

        public Criteria andCreatedByEqualTo(String value) {
            addCriterion("CreatedBy =", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotEqualTo(String value) {
            addCriterion("CreatedBy <>", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByGreaterThan(String value) {
            addCriterion("CreatedBy >", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByGreaterThanOrEqualTo(String value) {
            addCriterion("CreatedBy >=", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByLessThan(String value) {
            addCriterion("CreatedBy <", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByLessThanOrEqualTo(String value) {
            addCriterion("CreatedBy <=", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByLike(String value) {
            addCriterion("CreatedBy like", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotLike(String value) {
            addCriterion("CreatedBy not like", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByIn(List<String> values) {
            addCriterion("CreatedBy in", values, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotIn(List<String> values) {
            addCriterion("CreatedBy not in", values, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByBetween(String value1, String value2) {
            addCriterion("CreatedBy between", value1, value2, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotBetween(String value1, String value2) {
            addCriterion("CreatedBy not between", value1, value2, "createdBy");
            return (Criteria) this;
        }

        public Criteria andModifiedDateIsNull() {
            addCriterion("ModifiedDate is null");
            return (Criteria) this;
        }

        public Criteria andModifiedDateIsNotNull() {
            addCriterion("ModifiedDate is not null");
            return (Criteria) this;
        }

        public Criteria andModifiedDateEqualTo(Date value) {
            addCriterion("ModifiedDate =", value, "modifiedDate");
            return (Criteria) this;
        }

        public Criteria andModifiedDateNotEqualTo(Date value) {
            addCriterion("ModifiedDate <>", value, "modifiedDate");
            return (Criteria) this;
        }

        public Criteria andModifiedDateGreaterThan(Date value) {
            addCriterion("ModifiedDate >", value, "modifiedDate");
            return (Criteria) this;
        }

        public Criteria andModifiedDateGreaterThanOrEqualTo(Date value) {
            addCriterion("ModifiedDate >=", value, "modifiedDate");
            return (Criteria) this;
        }

        public Criteria andModifiedDateLessThan(Date value) {
            addCriterion("ModifiedDate <", value, "modifiedDate");
            return (Criteria) this;
        }

        public Criteria andModifiedDateLessThanOrEqualTo(Date value) {
            addCriterion("ModifiedDate <=", value, "modifiedDate");
            return (Criteria) this;
        }

        public Criteria andModifiedDateIn(List<Date> values) {
            addCriterion("ModifiedDate in", values, "modifiedDate");
            return (Criteria) this;
        }

        public Criteria andModifiedDateNotIn(List<Date> values) {
            addCriterion("ModifiedDate not in", values, "modifiedDate");
            return (Criteria) this;
        }

        public Criteria andModifiedDateBetween(Date value1, Date value2) {
            addCriterion("ModifiedDate between", value1, value2, "modifiedDate");
            return (Criteria) this;
        }

        public Criteria andModifiedDateNotBetween(Date value1, Date value2) {
            addCriterion("ModifiedDate not between", value1, value2, "modifiedDate");
            return (Criteria) this;
        }

        public Criteria andModifiedByIsNull() {
            addCriterion("ModifiedBy is null");
            return (Criteria) this;
        }

        public Criteria andModifiedByIsNotNull() {
            addCriterion("ModifiedBy is not null");
            return (Criteria) this;
        }

        public Criteria andModifiedByEqualTo(String value) {
            addCriterion("ModifiedBy =", value, "modifiedBy");
            return (Criteria) this;
        }

        public Criteria andModifiedByNotEqualTo(String value) {
            addCriterion("ModifiedBy <>", value, "modifiedBy");
            return (Criteria) this;
        }

        public Criteria andModifiedByGreaterThan(String value) {
            addCriterion("ModifiedBy >", value, "modifiedBy");
            return (Criteria) this;
        }

        public Criteria andModifiedByGreaterThanOrEqualTo(String value) {
            addCriterion("ModifiedBy >=", value, "modifiedBy");
            return (Criteria) this;
        }

        public Criteria andModifiedByLessThan(String value) {
            addCriterion("ModifiedBy <", value, "modifiedBy");
            return (Criteria) this;
        }

        public Criteria andModifiedByLessThanOrEqualTo(String value) {
            addCriterion("ModifiedBy <=", value, "modifiedBy");
            return (Criteria) this;
        }

        public Criteria andModifiedByLike(String value) {
            addCriterion("ModifiedBy like", value, "modifiedBy");
            return (Criteria) this;
        }

        public Criteria andModifiedByNotLike(String value) {
            addCriterion("ModifiedBy not like", value, "modifiedBy");
            return (Criteria) this;
        }

        public Criteria andModifiedByIn(List<String> values) {
            addCriterion("ModifiedBy in", values, "modifiedBy");
            return (Criteria) this;
        }

        public Criteria andModifiedByNotIn(List<String> values) {
            addCriterion("ModifiedBy not in", values, "modifiedBy");
            return (Criteria) this;
        }

        public Criteria andModifiedByBetween(String value1, String value2) {
            addCriterion("ModifiedBy between", value1, value2, "modifiedBy");
            return (Criteria) this;
        }

        public Criteria andModifiedByNotBetween(String value1, String value2) {
            addCriterion("ModifiedBy not between", value1, value2, "modifiedBy");
            return (Criteria) this;
        }

        public Criteria andValidateDateIsNull() {
            addCriterion("ValidateDate is null");
            return (Criteria) this;
        }

        public Criteria andValidateDateIsNotNull() {
            addCriterion("ValidateDate is not null");
            return (Criteria) this;
        }

        public Criteria andValidateDateEqualTo(Date value) {
            addCriterion("ValidateDate =", value, "validateDate");
            return (Criteria) this;
        }

        public Criteria andValidateDateNotEqualTo(Date value) {
            addCriterion("ValidateDate <>", value, "validateDate");
            return (Criteria) this;
        }

        public Criteria andValidateDateGreaterThan(Date value) {
            addCriterion("ValidateDate >", value, "validateDate");
            return (Criteria) this;
        }

        public Criteria andValidateDateGreaterThanOrEqualTo(Date value) {
            addCriterion("ValidateDate >=", value, "validateDate");
            return (Criteria) this;
        }

        public Criteria andValidateDateLessThan(Date value) {
            addCriterion("ValidateDate <", value, "validateDate");
            return (Criteria) this;
        }

        public Criteria andValidateDateLessThanOrEqualTo(Date value) {
            addCriterion("ValidateDate <=", value, "validateDate");
            return (Criteria) this;
        }

        public Criteria andValidateDateIn(List<Date> values) {
            addCriterion("ValidateDate in", values, "validateDate");
            return (Criteria) this;
        }

        public Criteria andValidateDateNotIn(List<Date> values) {
            addCriterion("ValidateDate not in", values, "validateDate");
            return (Criteria) this;
        }

        public Criteria andValidateDateBetween(Date value1, Date value2) {
            addCriterion("ValidateDate between", value1, value2, "validateDate");
            return (Criteria) this;
        }

        public Criteria andValidateDateNotBetween(Date value1, Date value2) {
            addCriterion("ValidateDate not between", value1, value2, "validateDate");
            return (Criteria) this;
        }

        public Criteria andValidateByIsNull() {
            addCriterion("ValidateBy is null");
            return (Criteria) this;
        }

        public Criteria andValidateByIsNotNull() {
            addCriterion("ValidateBy is not null");
            return (Criteria) this;
        }

        public Criteria andValidateByEqualTo(String value) {
            addCriterion("ValidateBy =", value, "validateBy");
            return (Criteria) this;
        }

        public Criteria andValidateByNotEqualTo(String value) {
            addCriterion("ValidateBy <>", value, "validateBy");
            return (Criteria) this;
        }

        public Criteria andValidateByGreaterThan(String value) {
            addCriterion("ValidateBy >", value, "validateBy");
            return (Criteria) this;
        }

        public Criteria andValidateByGreaterThanOrEqualTo(String value) {
            addCriterion("ValidateBy >=", value, "validateBy");
            return (Criteria) this;
        }

        public Criteria andValidateByLessThan(String value) {
            addCriterion("ValidateBy <", value, "validateBy");
            return (Criteria) this;
        }

        public Criteria andValidateByLessThanOrEqualTo(String value) {
            addCriterion("ValidateBy <=", value, "validateBy");
            return (Criteria) this;
        }

        public Criteria andValidateByLike(String value) {
            addCriterion("ValidateBy like", value, "validateBy");
            return (Criteria) this;
        }

        public Criteria andValidateByNotLike(String value) {
            addCriterion("ValidateBy not like", value, "validateBy");
            return (Criteria) this;
        }

        public Criteria andValidateByIn(List<String> values) {
            addCriterion("ValidateBy in", values, "validateBy");
            return (Criteria) this;
        }

        public Criteria andValidateByNotIn(List<String> values) {
            addCriterion("ValidateBy not in", values, "validateBy");
            return (Criteria) this;
        }

        public Criteria andValidateByBetween(String value1, String value2) {
            addCriterion("ValidateBy between", value1, value2, "validateBy");
            return (Criteria) this;
        }

        public Criteria andValidateByNotBetween(String value1, String value2) {
            addCriterion("ValidateBy not between", value1, value2, "validateBy");
            return (Criteria) this;
        }

        public Criteria andReportSeqIsNull() {
            addCriterion("ReportSeq is null");
            return (Criteria) this;
        }

        public Criteria andReportSeqIsNotNull() {
            addCriterion("ReportSeq is not null");
            return (Criteria) this;
        }

        public Criteria andReportSeqEqualTo(Integer value) {
            addCriterion("ReportSeq =", value, "reportSeq");
            return (Criteria) this;
        }

        public Criteria andReportSeqNotEqualTo(Integer value) {
            addCriterion("ReportSeq <>", value, "reportSeq");
            return (Criteria) this;
        }

        public Criteria andReportSeqGreaterThan(Integer value) {
            addCriterion("ReportSeq >", value, "reportSeq");
            return (Criteria) this;
        }

        public Criteria andReportSeqGreaterThanOrEqualTo(Integer value) {
            addCriterion("ReportSeq >=", value, "reportSeq");
            return (Criteria) this;
        }

        public Criteria andReportSeqLessThan(Integer value) {
            addCriterion("ReportSeq <", value, "reportSeq");
            return (Criteria) this;
        }

        public Criteria andReportSeqLessThanOrEqualTo(Integer value) {
            addCriterion("ReportSeq <=", value, "reportSeq");
            return (Criteria) this;
        }

        public Criteria andReportSeqIn(List<Integer> values) {
            addCriterion("ReportSeq in", values, "reportSeq");
            return (Criteria) this;
        }

        public Criteria andReportSeqNotIn(List<Integer> values) {
            addCriterion("ReportSeq not in", values, "reportSeq");
            return (Criteria) this;
        }

        public Criteria andReportSeqBetween(Integer value1, Integer value2) {
            addCriterion("ReportSeq between", value1, value2, "reportSeq");
            return (Criteria) this;
        }

        public Criteria andReportSeqNotBetween(Integer value1, Integer value2) {
            addCriterion("ReportSeq not between", value1, value2, "reportSeq");
            return (Criteria) this;
        }

        public Criteria andTestLineAliasIsNull() {
            addCriterion("TestLineAlias is null");
            return (Criteria) this;
        }

        public Criteria andTestLineAliasIsNotNull() {
            addCriterion("TestLineAlias is not null");
            return (Criteria) this;
        }

        public Criteria andTestLineAliasEqualTo(String value) {
            addCriterion("TestLineAlias =", value, "testLineAlias");
            return (Criteria) this;
        }

        public Criteria andTestLineAliasNotEqualTo(String value) {
            addCriterion("TestLineAlias <>", value, "testLineAlias");
            return (Criteria) this;
        }

        public Criteria andTestLineAliasGreaterThan(String value) {
            addCriterion("TestLineAlias >", value, "testLineAlias");
            return (Criteria) this;
        }

        public Criteria andTestLineAliasGreaterThanOrEqualTo(String value) {
            addCriterion("TestLineAlias >=", value, "testLineAlias");
            return (Criteria) this;
        }

        public Criteria andTestLineAliasLessThan(String value) {
            addCriterion("TestLineAlias <", value, "testLineAlias");
            return (Criteria) this;
        }

        public Criteria andTestLineAliasLessThanOrEqualTo(String value) {
            addCriterion("TestLineAlias <=", value, "testLineAlias");
            return (Criteria) this;
        }

        public Criteria andTestLineAliasLike(String value) {
            addCriterion("TestLineAlias like", value, "testLineAlias");
            return (Criteria) this;
        }

        public Criteria andTestLineAliasNotLike(String value) {
            addCriterion("TestLineAlias not like", value, "testLineAlias");
            return (Criteria) this;
        }

        public Criteria andTestLineAliasIn(List<String> values) {
            addCriterion("TestLineAlias in", values, "testLineAlias");
            return (Criteria) this;
        }

        public Criteria andTestLineAliasNotIn(List<String> values) {
            addCriterion("TestLineAlias not in", values, "testLineAlias");
            return (Criteria) this;
        }

        public Criteria andTestLineAliasBetween(String value1, String value2) {
            addCriterion("TestLineAlias between", value1, value2, "testLineAlias");
            return (Criteria) this;
        }

        public Criteria andTestLineAliasNotBetween(String value1, String value2) {
            addCriterion("TestLineAlias not between", value1, value2, "testLineAlias");
            return (Criteria) this;
        }

        public Criteria andCalculateConclusionFlagIsNull() {
            addCriterion("CalculateConclusionFlag is null");
            return (Criteria) this;
        }

        public Criteria andCalculateConclusionFlagIsNotNull() {
            addCriterion("CalculateConclusionFlag is not null");
            return (Criteria) this;
        }

        public Criteria andCalculateConclusionFlagEqualTo(Integer value) {
            addCriterion("CalculateConclusionFlag =", value, "calculateConclusionFlag");
            return (Criteria) this;
        }

        public Criteria andCalculateConclusionFlagNotEqualTo(Integer value) {
            addCriterion("CalculateConclusionFlag <>", value, "calculateConclusionFlag");
            return (Criteria) this;
        }

        public Criteria andCalculateConclusionFlagGreaterThan(Integer value) {
            addCriterion("CalculateConclusionFlag >", value, "calculateConclusionFlag");
            return (Criteria) this;
        }

        public Criteria andCalculateConclusionFlagGreaterThanOrEqualTo(Integer value) {
            addCriterion("CalculateConclusionFlag >=", value, "calculateConclusionFlag");
            return (Criteria) this;
        }

        public Criteria andCalculateConclusionFlagLessThan(Integer value) {
            addCriterion("CalculateConclusionFlag <", value, "calculateConclusionFlag");
            return (Criteria) this;
        }

        public Criteria andCalculateConclusionFlagLessThanOrEqualTo(Integer value) {
            addCriterion("CalculateConclusionFlag <=", value, "calculateConclusionFlag");
            return (Criteria) this;
        }

        public Criteria andCalculateConclusionFlagIn(List<Integer> values) {
            addCriterion("CalculateConclusionFlag in", values, "calculateConclusionFlag");
            return (Criteria) this;
        }

        public Criteria andCalculateConclusionFlagNotIn(List<Integer> values) {
            addCriterion("CalculateConclusionFlag not in", values, "calculateConclusionFlag");
            return (Criteria) this;
        }

        public Criteria andCalculateConclusionFlagBetween(Integer value1, Integer value2) {
            addCriterion("CalculateConclusionFlag between", value1, value2, "calculateConclusionFlag");
            return (Criteria) this;
        }

        public Criteria andCalculateConclusionFlagNotBetween(Integer value1, Integer value2) {
            addCriterion("CalculateConclusionFlag not between", value1, value2, "calculateConclusionFlag");
            return (Criteria) this;
        }

        public Criteria andProductLineAbbrIsNull() {
            addCriterion("ProductLineAbbr is null");
            return (Criteria) this;
        }

        public Criteria andProductLineAbbrIsNotNull() {
            addCriterion("ProductLineAbbr is not null");
            return (Criteria) this;
        }

        public Criteria andProductLineAbbrEqualTo(String value) {
            addCriterion("ProductLineAbbr =", value, "productLineAbbr");
            return (Criteria) this;
        }

        public Criteria andProductLineAbbrNotEqualTo(String value) {
            addCriterion("ProductLineAbbr <>", value, "productLineAbbr");
            return (Criteria) this;
        }

        public Criteria andProductLineAbbrGreaterThan(String value) {
            addCriterion("ProductLineAbbr >", value, "productLineAbbr");
            return (Criteria) this;
        }

        public Criteria andProductLineAbbrGreaterThanOrEqualTo(String value) {
            addCriterion("ProductLineAbbr >=", value, "productLineAbbr");
            return (Criteria) this;
        }

        public Criteria andProductLineAbbrLessThan(String value) {
            addCriterion("ProductLineAbbr <", value, "productLineAbbr");
            return (Criteria) this;
        }

        public Criteria andProductLineAbbrLessThanOrEqualTo(String value) {
            addCriterion("ProductLineAbbr <=", value, "productLineAbbr");
            return (Criteria) this;
        }

        public Criteria andProductLineAbbrLike(String value) {
            addCriterion("ProductLineAbbr like", value, "productLineAbbr");
            return (Criteria) this;
        }

        public Criteria andProductLineAbbrNotLike(String value) {
            addCriterion("ProductLineAbbr not like", value, "productLineAbbr");
            return (Criteria) this;
        }

        public Criteria andProductLineAbbrIn(List<String> values) {
            addCriterion("ProductLineAbbr in", values, "productLineAbbr");
            return (Criteria) this;
        }

        public Criteria andProductLineAbbrNotIn(List<String> values) {
            addCriterion("ProductLineAbbr not in", values, "productLineAbbr");
            return (Criteria) this;
        }

        public Criteria andProductLineAbbrBetween(String value1, String value2) {
            addCriterion("ProductLineAbbr between", value1, value2, "productLineAbbr");
            return (Criteria) this;
        }

        public Criteria andProductLineAbbrNotBetween(String value1, String value2) {
            addCriterion("ProductLineAbbr not between", value1, value2, "productLineAbbr");
            return (Criteria) this;
        }

        public Criteria andTestLineTypeIsNull() {
            addCriterion("TestLineType is null");
            return (Criteria) this;
        }

        public Criteria andTestLineTypeIsNotNull() {
            addCriterion("TestLineType is not null");
            return (Criteria) this;
        }

        public Criteria andTestLineTypeEqualTo(Integer value) {
            addCriterion("TestLineType =", value, "testLineType");
            return (Criteria) this;
        }

        public Criteria andTestLineTypeNotEqualTo(Integer value) {
            addCriterion("TestLineType <>", value, "testLineType");
            return (Criteria) this;
        }

        public Criteria andTestLineTypeGreaterThan(Integer value) {
            addCriterion("TestLineType >", value, "testLineType");
            return (Criteria) this;
        }

        public Criteria andTestLineTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("TestLineType >=", value, "testLineType");
            return (Criteria) this;
        }

        public Criteria andTestLineTypeLessThan(Integer value) {
            addCriterion("TestLineType <", value, "testLineType");
            return (Criteria) this;
        }

        public Criteria andTestLineTypeLessThanOrEqualTo(Integer value) {
            addCriterion("TestLineType <=", value, "testLineType");
            return (Criteria) this;
        }

        public Criteria andTestLineTypeIn(List<Integer> values) {
            addCriterion("TestLineType in", values, "testLineType");
            return (Criteria) this;
        }

        public Criteria andTestLineTypeNotIn(List<Integer> values) {
            addCriterion("TestLineType not in", values, "testLineType");
            return (Criteria) this;
        }

        public Criteria andTestLineTypeBetween(Integer value1, Integer value2) {
            addCriterion("TestLineType between", value1, value2, "testLineType");
            return (Criteria) this;
        }

        public Criteria andTestLineTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("TestLineType not between", value1, value2, "testLineType");
            return (Criteria) this;
        }

        public Criteria andTestLineBaseIdIsNull() {
            addCriterion("TestLineBaseId is null");
            return (Criteria) this;
        }

        public Criteria andTestLineBaseIdIsNotNull() {
            addCriterion("TestLineBaseId is not null");
            return (Criteria) this;
        }

        public Criteria andTestLineBaseIdEqualTo(Long value) {
            addCriterion("TestLineBaseId =", value, "testLineBaseId");
            return (Criteria) this;
        }

        public Criteria andTestLineBaseIdNotEqualTo(Long value) {
            addCriterion("TestLineBaseId <>", value, "testLineBaseId");
            return (Criteria) this;
        }

        public Criteria andTestLineBaseIdGreaterThan(Long value) {
            addCriterion("TestLineBaseId >", value, "testLineBaseId");
            return (Criteria) this;
        }

        public Criteria andTestLineBaseIdGreaterThanOrEqualTo(Long value) {
            addCriterion("TestLineBaseId >=", value, "testLineBaseId");
            return (Criteria) this;
        }

        public Criteria andTestLineBaseIdLessThan(Long value) {
            addCriterion("TestLineBaseId <", value, "testLineBaseId");
            return (Criteria) this;
        }

        public Criteria andTestLineBaseIdLessThanOrEqualTo(Long value) {
            addCriterion("TestLineBaseId <=", value, "testLineBaseId");
            return (Criteria) this;
        }

        public Criteria andTestLineBaseIdIn(List<Long> values) {
            addCriterion("TestLineBaseId in", values, "testLineBaseId");
            return (Criteria) this;
        }

        public Criteria andTestLineBaseIdNotIn(List<Long> values) {
            addCriterion("TestLineBaseId not in", values, "testLineBaseId");
            return (Criteria) this;
        }

        public Criteria andTestLineBaseIdBetween(Long value1, Long value2) {
            addCriterion("TestLineBaseId between", value1, value2, "testLineBaseId");
            return (Criteria) this;
        }

        public Criteria andTestLineBaseIdNotBetween(Long value1, Long value2) {
            addCriterion("TestLineBaseId not between", value1, value2, "testLineBaseId");
            return (Criteria) this;
        }

        public Criteria andCitationBaseIdIsNull() {
            addCriterion("CitationBaseId is null");
            return (Criteria) this;
        }

        public Criteria andCitationBaseIdIsNotNull() {
            addCriterion("CitationBaseId is not null");
            return (Criteria) this;
        }

        public Criteria andCitationBaseIdEqualTo(Long value) {
            addCriterion("CitationBaseId =", value, "citationBaseId");
            return (Criteria) this;
        }

        public Criteria andCitationBaseIdNotEqualTo(Long value) {
            addCriterion("CitationBaseId <>", value, "citationBaseId");
            return (Criteria) this;
        }

        public Criteria andCitationBaseIdGreaterThan(Long value) {
            addCriterion("CitationBaseId >", value, "citationBaseId");
            return (Criteria) this;
        }

        public Criteria andCitationBaseIdGreaterThanOrEqualTo(Long value) {
            addCriterion("CitationBaseId >=", value, "citationBaseId");
            return (Criteria) this;
        }

        public Criteria andCitationBaseIdLessThan(Long value) {
            addCriterion("CitationBaseId <", value, "citationBaseId");
            return (Criteria) this;
        }

        public Criteria andCitationBaseIdLessThanOrEqualTo(Long value) {
            addCriterion("CitationBaseId <=", value, "citationBaseId");
            return (Criteria) this;
        }

        public Criteria andCitationBaseIdIn(List<Long> values) {
            addCriterion("CitationBaseId in", values, "citationBaseId");
            return (Criteria) this;
        }

        public Criteria andCitationBaseIdNotIn(List<Long> values) {
            addCriterion("CitationBaseId not in", values, "citationBaseId");
            return (Criteria) this;
        }

        public Criteria andCitationBaseIdBetween(Long value1, Long value2) {
            addCriterion("CitationBaseId between", value1, value2, "citationBaseId");
            return (Criteria) this;
        }

        public Criteria andCitationBaseIdNotBetween(Long value1, Long value2) {
            addCriterion("CitationBaseId not between", value1, value2, "citationBaseId");
            return (Criteria) this;
        }

        public Criteria andLabSectionBaseIdIsNull() {
            addCriterion("LabSectionBaseId is null");
            return (Criteria) this;
        }

        public Criteria andLabSectionBaseIdIsNotNull() {
            addCriterion("LabSectionBaseId is not null");
            return (Criteria) this;
        }

        public Criteria andLabSectionBaseIdEqualTo(Long value) {
            addCriterion("LabSectionBaseId =", value, "labSectionBaseId");
            return (Criteria) this;
        }

        public Criteria andLabSectionBaseIdNotEqualTo(Long value) {
            addCriterion("LabSectionBaseId <>", value, "labSectionBaseId");
            return (Criteria) this;
        }

        public Criteria andLabSectionBaseIdGreaterThan(Long value) {
            addCriterion("LabSectionBaseId >", value, "labSectionBaseId");
            return (Criteria) this;
        }

        public Criteria andLabSectionBaseIdGreaterThanOrEqualTo(Long value) {
            addCriterion("LabSectionBaseId >=", value, "labSectionBaseId");
            return (Criteria) this;
        }

        public Criteria andLabSectionBaseIdLessThan(Long value) {
            addCriterion("LabSectionBaseId <", value, "labSectionBaseId");
            return (Criteria) this;
        }

        public Criteria andLabSectionBaseIdLessThanOrEqualTo(Long value) {
            addCriterion("LabSectionBaseId <=", value, "labSectionBaseId");
            return (Criteria) this;
        }

        public Criteria andLabSectionBaseIdIn(List<Long> values) {
            addCriterion("LabSectionBaseId in", values, "labSectionBaseId");
            return (Criteria) this;
        }

        public Criteria andLabSectionBaseIdNotIn(List<Long> values) {
            addCriterion("LabSectionBaseId not in", values, "labSectionBaseId");
            return (Criteria) this;
        }

        public Criteria andLabSectionBaseIdBetween(Long value1, Long value2) {
            addCriterion("LabSectionBaseId between", value1, value2, "labSectionBaseId");
            return (Criteria) this;
        }

        public Criteria andLabSectionBaseIdNotBetween(Long value1, Long value2) {
            addCriterion("LabSectionBaseId not between", value1, value2, "labSectionBaseId");
            return (Criteria) this;
        }

        public Criteria andCitationIdIsNull() {
            addCriterion("CitationId is null");
            return (Criteria) this;
        }

        public Criteria andCitationIdIsNotNull() {
            addCriterion("CitationId is not null");
            return (Criteria) this;
        }

        public Criteria andCitationIdEqualTo(Integer value) {
            addCriterion("CitationId =", value, "citationId");
            return (Criteria) this;
        }

        public Criteria andCitationIdNotEqualTo(Integer value) {
            addCriterion("CitationId <>", value, "citationId");
            return (Criteria) this;
        }

        public Criteria andCitationIdGreaterThan(Integer value) {
            addCriterion("CitationId >", value, "citationId");
            return (Criteria) this;
        }

        public Criteria andCitationIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("CitationId >=", value, "citationId");
            return (Criteria) this;
        }

        public Criteria andCitationIdLessThan(Integer value) {
            addCriterion("CitationId <", value, "citationId");
            return (Criteria) this;
        }

        public Criteria andCitationIdLessThanOrEqualTo(Integer value) {
            addCriterion("CitationId <=", value, "citationId");
            return (Criteria) this;
        }

        public Criteria andCitationIdIn(List<Integer> values) {
            addCriterion("CitationId in", values, "citationId");
            return (Criteria) this;
        }

        public Criteria andCitationIdNotIn(List<Integer> values) {
            addCriterion("CitationId not in", values, "citationId");
            return (Criteria) this;
        }

        public Criteria andCitationIdBetween(Integer value1, Integer value2) {
            addCriterion("CitationId between", value1, value2, "citationId");
            return (Criteria) this;
        }

        public Criteria andCitationIdNotBetween(Integer value1, Integer value2) {
            addCriterion("CitationId not between", value1, value2, "citationId");
            return (Criteria) this;
        }

        public Criteria andCitationVersionIdIsNull() {
            addCriterion("CitationVersionId is null");
            return (Criteria) this;
        }

        public Criteria andCitationVersionIdIsNotNull() {
            addCriterion("CitationVersionId is not null");
            return (Criteria) this;
        }

        public Criteria andCitationVersionIdEqualTo(Integer value) {
            addCriterion("CitationVersionId =", value, "citationVersionId");
            return (Criteria) this;
        }

        public Criteria andCitationVersionIdNotEqualTo(Integer value) {
            addCriterion("CitationVersionId <>", value, "citationVersionId");
            return (Criteria) this;
        }

        public Criteria andCitationVersionIdGreaterThan(Integer value) {
            addCriterion("CitationVersionId >", value, "citationVersionId");
            return (Criteria) this;
        }

        public Criteria andCitationVersionIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("CitationVersionId >=", value, "citationVersionId");
            return (Criteria) this;
        }

        public Criteria andCitationVersionIdLessThan(Integer value) {
            addCriterion("CitationVersionId <", value, "citationVersionId");
            return (Criteria) this;
        }

        public Criteria andCitationVersionIdLessThanOrEqualTo(Integer value) {
            addCriterion("CitationVersionId <=", value, "citationVersionId");
            return (Criteria) this;
        }

        public Criteria andCitationVersionIdIn(List<Integer> values) {
            addCriterion("CitationVersionId in", values, "citationVersionId");
            return (Criteria) this;
        }

        public Criteria andCitationVersionIdNotIn(List<Integer> values) {
            addCriterion("CitationVersionId not in", values, "citationVersionId");
            return (Criteria) this;
        }

        public Criteria andCitationVersionIdBetween(Integer value1, Integer value2) {
            addCriterion("CitationVersionId between", value1, value2, "citationVersionId");
            return (Criteria) this;
        }

        public Criteria andCitationVersionIdNotBetween(Integer value1, Integer value2) {
            addCriterion("CitationVersionId not between", value1, value2, "citationVersionId");
            return (Criteria) this;
        }

        public Criteria andSampleSegegrationWIIDIsNull() {
            addCriterion("SampleSegegrationWIID is null");
            return (Criteria) this;
        }

        public Criteria andSampleSegegrationWIIDIsNotNull() {
            addCriterion("SampleSegegrationWIID is not null");
            return (Criteria) this;
        }

        public Criteria andSampleSegegrationWIIDEqualTo(Long value) {
            addCriterion("SampleSegegrationWIID =", value, "sampleSegegrationWIID");
            return (Criteria) this;
        }

        public Criteria andSampleSegegrationWIIDNotEqualTo(Long value) {
            addCriterion("SampleSegegrationWIID <>", value, "sampleSegegrationWIID");
            return (Criteria) this;
        }

        public Criteria andSampleSegegrationWIIDGreaterThan(Long value) {
            addCriterion("SampleSegegrationWIID >", value, "sampleSegegrationWIID");
            return (Criteria) this;
        }

        public Criteria andSampleSegegrationWIIDGreaterThanOrEqualTo(Long value) {
            addCriterion("SampleSegegrationWIID >=", value, "sampleSegegrationWIID");
            return (Criteria) this;
        }

        public Criteria andSampleSegegrationWIIDLessThan(Long value) {
            addCriterion("SampleSegegrationWIID <", value, "sampleSegegrationWIID");
            return (Criteria) this;
        }

        public Criteria andSampleSegegrationWIIDLessThanOrEqualTo(Long value) {
            addCriterion("SampleSegegrationWIID <=", value, "sampleSegegrationWIID");
            return (Criteria) this;
        }

        public Criteria andSampleSegegrationWIIDIn(List<Long> values) {
            addCriterion("SampleSegegrationWIID in", values, "sampleSegegrationWIID");
            return (Criteria) this;
        }

        public Criteria andSampleSegegrationWIIDNotIn(List<Long> values) {
            addCriterion("SampleSegegrationWIID not in", values, "sampleSegegrationWIID");
            return (Criteria) this;
        }

        public Criteria andSampleSegegrationWIIDBetween(Long value1, Long value2) {
            addCriterion("SampleSegegrationWIID between", value1, value2, "sampleSegegrationWIID");
            return (Criteria) this;
        }

        public Criteria andSampleSegegrationWIIDNotBetween(Long value1, Long value2) {
            addCriterion("SampleSegegrationWIID not between", value1, value2, "sampleSegegrationWIID");
            return (Criteria) this;
        }

        public Criteria andSampleSegegrationWITextIsNull() {
            addCriterion("SampleSegegrationWIText is null");
            return (Criteria) this;
        }

        public Criteria andSampleSegegrationWITextIsNotNull() {
            addCriterion("SampleSegegrationWIText is not null");
            return (Criteria) this;
        }

        public Criteria andSampleSegegrationWITextEqualTo(String value) {
            addCriterion("SampleSegegrationWIText =", value, "sampleSegegrationWIText");
            return (Criteria) this;
        }

        public Criteria andSampleSegegrationWITextNotEqualTo(String value) {
            addCriterion("SampleSegegrationWIText <>", value, "sampleSegegrationWIText");
            return (Criteria) this;
        }

        public Criteria andSampleSegegrationWITextGreaterThan(String value) {
            addCriterion("SampleSegegrationWIText >", value, "sampleSegegrationWIText");
            return (Criteria) this;
        }

        public Criteria andSampleSegegrationWITextGreaterThanOrEqualTo(String value) {
            addCriterion("SampleSegegrationWIText >=", value, "sampleSegegrationWIText");
            return (Criteria) this;
        }

        public Criteria andSampleSegegrationWITextLessThan(String value) {
            addCriterion("SampleSegegrationWIText <", value, "sampleSegegrationWIText");
            return (Criteria) this;
        }

        public Criteria andSampleSegegrationWITextLessThanOrEqualTo(String value) {
            addCriterion("SampleSegegrationWIText <=", value, "sampleSegegrationWIText");
            return (Criteria) this;
        }

        public Criteria andSampleSegegrationWITextLike(String value) {
            addCriterion("SampleSegegrationWIText like", value, "sampleSegegrationWIText");
            return (Criteria) this;
        }

        public Criteria andSampleSegegrationWITextNotLike(String value) {
            addCriterion("SampleSegegrationWIText not like", value, "sampleSegegrationWIText");
            return (Criteria) this;
        }

        public Criteria andSampleSegegrationWITextIn(List<String> values) {
            addCriterion("SampleSegegrationWIText in", values, "sampleSegegrationWIText");
            return (Criteria) this;
        }

        public Criteria andSampleSegegrationWITextNotIn(List<String> values) {
            addCriterion("SampleSegegrationWIText not in", values, "sampleSegegrationWIText");
            return (Criteria) this;
        }

        public Criteria andSampleSegegrationWITextBetween(String value1, String value2) {
            addCriterion("SampleSegegrationWIText between", value1, value2, "sampleSegegrationWIText");
            return (Criteria) this;
        }

        public Criteria andSampleSegegrationWITextNotBetween(String value1, String value2) {
            addCriterion("SampleSegegrationWIText not between", value1, value2, "sampleSegegrationWIText");
            return (Criteria) this;
        }

        public Criteria andLabTeamCodeIsNull() {
            addCriterion("LabTeamCode is null");
            return (Criteria) this;
        }

        public Criteria andLabTeamCodeIsNotNull() {
            addCriterion("LabTeamCode is not null");
            return (Criteria) this;
        }

        public Criteria andLabTeamCodeEqualTo(String value) {
            addCriterion("LabTeamCode =", value, "labTeamCode");
            return (Criteria) this;
        }

        public Criteria andLabTeamCodeNotEqualTo(String value) {
            addCriterion("LabTeamCode <>", value, "labTeamCode");
            return (Criteria) this;
        }

        public Criteria andLabTeamCodeGreaterThan(String value) {
            addCriterion("LabTeamCode >", value, "labTeamCode");
            return (Criteria) this;
        }

        public Criteria andLabTeamCodeGreaterThanOrEqualTo(String value) {
            addCriterion("LabTeamCode >=", value, "labTeamCode");
            return (Criteria) this;
        }

        public Criteria andLabTeamCodeLessThan(String value) {
            addCriterion("LabTeamCode <", value, "labTeamCode");
            return (Criteria) this;
        }

        public Criteria andLabTeamCodeLessThanOrEqualTo(String value) {
            addCriterion("LabTeamCode <=", value, "labTeamCode");
            return (Criteria) this;
        }

        public Criteria andLabTeamCodeLike(String value) {
            addCriterion("LabTeamCode like", value, "labTeamCode");
            return (Criteria) this;
        }

        public Criteria andLabTeamCodeNotLike(String value) {
            addCriterion("LabTeamCode not like", value, "labTeamCode");
            return (Criteria) this;
        }

        public Criteria andLabTeamCodeIn(List<String> values) {
            addCriterion("LabTeamCode in", values, "labTeamCode");
            return (Criteria) this;
        }

        public Criteria andLabTeamCodeNotIn(List<String> values) {
            addCriterion("LabTeamCode not in", values, "labTeamCode");
            return (Criteria) this;
        }

        public Criteria andLabTeamCodeBetween(String value1, String value2) {
            addCriterion("LabTeamCode between", value1, value2, "labTeamCode");
            return (Criteria) this;
        }

        public Criteria andLabTeamCodeNotBetween(String value1, String value2) {
            addCriterion("LabTeamCode not between", value1, value2, "labTeamCode");
            return (Criteria) this;
        }

        public Criteria andPendingFlagIsNull() {
            addCriterion("PendingFlag is null");
            return (Criteria) this;
        }

        public Criteria andPendingFlagIsNotNull() {
            addCriterion("PendingFlag is not null");
            return (Criteria) this;
        }

        public Criteria andPendingFlagEqualTo(Boolean value) {
            addCriterion("PendingFlag =", value, "pendingFlag");
            return (Criteria) this;
        }

        public Criteria andPendingFlagNotEqualTo(Boolean value) {
            addCriterion("PendingFlag <>", value, "pendingFlag");
            return (Criteria) this;
        }

        public Criteria andPendingFlagGreaterThan(Boolean value) {
            addCriterion("PendingFlag >", value, "pendingFlag");
            return (Criteria) this;
        }

        public Criteria andPendingFlagGreaterThanOrEqualTo(Boolean value) {
            addCriterion("PendingFlag >=", value, "pendingFlag");
            return (Criteria) this;
        }

        public Criteria andPendingFlagLessThan(Boolean value) {
            addCriterion("PendingFlag <", value, "pendingFlag");
            return (Criteria) this;
        }

        public Criteria andPendingFlagLessThanOrEqualTo(Boolean value) {
            addCriterion("PendingFlag <=", value, "pendingFlag");
            return (Criteria) this;
        }

        public Criteria andPendingFlagIn(List<Boolean> values) {
            addCriterion("PendingFlag in", values, "pendingFlag");
            return (Criteria) this;
        }

        public Criteria andPendingFlagNotIn(List<Boolean> values) {
            addCriterion("PendingFlag not in", values, "pendingFlag");
            return (Criteria) this;
        }

        public Criteria andPendingFlagBetween(Boolean value1, Boolean value2) {
            addCriterion("PendingFlag between", value1, value2, "pendingFlag");
            return (Criteria) this;
        }

        public Criteria andPendingFlagNotBetween(Boolean value1, Boolean value2) {
            addCriterion("PendingFlag not between", value1, value2, "pendingFlag");
            return (Criteria) this;
        }

        public Criteria andEngineerIsNull() {
            addCriterion("Engineer is null");
            return (Criteria) this;
        }

        public Criteria andEngineerIsNotNull() {
            addCriterion("Engineer is not null");
            return (Criteria) this;
        }

        public Criteria andEngineerEqualTo(String value) {
            addCriterion("Engineer =", value, "engineer");
            return (Criteria) this;
        }

        public Criteria andEngineerNotEqualTo(String value) {
            addCriterion("Engineer <>", value, "engineer");
            return (Criteria) this;
        }

        public Criteria andEngineerGreaterThan(String value) {
            addCriterion("Engineer >", value, "engineer");
            return (Criteria) this;
        }

        public Criteria andEngineerGreaterThanOrEqualTo(String value) {
            addCriterion("Engineer >=", value, "engineer");
            return (Criteria) this;
        }

        public Criteria andEngineerLessThan(String value) {
            addCriterion("Engineer <", value, "engineer");
            return (Criteria) this;
        }

        public Criteria andEngineerLessThanOrEqualTo(String value) {
            addCriterion("Engineer <=", value, "engineer");
            return (Criteria) this;
        }

        public Criteria andEngineerLike(String value) {
            addCriterion("Engineer like", value, "engineer");
            return (Criteria) this;
        }

        public Criteria andEngineerNotLike(String value) {
            addCriterion("Engineer not like", value, "engineer");
            return (Criteria) this;
        }

        public Criteria andEngineerIn(List<String> values) {
            addCriterion("Engineer in", values, "engineer");
            return (Criteria) this;
        }

        public Criteria andEngineerNotIn(List<String> values) {
            addCriterion("Engineer not in", values, "engineer");
            return (Criteria) this;
        }

        public Criteria andEngineerBetween(String value1, String value2) {
            addCriterion("Engineer between", value1, value2, "engineer");
            return (Criteria) this;
        }

        public Criteria andEngineerNotBetween(String value1, String value2) {
            addCriterion("Engineer not between", value1, value2, "engineer");
            return (Criteria) this;
        }

        public Criteria andTestStartDateIsNull() {
            addCriterion("TestStartDate is null");
            return (Criteria) this;
        }

        public Criteria andTestStartDateIsNotNull() {
            addCriterion("TestStartDate is not null");
            return (Criteria) this;
        }

        public Criteria andTestStartDateEqualTo(Date value) {
            addCriterion("TestStartDate =", value, "testStartDate");
            return (Criteria) this;
        }

        public Criteria andTestStartDateNotEqualTo(Date value) {
            addCriterion("TestStartDate <>", value, "testStartDate");
            return (Criteria) this;
        }

        public Criteria andTestStartDateGreaterThan(Date value) {
            addCriterion("TestStartDate >", value, "testStartDate");
            return (Criteria) this;
        }

        public Criteria andTestStartDateGreaterThanOrEqualTo(Date value) {
            addCriterion("TestStartDate >=", value, "testStartDate");
            return (Criteria) this;
        }

        public Criteria andTestStartDateLessThan(Date value) {
            addCriterion("TestStartDate <", value, "testStartDate");
            return (Criteria) this;
        }

        public Criteria andTestStartDateLessThanOrEqualTo(Date value) {
            addCriterion("TestStartDate <=", value, "testStartDate");
            return (Criteria) this;
        }

        public Criteria andTestStartDateIn(List<Date> values) {
            addCriterion("TestStartDate in", values, "testStartDate");
            return (Criteria) this;
        }

        public Criteria andTestStartDateNotIn(List<Date> values) {
            addCriterion("TestStartDate not in", values, "testStartDate");
            return (Criteria) this;
        }

        public Criteria andTestStartDateBetween(Date value1, Date value2) {
            addCriterion("TestStartDate between", value1, value2, "testStartDate");
            return (Criteria) this;
        }

        public Criteria andTestStartDateNotBetween(Date value1, Date value2) {
            addCriterion("TestStartDate not between", value1, value2, "testStartDate");
            return (Criteria) this;
        }

        public Criteria andTestEndDateIsNull() {
            addCriterion("TestEndDate is null");
            return (Criteria) this;
        }

        public Criteria andTestEndDateIsNotNull() {
            addCriterion("TestEndDate is not null");
            return (Criteria) this;
        }

        public Criteria andTestEndDateEqualTo(Date value) {
            addCriterion("TestEndDate =", value, "testEndDate");
            return (Criteria) this;
        }

        public Criteria andTestEndDateNotEqualTo(Date value) {
            addCriterion("TestEndDate <>", value, "testEndDate");
            return (Criteria) this;
        }

        public Criteria andTestEndDateGreaterThan(Date value) {
            addCriterion("TestEndDate >", value, "testEndDate");
            return (Criteria) this;
        }

        public Criteria andTestEndDateGreaterThanOrEqualTo(Date value) {
            addCriterion("TestEndDate >=", value, "testEndDate");
            return (Criteria) this;
        }

        public Criteria andTestEndDateLessThan(Date value) {
            addCriterion("TestEndDate <", value, "testEndDate");
            return (Criteria) this;
        }

        public Criteria andTestEndDateLessThanOrEqualTo(Date value) {
            addCriterion("TestEndDate <=", value, "testEndDate");
            return (Criteria) this;
        }

        public Criteria andTestEndDateIn(List<Date> values) {
            addCriterion("TestEndDate in", values, "testEndDate");
            return (Criteria) this;
        }

        public Criteria andTestEndDateNotIn(List<Date> values) {
            addCriterion("TestEndDate not in", values, "testEndDate");
            return (Criteria) this;
        }

        public Criteria andTestEndDateBetween(Date value1, Date value2) {
            addCriterion("TestEndDate between", value1, value2, "testEndDate");
            return (Criteria) this;
        }

        public Criteria andTestEndDateNotBetween(Date value1, Date value2) {
            addCriterion("TestEndDate not between", value1, value2, "testEndDate");
            return (Criteria) this;
        }

        public Criteria andOrderSeqIsNull() {
            addCriterion("OrderSeq is null");
            return (Criteria) this;
        }

        public Criteria andOrderSeqIsNotNull() {
            addCriterion("OrderSeq is not null");
            return (Criteria) this;
        }

        public Criteria andOrderSeqEqualTo(Integer value) {
            addCriterion("OrderSeq =", value, "orderSeq");
            return (Criteria) this;
        }

        public Criteria andOrderSeqNotEqualTo(Integer value) {
            addCriterion("OrderSeq <>", value, "orderSeq");
            return (Criteria) this;
        }

        public Criteria andOrderSeqGreaterThan(Integer value) {
            addCriterion("OrderSeq >", value, "orderSeq");
            return (Criteria) this;
        }

        public Criteria andOrderSeqGreaterThanOrEqualTo(Integer value) {
            addCriterion("OrderSeq >=", value, "orderSeq");
            return (Criteria) this;
        }

        public Criteria andOrderSeqLessThan(Integer value) {
            addCriterion("OrderSeq <", value, "orderSeq");
            return (Criteria) this;
        }

        public Criteria andOrderSeqLessThanOrEqualTo(Integer value) {
            addCriterion("OrderSeq <=", value, "orderSeq");
            return (Criteria) this;
        }

        public Criteria andOrderSeqIn(List<Integer> values) {
            addCriterion("OrderSeq in", values, "orderSeq");
            return (Criteria) this;
        }

        public Criteria andOrderSeqNotIn(List<Integer> values) {
            addCriterion("OrderSeq not in", values, "orderSeq");
            return (Criteria) this;
        }

        public Criteria andOrderSeqBetween(Integer value1, Integer value2) {
            addCriterion("OrderSeq between", value1, value2, "orderSeq");
            return (Criteria) this;
        }

        public Criteria andOrderSeqNotBetween(Integer value1, Integer value2) {
            addCriterion("OrderSeq not between", value1, value2, "orderSeq");
            return (Criteria) this;
        }

        public Criteria andTestDueDateIsNull() {
            addCriterion("TestDueDate is null");
            return (Criteria) this;
        }

        public Criteria andTestDueDateIsNotNull() {
            addCriterion("TestDueDate is not null");
            return (Criteria) this;
        }

        public Criteria andTestDueDateEqualTo(Date value) {
            addCriterion("TestDueDate =", value, "testDueDate");
            return (Criteria) this;
        }

        public Criteria andTestDueDateNotEqualTo(Date value) {
            addCriterion("TestDueDate <>", value, "testDueDate");
            return (Criteria) this;
        }

        public Criteria andTestDueDateGreaterThan(Date value) {
            addCriterion("TestDueDate >", value, "testDueDate");
            return (Criteria) this;
        }

        public Criteria andTestDueDateGreaterThanOrEqualTo(Date value) {
            addCriterion("TestDueDate >=", value, "testDueDate");
            return (Criteria) this;
        }

        public Criteria andTestDueDateLessThan(Date value) {
            addCriterion("TestDueDate <", value, "testDueDate");
            return (Criteria) this;
        }

        public Criteria andTestDueDateLessThanOrEqualTo(Date value) {
            addCriterion("TestDueDate <=", value, "testDueDate");
            return (Criteria) this;
        }

        public Criteria andTestDueDateIn(List<Date> values) {
            addCriterion("TestDueDate in", values, "testDueDate");
            return (Criteria) this;
        }

        public Criteria andTestDueDateNotIn(List<Date> values) {
            addCriterion("TestDueDate not in", values, "testDueDate");
            return (Criteria) this;
        }

        public Criteria andTestDueDateBetween(Date value1, Date value2) {
            addCriterion("TestDueDate between", value1, value2, "testDueDate");
            return (Criteria) this;
        }

        public Criteria andTestDueDateNotBetween(Date value1, Date value2) {
            addCriterion("TestDueDate not between", value1, value2, "testDueDate");
            return (Criteria) this;
        }

        public Criteria andTestItemNoIsNull() {
            addCriterion("TestItemNo is null");
            return (Criteria) this;
        }

        public Criteria andTestItemNoIsNotNull() {
            addCriterion("TestItemNo is not null");
            return (Criteria) this;
        }

        public Criteria andTestItemNoEqualTo(String value) {
            addCriterion("TestItemNo =", value, "testItemNo");
            return (Criteria) this;
        }

        public Criteria andTestItemNoNotEqualTo(String value) {
            addCriterion("TestItemNo <>", value, "testItemNo");
            return (Criteria) this;
        }

        public Criteria andTestItemNoGreaterThan(String value) {
            addCriterion("TestItemNo >", value, "testItemNo");
            return (Criteria) this;
        }

        public Criteria andTestItemNoGreaterThanOrEqualTo(String value) {
            addCriterion("TestItemNo >=", value, "testItemNo");
            return (Criteria) this;
        }

        public Criteria andTestItemNoLessThan(String value) {
            addCriterion("TestItemNo <", value, "testItemNo");
            return (Criteria) this;
        }

        public Criteria andTestItemNoLessThanOrEqualTo(String value) {
            addCriterion("TestItemNo <=", value, "testItemNo");
            return (Criteria) this;
        }

        public Criteria andTestItemNoLike(String value) {
            addCriterion("TestItemNo like", value, "testItemNo");
            return (Criteria) this;
        }

        public Criteria andTestItemNoNotLike(String value) {
            addCriterion("TestItemNo not like", value, "testItemNo");
            return (Criteria) this;
        }

        public Criteria andTestItemNoIn(List<String> values) {
            addCriterion("TestItemNo in", values, "testItemNo");
            return (Criteria) this;
        }

        public Criteria andTestItemNoNotIn(List<String> values) {
            addCriterion("TestItemNo not in", values, "testItemNo");
            return (Criteria) this;
        }

        public Criteria andTestItemNoBetween(String value1, String value2) {
            addCriterion("TestItemNo between", value1, value2, "testItemNo");
            return (Criteria) this;
        }

        public Criteria andTestItemNoNotBetween(String value1, String value2) {
            addCriterion("TestItemNo not between", value1, value2, "testItemNo");
            return (Criteria) this;
        }

        public Criteria andCustomerTestLineNameIsNull() {
            addCriterion("CustomerTestLineName is null");
            return (Criteria) this;
        }

        public Criteria andCustomerTestLineNameIsNotNull() {
            addCriterion("CustomerTestLineName is not null");
            return (Criteria) this;
        }

        public Criteria andCustomerTestLineNameEqualTo(String value) {
            addCriterion("CustomerTestLineName =", value, "customerTestLineName");
            return (Criteria) this;
        }

        public Criteria andCustomerTestLineNameNotEqualTo(String value) {
            addCriterion("CustomerTestLineName <>", value, "customerTestLineName");
            return (Criteria) this;
        }

        public Criteria andCustomerTestLineNameGreaterThan(String value) {
            addCriterion("CustomerTestLineName >", value, "customerTestLineName");
            return (Criteria) this;
        }

        public Criteria andCustomerTestLineNameGreaterThanOrEqualTo(String value) {
            addCriterion("CustomerTestLineName >=", value, "customerTestLineName");
            return (Criteria) this;
        }

        public Criteria andCustomerTestLineNameLessThan(String value) {
            addCriterion("CustomerTestLineName <", value, "customerTestLineName");
            return (Criteria) this;
        }

        public Criteria andCustomerTestLineNameLessThanOrEqualTo(String value) {
            addCriterion("CustomerTestLineName <=", value, "customerTestLineName");
            return (Criteria) this;
        }

        public Criteria andCustomerTestLineNameLike(String value) {
            addCriterion("CustomerTestLineName like", value, "customerTestLineName");
            return (Criteria) this;
        }

        public Criteria andCustomerTestLineNameNotLike(String value) {
            addCriterion("CustomerTestLineName not like", value, "customerTestLineName");
            return (Criteria) this;
        }

        public Criteria andCustomerTestLineNameIn(List<String> values) {
            addCriterion("CustomerTestLineName in", values, "customerTestLineName");
            return (Criteria) this;
        }

        public Criteria andCustomerTestLineNameNotIn(List<String> values) {
            addCriterion("CustomerTestLineName not in", values, "customerTestLineName");
            return (Criteria) this;
        }

        public Criteria andCustomerTestLineNameBetween(String value1, String value2) {
            addCriterion("CustomerTestLineName between", value1, value2, "customerTestLineName");
            return (Criteria) this;
        }

        public Criteria andCustomerTestLineNameNotBetween(String value1, String value2) {
            addCriterion("CustomerTestLineName not between", value1, value2, "customerTestLineName");
            return (Criteria) this;
        }

        public Criteria andStyleVersionIdIsNull() {
            addCriterion("StyleVersionId is null");
            return (Criteria) this;
        }

        public Criteria andStyleVersionIdIsNotNull() {
            addCriterion("StyleVersionId is not null");
            return (Criteria) this;
        }

        public Criteria andStyleVersionIdEqualTo(Integer value) {
            addCriterion("StyleVersionId =", value, "styleVersionId");
            return (Criteria) this;
        }

        public Criteria andStyleVersionIdNotEqualTo(Integer value) {
            addCriterion("StyleVersionId <>", value, "styleVersionId");
            return (Criteria) this;
        }

        public Criteria andStyleVersionIdGreaterThan(Integer value) {
            addCriterion("StyleVersionId >", value, "styleVersionId");
            return (Criteria) this;
        }

        public Criteria andStyleVersionIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("StyleVersionId >=", value, "styleVersionId");
            return (Criteria) this;
        }

        public Criteria andStyleVersionIdLessThan(Integer value) {
            addCriterion("StyleVersionId <", value, "styleVersionId");
            return (Criteria) this;
        }

        public Criteria andStyleVersionIdLessThanOrEqualTo(Integer value) {
            addCriterion("StyleVersionId <=", value, "styleVersionId");
            return (Criteria) this;
        }

        public Criteria andStyleVersionIdIn(List<Integer> values) {
            addCriterion("StyleVersionId in", values, "styleVersionId");
            return (Criteria) this;
        }

        public Criteria andStyleVersionIdNotIn(List<Integer> values) {
            addCriterion("StyleVersionId not in", values, "styleVersionId");
            return (Criteria) this;
        }

        public Criteria andStyleVersionIdBetween(Integer value1, Integer value2) {
            addCriterion("StyleVersionId between", value1, value2, "styleVersionId");
            return (Criteria) this;
        }

        public Criteria andStyleVersionIdNotBetween(Integer value1, Integer value2) {
            addCriterion("StyleVersionId not between", value1, value2, "styleVersionId");
            return (Criteria) this;
        }

        public Criteria andClientStandardIsNull() {
            addCriterion("ClientStandard is null");
            return (Criteria) this;
        }

        public Criteria andClientStandardIsNotNull() {
            addCriterion("ClientStandard is not null");
            return (Criteria) this;
        }

        public Criteria andClientStandardEqualTo(String value) {
            addCriterion("ClientStandard =", value, "clientStandard");
            return (Criteria) this;
        }

        public Criteria andClientStandardNotEqualTo(String value) {
            addCriterion("ClientStandard <>", value, "clientStandard");
            return (Criteria) this;
        }

        public Criteria andClientStandardGreaterThan(String value) {
            addCriterion("ClientStandard >", value, "clientStandard");
            return (Criteria) this;
        }

        public Criteria andClientStandardGreaterThanOrEqualTo(String value) {
            addCriterion("ClientStandard >=", value, "clientStandard");
            return (Criteria) this;
        }

        public Criteria andClientStandardLessThan(String value) {
            addCriterion("ClientStandard <", value, "clientStandard");
            return (Criteria) this;
        }

        public Criteria andClientStandardLessThanOrEqualTo(String value) {
            addCriterion("ClientStandard <=", value, "clientStandard");
            return (Criteria) this;
        }

        public Criteria andClientStandardLike(String value) {
            addCriterion("ClientStandard like", value, "clientStandard");
            return (Criteria) this;
        }

        public Criteria andClientStandardNotLike(String value) {
            addCriterion("ClientStandard not like", value, "clientStandard");
            return (Criteria) this;
        }

        public Criteria andClientStandardIn(List<String> values) {
            addCriterion("ClientStandard in", values, "clientStandard");
            return (Criteria) this;
        }

        public Criteria andClientStandardNotIn(List<String> values) {
            addCriterion("ClientStandard not in", values, "clientStandard");
            return (Criteria) this;
        }

        public Criteria andClientStandardBetween(String value1, String value2) {
            addCriterion("ClientStandard between", value1, value2, "clientStandard");
            return (Criteria) this;
        }

        public Criteria andClientStandardNotBetween(String value1, String value2) {
            addCriterion("ClientStandard not between", value1, value2, "clientStandard");
            return (Criteria) this;
        }

        public Criteria andCustomerTestLineNameCNIsNull() {
            addCriterion("CustomerTestLineNameCN is null");
            return (Criteria) this;
        }

        public Criteria andCustomerTestLineNameCNIsNotNull() {
            addCriterion("CustomerTestLineNameCN is not null");
            return (Criteria) this;
        }

        public Criteria andCustomerTestLineNameCNEqualTo(String value) {
            addCriterion("CustomerTestLineNameCN =", value, "customerTestLineNameCN");
            return (Criteria) this;
        }

        public Criteria andCustomerTestLineNameCNNotEqualTo(String value) {
            addCriterion("CustomerTestLineNameCN <>", value, "customerTestLineNameCN");
            return (Criteria) this;
        }

        public Criteria andCustomerTestLineNameCNGreaterThan(String value) {
            addCriterion("CustomerTestLineNameCN >", value, "customerTestLineNameCN");
            return (Criteria) this;
        }

        public Criteria andCustomerTestLineNameCNGreaterThanOrEqualTo(String value) {
            addCriterion("CustomerTestLineNameCN >=", value, "customerTestLineNameCN");
            return (Criteria) this;
        }

        public Criteria andCustomerTestLineNameCNLessThan(String value) {
            addCriterion("CustomerTestLineNameCN <", value, "customerTestLineNameCN");
            return (Criteria) this;
        }

        public Criteria andCustomerTestLineNameCNLessThanOrEqualTo(String value) {
            addCriterion("CustomerTestLineNameCN <=", value, "customerTestLineNameCN");
            return (Criteria) this;
        }

        public Criteria andCustomerTestLineNameCNLike(String value) {
            addCriterion("CustomerTestLineNameCN like", value, "customerTestLineNameCN");
            return (Criteria) this;
        }

        public Criteria andCustomerTestLineNameCNNotLike(String value) {
            addCriterion("CustomerTestLineNameCN not like", value, "customerTestLineNameCN");
            return (Criteria) this;
        }

        public Criteria andCustomerTestLineNameCNIn(List<String> values) {
            addCriterion("CustomerTestLineNameCN in", values, "customerTestLineNameCN");
            return (Criteria) this;
        }

        public Criteria andCustomerTestLineNameCNNotIn(List<String> values) {
            addCriterion("CustomerTestLineNameCN not in", values, "customerTestLineNameCN");
            return (Criteria) this;
        }

        public Criteria andCustomerTestLineNameCNBetween(String value1, String value2) {
            addCriterion("CustomerTestLineNameCN between", value1, value2, "customerTestLineNameCN");
            return (Criteria) this;
        }

        public Criteria andCustomerTestLineNameCNNotBetween(String value1, String value2) {
            addCriterion("CustomerTestLineNameCN not between", value1, value2, "customerTestLineNameCN");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}