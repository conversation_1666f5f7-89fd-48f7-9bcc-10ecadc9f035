package com.sgs.otsnotes.dbstorages.mybatis.model;

import java.util.ArrayList;
import java.util.List;

public class PPTestLineAnalyteRelInfoExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public PPTestLineAnalyteRelInfoExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("Id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("Id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("Id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("Id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("Id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("Id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("Id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("Id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("Id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("Id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("Id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("Id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andPpVersionIdIsNull() {
            addCriterion("PpVersionId is null");
            return (Criteria) this;
        }

        public Criteria andPpVersionIdIsNotNull() {
            addCriterion("PpVersionId is not null");
            return (Criteria) this;
        }

        public Criteria andPpVersionIdEqualTo(Integer value) {
            addCriterion("PpVersionId =", value, "ppVersionId");
            return (Criteria) this;
        }

        public Criteria andPpVersionIdNotEqualTo(Integer value) {
            addCriterion("PpVersionId <>", value, "ppVersionId");
            return (Criteria) this;
        }

        public Criteria andPpVersionIdGreaterThan(Integer value) {
            addCriterion("PpVersionId >", value, "ppVersionId");
            return (Criteria) this;
        }

        public Criteria andPpVersionIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("PpVersionId >=", value, "ppVersionId");
            return (Criteria) this;
        }

        public Criteria andPpVersionIdLessThan(Integer value) {
            addCriterion("PpVersionId <", value, "ppVersionId");
            return (Criteria) this;
        }

        public Criteria andPpVersionIdLessThanOrEqualTo(Integer value) {
            addCriterion("PpVersionId <=", value, "ppVersionId");
            return (Criteria) this;
        }

        public Criteria andPpVersionIdIn(List<Integer> values) {
            addCriterion("PpVersionId in", values, "ppVersionId");
            return (Criteria) this;
        }

        public Criteria andPpVersionIdNotIn(List<Integer> values) {
            addCriterion("PpVersionId not in", values, "ppVersionId");
            return (Criteria) this;
        }

        public Criteria andPpVersionIdBetween(Integer value1, Integer value2) {
            addCriterion("PpVersionId between", value1, value2, "ppVersionId");
            return (Criteria) this;
        }

        public Criteria andPpVersionIdNotBetween(Integer value1, Integer value2) {
            addCriterion("PpVersionId not between", value1, value2, "ppVersionId");
            return (Criteria) this;
        }

        public Criteria andArtifactIdIsNull() {
            addCriterion("ArtifactId is null");
            return (Criteria) this;
        }

        public Criteria andArtifactIdIsNotNull() {
            addCriterion("ArtifactId is not null");
            return (Criteria) this;
        }

        public Criteria andArtifactIdEqualTo(Integer value) {
            addCriterion("ArtifactId =", value, "artifactId");
            return (Criteria) this;
        }

        public Criteria andArtifactIdNotEqualTo(Integer value) {
            addCriterion("ArtifactId <>", value, "artifactId");
            return (Criteria) this;
        }

        public Criteria andArtifactIdGreaterThan(Integer value) {
            addCriterion("ArtifactId >", value, "artifactId");
            return (Criteria) this;
        }

        public Criteria andArtifactIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("ArtifactId >=", value, "artifactId");
            return (Criteria) this;
        }

        public Criteria andArtifactIdLessThan(Integer value) {
            addCriterion("ArtifactId <", value, "artifactId");
            return (Criteria) this;
        }

        public Criteria andArtifactIdLessThanOrEqualTo(Integer value) {
            addCriterion("ArtifactId <=", value, "artifactId");
            return (Criteria) this;
        }

        public Criteria andArtifactIdIn(List<Integer> values) {
            addCriterion("ArtifactId in", values, "artifactId");
            return (Criteria) this;
        }

        public Criteria andArtifactIdNotIn(List<Integer> values) {
            addCriterion("ArtifactId not in", values, "artifactId");
            return (Criteria) this;
        }

        public Criteria andArtifactIdBetween(Integer value1, Integer value2) {
            addCriterion("ArtifactId between", value1, value2, "artifactId");
            return (Criteria) this;
        }

        public Criteria andArtifactIdNotBetween(Integer value1, Integer value2) {
            addCriterion("ArtifactId not between", value1, value2, "artifactId");
            return (Criteria) this;
        }

        public Criteria andAnalyteIdIsNull() {
            addCriterion("AnalyteId is null");
            return (Criteria) this;
        }

        public Criteria andAnalyteIdIsNotNull() {
            addCriterion("AnalyteId is not null");
            return (Criteria) this;
        }

        public Criteria andAnalyteIdEqualTo(Long value) {
            addCriterion("AnalyteId =", value, "analyteId");
            return (Criteria) this;
        }

        public Criteria andAnalyteIdNotEqualTo(Long value) {
            addCriterion("AnalyteId <>", value, "analyteId");
            return (Criteria) this;
        }

        public Criteria andAnalyteIdGreaterThan(Long value) {
            addCriterion("AnalyteId >", value, "analyteId");
            return (Criteria) this;
        }

        public Criteria andAnalyteIdGreaterThanOrEqualTo(Long value) {
            addCriterion("AnalyteId >=", value, "analyteId");
            return (Criteria) this;
        }

        public Criteria andAnalyteIdLessThan(Long value) {
            addCriterion("AnalyteId <", value, "analyteId");
            return (Criteria) this;
        }

        public Criteria andAnalyteIdLessThanOrEqualTo(Long value) {
            addCriterion("AnalyteId <=", value, "analyteId");
            return (Criteria) this;
        }

        public Criteria andAnalyteIdIn(List<Long> values) {
            addCriterion("AnalyteId in", values, "analyteId");
            return (Criteria) this;
        }

        public Criteria andAnalyteIdNotIn(List<Long> values) {
            addCriterion("AnalyteId not in", values, "analyteId");
            return (Criteria) this;
        }

        public Criteria andAnalyteIdBetween(Long value1, Long value2) {
            addCriterion("AnalyteId between", value1, value2, "analyteId");
            return (Criteria) this;
        }

        public Criteria andAnalyteIdNotBetween(Long value1, Long value2) {
            addCriterion("AnalyteId not between", value1, value2, "analyteId");
            return (Criteria) this;
        }

        public Criteria andAnalyteSeqIsNull() {
            addCriterion("AnalyteSeq is null");
            return (Criteria) this;
        }

        public Criteria andAnalyteSeqIsNotNull() {
            addCriterion("AnalyteSeq is not null");
            return (Criteria) this;
        }

        public Criteria andAnalyteSeqEqualTo(Integer value) {
            addCriterion("AnalyteSeq =", value, "analyteSeq");
            return (Criteria) this;
        }

        public Criteria andAnalyteSeqNotEqualTo(Integer value) {
            addCriterion("AnalyteSeq <>", value, "analyteSeq");
            return (Criteria) this;
        }

        public Criteria andAnalyteSeqGreaterThan(Integer value) {
            addCriterion("AnalyteSeq >", value, "analyteSeq");
            return (Criteria) this;
        }

        public Criteria andAnalyteSeqGreaterThanOrEqualTo(Integer value) {
            addCriterion("AnalyteSeq >=", value, "analyteSeq");
            return (Criteria) this;
        }

        public Criteria andAnalyteSeqLessThan(Integer value) {
            addCriterion("AnalyteSeq <", value, "analyteSeq");
            return (Criteria) this;
        }

        public Criteria andAnalyteSeqLessThanOrEqualTo(Integer value) {
            addCriterion("AnalyteSeq <=", value, "analyteSeq");
            return (Criteria) this;
        }

        public Criteria andAnalyteSeqIn(List<Integer> values) {
            addCriterion("AnalyteSeq in", values, "analyteSeq");
            return (Criteria) this;
        }

        public Criteria andAnalyteSeqNotIn(List<Integer> values) {
            addCriterion("AnalyteSeq not in", values, "analyteSeq");
            return (Criteria) this;
        }

        public Criteria andAnalyteSeqBetween(Integer value1, Integer value2) {
            addCriterion("AnalyteSeq between", value1, value2, "analyteSeq");
            return (Criteria) this;
        }

        public Criteria andAnalyteSeqNotBetween(Integer value1, Integer value2) {
            addCriterion("AnalyteSeq not between", value1, value2, "analyteSeq");
            return (Criteria) this;
        }

        public Criteria andBizVersionIdIsNull() {
            addCriterion("BizVersionId is null");
            return (Criteria) this;
        }

        public Criteria andBizVersionIdIsNotNull() {
            addCriterion("BizVersionId is not null");
            return (Criteria) this;
        }

        public Criteria andBizVersionIdEqualTo(String value) {
            addCriterion("BizVersionId =", value, "bizVersionId");
            return (Criteria) this;
        }

        public Criteria andBizVersionIdNotEqualTo(String value) {
            addCriterion("BizVersionId <>", value, "bizVersionId");
            return (Criteria) this;
        }

        public Criteria andBizVersionIdGreaterThan(String value) {
            addCriterion("BizVersionId >", value, "bizVersionId");
            return (Criteria) this;
        }

        public Criteria andBizVersionIdGreaterThanOrEqualTo(String value) {
            addCriterion("BizVersionId >=", value, "bizVersionId");
            return (Criteria) this;
        }

        public Criteria andBizVersionIdLessThan(String value) {
            addCriterion("BizVersionId <", value, "bizVersionId");
            return (Criteria) this;
        }

        public Criteria andBizVersionIdLessThanOrEqualTo(String value) {
            addCriterion("BizVersionId <=", value, "bizVersionId");
            return (Criteria) this;
        }

        public Criteria andBizVersionIdLike(String value) {
            addCriterion("BizVersionId like", value, "bizVersionId");
            return (Criteria) this;
        }

        public Criteria andBizVersionIdNotLike(String value) {
            addCriterion("BizVersionId not like", value, "bizVersionId");
            return (Criteria) this;
        }

        public Criteria andBizVersionIdIn(List<String> values) {
            addCriterion("BizVersionId in", values, "bizVersionId");
            return (Criteria) this;
        }

        public Criteria andBizVersionIdNotIn(List<String> values) {
            addCriterion("BizVersionId not in", values, "bizVersionId");
            return (Criteria) this;
        }

        public Criteria andBizVersionIdBetween(String value1, String value2) {
            addCriterion("BizVersionId between", value1, value2, "bizVersionId");
            return (Criteria) this;
        }

        public Criteria andBizVersionIdNotBetween(String value1, String value2) {
            addCriterion("BizVersionId not between", value1, value2, "bizVersionId");
            return (Criteria) this;
        }

        public Criteria andRelStatusIsNull() {
            addCriterion("RelStatus is null");
            return (Criteria) this;
        }

        public Criteria andRelStatusIsNotNull() {
            addCriterion("RelStatus is not null");
            return (Criteria) this;
        }

        public Criteria andRelStatusEqualTo(Integer value) {
            addCriterion("RelStatus =", value, "relStatus");
            return (Criteria) this;
        }

        public Criteria andRelStatusNotEqualTo(Integer value) {
            addCriterion("RelStatus <>", value, "relStatus");
            return (Criteria) this;
        }

        public Criteria andRelStatusGreaterThan(Integer value) {
            addCriterion("RelStatus >", value, "relStatus");
            return (Criteria) this;
        }

        public Criteria andRelStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("RelStatus >=", value, "relStatus");
            return (Criteria) this;
        }

        public Criteria andRelStatusLessThan(Integer value) {
            addCriterion("RelStatus <", value, "relStatus");
            return (Criteria) this;
        }

        public Criteria andRelStatusLessThanOrEqualTo(Integer value) {
            addCriterion("RelStatus <=", value, "relStatus");
            return (Criteria) this;
        }

        public Criteria andRelStatusIn(List<Integer> values) {
            addCriterion("RelStatus in", values, "relStatus");
            return (Criteria) this;
        }

        public Criteria andRelStatusNotIn(List<Integer> values) {
            addCriterion("RelStatus not in", values, "relStatus");
            return (Criteria) this;
        }

        public Criteria andRelStatusBetween(Integer value1, Integer value2) {
            addCriterion("RelStatus between", value1, value2, "relStatus");
            return (Criteria) this;
        }

        public Criteria andRelStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("RelStatus not between", value1, value2, "relStatus");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}