package com.sgs.otsnotes.dbstorages.mybatis.model;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class OrderTestLineVMExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public OrderTestLineVMExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andOrderIdIsNull() {
            addCriterion("orderId is null");
            return (Criteria) this;
        }

        public Criteria andOrderIdIsNotNull() {
            addCriterion("orderId is not null");
            return (Criteria) this;
        }

        public Criteria andOrderIdEqualTo(String value) {
            addCriterion("orderId =", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdNotEqualTo(String value) {
            addCriterion("orderId <>", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdGreaterThan(String value) {
            addCriterion("orderId >", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdGreaterThanOrEqualTo(String value) {
            addCriterion("orderId >=", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdLessThan(String value) {
            addCriterion("orderId <", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdLessThanOrEqualTo(String value) {
            addCriterion("orderId <=", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdLike(String value) {
            addCriterion("orderId like", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdNotLike(String value) {
            addCriterion("orderId not like", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdIn(List<String> values) {
            addCriterion("orderId in", values, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdNotIn(List<String> values) {
            addCriterion("orderId not in", values, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdBetween(String value1, String value2) {
            addCriterion("orderId between", value1, value2, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdNotBetween(String value1, String value2) {
            addCriterion("orderId not between", value1, value2, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderNoIsNull() {
            addCriterion("orderNo is null");
            return (Criteria) this;
        }

        public Criteria andOrderNoIsNotNull() {
            addCriterion("orderNo is not null");
            return (Criteria) this;
        }

        public Criteria andOrderNoEqualTo(String value) {
            addCriterion("orderNo =", value, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoNotEqualTo(String value) {
            addCriterion("orderNo <>", value, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoGreaterThan(String value) {
            addCriterion("orderNo >", value, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoGreaterThanOrEqualTo(String value) {
            addCriterion("orderNo >=", value, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoLessThan(String value) {
            addCriterion("orderNo <", value, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoLessThanOrEqualTo(String value) {
            addCriterion("orderNo <=", value, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoLike(String value) {
            addCriterion("orderNo like", value, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoNotLike(String value) {
            addCriterion("orderNo not like", value, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoIn(List<String> values) {
            addCriterion("orderNo in", values, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoNotIn(List<String> values) {
            addCriterion("orderNo not in", values, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoBetween(String value1, String value2) {
            addCriterion("orderNo between", value1, value2, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoNotBetween(String value1, String value2) {
            addCriterion("orderNo not between", value1, value2, "orderNo");
            return (Criteria) this;
        }

        public Criteria andLabCodeIsNull() {
            addCriterion("labCode is null");
            return (Criteria) this;
        }

        public Criteria andLabCodeIsNotNull() {
            addCriterion("labCode is not null");
            return (Criteria) this;
        }

        public Criteria andLabCodeEqualTo(String value) {
            addCriterion("labCode =", value, "labCode");
            return (Criteria) this;
        }

        public Criteria andLabCodeNotEqualTo(String value) {
            addCriterion("labCode <>", value, "labCode");
            return (Criteria) this;
        }

        public Criteria andLabCodeGreaterThan(String value) {
            addCriterion("labCode >", value, "labCode");
            return (Criteria) this;
        }

        public Criteria andLabCodeGreaterThanOrEqualTo(String value) {
            addCriterion("labCode >=", value, "labCode");
            return (Criteria) this;
        }

        public Criteria andLabCodeLessThan(String value) {
            addCriterion("labCode <", value, "labCode");
            return (Criteria) this;
        }

        public Criteria andLabCodeLessThanOrEqualTo(String value) {
            addCriterion("labCode <=", value, "labCode");
            return (Criteria) this;
        }

        public Criteria andLabCodeLike(String value) {
            addCriterion("labCode like", value, "labCode");
            return (Criteria) this;
        }

        public Criteria andLabCodeNotLike(String value) {
            addCriterion("labCode not like", value, "labCode");
            return (Criteria) this;
        }

        public Criteria andLabCodeIn(List<String> values) {
            addCriterion("labCode in", values, "labCode");
            return (Criteria) this;
        }

        public Criteria andLabCodeNotIn(List<String> values) {
            addCriterion("labCode not in", values, "labCode");
            return (Criteria) this;
        }

        public Criteria andLabCodeBetween(String value1, String value2) {
            addCriterion("labCode between", value1, value2, "labCode");
            return (Criteria) this;
        }

        public Criteria andLabCodeNotBetween(String value1, String value2) {
            addCriterion("labCode not between", value1, value2, "labCode");
            return (Criteria) this;
        }

        public Criteria andPpVersionIdIsNull() {
            addCriterion("ppVersionId is null");
            return (Criteria) this;
        }

        public Criteria andPpVersionIdIsNotNull() {
            addCriterion("ppVersionId is not null");
            return (Criteria) this;
        }

        public Criteria andPpVersionIdEqualTo(Integer value) {
            addCriterion("ppVersionId =", value, "ppVersionId");
            return (Criteria) this;
        }

        public Criteria andPpVersionIdNotEqualTo(Integer value) {
            addCriterion("ppVersionId <>", value, "ppVersionId");
            return (Criteria) this;
        }

        public Criteria andPpVersionIdGreaterThan(Integer value) {
            addCriterion("ppVersionId >", value, "ppVersionId");
            return (Criteria) this;
        }

        public Criteria andPpVersionIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("ppVersionId >=", value, "ppVersionId");
            return (Criteria) this;
        }

        public Criteria andPpVersionIdLessThan(Integer value) {
            addCriterion("ppVersionId <", value, "ppVersionId");
            return (Criteria) this;
        }

        public Criteria andPpVersionIdLessThanOrEqualTo(Integer value) {
            addCriterion("ppVersionId <=", value, "ppVersionId");
            return (Criteria) this;
        }

        public Criteria andPpVersionIdIn(List<Integer> values) {
            addCriterion("ppVersionId in", values, "ppVersionId");
            return (Criteria) this;
        }

        public Criteria andPpVersionIdNotIn(List<Integer> values) {
            addCriterion("ppVersionId not in", values, "ppVersionId");
            return (Criteria) this;
        }

        public Criteria andPpVersionIdBetween(Integer value1, Integer value2) {
            addCriterion("ppVersionId between", value1, value2, "ppVersionId");
            return (Criteria) this;
        }

        public Criteria andPpVersionIdNotBetween(Integer value1, Integer value2) {
            addCriterion("ppVersionId not between", value1, value2, "ppVersionId");
            return (Criteria) this;
        }

        public Criteria andPpBaseIdIsNull() {
            addCriterion("ppBaseId is null");
            return (Criteria) this;
        }

        public Criteria andPpBaseIdIsNotNull() {
            addCriterion("ppBaseId is not null");
            return (Criteria) this;
        }

        public Criteria andPpBaseIdEqualTo(Long value) {
            addCriterion("ppBaseId =", value, "ppBaseId");
            return (Criteria) this;
        }

        public Criteria andPpBaseIdNotEqualTo(Long value) {
            addCriterion("ppBaseId <>", value, "ppBaseId");
            return (Criteria) this;
        }

        public Criteria andPpBaseIdGreaterThan(Long value) {
            addCriterion("ppBaseId >", value, "ppBaseId");
            return (Criteria) this;
        }

        public Criteria andPpBaseIdGreaterThanOrEqualTo(Long value) {
            addCriterion("ppBaseId >=", value, "ppBaseId");
            return (Criteria) this;
        }

        public Criteria andPpBaseIdLessThan(Long value) {
            addCriterion("ppBaseId <", value, "ppBaseId");
            return (Criteria) this;
        }

        public Criteria andPpBaseIdLessThanOrEqualTo(Long value) {
            addCriterion("ppBaseId <=", value, "ppBaseId");
            return (Criteria) this;
        }

        public Criteria andPpBaseIdIn(List<Long> values) {
            addCriterion("ppBaseId in", values, "ppBaseId");
            return (Criteria) this;
        }

        public Criteria andPpBaseIdNotIn(List<Long> values) {
            addCriterion("ppBaseId not in", values, "ppBaseId");
            return (Criteria) this;
        }

        public Criteria andPpBaseIdBetween(Long value1, Long value2) {
            addCriterion("ppBaseId between", value1, value2, "ppBaseId");
            return (Criteria) this;
        }

        public Criteria andPpBaseIdNotBetween(Long value1, Long value2) {
            addCriterion("ppBaseId not between", value1, value2, "ppBaseId");
            return (Criteria) this;
        }

        public Criteria andPpNoIsNull() {
            addCriterion("PpNo is null");
            return (Criteria) this;
        }

        public Criteria andPpNoIsNotNull() {
            addCriterion("PpNo is not null");
            return (Criteria) this;
        }

        public Criteria andPpNoEqualTo(Integer value) {
            addCriterion("PpNo =", value, "ppNo");
            return (Criteria) this;
        }

        public Criteria andPpNoNotEqualTo(Integer value) {
            addCriterion("PpNo <>", value, "ppNo");
            return (Criteria) this;
        }

        public Criteria andPpNoGreaterThan(Integer value) {
            addCriterion("PpNo >", value, "ppNo");
            return (Criteria) this;
        }

        public Criteria andPpNoGreaterThanOrEqualTo(Integer value) {
            addCriterion("PpNo >=", value, "ppNo");
            return (Criteria) this;
        }

        public Criteria andPpNoLessThan(Integer value) {
            addCriterion("PpNo <", value, "ppNo");
            return (Criteria) this;
        }

        public Criteria andPpNoLessThanOrEqualTo(Integer value) {
            addCriterion("PpNo <=", value, "ppNo");
            return (Criteria) this;
        }

        public Criteria andPpNoIn(List<Integer> values) {
            addCriterion("PpNo in", values, "ppNo");
            return (Criteria) this;
        }

        public Criteria andPpNoNotIn(List<Integer> values) {
            addCriterion("PpNo not in", values, "ppNo");
            return (Criteria) this;
        }

        public Criteria andPpNoBetween(Integer value1, Integer value2) {
            addCriterion("PpNo between", value1, value2, "ppNo");
            return (Criteria) this;
        }

        public Criteria andPpNoNotBetween(Integer value1, Integer value2) {
            addCriterion("PpNo not between", value1, value2, "ppNo");
            return (Criteria) this;
        }

        public Criteria andPpNameIsNull() {
            addCriterion("ppName is null");
            return (Criteria) this;
        }

        public Criteria andPpNameIsNotNull() {
            addCriterion("ppName is not null");
            return (Criteria) this;
        }

        public Criteria andPpNameEqualTo(String value) {
            addCriterion("ppName =", value, "ppName");
            return (Criteria) this;
        }

        public Criteria andPpNameNotEqualTo(String value) {
            addCriterion("ppName <>", value, "ppName");
            return (Criteria) this;
        }

        public Criteria andPpNameGreaterThan(String value) {
            addCriterion("ppName >", value, "ppName");
            return (Criteria) this;
        }

        public Criteria andPpNameGreaterThanOrEqualTo(String value) {
            addCriterion("ppName >=", value, "ppName");
            return (Criteria) this;
        }

        public Criteria andPpNameLessThan(String value) {
            addCriterion("ppName <", value, "ppName");
            return (Criteria) this;
        }

        public Criteria andPpNameLessThanOrEqualTo(String value) {
            addCriterion("ppName <=", value, "ppName");
            return (Criteria) this;
        }

        public Criteria andPpNameLike(String value) {
            addCriterion("ppName like", value, "ppName");
            return (Criteria) this;
        }

        public Criteria andPpNameNotLike(String value) {
            addCriterion("ppName not like", value, "ppName");
            return (Criteria) this;
        }

        public Criteria andPpNameIn(List<String> values) {
            addCriterion("ppName in", values, "ppName");
            return (Criteria) this;
        }

        public Criteria andPpNameNotIn(List<String> values) {
            addCriterion("ppName not in", values, "ppName");
            return (Criteria) this;
        }

        public Criteria andPpNameBetween(String value1, String value2) {
            addCriterion("ppName between", value1, value2, "ppName");
            return (Criteria) this;
        }

        public Criteria andPpNameNotBetween(String value1, String value2) {
            addCriterion("ppName not between", value1, value2, "ppName");
            return (Criteria) this;
        }

        public Criteria andPpTlRelIdIsNull() {
            addCriterion("ppTlRelId is null");
            return (Criteria) this;
        }

        public Criteria andPpTlRelIdIsNotNull() {
            addCriterion("ppTlRelId is not null");
            return (Criteria) this;
        }

        public Criteria andPpTlRelIdEqualTo(String value) {
            addCriterion("ppTlRelId =", value, "ppTlRelId");
            return (Criteria) this;
        }

        public Criteria andPpTlRelIdNotEqualTo(String value) {
            addCriterion("ppTlRelId <>", value, "ppTlRelId");
            return (Criteria) this;
        }

        public Criteria andPpTlRelIdGreaterThan(String value) {
            addCriterion("ppTlRelId >", value, "ppTlRelId");
            return (Criteria) this;
        }

        public Criteria andPpTlRelIdGreaterThanOrEqualTo(String value) {
            addCriterion("ppTlRelId >=", value, "ppTlRelId");
            return (Criteria) this;
        }

        public Criteria andPpTlRelIdLessThan(String value) {
            addCriterion("ppTlRelId <", value, "ppTlRelId");
            return (Criteria) this;
        }

        public Criteria andPpTlRelIdLessThanOrEqualTo(String value) {
            addCriterion("ppTlRelId <=", value, "ppTlRelId");
            return (Criteria) this;
        }

        public Criteria andPpTlRelIdLike(String value) {
            addCriterion("ppTlRelId like", value, "ppTlRelId");
            return (Criteria) this;
        }

        public Criteria andPpTlRelIdNotLike(String value) {
            addCriterion("ppTlRelId not like", value, "ppTlRelId");
            return (Criteria) this;
        }

        public Criteria andPpTlRelIdIn(List<String> values) {
            addCriterion("ppTlRelId in", values, "ppTlRelId");
            return (Criteria) this;
        }

        public Criteria andPpTlRelIdNotIn(List<String> values) {
            addCriterion("ppTlRelId not in", values, "ppTlRelId");
            return (Criteria) this;
        }

        public Criteria andPpTlRelIdBetween(String value1, String value2) {
            addCriterion("ppTlRelId between", value1, value2, "ppTlRelId");
            return (Criteria) this;
        }

        public Criteria andPpTlRelIdNotBetween(String value1, String value2) {
            addCriterion("ppTlRelId not between", value1, value2, "ppTlRelId");
            return (Criteria) this;
        }

        public Criteria andSectionNameIsNull() {
            addCriterion("SectionName is null");
            return (Criteria) this;
        }

        public Criteria andSectionNameIsNotNull() {
            addCriterion("SectionName is not null");
            return (Criteria) this;
        }

        public Criteria andSectionNameEqualTo(String value) {
            addCriterion("SectionName =", value, "sectionName");
            return (Criteria) this;
        }

        public Criteria andSectionNameNotEqualTo(String value) {
            addCriterion("SectionName <>", value, "sectionName");
            return (Criteria) this;
        }

        public Criteria andSectionNameGreaterThan(String value) {
            addCriterion("SectionName >", value, "sectionName");
            return (Criteria) this;
        }

        public Criteria andSectionNameGreaterThanOrEqualTo(String value) {
            addCriterion("SectionName >=", value, "sectionName");
            return (Criteria) this;
        }

        public Criteria andSectionNameLessThan(String value) {
            addCriterion("SectionName <", value, "sectionName");
            return (Criteria) this;
        }

        public Criteria andSectionNameLessThanOrEqualTo(String value) {
            addCriterion("SectionName <=", value, "sectionName");
            return (Criteria) this;
        }

        public Criteria andSectionNameLike(String value) {
            addCriterion("SectionName like", value, "sectionName");
            return (Criteria) this;
        }

        public Criteria andSectionNameNotLike(String value) {
            addCriterion("SectionName not like", value, "sectionName");
            return (Criteria) this;
        }

        public Criteria andSectionNameIn(List<String> values) {
            addCriterion("SectionName in", values, "sectionName");
            return (Criteria) this;
        }

        public Criteria andSectionNameNotIn(List<String> values) {
            addCriterion("SectionName not in", values, "sectionName");
            return (Criteria) this;
        }

        public Criteria andSectionNameBetween(String value1, String value2) {
            addCriterion("SectionName between", value1, value2, "sectionName");
            return (Criteria) this;
        }

        public Criteria andSectionNameNotBetween(String value1, String value2) {
            addCriterion("SectionName not between", value1, value2, "sectionName");
            return (Criteria) this;
        }

        public Criteria andTestLineTypeIsNull() {
            addCriterion("testLineType is null");
            return (Criteria) this;
        }

        public Criteria andTestLineTypeIsNotNull() {
            addCriterion("testLineType is not null");
            return (Criteria) this;
        }

        public Criteria andTestLineTypeEqualTo(Integer value) {
            addCriterion("testLineType =", value, "testLineType");
            return (Criteria) this;
        }

        public Criteria andTestLineTypeNotEqualTo(Integer value) {
            addCriterion("testLineType <>", value, "testLineType");
            return (Criteria) this;
        }

        public Criteria andTestLineTypeGreaterThan(Integer value) {
            addCriterion("testLineType >", value, "testLineType");
            return (Criteria) this;
        }

        public Criteria andTestLineTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("testLineType >=", value, "testLineType");
            return (Criteria) this;
        }

        public Criteria andTestLineTypeLessThan(Integer value) {
            addCriterion("testLineType <", value, "testLineType");
            return (Criteria) this;
        }

        public Criteria andTestLineTypeLessThanOrEqualTo(Integer value) {
            addCriterion("testLineType <=", value, "testLineType");
            return (Criteria) this;
        }

        public Criteria andTestLineTypeIn(List<Integer> values) {
            addCriterion("testLineType in", values, "testLineType");
            return (Criteria) this;
        }

        public Criteria andTestLineTypeNotIn(List<Integer> values) {
            addCriterion("testLineType not in", values, "testLineType");
            return (Criteria) this;
        }

        public Criteria andTestLineTypeBetween(Integer value1, Integer value2) {
            addCriterion("testLineType between", value1, value2, "testLineType");
            return (Criteria) this;
        }

        public Criteria andTestLineTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("testLineType not between", value1, value2, "testLineType");
            return (Criteria) this;
        }

        public Criteria andTestLineInstanceIdIsNull() {
            addCriterion("testLineInstanceId is null");
            return (Criteria) this;
        }

        public Criteria andTestLineInstanceIdIsNotNull() {
            addCriterion("testLineInstanceId is not null");
            return (Criteria) this;
        }

        public Criteria andTestLineInstanceIdEqualTo(String value) {
            addCriterion("testLineInstanceId =", value, "testLineInstanceId");
            return (Criteria) this;
        }

        public Criteria andTestLineInstanceIdNotEqualTo(String value) {
            addCriterion("testLineInstanceId <>", value, "testLineInstanceId");
            return (Criteria) this;
        }

        public Criteria andTestLineInstanceIdGreaterThan(String value) {
            addCriterion("testLineInstanceId >", value, "testLineInstanceId");
            return (Criteria) this;
        }

        public Criteria andTestLineInstanceIdGreaterThanOrEqualTo(String value) {
            addCriterion("testLineInstanceId >=", value, "testLineInstanceId");
            return (Criteria) this;
        }

        public Criteria andTestLineInstanceIdLessThan(String value) {
            addCriterion("testLineInstanceId <", value, "testLineInstanceId");
            return (Criteria) this;
        }

        public Criteria andTestLineInstanceIdLessThanOrEqualTo(String value) {
            addCriterion("testLineInstanceId <=", value, "testLineInstanceId");
            return (Criteria) this;
        }

        public Criteria andTestLineInstanceIdLike(String value) {
            addCriterion("testLineInstanceId like", value, "testLineInstanceId");
            return (Criteria) this;
        }

        public Criteria andTestLineInstanceIdNotLike(String value) {
            addCriterion("testLineInstanceId not like", value, "testLineInstanceId");
            return (Criteria) this;
        }

        public Criteria andTestLineInstanceIdIn(List<String> values) {
            addCriterion("testLineInstanceId in", values, "testLineInstanceId");
            return (Criteria) this;
        }

        public Criteria andTestLineInstanceIdNotIn(List<String> values) {
            addCriterion("testLineInstanceId not in", values, "testLineInstanceId");
            return (Criteria) this;
        }

        public Criteria andTestLineInstanceIdBetween(String value1, String value2) {
            addCriterion("testLineInstanceId between", value1, value2, "testLineInstanceId");
            return (Criteria) this;
        }

        public Criteria andTestLineInstanceIdNotBetween(String value1, String value2) {
            addCriterion("testLineInstanceId not between", value1, value2, "testLineInstanceId");
            return (Criteria) this;
        }

        public Criteria andTestItemNoIsNull() {
            addCriterion("TestItemNo is null");
            return (Criteria) this;
        }

        public Criteria andTestItemNoIsNotNull() {
            addCriterion("TestItemNo is not null");
            return (Criteria) this;
        }

        public Criteria andTestItemNoEqualTo(String value) {
            addCriterion("TestItemNo =", value, "testItemNo");
            return (Criteria) this;
        }

        public Criteria andTestItemNoNotEqualTo(String value) {
            addCriterion("TestItemNo <>", value, "testItemNo");
            return (Criteria) this;
        }

        public Criteria andTestItemNoGreaterThan(String value) {
            addCriterion("TestItemNo >", value, "testItemNo");
            return (Criteria) this;
        }

        public Criteria andTestItemNoGreaterThanOrEqualTo(String value) {
            addCriterion("TestItemNo >=", value, "testItemNo");
            return (Criteria) this;
        }

        public Criteria andTestItemNoLessThan(String value) {
            addCriterion("TestItemNo <", value, "testItemNo");
            return (Criteria) this;
        }

        public Criteria andTestItemNoLessThanOrEqualTo(String value) {
            addCriterion("TestItemNo <=", value, "testItemNo");
            return (Criteria) this;
        }

        public Criteria andTestItemNoLike(String value) {
            addCriterion("TestItemNo like", value, "testItemNo");
            return (Criteria) this;
        }

        public Criteria andTestItemNoNotLike(String value) {
            addCriterion("TestItemNo not like", value, "testItemNo");
            return (Criteria) this;
        }

        public Criteria andTestItemNoIn(List<String> values) {
            addCriterion("TestItemNo in", values, "testItemNo");
            return (Criteria) this;
        }

        public Criteria andTestItemNoNotIn(List<String> values) {
            addCriterion("TestItemNo not in", values, "testItemNo");
            return (Criteria) this;
        }

        public Criteria andTestItemNoBetween(String value1, String value2) {
            addCriterion("TestItemNo between", value1, value2, "testItemNo");
            return (Criteria) this;
        }

        public Criteria andTestItemNoNotBetween(String value1, String value2) {
            addCriterion("TestItemNo not between", value1, value2, "testItemNo");
            return (Criteria) this;
        }

        public Criteria andProductLineCodeIsNull() {
            addCriterion("productLineCode is null");
            return (Criteria) this;
        }

        public Criteria andProductLineCodeIsNotNull() {
            addCriterion("productLineCode is not null");
            return (Criteria) this;
        }

        public Criteria andProductLineCodeEqualTo(String value) {
            addCriterion("productLineCode =", value, "productLineCode");
            return (Criteria) this;
        }

        public Criteria andProductLineCodeNotEqualTo(String value) {
            addCriterion("productLineCode <>", value, "productLineCode");
            return (Criteria) this;
        }

        public Criteria andProductLineCodeGreaterThan(String value) {
            addCriterion("productLineCode >", value, "productLineCode");
            return (Criteria) this;
        }

        public Criteria andProductLineCodeGreaterThanOrEqualTo(String value) {
            addCriterion("productLineCode >=", value, "productLineCode");
            return (Criteria) this;
        }

        public Criteria andProductLineCodeLessThan(String value) {
            addCriterion("productLineCode <", value, "productLineCode");
            return (Criteria) this;
        }

        public Criteria andProductLineCodeLessThanOrEqualTo(String value) {
            addCriterion("productLineCode <=", value, "productLineCode");
            return (Criteria) this;
        }

        public Criteria andProductLineCodeLike(String value) {
            addCriterion("productLineCode like", value, "productLineCode");
            return (Criteria) this;
        }

        public Criteria andProductLineCodeNotLike(String value) {
            addCriterion("productLineCode not like", value, "productLineCode");
            return (Criteria) this;
        }

        public Criteria andProductLineCodeIn(List<String> values) {
            addCriterion("productLineCode in", values, "productLineCode");
            return (Criteria) this;
        }

        public Criteria andProductLineCodeNotIn(List<String> values) {
            addCriterion("productLineCode not in", values, "productLineCode");
            return (Criteria) this;
        }

        public Criteria andProductLineCodeBetween(String value1, String value2) {
            addCriterion("productLineCode between", value1, value2, "productLineCode");
            return (Criteria) this;
        }

        public Criteria andProductLineCodeNotBetween(String value1, String value2) {
            addCriterion("productLineCode not between", value1, value2, "productLineCode");
            return (Criteria) this;
        }

        public Criteria andTestLineBaseIdIsNull() {
            addCriterion("testLineBaseId is null");
            return (Criteria) this;
        }

        public Criteria andTestLineBaseIdIsNotNull() {
            addCriterion("testLineBaseId is not null");
            return (Criteria) this;
        }

        public Criteria andTestLineBaseIdEqualTo(Long value) {
            addCriterion("testLineBaseId =", value, "testLineBaseId");
            return (Criteria) this;
        }

        public Criteria andTestLineBaseIdNotEqualTo(Long value) {
            addCriterion("testLineBaseId <>", value, "testLineBaseId");
            return (Criteria) this;
        }

        public Criteria andTestLineBaseIdGreaterThan(Long value) {
            addCriterion("testLineBaseId >", value, "testLineBaseId");
            return (Criteria) this;
        }

        public Criteria andTestLineBaseIdGreaterThanOrEqualTo(Long value) {
            addCriterion("testLineBaseId >=", value, "testLineBaseId");
            return (Criteria) this;
        }

        public Criteria andTestLineBaseIdLessThan(Long value) {
            addCriterion("testLineBaseId <", value, "testLineBaseId");
            return (Criteria) this;
        }

        public Criteria andTestLineBaseIdLessThanOrEqualTo(Long value) {
            addCriterion("testLineBaseId <=", value, "testLineBaseId");
            return (Criteria) this;
        }

        public Criteria andTestLineBaseIdIn(List<Long> values) {
            addCriterion("testLineBaseId in", values, "testLineBaseId");
            return (Criteria) this;
        }

        public Criteria andTestLineBaseIdNotIn(List<Long> values) {
            addCriterion("testLineBaseId not in", values, "testLineBaseId");
            return (Criteria) this;
        }

        public Criteria andTestLineBaseIdBetween(Long value1, Long value2) {
            addCriterion("testLineBaseId between", value1, value2, "testLineBaseId");
            return (Criteria) this;
        }

        public Criteria andTestLineBaseIdNotBetween(Long value1, Long value2) {
            addCriterion("testLineBaseId not between", value1, value2, "testLineBaseId");
            return (Criteria) this;
        }

        public Criteria andTestLineVersionIdIsNull() {
            addCriterion("testLineVersionId is null");
            return (Criteria) this;
        }

        public Criteria andTestLineVersionIdIsNotNull() {
            addCriterion("testLineVersionId is not null");
            return (Criteria) this;
        }

        public Criteria andTestLineVersionIdEqualTo(Integer value) {
            addCriterion("testLineVersionId =", value, "testLineVersionId");
            return (Criteria) this;
        }

        public Criteria andTestLineVersionIdNotEqualTo(Integer value) {
            addCriterion("testLineVersionId <>", value, "testLineVersionId");
            return (Criteria) this;
        }

        public Criteria andTestLineVersionIdGreaterThan(Integer value) {
            addCriterion("testLineVersionId >", value, "testLineVersionId");
            return (Criteria) this;
        }

        public Criteria andTestLineVersionIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("testLineVersionId >=", value, "testLineVersionId");
            return (Criteria) this;
        }

        public Criteria andTestLineVersionIdLessThan(Integer value) {
            addCriterion("testLineVersionId <", value, "testLineVersionId");
            return (Criteria) this;
        }

        public Criteria andTestLineVersionIdLessThanOrEqualTo(Integer value) {
            addCriterion("testLineVersionId <=", value, "testLineVersionId");
            return (Criteria) this;
        }

        public Criteria andTestLineVersionIdIn(List<Integer> values) {
            addCriterion("testLineVersionId in", values, "testLineVersionId");
            return (Criteria) this;
        }

        public Criteria andTestLineVersionIdNotIn(List<Integer> values) {
            addCriterion("testLineVersionId not in", values, "testLineVersionId");
            return (Criteria) this;
        }

        public Criteria andTestLineVersionIdBetween(Integer value1, Integer value2) {
            addCriterion("testLineVersionId between", value1, value2, "testLineVersionId");
            return (Criteria) this;
        }

        public Criteria andTestLineVersionIdNotBetween(Integer value1, Integer value2) {
            addCriterion("testLineVersionId not between", value1, value2, "testLineVersionId");
            return (Criteria) this;
        }

        public Criteria andTestLineIdIsNull() {
            addCriterion("testLineId is null");
            return (Criteria) this;
        }

        public Criteria andTestLineIdIsNotNull() {
            addCriterion("testLineId is not null");
            return (Criteria) this;
        }

        public Criteria andTestLineIdEqualTo(Integer value) {
            addCriterion("testLineId =", value, "testLineId");
            return (Criteria) this;
        }

        public Criteria andTestLineIdNotEqualTo(Integer value) {
            addCriterion("testLineId <>", value, "testLineId");
            return (Criteria) this;
        }

        public Criteria andTestLineIdGreaterThan(Integer value) {
            addCriterion("testLineId >", value, "testLineId");
            return (Criteria) this;
        }

        public Criteria andTestLineIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("testLineId >=", value, "testLineId");
            return (Criteria) this;
        }

        public Criteria andTestLineIdLessThan(Integer value) {
            addCriterion("testLineId <", value, "testLineId");
            return (Criteria) this;
        }

        public Criteria andTestLineIdLessThanOrEqualTo(Integer value) {
            addCriterion("testLineId <=", value, "testLineId");
            return (Criteria) this;
        }

        public Criteria andTestLineIdIn(List<Integer> values) {
            addCriterion("testLineId in", values, "testLineId");
            return (Criteria) this;
        }

        public Criteria andTestLineIdNotIn(List<Integer> values) {
            addCriterion("testLineId not in", values, "testLineId");
            return (Criteria) this;
        }

        public Criteria andTestLineIdBetween(Integer value1, Integer value2) {
            addCriterion("testLineId between", value1, value2, "testLineId");
            return (Criteria) this;
        }

        public Criteria andTestLineIdNotBetween(Integer value1, Integer value2) {
            addCriterion("testLineId not between", value1, value2, "testLineId");
            return (Criteria) this;
        }

        public Criteria andTestItemCnIsNull() {
            addCriterion("testItemCn is null");
            return (Criteria) this;
        }

        public Criteria andTestItemCnIsNotNull() {
            addCriterion("testItemCn is not null");
            return (Criteria) this;
        }

        public Criteria andTestItemCnEqualTo(String value) {
            addCriterion("testItemCn =", value, "testItemCn");
            return (Criteria) this;
        }

        public Criteria andTestItemCnNotEqualTo(String value) {
            addCriterion("testItemCn <>", value, "testItemCn");
            return (Criteria) this;
        }

        public Criteria andTestItemCnGreaterThan(String value) {
            addCriterion("testItemCn >", value, "testItemCn");
            return (Criteria) this;
        }

        public Criteria andTestItemCnGreaterThanOrEqualTo(String value) {
            addCriterion("testItemCn >=", value, "testItemCn");
            return (Criteria) this;
        }

        public Criteria andTestItemCnLessThan(String value) {
            addCriterion("testItemCn <", value, "testItemCn");
            return (Criteria) this;
        }

        public Criteria andTestItemCnLessThanOrEqualTo(String value) {
            addCriterion("testItemCn <=", value, "testItemCn");
            return (Criteria) this;
        }

        public Criteria andTestItemCnLike(String value) {
            addCriterion("testItemCn like", value, "testItemCn");
            return (Criteria) this;
        }

        public Criteria andTestItemCnNotLike(String value) {
            addCriterion("testItemCn not like", value, "testItemCn");
            return (Criteria) this;
        }

        public Criteria andTestItemCnIn(List<String> values) {
            addCriterion("testItemCn in", values, "testItemCn");
            return (Criteria) this;
        }

        public Criteria andTestItemCnNotIn(List<String> values) {
            addCriterion("testItemCn not in", values, "testItemCn");
            return (Criteria) this;
        }

        public Criteria andTestItemCnBetween(String value1, String value2) {
            addCriterion("testItemCn between", value1, value2, "testItemCn");
            return (Criteria) this;
        }

        public Criteria andTestItemCnNotBetween(String value1, String value2) {
            addCriterion("testItemCn not between", value1, value2, "testItemCn");
            return (Criteria) this;
        }

        public Criteria andCustomerTestLineNameIsNull() {
            addCriterion("customerTestLineName is null");
            return (Criteria) this;
        }

        public Criteria andCustomerTestLineNameIsNotNull() {
            addCriterion("customerTestLineName is not null");
            return (Criteria) this;
        }

        public Criteria andCustomerTestLineNameEqualTo(String value) {
            addCriterion("customerTestLineName =", value, "customerTestLineName");
            return (Criteria) this;
        }

        public Criteria andCustomerTestLineNameNotEqualTo(String value) {
            addCriterion("customerTestLineName <>", value, "customerTestLineName");
            return (Criteria) this;
        }

        public Criteria andCustomerTestLineNameGreaterThan(String value) {
            addCriterion("customerTestLineName >", value, "customerTestLineName");
            return (Criteria) this;
        }

        public Criteria andCustomerTestLineNameGreaterThanOrEqualTo(String value) {
            addCriterion("customerTestLineName >=", value, "customerTestLineName");
            return (Criteria) this;
        }

        public Criteria andCustomerTestLineNameLessThan(String value) {
            addCriterion("customerTestLineName <", value, "customerTestLineName");
            return (Criteria) this;
        }

        public Criteria andCustomerTestLineNameLessThanOrEqualTo(String value) {
            addCriterion("customerTestLineName <=", value, "customerTestLineName");
            return (Criteria) this;
        }

        public Criteria andCustomerTestLineNameLike(String value) {
            addCriterion("customerTestLineName like", value, "customerTestLineName");
            return (Criteria) this;
        }

        public Criteria andCustomerTestLineNameNotLike(String value) {
            addCriterion("customerTestLineName not like", value, "customerTestLineName");
            return (Criteria) this;
        }

        public Criteria andCustomerTestLineNameIn(List<String> values) {
            addCriterion("customerTestLineName in", values, "customerTestLineName");
            return (Criteria) this;
        }

        public Criteria andCustomerTestLineNameNotIn(List<String> values) {
            addCriterion("customerTestLineName not in", values, "customerTestLineName");
            return (Criteria) this;
        }

        public Criteria andCustomerTestLineNameBetween(String value1, String value2) {
            addCriterion("customerTestLineName between", value1, value2, "customerTestLineName");
            return (Criteria) this;
        }

        public Criteria andCustomerTestLineNameNotBetween(String value1, String value2) {
            addCriterion("customerTestLineName not between", value1, value2, "customerTestLineName");
            return (Criteria) this;
        }

        public Criteria andTestStandardIsNull() {
            addCriterion("testStandard is null");
            return (Criteria) this;
        }

        public Criteria andTestStandardIsNotNull() {
            addCriterion("testStandard is not null");
            return (Criteria) this;
        }

        public Criteria andTestStandardEqualTo(String value) {
            addCriterion("testStandard =", value, "testStandard");
            return (Criteria) this;
        }

        public Criteria andTestStandardNotEqualTo(String value) {
            addCriterion("testStandard <>", value, "testStandard");
            return (Criteria) this;
        }

        public Criteria andTestStandardGreaterThan(String value) {
            addCriterion("testStandard >", value, "testStandard");
            return (Criteria) this;
        }

        public Criteria andTestStandardGreaterThanOrEqualTo(String value) {
            addCriterion("testStandard >=", value, "testStandard");
            return (Criteria) this;
        }

        public Criteria andTestStandardLessThan(String value) {
            addCriterion("testStandard <", value, "testStandard");
            return (Criteria) this;
        }

        public Criteria andTestStandardLessThanOrEqualTo(String value) {
            addCriterion("testStandard <=", value, "testStandard");
            return (Criteria) this;
        }

        public Criteria andTestStandardLike(String value) {
            addCriterion("testStandard like", value, "testStandard");
            return (Criteria) this;
        }

        public Criteria andTestStandardNotLike(String value) {
            addCriterion("testStandard not like", value, "testStandard");
            return (Criteria) this;
        }

        public Criteria andTestStandardIn(List<String> values) {
            addCriterion("testStandard in", values, "testStandard");
            return (Criteria) this;
        }

        public Criteria andTestStandardNotIn(List<String> values) {
            addCriterion("testStandard not in", values, "testStandard");
            return (Criteria) this;
        }

        public Criteria andTestStandardBetween(String value1, String value2) {
            addCriterion("testStandard between", value1, value2, "testStandard");
            return (Criteria) this;
        }

        public Criteria andTestStandardNotBetween(String value1, String value2) {
            addCriterion("testStandard not between", value1, value2, "testStandard");
            return (Criteria) this;
        }

        public Criteria andCitationSectionNameIsNull() {
            addCriterion("citationSectionName is null");
            return (Criteria) this;
        }

        public Criteria andCitationSectionNameIsNotNull() {
            addCriterion("citationSectionName is not null");
            return (Criteria) this;
        }

        public Criteria andCitationSectionNameEqualTo(String value) {
            addCriterion("citationSectionName =", value, "citationSectionName");
            return (Criteria) this;
        }

        public Criteria andCitationSectionNameNotEqualTo(String value) {
            addCriterion("citationSectionName <>", value, "citationSectionName");
            return (Criteria) this;
        }

        public Criteria andCitationSectionNameGreaterThan(String value) {
            addCriterion("citationSectionName >", value, "citationSectionName");
            return (Criteria) this;
        }

        public Criteria andCitationSectionNameGreaterThanOrEqualTo(String value) {
            addCriterion("citationSectionName >=", value, "citationSectionName");
            return (Criteria) this;
        }

        public Criteria andCitationSectionNameLessThan(String value) {
            addCriterion("citationSectionName <", value, "citationSectionName");
            return (Criteria) this;
        }

        public Criteria andCitationSectionNameLessThanOrEqualTo(String value) {
            addCriterion("citationSectionName <=", value, "citationSectionName");
            return (Criteria) this;
        }

        public Criteria andCitationSectionNameLike(String value) {
            addCriterion("citationSectionName like", value, "citationSectionName");
            return (Criteria) this;
        }

        public Criteria andCitationSectionNameNotLike(String value) {
            addCriterion("citationSectionName not like", value, "citationSectionName");
            return (Criteria) this;
        }

        public Criteria andCitationSectionNameIn(List<String> values) {
            addCriterion("citationSectionName in", values, "citationSectionName");
            return (Criteria) this;
        }

        public Criteria andCitationSectionNameNotIn(List<String> values) {
            addCriterion("citationSectionName not in", values, "citationSectionName");
            return (Criteria) this;
        }

        public Criteria andCitationSectionNameBetween(String value1, String value2) {
            addCriterion("citationSectionName between", value1, value2, "citationSectionName");
            return (Criteria) this;
        }

        public Criteria andCitationSectionNameNotBetween(String value1, String value2) {
            addCriterion("citationSectionName not between", value1, value2, "citationSectionName");
            return (Criteria) this;
        }

        public Criteria andArtifactVersionIdIsNull() {
            addCriterion("artifactVersionId is null");
            return (Criteria) this;
        }

        public Criteria andArtifactVersionIdIsNotNull() {
            addCriterion("artifactVersionId is not null");
            return (Criteria) this;
        }

        public Criteria andArtifactVersionIdEqualTo(Integer value) {
            addCriterion("artifactVersionId =", value, "artifactVersionId");
            return (Criteria) this;
        }

        public Criteria andArtifactVersionIdNotEqualTo(Integer value) {
            addCriterion("artifactVersionId <>", value, "artifactVersionId");
            return (Criteria) this;
        }

        public Criteria andArtifactVersionIdGreaterThan(Integer value) {
            addCriterion("artifactVersionId >", value, "artifactVersionId");
            return (Criteria) this;
        }

        public Criteria andArtifactVersionIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("artifactVersionId >=", value, "artifactVersionId");
            return (Criteria) this;
        }

        public Criteria andArtifactVersionIdLessThan(Integer value) {
            addCriterion("artifactVersionId <", value, "artifactVersionId");
            return (Criteria) this;
        }

        public Criteria andArtifactVersionIdLessThanOrEqualTo(Integer value) {
            addCriterion("artifactVersionId <=", value, "artifactVersionId");
            return (Criteria) this;
        }

        public Criteria andArtifactVersionIdIn(List<Integer> values) {
            addCriterion("artifactVersionId in", values, "artifactVersionId");
            return (Criteria) this;
        }

        public Criteria andArtifactVersionIdNotIn(List<Integer> values) {
            addCriterion("artifactVersionId not in", values, "artifactVersionId");
            return (Criteria) this;
        }

        public Criteria andArtifactVersionIdBetween(Integer value1, Integer value2) {
            addCriterion("artifactVersionId between", value1, value2, "artifactVersionId");
            return (Criteria) this;
        }

        public Criteria andArtifactVersionIdNotBetween(Integer value1, Integer value2) {
            addCriterion("artifactVersionId not between", value1, value2, "artifactVersionId");
            return (Criteria) this;
        }

        public Criteria andTestStandardCnIsNull() {
            addCriterion("testStandardCn is null");
            return (Criteria) this;
        }

        public Criteria andTestStandardCnIsNotNull() {
            addCriterion("testStandardCn is not null");
            return (Criteria) this;
        }

        public Criteria andTestStandardCnEqualTo(String value) {
            addCriterion("testStandardCn =", value, "testStandardCn");
            return (Criteria) this;
        }

        public Criteria andTestStandardCnNotEqualTo(String value) {
            addCriterion("testStandardCn <>", value, "testStandardCn");
            return (Criteria) this;
        }

        public Criteria andTestStandardCnGreaterThan(String value) {
            addCriterion("testStandardCn >", value, "testStandardCn");
            return (Criteria) this;
        }

        public Criteria andTestStandardCnGreaterThanOrEqualTo(String value) {
            addCriterion("testStandardCn >=", value, "testStandardCn");
            return (Criteria) this;
        }

        public Criteria andTestStandardCnLessThan(String value) {
            addCriterion("testStandardCn <", value, "testStandardCn");
            return (Criteria) this;
        }

        public Criteria andTestStandardCnLessThanOrEqualTo(String value) {
            addCriterion("testStandardCn <=", value, "testStandardCn");
            return (Criteria) this;
        }

        public Criteria andTestStandardCnLike(String value) {
            addCriterion("testStandardCn like", value, "testStandardCn");
            return (Criteria) this;
        }

        public Criteria andTestStandardCnNotLike(String value) {
            addCriterion("testStandardCn not like", value, "testStandardCn");
            return (Criteria) this;
        }

        public Criteria andTestStandardCnIn(List<String> values) {
            addCriterion("testStandardCn in", values, "testStandardCn");
            return (Criteria) this;
        }

        public Criteria andTestStandardCnNotIn(List<String> values) {
            addCriterion("testStandardCn not in", values, "testStandardCn");
            return (Criteria) this;
        }

        public Criteria andTestStandardCnBetween(String value1, String value2) {
            addCriterion("testStandardCn between", value1, value2, "testStandardCn");
            return (Criteria) this;
        }

        public Criteria andTestStandardCnNotBetween(String value1, String value2) {
            addCriterion("testStandardCn not between", value1, value2, "testStandardCn");
            return (Criteria) this;
        }

        public Criteria andCitationSectionNameCnIsNull() {
            addCriterion("citationSectionNameCn is null");
            return (Criteria) this;
        }

        public Criteria andCitationSectionNameCnIsNotNull() {
            addCriterion("citationSectionNameCn is not null");
            return (Criteria) this;
        }

        public Criteria andCitationSectionNameCnEqualTo(String value) {
            addCriterion("citationSectionNameCn =", value, "citationSectionNameCn");
            return (Criteria) this;
        }

        public Criteria andCitationSectionNameCnNotEqualTo(String value) {
            addCriterion("citationSectionNameCn <>", value, "citationSectionNameCn");
            return (Criteria) this;
        }

        public Criteria andCitationSectionNameCnGreaterThan(String value) {
            addCriterion("citationSectionNameCn >", value, "citationSectionNameCn");
            return (Criteria) this;
        }

        public Criteria andCitationSectionNameCnGreaterThanOrEqualTo(String value) {
            addCriterion("citationSectionNameCn >=", value, "citationSectionNameCn");
            return (Criteria) this;
        }

        public Criteria andCitationSectionNameCnLessThan(String value) {
            addCriterion("citationSectionNameCn <", value, "citationSectionNameCn");
            return (Criteria) this;
        }

        public Criteria andCitationSectionNameCnLessThanOrEqualTo(String value) {
            addCriterion("citationSectionNameCn <=", value, "citationSectionNameCn");
            return (Criteria) this;
        }

        public Criteria andCitationSectionNameCnLike(String value) {
            addCriterion("citationSectionNameCn like", value, "citationSectionNameCn");
            return (Criteria) this;
        }

        public Criteria andCitationSectionNameCnNotLike(String value) {
            addCriterion("citationSectionNameCn not like", value, "citationSectionNameCn");
            return (Criteria) this;
        }

        public Criteria andCitationSectionNameCnIn(List<String> values) {
            addCriterion("citationSectionNameCn in", values, "citationSectionNameCn");
            return (Criteria) this;
        }

        public Criteria andCitationSectionNameCnNotIn(List<String> values) {
            addCriterion("citationSectionNameCn not in", values, "citationSectionNameCn");
            return (Criteria) this;
        }

        public Criteria andCitationSectionNameCnBetween(String value1, String value2) {
            addCriterion("citationSectionNameCn between", value1, value2, "citationSectionNameCn");
            return (Criteria) this;
        }

        public Criteria andCitationSectionNameCnNotBetween(String value1, String value2) {
            addCriterion("citationSectionNameCn not between", value1, value2, "citationSectionNameCn");
            return (Criteria) this;
        }

        public Criteria andRemarkIsNull() {
            addCriterion("remark is null");
            return (Criteria) this;
        }

        public Criteria andRemarkIsNotNull() {
            addCriterion("remark is not null");
            return (Criteria) this;
        }

        public Criteria andRemarkEqualTo(String value) {
            addCriterion("remark =", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotEqualTo(String value) {
            addCriterion("remark <>", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkGreaterThan(String value) {
            addCriterion("remark >", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkGreaterThanOrEqualTo(String value) {
            addCriterion("remark >=", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkLessThan(String value) {
            addCriterion("remark <", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkLessThanOrEqualTo(String value) {
            addCriterion("remark <=", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkLike(String value) {
            addCriterion("remark like", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotLike(String value) {
            addCriterion("remark not like", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkIn(List<String> values) {
            addCriterion("remark in", values, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotIn(List<String> values) {
            addCriterion("remark not in", values, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkBetween(String value1, String value2) {
            addCriterion("remark between", value1, value2, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotBetween(String value1, String value2) {
            addCriterion("remark not between", value1, value2, "remark");
            return (Criteria) this;
        }

        public Criteria andCitationBaseIdIsNull() {
            addCriterion("citationBaseId is null");
            return (Criteria) this;
        }

        public Criteria andCitationBaseIdIsNotNull() {
            addCriterion("citationBaseId is not null");
            return (Criteria) this;
        }

        public Criteria andCitationBaseIdEqualTo(Long value) {
            addCriterion("citationBaseId =", value, "citationBaseId");
            return (Criteria) this;
        }

        public Criteria andCitationBaseIdNotEqualTo(Long value) {
            addCriterion("citationBaseId <>", value, "citationBaseId");
            return (Criteria) this;
        }

        public Criteria andCitationBaseIdGreaterThan(Long value) {
            addCriterion("citationBaseId >", value, "citationBaseId");
            return (Criteria) this;
        }

        public Criteria andCitationBaseIdGreaterThanOrEqualTo(Long value) {
            addCriterion("citationBaseId >=", value, "citationBaseId");
            return (Criteria) this;
        }

        public Criteria andCitationBaseIdLessThan(Long value) {
            addCriterion("citationBaseId <", value, "citationBaseId");
            return (Criteria) this;
        }

        public Criteria andCitationBaseIdLessThanOrEqualTo(Long value) {
            addCriterion("citationBaseId <=", value, "citationBaseId");
            return (Criteria) this;
        }

        public Criteria andCitationBaseIdIn(List<Long> values) {
            addCriterion("citationBaseId in", values, "citationBaseId");
            return (Criteria) this;
        }

        public Criteria andCitationBaseIdNotIn(List<Long> values) {
            addCriterion("citationBaseId not in", values, "citationBaseId");
            return (Criteria) this;
        }

        public Criteria andCitationBaseIdBetween(Long value1, Long value2) {
            addCriterion("citationBaseId between", value1, value2, "citationBaseId");
            return (Criteria) this;
        }

        public Criteria andCitationBaseIdNotBetween(Long value1, Long value2) {
            addCriterion("citationBaseId not between", value1, value2, "citationBaseId");
            return (Criteria) this;
        }

        public Criteria andCitationIdIsNull() {
            addCriterion("citationId is null");
            return (Criteria) this;
        }

        public Criteria andCitationIdIsNotNull() {
            addCriterion("citationId is not null");
            return (Criteria) this;
        }

        public Criteria andCitationIdEqualTo(Integer value) {
            addCriterion("citationId =", value, "citationId");
            return (Criteria) this;
        }

        public Criteria andCitationIdNotEqualTo(Integer value) {
            addCriterion("citationId <>", value, "citationId");
            return (Criteria) this;
        }

        public Criteria andCitationIdGreaterThan(Integer value) {
            addCriterion("citationId >", value, "citationId");
            return (Criteria) this;
        }

        public Criteria andCitationIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("citationId >=", value, "citationId");
            return (Criteria) this;
        }

        public Criteria andCitationIdLessThan(Integer value) {
            addCriterion("citationId <", value, "citationId");
            return (Criteria) this;
        }

        public Criteria andCitationIdLessThanOrEqualTo(Integer value) {
            addCriterion("citationId <=", value, "citationId");
            return (Criteria) this;
        }

        public Criteria andCitationIdIn(List<Integer> values) {
            addCriterion("citationId in", values, "citationId");
            return (Criteria) this;
        }

        public Criteria andCitationIdNotIn(List<Integer> values) {
            addCriterion("citationId not in", values, "citationId");
            return (Criteria) this;
        }

        public Criteria andCitationIdBetween(Integer value1, Integer value2) {
            addCriterion("citationId between", value1, value2, "citationId");
            return (Criteria) this;
        }

        public Criteria andCitationIdNotBetween(Integer value1, Integer value2) {
            addCriterion("citationId not between", value1, value2, "citationId");
            return (Criteria) this;
        }

        public Criteria andCitationVersionIdIsNull() {
            addCriterion("citationVersionId is null");
            return (Criteria) this;
        }

        public Criteria andCitationVersionIdIsNotNull() {
            addCriterion("citationVersionId is not null");
            return (Criteria) this;
        }

        public Criteria andCitationVersionIdEqualTo(Integer value) {
            addCriterion("citationVersionId =", value, "citationVersionId");
            return (Criteria) this;
        }

        public Criteria andCitationVersionIdNotEqualTo(Integer value) {
            addCriterion("citationVersionId <>", value, "citationVersionId");
            return (Criteria) this;
        }

        public Criteria andCitationVersionIdGreaterThan(Integer value) {
            addCriterion("citationVersionId >", value, "citationVersionId");
            return (Criteria) this;
        }

        public Criteria andCitationVersionIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("citationVersionId >=", value, "citationVersionId");
            return (Criteria) this;
        }

        public Criteria andCitationVersionIdLessThan(Integer value) {
            addCriterion("citationVersionId <", value, "citationVersionId");
            return (Criteria) this;
        }

        public Criteria andCitationVersionIdLessThanOrEqualTo(Integer value) {
            addCriterion("citationVersionId <=", value, "citationVersionId");
            return (Criteria) this;
        }

        public Criteria andCitationVersionIdIn(List<Integer> values) {
            addCriterion("citationVersionId in", values, "citationVersionId");
            return (Criteria) this;
        }

        public Criteria andCitationVersionIdNotIn(List<Integer> values) {
            addCriterion("citationVersionId not in", values, "citationVersionId");
            return (Criteria) this;
        }

        public Criteria andCitationVersionIdBetween(Integer value1, Integer value2) {
            addCriterion("citationVersionId between", value1, value2, "citationVersionId");
            return (Criteria) this;
        }

        public Criteria andCitationVersionIdNotBetween(Integer value1, Integer value2) {
            addCriterion("citationVersionId not between", value1, value2, "citationVersionId");
            return (Criteria) this;
        }

        public Criteria andConditionStatusIsNull() {
            addCriterion("conditionStatus is null");
            return (Criteria) this;
        }

        public Criteria andConditionStatusIsNotNull() {
            addCriterion("conditionStatus is not null");
            return (Criteria) this;
        }

        public Criteria andConditionStatusEqualTo(Integer value) {
            addCriterion("conditionStatus =", value, "conditionStatus");
            return (Criteria) this;
        }

        public Criteria andConditionStatusNotEqualTo(Integer value) {
            addCriterion("conditionStatus <>", value, "conditionStatus");
            return (Criteria) this;
        }

        public Criteria andConditionStatusGreaterThan(Integer value) {
            addCriterion("conditionStatus >", value, "conditionStatus");
            return (Criteria) this;
        }

        public Criteria andConditionStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("conditionStatus >=", value, "conditionStatus");
            return (Criteria) this;
        }

        public Criteria andConditionStatusLessThan(Integer value) {
            addCriterion("conditionStatus <", value, "conditionStatus");
            return (Criteria) this;
        }

        public Criteria andConditionStatusLessThanOrEqualTo(Integer value) {
            addCriterion("conditionStatus <=", value, "conditionStatus");
            return (Criteria) this;
        }

        public Criteria andConditionStatusIn(List<Integer> values) {
            addCriterion("conditionStatus in", values, "conditionStatus");
            return (Criteria) this;
        }

        public Criteria andConditionStatusNotIn(List<Integer> values) {
            addCriterion("conditionStatus not in", values, "conditionStatus");
            return (Criteria) this;
        }

        public Criteria andConditionStatusBetween(Integer value1, Integer value2) {
            addCriterion("conditionStatus between", value1, value2, "conditionStatus");
            return (Criteria) this;
        }

        public Criteria andConditionStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("conditionStatus not between", value1, value2, "conditionStatus");
            return (Criteria) this;
        }

        public Criteria andLabSectionBaseIdIsNull() {
            addCriterion("labSectionBaseId is null");
            return (Criteria) this;
        }

        public Criteria andLabSectionBaseIdIsNotNull() {
            addCriterion("labSectionBaseId is not null");
            return (Criteria) this;
        }

        public Criteria andLabSectionBaseIdEqualTo(Long value) {
            addCriterion("labSectionBaseId =", value, "labSectionBaseId");
            return (Criteria) this;
        }

        public Criteria andLabSectionBaseIdNotEqualTo(Long value) {
            addCriterion("labSectionBaseId <>", value, "labSectionBaseId");
            return (Criteria) this;
        }

        public Criteria andLabSectionBaseIdGreaterThan(Long value) {
            addCriterion("labSectionBaseId >", value, "labSectionBaseId");
            return (Criteria) this;
        }

        public Criteria andLabSectionBaseIdGreaterThanOrEqualTo(Long value) {
            addCriterion("labSectionBaseId >=", value, "labSectionBaseId");
            return (Criteria) this;
        }

        public Criteria andLabSectionBaseIdLessThan(Long value) {
            addCriterion("labSectionBaseId <", value, "labSectionBaseId");
            return (Criteria) this;
        }

        public Criteria andLabSectionBaseIdLessThanOrEqualTo(Long value) {
            addCriterion("labSectionBaseId <=", value, "labSectionBaseId");
            return (Criteria) this;
        }

        public Criteria andLabSectionBaseIdIn(List<Long> values) {
            addCriterion("labSectionBaseId in", values, "labSectionBaseId");
            return (Criteria) this;
        }

        public Criteria andLabSectionBaseIdNotIn(List<Long> values) {
            addCriterion("labSectionBaseId not in", values, "labSectionBaseId");
            return (Criteria) this;
        }

        public Criteria andLabSectionBaseIdBetween(Long value1, Long value2) {
            addCriterion("labSectionBaseId between", value1, value2, "labSectionBaseId");
            return (Criteria) this;
        }

        public Criteria andLabSectionBaseIdNotBetween(Long value1, Long value2) {
            addCriterion("labSectionBaseId not between", value1, value2, "labSectionBaseId");
            return (Criteria) this;
        }

        public Criteria andLabsectionbaseIsNull() {
            addCriterion("labsectionbase is null");
            return (Criteria) this;
        }

        public Criteria andLabsectionbaseIsNotNull() {
            addCriterion("labsectionbase is not null");
            return (Criteria) this;
        }

        public Criteria andLabsectionbaseEqualTo(Integer value) {
            addCriterion("labsectionbase =", value, "labsectionbase");
            return (Criteria) this;
        }

        public Criteria andLabsectionbaseNotEqualTo(Integer value) {
            addCriterion("labsectionbase <>", value, "labsectionbase");
            return (Criteria) this;
        }

        public Criteria andLabsectionbaseGreaterThan(Integer value) {
            addCriterion("labsectionbase >", value, "labsectionbase");
            return (Criteria) this;
        }

        public Criteria andLabsectionbaseGreaterThanOrEqualTo(Integer value) {
            addCriterion("labsectionbase >=", value, "labsectionbase");
            return (Criteria) this;
        }

        public Criteria andLabsectionbaseLessThan(Integer value) {
            addCriterion("labsectionbase <", value, "labsectionbase");
            return (Criteria) this;
        }

        public Criteria andLabsectionbaseLessThanOrEqualTo(Integer value) {
            addCriterion("labsectionbase <=", value, "labsectionbase");
            return (Criteria) this;
        }

        public Criteria andLabsectionbaseIn(List<Integer> values) {
            addCriterion("labsectionbase in", values, "labsectionbase");
            return (Criteria) this;
        }

        public Criteria andLabsectionbaseNotIn(List<Integer> values) {
            addCriterion("labsectionbase not in", values, "labsectionbase");
            return (Criteria) this;
        }

        public Criteria andLabsectionbaseBetween(Integer value1, Integer value2) {
            addCriterion("labsectionbase between", value1, value2, "labsectionbase");
            return (Criteria) this;
        }

        public Criteria andLabsectionbaseNotBetween(Integer value1, Integer value2) {
            addCriterion("labsectionbase not between", value1, value2, "labsectionbase");
            return (Criteria) this;
        }

        public Criteria andLabTeamCodeIsNull() {
            addCriterion("labTeamCode is null");
            return (Criteria) this;
        }

        public Criteria andLabTeamCodeIsNotNull() {
            addCriterion("labTeamCode is not null");
            return (Criteria) this;
        }

        public Criteria andLabTeamCodeEqualTo(String value) {
            addCriterion("labTeamCode =", value, "labTeamCode");
            return (Criteria) this;
        }

        public Criteria andLabTeamCodeNotEqualTo(String value) {
            addCriterion("labTeamCode <>", value, "labTeamCode");
            return (Criteria) this;
        }

        public Criteria andLabTeamCodeGreaterThan(String value) {
            addCriterion("labTeamCode >", value, "labTeamCode");
            return (Criteria) this;
        }

        public Criteria andLabTeamCodeGreaterThanOrEqualTo(String value) {
            addCriterion("labTeamCode >=", value, "labTeamCode");
            return (Criteria) this;
        }

        public Criteria andLabTeamCodeLessThan(String value) {
            addCriterion("labTeamCode <", value, "labTeamCode");
            return (Criteria) this;
        }

        public Criteria andLabTeamCodeLessThanOrEqualTo(String value) {
            addCriterion("labTeamCode <=", value, "labTeamCode");
            return (Criteria) this;
        }

        public Criteria andLabTeamCodeLike(String value) {
            addCriterion("labTeamCode like", value, "labTeamCode");
            return (Criteria) this;
        }

        public Criteria andLabTeamCodeNotLike(String value) {
            addCriterion("labTeamCode not like", value, "labTeamCode");
            return (Criteria) this;
        }

        public Criteria andLabTeamCodeIn(List<String> values) {
            addCriterion("labTeamCode in", values, "labTeamCode");
            return (Criteria) this;
        }

        public Criteria andLabTeamCodeNotIn(List<String> values) {
            addCriterion("labTeamCode not in", values, "labTeamCode");
            return (Criteria) this;
        }

        public Criteria andLabTeamCodeBetween(String value1, String value2) {
            addCriterion("labTeamCode between", value1, value2, "labTeamCode");
            return (Criteria) this;
        }

        public Criteria andLabTeamCodeNotBetween(String value1, String value2) {
            addCriterion("labTeamCode not between", value1, value2, "labTeamCode");
            return (Criteria) this;
        }

        public Criteria andLabSectionNameIsNull() {
            addCriterion("labSectionName is null");
            return (Criteria) this;
        }

        public Criteria andLabSectionNameIsNotNull() {
            addCriterion("labSectionName is not null");
            return (Criteria) this;
        }

        public Criteria andLabSectionNameEqualTo(String value) {
            addCriterion("labSectionName =", value, "labSectionName");
            return (Criteria) this;
        }

        public Criteria andLabSectionNameNotEqualTo(String value) {
            addCriterion("labSectionName <>", value, "labSectionName");
            return (Criteria) this;
        }

        public Criteria andLabSectionNameGreaterThan(String value) {
            addCriterion("labSectionName >", value, "labSectionName");
            return (Criteria) this;
        }

        public Criteria andLabSectionNameGreaterThanOrEqualTo(String value) {
            addCriterion("labSectionName >=", value, "labSectionName");
            return (Criteria) this;
        }

        public Criteria andLabSectionNameLessThan(String value) {
            addCriterion("labSectionName <", value, "labSectionName");
            return (Criteria) this;
        }

        public Criteria andLabSectionNameLessThanOrEqualTo(String value) {
            addCriterion("labSectionName <=", value, "labSectionName");
            return (Criteria) this;
        }

        public Criteria andLabSectionNameLike(String value) {
            addCriterion("labSectionName like", value, "labSectionName");
            return (Criteria) this;
        }

        public Criteria andLabSectionNameNotLike(String value) {
            addCriterion("labSectionName not like", value, "labSectionName");
            return (Criteria) this;
        }

        public Criteria andLabSectionNameIn(List<String> values) {
            addCriterion("labSectionName in", values, "labSectionName");
            return (Criteria) this;
        }

        public Criteria andLabSectionNameNotIn(List<String> values) {
            addCriterion("labSectionName not in", values, "labSectionName");
            return (Criteria) this;
        }

        public Criteria andLabSectionNameBetween(String value1, String value2) {
            addCriterion("labSectionName between", value1, value2, "labSectionName");
            return (Criteria) this;
        }

        public Criteria andLabSectionNameNotBetween(String value1, String value2) {
            addCriterion("labSectionName not between", value1, value2, "labSectionName");
            return (Criteria) this;
        }

        public Criteria andSubContractLabCodeIsNull() {
            addCriterion("subContractLabCode is null");
            return (Criteria) this;
        }

        public Criteria andSubContractLabCodeIsNotNull() {
            addCriterion("subContractLabCode is not null");
            return (Criteria) this;
        }

        public Criteria andSubContractLabCodeEqualTo(String value) {
            addCriterion("subContractLabCode =", value, "subContractLabCode");
            return (Criteria) this;
        }

        public Criteria andSubContractLabCodeNotEqualTo(String value) {
            addCriterion("subContractLabCode <>", value, "subContractLabCode");
            return (Criteria) this;
        }

        public Criteria andSubContractLabCodeGreaterThan(String value) {
            addCriterion("subContractLabCode >", value, "subContractLabCode");
            return (Criteria) this;
        }

        public Criteria andSubContractLabCodeGreaterThanOrEqualTo(String value) {
            addCriterion("subContractLabCode >=", value, "subContractLabCode");
            return (Criteria) this;
        }

        public Criteria andSubContractLabCodeLessThan(String value) {
            addCriterion("subContractLabCode <", value, "subContractLabCode");
            return (Criteria) this;
        }

        public Criteria andSubContractLabCodeLessThanOrEqualTo(String value) {
            addCriterion("subContractLabCode <=", value, "subContractLabCode");
            return (Criteria) this;
        }

        public Criteria andSubContractLabCodeLike(String value) {
            addCriterion("subContractLabCode like", value, "subContractLabCode");
            return (Criteria) this;
        }

        public Criteria andSubContractLabCodeNotLike(String value) {
            addCriterion("subContractLabCode not like", value, "subContractLabCode");
            return (Criteria) this;
        }

        public Criteria andSubContractLabCodeIn(List<String> values) {
            addCriterion("subContractLabCode in", values, "subContractLabCode");
            return (Criteria) this;
        }

        public Criteria andSubContractLabCodeNotIn(List<String> values) {
            addCriterion("subContractLabCode not in", values, "subContractLabCode");
            return (Criteria) this;
        }

        public Criteria andSubContractLabCodeBetween(String value1, String value2) {
            addCriterion("subContractLabCode between", value1, value2, "subContractLabCode");
            return (Criteria) this;
        }

        public Criteria andSubContractLabCodeNotBetween(String value1, String value2) {
            addCriterion("subContractLabCode not between", value1, value2, "subContractLabCode");
            return (Criteria) this;
        }

        public Criteria andPpTlRelBaseIdIsNull() {
            addCriterion("ppTlRelBaseId is null");
            return (Criteria) this;
        }

        public Criteria andPpTlRelBaseIdIsNotNull() {
            addCriterion("ppTlRelBaseId is not null");
            return (Criteria) this;
        }

        public Criteria andPpTlRelBaseIdEqualTo(Long value) {
            addCriterion("ppTlRelBaseId =", value, "ppTlRelBaseId");
            return (Criteria) this;
        }

        public Criteria andPpTlRelBaseIdNotEqualTo(Long value) {
            addCriterion("ppTlRelBaseId <>", value, "ppTlRelBaseId");
            return (Criteria) this;
        }

        public Criteria andPpTlRelBaseIdGreaterThan(Long value) {
            addCriterion("ppTlRelBaseId >", value, "ppTlRelBaseId");
            return (Criteria) this;
        }

        public Criteria andPpTlRelBaseIdGreaterThanOrEqualTo(Long value) {
            addCriterion("ppTlRelBaseId >=", value, "ppTlRelBaseId");
            return (Criteria) this;
        }

        public Criteria andPpTlRelBaseIdLessThan(Long value) {
            addCriterion("ppTlRelBaseId <", value, "ppTlRelBaseId");
            return (Criteria) this;
        }

        public Criteria andPpTlRelBaseIdLessThanOrEqualTo(Long value) {
            addCriterion("ppTlRelBaseId <=", value, "ppTlRelBaseId");
            return (Criteria) this;
        }

        public Criteria andPpTlRelBaseIdIn(List<Long> values) {
            addCriterion("ppTlRelBaseId in", values, "ppTlRelBaseId");
            return (Criteria) this;
        }

        public Criteria andPpTlRelBaseIdNotIn(List<Long> values) {
            addCriterion("ppTlRelBaseId not in", values, "ppTlRelBaseId");
            return (Criteria) this;
        }

        public Criteria andPpTlRelBaseIdBetween(Long value1, Long value2) {
            addCriterion("ppTlRelBaseId between", value1, value2, "ppTlRelBaseId");
            return (Criteria) this;
        }

        public Criteria andPpTlRelBaseIdNotBetween(Long value1, Long value2) {
            addCriterion("ppTlRelBaseId not between", value1, value2, "ppTlRelBaseId");
            return (Criteria) this;
        }

        public Criteria andJobIdIsNull() {
            addCriterion("jobId is null");
            return (Criteria) this;
        }

        public Criteria andJobIdIsNotNull() {
            addCriterion("jobId is not null");
            return (Criteria) this;
        }

        public Criteria andJobIdEqualTo(String value) {
            addCriterion("jobId =", value, "jobId");
            return (Criteria) this;
        }

        public Criteria andJobIdNotEqualTo(String value) {
            addCriterion("jobId <>", value, "jobId");
            return (Criteria) this;
        }

        public Criteria andJobIdGreaterThan(String value) {
            addCriterion("jobId >", value, "jobId");
            return (Criteria) this;
        }

        public Criteria andJobIdGreaterThanOrEqualTo(String value) {
            addCriterion("jobId >=", value, "jobId");
            return (Criteria) this;
        }

        public Criteria andJobIdLessThan(String value) {
            addCriterion("jobId <", value, "jobId");
            return (Criteria) this;
        }

        public Criteria andJobIdLessThanOrEqualTo(String value) {
            addCriterion("jobId <=", value, "jobId");
            return (Criteria) this;
        }

        public Criteria andJobIdLike(String value) {
            addCriterion("jobId like", value, "jobId");
            return (Criteria) this;
        }

        public Criteria andJobIdNotLike(String value) {
            addCriterion("jobId not like", value, "jobId");
            return (Criteria) this;
        }

        public Criteria andJobIdIn(List<String> values) {
            addCriterion("jobId in", values, "jobId");
            return (Criteria) this;
        }

        public Criteria andJobIdNotIn(List<String> values) {
            addCriterion("jobId not in", values, "jobId");
            return (Criteria) this;
        }

        public Criteria andJobIdBetween(String value1, String value2) {
            addCriterion("jobId between", value1, value2, "jobId");
            return (Criteria) this;
        }

        public Criteria andJobIdNotBetween(String value1, String value2) {
            addCriterion("jobId not between", value1, value2, "jobId");
            return (Criteria) this;
        }

        public Criteria andJobNoIsNull() {
            addCriterion("jobNo is null");
            return (Criteria) this;
        }

        public Criteria andJobNoIsNotNull() {
            addCriterion("jobNo is not null");
            return (Criteria) this;
        }

        public Criteria andJobNoEqualTo(String value) {
            addCriterion("jobNo =", value, "jobNo");
            return (Criteria) this;
        }

        public Criteria andJobNoNotEqualTo(String value) {
            addCriterion("jobNo <>", value, "jobNo");
            return (Criteria) this;
        }

        public Criteria andJobNoGreaterThan(String value) {
            addCriterion("jobNo >", value, "jobNo");
            return (Criteria) this;
        }

        public Criteria andJobNoGreaterThanOrEqualTo(String value) {
            addCriterion("jobNo >=", value, "jobNo");
            return (Criteria) this;
        }

        public Criteria andJobNoLessThan(String value) {
            addCriterion("jobNo <", value, "jobNo");
            return (Criteria) this;
        }

        public Criteria andJobNoLessThanOrEqualTo(String value) {
            addCriterion("jobNo <=", value, "jobNo");
            return (Criteria) this;
        }

        public Criteria andJobNoLike(String value) {
            addCriterion("jobNo like", value, "jobNo");
            return (Criteria) this;
        }

        public Criteria andJobNoNotLike(String value) {
            addCriterion("jobNo not like", value, "jobNo");
            return (Criteria) this;
        }

        public Criteria andJobNoIn(List<String> values) {
            addCriterion("jobNo in", values, "jobNo");
            return (Criteria) this;
        }

        public Criteria andJobNoNotIn(List<String> values) {
            addCriterion("jobNo not in", values, "jobNo");
            return (Criteria) this;
        }

        public Criteria andJobNoBetween(String value1, String value2) {
            addCriterion("jobNo between", value1, value2, "jobNo");
            return (Criteria) this;
        }

        public Criteria andJobNoNotBetween(String value1, String value2) {
            addCriterion("jobNo not between", value1, value2, "jobNo");
            return (Criteria) this;
        }

        public Criteria andSubContractIDIsNull() {
            addCriterion("subContractID is null");
            return (Criteria) this;
        }

        public Criteria andSubContractIDIsNotNull() {
            addCriterion("subContractID is not null");
            return (Criteria) this;
        }

        public Criteria andSubContractIDEqualTo(String value) {
            addCriterion("subContractID =", value, "subContractID");
            return (Criteria) this;
        }

        public Criteria andSubContractIDNotEqualTo(String value) {
            addCriterion("subContractID <>", value, "subContractID");
            return (Criteria) this;
        }

        public Criteria andSubContractIDGreaterThan(String value) {
            addCriterion("subContractID >", value, "subContractID");
            return (Criteria) this;
        }

        public Criteria andSubContractIDGreaterThanOrEqualTo(String value) {
            addCriterion("subContractID >=", value, "subContractID");
            return (Criteria) this;
        }

        public Criteria andSubContractIDLessThan(String value) {
            addCriterion("subContractID <", value, "subContractID");
            return (Criteria) this;
        }

        public Criteria andSubContractIDLessThanOrEqualTo(String value) {
            addCriterion("subContractID <=", value, "subContractID");
            return (Criteria) this;
        }

        public Criteria andSubContractIDLike(String value) {
            addCriterion("subContractID like", value, "subContractID");
            return (Criteria) this;
        }

        public Criteria andSubContractIDNotLike(String value) {
            addCriterion("subContractID not like", value, "subContractID");
            return (Criteria) this;
        }

        public Criteria andSubContractIDIn(List<String> values) {
            addCriterion("subContractID in", values, "subContractID");
            return (Criteria) this;
        }

        public Criteria andSubContractIDNotIn(List<String> values) {
            addCriterion("subContractID not in", values, "subContractID");
            return (Criteria) this;
        }

        public Criteria andSubContractIDBetween(String value1, String value2) {
            addCriterion("subContractID between", value1, value2, "subContractID");
            return (Criteria) this;
        }

        public Criteria andSubContractIDNotBetween(String value1, String value2) {
            addCriterion("subContractID not between", value1, value2, "subContractID");
            return (Criteria) this;
        }

        public Criteria andSubContractNoIsNull() {
            addCriterion("subContractNo is null");
            return (Criteria) this;
        }

        public Criteria andSubContractNoIsNotNull() {
            addCriterion("subContractNo is not null");
            return (Criteria) this;
        }

        public Criteria andSubContractNoEqualTo(String value) {
            addCriterion("subContractNo =", value, "subContractNo");
            return (Criteria) this;
        }

        public Criteria andSubContractNoNotEqualTo(String value) {
            addCriterion("subContractNo <>", value, "subContractNo");
            return (Criteria) this;
        }

        public Criteria andSubContractNoGreaterThan(String value) {
            addCriterion("subContractNo >", value, "subContractNo");
            return (Criteria) this;
        }

        public Criteria andSubContractNoGreaterThanOrEqualTo(String value) {
            addCriterion("subContractNo >=", value, "subContractNo");
            return (Criteria) this;
        }

        public Criteria andSubContractNoLessThan(String value) {
            addCriterion("subContractNo <", value, "subContractNo");
            return (Criteria) this;
        }

        public Criteria andSubContractNoLessThanOrEqualTo(String value) {
            addCriterion("subContractNo <=", value, "subContractNo");
            return (Criteria) this;
        }

        public Criteria andSubContractNoLike(String value) {
            addCriterion("subContractNo like", value, "subContractNo");
            return (Criteria) this;
        }

        public Criteria andSubContractNoNotLike(String value) {
            addCriterion("subContractNo not like", value, "subContractNo");
            return (Criteria) this;
        }

        public Criteria andSubContractNoIn(List<String> values) {
            addCriterion("subContractNo in", values, "subContractNo");
            return (Criteria) this;
        }

        public Criteria andSubContractNoNotIn(List<String> values) {
            addCriterion("subContractNo not in", values, "subContractNo");
            return (Criteria) this;
        }

        public Criteria andSubContractNoBetween(String value1, String value2) {
            addCriterion("subContractNo between", value1, value2, "subContractNo");
            return (Criteria) this;
        }

        public Criteria andSubContractNoNotBetween(String value1, String value2) {
            addCriterion("subContractNo not between", value1, value2, "subContractNo");
            return (Criteria) this;
        }

        public Criteria andSampleSegegrationWIIDIsNull() {
            addCriterion("sampleSegegrationWIID is null");
            return (Criteria) this;
        }

        public Criteria andSampleSegegrationWIIDIsNotNull() {
            addCriterion("sampleSegegrationWIID is not null");
            return (Criteria) this;
        }

        public Criteria andSampleSegegrationWIIDEqualTo(Long value) {
            addCriterion("sampleSegegrationWIID =", value, "sampleSegegrationWIID");
            return (Criteria) this;
        }

        public Criteria andSampleSegegrationWIIDNotEqualTo(Long value) {
            addCriterion("sampleSegegrationWIID <>", value, "sampleSegegrationWIID");
            return (Criteria) this;
        }

        public Criteria andSampleSegegrationWIIDGreaterThan(Long value) {
            addCriterion("sampleSegegrationWIID >", value, "sampleSegegrationWIID");
            return (Criteria) this;
        }

        public Criteria andSampleSegegrationWIIDGreaterThanOrEqualTo(Long value) {
            addCriterion("sampleSegegrationWIID >=", value, "sampleSegegrationWIID");
            return (Criteria) this;
        }

        public Criteria andSampleSegegrationWIIDLessThan(Long value) {
            addCriterion("sampleSegegrationWIID <", value, "sampleSegegrationWIID");
            return (Criteria) this;
        }

        public Criteria andSampleSegegrationWIIDLessThanOrEqualTo(Long value) {
            addCriterion("sampleSegegrationWIID <=", value, "sampleSegegrationWIID");
            return (Criteria) this;
        }

        public Criteria andSampleSegegrationWIIDIn(List<Long> values) {
            addCriterion("sampleSegegrationWIID in", values, "sampleSegegrationWIID");
            return (Criteria) this;
        }

        public Criteria andSampleSegegrationWIIDNotIn(List<Long> values) {
            addCriterion("sampleSegegrationWIID not in", values, "sampleSegegrationWIID");
            return (Criteria) this;
        }

        public Criteria andSampleSegegrationWIIDBetween(Long value1, Long value2) {
            addCriterion("sampleSegegrationWIID between", value1, value2, "sampleSegegrationWIID");
            return (Criteria) this;
        }

        public Criteria andSampleSegegrationWIIDNotBetween(Long value1, Long value2) {
            addCriterion("sampleSegegrationWIID not between", value1, value2, "sampleSegegrationWIID");
            return (Criteria) this;
        }

        public Criteria andSampleSegegrationWITextIsNull() {
            addCriterion("sampleSegegrationWIText is null");
            return (Criteria) this;
        }

        public Criteria andSampleSegegrationWITextIsNotNull() {
            addCriterion("sampleSegegrationWIText is not null");
            return (Criteria) this;
        }

        public Criteria andSampleSegegrationWITextEqualTo(String value) {
            addCriterion("sampleSegegrationWIText =", value, "sampleSegegrationWIText");
            return (Criteria) this;
        }

        public Criteria andSampleSegegrationWITextNotEqualTo(String value) {
            addCriterion("sampleSegegrationWIText <>", value, "sampleSegegrationWIText");
            return (Criteria) this;
        }

        public Criteria andSampleSegegrationWITextGreaterThan(String value) {
            addCriterion("sampleSegegrationWIText >", value, "sampleSegegrationWIText");
            return (Criteria) this;
        }

        public Criteria andSampleSegegrationWITextGreaterThanOrEqualTo(String value) {
            addCriterion("sampleSegegrationWIText >=", value, "sampleSegegrationWIText");
            return (Criteria) this;
        }

        public Criteria andSampleSegegrationWITextLessThan(String value) {
            addCriterion("sampleSegegrationWIText <", value, "sampleSegegrationWIText");
            return (Criteria) this;
        }

        public Criteria andSampleSegegrationWITextLessThanOrEqualTo(String value) {
            addCriterion("sampleSegegrationWIText <=", value, "sampleSegegrationWIText");
            return (Criteria) this;
        }

        public Criteria andSampleSegegrationWITextLike(String value) {
            addCriterion("sampleSegegrationWIText like", value, "sampleSegegrationWIText");
            return (Criteria) this;
        }

        public Criteria andSampleSegegrationWITextNotLike(String value) {
            addCriterion("sampleSegegrationWIText not like", value, "sampleSegegrationWIText");
            return (Criteria) this;
        }

        public Criteria andSampleSegegrationWITextIn(List<String> values) {
            addCriterion("sampleSegegrationWIText in", values, "sampleSegegrationWIText");
            return (Criteria) this;
        }

        public Criteria andSampleSegegrationWITextNotIn(List<String> values) {
            addCriterion("sampleSegegrationWIText not in", values, "sampleSegegrationWIText");
            return (Criteria) this;
        }

        public Criteria andSampleSegegrationWITextBetween(String value1, String value2) {
            addCriterion("sampleSegegrationWIText between", value1, value2, "sampleSegegrationWIText");
            return (Criteria) this;
        }

        public Criteria andSampleSegegrationWITextNotBetween(String value1, String value2) {
            addCriterion("sampleSegegrationWIText not between", value1, value2, "sampleSegegrationWIText");
            return (Criteria) this;
        }

        public Criteria andReportSeqIsNull() {
            addCriterion("reportSeq is null");
            return (Criteria) this;
        }

        public Criteria andReportSeqIsNotNull() {
            addCriterion("reportSeq is not null");
            return (Criteria) this;
        }

        public Criteria andReportSeqEqualTo(Integer value) {
            addCriterion("reportSeq =", value, "reportSeq");
            return (Criteria) this;
        }

        public Criteria andReportSeqNotEqualTo(Integer value) {
            addCriterion("reportSeq <>", value, "reportSeq");
            return (Criteria) this;
        }

        public Criteria andReportSeqGreaterThan(Integer value) {
            addCriterion("reportSeq >", value, "reportSeq");
            return (Criteria) this;
        }

        public Criteria andReportSeqGreaterThanOrEqualTo(Integer value) {
            addCriterion("reportSeq >=", value, "reportSeq");
            return (Criteria) this;
        }

        public Criteria andReportSeqLessThan(Integer value) {
            addCriterion("reportSeq <", value, "reportSeq");
            return (Criteria) this;
        }

        public Criteria andReportSeqLessThanOrEqualTo(Integer value) {
            addCriterion("reportSeq <=", value, "reportSeq");
            return (Criteria) this;
        }

        public Criteria andReportSeqIn(List<Integer> values) {
            addCriterion("reportSeq in", values, "reportSeq");
            return (Criteria) this;
        }

        public Criteria andReportSeqNotIn(List<Integer> values) {
            addCriterion("reportSeq not in", values, "reportSeq");
            return (Criteria) this;
        }

        public Criteria andReportSeqBetween(Integer value1, Integer value2) {
            addCriterion("reportSeq between", value1, value2, "reportSeq");
            return (Criteria) this;
        }

        public Criteria andReportSeqNotBetween(Integer value1, Integer value2) {
            addCriterion("reportSeq not between", value1, value2, "reportSeq");
            return (Criteria) this;
        }

        public Criteria andOrderSeqIsNull() {
            addCriterion("orderSeq is null");
            return (Criteria) this;
        }

        public Criteria andOrderSeqIsNotNull() {
            addCriterion("orderSeq is not null");
            return (Criteria) this;
        }

        public Criteria andOrderSeqEqualTo(Integer value) {
            addCriterion("orderSeq =", value, "orderSeq");
            return (Criteria) this;
        }

        public Criteria andOrderSeqNotEqualTo(Integer value) {
            addCriterion("orderSeq <>", value, "orderSeq");
            return (Criteria) this;
        }

        public Criteria andOrderSeqGreaterThan(Integer value) {
            addCriterion("orderSeq >", value, "orderSeq");
            return (Criteria) this;
        }

        public Criteria andOrderSeqGreaterThanOrEqualTo(Integer value) {
            addCriterion("orderSeq >=", value, "orderSeq");
            return (Criteria) this;
        }

        public Criteria andOrderSeqLessThan(Integer value) {
            addCriterion("orderSeq <", value, "orderSeq");
            return (Criteria) this;
        }

        public Criteria andOrderSeqLessThanOrEqualTo(Integer value) {
            addCriterion("orderSeq <=", value, "orderSeq");
            return (Criteria) this;
        }

        public Criteria andOrderSeqIn(List<Integer> values) {
            addCriterion("orderSeq in", values, "orderSeq");
            return (Criteria) this;
        }

        public Criteria andOrderSeqNotIn(List<Integer> values) {
            addCriterion("orderSeq not in", values, "orderSeq");
            return (Criteria) this;
        }

        public Criteria andOrderSeqBetween(Integer value1, Integer value2) {
            addCriterion("orderSeq between", value1, value2, "orderSeq");
            return (Criteria) this;
        }

        public Criteria andOrderSeqNotBetween(Integer value1, Integer value2) {
            addCriterion("orderSeq not between", value1, value2, "orderSeq");
            return (Criteria) this;
        }

        public Criteria andSectionLevelIsNull() {
            addCriterion("sectionLevel is null");
            return (Criteria) this;
        }

        public Criteria andSectionLevelIsNotNull() {
            addCriterion("sectionLevel is not null");
            return (Criteria) this;
        }

        public Criteria andSectionLevelEqualTo(Integer value) {
            addCriterion("sectionLevel =", value, "sectionLevel");
            return (Criteria) this;
        }

        public Criteria andSectionLevelNotEqualTo(Integer value) {
            addCriterion("sectionLevel <>", value, "sectionLevel");
            return (Criteria) this;
        }

        public Criteria andSectionLevelGreaterThan(Integer value) {
            addCriterion("sectionLevel >", value, "sectionLevel");
            return (Criteria) this;
        }

        public Criteria andSectionLevelGreaterThanOrEqualTo(Integer value) {
            addCriterion("sectionLevel >=", value, "sectionLevel");
            return (Criteria) this;
        }

        public Criteria andSectionLevelLessThan(Integer value) {
            addCriterion("sectionLevel <", value, "sectionLevel");
            return (Criteria) this;
        }

        public Criteria andSectionLevelLessThanOrEqualTo(Integer value) {
            addCriterion("sectionLevel <=", value, "sectionLevel");
            return (Criteria) this;
        }

        public Criteria andSectionLevelIn(List<Integer> values) {
            addCriterion("sectionLevel in", values, "sectionLevel");
            return (Criteria) this;
        }

        public Criteria andSectionLevelNotIn(List<Integer> values) {
            addCriterion("sectionLevel not in", values, "sectionLevel");
            return (Criteria) this;
        }

        public Criteria andSectionLevelBetween(Integer value1, Integer value2) {
            addCriterion("sectionLevel between", value1, value2, "sectionLevel");
            return (Criteria) this;
        }

        public Criteria andSectionLevelNotBetween(Integer value1, Integer value2) {
            addCriterion("sectionLevel not between", value1, value2, "sectionLevel");
            return (Criteria) this;
        }

        public Criteria andTestLineSeqIsNull() {
            addCriterion("testLineSeq is null");
            return (Criteria) this;
        }

        public Criteria andTestLineSeqIsNotNull() {
            addCriterion("testLineSeq is not null");
            return (Criteria) this;
        }

        public Criteria andTestLineSeqEqualTo(Integer value) {
            addCriterion("testLineSeq =", value, "testLineSeq");
            return (Criteria) this;
        }

        public Criteria andTestLineSeqNotEqualTo(Integer value) {
            addCriterion("testLineSeq <>", value, "testLineSeq");
            return (Criteria) this;
        }

        public Criteria andTestLineSeqGreaterThan(Integer value) {
            addCriterion("testLineSeq >", value, "testLineSeq");
            return (Criteria) this;
        }

        public Criteria andTestLineSeqGreaterThanOrEqualTo(Integer value) {
            addCriterion("testLineSeq >=", value, "testLineSeq");
            return (Criteria) this;
        }

        public Criteria andTestLineSeqLessThan(Integer value) {
            addCriterion("testLineSeq <", value, "testLineSeq");
            return (Criteria) this;
        }

        public Criteria andTestLineSeqLessThanOrEqualTo(Integer value) {
            addCriterion("testLineSeq <=", value, "testLineSeq");
            return (Criteria) this;
        }

        public Criteria andTestLineSeqIn(List<Integer> values) {
            addCriterion("testLineSeq in", values, "testLineSeq");
            return (Criteria) this;
        }

        public Criteria andTestLineSeqNotIn(List<Integer> values) {
            addCriterion("testLineSeq not in", values, "testLineSeq");
            return (Criteria) this;
        }

        public Criteria andTestLineSeqBetween(Integer value1, Integer value2) {
            addCriterion("testLineSeq between", value1, value2, "testLineSeq");
            return (Criteria) this;
        }

        public Criteria andTestLineSeqNotBetween(Integer value1, Integer value2) {
            addCriterion("testLineSeq not between", value1, value2, "testLineSeq");
            return (Criteria) this;
        }

        public Criteria andPendingFlagIsNull() {
            addCriterion("pendingFlag is null");
            return (Criteria) this;
        }

        public Criteria andPendingFlagIsNotNull() {
            addCriterion("pendingFlag is not null");
            return (Criteria) this;
        }

        public Criteria andPendingFlagEqualTo(Boolean value) {
            addCriterion("pendingFlag =", value, "pendingFlag");
            return (Criteria) this;
        }

        public Criteria andPendingFlagNotEqualTo(Boolean value) {
            addCriterion("pendingFlag <>", value, "pendingFlag");
            return (Criteria) this;
        }

        public Criteria andPendingFlagGreaterThan(Boolean value) {
            addCriterion("pendingFlag >", value, "pendingFlag");
            return (Criteria) this;
        }

        public Criteria andPendingFlagGreaterThanOrEqualTo(Boolean value) {
            addCriterion("pendingFlag >=", value, "pendingFlag");
            return (Criteria) this;
        }

        public Criteria andPendingFlagLessThan(Boolean value) {
            addCriterion("pendingFlag <", value, "pendingFlag");
            return (Criteria) this;
        }

        public Criteria andPendingFlagLessThanOrEqualTo(Boolean value) {
            addCriterion("pendingFlag <=", value, "pendingFlag");
            return (Criteria) this;
        }

        public Criteria andPendingFlagIn(List<Boolean> values) {
            addCriterion("pendingFlag in", values, "pendingFlag");
            return (Criteria) this;
        }

        public Criteria andPendingFlagNotIn(List<Boolean> values) {
            addCriterion("pendingFlag not in", values, "pendingFlag");
            return (Criteria) this;
        }

        public Criteria andPendingFlagBetween(Boolean value1, Boolean value2) {
            addCriterion("pendingFlag between", value1, value2, "pendingFlag");
            return (Criteria) this;
        }

        public Criteria andPendingFlagNotBetween(Boolean value1, Boolean value2) {
            addCriterion("pendingFlag not between", value1, value2, "pendingFlag");
            return (Criteria) this;
        }

        public Criteria andTestLineStatusIsNull() {
            addCriterion("testLineStatus is null");
            return (Criteria) this;
        }

        public Criteria andTestLineStatusIsNotNull() {
            addCriterion("testLineStatus is not null");
            return (Criteria) this;
        }

        public Criteria andTestLineStatusEqualTo(Integer value) {
            addCriterion("testLineStatus =", value, "testLineStatus");
            return (Criteria) this;
        }

        public Criteria andTestLineStatusNotEqualTo(Integer value) {
            addCriterion("testLineStatus <>", value, "testLineStatus");
            return (Criteria) this;
        }

        public Criteria andTestLineStatusGreaterThan(Integer value) {
            addCriterion("testLineStatus >", value, "testLineStatus");
            return (Criteria) this;
        }

        public Criteria andTestLineStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("testLineStatus >=", value, "testLineStatus");
            return (Criteria) this;
        }

        public Criteria andTestLineStatusLessThan(Integer value) {
            addCriterion("testLineStatus <", value, "testLineStatus");
            return (Criteria) this;
        }

        public Criteria andTestLineStatusLessThanOrEqualTo(Integer value) {
            addCriterion("testLineStatus <=", value, "testLineStatus");
            return (Criteria) this;
        }

        public Criteria andTestLineStatusIn(List<Integer> values) {
            addCriterion("testLineStatus in", values, "testLineStatus");
            return (Criteria) this;
        }

        public Criteria andTestLineStatusNotIn(List<Integer> values) {
            addCriterion("testLineStatus not in", values, "testLineStatus");
            return (Criteria) this;
        }

        public Criteria andTestLineStatusBetween(Integer value1, Integer value2) {
            addCriterion("testLineStatus between", value1, value2, "testLineStatus");
            return (Criteria) this;
        }

        public Criteria andTestLineStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("testLineStatus not between", value1, value2, "testLineStatus");
            return (Criteria) this;
        }

        public Criteria andCreatedDateIsNull() {
            addCriterion("createdDate is null");
            return (Criteria) this;
        }

        public Criteria andCreatedDateIsNotNull() {
            addCriterion("createdDate is not null");
            return (Criteria) this;
        }

        public Criteria andCreatedDateEqualTo(Date value) {
            addCriterion("createdDate =", value, "createdDate");
            return (Criteria) this;
        }

        public Criteria andCreatedDateNotEqualTo(Date value) {
            addCriterion("createdDate <>", value, "createdDate");
            return (Criteria) this;
        }

        public Criteria andCreatedDateGreaterThan(Date value) {
            addCriterion("createdDate >", value, "createdDate");
            return (Criteria) this;
        }

        public Criteria andCreatedDateGreaterThanOrEqualTo(Date value) {
            addCriterion("createdDate >=", value, "createdDate");
            return (Criteria) this;
        }

        public Criteria andCreatedDateLessThan(Date value) {
            addCriterion("createdDate <", value, "createdDate");
            return (Criteria) this;
        }

        public Criteria andCreatedDateLessThanOrEqualTo(Date value) {
            addCriterion("createdDate <=", value, "createdDate");
            return (Criteria) this;
        }

        public Criteria andCreatedDateIn(List<Date> values) {
            addCriterion("createdDate in", values, "createdDate");
            return (Criteria) this;
        }

        public Criteria andCreatedDateNotIn(List<Date> values) {
            addCriterion("createdDate not in", values, "createdDate");
            return (Criteria) this;
        }

        public Criteria andCreatedDateBetween(Date value1, Date value2) {
            addCriterion("createdDate between", value1, value2, "createdDate");
            return (Criteria) this;
        }

        public Criteria andCreatedDateNotBetween(Date value1, Date value2) {
            addCriterion("createdDate not between", value1, value2, "createdDate");
            return (Criteria) this;
        }

        public Criteria andTestStartDateIsNull() {
            addCriterion("testStartDate is null");
            return (Criteria) this;
        }

        public Criteria andTestStartDateIsNotNull() {
            addCriterion("testStartDate is not null");
            return (Criteria) this;
        }

        public Criteria andTestStartDateEqualTo(Date value) {
            addCriterion("testStartDate =", value, "testStartDate");
            return (Criteria) this;
        }

        public Criteria andTestStartDateNotEqualTo(Date value) {
            addCriterion("testStartDate <>", value, "testStartDate");
            return (Criteria) this;
        }

        public Criteria andTestStartDateGreaterThan(Date value) {
            addCriterion("testStartDate >", value, "testStartDate");
            return (Criteria) this;
        }

        public Criteria andTestStartDateGreaterThanOrEqualTo(Date value) {
            addCriterion("testStartDate >=", value, "testStartDate");
            return (Criteria) this;
        }

        public Criteria andTestStartDateLessThan(Date value) {
            addCriterion("testStartDate <", value, "testStartDate");
            return (Criteria) this;
        }

        public Criteria andTestStartDateLessThanOrEqualTo(Date value) {
            addCriterion("testStartDate <=", value, "testStartDate");
            return (Criteria) this;
        }

        public Criteria andTestStartDateIn(List<Date> values) {
            addCriterion("testStartDate in", values, "testStartDate");
            return (Criteria) this;
        }

        public Criteria andTestStartDateNotIn(List<Date> values) {
            addCriterion("testStartDate not in", values, "testStartDate");
            return (Criteria) this;
        }

        public Criteria andTestStartDateBetween(Date value1, Date value2) {
            addCriterion("testStartDate between", value1, value2, "testStartDate");
            return (Criteria) this;
        }

        public Criteria andTestStartDateNotBetween(Date value1, Date value2) {
            addCriterion("testStartDate not between", value1, value2, "testStartDate");
            return (Criteria) this;
        }

        public Criteria andTestEndDateIsNull() {
            addCriterion("testEndDate is null");
            return (Criteria) this;
        }

        public Criteria andTestEndDateIsNotNull() {
            addCriterion("testEndDate is not null");
            return (Criteria) this;
        }

        public Criteria andTestEndDateEqualTo(Date value) {
            addCriterion("testEndDate =", value, "testEndDate");
            return (Criteria) this;
        }

        public Criteria andTestEndDateNotEqualTo(Date value) {
            addCriterion("testEndDate <>", value, "testEndDate");
            return (Criteria) this;
        }

        public Criteria andTestEndDateGreaterThan(Date value) {
            addCriterion("testEndDate >", value, "testEndDate");
            return (Criteria) this;
        }

        public Criteria andTestEndDateGreaterThanOrEqualTo(Date value) {
            addCriterion("testEndDate >=", value, "testEndDate");
            return (Criteria) this;
        }

        public Criteria andTestEndDateLessThan(Date value) {
            addCriterion("testEndDate <", value, "testEndDate");
            return (Criteria) this;
        }

        public Criteria andTestEndDateLessThanOrEqualTo(Date value) {
            addCriterion("testEndDate <=", value, "testEndDate");
            return (Criteria) this;
        }

        public Criteria andTestEndDateIn(List<Date> values) {
            addCriterion("testEndDate in", values, "testEndDate");
            return (Criteria) this;
        }

        public Criteria andTestEndDateNotIn(List<Date> values) {
            addCriterion("testEndDate not in", values, "testEndDate");
            return (Criteria) this;
        }

        public Criteria andTestEndDateBetween(Date value1, Date value2) {
            addCriterion("testEndDate between", value1, value2, "testEndDate");
            return (Criteria) this;
        }

        public Criteria andTestEndDateNotBetween(Date value1, Date value2) {
            addCriterion("testEndDate not between", value1, value2, "testEndDate");
            return (Criteria) this;
        }

        public Criteria andTestDueDateIsNull() {
            addCriterion("testDueDate is null");
            return (Criteria) this;
        }

        public Criteria andTestDueDateIsNotNull() {
            addCriterion("testDueDate is not null");
            return (Criteria) this;
        }

        public Criteria andTestDueDateEqualTo(Date value) {
            addCriterion("testDueDate =", value, "testDueDate");
            return (Criteria) this;
        }

        public Criteria andTestDueDateNotEqualTo(Date value) {
            addCriterion("testDueDate <>", value, "testDueDate");
            return (Criteria) this;
        }

        public Criteria andTestDueDateGreaterThan(Date value) {
            addCriterion("testDueDate >", value, "testDueDate");
            return (Criteria) this;
        }

        public Criteria andTestDueDateGreaterThanOrEqualTo(Date value) {
            addCriterion("testDueDate >=", value, "testDueDate");
            return (Criteria) this;
        }

        public Criteria andTestDueDateLessThan(Date value) {
            addCriterion("testDueDate <", value, "testDueDate");
            return (Criteria) this;
        }

        public Criteria andTestDueDateLessThanOrEqualTo(Date value) {
            addCriterion("testDueDate <=", value, "testDueDate");
            return (Criteria) this;
        }

        public Criteria andTestDueDateIn(List<Date> values) {
            addCriterion("testDueDate in", values, "testDueDate");
            return (Criteria) this;
        }

        public Criteria andTestDueDateNotIn(List<Date> values) {
            addCriterion("testDueDate not in", values, "testDueDate");
            return (Criteria) this;
        }

        public Criteria andTestDueDateBetween(Date value1, Date value2) {
            addCriterion("testDueDate between", value1, value2, "testDueDate");
            return (Criteria) this;
        }

        public Criteria andTestDueDateNotBetween(Date value1, Date value2) {
            addCriterion("testDueDate not between", value1, value2, "testDueDate");
            return (Criteria) this;
        }

        public Criteria andEngineerIsNull() {
            addCriterion("engineer is null");
            return (Criteria) this;
        }

        public Criteria andEngineerIsNotNull() {
            addCriterion("engineer is not null");
            return (Criteria) this;
        }

        public Criteria andEngineerEqualTo(String value) {
            addCriterion("engineer =", value, "engineer");
            return (Criteria) this;
        }

        public Criteria andEngineerNotEqualTo(String value) {
            addCriterion("engineer <>", value, "engineer");
            return (Criteria) this;
        }

        public Criteria andEngineerGreaterThan(String value) {
            addCriterion("engineer >", value, "engineer");
            return (Criteria) this;
        }

        public Criteria andEngineerGreaterThanOrEqualTo(String value) {
            addCriterion("engineer >=", value, "engineer");
            return (Criteria) this;
        }

        public Criteria andEngineerLessThan(String value) {
            addCriterion("engineer <", value, "engineer");
            return (Criteria) this;
        }

        public Criteria andEngineerLessThanOrEqualTo(String value) {
            addCriterion("engineer <=", value, "engineer");
            return (Criteria) this;
        }

        public Criteria andEngineerLike(String value) {
            addCriterion("engineer like", value, "engineer");
            return (Criteria) this;
        }

        public Criteria andEngineerNotLike(String value) {
            addCriterion("engineer not like", value, "engineer");
            return (Criteria) this;
        }

        public Criteria andEngineerIn(List<String> values) {
            addCriterion("engineer in", values, "engineer");
            return (Criteria) this;
        }

        public Criteria andEngineerNotIn(List<String> values) {
            addCriterion("engineer not in", values, "engineer");
            return (Criteria) this;
        }

        public Criteria andEngineerBetween(String value1, String value2) {
            addCriterion("engineer between", value1, value2, "engineer");
            return (Criteria) this;
        }

        public Criteria andEngineerNotBetween(String value1, String value2) {
            addCriterion("engineer not between", value1, value2, "engineer");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}