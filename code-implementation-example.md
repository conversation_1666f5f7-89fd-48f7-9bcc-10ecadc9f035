# 代码实现示例

## 1. 核心注解定义

### ModuleTag.java
```java
package com.otsnotes.annotation;

import java.lang.annotation.*;

@Target(ElementType.TYPE)
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface ModuleTag {
    String value();                    // 模块名称
    String description() default "";   // 模块描述
    String domain() default "";        // 所属域
    String version() default "1.0";    // 版本
}
```

### Capability.java
```java
package com.otsnotes.annotation;

import java.lang.annotation.*;

@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface Capability {
    String name();                     // 能力名称
    String description() default "";   // 能力描述
    String[] tags() default {};        // 标签
    Class<? extends Rule>[] rules() default {}; // 执行规则
    int priority() default 0;          // 优先级
}
```

### Function.java
```java
package com.otsnotes.annotation;

import java.lang.annotation.*;

@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface Function {
    String name();                     // 功能名称
    String description() default "";   // 功能描述
    String capability();               // 对应的能力
    String[] dependencies() default {}; // 依赖的其他功能
}
```

## 2. 规则引擎

### Rule.java
```java
package com.otsnotes.rule;

public interface Rule {
    boolean evaluate(Object... params);
    String getRuleName();
    String getDescription();
}
```

### AbstractRule.java
```java
package com.otsnotes.rule;

public abstract class AbstractRule implements Rule {
    @Override
    public String getRuleName() {
        RuleDefinition annotation = this.getClass().getAnnotation(RuleDefinition.class);
        return annotation != null ? annotation.name() : this.getClass().getSimpleName();
    }
    
    @Override
    public String getDescription() {
        RuleDefinition annotation = this.getClass().getAnnotation(RuleDefinition.class);
        return annotation != null ? annotation.description() : "";
    }
}
```

## 3. 元数据模型

### ModuleMetadata.java
```java
package com.otsnotes.metadata;

import java.util.List;
import java.util.Map;

public class ModuleMetadata {
    private String moduleName;
    private String description;
    private String domain;
    private String version;
    private Class<?> serviceClass;
    private List<CapabilityMetadata> capabilities;
    private Map<String, Object> properties;
    
    // getters and setters
}
```

### CapabilityMetadata.java
```java
package com.otsnotes.metadata;

import java.lang.reflect.Method;
import java.util.List;

public class CapabilityMetadata {
    private String name;
    private String description;
    private String[] tags;
    private Method method;
    private List<Class<? extends Rule>> rules;
    private List<FunctionMetadata> functions;
    private int priority;
    
    // getters and setters
}
```

## 4. 完整的订单模块示例

### OrderBizService.java
```java
package com.otsnotes.service.biz;

import com.otsnotes.annotation.*;
import com.otsnotes.rule.*;
import com.otsnotes.domain.OrderInfo;
import com.otsnotes.service.domain.OrderDomainService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
@ModuleTag(
    value = "OrderModule", 
    description = "订单模块", 
    domain = "Order",
    version = "1.0"
)
public class OrderBizService {
    
    @Autowired
    private OrderDomainService orderDomainService;
    
    @Capability(
        name = "query", 
        description = "查询订单信息包含日志",
        tags = {"query", "order", "log"},
        rules = {OrderIdNotEmptyRule.class},
        priority = 1
    )
    public OrderInfo queryOrderInfo(String orderId) {
        // 执行规则验证
        if (!validateRules(orderId)) {
            throw new IllegalArgumentException("规则验证失败");
        }
        
        // 调用域服务
        OrderInfo orderInfo = orderDomainService.queryOrderInfo(orderId);
        orderDomainService.queryOrderLogInfo(orderId);
        
        return orderInfo;
    }
    
    @Capability(
        name = "save", 
        description = "保存订单信息包含日志",
        tags = {"save", "order", "log"},
        rules = {OrderStatusValidRule.class},
        priority = 2
    )
    public void saveOrderInfo(OrderInfo orderInfo) {
        // 执行规则验证
        if (!validateRules(orderInfo)) {
            throw new IllegalArgumentException("规则验证失败");
        }
        
        // 调用域服务
        orderDomainService.saveOrderInfo(orderInfo);
        orderDomainService.saveOrderLogInfo(orderInfo);
    }
    
    private boolean validateRules(Object... params) {
        // 规则验证逻辑
        return true;
    }
}
```

### OrderDomainService.java
```java
package com.otsnotes.service.domain;

import com.otsnotes.annotation.Function;
import com.otsnotes.domain.OrderInfo;
import com.otsnotes.domain.OrderLog;
import org.springframework.stereotype.Service;
import java.util.List;

@Service
public class OrderDomainService {
    
    @Function(
        name = "queryOrderInfo", 
        description = "查询订单信息",
        capability = "query"
    )
    public OrderInfo queryOrderInfo(String orderId) {
        // 具体查询逻辑
        OrderInfo orderInfo = new OrderInfo();
        orderInfo.setOrderId(orderId);
        // ... 其他属性设置
        return orderInfo;
    }
    
    @Function(
        name = "queryOrderLogInfo", 
        description = "查询订单日志信息",
        capability = "query",
        dependencies = {"queryOrderInfo"}
    )
    public List<OrderLog> queryOrderLogInfo(String orderId) {
        // 具体查询日志逻辑
        return null;
    }
    
    @Function(
        name = "saveOrderInfo", 
        description = "保存订单信息",
        capability = "save"
    )
    public void saveOrderInfo(OrderInfo orderInfo) {
        // 具体保存逻辑
        System.out.println("保存订单信息: " + orderInfo.getOrderId());
    }
    
    @Function(
        name = "saveOrderLogInfo", 
        description = "保存订单日志信息",
        capability = "save",
        dependencies = {"saveOrderInfo"}
    )
    public void saveOrderLogInfo(OrderInfo orderInfo) {
        // 具体保存日志逻辑
        System.out.println("保存订单日志: " + orderInfo.getOrderId());
    }
}
```

## 5. 规则实现

### OrderIdNotEmptyRule.java
```java
package com.otsnotes.rule.order;

import com.otsnotes.annotation.RuleDefinition;
import com.otsnotes.rule.AbstractRule;
import org.springframework.util.StringUtils;

@RuleDefinition(
    name = "OrderIdNotEmpty", 
    description = "订单ID不为空",
    priority = 1
)
public class OrderIdNotEmptyRule extends AbstractRule {
    
    @Override
    public boolean evaluate(Object... params) {
        if (params.length > 0 && params[0] instanceof String) {
            String orderId = (String) params[0];
            return StringUtils.hasText(orderId);
        }
        return false;
    }
}
```

### OrderStatusValidRule.java
```java
package com.otsnotes.rule.order;

import com.otsnotes.annotation.RuleDefinition;
import com.otsnotes.rule.AbstractRule;
import com.otsnotes.domain.OrderInfo;

@RuleDefinition(
    name = "OrderStatusValid", 
    description = "订单状态=2",
    priority = 2
)
public class OrderStatusValidRule extends AbstractRule {
    
    @Override
    public boolean evaluate(Object... params) {
        if (params.length > 0 && params[0] instanceof OrderInfo) {
            OrderInfo order = (OrderInfo) params[0];
            return order.getStatus() == 2;
        }
        return false;
    }
}
```

## 6. 使用示例

### 查询特定模块的所有能力
```java
@RestController
@RequestMapping("/api/architecture")
public class ArchitectureController {
    
    @Autowired
    private ArchitectureQueryService queryService;
    
    @GetMapping("/modules/{moduleName}/capabilities")
    public List<CapabilityMetadata> getModuleCapabilities(@PathVariable String moduleName) {
        return queryService.findCapabilitiesByModule(moduleName);
    }
    
    @GetMapping("/capabilities/search")
    public List<CapabilityMetadata> searchCapabilities(@RequestParam String tag) {
        return queryService.findCapabilitiesByTag(tag);
    }
}
```

这个实现方案提供了：

1. **完整的注解体系** - 支持模块、能力、功能的标记
2. **规则引擎** - 支持动态规则验证
3. **元数据收集** - 自动扫描和收集架构信息
4. **查询接口** - 支持多维度查询
5. **文档生成** - 可以基于元数据生成文档

你可以根据这个框架继续扩展其他模块，比如用户模块、商品模块等。